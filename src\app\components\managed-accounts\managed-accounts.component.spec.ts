import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ManagedAccountsComponent } from './managed-accounts.component';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';
import { NoDataContainerComponent } from './no-data-container/no-data-container.component';

describe('ManagedAccountsComponent', () => {
  let component: ManagedAccountsComponent;
  let fixture: ComponentFixture<ManagedAccountsComponent>;
  let mockBreadcrumbService: jasmine.SpyObj<BreadcrumbService>;

  beforeEach(async () => {
    mockBreadcrumbService = jasmine.createSpyObj('BreadcrumbService', ['setBreadcrumbs']);
    await TestBed.configureTestingModule({
      declarations: [ManagedAccountsComponent, NoDataContainerComponent],
      providers: [
        { provide: BreadcrumbService, useValue: mockBreadcrumbService }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ManagedAccountsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize gridData as an empty array', () => {
    expect(Array.isArray(component.gridData)).toBeTrue();
    expect(component.gridData.length).toBe(0);
  });

  it('should call setBreadcrumbs with correct value on init', () => {
    const expectedBreadcrumbs = [{ label: 'Managed Accounts', url: '/managed-accounts' }];
    expect(mockBreadcrumbService.setBreadcrumbs).toHaveBeenCalledWith(expectedBreadcrumbs);
  });

  it('should update breadcrumbs when updateBreadcrumbs is called', () => {
    mockBreadcrumbService.setBreadcrumbs.calls.reset();
    component.updateBreadcrumbs();
    expect(mockBreadcrumbService.setBreadcrumbs).toHaveBeenCalledWith([
      { label: 'Managed Accounts', url: '/managed-accounts' }
    ]);
  });

  it('should not throw error when updateBreadcrumbs is called multiple times', () => {
    expect(() => {
      component.updateBreadcrumbs();
      component.updateBreadcrumbs();
    }).not.toThrow();
  });
});
