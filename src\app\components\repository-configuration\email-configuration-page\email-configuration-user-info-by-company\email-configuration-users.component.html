<div class="email-user-container" *ngIf="!selectedCompanies || selectedCompanies.length === 0">
  <div class="text-center">
    <img src="assets/dist/images/Illustrations.svg" alt="No company" class="no-content-image" />
    <p>Please Select Company</p>
  </div>
</div>

<div class="email-user-wrapper" *ngIf="selectedCompanies?.length > 0">
  <div class="user-info-title-container pl-3 pr-3 d-flex justify-content-between align-items-center">
    <span class="Body-M user-info-text">User Info</span>
    <button kendoButton look="flat" class="add-user-btn" fillMode="outline" themeColor="primary"
      (click)="showAddUserOverlay()">
      <img src="assets/dist/images/Plus-icon.svg" alt="Add User" />
    </button>
  </div>

  <kendo-grid [data]="userInfoItems" scrollable="virtual" [rowHeight]="44" [resizable]="true"
    class="user-info-grid bordered-rows">
    <kendo-grid-column field="employeeName" title="User Name" [width]="180"></kendo-grid-column>
    <kendo-grid-column field="category" title="Category" [width]="120"></kendo-grid-column>
    <kendo-grid-column field="documentType" title="Document Type" [width]="140"> <ng-template kendoGridCellTemplate
        let-dataItem>
        <div class="doc-type-chips">
          <span class="doc-type-chip">
            <span class="doc-name Caption-R">{{ dataItem.documentType.split(',')[0] }}</span>
            <span kendoPopoverAnchor [popover]="myPopover" themeColor="primary"
              *ngIf="dataItem.documentType.split(',').length > 1" themeColor="primary"
              class="ml-1 more-indicator Caption-R">
              +{{ dataItem.documentType.split(',').length - 1 }}
            </span>
            <kendo-popover #myPopover [width]="300" position="left" [popupClass]="'user-info-popover-class'">
              <ng-template kendoPopoverBodyTemplate>
                <div *ngFor="let doc of getAdditionalDocumentTypes(dataItem.documentType)"
                  class="doc-tooltip-item Body-M">
                  {{ doc }}
                </div>
              </ng-template>
            </kendo-popover>
          </span>
        </div>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="recipientType" title="Recipient Type" [width]="100"></kendo-grid-column>
    <kendo-grid-column field="email" title="Email" [width]="200"></kendo-grid-column>
    <kendo-grid-column title="Action" [width]="120" [resizable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="float-right d-flex align-items-end">
          <a class="action-icon" (click)="deleteUserInfo(dataItem)" href="javascript:void(0);">
            <img src="assets/dist/images/delete-company.svg" alt="Delete User Info"/>
          </a>
          <a class="action-icon ml-3 mr-3" (click)="editUserInfo(dataItem)" href="javascript:void(0);">
            <img src="assets/dist/images/edit-user-icon.svg" alt="Edit User Info"/>
          </a>
        </div>
      </ng-template>
    </kendo-grid-column>

    <ng-template kendoGridNoRecordsTemplate>
      <div class="no-data-container text-center">
        <img src="assets/dist/images/Illustrations.svg" alt="No User Info" class="no-data-image" />
        <p class="Body-R">No User Info added</p>
      </div>
    </ng-template>
  </kendo-grid>
</div>

<!-- User Information Side Overlay -->
<div class="notification-sidebar" *ngIf="showAddUser">
  <div class="side-pane">
    <div class="side-header">
      <div class="title-h float-left mb-3">{{ isEditMode ? 'Update User Information' : 'Add User Information' }}</div>
    </div>
    <!-- Employee Name -->
    <div class="form-group mt-3">
      <div class="d-flex justify-content-between">
        <label for="employeeNameInput" class="Caption-M">Employee Name</label>
      </div>
      <div class="input-container">
        <input id="employeeNameInput" class="form-control custom-input Body-R" type="text" maxlength="30"
          placeholder="Enter Name" [(ngModel)]="newUserInfo.employeeName" (input)="checkFormValidity()"
          name="employeeName">
      </div>
    </div> <!-- Categories -->
    <div class="form-group mt-3">
      <div class="d-flex justify-content-between">
        <label for="categoriesInput" class="Caption-M">Categories <span class="text-danger">*</span></label>
      </div>
      <div class="dropdown-container">
        <kendo-combobox id="categoriesInput" class="k-custom-solid-dropdown k-dropdown-height-32" [data]="categories"
          [clearButton]="false" [rounded]="'medium'" [fillMode]="'solid'" placeholder="Select Category"
          valueField="categoryId" textField="category" [(ngModel)]="selectedCategory"
          (valueChange)="checkFormValidity()">
        </kendo-combobox>
      </div>
    </div><!-- Document Type -->
    <div class="form-group mt-3">
      <div class="d-flex justify-content-between">
        <label for="documentTypeSelect" class="Caption-M">Document Type <span class="text-danger">*</span></label>
      </div>
      <div class="dropdown-container">
        <kendo-multiselect class="k-multiselect-custom k-dropdown-width-280" #multiSelect id="documentTypeSelect"
          [data]="documentTypes" [(ngModel)]="selectedDocTypes" [checkboxes]="true" [rounded]="'medium'"
          [fillMode]="'solid'" [clearButton]="false" textField="documentName" valueField="documentTypeID"
          [filterable]="true" [autoClose]="false" (valueChange)="onDocumentTypeSelectionChange($event)"
          [tagMapper]="tagMapper" placeholder="Select Document type" #documentTypeSelect>

          <ng-template kendoMultiSelectHeaderTemplate>
            <div class="inline-container">
              <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo" kendoCheckBox
                [checked]="isDocumentTypeCheckAll" [indeterminate]="isDocumentTypeIndet()"
                (click)="onSelectAllDocumentTypes()" (keydown.space)="onSelectAllDocumentTypes()" />
              <kendo-label>Select All</kendo-label>
            </div>
          </ng-template>
          <ng-template kendoMultiSelectItemTemplate let-dataItem>
            <div class="doc-type-item">
              <div class="TextTruncate Body-R">
                <span [title]="dataItem.documentName">
                  {{ dataItem.documentName }}
                </span>
              </div>
            </div>
          </ng-template>
          <ng-template kendoMultiSelectTagTemplate let-dataItem>
            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.documentName">
              {{ dataItem.documentName }}
            </span>
          </ng-template>
        </kendo-multiselect>
      </div>
    </div> <!-- Recipient Type -->
    <div class="form-group mt-3">
      <div class="d-flex justify-content-between">
        <label for="recipientTypeInput" class="Caption-M">Recipient <span class="text-danger">*</span></label>
      </div>
      <div class="dropdown-container">
        <kendo-combobox class="k-custom-solid-dropdown k-dropdown-height-32" [data]="recipientTypes"
          [clearButton]="false" [rounded]="'medium'" [fillMode]="'solid'" placeholder="Select Type of recipient"
          textField="text" valueField="value" [(ngModel)]="selectedRecipient" (valueChange)="checkFormValidity()">
        </kendo-combobox>
      </div>
    </div><!-- Email -->
    <div class="form-group mt-3">
      <div class="d-flex justify-content-between">
        <label for="emailInput" class="Caption-M">Email <span class="text-danger">*</span></label>
      </div>
      <div class="input-container"> <input id="emailInput" class="form-control Body-R custom-input" type="email"
          placeholder="Enter Email ID" [(ngModel)]="newUserInfo.email" (input)="clearError(); checkFormValidity();"
          name="email" [ngClass]="{'is-invalid': hasError}">
        <div *ngIf="hasError" class="invalid-feedback d-block text-danger Caption-R">
          {{errorMessage}}
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="button-group fixed-bottom custom-bottom">
      <div class="float-right"> <button type="button" class="kendo-custom-button Body-R apply-btn mr-3"
          fillMode="outline" kendoButton (click)="cancelAddDoc()" themeColor="primary">Cancel</button>
        <button (click)="addNewDocumentType()" [disabled]="!formValid" class="kendo-custom-button Body-R apply-btn"
          kendoButton themeColor="primary">Save</button>
      </div>
    </div>
  </div>
</div>

<!-- delete confirmation dialog-->
<div *ngIf="showDeletePopup" class="selection-popup">
  <confirm-modal
      customwidth="500px"
      isCustomFooterClass="true"
      primaryButtonIsDanger="true"
      primaryButtonName="Yes, Delete"
      secondaryButtonName="No, keep it" 
      (primaryButtonEvent)="triggerDeleteAction()"
      modalTitle="{{ deleteConfirmationModalTitle }}"
      (secondaryButtonEvent)="cancelDelete()"
      isDeleteConfirmModal="true"
      isCloseEnable="true"
      (closeIconClick)="cancelDelete()"
    >
      <div class="container px-2">
      <div class="d-flex">
        <div class="mr-13">
          <img src="assets/dist/images/exclamation-circle-delete.svg" alt="Exclamation Circle" />
        </div>
        <div>
          <div class="Heading2-M mb-1">Are You Sure ?</div>
          <div class="Caption-R content-secondary">{{confirmationDialogContent}}</div>
        </div>
      </div>
    </div>
    </confirm-modal>
</div>
