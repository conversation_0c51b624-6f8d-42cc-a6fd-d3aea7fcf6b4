import { Component } from '@angular/core';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';

@Component({
  selector: 'app-managed-accounts',
  templateUrl: './managed-accounts.component.html',
  styleUrls: ['./managed-accounts.component.scss']
})
export class ManagedAccountsComponent {
  public gridData: any[] = [];

  constructor(private breadcrumbService: BreadcrumbService) {
    this.updateBreadcrumbs();
  }

  updateBreadcrumbs() {
    let newBreadcrumbs: any[] = [];
    newBreadcrumbs.push({ label: 'Managed Accounts', url: '/managed-accounts' });
    this.breadcrumbService.setBreadcrumbs(newBreadcrumbs);
  }
}
