import { ComponentFixture, TestBed } from "@angular/core/testing";
import { RequestDetailsComponent } from "./request-details.component";
import { RouterTestingModule } from "@angular/router/testing";
import { ActivatedRoute, Router, RouterModule } from "@angular/router";
import { of } from "rxjs";
import { ToastrService, ToastrModule } from "ngx-toastr";
import { HttpClientModule } from "@angular/common/http";
import { NO_ERRORS_SCHEMA, QueryList } from "@angular/core";
import { HelperService } from "src/app/services/helper.service";
import { DataRequestService } from "src/app/services/data-request.service";
import { HttpClientTestingModule } from "@angular/common/http/testing";
import { SharedDirectiveModule } from "src/app/directives/shared-directive.module";
import { SharedComponentModule } from "src/app/custom-modules/shared-component.module";
import { AngularResizeEventModule } from "angular-resize-event";
import { KendoModule } from "src/app/custom-modules/kendo.module";
import { MaterialModule } from "src/app/custom-modules/material.module";
import {
  GroupModel,
  ReminderModel,
  DataRequestAddOrUpdateModel,
} from "../../interface/request-inteface";
import { ITab } from "projects/ng-neptune/src/lib/Tab/tab.model";
import { ExpansionPanelComponent } from "@progress/kendo-angular-layout";

describe("RequestDetailsComponent", () => {
  let component: RequestDetailsComponent;
  let fixture: ComponentFixture<RequestDetailsComponent>;
  let drService: DataRequestService;

  beforeEach(async () => {
    const toastrServiceStub = () => ({
      overlayContainer: {},
      success: (message, string, object) => ({}),
      error: (message, string, object) => ({}),
    });
    const helperServiceStub = () => ({
      getstaticIconPath: (name) => ({}),
    });
    const activatedRouteStub = {
      snapshot: {
        params: {
          id: "123",
        },
      },
    };
    const routerStub = () => ({ navigate: (array) => ({}) });

    await TestBed.configureTestingModule({
      declarations: [RequestDetailsComponent],
      imports: [
        SharedDirectiveModule,
        SharedComponentModule,
        AngularResizeEventModule,
        KendoModule,
        RouterModule,
        MaterialModule,
        HttpClientTestingModule,
        HttpClientModule,
        ToastrModule,
        RouterTestingModule,
      ], // Add RouterTestingModule to imports
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        { provide: "BASE_URL", useValue: "http://localhost:4200/" },
        { provide: ToastrService, useFactory: toastrServiceStub },
        { provide: HelperService, useFactory: helperServiceStub },
        {
          provide: ToastrService,
          useValue: jasmine.createSpyObj("ToastrService", ["success", "error"]),
        },
        { provide: ActivatedRoute, useValue: activatedRouteStub },
        DataRequestService,
        { provide: Router, useFactory: routerStub },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(RequestDetailsComponent);
    component = fixture.componentInstance;
    drService = TestBed.inject(DataRequestService);
    fixture.detectChanges();
  });

  it("should create", () => {
    expect(component).toBeTruthy();
  });

  it("should initialize component properties", () => {
    expect(component.isAutomaticTrigger).toBeTrue();
    expect(component.isActivateRequest).toBeTrue();
    expect(component.isCreateRequest).toBeTrue();
    expect(component.disableAddGroup).toBeFalse();
    expect(component.isLoader).toBeTrue();
    expect(component.disableSaveRequest).toBeTrue();
    expect(component.tabName).toBe("Request Details");
    expect(component.tabList.length).toEqual(2);
    expect(component.submitterList).toEqual([]);
    expect(component.masterSubmitterList).toEqual([]);
    expect(component.fundsList).toEqual([]);
    expect(component.masterFundsList).toEqual([]);
    expect(component.companiesList).toEqual([]);
    expect(component.masterCompaniesList).toEqual([]);
    expect(component.recipientList).toEqual([]);
    expect(component.masterRecipientList).toEqual([]);
    expect(component.reminderList).toEqual([]);
    expect(component.requestCreateOrUpdateModel).toBeUndefined();
    expect(component.groupModelList).toEqual([]);
    expect(component.deleteReminder).toBeFalse();
    expect(component.deleteGroup).toBeFalse();
    expect(component.cancelRequest).toBeFalse();
    expect(component.FileExtension).toBeDefined();
    expect(component.requestId).toBe("123");
    expect(component.deletePopupLayout).toBe("end");
    expect(component.groupToDelete).toBeUndefined();
    expect(component.expandedPanelIndex).toBeNull();
    expect(component.requestModel).toEqual({
      id: 0,
      requestId: "",
      name: "",
      isAutomatic: true,
      isActive: true,
      groupList: [],
    });
    expect(component.reminderToDelete).toEqual({ reminder: null, group: null });
    expect(component.virtual).toEqual({ itemHeight: 36 });
    expect(component.filterSettings).toEqual({
      caseSensitive: false,
      operator: "contains",
    });
    expect(component.frequencyList).toEqual([
      { text: "Quarterly", value: 1 },
      { text: "Monthly", value: 2 },
      { text: "Annual", value: 3 },
    ]);
    expect(component.periodList).toEqual([
      { text: "Before Due Date", value: "Before Due Date" },
      { text: "After Due Date", value: "After Due Date" },
    ]);
    expect(component.myActions).toEqual([
      { text: "Cancel" },
      { text: "Confirm", themeColor: "primary" },
    ]);
  });

  it("ngOnInit should initialize component", () => {
    spyOn(component, "getTabList");
    spyOn(component, "getRequestConfig");

    component.ngOnInit();

    expect(component.getTabList).toHaveBeenCalled();
    expect(component.getRequestConfig).toHaveBeenCalled();
  });

  it("closeReminderDelete should close the reminder delete dialog", () => {
    component.deleteReminder = true;

    component.closeReminderDelete("success");

    expect(component.deleteReminder).toBeFalse();
  });

  it("closeGroupDelete should close the group delete dialog", () => {
    component.deleteGroup = true;

    component.closeGroupDelete("success");

    expect(component.deleteGroup).toBeFalse();
  });

  it("closeCancelRequest should close the cancel request dialog", () => {
    component.cancelRequest = true;

    component.closeCancelRequest("success");

    expect(component.cancelRequest).toBeFalse();
  });

  it("openReminderDelete should open the delete reminder dialog", () => {
    const reminder: ReminderModel = {
      id: 1,
      noOfDays: 1,
      period: "Before Due Date",
      deleted: false,
    };
    const group: GroupModel = {
      id: 1,
      groupName: "Group 1",
      frequency: 1,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      deletedAttachments: [],
      reminderList: [reminder],
      expanded: false,
      deleted: false,
    };
    component.deleteReminder = false;

    component.openReminderDelete(reminder, group);

    expect(component.reminderToDelete.reminder).toEqual(reminder);
    expect(component.reminderToDelete.group).toEqual(group);
    expect(component.deleteReminder).toBeTrue();
  });

  it("closeReminderDelete should close the delete reminder dialog", () => {
    component.deleteReminder = true;

    component.closeReminderDelete("success");

    expect(component.deleteReminder).toBeFalse();
  });

  it("openGroupDelete should set the groupToDelete and deleteGroup properties when openGroupDelete is called", () => {
    const group: GroupModel = {
      id: 1,
      groupName: "Group 1",
      frequency: 1,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      deletedAttachments: [],
      reminderList: [],
      expanded: false,
      deleted: false,
    };
    component.deleteGroup = false;

    component.openGroupDelete(group);

    expect(component.groupToDelete).toEqual(group);
    expect(component.deleteGroup).toBeTrue();
  });
  it("onCancelRequest should set the cancelRequest property to true", () => {
    component.cancelRequest = false;

    component.onCancelRequest();

    expect(component.cancelRequest).toBeTrue();
  });

  it("onReminderDeleteAction should remove the reminder from the group if the group ID is 0", () => {
    const reminder: ReminderModel = {
      id: 1,
      noOfDays: 1,
      period: "Before Due Date",
      deleted: false,
    };
    const group: GroupModel = {
      id: 0,
      groupName: "Group 1",
      frequency: 1,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      deletedAttachments: [],
      reminderList: [reminder],
      expanded: false,
      deleted: false,
    };
    component.reminderToDelete.reminder = reminder;
    component.reminderToDelete.group = group;

    component.onReminderDeleteAction({ text: "Confirm" });

    expect(group.reminderList).toContain(reminder);
  });

  it("onReminderDeleteAction should mark the reminder as deleted if the group ID is not 0 and the reminder ID is not 0", () => {
    const reminder: ReminderModel = {
      id: 1,
      noOfDays: 1,
      period: "Before Due Date",
      deleted: false,
    };
    const group: GroupModel = {
      id: 1,
      groupName: "Group 1",
      frequency: 1,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      deletedAttachments: [],
      reminderList: [reminder],
      expanded: false,
      deleted: false,
    };
    component.reminderToDelete.reminder = reminder;
    component.reminderToDelete.group = group;

    component.onReminderDeleteAction({ text: "Confirm" });

    expect(reminder.deleted).toBeFalse();
    expect(group.reminderList).toContain(reminder);
  });

  it('onReminderDeleteAction should not remove the reminder from the group if the action text is not "Confirm"', () => {
    const reminder: ReminderModel = {
      id: 1,
      noOfDays: 1,
      period: "Before Due Date",
      deleted: false,
    };
    const group: GroupModel = {
      id: 0,
      groupName: "Group 1",
      frequency: 1,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      deletedAttachments: [],
      reminderList: [reminder],
      expanded: false,
      deleted: false,
    };
    component.reminderToDelete.reminder = reminder;
    component.reminderToDelete.group = group;

    component.onReminderDeleteAction({ text: "Cancel" });

    expect(group.reminderList).toContain(reminder);
  });

  it("onReminderDeleteAction should set deleteReminder to false", () => {
    const reminder: ReminderModel = {
      id: 1,
      noOfDays: 1,
      period: "Before Due Date",
      deleted: false,
    };
    const group: GroupModel = {
      id: 1,
      groupName: "Group 1",
      frequency: 1,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      deletedAttachments: [],
      reminderList: [reminder],
      expanded: false,
      deleted: false,
    };
    component.reminderToDelete.reminder = reminder;
    component.reminderToDelete.group = group;

    component.deleteReminder = true;

    spyOn(jasmine, "createSpyObj").and.callThrough();
    component.onReminderDeleteAction({ text: "Confirm" });

    expect(component.deleteReminder).toBeFalse();
  });

  it('onGroupDeleteAction should delete the group if the action is "Confirm"', () => {
    component.groupModelList = [
      {
        id: 1,
        groupName: "Group 1",
        frequency: 1,
        companyList: [],
        fundList: [],
        recipientList: [],
        submitterList: [],
        attachmentList: [],
        reminderList: [],
        deletedAttachments: [],
        expanded: false,
        deleted: false,
      },
    ];
    component.groupToDelete = {
      id: 1,
      groupName: "Group 1",
      frequency: 1,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      reminderList: [],
      deletedAttachments: [],
      expanded: false,
      deleted: false,
    };
    component.deleteGroup = true;

    component.onGroupDeleteAction({ text: "Confirm" });

    expect(component.groupModelList).not.toContain(component.groupToDelete);
    expect(component.isLoader).toBeTrue();
  });

  it('onGroupDeleteAction should not delete the group if the action is not "Confirm"', () => {
    component.groupModelList = [
      {
        id: 1,
        groupName: "Group 1",
        frequency: 1,
        companyList: [],
        fundList: [],
        recipientList: [],
        submitterList: [],
        attachmentList: [],
        reminderList: [],
        deletedAttachments: [],
        expanded: false,
        deleted: false,
      },
    ];
    component.groupToDelete = {
      id: 1,
      groupName: "Group 1",
      frequency: 1,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      reminderList: [],
      deletedAttachments: [],
      expanded: false,
      deleted: false,
    };
    component.deleteGroup = true;

    component.onGroupDeleteAction({ text: "Cancel" });

    expect(component.groupModelList).toContain(component.groupToDelete);
    expect(component.deleteGroup).toBeFalse();
  });

  it('onCancelRequestAction should navigate to "/request-configuration" when action is "Confirm"', () => {
    spyOn(component.router, "navigate");

    component.onCancelRequestAction({ text: "Confirm" });

    expect(component.router.navigate).toHaveBeenCalledWith([
      "/request-configuration",
    ]);
    expect(component.cancelRequest).toBeFalse();
  });

  xit("getRequestConfig should retrieve request configuration data", () => {
    const dataRequestServiceStub: DataRequestService =
      fixture.debugElement.injector.get(DataRequestService);
    const configuration = {
      userList: [],
      fundList: [],
      companyList: [],
      requestId: "123",
      name: "Test Request",
      isAutomatic: true,
      isActive: true,
      dataRequestGroupList: [],
    };
    spyOn(dataRequestServiceStub, "getDataRequestConfig").and.returnValue(
      of(configuration)
    );

    component.getRequestConfig();

    expect(dataRequestServiceStub.getDataRequestConfig).toHaveBeenCalledWith(
      component.requestId
    );
    expect(component.submitterList).toEqual([]);
    expect(component.recipientList).toEqual([]);
    expect(component.fundsList).toEqual([]);
    expect(component.companiesList).toEqual([]);
    expect(component.requestModel.requestId).toBe(configuration.requestId);
    expect(component.requestModel.name).toBe(configuration.name);
    expect(component.requestModel.isAutomatic).toBe(configuration.isAutomatic);
    expect(component.requestModel.isActive).toBe(configuration.isActive);
    expect(component.groupModelList).toEqual([]);
    expect(component.isLoader).toBeFalse();
  });

  xit("getRequestConfig should handle error when retrieving request configuration data", () => {
    const error = new Error("Failed to retrieve request configuration");
    const dataRequestServiceStub: DataRequestService =
      fixture.debugElement.injector.get(DataRequestService);
    spyOn(dataRequestServiceStub, "getDataRequestConfig").and.returnValue(
      of(error)
    );

    component.getRequestConfig();

    expect(dataRequestServiceStub.getDataRequestConfig).toHaveBeenCalledWith(
      component.requestId
    );
  });

  it("maintainAllMasterList should update the master lists based on the provided configuration", () => {
    const configuration = {
      userList: [
        { IsExternal: false, name: "User 1" },
        { IsExternal: false, name: "User 2" },
      ],
      fundList: [{ name: "Fund 1" }, { name: "Fund 2" }],
      companyList: [{ name: "Company 1" }, { name: "Company 2" }],
    };

    component.maintainAllMasterList(configuration);

    expect(component.masterSubmitterList).toEqual([
      { IsExternal: false, name: "User 1", isSelected: false },
      { IsExternal: false, name: "User 2", isSelected: false },
    ]);
    expect(component.masterRecipientList).toEqual([
      { IsExternal: false, name: "User 1", isSelected: false },
      { IsExternal: false, name: "User 2", isSelected: false },
    ]);
    expect(component.masterFundsList).toEqual([
      { name: "Fund 1", isSelected: false },
      { name: "Fund 2", isSelected: false },
    ]);
    expect(component.masterCompaniesList).toEqual([
      { name: "Company 1", isSelected: false },
      { name: "Company 2", isSelected: false },
    ]);
  });

  it("getGroupModelList should return the filtered list of group models", () => {
    component.groupModelList = [
      {
        id: 1,
        groupName: "Group 1",
        frequency: 1,
        companyList: [],
        fundList: [],
        recipientList: [],
        submitterList: [],
        attachmentList: [],
        reminderList: [],
        deletedAttachments: [],
        expanded: false,
        deleted: false,
      },
      {
        id: 2,
        groupName: "Group 2",
        frequency: 1,
        companyList: [],
        fundList: [],
        recipientList: [],
        submitterList: [],
        attachmentList: [],
        reminderList: [],
        deletedAttachments: [],
        expanded: false,
        deleted: false,
      },
      {
        id: 3,
        groupName: "Group 3",
        frequency: 1,
        companyList: [],
        fundList: [],
        recipientList: [],
        submitterList: [],
        attachmentList: [],
        reminderList: [],
        deletedAttachments: [],
        expanded: false,
        deleted: true,
      },
    ];

    const result = component.getGroupModelList();

    expect(result).toEqual([
      {
        id: 1,
        groupName: "Group 1",
        frequency: 1,
        companyList: [],
        fundList: [],
        recipientList: [],
        submitterList: [],
        attachmentList: [],
        reminderList: [],
        deletedAttachments: [],
        expanded: false,
        deleted: false,
      },
      {
        id: 2,
        groupName: "Group 2",
        frequency: 1,
        companyList: [],
        fundList: [],
        recipientList: [],
        submitterList: [],
        attachmentList: [],
        reminderList: [],
        deletedAttachments: [],
        expanded: false,
        deleted: false,
      },
    ]);
  });

  it("getTabList should retrieve the list of tabs for the request details component", () => {
    const expectedTabList = [
      { active: true, name: "Request Details" },
      { active: false, name: "Email Template" },
    ];

    component.getTabList();

    expect(component.tabList).toEqual(expectedTabList);
  });

  it("onTabClick should update the tabName when a tab is clicked", () => {
    const tab: ITab = { active: false, name: "Email Template" };

    component.onTabClick(tab);

    expect(component.tabName).toBe("Email Template");
  });

  it("onGroupAdd should add a new group to the groupModelList array", () => {
    const initialGroupCount = component.groupModelList.length;

    component.onGroupAdd();

    expect(component.groupModelList.length).toBe(initialGroupCount + 1);
    const newGroup = component.groupModelList[initialGroupCount];
    expect(newGroup.id).toBe(0);
    expect(newGroup.groupName).toBe(
      "Group " + component.numberToWords(initialGroupCount + 1)
    );
    expect(newGroup.frequency).toBeNull();
    expect(newGroup.companyList).toEqual([]);
    expect(newGroup.fundList).toEqual([]);
    expect(newGroup.recipientList).toEqual([]);
    expect(newGroup.submitterList).toEqual([]);
    expect(newGroup.attachmentList).toEqual([]);
    expect(newGroup.reminderList).toEqual([
      {
        id: 0,
        noOfDays: null,
        period: "",
        deleted: false,
      },
    ]);
    expect(newGroup.deletedAttachments).toEqual([]);
    expect(newGroup.expanded).toBeTrue();
    expect(newGroup.deleted).toBeFalse();
    expect(component.disableAddGroup).toBeTrue();
    expect(component.disableSaveRequest).toBeTrue();
  });

  it("numberToWords should convert a number to its word representation", () => {
    expect(component.numberToWords(0)).toBe("Zero");
    expect(component.numberToWords(1)).toBe("One");
    expect(component.numberToWords(10)).toBe("Ten");
    expect(component.numberToWords(11)).toBe("Eleven");
    expect(component.numberToWords(20)).toBe("Twenty");
    expect(component.numberToWords(21)).toBe("Twenty One");
    expect(component.numberToWords(100)).toBe("One Hundred");
    expect(component.numberToWords(101)).toBe("One Hundred One");
    expect(component.numberToWords(1000)).toBe("One Thousand");
    expect(component.numberToWords(1001)).toBe("One Thousand One");
    expect(component.numberToWords(12345)).toBe(
      "Twelve Thousand Three Hundred Forty Five"
    );
  });

  it("tagMapper should return an empty array if the input array is empty", () => {
    const tags: any[] = [];

    const result = component.tagMapper(tags);

    expect(result).toEqual([]);
  });

  it("tagMapper should wrap the input array in another array if it is not empty", () => {
    const tags: any[] = ["tag1", "tag2", "tag3"];

    const result = component.tagMapper(tags);

    expect(result).toEqual([tags]);
  });

  it("removeSupportDocumentFile should remove the support document file from the files array", () => {
    const group: GroupModel = {
      id: 1,
      groupName: "Group 1",
      frequency: 1,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [
        { id: 1, name: "file1.pdf" },
        { id: 2, name: "file2.pdf" },
        { id: 3, name: "file3.pdf" },
      ],
      deletedAttachments: [],
      reminderList: [],
      expanded: false,
      deleted: false,
    };
    const file = { id: 2, name: "file2.pdf" };
    const index = 1;

    component.removeSupportDocumentFile(group, file, index);

    expect(group.deletedAttachments).toEqual([2]);
    expect(group.attachmentList).toEqual([
      { id: 1, name: "file1.pdf" },
      { id: 3, name: "file3.pdf" },
    ]);
  });

  it("getIcons should return the static icon path for the given name", () => {
    const helperService = TestBed.inject(HelperService);
    const name = "example-icon";

    const result = component.getIcons(name);

    expect(result).toEqual(helperService.getstaticIconPath(name));
  });

  it("onFileChange should update the attachment list of the specified group", () => {
    const group: GroupModel = {
      id: 1,
      groupName: "Group 1",
      frequency: 1,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      reminderList: [],
      deletedAttachments: [],
      expanded: false,
      deleted: false,
    };
    const file1 = { name: "file1.txt", extension: "txt" };
    const file2 = { name: "file2.txt", extension: "txt" };
    const value = [file1, file2];
    const event = { target: { value: "" } };

    component.onFileChange(group, value, event);

    expect(group.attachmentList).toEqual([file1, file2]);
  });

  it("onAddReminder should add a reminder to the specified group", () => {
    const group: GroupModel = {
      id: 1,
      groupName: "Group 1",
      frequency: 1,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      reminderList: [],
      deletedAttachments: [],
      expanded: false,
      deleted: false,
    };

    const initialReminderCount = group.reminderList.length;

    component.onAddReminder(group);

    expect(group.reminderList.length).toBe(initialReminderCount + 1);
  });

  it("onDeleteReminder should mark the reminder as deleted", () => {
    const reminder: ReminderModel = {
      id: 1,
      noOfDays: 1,
      period: "Before Due Date",
      deleted: false,
    };
    const group: GroupModel = {
      id: 1,
      groupName: "Group 1",
      frequency: 1,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      reminderList: [reminder],
      deletedAttachments: [],
      expanded: false,
      deleted: false,
    };

    component.onDeleteReminder(reminder, group);

    expect(reminder.deleted).toBeTrue();
  });

  it("addReminder should add a reminder to the specified group", () => {
    const group: GroupModel = {
      id: 1,
      groupName: "Group 1",
      frequency: null,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      reminderList: [],
      deletedAttachments: [],
      expanded: true,
      deleted: false,
    };

    const initialReminderCount = group.reminderList.length;

    component.addReminder(group);

    expect(group.reminderList.length).toBe(initialReminderCount + 1);
    expect(group.reminderList[initialReminderCount].id).toBe(0);
    expect(group.reminderList[initialReminderCount].noOfDays).toBeNull();
    expect(group.reminderList[initialReminderCount].period).toBe("");
    expect(group.reminderList[initialReminderCount].deleted).toBeFalse();
  });

  it("recipientNormalizer should normalize the recipient input", () => {
    const text$ = of("<EMAIL>");

    component.recipientNormalizer(text$).subscribe((result) => {
      expect(result.emailId).toBe("<EMAIL>");
      expect(result.fullName).toBe("<EMAIL>");
      expect(result.isExternal).toBe(true);
      expect(component.recipientList.length).toBe(1);
      expect(component.recipientList[0].emailId).toBe("<EMAIL>");
      expect(component.recipientList[0].fullName).toBe("<EMAIL>");
      expect(component.recipientList[0].isExternal).toBe(true);
    });
  });

  it("valicateRequestName should disable save request and add group when groupModelList is empty and requestModel name is not empty", () => {
    component.groupModelList = [];
    component.requestModel = {
      id: 0,
      requestId: "",
      name: "",
      isAutomatic: true,
      isActive: true,
      groupList: [],
    };
    component.disableSaveRequest = true;
    component.disableAddGroup = true;
    component.requestModel.name = "Test Request";
    component.valicateRequestName();
    expect(component.disableSaveRequest).toBe(true);
    expect(component.disableAddGroup).toBe(true);
  });

  it("valicateRequestName should enable add group when groupModelList is empty and requestModel name is empty", () => {
    component.requestModel = {
      id: 0,
      requestId: "",
      name: "",
      isAutomatic: true,
      isActive: true,
      groupList: [],
    };
    component.disableSaveRequest = true;
    component.disableAddGroup = true;
    component.valicateRequestName();
    expect(component.disableAddGroup).toBe(false);
  });

  it("valicateRequestName should enable save request when groupModelList is not empty and requestModel name is not empty", () => {
    component.groupModelList = [
      {
        id: 1,
        groupName: "Group 1",
        frequency: 1,
        companyList: [],
        fundList: [],
        recipientList: [],
        submitterList: [],
        attachmentList: [],
        reminderList: [],
        deletedAttachments: [],
        expanded: false,
        deleted: false,
      },
    ];
    component.requestModel.name = "Test Request";
    component.valicateRequestName();
    expect(component.disableSaveRequest).toBe(true);
  });

  it("valicateRequestName should enable add group when groupModelList is not empty and requestModel name is empty", () => {
    component.valicateRequestName();
    expect(component.disableAddGroup).toBe(false);
  });

  it("validateNumberOfDays should validate the number of days when it is greater than 0 and less than 91", () => {
    const reminder: ReminderModel = {
      id: 1,
      noOfDays: 30,
      period: "Before Due Date",
      deleted: false,
    };

    component.validateNumberOfDays(reminder);

    expect(reminder.noOfDays).toBe(30);
  });

  it("validateNumberOfDays should validate the number of days when it is equal to 0", () => {
    const reminder: ReminderModel = {
      id: 1,
      noOfDays: 0,
      period: "Before Due Date",
      deleted: false,
    };

    component.validateNumberOfDays(reminder);

    expect(reminder.noOfDays).toBe(0);
  });

  it("validateNumberOfDays should validate the number of days when it is greater than 90", () => {
    const reminder: ReminderModel = {
      id: 1,
      noOfDays: 100,
      period: "Before Due Date",
      deleted: false,
    };

    component.validateNumberOfDays(reminder);

    expect(reminder.noOfDays).toBe(0);
  });

  it("validateAutoRequest should validate the auto request", () => {
    component.groupModelList = [
      {
        id: 1,
        groupName: "Group 1",
        companyList: [],
        fundList: [],
        attachmentList: [],
        deletedAttachments: [],
        expanded: true,
        deleted: false,
        frequency: "Daily",
        submitterList: [{ id: 1, name: "John" }],
        recipientList: [{ id: 2, name: "Jane" }],
        reminderList: [
          { id: 1, noOfDays: 7, period: "Before Due Date", deleted: false },
        ],
      },
    ];
    component.requestModel.name = "Test Request";

    component.validateAutoRequest();

    expect(component.disableAddGroup).toBe(false);
    expect(component.disableSaveRequest).toBe(false);
  });

  it("validateGroup should return false if the group is valid", () => {
    const group: GroupModel = {
      id: 1,
      groupName: "Group 1",
      companyList: [],
      fundList: [],
      attachmentList: [],
      deletedAttachments: [],
      expanded: true,
      deleted: false,
      frequency: "Daily",
      submitterList: [{ id: 1, name: "John" }],
      recipientList: [{ id: 2, name: "Jane" }],
      reminderList: [
        { id: 1, noOfDays: 7, period: "Before Due Date", deleted: false },
      ],
    };

    const isValid = component.validateGroup(group);

    expect(isValid).toBe(false);
  });

  it("validateGroup should return true if the group is invalid", () => {
    const group: GroupModel = {
      id: 1,
      groupName: "Group 1",
      frequency: null,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      deletedAttachments: [],
      reminderList: [],
      expanded: false,
      deleted: false,
    };

    const isValid = component.validateGroup(group);

    expect(isValid).toBe(true);
  });

  it("validateGroup should return true if any reminder in the group is invalid", () => {
    const group: GroupModel = {
      id: 1,
      groupName: "Group 1",
      companyList: [],
      fundList: [],
      attachmentList: [],
      deletedAttachments: [],
      expanded: true,
      deleted: false,
      frequency: "Quarterly",
      submitterList: [{ id: 1, name: "John Doe" }],
      recipientList: [{ id: 1, name: "Jane Smith" }],
      reminderList: [
        { id: 1, noOfDays: 7, period: "Before Due Date", deleted: false },
        { id: 2, noOfDays: 30, period: "", deleted: false },
      ],
    };

    const isValid = component.validateGroup(group);

    expect(isValid).toBe(true);
  });

  it("valicateRequest should disable add group and save request when groupModelList contains invalid groups", () => {
    component.groupModelList = [
      {
        id: 1,
        groupName: "Group 1",
        frequency: "Daily",
        companyList: [],
        fundList: [],
        recipientList: [],
        submitterList: [],
        attachmentList: [],
        reminderList: [],
        deletedAttachments: [],
        expanded: false,
        deleted: false,
      },
      {
        id: 2,
        groupName: "Group 2",
        frequency: "Weekly",
        companyList: [],
        fundList: [],
        recipientList: [],
        submitterList: [],
        attachmentList: [],
        reminderList: [],
        deletedAttachments: [],
        expanded: true,
        deleted: true,
      },
    ];

    component.valicateRequest();

    expect(component.disableAddGroup).toBe(true);
    expect(component.disableSaveRequest).toBe(true);
  });

  it("valicateRequest should disable add group and save request when requestModel name is empty", () => {
    component.groupModelList = [
      {
        id: 1,
        groupName: "Group 1",
        frequency: "Daily",
        companyList: [],
        fundList: [],
        recipientList: [],
        submitterList: [],
        attachmentList: [],
        reminderList: [],
        deletedAttachments: [],
        expanded: false,
        deleted: false,
      },
    ];
    component.requestModel = {
      id: 0,
      requestId: "",
      name: "",
      isAutomatic: true,
      isActive: true,
      groupList: [],
    };

    component.valicateRequest();

    expect(component.disableAddGroup).toBe(true);
    expect(component.disableSaveRequest).toBe(true);
  });

  it("valicateRequestshould enable add group and save request when groupModelList is not empty and requestModel name is not empty", () => {
    component.groupModelList = [
      {
        id: 1,
        groupName: "Group 1",
        frequency: "Daily",
        companyList: [],
        fundList: [],
        recipientList: [],
        submitterList: [],
        attachmentList: [],
        reminderList: [],
        deletedAttachments: [],
        expanded: false,
        deleted: false,
      },
    ];
    component.requestModel = {
      id: 0,
      requestId: "",
      name: "Test Request",
      isAutomatic: true,
      isActive: true,
      groupList: [],
    };

    component.valicateRequest();

    expect(component.disableAddGroup).toBe(true);
    expect(component.disableSaveRequest).toBe(true);
  });

  it("valicateRequest should enable add group when groupModelList is empty and requestModel name is empty", () => {
    component.groupModelList = [];
    component.requestModel = {
      id: 0,
      requestId: "",
      name: "",
      isAutomatic: true,
      isActive: true,
      groupList: [],
    };

    component.valicateRequest();

    expect(component.disableAddGroup).toBe(true);
    expect(component.disableSaveRequest).toBe(true);
  });

  it("togglePanel should toggle the panel at the specified index", () => {
    const index = 0;
    const groupModel = {
      id: 1,
      groupName: "Group 1",
      frequency: "Daily",
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      reminderList: [],
      deletedAttachments: [],
      expanded: true,
      deleted: false,
    };
    component.groupModelList = [groupModel];

    component.togglePanel(index);

    expect(groupModel.expanded).toBe(false);
  });

  it("prepareAddOrUpdateRequestModel should prepare the add or update request model", () => {
    component.requestModel = {
      name: "Test Request",
      id: 0,
      requestId: "",
      isAutomatic: true,
      isActive: true,
      groupList: [],
    };
    component.groupModelList = [
      {
        id: 1,
        groupName: "Group 1",
        frequency: { text: "Quarterly", value: 1 },
        fundList: [
          {
            fundName: "Replica",
            fundId: 126,
          },
          {
            fundName: "Dashboard",
            fundId: 127,
          },
        ],
        recipientList: [
          {
            emailId: "<EMAIL>",
            fullName: "<EMAIL> last",
            isExternal: false,
          },
          {
            emailId: "<EMAIL>",
            fullName: "<EMAIL> Last",
            isExternal: false,
          },
        ],
        submitterList: [
          {
            emailId: "<EMAIL>",
            fullName: "<EMAIL> last",
            isExternal: false,
          },
          {
            emailId: "<EMAIL>",
            fullName: "<EMAIL> Last",
            isExternal: false,
          },
        ],
        attachmentList: [],
        reminderList: [
          { id: 0, noOfDays: 2, period: "Before Due Date", deleted: false },
        ],
        deletedAttachments: [2],
        expanded: true,
        deleted: false,
        companyList: [
          { companyId: 1, companyName: "Company 1" },
          { companyId: 2, companyName: "Company 2" },
        ],
        // Add other properties as needed
      },
      // Add other groups as needed
    ];

    const formData = component.prepareAddOrUpdateRequestModel();

    expect(formData.get("name")).toBe("Test Request");
    expect(formData.get("groupList[0].name")).toBe("Group 1");
    expect(formData.get("groupList[0].frequency.text")).toBe(null);
    expect(formData.get("groupList[0].companyIds")).toBe("1,2");
    expect(formData.get("groupList[0].fundIds")).toBe("126,127");
    expect(formData.get("groupList[0].receiverIds")).toBe(
      "<EMAIL>,<EMAIL>"
    );
    expect(formData.get("groupList[0].submitterIds")).toBe(
      "<EMAIL>,<EMAIL>"
    );
    expect(formData.get("groupList[0].externalUserIds")).toBe("");
    expect(formData.get("groupList[0].deletedAttachments")).toBe("2");
    expect(formData.get("groupList[0].reminderList[0].noOfDays")).toBe("2");
    expect(formData.get("groupList[0].reminderList[0].period")).toBe(
      "Before Due Date"
    );
    expect(formData.get("groupList[0].reminderList[0].deleted")).toBe("false");
    expect(formData.get("groupList[0].reminderList[0].id")).toBe("0");
    // Add other assertions as needed
  });

  it("onSaveRequest should call addOrUpdateDataRequest method of dataRequestService with the correct request model", () => {
    const dataRequestServiceStub: DataRequestService =
      fixture.debugElement.injector.get(DataRequestService);
    const toastrServiceStub: ToastrService =
      fixture.debugElement.injector.get(ToastrService);
    component.requestModel = {
      name: "Test Request",
      id: 0,
      requestId: "",
      isAutomatic: true,
      isActive: true,
      groupList: [],
    };
    component.groupModelList = [
      {
        id: 1,
        groupName: "Group 1",
        frequency: { text: "Quarterly", value: 1 },
        fundList: [],
        recipientList: [],
        submitterList: [],
        attachmentList: [],
        reminderList: [],
        deletedAttachments: [],
        expanded: true,
        deleted: false,
        companyList: [
          { companyId: 1, companyName: "Company 1" },
          { companyId: 2, companyName: "Company 2" },
        ],
        // Add other properties as needed
      },
      // Add other groups as needed
    ];
    const formData = component.prepareAddOrUpdateRequestModel();
    spyOn(component.router, "navigate").and.callThrough();
    spyOn(dataRequestServiceStub, "addOrUpdateDataRequest")
      .and.callThrough()
      .and.returnValue(of({ requestId: "123" }));

    component.onSaveRequest();

    expect(dataRequestServiceStub.addOrUpdateDataRequest).toHaveBeenCalledWith(
      formData
    );
    expect(component.disableSaveRequest).toBeTrue();
    expect(component.disableAddGroup).toBeFalse();
    expect(component.isLoader).toBeFalse();
    expect(component.router.navigate).toHaveBeenCalledWith([
      "/request-configuration-details/123",
    ]);
  });
});
