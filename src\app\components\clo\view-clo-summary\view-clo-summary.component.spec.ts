import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ViewCloSummaryComponent } from './view-clo-summary.component';
import { RouterTestingModule } from '@angular/router/testing';
import { FormBuilder } from '@angular/forms';
import { DatePipe } from '@angular/common';
import { CloService } from '../../../services/clo.service';
import { ToastrService } from 'ngx-toastr';
import { CommonSubFeaturePermissionService } from 'src/app/services/subPermission.service';
import { PanelbarItemService } from '../shared/panelbar-item/panelbar-item.service';
import { of, throwError } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { TOASTER_MSG } from 'src/app/common/constants';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { FlatTableComponent } from '../flat-table/flat-table.component';
import { TabStripModule } from '@progress/kendo-angular-layout';
import { GridModule } from '@progress/kendo-angular-grid';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { InvestCompanyService } from '../investmentcompany/investmentcompany.service';
import { AuditLogService } from 'src/app/components/clo/services/audit-log.service';

describe('ViewCloSummaryComponent', () => {
  let component: ViewCloSummaryComponent;
  let fixture: ComponentFixture<ViewCloSummaryComponent>;
  let cloService: jasmine.SpyObj<CloService>;
  let toastrService: jasmine.SpyObj<ToastrService>;
  let router: jasmine.SpyObj<Router>;
  let subPermissionService: jasmine.SpyObj<CommonSubFeaturePermissionService>;
  let panelbarItemService: jasmine.SpyObj<PanelbarItemService>;

  const mockCLOData = {
    uniqueID: '123',
    companyID: 1,
    companyName: 'Test Company',
    domicile: 'US'
  };

  const mockPermissions = [
    { subFeatureId: 82, canView: true, canEdit: true }, // CLOSummary
    { subFeatureId: 83, canView: true, canEdit: false }, // CapitalStructure
    { subFeatureId: 84, canView: true, canEdit: false }  // Collateral
  ];

  const mockInvestCompanyService = jasmine.createSpyObj('InvestCompanyService', [
    'getFootnote',
    'getTableData',
    'saveTableData',
    'getStaticTableData'
  ]);

  const mockAuditLogService = jasmine.createSpyObj('AuditLogService', ['logActivity']);

  beforeEach(async () => {
    // Spy on console.error
    spyOn(console, 'error');

    // Setup mock responses
    mockInvestCompanyService.getFootnote.and.returnValue(of([]));
    mockInvestCompanyService.getTableData.and.returnValue(of({
      table: [],
      footnote: []
    }));
    mockInvestCompanyService.saveTableData.and.returnValue(of({}));
    mockInvestCompanyService.getStaticTableData.and.returnValue(of([]));

    const cloServiceSpy = jasmine.createSpyObj('CloService', ['getCloById', 'currentData', 'getTabList']);
    const toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['error']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const subPermissionServiceSpy = jasmine.createSpyObj('CommonSubFeaturePermissionService', 
      ['getCommonSubFeatureAccessPermissions']);
    const panelbarServiceSpy = jasmine.createSpyObj('PanelbarItemService', ['getTabList', 'updateTableVisibility']);

    await TestBed.configureTestingModule({
      declarations: [ 
        ViewCloSummaryComponent,
        FlatTableComponent  // Add FlatTableComponent to declarations
      ],
      imports: [ 
        RouterTestingModule,
        TabStripModule,
        GridModule,
        HttpClientTestingModule // Add HttpClientTestingModule
      ],
      providers: [
        FormBuilder,
        DatePipe,
        { provide: CloService, useValue: cloServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: CommonSubFeaturePermissionService, useValue: subPermissionServiceSpy },
        { provide: PanelbarItemService, useValue: panelbarServiceSpy },
        { provide: InvestCompanyService, useValue: mockInvestCompanyService }, // Add mock InvestCompanyService
        { provide: AuditLogService, useValue: mockAuditLogService },
        { provide: 'BASE_URL', useValue: 'http://localhost:4200' }, // Add BASE_URL provider
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({ get: (key) => '123' }),
            queryParams: of({ name: 'Test Company' })
          }
        }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA] // Add schemas to handle unknown elements
    }).compileComponents();

    cloService = TestBed.inject(CloService) as jasmine.SpyObj<CloService>;
    toastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    subPermissionService = TestBed.inject(CommonSubFeaturePermissionService) as jasmine.SpyObj<CommonSubFeaturePermissionService>;
    panelbarItemService = TestBed.inject(PanelbarItemService) as jasmine.SpyObj<PanelbarItemService>;
  });

  beforeEach(() => {
    cloService.getCloById.and.returnValue(of(mockCLOData));
    cloService.currentData = of('123');
    cloService.getTabList.and.returnValue(of([
      {
        tabId: 1,
        tabName: 'Summary',
        isActive: true,
        tableList: [],
        subTabList: [
          { tabId: 11, tabName: 'Sub Tab 1', tableList: [] }
        ]
      },
      {
        tabId: 2,
        tabName: 'Capital Structure',
        isActive: false,
        tableList: [],
        subTabList: [
          { tabId: 21, tabName: 'Sub Tab 2', tableList: [] }
        ]
      }
    ]));
    subPermissionService.getCommonSubFeatureAccessPermissions.and.returnValue(of(mockPermissions));
    panelbarItemService.getTabList.and.returnValue(of([]));
    panelbarItemService.updateTableVisibility.and.returnValue(undefined);


    fixture = TestBed.createComponent(ViewCloSummaryComponent);
    component = fixture.componentInstance;
    component.CloPerformanceIndicatorTabs = [{ tabId: 1 }, { tabId: 2 }];
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct CLO data', fakeAsync(() => {
    component.ngOnInit();
    tick();
    
    expect(component.uniqueId).toBe('123');
    expect(component.CLOModel).toEqual(mockCLOData);
    expect(cloService.getCloById).toHaveBeenCalledWith('123');
  }));

  it('should handle CLO not found error', fakeAsync(() => {
    cloService.getCloById.and.returnValue(of(null));
    component.ngOnInit();
    tick();

    expect(toastrService.error).toHaveBeenCalledWith(
      TOASTER_MSG.CLO_NOT_FOUND, 
      '', 
      { positionClass: TOASTER_MSG.POS_CENTER }
    );
    expect(router.navigate).toHaveBeenCalledWith(['/clo-list']);
  }));

  it('should set active tab based on permissions', fakeAsync(() => {
    component.ngOnInit();
    tick();

    expect(component.activeTab).toBe(component.TAB_NAMES.CLO_SUMMARY);
    expect(component.canViewCLOSummary).toBeTrue();
  }));

  it('should handle API error when fetching CLO data', fakeAsync(() => {
    cloService.getCloById.and.returnValue(throwError(() => new Error('API Error')));
    component.ngOnInit();
    tick();

    expect(console.error).toHaveBeenCalledWith('Error fetching investment company list', jasmine.any(Error));
  }));

  it('should redirect to edit CLO when user has permission', () => {
    component.canEditCLOSummary = true;
    component.CLOModel = mockCLOData;
    
    component.redirectToEditClo();

    expect(router.navigate).toHaveBeenCalledWith(
      ['/add-clo'], 
      { 
        queryParams: { 
          name: mockCLOData.companyName, 
          id: mockCLOData.companyID, 
          uniqueId: mockCLOData.uniqueID
        } 
      }
    );
  });

  it('should show error when user lacks edit permission', () => {
    component.canEditCLOSummary = false;
    
    component.redirectToEditClo();

    expect(toastrService.error).toHaveBeenCalled();
    expect(router.navigate).not.toHaveBeenCalled();
  });

  it('should handle tab selection correctly', () => {
    const event = { index: 0 };
    component.onTabSelect(event);
    component.CloPerformanceIndicatorTabs=[{tabId:1},{tabId:2}];
    component.activePerformanceIndicatorTab=1;
    expect(component.activePerformanceIndicatorTab)
      .toBe(component.CloPerformanceIndicatorTabs[0].tabId);
  });

  it('should check tab visibility based on permissions', () => {
    // Initialize all permissions to false
    component.canViewCLOSummary = false;
    component.canViewCapitalStructure = false;
    component.canViewCollateral = false;
    component.canViewKeyKPI = false;
    component.canViewOvercollateralisationTest = false;
    component.canViewCollateralQualityTest = false;
    component.canViewcCLOVersusCLOSector = false;
    component.canViewCLODistributionsToDate = false;

    // Test CLO Summary tab visibility
    expect(component.isTabVisible(component.TAB_NAMES.CLO_SUMMARY)).toBeFalse();
    component.canViewCLOSummary = true;
    expect(component.isTabVisible(component.TAB_NAMES.CLO_SUMMARY)).toBeTrue();
  });

  it('should get first available tab correctly', () => {
    // Reset all permissions
    component.canViewCLOSummary = false;
    component.canViewCapitalStructure = false;
    component.canViewCollateral = false;
    component.canViewKeyKPI = false;
    component.canViewOvercollateralisationTest = false;
    component.canViewCollateralQualityTest = false;
    component.canViewcCLOVersusCLOSector = false;
    component.canViewCLODistributionsToDate = false;

    // Test CLO Performance Indicator case
    component.canViewKeyKPI = true;
    expect(component.getFirstAvailableTab()).toBe(component.TAB_NAMES.CLO_PERFORMANCE_INDICATOR);

    // Test CLO Summary case
    component.canViewCLOSummary = true;
    expect(component.getFirstAvailableTab()).toBe(component.TAB_NAMES.CLO_SUMMARY);
  });
});
