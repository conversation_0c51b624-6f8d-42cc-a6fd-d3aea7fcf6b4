<div class="row mr-0 ml-0 firm-detail-section">
    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12 pr-0 pl-0 pb-3">
        <div class="row mr-0 ml-0 card card-main static-card">
            <div class="col-md-12 col-sm-12 col-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0">
                <div class="static-pc-section fund-detail section1-height chart-area row mr-0 ml-0 mb-0">
                    <div class="col-12 pr-0 pl-0">
                        <div class="row mr-0 ml-0 pb-1 static-bg rowfirm-h firmdet-row-h">
                            <div class="col-12 pr-0 pl-0 chart-title pc-section-header">
                                <div class="float-right margin-right fund-static-header">
                                    <a
                    id="edit-firm"
[routerLink]="['/add-firm',model.encryptedFirmID]" title="Edit "><img alt=""
                                            src="assets/dist/images/EditIcon.svg"></a>
                                </div>
                            </div>
                        </div>
                        <div class="row mr-0 ml-0">
                            <div class="col-sm-12 pr-3 pt-3 pb-2">
                                <div class="line-wrapper">
                                    <span class="TextTruncate" title="Firm Information">Firm Information</span>
                                    <div class="line ml-2"></div>
                                </div>
                            </div>
                            <div class="row mr-0 ml-0pl-3 pr-3 Fund-section col-12 ">
                                <div class="col-6">
                                    <div class="row">
                                        <div class="col-sm-2 pr-0 label-color"><label class="TextTruncate d-block"
                                                for="FirmName" title="Firm Name">Firm
                                                Name</label></div>
                                        <div class="col-sm-10 firm-value-padding">
                                            <span class="detail-sec"><label class="TextTruncate d-block"
                                                    title="{{model.firmName?model.firmName:'NA'}}">{{model.firmName?model.firmName:"NA"}}</label></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="row">
                                        <div class="col-sm-2 firm-label-padding">
                                            <label for="Website" class="TextTruncate d-block"
                                                title="Website">Website</label>
                                        </div>
                                        <div class="col-sm-10 firm-value-padding">

                                            <span class="detail-sec"> <label class="d-block TextTruncate"><a
                                                        class="click-view " href="//{{model.website}}" target="_blank"
                                                        title="{{model.website?model.website:'NA'}}">{{model.website?model.website:"NA"}}</a></label></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mr-0 ml-0">
                            <div class="col-sm-12 pr-3 custom1-pt pb-2">
                                <div class="line-wrapper">
                                    <span class="TextTruncate" title="Business Information">Business Information</span>
                                    <div class="line ml-2"></div>
                                </div>
                            </div>
                            <div class="pl-3 pr-3  col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
                                <div class="TextTruncate  pr-0 pl-0">
                                    <label class="business-section"
                                        title="{{model.businessDescription?model.businessDescription:'NA'}}">{{model.businessDescription?model.businessDescription:"NA"}}</label>
                                </div>
                            </div>
                        </div>
                        <div class="row mr-0 ml-0">
                            <div class="col-sm-12 pt-2 pr-3 custom1-pt pb-2">
                                <div class="line-wrapper">
                                    <span class="TextTruncate" title="Geographic Locations"> Geographic Locations</span>
                                    <div class="line ml-2"></div>
                                </div>
                            </div>
                            <div class="row mr-0 ml-0 Fund-section col-12 ">
                                <div class="col-12 pr-0 pl-0">
                                    <div class="card pl-0 pr-0">
                                        <div class="card-body mb-0">
                                            <div class="table-responsive card-border">
                                                <kendo-grid
                                                    class="k-grid-border-right-width k-grid-outline-none custom-kendo-cab-table-grid"
                                                    [data]="model.geographicLocations?? []">
                                                    <kendo-grid-column field="region" title="Region" [width]="200">
                                                        <ng-template kendoGridCellTemplate let-dataItem>
                                                            {{dataItem.region?.region ? dataItem.region.region : 'NA'}}
                                                        </ng-template>
                                                    </kendo-grid-column>
                                                    <kendo-grid-column field="country" title="Country" [width]="200">
                                                        <ng-template kendoGridCellTemplate let-dataItem>
                                                            {{dataItem.country?.country ? dataItem.country.country :
                                                            'NA'}}
                                                        </ng-template>
                                                    </kendo-grid-column>
                                                    <kendo-grid-column field="state" title="State" [width]="200">
                                                        <ng-template kendoGridCellTemplate let-dataItem>
                                                            {{dataItem.state?.state ? dataItem.state.state : 'NA'}}
                                                        </ng-template>
                                                    </kendo-grid-column>
                                                    <kendo-grid-column field="city" title="City" [width]="200">
                                                        <ng-template kendoGridCellTemplate let-dataItem>
                                                            {{dataItem.city?.city ? dataItem.city.city : 'NA'}}
                                                        </ng-template>
                                                    </kendo-grid-column>
                                                    <ng-template kendoGridNoRecordsTemplate>
                                                        <app-empty-state class="finacials-beta-empty-state"
                                                            [isGraphImage]="false"></app-empty-state>
                                                    </ng-template>
                                                </kendo-grid>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mr-0 ml-0">
                            <div class="col-sm-12 pr-3 pt-3 pb-2">
                                <div class="line-wrapper">
                                    <span title="Investment Professionals">Investment Professionals</span>
                                    <div class="line ml-2"></div>
                                </div>
                            </div>
                            <div class="row mr-0 ml-0 Fund-section col-12 ">
                                <div class="col-12 pl-0 pr-0">
                                    <div class="card pl-0 pr-0">
                                        <div class="card-body mb-0">
                                            <div class="table-responsive card-border">
                                                <kendo-grid
                                                    class="k-grid-border-right-width  k-grid-outline-none custom-kendo-cab-table-grid"
                                                    [data]="model.firmEmployees?? []">
                                                    <kendo-grid-column field="employeeName" title="Employee Name"
                                                        [width]="200">
                                                        <ng-template kendoGridCellTemplate let-dataItem>
                                                            {{dataItem.employeeName ? dataItem.employeeName : 'NA'}}
                                                        </ng-template>
                                                    </kendo-grid-column>
                                                    <kendo-grid-column field="designation" title="Designation"
                                                        [width]="200">
                                                        <ng-template kendoGridCellTemplate let-dataItem>
                                                            {{dataItem.designation?.designation ?
                                                            dataItem.designation.designation : 'NA'}}
                                                        </ng-template>
                                                    </kendo-grid-column>
                                                    <kendo-grid-column field="emailId" title="Email" [width]="200">
                                                        <ng-template kendoGridCellTemplate let-dataItem>
                                                            {{dataItem.emailId ? dataItem.emailId : 'NA'}}
                                                        </ng-template>
                                                    </kendo-grid-column>
                                                    <kendo-grid-column field="education" title="Education"
                                                        [width]="200">
                                                        <ng-template kendoGridCellTemplate let-dataItem>
                                                            {{dataItem.education ? dataItem.education : 'NA'}}
                                                        </ng-template>
                                                    </kendo-grid-column>
                                                    <kendo-grid-column field="pastExperience" title="Past Experience"
                                                        [width]="200">
                                                        <ng-template kendoGridCellTemplate let-dataItem>
                                                            {{dataItem.pastExperience ? dataItem.pastExperience : 'NA'}}
                                                        </ng-template>
                                                    </kendo-grid-column>
                                                    <ng-template kendoGridNoRecordsTemplate>
                                                        <app-empty-state class="finacials-beta-empty-state"                                                           
                                                            [isGraphImage]="false"></app-empty-state>
                                                    </ng-template>
                                                </kendo-grid>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mr-0 ml-0 header-section header-performance">
            <div class="header-section col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                <div class="fund-header header-style pb-2 TextTruncate" title="Fund Details">
                    Fund Details
                </div>
            </div>

            <div
                class="col-md-12 col-sm-12 col-12 col-lg-12 col-xl-12 col-xs-12 mr-0 ml-0 card card-main static-card1 pb-4 ">
                <div class="row pr-0 pl-0">
                    <div
                        class="col-12 pr-0 pl-0 static-pc-section fund-detail section1-height chart-area row mr-0 ml-0 mb-0 ">
                        <div class="row mr-0 chart-title pc-section-header ml-0 pb-1 static-bg firmdetail-h">
                            <div class="float-right">
                                <div class="d-inline-block search">
                                    <span class="fa fa-search fasearchicon"></span>
                                    <input type="text" pInputText
                                        class="search-text-company companyListSearchHeight TextTruncate"
                                        placeholder="Search" (input)="filterGrid($event.target.value)">
                                </div>
                            </div>
                        </div>
                        <div class="col-12 pl-0 pr-0">
                            <div class="row mr-0 ml-0">
                                <kendo-grid [data]="flattenedFunds?? []" [filter]="gridFilter"
                                    class="k-grid-border-right-width  k-grid-outline-none custom-kendo-cab-table-grid">
                                    <kendo-grid-column field="fundName" title="Fund Name" [width]="200">
                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            <a *ngIf="dataItem.fundName != ''" class="click-view TextTruncate"
                                                [routerLink]="['/fund-details', dataItem.encryptedFundId]">
                                                {{dataItem.fundName ? dataItem.fundName : 'NA'}}
                                            </a>
                                        </ng-template>
                                    </kendo-grid-column>
                                    <kendo-grid-column field="strategy" title="Strategy" [width]="200">
                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{dataItem?.strategy ? dataItem.strategy :
                                            'NA'}}
                                        </ng-template>
                                    </kendo-grid-column>
                                    <kendo-grid-column field="accountType" title="Account Type"
                                        [width]="200">
                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{dataItem?.accountType ?
                                            dataItem.accountType : 'NA'}}
                                        </ng-template>
                                    </kendo-grid-column>
                                    <kendo-grid-column field="sector" title="Sector" [width]="200">
                                        <ng-template kendoGridCellTemplate let-dataItem>
                                            {{dataItem?.sector ? dataItem.sector : 'NA'}}
                                        </ng-template>
                                    </kendo-grid-column>
                                    <ng-template kendoGridNoRecordsTemplate>
                                        <app-empty-state class="finacials-beta-empty-state" [isGraphImage]="false"></app-empty-state>
                                    </ng-template>
                                </kendo-grid>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>