@import "../../../../_variables";
@import "../../../../assets/dist/css/font.scss";

.data-extraction-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: $Primary-35;
  border: 1px solid $Neutral-Gray-05;
  border-radius: 8px;
  padding: 12px;
  gap: 16px;
  width: 100%;
}

.left-content {
  display: flex;
  gap: 16px;
}

.company-header {
  display: flex;
  gap: 8px;
  align-items: center;
}

.logo {
  width: 50px;
  height: 50px;
  border-radius: 4px;
}

.logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.company-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.company-name-fetch-file {
  @extend .Heading2-M;
}

.extra-info {
  display: flex;
  gap: 4px;
  align-items: center;
}

.reportingPeriod {
  @extend .Caption-R;
}

.action-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.extract-icon {
  width: 16px;
  height: 16px;
}

.documents-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  min-height: 32px;
  padding: 12px 0;
  box-sizing: border-box;
}

.title-section {
  flex: 1;
  @extend .Body-B
}

.button-section {
  display: flex;
  align-items: center;
}

.upload-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 16px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  box-shadow:none !important;
}

.left-icon {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

.button-text {
  color: $nep-dark-blue-link;
}

.hidden-file-input {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
  overflow: hidden;
}

.pdf-icon {
  color: $Negative-100;
}

.data-extraction-icon {
  width: 32px;
  height: 32px;
}

.logo-bg-color {
  color: $nep-white;
  padding: 16px;
  text-align: center;
}
.document-disable{
  background-color: $Neutral-Gray-20;
}
.files-container{
  width: calc(100% - 16px);
  .pages-container {
    min-width: 146px;
    max-width: 33.33%;
    .page-section{
      width:100%;
    }
    .page-text{
      width:100%;
      margin-right: 8px;
      &:last-child{
        margin-right: 0px;
      }
    }
  }
}

.no-content-section {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.k-popover-header-radius{
  border-top-left-radius: 8px !important;
  border-top-right-radius: 8px !important;
}
.close-icon {
  cursor: pointer;
}
.k-popover-body-radius{
  border-bottom-left-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}
.file-name{
  width:100%;i
  a{
    &:hover{
      color: $Primary-78;
    }
  }
}
::ng-deep .k-popover{
  @extend .k-popover-header-radius;
  border-color: transparent !important;
  border:none !important;
  .k-popover-header{
    background-color: $Primary-40 !important;
    color: $nep-dark-h !important ;
    padding: 16px 24px !important;
    @extend .k-popover-header-radius;
  }
  .k-popover-body{
    padding: 20px;
    color: $Neutral-Gray-100 !important;
    @extend .k-popover-body-radius;
    @extend .Body-R
  }
}
::ng-deep kendo-popover {
  .k-popover {
    &.k-popup {
      border-color:none !important;
      @extend .k-popover-header-radius;
      @extend .k-popover-body-radius;
    }
  }
}
.file-name {
  a {    
    &:hover {
      color: $nep-primary !important;
    }
  }
}
.selection-popup {
  position: fixed;
  bottom: 24px;
  left: 48px;
  right: 48px;
  background-color: $nep-white;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12);
  border: 1px solid $Neutral-Gray-05;
  border-radius: 8px;
}
.selection-content {
  .dropdown-group {
    label {
      white-space: nowrap;
      color: $Neutral-Gray-80;
    }
  }
}
.selection-popup .apply-btn:disabled {
  cursor: not-allowed;
}
.custom-gap{
  gap: 32px;
}
.cross-icon{
  cursor: pointer 
}
.text-color{
  color: $Neutral-Gray-30;
}
.chkbx-border{
  border-color: $Neutral-Gray-80;
}
