<div class="row mt-3 sdg-section">
    <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0 c-header align-items-center sdg-header">
        <div class="col">
            <span class="Heading2-M text-truncate-width TextTruncate" title="{{sdgTitle}}">
                {{sdgTitle}}
            </span>
        </div>
    </div>
    <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0 content-body-r-section panel-section sdg-panel">
        <div class="row mr-0 ml-0 no-data-container" *ngIf="sdgImages.length === 0">
            <ng-container
                *ngTemplateOutlet="noImageTemplate; context: { message: 'Click on “Browse” button to start uploading images' ,icon:'no-image-pc' }"></ng-container>
        </div>
        <div class="sdg-image-container" *ngIf="sdgImages.length > 0">
            <div class="row sdg-image-container-div">
                <div *ngFor="let image of sdgImages; let i = index" class="col-12 col-sm-6 col-md-4 col-lg-3 sdg-image">
                    <img src="assets/dist/images/cross-icon.svg" alt="No Content" class="remove-image-btn" id="remove-image-btn-{{i}}" (click)="removeImage(i)" />
                    <img [src]="image?.url" [alt]="image?.file?.name" class="uploaded-image" id="uploaded-image-{{i}}" />
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0 content-body-r-section panel-section sdg-footer button-footer">
        <div class="float-right btn-comment-section">
            <button kendoButton [disabled]="uploadAndSavaEnabled" id="uploadAndSave" class="kendo-custom-button Body-R apply-btn mr-2 upload" fillMode="outline" themeColor="primary" (click)="uploadAndSave()">
                Upload & Save
            </button>
            <label for="sdgUpload" kendoButton class="kendo-custom-button Body-R apply-btn btn-width-102 k-button-solid-primary browse-button" title="Browse">Browse</label>
            <input type="file" name="file" #sdgUpload id="sdgUpload" (change)="onBrowseImageChange($event.target.files)" multiple class="multiple-file-drop" accept="image/*" hidden />
        </div>
    </div>
    <app-loader-component *ngIf="isLoading"></app-loader-component>
</div>
<p class="allowed-ext pt-2">Allowed file formats : .jpg, .jpeg, .png, .bmp, .tiff, .tif, .svg, .ico, .heic, .heif, .jfif, .pjpeg, .pjp.</p>
<ng-template #noImageTemplate let-message="message" let-icon="icon">
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 no-content-section">
        <div class="text-center">
            <img src="assets/dist/images/{{icon}}.svg" alt="No Content" class="no-content-image">
        </div>
        <div class="text-center no-content-text pt-3 pb-2 Body-M no-comment-font">
            {{ message }}
        </div>
    </div>
</ng-template>