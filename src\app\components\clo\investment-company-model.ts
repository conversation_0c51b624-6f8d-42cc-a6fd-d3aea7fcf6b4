  export class InvestmentCompany {
    id: number;
    companyName: string;
    domicile: string;
    incorporationDate: string;
    firstClose: string;
    investmentPeriodEndDate: string;
    maturityDate: string;
    custodian: string;
    legalCounsel: string;
    finalClose: string;
    commitments: string;
    administrator: string;
    portfolioAdvisor: string;
    baseCurrency: string;
    listingAgent: string;
    investmentSummary: string;
    
    constructor(init?: Partial<InvestmentCompany>) {
      Object.assign(this, init);
    }
  }
