import { Component, OnInit, Input, ElementRef, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { InvestCompanyService } from "../investmentcompany/investmentcompany.service";
import { ToastrService } from "ngx-toastr";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { EditCellDialogComponent } from "./edit-cell-dialog.component";
import { UploadPerformanceData } from "./upload-performance-data.component";
import { TableCellUpdate } from "../models/table-cell-update.model";
import { TableFootnoteModel } from "../models/table-footnote.model";
import { AuditLogDialogComponent } from "../audit-log-dialog/audit-log-dialog.component";
import { AuditLogService, AuditLogQuery } from "../services/audit-log.service";
import { CloListService } from "../clo-list/clo-list.service";
import { ErrorMessage } from "src/app/services/miscellaneous.service";
import { CommonSubFeaturePermissionService } from "src/app/services/subPermission.service";
import { FeatureTableMapping } from "src/app/common/constants";
import { CloService } from "src/app/services/clo.service";
import { PanelbarItemService } from "../shared/panelbar-item/panelbar-item.service";
// Interface for type safety (adjust properties based on your API response)
interface GridData {
  id: string; // Changed from number to string
  [key: string]: any; // for dynamic columns
}

@Component({
  selector: "app-flat-table",
  templateUrl: "./flat-table.component.html",
  styleUrls: ["./flat-table.component.scss"],
})
export class FlatTableComponent implements OnInit {
  @Input() data: GridData[] = [];
  @Input() isCompositeRowFilterRequired: boolean = false;
  @Input() columns: Array<{
    field: string;
    header: string;
    title?: string;
    width?: number;
    group?: string;
    parent?: string;
    isStaticTableHeader?: boolean;
    isUniqueIdentifier?: boolean;
  }> = [];
  @Input() tableType: "regular" | "pivot" = "regular";
  @Input() tableName: string = "";
  @Input() tableTitle: string = "";
  @Input() isStaticTable: boolean = false;
  @Input() isShouldFetchDataOnLoad: boolean = false;
  @Input() quillConfig: any;
  @Input() companyID: string = "";
  @Input() openUploadDialog: boolean = false;
  @Input() cloId: string = "";
  @Input() cloDomcile: string = "";
  @Input() canExport: boolean = true;
  @Input() canImport: boolean = true;
  @Input() canEdit: boolean = true;
  @ViewChild("quillEditor", { static: false }) quillEditor: ElementRef;
  staticTableHeader?: {
    field: string;
    header: string;
    title?: string;
    width?: number;
    group?: string;
    parent?: string;
    isStaticTableHeader?: boolean;
  };
  normalizedTableName: string;
  CLOModel: any;
  footnotes = [
    { name: "FootNote", newComment: "", isExpanded: false, isEdit: false },
  ];
  commentarylist: {
    id: number;
    name: string;
    newComment: string;
    commentaryType: string;
    glicommentry?: string;
    marketCommentry?: string;
    isExpanded: boolean;
    isEdit: boolean;
  }[] = [
    {
      id: 0,
      name: "GLI Commentary",
      newComment: "",
      commentaryType: "GLI Commentary",
      glicommentry: "",
      marketCommentry: "",
      isExpanded: false,
      isEdit: false,
    },
    {
      id: 0,
      name: "Market Commentary - Global loans and CLOs",
      newComment: "",
      commentaryType: "Market Commentary - Global loans and CLOs",
      glicommentry: "",
      marketCommentry: "",
      isExpanded: false,
      isEdit: false,
    },
  ];
  DOWNLOAD_SUCCESS_MESSAGE =
    "You have successfully downloaded the Currency Exposure";
  successMessage: string = "You have successfully updated the cell value";
  successTableMessage: string = "You have successfully Deleted";
  @Input() tableId=0;
  public TAB_NAMES = {
    Aggregate_CLO_Metrics:[7,8]
  };
  isMenuOpen:boolean = false;
  
menuItems: any[] = [
      { text: 'Delete Table'}
    ];
  
  @ViewChild('anchor', { static: true }) anchor: ElementRef;

  showPopup:boolean = false;
  @Input() isCLO:boolean = false;

  constructor(
    private readonly investCompanyService: InvestCompanyService,
    private readonly auditLogService: AuditLogService,
    private readonly toastrService: ToastrService,
    private readonly modalService: NgbModal,
    private readonly router: Router,
    private readonly subPermissionService: CommonSubFeaturePermissionService,
    private readonly viewCloService: CloService,
    private readonly cloListService: CloListService,
    private pagePanelService: PanelbarItemService
  ) {}

  private readonly ALLOWED_FILE_TYPES = ["xlsx", "xls"];
  private readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  downloadTemplate(): void {
    if (!this.canImport) {
      this.toastrService.error(ErrorMessage.NoAccess, "", {
        positionClass: "toast-center-center",
      });
      return;
    }

    if (!this.tableName) {
      this.toastrService.error("Table name is required");
      return;
    }
    // Add loading state if needed
    const loadingToastRef = this.toastrService.info(
      "Downloading template...",
      "",
      {
        disableTimeOut: true,
      }
    );

    this.investCompanyService.downloadTemplate(this.tableName).subscribe({
      next: (response: Blob) => {
        if (response.size === 0) {
          this.toastrService.error("Empty file received");
          return;
        }
        // Create blob URL and trigger download
        const blob = new Blob([response], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = this.sanitizeUrl(url);
        link.download = `${this.sanitizeFileName(this.tableName)}_template.xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);
        // Remove loading toast and show success
        loadingToastRef.toastRef.close();
        this.toastrService.success("Template downloaded successfully");
      },
      error: (error) => {
        loadingToastRef.toastRef.close();
        this.toastrService.error("Error downloading template");
      },
    });
  }

  public domicileOptions: string[] = ["EU", "US"];
  public selectedDomicile: string = "EU";
  GLI_Portfolio_Composition=FeatureTableMapping.TABLES_NAME.GLI_Portfolio_Composition[0];
  NAV_DISTRIBUTION=FeatureTableMapping.TABLES_NAME.NAV_DISTRIBUTION[0];
  CLO_Distributions_To_Date=FeatureTableMapping.TABLES_NAME.CLO_Distributions_To_Date[0];
  Aggregate_CLO_Metrics_EU=FeatureTableMapping.TABLES_NAME.Aggregate_CLO_Metrics_EU[0];
  Aggregate_CLO_Metrics_US=FeatureTableMapping.TABLES_NAME.Aggregate_CLO_Metrics_US[0];
  Capital_Structure=FeatureTableMapping.TABLES_NAME.Capital_Structure[0];

  ngOnInit(): void {
    if (this.tableName && this.companyID) {
      this.loadFootnote();
      this.loadData();
    }
  }

  checkPermissionAccess(permission: any[], permissionType: string): boolean {
    return permission.map((x) => x[permissionType]).includes(true);
  }

  onDomicileChange(value: string): void {
    if (this.tableTitle === "Aggregate CLO Metrics") {
      this.selectedDomicile = value;
      this.tableName = `Aggregate_CLO_Metrics_${value}`;
      this.data = [];
      this.columns = [];
      this.loadData(); // Reload grid data with new tableName
    }
  }

  private loadFootnote(): void {
    this.investCompanyService
      .getFootnote(this.tableName, this.companyID)
      .subscribe({
        next: (response) => {
          if (response && response.footnoteContent) {
            // Update the footnote content in the existing footnotes array
            if (this.footnotes.length > 0) {
              this.footnotes[0].newComment = response.footnoteContent;
              this.footnotes[0].isExpanded =
                response.footnoteContent.trim().length > 0;
            }
          }
        },
        error: (error) => {
          this.toastrService.error("Error loading footnote");
        },
      });
  }

  toggleEdit(clo: any) {
    clo.isEdit = !clo.isEdit;
  }

  expandPanel(clo: any) {
    this.footnotes.forEach((item) => {
      if (item !== clo) {
        item.isExpanded = false;
        item.isEdit = false;
      }
    });
    clo.isExpanded = !clo.isExpanded;
  }

  handleSave(footnote: any): void {
    const footnoteContent = footnote.newComment || "";
    const footnotePayload: TableFootnoteModel = {
      footenote_ID: 0, // Set to 0 for new footnotes or actual ID for updates
      footnoteMapping: this.tableName,
      footnoteIdentifier: String(this.companyID),
      footnoteContent: footnoteContent, // Using HTML content directly
    };

    this.investCompanyService.saveFootnote(footnotePayload).subscribe({
      next: (response) => {
        this.toastrService.success("Footnote saved successfully", "", {
          positionClass: "toast-center-center",
        });
        footnote.isEdit = false;
      },
      error: (error) => {
        this.toastrService.error("Error saving footnote");
      },
    });
  }

  handleCancel(footnote: any): void {
    footnote.isEdit = false;
    this.loadFootnote();
  }

  handleReset(clo: any): void {
    clo.newComment = "";
  }

  loadData() {
    this.investCompanyService
      .getTableData(this.tableName, String(this.companyID))
      .subscribe({
        next: (tableData) => {
          this.data=[];
            this.data = tableData.data.map((row) => {
              const processedRow = { ...row };
              Object.keys(processedRow).forEach((key) => {
                if (
                  processedRow[key] === null ||
                  typeof processedRow[key] === "object"
                ) {
                  processedRow[key] = "_ _";
                }
              });
              return processedRow;
            });
            this.updateTableColumns(tableData);
          if(!this.isStaticTable)
            this.adjustWidthBasedOnData();
        },
        error: (error) => {
          this.toastrService.error("Error refreshing data", "");
        },
      });
  }
  navColumns: any[] = []
  distColumns: any[] = [];
  private updateTableColumns(tableData: { data: any[]; columns: any[] }) {
    this.columns = tableData.columns;
    if (this.tableId == FeatureTableMapping.TABLES_NAME.NAV_DISTRIBUTION[0]) {
      this.navColumns = this.columns.filter((column) => !column.field.toLocaleLowerCase().includes("distribution") && !column.isStaticTableHeader);
      this.distColumns = this.columns.filter((col) => col.field.toLocaleLowerCase().includes("distribution"));
    }
    if (this.columns.length > 0 && this.columns[0].isStaticTableHeader) {
      this.staticTableHeader = this.columns[0];
      this.columns = this.columns.slice(1); // Remove the static table header from columns
    }
  }
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (!file) return;

    // Security check for file type
    const fileExtension = file.name.split(".").pop()?.toLowerCase();
    if (!this.ALLOWED_FILE_TYPES.includes(fileExtension || "")) {
      this.toastrService.error(
        "Invalid file type. Only Excel files are allowed."
      );
      return;
    }

    // Security check for file size
    if (file.size > this.MAX_FILE_SIZE) {
      this.toastrService.error("File size exceeds 5MB limit");
      return;
    }

    if (file) {
      const fileExtension = file.name.split(".").pop()?.toLowerCase();
      if (fileExtension === "xlsx" || fileExtension === "xls") {
        this.investCompanyService
          .uploadExcelFile(file, this.tableName, String(this.companyID))
          .subscribe({
            next: (response) => {
              this.investCompanyService
                .getTableData(this.tableName, String(this.companyID))
                .subscribe({
                  next: (tableData) => {
                    this.data = tableData.data;
                    this.updateTableColumns(tableData);
                    if(!this.isStaticTable)
                      this.adjustWidthBasedOnData();
                  },
                  error: (error) => {
                    this.toastrService.error("Error refreshing data");
                  },
                });
            },
            error: (error) => {
              this.toastrService.error(error.error.message, "", {
                positionClass: "toast-center-center",
              });
            },
          });
      } else {
        this.toastrService.warning("Please select only Excel files", "", {
          positionClass: "toast-center-center",
        });
      }
    }
  }

  onCellClick(
    dataItem: any,
    column: any,
    isNavTbl: boolean,
    isAudit: boolean,
    isPivot: boolean = false
  ): void {
    if (!this.canEdit) {
      this.toastrService.error(ErrorMessage.NoAccess, "", {
        positionClass: "toast-center-center",
      });
      return;
    }
     
    if (isAudit) {
      const uniqueIdentifierColumns = this.columns.filter(col => col.isUniqueIdentifier);
      const rowHeaderField = uniqueIdentifierColumns[0]?.field || this.columns[0].field;
     
      const RowIdentifier = this.getRowIdentifier(dataItem, uniqueIdentifierColumns, rowHeaderField);
      const query: AuditLogQuery = {
        rowIdentity: RowIdentifier,
        columnIdentity: column.actualColumnName,
        entity: this.tableName,
        companyId: String(this.companyID),
      };

      this.auditLogService.getAuditHistory(query).subscribe({
        next: (auditData) => {
          const dialogRef = this.modalService.open(AuditLogDialogComponent, {
            size: "lg",
            backdrop: "static",
          });
          
          Object.assign(dialogRef.componentInstance, {
            rowHeader: this.getRowIdentifier(dataItem, uniqueIdentifierColumns, rowHeaderField, ' & '),
            tableName: this.tableTitle,
            columnHeader: column.title || column.field,
            gridData: auditData,
          });
        },
        error: (error) => {
          this.toastrService.error("Error loading audit history");
        },
      });
      return;
    }

    // Regular edit cell dialog
    const dialogRef = this.modalService.open(EditCellDialogComponent);
    const firstColumnField = this.columns[0].field;
    const firstColumnValue = dataItem[firstColumnField];

    const cellInfo = {
      columnName: column.parent ? column.header : column.field,
      columnValue: dataItem[column.field],
      firstColumnValue: firstColumnValue,
      isNavTbl: isNavTbl || false,
    };

    dialogRef.componentInstance.oldValue = cellInfo;

    dialogRef.closed.subscribe((result: any) => {
      if (!result) return;

      const oldValue = dataItem[column.field];
      const firstColumnField = this.columns[0].field;
      const secondColumnField = this.columns[1]?.field;

      const rowIdArray = [String(dataItem[firstColumnField])];
      if (this.isCompositeRowFilterRequired && secondColumnField) {
        rowIdArray.push(String(dataItem[secondColumnField]));
      }

      const updatePayload: TableCellUpdate = {
        tableName: this.tableName,
        rowId: rowIdArray, // Send as array
        field: column.parent ? column.header : column.field,
        oldValue: String(oldValue),
        newValue: String(result.newValue),
        companyID: String(this.companyID),
        parent: column.parent || "NA",
        supportingDocuments: result.supportingDocument, // Match backend property name
        comment: result.comments,
      };

      this.investCompanyService.updateTableCellValue(updatePayload).subscribe({
        next: () => {
          // After successful update, refresh the grid data
          this.investCompanyService
            .getTableData(this.tableName, String(this.companyID))
            .subscribe({
              next: (tableData) => {
                this.data = tableData.data.map((row) => {
                  const processedRow = { ...row };
                  Object.keys(processedRow).forEach((key) => {
                    if (
                      processedRow[key] === null ||
                      typeof processedRow[key] === "object"
                    ) {
                      processedRow[key] = "_ _";
                    }
                  });
                  return processedRow;
                });
                this.updateTableColumns(tableData);
                if(!this.isStaticTable)
                  this.adjustWidthBasedOnData();
                this.toastrService.success(this.successMessage, "", {
                  positionClass: "toast-center-center",
                });
              },
              error: (error) => {
                this.toastrService.error("Error refreshing data", "", {
                  positionClass: "toast-center-center",
                });
              },
            });
        },
        error: (error) =>
          this.toastrService.error(error.error.error, "", {
            positionClass: "toast-center-center",
          }),
      });
    });
  }
  openUpload() {
    if (!this.canImport) {
      this.toastrService.error(ErrorMessage.NoAccess, "", {
        positionClass: "toast-center-center",
      });
      return;
    }
    this.openUploadDialog = true;
    const dialogRef = this.modalService.open(UploadPerformanceData);
    dialogRef.componentInstance.tableName = this.tableName;
    dialogRef.componentInstance.companyID = this.companyID;
    dialogRef.componentInstance.fileUploaded.subscribe((file: File) => {
      this.handleFileUpload(file);
    });
  }

  handleFileUpload(file: File): void {
    if (!file) return;

    // Security check for file type
    const fileExtension = file.name.split(".").pop()?.toLowerCase();
    if (!this.ALLOWED_FILE_TYPES.includes(fileExtension || "")) {
      this.toastrService.error(
        "Invalid file type. Only Excel files are allowed."
      );
      return;
    }

    // Security check for file size
    if (file.size > this.MAX_FILE_SIZE) {
      this.toastrService.error("File size exceeds 5MB limit");
      return;
    }

    this.investCompanyService
      .uploadExcelFile(file, this.tableName, String(this.companyID))
      .subscribe({
        next: (response) => {
          this.investCompanyService
            .getTableData(this.tableName, String(this.companyID))
            .subscribe({
              next: (tableData) => {
                // Process the data to replace null or object values with '_'
                this.data = tableData.data.map((row) => {
                  const processedRow = { ...row };
                  Object.keys(processedRow).forEach((key) => {
                    if (
                      processedRow[key] === null ||
                      typeof processedRow[key] === "object"
                    ) {
                      processedRow[key] = "_ _";
                    }
                  });
                  return processedRow;
                });
                this.updateTableColumns(tableData);
                if(!this.isStaticTable)
                  this.adjustWidthBasedOnData();
                this.toastrService.success(
                  `You have successfully uploaded the ${this.tableTitle}`,
                  "",
                  { positionClass: "toast-center-center" }
                );
              },
              error: (error) => {
                this.toastrService.error("Error refreshing data");
              },
            });
        },
        error: (error) => {
          this.toastrService.error(error.error.message, "", {
            positionClass: "toast-center-center",
          });
        },
      });
  }

  isFirstInGroup(column: any): boolean {
    if (!column.parent) return false;
    const groupColumns = this.columns.filter(
      (col) => col.parent === column.parent
    );
    return groupColumns[0] === column;
  }

  getColumnsForParent(parentName: string): any[] {
    return this.columns.filter((col) => col.parent === parentName);
  }

  exportToExcel(): void {
    if (!this.canExport) {
      this.toastrService.error(ErrorMessage.NoAccess, "", {
        positionClass: "toast-center-center",
      });
      return;
    }
    if (!this.tableName) {
      this.toastrService.error("Table name is required");
      return;
    }
    this.investCompanyService
      .exportTableData(this.tableName, String(this.companyID))
      .subscribe({
        next: (response: Blob) => {
          const blob = new Blob([response], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = this.sanitizeUrl(url);
          link.download = `${this.sanitizeFileName(this.tableName)}_template.xlsx`;
          link.click();
          this.toastrService.success(this.DOWNLOAD_SUCCESS_MESSAGE, "", {
            positionClass: "toast-center-center",
          });
        },
      });
  }

  onLinkClick(linkText: string): void {
    if (this.tableName == "Aggregate_CLO_Metrics_EU" || this.tableName == "Aggregate_CLO_Metrics_US") {
      this.onGLIPortfolioClick(linkText);
      return;
    }
    this.router.navigate(["/clo-kpi-history"], {
      queryParams: {
        kpiname: linkText,
        cloid: this.cloId,
        dmcl: this.cloDomcile,
      },
    });
  }
  
  onGLIPortfolioClick(transaction: string): void {
    if (this.tableName !== "GLI_Portfolio_Composition" && this.tableName !== "Aggregate_CLO_Metrics_EU" && this.tableName !== "Aggregate_CLO_Metrics_US") {
      return;
    }
    const companyId = Number(this.companyID);
    this.cloListService.getCloByIssuer(companyId, transaction).subscribe({
      next: (clo) => {
        if (clo && clo.length > 0) {
          this.router.navigate(["/view-clo-summary", clo[0].uniqueID]);
        }
      },
      error: (error) => {
        let errorTable="GLI Portfolio";
        if (this.tableName == "Aggregate_CLO_Metrics_EU" || this.tableName == "Aggregate_CLO_Metrics_US") {
          errorTable="Aggregate CLO Metrics";
        }
        if(error.error && error.error.title=="Unauthorized") {
          this.router.navigate(["/401"]);
        }
        else{
          this.toastrService.error(error.error+" for this "+errorTable, "", {
            positionClass: "toast-center-center",
          });
        }
       
      },
    });
  }

  private getRowIdentifier(dataItem: any, uniqueIdentifierColumns: any[], rowHeaderField: string, joinSeparator: string = '_'): string {
    if (uniqueIdentifierColumns.length === 0) {
      return 'NA';
    }
    
    return uniqueIdentifierColumns.length > 1 
      ? uniqueIdentifierColumns.map(col => dataItem[col.field]).join(joinSeparator)
      : dataItem[rowHeaderField];
  }

  private sanitizeUrl(url: string): string {
    // Use a URL object to ensure the URL is properly formatted
    const sanitizedUrl = new URL(url);
    return sanitizedUrl.href;
  }

  private sanitizeFileName(fileName: string): string {
    // Replace any character that is not a letter, number, underscore, hyphen, or dot
    return fileName.replace(/[^a-zA-Z0-9_\-\.]/g, '_');
  }

  adjustWidthBasedOnData() {
    let iconSix=24;
    let iconPadding=20;
    let contentPadding=32;
    this.columns.forEach((column:any) => {
      const field = column.field;
      const maxDataWidth = this.data.reduce((max, item) => {
        const contentdataWidth = this.getTextWidth(item[field],'14px Arial');
        return Math.max(max, contentdataWidth);
      }, 0);
       let dataWidth= Math.round(maxDataWidth + contentPadding + iconSix + iconPadding);
      //  calculate longest word
      const words = column.header?.split(' ');
      let longestWord = words?.reduce((longest, current) => 
        current.length > longest.length ? current : longest, '');
        const longestWordWidth = Math.round(this.getTextWidth(longestWord,'500 14px Arial') + contentPadding + iconSix); // Adding some padding
      column.width = Math.max(longestWordWidth,dataWidth); 
    });
  }
   
  getTextWidth(text: string, font:string): number {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    context.font = font;
    return context.measureText(text).width;
  }
  

  openMenu(event: MouseEvent) {
        this.isMenuOpen = !this.isMenuOpen;
      }
    
      onSelect(event: { item: any }) {      
        if (event.item.text === 'Delete Table') {
    this.showDeletePopup();
        }
    this.isMenuOpen = false;
        
      }
    
  
deleteItem() {
  this.isMenuOpen = false;
  this.hideDeletePopup();
  if(!this.isCLO){
    this.deleteInvestmentCompany();
  }
  else{
this.deleteCLOTabls();
  }
      
    }
  deleteInvestmentCompany() {
    this.investCompanyService.deleteInvestmentCompany(+this.companyID,this.tableName).subscribe(
           response => {
        this.deleteSuccess();      
   },
   error => {
     this.deleteError();
   },
         );
  }

  deleteCLOTabls() {
    this.cloListService.DeleteClo(this.companyID,this.tableName).subscribe(
            response => {
         this.deleteSuccess();      
    },
    error => {
      this.deleteError();
    },
          );
  }
deleteSuccess(){
  this.toastrService.success(this.successTableMessage, "", {
    positionClass: "toast-center-center",
  });
  this.reloadData(); 
}
  private reloadData() {
    this.data = [];
    this.columns = [];
    this.footnotes = [
      { name: "FootNote", newComment: "", isExpanded: false, isEdit: false },
    ];
    this.loadFootnote();
    this.loadData();
  }

deleteError(){
  this.isMenuOpen = false;
  this.hideDeletePopup();
      this.toastrService.error("Error while deleting data", "", {
        positionClass: "toast-center-center",
      });
      this.reloadData();  
}
  showDeletePopup() {
    this.showPopup = true;
  }
  hideDeletePopup() {
    this.showPopup = false;
  }
  
calculateRowHeight(row: any): number {
  if (this.tableId == this.Aggregate_CLO_Metrics_EU || this.tableId == this.Aggregate_CLO_Metrics_US){
return 16; 
  }
  else if(this.tableId == this.GLI_Portfolio_Composition || this.tableId == this.Capital_Structure || this.tableId == this.CLO_Distributions_To_Date){
      return 1; // Default row height
    }
}
  
  
}