import { ViewCompanyDetailsComponent } from './view-company-details/view-company-details.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { AngularResizeEventModule } from 'angular-resize-event';
import { QuillModule } from 'ngx-quill';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { AuditLogService } from './services/audit-log.service';
import { SharedCloModule } from 'src/app/components/clo/shared-clo.module';

@NgModule({
  declarations: [

    ViewCompanyDetailsComponent,

  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,

    SharedComponentModule,
    MaterialModule,
    PrimeNgModule,
    AngularResizeEventModule,
    SharedDirectiveModule,
    FormsModule,
    ReactiveFormsModule,
    QuillModule,
    RouterModule.forChild([
      { path: '', component: ViewCompanyDetailsComponent }
  ]),
  
  KendoModule,
  SharedCloModule
],
providers: [
  AuditLogService
]
})
export class ViewCompanyDeatilsComponentModule { }