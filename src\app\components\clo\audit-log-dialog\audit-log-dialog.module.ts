import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GridModule } from '@progress/kendo-angular-grid';
import { DialogModule } from '@progress/kendo-angular-dialog';
import { ButtonModule } from '@progress/kendo-angular-buttons';
import { FormsModule } from '@angular/forms';
import { AuditLogDialogComponent } from './audit-log-dialog.component';
import { AuditLogService } from '../services/audit-log.service';

@NgModule({
  declarations: [
    AuditLogDialogComponent
  ],
  imports: [
    CommonModule,
    GridModule,
    DialogModule,
    ButtonModule,
    FormsModule
  ],
  exports: [
    AuditLogDialogComponent
  ],
  providers: [
    AuditLogService
  ]
})
export class AuditLogDialogModule { }
