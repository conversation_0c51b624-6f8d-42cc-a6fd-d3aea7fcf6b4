import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CloKpiHistoryComponent } from './clo-kpi-history/clo-kpi-history.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { AngularResizeEventModule } from 'angular-resize-event';
import { QuillModule } from 'ngx-quill';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { SharedCloModule } from './shared-clo.module';

@NgModule({
  declarations: [
    CloKpiHistoryComponent    
  ],
  imports: [
      CommonModule,
      FormsModule,
      RouterModule,
      MaterialModule,
      PrimeNgModule,
      AngularResizeEventModule,
      QuillModule,
      SharedDirectiveModule,
      FormsModule,
      ReactiveFormsModule,
      SharedCloModule,
      RouterModule.forChild([
        { path: '', component: CloKpiHistoryComponent }
    ]),
    KendoModule,
    
  ],
  exports: [CloKpiHistoryComponent]
})
export class CloKpiHistoryModule { }
