import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of } from 'rxjs';

import { FileHistoryComponent } from './file-history.component';
import { FileHistoryService } from '../../services/file-history.service';
import { FileUploadProgressService } from '../../services/file-upload-progress.service';

describe('FileHistoryComponent', () => {
  let component: FileHistoryComponent;
  let fixture: ComponentFixture<FileHistoryComponent>;

  beforeEach(() => {
    const mockFileHistoryService = jasmine.createSpyObj('FileHistoryService', ['toggleFileHistoryPopup'], {
      showPopup$: of(false),
      anchorElement$: of(null)
    });

    const mockFileUploadProgressService = jasmine.createSpyObj('FileUploadProgressService', ['newUploadCount$']);
    mockFileUploadProgressService.newUploadCount$ = of(0);

    TestBed.configureTestingModule({
      declarations: [FileHistoryComponent],
      imports: [
        HttpClientTestingModule,
        ReactiveFormsModule
      ],
      providers: [
        { provide: FileHistoryService, useValue: mockFileHistoryService },
        { provide: FileUploadProgressService, useValue: mockFileUploadProgressService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    });
    fixture = TestBed.createComponent(FileHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
