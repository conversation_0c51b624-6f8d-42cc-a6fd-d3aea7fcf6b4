<div class="row mr-0 ml-0 view-esg-audits-component">
  <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0">
    <div class="row mr-0 ml-0 audit-header">
      <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0">
        <div class="float-left audit-kpi-title">
          <div class="d-inline-block">
            <div class="pt-1">
              <img src="assets/dist/images/kpi-detail.svg" alt="audit-icon" class="audit-icon">
            </div>
          </div>
          <div class="d-inline-block title-w-block">
            <div class="Heading1-M TextTruncate title-w" title="{{auditInfo?.kpi}}">{{auditInfo?.kpi}}</div>
            <div class="S-R sub-title-block">{{auditInfo?.subPageName}}</div>
          </div>
        </div>
        <div class="float-right audit-sub-section">
          <div class="d-inline-block audit-pr">
            <div class="audit-sub-header XS-R">
              Period
            </div>
            <div class="Heading2-R sub-section-title S-B">
              {{routeData?.Period}}
            </div>
          </div>
          <div class="d-inline-block audit-pr" *ngIf="routeData?.KpiInfo == '$'">
            <div class="audit-sub-header XS-R">
              Reporting Currency
            </div>
            <div class="Heading2-R sub-section-title S-B">
              {{auditInfo?.currencyCode}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0 esg-audit-table">
    <div class="row mr-0 ml-0">
      <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 esg-audit-desc border-bottom">
        <div class="content">
          Audit Log
        </div>
        <div class="Caption-R TextTruncate sub-title-audit">
          All activity history is listed as individual items, starting with the most recent
        </div>
      </div>
      <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0">
        <kendo-grid id="esg-kpi-audit-log-grid" [kendoGridBinding]="auditLogData" scrollable="virtual" [rowHeight]="44"
          [resizable]="true" class="k-grid-border-right-width k-grid-outline-none custom-kendo-cab-table-grid audit-log-grid">
          <kendo-grid-column [width]="200" title="Current Value">
            <ng-template kendoGridHeaderTemplate>
              <span class="S-M" [ngClass]="auditInfo?.KpiInfo == 'Text'?'float-left':'float-right'">Current
                Value</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
              <span class="TextTruncate"
                [ngClass]="auditInfo?.KpiInfo == kpiInfo.Text ? 'text-align-l float-left':'text-align-r float-right'"
                *ngIf="rowData.newValue!=null;else empty_Text">
                <container-element [ngSwitch]="routeData?.KpiInfo">
                  <span [title]="rowData.newValue" *ngSwitchCase="kpiInfo.Text" [innerHtml]="rowData.newValue"
                    class="TextTruncate"></span>
                  <span [title]="rowData.newValue" *ngSwitchCase="kpiInfo.Number"
                    [innerHtml]="isNumberCheck(rowData.newValue) ? (rowData.newValue | number:NumberDecimalConst.noDecimal | minusSignToBrackets) : rowData.newValue"
                    class="TextTruncate"></span>
                  <span [title]="rowData.newValue" *ngSwitchCase="kpiInfo.Currency" class="TextTruncate"
                    [innerHtml]="isNumberCheck(rowData.newValue) ? (rowData.newValue | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : rowData.newValue"></span>
                  <span [title]="rowData.newValue" *ngSwitchCase="kpiInfo.Multiple" class="TextTruncate"
                    [innerHtml]="isNumberCheck(rowData.newValue) ? (rowData.newValue | number: NumberDecimalConst.multipleDecimal)+'x': rowData.newValue"></span>
                  <span [title]="rowData.newValue" *ngSwitchCase="kpiInfo.Percentage" class="TextTruncate"
                    [innerHtml]="isNumberCheck(rowData.newValue ) ? (rowData.newValue  | number: NumberDecimalConst.percentDecimal | minusToBracketsWithPercentage: kpiInfo.Percentage): rowData.newValue"></span>
                  <container *ngSwitchDefault>
                  </container>
                </container-element>
              </span>
              <ng-template #empty_Text class="detail-sec">
                <span
                  [ngClass]="auditInfo?.kpiInfo ==kpiInfo.Text ? 'text-align-l float-left':'text-align-r float-right'">NA</span>
              </ng-template>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column [width]="200" title="Old Value">
            <ng-template kendoGridHeaderTemplate>
              <span class="S-M" [ngClass]="auditInfo?.KpiInfo == 'Text'?'float-left':'float-right'">Old Value</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
              <span class="TextTruncate"
                [ngClass]="auditInfo?.KpiInfo == kpiInfo.Text ? 'text-align-l float-left':'text-align-r float-right'"
                *ngIf="rowData.oldValue!=null;else empty_Text">
                <container-element [ngSwitch]="routeData?.KpiInfo">
                  <span [title]="rowData.oldValue" *ngSwitchCase="kpiInfo.Text" [innerHtml]="rowData.oldValue"
                    class="TextTruncate"></span>
                  <span [title]="rowData.oldValue" *ngSwitchCase="kpiInfo.Number"
                    [innerHtml]="isNumberCheck(rowData.oldValue) ? (rowData.oldValue | number:NumberDecimalConst.noDecimal | minusSignToBrackets) : rowData.oldValue"
                    class="TextTruncate"></span>
                  <span [title]="rowData.oldValue" *ngSwitchCase="kpiInfo.Currency" class="TextTruncate"
                    [innerHtml]="isNumberCheck(rowData.oldValue) ? (rowData.oldValue | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : rowData.oldValue"></span>
                  <span [title]="rowData.oldValue" *ngSwitchCase="kpiInfo.Multiple" class="TextTruncate"
                    [innerHtml]="isNumberCheck(rowData.oldValue) ? (rowData.oldValue | number: NumberDecimalConst.multipleDecimal)+'x': rowData.oldValue"></span>
                  <span [title]="rowData.oldValue" *ngSwitchCase="kpiInfo.Percentage" class="TextTruncate"
                    [innerHtml]="isNumberCheck(rowData.oldValue ) ? (rowData.oldValue  | number: NumberDecimalConst.percentDecimal | minusToBracketsWithPercentage: kpiInfo.Percentage): rowData.oldValue"></span>
                  <container *ngSwitchDefault>
                  </container>
                </container-element>
              </span>
              <ng-template #empty_Text class="detail-sec">
                <span
                  [ngClass]="auditInfo?.kpiInfo ==kpiInfo.Text ? 'text-align-l float-left':'text-align-r float-right'">NA</span>
              </ng-template>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column [width]="200" title="Source">
            <ng-template kendoGridHeaderTemplate>
              <span class="S-M">Source</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
              <span class="align-top-left">
                <span>{{rowData.auditType}}</span>
              </span>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column [width]="400" title="Bulk Upload File">
            <ng-template kendoGridHeaderTemplate>
              <span class="S-M doc-support">Bulk Upload File
                <span> <img tooltipPosition="top" tooltipStyleClass="bg-tooltip-fund-color"
                    [pTooltip]="'Click on file name for individual file download'"  class="all-download-icon"
                    src="assets/dist/images/info-icon.svg" alt="info-icon" /></span>
                <span class="pl-2 upload-loader" *ngIf="isDocSupportLoading">
                  <i aria-hidden="true" class="download-circle-btn-loader fa fa-circle-o-notch fa-1x fa-fw"></i>
                </span>
              </span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
              <div class="align-top-left">
                <div class="float-left circle-doc" title="{{rowData.fileName}}" *ngIf="rowData.fileName!=null">
                  <div class="source-doc TextTruncate">
                    <a (click)="downloadSourceFile(rowData.documentId,rowData.fileName)" id="downloadsource">
                      <ng-container [ngSwitch]="rowData.extension.trim()">
                        <img *ngSwitchCase="fileExtension.XLSX" [src]="'assets/dist/images/FaRegFileExcel.svg'"
                          alt="xlsx file">
                        <img *ngSwitchCase="fileExtension.XLS" [src]="'assets/dist/images/FaRegFileExcel.svg'"
                          alt="xlsx file">
                        <img *ngSwitchCase="fileExtension.PDF" [src]="'assets/dist/images/Left Icon.svg'"
                          alt="pdf file">
                        <img *ngSwitchCase="fileExtension.ZIP" [src]="'assets/dist/images/FaRegFileArchive.svg'"
                          alt="zip file">
                        <img *ngSwitchCase="fileExtension.PNG" [src]="'assets/dist/images/FaRegFileImage.svg'"
                          alt="image file">
                        <img *ngSwitchCase="fileExtension.JPG" [src]="'assets/dist/images/FaRegFileImage.svg'"
                          alt="image  file">
                        <img *ngSwitchCase="fileExtension.TXT" [src]="'assets/dist/images/FaRegFileWord.svg'"
                          alt="doc file">
                        <img *ngSwitchCase="fileExtension.DOCX" [src]="'assets/dist/images/FaRegFileWord.svg'"
                          alt="doc file">
                        <img *ngSwitchCase="fileExtension.PPTX" [src]="'assets/dist/images/FaRegFilePowerpoint.svg'"
                          alt="ppt file">
                        <img *ngSwitchCase="fileExtension.PPT" [src]="'assets/dist/images/FaRegFilePowerpoint.svg'"
                          alt="ppt file">
                        <img *ngSwitchDefault [src]="'assets/dist/images/FaRegFileArchive.svg'" alt="default file">
                      </ng-container> {{rowData.fileName}}
                    </a>
                  </div>
                </div>
                <div class="float-left S-I source-doc-t" *ngIf="rowData.fileName==null || rowData.fileName==''">
                  No source file uploaded
                </div>
              </div>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column [width]="200" title="Supporting Evidence">
            <ng-template kendoGridHeaderTemplate>
              <span class="S-M">Supporting Evidence</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
              <div class="align-top-left">
                <div class="float-left">
                  <span class="pr-2" title="Supporting Documents"><a
                      (click)="openDoc(rowData.supportingDocumentsId)" id="opendoc"><img src="assets/dist/images/fi-text.svg"
                        alt="text-image" /></a></span>
                  <span title="Comments"><a (click)="openCommentsPopUp(rowData.commentId)"><img
                        src="assets/dist/images/fi-message.svg" alt="message-image" /></a></span>
                </div>
              </div>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column [width]="200" title="Created By">
            <ng-template kendoGridHeaderTemplate>
              <span class="S-M">Created By</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
              <div class="align-top-left" title="rowData.uploadedBy">
                <div title="{{rowData.uploadedBy}}">
                  <div class="text-above-image XS-R">
                    {{rowData.uploadedBy.split(' ')[0].charAt(0).toUpperCase()}}{{rowData.uploadedBy.split(' ')[1].charAt(0).toUpperCase()}}
                  </div>
                  <img src="assets/dist/images/profile-icon.svg" alt="text-image" />
                </div>
              </div>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column [width]="200" title="Date Time">
            <ng-template kendoGridHeaderTemplate>
              <span class="S-M">Date Time</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
              <div class="doc-time align-top-left">
                {{rowData.createdOn}}
              </div>
            </ng-template>
          </kendo-grid-column>
        </kendo-grid>
      </div>
    </div>
  </div>
</div>
<div *ngIf="isOpenPopup" class="nep-modal nep-modal-show kpi-audit-page">
  <div class="nep-modal-mask"></div>
  <div class="nep-card nep-card-shadow nep-modal-panel nep-modal-default"
    [ngStyle]="{'width': sideNavWidth,'left': leftWidth}">
    <div class="nep-card-header nep-modal-title M-M">
      Supporting Evidence <a class="float-right  cursor-filter cursor-pointer" id="isopenpopup" (click)="isOpenPopup = false;">
        <i class="pi pi-times custom-close-icon"></i>
      </a>
    </div>
    <div class="nep-card-body audit-body">
      <div class="row mr-0 ml-0 support-body">
        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0">
          <div class="row mr-0 ml-0">
            <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 support-header">
              <div class="float-left S-B">
                <span *ngIf="isDocument">Supporting Document(s)</span> <span *ngIf="isComments">Comments</span> <span
                  *ngIf="isDocument" class="pl-1"> <img tooltipPosition="top" tooltipStyleClass="bg-tooltip-fund-color"
                    [pTooltip]="'Click on file name for individual file download'" class="all-download-icon"
                    src="assets/dist/images/info-icon.svg" alt="info-icon" /> </span>
                <span class="pl-2 upload-loader" *ngIf="isDocSupportLoading">
                  <i aria-hidden="true" class="download-circle-btn-loader fa fa-circle-o-notch fa-1x fa-fw"></i>
                </span>
              </div>
              <div class="float-right" *ngIf="isDocument && documentData.length > 0">
                <span class="pr-2 upload-loader" *ngIf="isDocLoading">
                  <i aria-hidden="true" class="download-circle-btn-loader fa fa-circle-o-notch fa-1x fa-fw"></i>
                </span>
                <span class="pr-1"> <a><img class="all-download-icon" src="assets/dist/images/all-download.svg"
                      alt="info-icon" /></a></span>
                <span>
                  <a class="S-R download-link" href="javascript:void" id="downloadzip" (click)="downloadZip()">Consolidated Download</a>
                </span>
              </div>
            </div>
            <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 support-body-content">
              <div class="row mr-0 ml-0">
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0"
                  *ngIf="documentData.length > 0  && isDocument">
                  <div class="float-left circle-doc circle-doc-all" *ngFor="let document of documentData">
                    <div class="source-doc TextTruncate">
                      <a class="S-R source-link" id="downloadfile" (click)="downloadFile(document.documentId,document.documentName)">
                        <ng-container [ngSwitch]="document.extension">
                          <img *ngSwitchCase="fileExtension.XLSX" [src]="'assets/dist/images/FaRegFileExcel.svg'"
                            alt="xlsx file">
                          <img *ngSwitchCase="fileExtension.XLS" [src]="'assets/dist/images/FaRegFileExcel.svg'"
                            alt="xlsx file">
                          <img *ngSwitchCase="fileExtension.PDF" [src]="'assets/dist/images/Left Icon.svg'"
                            alt="pdf file">
                          <img *ngSwitchCase="fileExtension.ZIP" [src]="'assets/dist/images/FaRegFileArchive.svg'"
                            alt="zip file">
                          <img *ngSwitchCase="fileExtension.PNG" [src]="'assets/dist/images/FaRegFileImage.svg'"
                            alt="image file">
                          <img *ngSwitchCase="fileExtension.JPG" [src]="'assets/dist/images/FaRegFileImage.svg'"
                            alt="image file">
                          <img *ngSwitchCase="fileExtension.TXT" [src]="'assets/dist/images/FaRegFileWord.svg'"
                            alt="doc file">
                          <img *ngSwitchCase="fileExtension.DOCX" [src]="'assets/dist/images/FaRegFileWord.svg'"
                            alt="doc file">
                          <img *ngSwitchCase="fileExtension.PPTX" [src]="'assets/dist/images/FaRegFilePowerpoint.svg'"
                            alt="ppt file">
                          <img *ngSwitchCase="fileExtension.PPT" [src]="'assets/dist/images/FaRegFilePowerpoint.svg'"
                            alt="ppt file">
                          <img *ngSwitchDefault [src]="'assets/dist/images/FaRegFileArchive.svg'" alt="defaultfile">
                        </ng-container>
                        {{document.documentName}}</a>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0"
                  *ngIf="documentData.length  == 0 && isDocument">
                  <div class="text-center">
                    <img src="assets/dist/images/empty-state-doc.svg" alt="empty-state" />
                  </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0"
                  *ngIf="(commentText == null || commentText == '') && isComments">
                  <div class="text-center empty-comments">
                    <img src="assets/dist/images/empty-state-comments.svg" alt="empty-state" />
                  </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0"
                  *ngIf="isComments && commentText != null">
                  <div class="text-comments S-R">
                    {{commentText}}

                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<app-loader-component *ngIf="isLoader"></app-loader-component>