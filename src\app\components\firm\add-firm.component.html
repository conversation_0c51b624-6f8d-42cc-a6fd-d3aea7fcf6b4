<div class="row mr-0 ml-0 firm-detail-section add-firm-section">
    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12 pr-0 pl-0">
        <form name="form" class="mt-0 pt-0 pb-0 pl-0 pr-0" (ngSubmit)="f.form.valid && saveFirm(f)" #f="ngForm">
            <div class="row mr-0 ml-0 pt-0 header-section custom-padding-bottom header-performance">
                <div class="header-section col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                    <div class="fund-header pb-2 TextTruncate" title="Firm Information">
                    Firm Information
                    </div>
                </div>

                <div class="col-md-12 col-sm-12 col-12 col-lg-12 col-xl-12 col-xs-12 mr-0 ml-0 card card-main static-card pb-4">
                    <div class="row mr-0 ml-0">
                        <div class="col-md-6 col-sm-12 col-12 col-lg-4 col-xl-3 col-xs-3 pl-0 pr-0">
                            <div class="form-group" [ngClass]="{ 'has-error': f.submitted && !FirmName.valid }">
                                <div class="row mr-0 ml-0">
                                    <div class="col-12 pl-0">
                                        <label for="FirmName" class="TextTruncate mandatory-label" title="Firm Name">Firm Name</label>
                                    </div>
                                    <div class="col-12 pl-0">
                                        <input type="text"placeholder="Enter Name"  class="form-control TextTruncate mandatory-label " name="FirmName" [(ngModel)]="model.firmName" #FirmName="ngModel" validateBusinessName autocomplete="off" maxlength="100" required />
                                        <div *ngIf="FirmName.invalid && (FirmName.dirty|| f.submitted)" class="text-danger">
                                            <p *ngIf="!FirmName.errors.validateRequired && FirmName.errors.validateBusinessName">
                                                Please enter valid firm name
                                            </p>
                                            <p *ngIf="FirmName.errors.validateRequired">Firm name is required
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-sm-12 col-12 col-lg-4 col-xl-3 col-xs-3 pl-0 pr-0">
                            <div class="form-group" [ngClass]="{ 'has-error': f.submitted && !Website.valid }">
                                <div class="row mr-0 ml-0">
                                    <div class="col-12">
                                        <label for="Website" class="TextTruncate" title="Website">Website</label>
                                    </div>
                                    <div class="col-12">
                                        <input type="text" placeholder="Enter website" class="form-control TextTruncate" name="Website" [(ngModel)]="model.website" #Website="ngModel" validateURL autocomplete="off" maxlength="100" />
                                        <div *ngIf="Website.invalid && (Website.dirty ||f.submitted)" class="text-danger">
                                            <p *ngIf="Website.errors.validateURL">Please provide a valid website url
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mr-0 ml-0 header-section ">
                <div class="header-section col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                    <div class="fund-header">
                        <label  class="fund-header pl-0 TextTruncate fund-header-pl" for="BusinessDescription" title="Business Description">Business Description</label>
                    </div>
                </div>
                <div class="col-md-12 col-sm-12 col-12 col-lg-12 col-xl-12 col-xs-12 mr-0 ml-0 card card-main static-card custom-textarea">
                    <div class="row mr-0 ml-0">
                        <div class="form-group" class="col-12 pl-0 pr-0" [ngClass]="{ 'has-error': f.submitted && !BusinessDescription.valid }">
                            <textarea type="text" placeholder="Enter business description" class="form-control" rows="4" name="BusinessDescription" [(ngModel)]="model.businessDescription" #BusinessDescription="ngModel" autocomplete="off" maxlength="500"></textarea>
                                <div *ngIf="!BusinessDescription.valid && (BusinessDescription.dirty||f.submitted)" class="text-danger">
                                    <p *ngIf="(BusinessDescription.dirty) || BusinessDescription.errors.validateRequired">
                                        Business description is required
                                    </p>
                                </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mr-0 ml-0 header-section static-card1 header-performance tab-header mb-3">
                <div class="col-lg-12 pl-0 pr-0">
                    <div class="card-body portafolio-table">
                        <div class="row performance-section mr-0 ml-0">
                            <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 outer-section pl-0 pr-0">
                                <div class="panel panel-default border-0 pt-0 tab-bg">
                                    <div class="panel-heading pt-2">
                                        <div class="panel-title custom-tabs">
                                            <ul class="nav nav-tabs nav-custom">
                                                <li class="nav-item" role="presentation" *ngFor="let tab of tabList;" (click)="selectTab(tab)">
                                                    <button class="nav-link nav-custom TextTruncate" title="{{tab.aliasname}}" [ngClass]="tab.active?'active':''" id="home-tab" data-bs-toggle="tab" type="button" role="tab" aria-controls="home" aria-selected="true">
                                                      {{tab.aliasname}}
                                                   </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="content-bg">
                                    <div *ngIf="tabName=='Geographic-Locations'">
                                        <div class="row ml-0 mr-0">
                                            <div class="col-12 pr-0 pl-0">
                                                <form class="border-bottom pl-3 pr-3" ngForm name="geographyForm" #geographyForm="ngForm">
                                                    <div class="row pl-3 pr-0 pt-3 location-form">
                                                        <div class="col-sm-6 col-md-6  col-lg-3 pl-0 pr-3 firm-custom-padding">
                                                            <div class="TextTruncate Caption-M" for="region" title="Region">Region</div>
                                                            <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                                                [(ngModel)]="geographicLocation.region" #region="ngModel" [fillMode]="'flat'"
                                                                [filterable]="true" name="region" [virtual]="virtual"
                                                                class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                                                [size]="'medium'" [data]="masterModel?.regionList" [filterable]="true" textField="region"
                                                                valueField="regionId" [placeholder]="'Select Region'" (valueChange)="handleRegionChange()">
                                                            </kendo-combobox>
                                                        </div>
                                                        <div class="col-sm-6 col-md-6  col-lg-3 pl-0 pr-3 firm-custom-padding">
                                                            <div class="TextTruncate Caption-M" for="country" title="Country"> Country</div>
                                                            <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                                                [(ngModel)]="geographicLocation.country" #country="ngModel" [fillMode]="'flat'"
                                                                [filterable]="true" name="country" [virtual]="virtual"
                                                                class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                                                [size]="'medium'" [data]="masterModel?.countryList" [filterable]="true" textField="country"
                                                                valueField="countryId" [placeholder]="'Select Country'"
                                                                (valueChange)="handleCountryChange()">
                                                            </kendo-combobox>
                                                        </div>
                                                        <div class="col-sm-6 col-md-6  col-lg-3 pl-0 pr-3 firm-custom-padding">
                                                            <div class="TextTruncate Caption-M" for="state" title="State">State</div>
                                                            <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                                                [(ngModel)]="geographicLocation.state" #sate="ngModel" [fillMode]="'flat'"
                                                                [filterable]="true" name="state" [virtual]="virtual"
                                                                class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                                                [size]="'medium'" [data]="masterModel?.stateList" [filterable]="true" textField="state"
                                                                valueField="stateId" [placeholder]="'Select State'" (valueChange)="handleStateChange()">
                                                            </kendo-combobox>
                                                        </div>
                                                        <div class="col-sm-6 col-md-6  col-lg-3 pl-0 pr-3 firm-custom-padding">
                                                            <div class="TextTruncate Caption-M" for="city" title="City"> City</div>
                                                            <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                                                [(ngModel)]="geographicLocation.city" #city="ngModel" [fillMode]="'flat'"
                                                                [filterable]="true" name="city" [virtual]="virtual"
                                                                class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                                                [size]="'medium'" [data]="masterModel?.cityList" [filterable]="true" textField="city"
                                                                valueField="cityId" [placeholder]="'Select City'">
                                                            </kendo-combobox>
                                                        </div>
                                                    </div>
                                                    <div class="row headquarter-row">
                                                        <div class="col-12 pl-0 pr-0">
                                                            <div class="form-group custom-form-group formgroup-fl">
                                                                <mat-checkbox class="mat-custom-checkbox tree-chk-box TextTruncate" name="isHeadquarter"
                                                                    title="Headquarter"
                                                                    [(ngModel)]="geographicLocation.isHeadquarter">Headquarter</mat-checkbox>
                                    
                                                            </div>
                                                            <div class="form-group custom-form-group formgrup-fr">
                                    
                                                                <div class="add-control-btn">
                                                                    <a
                                    id="btn-clear-all"
class="geography-clear pr-3 TextTruncate"
                                                                        (click)="clearGeographicLocation(geographyForm)" title="Clear all">Clear all</a>
                                                                    <a
                                    id="btn-add-location"
class="nep-button nep-button-primary custom-bgcolor TextTruncate"
                                                                        (click)="addGeographicLocation(geographyForm)" title="Add Location">Add
                                                                        Location</a>
                                    
                                                                </div>
                                    
                                                            </div>
                                    
                                    
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                        <div class="row ml-0 mr-0">
                                            <div class="col-12 pl-0 pr-0">
                                                <div class="table-responsive" *ngIf="model.geographicLocations.length>0">
                                                    <kendo-grid [kendoGridBinding]="model.geographicLocations" [sortable]="true" scrollable="virtual"
                                                        [rowHeight]="44" [resizable]="true"
                                                        class="custom-kendo-pc-list-grid k-grid-border-right-width k-grid-outline-none kendo-fund-deal-grid">
                                                        <kendo-grid-column field="region" title="Region">
                                                            <ng-template kendoGridHeaderTemplate>
                                                                <span class="TextTruncate S-M">Region</span>
                                                            </ng-template>
                                                            <ng-template kendoGridCellTemplate let-item>
                                                                <span class="TextTruncate" title="{{ item?.region?.region }}">{{ item?.region?.region
                                                                    }}</span>
                                                            </ng-template>
                                                        </kendo-grid-column>
                                                        <kendo-grid-column field="country" title="Country">
                                                            <ng-template kendoGridHeaderTemplate>
                                                                <span class="TextTruncate S-M">Country</span>
                                                            </ng-template>
                                                            <ng-template kendoGridCellTemplate let-item>
                                                                <span class="TextTruncate" title="{{ item?.country?.country }}">{{ item?.country?.country
                                                                    }}</span>
                                                            </ng-template>
                                                        </kendo-grid-column>
                                                        <kendo-grid-column field="state" title="State">
                                                            <ng-template kendoGridHeaderTemplate>
                                                                <span class="TextTruncate S-M">State</span>
                                                            </ng-template>
                                                            <ng-template kendoGridCellTemplate let-item>
                                                                <span class="TextTruncate" title="{{ item?.state?.state }}">{{ item?.state?.state }}</span>
                                                            </ng-template>
                                                        </kendo-grid-column>
                                                        <kendo-grid-column field="city" title="City">
                                                            <ng-template kendoGridHeaderTemplate>
                                                                <span class="TextTruncate S-M">City</span>
                                                            </ng-template>
                                                            <ng-template kendoGridCellTemplate let-item>
                                                                <span class="TextTruncate" title="{{ item.city?.city }}">{{ item.city?.city }}</span>
                                                            </ng-template>
                                                        </kendo-grid-column>
                                                        <kendo-grid-column field="isheadquarter" title="Is Headquarter">
                                                            <ng-template kendoGridHeaderTemplate>
                                                                <span class="TextTruncate S-M">Is Headquarter</span>
                                                            </ng-template>
                                                            <ng-template kendoGridCellTemplate let-item>
                                                                <span title="{{item.isHeadquarter?'Yes':'No'}}">{{item.isHeadquarter?"Yes":"No"}} </span>
                                                            </ng-template>
                                                        </kendo-grid-column>
                                                        <kendo-grid-column field="Remove" title="Remove">
                                                            <ng-template kendoGridHeaderTemplate>
                                                                <span class="TextTruncate S-M">Remove</span>
                                                            </ng-template>
                                                            <ng-template kendoGridCellTemplate let-item>
                                                                <div class="text-center">
                                                                    <a (click)="removeLocation(item.uniquelocationID)">
                                                                        <img title="Remove Location" [src]="'assets/dist/images/Trash-icon.svg'" alt="">
                                                                    </a>
                                                                    <input type="hidden" value="{{item.uniquelocationID}}" />
                                                                </div>
                                                            </ng-template>
                                                        </kendo-grid-column>
                                                    </kendo-grid>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="tabName=='Investment-Professionals'" class="text-center">
                                        <div class="row ml-0 mr-0">
                                            <div class="col-sm-12 pl-0 pr-0">
                                                <form class="border-bottom" name="employeeForm" class="col-12" #employeeForm="ngForm">
                                                    <div class="row">
                                                        <div class="col-12 col-sm-6 col-md-6 col-xl-3 col-lg-4">
                                                            <div class="form-group" [ngClass]="{ 'has-error': f.submitted }">
                                                                <div class="row">
                                                                    <div class="col-12 text-align-left">
                                                                        <label for="EmployeeName TextTruncate" title="Employee Name">Employee Name</label>
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <input type="text" autocomplete="off" placeholder="Enter Employee Name" class="form-control TextTruncate" name="EmployeeName" [(ngModel)]="firmEmployee.employeeName" #EmployeeName="ngModel" inputValidator="validatePersonName" autocomplete="off" maxlength="100" validateRequired />
                                                                        <div *ngIf="EmployeeName.invalid && (EmployeeName.dirty|| employeeForm.submitted)" class="text-danger">
                                                                            <p *ngIf="EmployeeName.errors.validateRequired">
                                                                                Employee name is required
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-12 col-sm-6 col-md-6 col-xl-3 col-lg-4">
                                                            <div class="form-group custom-margin-top" [ngClass]="{ 'has-error': f.submitted }">
                                                                <div class="row">
                                                                    <div class="col-12 text-align-left">
                                                                        <label for="Designation TextTruncate" title="Designation">Designation</label>
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="firmEmployee.designation"
                                                                            #designation="ngModel" [fillMode]="'flat'" [filterable]="true" name="designation" [virtual]="virtual"
                                                                            class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                                                            [data]="designationList" [filterable]="true" textField="designation" valueField="designationId"
                                                                            [placeholder]="'Select Designation'">
                                                                        </kendo-combobox>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-12 col-sm-6 col-md-6 col-xl-3 col-lg-4">
                                                            <div class="form-group" [ngClass]="{ 'has-error': f.submitted }">
                                                                <div class="row">
                                                                    <div class="col-12 text-align-left">
                                                                        <label for="EmployeeEmail TextTruncate" title="Employee Email">Employee Email</label>
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <input type="text" autocomplete="off" class="form-control TextTruncate" placeholder="Enter Employee Email" name="EmployeeEmail"
                                                                        [(ngModel)]="firmEmployee.emailId" #EmployeeEmail="ngModel"  validateEmail
                                                                        pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}" required
                                                                        autocomplete="off" maxlength="100" validateRequired />
                                                                    <div *ngIf="EmployeeEmail.invalid && (EmployeeEmail.dirty || EmployeeEmail.touched)" class="text-danger">
                                                                        <p *ngIf="!EmployeeEmail.errors.validateRequired && EmployeeEmail.errors.validateEmail">
                                                                            Please enter valid email address</p>
                                                                        <p *ngIf="EmployeeEmail.errors.validateRequired">
                                                                            Employee email is required</p>
                                                                    </div>
                                                                    <div *ngIf="(f.submitted || EmployeeEmail?.dirty)" class="text-danger">
                                                                    
                                                                        <p *ngIf="EmployeeEmail.errors?.pattern">
                                                                            Please enter valid email address</p>
                                                                    </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-12 col-sm-6 col-md-6 col-xl-3 col-lg-4">
                                                            <div class="form-group" [ngClass]="{ 'has-error': f.submitted }">
                                                                <div class="row">
                                                                    <div class="col-12 text-align-left">
                                                                        <label for="Education TextTruncate" title="Education">Education</label>
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <input type="text" autocomplete="off" class="form-control TextTruncate" placeholder="Enter Education" name="Education" [(ngModel)]="firmEmployee.education" #Education="ngModel" autocomplete="off" inputValidator="noSpecialChars" maxlength="100" />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-12 col-sm-6 col-md-6 col-xl-3 col-lg-4">
                                                            <div class="form-group" [ngClass]="{ 'has-error': f.submitted }">
                                                                <div class="row">
                                                                    <div class="col-12 text-align-left">
                                                                        <label for="PastExperience TextTruncate" title="Past Experience">Past Experience</label>
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <input type="text" autocomplete="off" class="form-control TextTruncate" placeholder="Enter Past Experience" name="PastExperience" [(ngModel)]="firmEmployee.pastExperience" #PastExperience="ngModel" inputValidator="noSpecialChars" autocomplete="off" maxlength="500" />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row headquarter-row">
                                                        <div class="col-12 pl-0 pr-0">
                                                            <div class="form-group custom-form-group form-firm-fl">
                                                             
                                                                <div class="add-control-btn">
                                                                    <a class=" geography-clearnep-button nep-button-secondary pr-2" (click)="clearEmployees(employeeForm)" title="Clear all">Clear all</a>
                                                                    <a class="nep-button nep-button-primary custom-bgcolor" (click)="addEmployees(employeeForm)" title="{{employeeEditMode?'Update':'Add'}} Professional">{{employeeEditMode?"Update":"Add"}} Professional</a>
                                                                 
                                                                </div>
                                                                   
                                                            </div>
                                                        </div>
                                                    </div>
                                                   
                                                </form>
                                                <div class="table-responsive border-top" *ngIf="model?.firmEmployees?.length>0">
                                                    <kendo-grid [kendoGridBinding]="model.firmEmployees" [sortable]="true" scrollable="virtual" [rowHeight]="44"
                                                        [resizable]="true"
                                                        class="custom-kendo-pc-list-grid k-grid-border-right-width k-grid-outline-none kendo-fund-deal-grid">
                                                        <kendo-grid-column field="employeeName" title="Employee Name">
                                                            <ng-template kendoGridHeaderTemplate>
                                                                <span class="TextTruncate S-M">Employee Name</span>
                                                            </ng-template>
                                                            <ng-template kendoGridCellTemplate let-item>
                                                                <span class="TextTruncate" *ngIf="item.employeeName" title="{{ item.employeeName  }}">{{ item.employeeName
                                                                    }}</span>
                                                            </ng-template>
                                                        </kendo-grid-column>
                                                        <kendo-grid-column field="designation" title="Designation">
                                                            <ng-template kendoGridHeaderTemplate>
                                                                <span class="TextTruncate S-M">Designation</span>
                                                            </ng-template>
                                                            <ng-template kendoGridCellTemplate let-item>
                                                                <span class="TextTruncate" *ngIf="item.designation" title="{{ item.designation.designation }}">{{
                                                                    item.designation.designation }}</span>
                                                            </ng-template>
                                                        </kendo-grid-column>
                                                        <kendo-grid-column field="emailId" title="Employee Email">
                                                            <ng-template kendoGridHeaderTemplate>
                                                                <span class="TextTruncate S-M">Employee Email</span>
                                                            </ng-template>
                                                            <ng-template kendoGridCellTemplate let-item>
                                                                <span class="TextTruncate" *ngIf="item.emailId" title="{{ item.emailId }}">{{ item.emailId }}</span>
                                                            </ng-template>
                                                        </kendo-grid-column>
                                                        <kendo-grid-column field="education" title="Education">
                                                            <ng-template kendoGridHeaderTemplate>
                                                                <span class="TextTruncate S-M">Education</span>
                                                            </ng-template>
                                                            <ng-template kendoGridCellTemplate let-item>
                                                                <span class="TextTruncate" *ngIf="item.education" title="{{ item.education }}">
                                                                    {{ item.education }} </span>
                                                            </ng-template>
                                                        </kendo-grid-column>
                                                        <kendo-grid-column field="pastExperience" title="Past Experience">
                                                            <ng-template kendoGridHeaderTemplate>
                                                                <span class="TextTruncate S-M">Past Experience</span>
                                                            </ng-template>
                                                            <ng-template kendoGridCellTemplate let-item>
                                                                <span class="TextTruncate" *ngIf="item.pastExperience" title="{{ item.pastExperience }}">
                                                                    {{ item.pastExperience }} </span>
                                                            </ng-template>
                                                        </kendo-grid-column>
                                                        <kendo-grid-column field="Action" title="Action">
                                                            <ng-template kendoGridHeaderTemplate>
                                                                <span class="TextTruncate S-M">Action</span>
                                                            </ng-template>
                                                            <ng-template kendoGridCellTemplate let-item>
                                                                <div class="text-center TextTruncate">
                                                                    <div class="add-control-btn">
                                                                        <a class="btn btn-edit" title="Edit" (click)="removeFirmEmployee(item)">
                                                                            <img title="Remove Location" [src]="'assets/dist/images/Trash-icon.svg'" alt="">
                                                                        </a>
                                                                    </div>
                                                                    <a *ngIf="false" (click)="removeEmployee(item.emailId)">
                                                                        <a class="btn btn-remove">
                                                                            <i class="fa fa-trash" aria-hidden="true"></i>
                                                                        </a>
                                                                    </a>
                                                                </div>
                                                            </ng-template>
                                                        </kendo-grid-column>
                                                        <ng-template kendoGridNoRecordsTemplate>
                                                            <app-empty-state class="finacials-beta-empty-state" [isGraphImage]="false"></app-empty-state>
                                                        </ng-template>
                                                    </kendo-grid>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row text-center ml-0 mr-0 fixed-footer custom-footer" [ngStyle]="{'width': sideNavWidth}">
                <div class="col-12 pl-0 pr-0">
                    <div class="form-group update-row float-right">
                        <div class="loading-input-controls-manual" *ngIf="loading"><i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i></div>
                        <input
              id="btn-reset"
type="button" value="{{resetText}}" title="{{resetText}}" (click)="formReset(f)" [disabled]="loading" class="btn TextTruncate btn-warning mr-2 TextTruncate" />
                        <button
              id="btn-save-firm"
type="submit" class=" width-120 nep-button nep-button-primary width-135 reset-update-portfolio-css ml-2 TextTruncate"
                        [disabled]="loading || !f.form.valid" title="{{title}}">{{title}}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div  *ngIf="confirmLocDelete" class="nep-modal nep-modal-show custom-modal">
    <div class="nep-modal-mask"></div>
    <div  class="nep-card nep-card-shadow nep-modal-panel nep-modal-default" style="position: relative; display: inline-flex; top: 40%;width:24rem; height:10.5rem">
        <div class="nep-card-header nep-modal-title">
            <div class="float-left TextTruncate">Confirmation</div>
            <div class="float-right" (click)="closeDelLocModel()">
                <div class="close-icon"><i  class="pi pi-times"></i></div>
            </div>
        </div>
        <div class="nep-card-body">
            <strong>you sure that you want to delete this record?</strong><br/><br/>
                <div class="float-right">
                    <nep-button Type="Primary" (click)="delLocation()" class="mr-2">
                        Yes
                    </nep-button>
                    <nep-button Type="Secondary" (click)="closeDelLocModel()">
                        No
                    </nep-button>
                </div>        
        </div>
    </div>
</div>