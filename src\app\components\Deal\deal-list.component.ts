﻿import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  ViewChild,
  EventEmitter,
} from "@angular/core";
import { NgxSpinnerService } from "ngx-spinner";
import { LazyLoadEvent } from "primeng/api";
import { AccountService } from "../../services/account.service";
import { DealService } from "../../services/deal.service";
import {
  MiscellaneousService,
  ErrorMessage,
} from "../../services/miscellaneous.service";
import { FeaturesEnum } from "../../services/permission.service";
import { DealDetailsConstants } from "../../common/constants";
import { MatMenu, MatMenuTrigger } from "@angular/material/menu";
import { filter } from "rxjs/operators";
import { Observable, Subject, Subscription, of } from "rxjs";
import { SortDescriptor, State } from "@progress/kendo-data-query";
import { GridDataResult } from "@progress/kendo-angular-grid";
import { KendoService } from "src/app/services/kendo.service";
import { ToastContainerDirective, ToastrService } from "ngx-toastr";
import { Router } from "@angular/router";
import { CommonSubFeaturePermissionService } from "src/app/services/subPermission.service";
@Component({
  selector: "deal-list",
  templateUrl: "./deal-list.component.html",
  styleUrls: ["./deal-list.component.scss"],
})
export class DealListComponent {
  feature: typeof FeaturesEnum = FeaturesEnum;
  public deals: any;
  closeResult: string;
  blockedTable: boolean = false;
  totalRecords: number;
  dataTable: any;
  pagerLength: any;
  globalFilter: string = "";
  dealData = [];
  isLoader: boolean = false;
  isExportLoading: boolean = false;
  dealDetailsConstants = DealDetailsConstants;
  isdownloadfilter: boolean = true;
  @ViewChild("iMenuTrigger") menuTrigger: MatMenuTrigger;
  @ViewChild("menu") uiuxMenu!: MatMenu;
  show = false;
  disableConfirm = false;
  YearQuarter = null;
  year = null;
  quarter = null;
  newInvestmentsNotFound = false;
  totalPage: number;
  public view: Observable<GridDataResult>;
  public state: State = {
    skip: 0,
    take: 100,
  };
  sort: SortDescriptor[] = [];
  @ViewChild(ToastContainerDirective, {})
  toastContainer: ToastContainerDirective;
  constructor(
    private toastrService: ToastrService,
    private router: Router,
    private miscService: MiscellaneousService,
    private kendoService: KendoService,
    private elementRef: ElementRef,
    private accountService: AccountService,
    private _dealService: DealService,
    protected changeDetectorRef: ChangeDetectorRef,
    private spinner: NgxSpinnerService,
    private subPermissionService: CommonSubFeaturePermissionService
  ) {
    this.pagerLength = this.miscService.getPagerLength();
    localStorage.setItem("headerName", "");
    this.getSubFeatureAccessPermissions();
    this.getDealList(null);
  }

  configureMenuClose(old: MatMenu["closed"]): MatMenu["closed"] {
    const upd = new EventEmitter();
    feed(
      upd.pipe(
        filter((event) => {
          if (event === "click") {
            return false;
          }
          this.isdownloadfilter = true;
          return true;
        })
      ),
      old
    );
    return upd;
  }

  paginationFilterClone: any = {};

  getDealList(event: any) {
    this.isLoader = true;
    if (event == null) {
      event = {
        first: 0,
        rows: 100,
        globalFilter: null,
        sortField: null,
        FilterWithoutPaging: false,
        sortOrder: 1,
      };
    }
    if (event.multiSortMeta == undefined) {
      event.multiSortMeta = [{ field: "dealCustomID", order: 1 }];
      event.sortField = "dealCustomID";
    }
    this.paginationFilterClone = JSON.parse(JSON.stringify(event));
    this.blockedTable = true;
    this._dealService
      .getDealsQuery({
        paginationFilter: event,
        encryptedDealID: null,
        includeAllPageConfiguration: false,
      })
      .subscribe({
        next: (result) => {
          if (result?.dealDetails?.dealList?.length > 0) {
            this.deals = result?.dealDetails?.dealList;
            this.totalRecords = result?.dealDetails?.totalRecords;
            if (this.totalRecords > 100) {
              this.totalPage = Math.ceil(this.totalRecords / event.rows);
            } else {
              this.totalPage = 1;
            }
            this.dealData = result?.staticFieldValueList.filter(
              (x) =>
                x.name == DealDetailsConstants.FundName ||
                x.name == DealDetailsConstants.DealCustomID ||
                x.name == DealDetailsConstants.CompanyName
            );
            this.view = of<GridDataResult>({
              data: this.deals,
              total: this.totalRecords,
            });
            this.blockedTable = false;
            this.changeDetectorRef.detectChanges();
          } else {
            this.blockedTable = false;
            this.deals = [];
            this.totalRecords = 0;
            this.view = of<GridDataResult>({
              data: this.deals,
              total: this.totalRecords,
            });
          }
          this.isLoader = false;
        },
        error: (error) => {
          this.blockedTable = false;
          this.isLoader = false;
        },
      });
  }

  downloadNewInvestment() {
    this.exportDealNewInvestment(this.year, this.quarter);
  }

  exportDealNewInvestment(year, quarter) {
    this.isExportLoading = true;
    let event = JSON.parse(JSON.stringify(this.paginationFilterClone));
    event.globalFilter = this.globalFilter;
    event.filterWithoutPaging = true;
    this._dealService
      .exportDealNewTransaction({
        paginationFilter: event,
        includeAllDetails: true,
        year: year,
        quarter: quarter,
      })
      .subscribe({
        next: (response) => {
          this.miscService.downloadExcelFile(response);
          this.isExportLoading = false;
          this.year = null;
          this.quarter = null;
          this.YearQuarter = null;
          this.closeQuarterYearSelectionPopup();
        },
        error: (error) => {
          this.newInvestmentsNotFound = true;
          this.disableConfirm = true;
          this.isExportLoading = false;
        },
      });
  }
  showNoAccessError() {
    this.toastrService.error(ErrorMessage.NoAccessDeal, "", {
      positionClass: "toast-center-center",
    });
  }
  exportDealList() {
    if (this.canExportInv) {
      this.isExportLoading = true;
      let event = JSON.parse(JSON.stringify(this.paginationFilterClone));
      event.globalFilter = this.globalFilter;
      event.filterWithoutPaging = true;
      this._dealService
        .exportDealList({ paginationFilter: event, includeAllDetails: true })
        .subscribe({
          next: (response) => {
            this.miscService.downloadExcelFile(response);
            this.isExportLoading = false;
          },
          error: (error) => {
            this.isExportLoading = false;
          },
        });
    } else {
      this.showNoAccessError();
    }
  }

  loadDealsLazy(event: LazyLoadEvent) {
    this.getDealList(event);
  }
  setHeaderName(dealName: any) {
    localStorage.setItem("headerName", dealName);
  }

  openQuarterYearSelectionPopup() {
    if (this.canExportInv) {
      this.year = null;
      this.quarter = null;
      this.YearQuarter = null;
      this.newInvestmentsNotFound = false;
      this.disableConfirm = true;
      this.show = true;
    } else {
      this.showNoAccessError();
    }
  }
  addDeal(event: Event) {
    if (this.canAddInv) {
      this.router.navigate(["/save-deal"]);
    } else {
      event.preventDefault();
      this.showNoAccessError();
    }
  }

  closeQuarterYearSelectionPopup() {
    this.year = null;
    this.quarter = null;
    this.YearQuarter = null;
    this.newInvestmentsNotFound = false;
    this.disableConfirm = true;
    this.show = false;
  }

  QuarterYear(event: any) {
    this.YearQuarter = event.quarter + " " + event.year;
    this.year = event.year;
    this.quarter = event.quarter;
    this.newInvestmentsNotFound = false;
    this.disableConfirm = false;
  }
  searchLoadPCLazy() {
    if (this.deals.length != 0) {
      let params: any = {
        first: 0,
        rows: 100,
        globalFilter: this.globalFilter != "" ? this.globalFilter : null,
        sortField: null,
        sortOrder: 1,
      };
      let result = this.getHeaderValue(params, null, this.dealData, this.sort);
      params = result.params;
      this.sort = result.parentSort;
      this.paginationFilterClone = JSON.parse(JSON.stringify(params));
      this.getDealList(params);
    } else {
      let event = {
        first: 0,
        rows: 100,
        globalFilter: this.globalFilter != "" ? this.globalFilter : null,
        sortField: null,
        sortOrder: 1,
      };
      this.getDealList(event);
    }
  }
  dataStateChange($event) {
    this.state.skip = $event.skip;
    this.state.take = $event.take;
    let params: any = {
      first: $event.skip,
      rows: $event.take,
      globalFilter: this.globalFilter != "" ? this.globalFilter : null,
      sortField: null,
      sortOrder: 1,
    };
    let result = this.getHeaderValue(params, $event, this.dealData, this.sort);
    params = result.params;
    this.sort = result.parentSort;
    this.getDealList(params);
  }
  getHeaderValue(
    params: any,
    event?: any,
    headers?: any,
    parentSort?: any
  ): { params: any; parentSort: any } {
    let sort = event?.sort;
    if (params.multiSortMeta == undefined) {
      params.multiSortMeta = [];
      params.FilterWithoutPaging = false;

      if (headers.length > 0) {
        headers.forEach((element) => {
          if (event != null) {
            if (sort.length > 0 && element.name == sort[0]?.field) {
              params.sortField = element.name;
              if (parentSort.length == 0) {
                parentSort = sort;
              }
              parentSort = sort;
              params.multiSortMeta.push({
                field: element.name,
                order: sort?.length > 0 && sort[0].dir == "asc" ? -1 : 1,
              });
            }
          } else {
            parentSort = [];
            params.multiSortMeta.push({ field: element.name, order: 1 });
          }
        });
      }
    }
    return { params, parentSort };
  }
  canViewInv: boolean = false;
  canAddInv: boolean = false;
  canExportInv: boolean = false;
  getSubFeatureAccessPermissions() {
    this.subPermissionService
      .getCommonFeatureAccessPermissions(FeaturesEnum.Deal)
      .subscribe({
        next: (result) => {
          if (result.length > 0) {
            this.canViewInv = result?.map((x) => x.canView).includes(true);
            this.canAddInv = result?.map((x) => x.canAdd).includes(true);
            this.canExportInv = result?.map((x) => x.canExport).includes(true);
          }
        },
        error: (_error) => {},
      });
  }
}

function feed<T>(from: Observable<T>, to: Subject<T>): Subscription {
  return from.subscribe({
    next: (data) => to.next(data),
    error: (err) => to.error(err),
    complete: () => to.complete(),
  });
}
