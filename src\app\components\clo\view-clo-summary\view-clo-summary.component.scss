@import "../../../../variables.scss";

.selected-clo-tab {
  border: none;
  border-bottom: 2px solid #4061C7;
  color:#4061C7;
  display:grid;
  align-items: center;
}

.tab-background-color {
  background-color: #F5F9FF;
  height: 54px;
  align-items: center;
}

.clo-summary-title {
  font-size: 14px;
  font-weight: 500;
  height: 52px;
  background-color: #E3EEFF;
  padding: 0;
}

.header-title {
  font-size: 16px;
  margin-left: 15px;
}

.clo-summary-table {
  padding: $clo-summary-table-padding;
}

.clo-summary-heading {
  height: 54px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 20px;
  padding-right: 20px;
  font-size: 16px;
  font-weight: 500;
}

.clo-summary-tables {    
  border: 1px solid #E6E6E6;
  border-radius: 4px;
}

.clo-row-height {
  height: 34px;
}

.clo-container {
  margin: 1.25rem 1.875rem;

  .card{
    border: none;
  }

  .clo-summary-title{
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
  }

  .clo-summary-heading{
    border-bottom: $border-neutral-grey;
  }

  .clo-cell-padding{
    padding: 0.5rem 1rem;
  }

  .cell-height{
    height: $clo-content-cell-height;
  }

  .text-color{
    color: #666666;
  }

  .card-header{
    border-bottom: none;
  }

  .nav-tabs{
    border-bottom: none;
  }
}

.company-details {
  height: 48px;
  padding: 12px 40px;
  border-bottom: 1px solid #e6e6e6;
  width: -webkit-fill-available;
}

.investment-company {
  color: #666666;
  padding-right: 16px;
  letter-spacing: normal;
  cursor: pointer;
}

.company-names {
  color: #4061C7;
  padding-left: 16px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: normal;
}
.unselected-tab-background{
  background-color: transparent;
}

.table-layout-fixed {
  table-layout: fixed;
  
  th, td {
    vertical-align: middle;
    height: 34px;
    padding: 8px;
    border-bottom: 1px solid #E6E6E6;
  }

  th {
    font-weight: 400;
    font-size: 14px;
  }

  td {
    font-weight: 700;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: default;
  }

  tr:last-child {
    th, td {
      border-bottom: none;
    }
  }

}
.company-facts-glo-container{
  border:1px solid #E6E6E6;
  border-radius:4px;
  margin-top:25px;
}

.k-tabstrip-top > .k-tabstrip-items-wrapper .k-item.k-active {
  border-right: none;
  border-bottom: $border-primary-2 !important;
}

:host ::ng-deep {
  .k-tabstrip {
    width: 100%;
              
      .k-item {
        color: $content-label-color;
        
        &.k-active {
          color: $primary-color;
          border-bottom: $border-primary-2;
        }
      }
      .k-table-md .k-table-th{
        padding-block: 0px;
      }

      .k-grid .k-grid-md .k-table-th, .k-grid-md .k-table-th{
        padding-block: 0px;
      }
  }
}

:host ::ng-deep .k-tabstrip-top > .k-content, 
:host ::ng-deep .k-tabstrip-top > .k-tabstrip-content {
    padding: 0px;
}
.company-facts-section{
  font-size: 16px;
  font-weight: 600;
  color: #000000;
}
.company-facts {
  display: flex;
  align-items: center;
  padding-right:20px;
  padding-left:20px;
  gap: 16px;
  height: 52px;
  justify-content: space-between;
  border-bottom: 1px solid #e6e6e6;
}