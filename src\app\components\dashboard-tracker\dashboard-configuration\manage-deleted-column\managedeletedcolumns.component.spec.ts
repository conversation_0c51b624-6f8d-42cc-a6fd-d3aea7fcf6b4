import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of, throwError } from 'rxjs';
import { ManageDeletedColumnsComponent } from './managedeletedcolumns.component';
import { DashboardTrackerService } from 'src/app/services/dashboard-tracker.service';
import { DashboardConfigurationConstants } from 'src/app/common/constants';

describe('ManageDeletedColumnsComponent', () => {
  let component: ManageDeletedColumnsComponent;
  let fixture: ComponentFixture<ManageDeletedColumnsComponent>;
  let mockDashboardTrackerService: jasmine.SpyObj<DashboardTrackerService>;

  const mockColumns = [
    { id: 1, name: 'SerialNo', selected: false },
    { id: 2, name: 'FundName', selected: false },
    { id: 3, name: 'PortfolioCompany', selected: false }
  ];

  const mockData = [
    { PCID: 'PC001', FundID: 'F001', SerialNo: 1, FundName: 'Fund A', PortfolioCompany: 'Company A' },
    { PCID: 'PC002', FundID: 'F002', SerialNo: 2, FundName: 'Fund B', PortfolioCompany: 'Company B' }
  ];

  const mockResponse = {
    data: mockData,
    columns: mockColumns,
    totalRecords: 2
  };

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('DashboardTrackerService', ['getGetDeletedColumnsTableData']);
    
    // Mock the method to return an Observable
    spy.getGetDeletedColumnsTableData.and.returnValue(of(mockResponse));
    
    await TestBed.configureTestingModule({
      declarations: [ ManageDeletedColumnsComponent ],
      providers: [
        { provide: DashboardTrackerService, useValue: spy }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    })
    .compileComponents();

    mockDashboardTrackerService = TestBed.inject(DashboardTrackerService) as jasmine.SpyObj<DashboardTrackerService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageDeletedColumnsComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should call loadDashboardTableData and setupTextboxDebounce on initialization', () => {
      spyOn(component as any, 'loadDashboardTableData');
      spyOn(component as any, 'setupTextboxDebounce');
      
      component.ngOnInit();
      
      expect(component['loadDashboardTableData']).toHaveBeenCalledWith(component.state);
      expect(component['setupTextboxDebounce']).toHaveBeenCalled();
    });

    it('should log initialization message', () => {
      spyOn(console, 'log');
      
      component.ngOnInit();
      
      expect(console.log).toHaveBeenCalledWith('ManageDeletedColumnsComponent initialized');
    });
  });

  describe('ngOnDestroy', () => {
    it('should complete destroy subject', () => {
      spyOn(component['destroy$'], 'next');
      spyOn(component['destroy$'], 'complete');
      
      component.ngOnDestroy();
      
      expect(component['destroy$'].next).toHaveBeenCalled();
      expect(component['destroy$'].complete).toHaveBeenCalled();
    });
  });

  describe('loadDashboardTableData', () => {
    it('should load data successfully and update component properties', fakeAsync(() => {
      mockDashboardTrackerService.getGetDeletedColumnsTableData.and.returnValue(of(mockResponse));
      
      component.loadDashboardTableData(component.state);
      tick();
      
      expect(component.isLoading).toBeFalse();
      expect(component.totalRecords).toBe(2);
      expect(component.gridColumns).toEqual(mockColumns);
      expect(component.view).toBeDefined();
    }));

    it('should handle empty response', fakeAsync(() => {
      const emptyResponse = { data: [], columns: [], totalRecords: 0 };
      mockDashboardTrackerService.getGetDeletedColumnsTableData.and.returnValue(of(emptyResponse));
      
      component.loadDashboardTableData(component.state);
      tick();
      
      expect(component.isLoading).toBeFalse();
      expect(component.totalRecords).toBe(0);
      expect(component.gridColumns).toEqual([]);
    }));

    it('should handle response without data or columns', fakeAsync(() => {
      const invalidResponse = { data: null, columns: null };
      mockDashboardTrackerService.getGetDeletedColumnsTableData.and.returnValue(of(invalidResponse));
      
      component.loadDashboardTableData(component.state);
      tick();
      
      expect(component.isLoading).toBeFalse();
      expect(component.totalRecords).toBe(0);
      expect(component.gridColumns).toEqual([]);
    }));

    it('should handle error response', fakeAsync(() => {
      mockDashboardTrackerService.getGetDeletedColumnsTableData.and.returnValue(throwError(() => new Error('API Error')));
      
      component.loadDashboardTableData(component.state);
      tick();
      
      expect(component.isLoading).toBeFalse();
      expect(component.view).toBeDefined();
    }));

    it('should set loading state correctly', fakeAsync(() => {
      mockDashboardTrackerService.getGetDeletedColumnsTableData.and.returnValue(of(mockResponse));
      
      expect(component.isLoading).toBeTrue();
      
      component.loadDashboardTableData(component.state);
      tick();
      
      expect(component.isLoading).toBeFalse();
    }));

    it('should reorder columns to put SerialNo first', fakeAsync(() => {
      const columnsWithSerialNo = [
        { id: 2, name: 'FundName', selected: false },
        { id: 1, name: 'SerialNo', selected: false },
        { id: 3, name: 'PortfolioCompany', selected: false }
      ];
      
      const responseWithSerialNo = {
        ...mockResponse,
        columns: columnsWithSerialNo
      };
      
      mockDashboardTrackerService.getGetDeletedColumnsTableData.and.returnValue(of(responseWithSerialNo));
      
      component.loadDashboardTableData(component.state);
      tick();
      
      expect(component.gridColumns[0].name).toBe('SerialNo');
      expect(component.gridColumns.length).toBe(3);
    }));
  });

  describe('dataStateChange', () => {
    it('should update state and reload data', () => {
      const newState = { skip: 100, take: 50 };
      spyOn(component, 'loadDashboardTableData');
      
      component.dataStateChange(newState);
      
      expect(component.state).toEqual(newState);
      expect(component.loadDashboardTableData).toHaveBeenCalledWith(newState);
    });
  });

  describe('onTextboxValueChange', () => {
    it('should send value to textboxChangeSubject', () => {
      spyOn(component['textboxChangeSubject'], 'next');
      const value = 'test value';
      const dataItem = { id: 1 };
      const column = { name: 'testColumn' };
      
      component.onTextboxValueChange(value, dataItem, column);
      
      expect(component['textboxChangeSubject'].next).toHaveBeenCalledWith({ value, dataItem, column });
    });
  });

  describe('clearTextboxValue', () => {
    it('should clear validation error and call onTextboxValueChange', () => {
      const dataItem = { id: 1 };
      const column = { name: 'testColumn' };
      const validationKey = 'testKey';
      
      spyOn(component as any, 'getValidationKey').and.returnValue(validationKey);
      spyOn(component, 'onTextboxValueChange');
      
      component.clearTextboxValue(dataItem, column);
      
      expect(component.validationErrors.has(validationKey)).toBeFalse();
      expect(component.onTextboxValueChange).toHaveBeenCalledWith('', dataItem, column);
    });
  });

  describe('onColumnSelectionClick', () => {
    it('should toggle column selection', () => {
      component.gridColumns = [...mockColumns];
      
      // Initially not selected
      expect(component.gridColumns[0].selected).toBeFalse();
      
      // Select column
      component.onColumnSelectionClick(0);
      expect(component.gridColumns[0].selected).toBeTrue();
      
      // Deselect column
      component.onColumnSelectionClick(0);
      expect(component.gridColumns[0].selected).toBeFalse();
    });
  });

  describe('hasValidationError', () => {
    it('should return true when validation error exists', () => {
      const dataItem = { id: 1 };
      const column = { name: 'testColumn' };
      const validationKey = 'testKey';
      
      spyOn(component as any, 'getValidationKey').and.returnValue(validationKey);
      component.validationErrors.set(validationKey, 'Error message');
      
      const result = component.hasValidationError(dataItem, column);
      
      expect(result).toBeTrue();
    });

    it('should return false when no validation error exists', () => {
      const dataItem = { id: 1 };
      const column = { name: 'testColumn' };
      const validationKey = 'testKey';
      
      spyOn(component as any, 'getValidationKey').and.returnValue(validationKey);
      
      const result = component.hasValidationError(dataItem, column);
      
      expect(result).toBeFalse();
    });
  });

  describe('getValidationError', () => {
    it('should return error message when validation error exists', () => {
      const dataItem = { id: 1 };
      const column = { name: 'testColumn' };
      const validationKey = 'testKey';
      const errorMessage = 'This field is required';
      
      spyOn(component as any, 'getValidationKey').and.returnValue(validationKey);
      component.validationErrors.set(validationKey, errorMessage);
      
      const result = component.getValidationError(dataItem, column);
      
      expect(result).toBe(errorMessage);
    });

    it('should return empty string when no validation error exists', () => {
      const dataItem = { id: 1 };
      const column = { name: 'testColumn' };
      const validationKey = 'testKey';
      
      spyOn(component as any, 'getValidationKey').and.returnValue(validationKey);
      
      const result = component.getValidationError(dataItem, column);
      
      expect(result).toBe('');
    });
  });

  describe('setupTextboxDebounce', () => {
    it('should setup debounced subscription', fakeAsync(() => {
      spyOn(component as any, 'updateCellValue');
      
      // Call setupTextboxDebounce directly to avoid ngOnInit side effects
      component['setupTextboxDebounce']();
      
      // Simulate textbox change
      component['textboxChangeSubject'].next({ value: 'test', dataItem: {}, column: {} });
      tick(500);
      
      expect(component['updateCellValue']).toHaveBeenCalledWith({}, {}, 'test');
    }));
  });

  describe('updateCellValue', () => {
    it('should update dataItem column value', () => {
      const dataItem = { testColumn: 'oldValue' };
      const column = { name: 'testColumn' };
      const newValue = 'newValue';
      
      component['updateCellValue'](dataItem, column, newValue);
      
      expect(dataItem.testColumn).toBe(newValue);
    });
  });

  describe('getValidationKey', () => {
    it('should return empty string as implemented', () => {
      const dataItem = { id: 1 };
      const column = { name: 'testColumn' };
      
      const result = component['getValidationKey'](dataItem, column);
      
      expect(result).toBe('');
    });
  });

  describe('Constants', () => {
    it('should expose DashboardConfigurationConstants to template', () => {
      expect(component.DashboardConfigurationConstants).toBe(DashboardConfigurationConstants);
    });
  });

  describe('Initial State', () => {
    it('should have correct initial values', () => {
      expect(component.deletedColumns).toEqual([]);
      expect(component.state).toEqual({ skip: 0, take: 100 });
      expect(component.isLoading).toBeTrue();
      expect(component.gridColumns).toEqual([]);
      expect(component.totalRecords).toBe(0);
      expect(component.validationErrors).toBeDefined();
      expect(component['textboxChangeSubject']).toBeDefined();
      expect(component['destroy$']).toBeDefined();
    });
  });
});
