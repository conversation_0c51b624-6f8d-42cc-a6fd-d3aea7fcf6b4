@import '../../../../assets/dist/css/font.scss';
@import '../../../../variables';
$color-black: #000000;
$color-white: #FFFFFF;
$color-light-blue: #EBF3FF;
$color-active-blue: #D9EDFF;
$color-border: #F2F2F2;
$color-border-active: #93B0ED;
$padding-small: 0.5rem 1rem;
$padding-medium: 10px 1rem;
$border-radius-small: 4px;

.clo-container {
    .clo-title {
        color: $color-black;
        padding: $padding-small;
        border-top-right-radius: $border-radius-small;
        border-top-left-radius: $border-radius-small;
        border: 1px solid $color-border;
    }
}

.dropdown-container {
    padding-left: 16px;
    padding-right: 16px;
    border: 1px solid $color-border;
    padding-bottom: 16px;
    .custom-companydropdown {
        margin-top: 8px !important;
        width: 100%;
        .Caption-M {
            display: block;
            margin-bottom: 8px;
        }
        
        kendo-dropdownlist {
            width: 100%;

            ::ng-deep {
                .k-dropdown-wrap {
                    width: 100%;
                }
                
                .k-dropdown {
                    width: 100%;
                }
            }
        }
    }
    .form-group {
        label {
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid $color-border;
            border-radius: $border-radius-small;
            
            &:focus {
                outline: none;
                border-color: $color-border-active;
            }
        }
    }
}

:host ::ng-deep {
    .k-dropdown {
        width: 100%;
    }
    
    .k-dropdown-wrap {
        width: 100%;
    }
}
.custom-image{
    text-align: center;
}

.kpi-model-page.no-data{
    height: calc(100vh - 230px)!important;
    display: flex;
    align-items: center;
    justify-content: center;

    .image-padding {
        .EmptyStateImgStyle {
            margin-bottom: 16px;
        }
        
        .info-text {
            display: inline-block;
            margin-top: 8px;
        }
    }
}