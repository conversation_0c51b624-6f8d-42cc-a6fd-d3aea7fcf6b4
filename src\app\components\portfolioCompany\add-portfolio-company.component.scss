@import "../../../variables.scss";
.nav-tabs .nav-item
{
    margin-bottom: 0px !important;
}
input[type="text"] {
    border-color: #E6E6E6 !important;
    color: #333333 !important;
    font-weight: 400 !important;
    padding-left: 0px !important;
}
.Caption-M{
    color: #666666;
}
.p-datatable-scrollable-header-box {
    padding-right: none !important;
}

.label-align {
    padding-left: 12px !important;
}

.tabradius {
    border-radius: 4px;   
    border-top-left-radius: 0px; 
    border-top-right-radius: 0px;  
 margin-top:-2px;
 margin-bottom: -2px;
  
}

.second-spacing.panel .panel-heading .nav-tabs {
    margin-bottom: -11px;
}

.second-spacing .panel-default {
    border-color: $nep-panel-border;
}

.second-spacing>.panel {
    background-color: $nep-white-secondary;
    border: 1px solid transparent;
    border-radius: 4px;
}

.second-spacing>.panel-default>.panel-heading {
    color: $nep-panel-color;
    border-color: $nep-panel-border
}

.second-spacing>.panel-heading {
    padding: 10px 15px;
    padding-top: 0px;
    border-bottom: 1px solid transparent;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}

.second-spacing>.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
    color: $nep-nav-tab;
    background-color: $nep-white;
    border-color: $nep-nav-tab-border-color $nep-nav-tab-border-color $nep-white-secondary;
    border-bottom: $nep-white-secondary 4px solid;
    top: 2px !important;
    position: relative !important;
    z-index: 9999 !important;
}

.second-spacing>.nav-tabs .nav-link {
    border: 1px solid #f3000000;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    background: $gray-800;
    color: $nep-dark-thick;
    position: relative;
    top: 2px;
}

.card-body>form {
    padding: 0 !important;
    margin-bottom: 0 !important
}

.card-body .formcss {
    margin-bottom: 0rem !important
}

.tabradius>.card-body-form-form {
    padding: 0 !important;
    margin-bottom: 0 !important
}

.tabradius>.card-body-form-form {
    padding: 0 !important;
    margin-bottom: 0 !important
}

.investmentpadding {
    padding-top: 16px !important;
}

.default-txt {
    color: #333333 !important;
}

.add-update-padding {
    padding-top: 20px !important;
}

.left-border {
    border-left: none !important;
}

.bottom-border {
    border-bottom: none !important;
}

.formcontrol-height {
    height: none !important;
}

.form-control {
    display: block;
    width: 100%;
    /* height: calc(1.5em + 0.75rem + 2px); 
    padding: 0.375rem 0.75rem;
    font-size: 1rem;*/
    font-weight: 400;
    line-height: 1.5;
    color: $nep-nav-tab;
    background-color: $nep-white-secondary;
    background-clip: padding-box;
    // border: 1px solid #ced4da;
    // border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.nav-tabs .nav-link:hover {
    border-color: none !important;
}

.fixed-footer {
    position: fixed;
    bottom: 0;
    right: 0;
    background: #FAFAFB 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 6px #00000014;
    border: 1px solid #DEDFE0;
    opacity: 1;
    padding-top: 4px;
    padding-bottom: 2px;
}

.header-cntrl {
    p-autocomplete {
        .p-autocomplete {
            input {
                padding-left: 12px !important;
            }
        }
    }
}

.eachlabel-padding {
    // padding-top: 8px;
    padding-left: 0.75rem;
}

.main-row {
    padding-left: 20px;
    padding-right: 20px;
    overflow-y: scroll;
}

.second-spacing {
    padding-top: 1.25rem;
}

.top-spacing {
    padding-top: 1.25rem;
}

.bottom-spacing{
    padding-bottom: 1.25rem;

}

.ishdeadquater {
    padding-top: 6px;
    padding-bottom: 6px;
}

.nav-active-tabcolor {
    color: $nep-primary !important;
}

.nav-inactive-tabcolor {
    color: $nep-text-grey !important;
    background-color: transparent;
}

.tabs-shadow {
    box-shadow: 0px 4px 6px #00000014 !important;
}

.spacebtwndropdown {
    padding-top: 8px !important;
}

 
    .required-field:after {
    padding-left: 2px;
    content: "*";
    color: red;

}
.outer-section {
background-color:$nep-white!important;
border-top: 1px solid $nep-divider;
border-left: 1px solid $nep-divider;
border-right: 1px solid $nep-divider;
border-top-left-radius: 4px;
border-top-right-radius: 4px;
border-bottom-left-radius: 0px;
border-bottom-right-radius: 0px;

}

.businessContainer {
    background: $nep-white 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000014;
    border: 1px solid $nep-divider;
    border-radius: 4px;
    opacity: 1;
    padding: 16px ;
}
.bg-color-none {
    background-color :transparent !important;
}
.company-group-modal-open{
    input::-webkit-input-placeholder {
        color: #4061C7 !important;
      }
      input:-ms-input-placeholder {
        color: #4061C7 !important;
      }
      input:-moz-placeholder {
        color: #4061C7 !important;
      }
      input::-moz-placeholder {
        color: #4061C7 !important;
      }
}

.custom-button-hover .p-button:enabled:hover{
    color: #FFFFFF !important;
}

.business-tab .nav-tabs .nav-pc.active {
    z-index: 0 !important;
}