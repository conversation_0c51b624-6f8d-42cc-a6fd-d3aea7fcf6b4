@import "../../../../variables.scss";

// Spacing variables
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 32px;
$spacing-xl: 20px;
$container-height: 236px;
$btn-height: 32px;
$input-margin: 5px;
$section-margin: 10px;
$content-margin: 20px;
$padding-std: 6px;
$border-width-std: 1px;
$border-width-drag: 2px;
$border-radius-sm: 4px;
$border-radius-md: 5px;
$border-radius-lg: 8px;
$icon-size: 24px;
$btn-padding-x: 6px;
$btn-padding-y: 0px;
$transform-scale: 1.1;
$calc-height-docs: calc(100vh - 300px);
$calc-height-container: calc(100vh - $container-height);

// Color variables
$danger-red: #FF4136;
$light-bg: #F5F6FA;
$error-red: #C62828;
$delete-red: #DE3139;
$transparent: transparent;
$shadow-color: rgba(0, 0, 0, 0.15);
$light-shadow: rgba(0, 0, 0, 0.05);
$hover-blue-bg: rgba(0, 123, 255, 0.05);
$disabled-opacity: 0.7;

// Using these variables in the existing styles
.nodata-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: $Neutral-Gray-00;
  text-align: center;
  height: $calc-height-container;
  border-bottom-right-radius: $Radius-8;

  img {
    display: block;
    max-width: 100%;
    height: auto;
  }
}

.folder-list-container {
  border-bottom-right-radius: $Radius-8;
  position: relative; // Added for positioning the floating popup

  .add-document {
    border-bottom: $border-width-std solid $Neutral-Gray-10;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .doc-list-text {
      padding-top: $spacing-md;
      padding-bottom: $spacing-md;
    }

    .add-doc-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border: $border-width-std solid $Primary-78;
      border-radius: $spacing-xs;
      background-color: $Neutral-Gray-00;
      height: $btn-height;

      .add-doc-icon {
        padding-left: $spacing-lg;
      }
    }
  }
}

.add-doc-text {
  padding-right: $spacing-lg;
  padding-left: $spacing-sm;
  color: $Primary-78;
}

// Styling for checkboxes in the grid
kendo-grid-column.k-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;

  input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
  }
}

// Styling for grid to prevent unwanted columns
kendo-grid {
  overflow: hidden; // Prevent overflow that might cause extra columns
  
  .k-grid-header,
  .k-grid-content {
    width: 100%; // Ensure the grid takes full width
    overflow-x: hidden; // Hide horizontal overflow
  }
  
  // Ensure the table inside grid doesn't overflow
  .k-grid-table {
    width: 100%; // Make table take full width
    table-layout: fixed; // Use fixed table layout to prevent column width issues
  }
}

// Apply border radius to the grid in doc-container
.doc-container kendo-grid {
  border-radius: $border-radius-lg;
  
  .k-grid-header {
    border-top-left-radius: $border-radius-lg;
    border-top-right-radius: $border-radius-lg;
    overflow: hidden;
  }
  
  .k-grid-footer,
  .k-grid-content {
    border-bottom-left-radius: $border-radius-lg;
    border-bottom-right-radius: $border-radius-lg;
    overflow: hidden;
  }
}

// Action column styles
.action-column-right {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
.custom-mr{
  margin-right: $spacing-xl;
}
.action-btn {
  background: $transparent;
  border: none;
  cursor: pointer;
  padding: $spacing-xs;
  transition: all 0.2s;

  &:hover {
    transform: scale($transform-scale);
  }

  &.delete-btn:hover {
    color: $danger-red;
  }
  
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
}

// Floating selection popup
.selection-popup {
  position: fixed;
  bottom: $spacing-xl;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background-color: $Neutral-Gray-00;
  color: $Neutral-Gray-90;
  border-radius: $border-radius-lg;
  box-shadow: 0 $spacing-xs $spacing-md $shadow-color;

  .selection-content {
    display: flex;
    align-items: center;
    padding: $spacing-md $spacing-xl;
    position: relative;

    .selected-count {
      margin-right: $spacing-xl;
      font-weight: 500;
    }

    .delete-selected-btn {
      background-color: $Neutral-Gray-00;
      border: none;
      color: $error-red;
      padding: $padding-std $btn-padding-y;
      border-radius: $border-radius-sm;
      cursor: pointer;
      transition: background-color 0.2s;

      &:disabled {
        background-color: $Neutral-Gray-00;
        cursor: not-allowed;
        opacity: $disabled-opacity;
      }
    }

    .close-selection-btn {
      position: absolute;
      right: $spacing-sm;
      top: 50%;
      transform: translateY(-50%);
      background: $transparent;
      border: none;
      font-size: $spacing-xl;
      line-height: 1;
      cursor: pointer;
      color: $Neutral-Gray-70;
      display: flex;
      align-items: center;
      justify-content: center;
      width: $icon-size;
      height: $icon-size;
      padding-right: $spacing-sm;

      span {
        display: block;
        line-height: 0.8;
      }
    }
  }
}

.document-options {
  .radio-options {
    display: flex;
    gap: $spacing-xl;

    .radio-option {
      display: flex;
      align-items: center;

      input[type="radio"] {
        margin-right: $input-margin;
      }

    }

    .k-radio-label {
      display: flex;
      align-items: center;
      font-size: 14px;
      margin-bottom: 0;

      input {
        margin-right: $input-margin;
      }
    }
  }

  .document-content {
    margin-top: $spacing-xl;
  }
}

.no-content-section {
  .no-content-image {
    max-width: 120px;
    margin: 0 auto;
  }

  .no-content-text {
    color: $Neutral-Gray-90;
  }

  .no-content-sub {
    color: $Neutral-Gray-70;
  }

  .break-word {
    word-wrap: break-word;
  }
}

.drop-zone {
  border: $border-width-drag dashed $Primary-78;
  border-radius: $border-radius-md;
  cursor: pointer;
  transition: all 0.3s ease;
  height: $calc-height-docs;
  background: $light-bg;
}

.active-drop-zone {
  border-color: $Primary-78;
  background-color: $hover-blue-bg;
}

.browse-link {
  color: $Primary-90;
  text-decoration: underline;
  cursor: pointer;
  background: none;
  border: none;
}

.files-grid {
  width: 100%;
}

.back-btn {
  border: none;
  background-color: $Neutral-Gray-00;
}

.files-header {
  border-radius: $Radius-8;
  background-color: $Primary-35;
}

.custom-border {
  border: $border-width-std solid $Neutral-Gray-80;
}

.no-hover-effect:hover {
  background-color: inherit !important;
  color: $Primary-78 !important;
}

.delete-popup-content {
  .delete-warning {
    color: $delete-red;
  }

  .warning-text {
    margin-top: $section-margin;
  }
}

.doc-name-text{
  color: $Neutral-Gray-70;
}

.doc-date-text{
  color: $Neutral-Gray-90;
}