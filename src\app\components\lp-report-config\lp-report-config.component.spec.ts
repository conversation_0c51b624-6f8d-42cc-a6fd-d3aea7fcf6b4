import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { LpReportConfigComponent } from './lp-report-config.component';
import { RouterTestingModule } from '@angular/router/testing';
import { LpReportConfigService } from 'src/app/services/lp-report-config.service';
import { FormBuilder, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { ToastrService, ToastrModule } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { of, throwError } from 'rxjs';
import { ChangeDetectorRef } from '@angular/core';
import { LpTemplateConstants } from 'src/app/common/constants';
import { DataOrder, LpReportConfigModel, MappingLpReportKpiSectionModel, TemplateSectionModel } from './models/lp-report-config.model';
import { ButtonsModule } from '@progress/kendo-angular-buttons';
import { CdkDragDrop, DragDropModule } from '@angular/cdk/drag-drop';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { PortfolioCompanyService } from "src/app/services/portfolioCompany.service";
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { OidcAuthService } from 'src/app/services/oidc-auth.service';
import { AccountService } from 'src/app/services/account.service';
import { HTTP_INTERCEPTORS } from '@angular/common/http';

describe('LpReportConfigComponent', () => {
  let component: LpReportConfigComponent;
  let fixture: ComponentFixture<LpReportConfigComponent>;
  let lpReportConfigService: jasmine.SpyObj<LpReportConfigService>;
  let toastrService: jasmine.SpyObj<ToastrService>;
  let router: jasmine.SpyObj<Router>;
  let activatedRoute: Partial<ActivatedRoute>;
  let portfolioCompanyService: jasmine.SpyObj<PortfolioCompanyService>;

  const mockLpReportConfig:LpReportConfigModel = {
    fundList: [{ fundId: 1, fundName: 'Fund 1' }],
    companyList: [{ fundId: 1, fundName: 'Fund 1', companyId: 1, companyName: 'Company 1' }],
    alignments: [{ alignmentId: 1, alignment: 'Left' }],
    sectionFields: [],
    templateSections: [
      {
        sectionId: 1,
        sectionName: 'Test Section',
        moduleId: 1,
        pageConfigAliasName: 'test',
        isKpi: false,
        isStatic: true,
        isCommentary: false,
        kpiTypeId: 0,
        mappingSectionId: 0
      },
    ],
    currencies: [{ currencyId: 1, currencyCode: 'USD', currency: 'US Dollar' }],
    periodTypeModels: [],
    kpiPeriodTypes: [],
    unitTypes: [{ unitTypeId: 1, unitType: 'Test Unit' }],
    frequencyModel: [],
    companyModel:[],
    financialMetricsModel:  [
      { moduleId: 1,headerValue: 'Test Header', lpReportMetricId: 1,groupName: 'Test Group' },
      { moduleId: 1, lpReportMetricId: 2, headerValue: 'Test Header' ,groupName: 'Test Group' },
      { moduleId: 2, lpReportMetricId: 3 , headerValue: 'Test Header',groupName: 'Test Group' }
    ],
    dataOrder : [
      { id: 1, label: 'First Quarter', sequence: 1 },
      { id: 2, label: 'Second Quarter', sequence: 2 },
      { id: 3, label: 'Third Quarter', sequence: 3 },
      { id: 4, label: 'Fourth Quarter', sequence: 4 }
    ]
  };

  const mockTemplateConfig = {
    lpTemplateModel: { templateId: 1, templateName: 'Test Template' },
    mappingLpReportSectionModels: [],
    mappingLpReportTemplateModel: { templateId: 1, fundId: '1', companyId: '1' },
    mappingLpReportCommentarySectionModel: [],
    mappingLpReportKpiSectionModel: [],
    mappingLpReportPeriodSectionModel: []
  };
  let service: any;
  
  beforeEach(async () => {
    const lpReportServiceSpy = jasmine.createSpyObj('LpReportConfigService', [
      'getLpReportTemplateConfig',
      'getLpReportTemplateKpiConfigById',
      'getLpReportTemplateKpiConfig',
      'updateLpReportTemplateConfig',
      'saveLpReportTemplateConfig',
      'isTemplateExist'
    ]);
    const toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['success', 'error']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const portfolioCompanyServiceSpy = jasmine.createSpyObj('PortfolioCompanyService', ['getSDGImages']);

    const activatedRoute = {
      snapshot: {
        params: { id: '1' }
      }
    };

    await TestBed.configureTestingModule({
      declarations: [LpReportConfigComponent],
      imports: [
        RouterTestingModule,
        ReactiveFormsModule,
        FormsModule,
        ToastrModule.forRoot(),
        BrowserAnimationsModule,
        ButtonsModule,
        DragDropModule,
        KendoModule,
        HttpClientTestingModule
      ],
      providers: [
        FormBuilder,
        { provide: LpReportConfigService, useValue: lpReportServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: activatedRoute },
        { provide: PortfolioCompanyService, useValue: portfolioCompanyServiceSpy },
        { provide: 'BASE_URL', useValue: 'http://localhost/' },
        { provide: OidcAuthService, useValue: { getToken: jasmine.createSpy('getToken').and.returnValue('mock-token') } },
        { provide: AccountService, useValue: { getToken: jasmine.createSpy('getToken').and.returnValue('mock-token'), redirectToUnauthorized: jasmine.createSpy('redirectToUnauthorized') } },
        { provide: HTTP_INTERCEPTORS, useValue: {}, multi: true },
        ChangeDetectorRef
      ]
    }).compileComponents();

    lpReportConfigService = TestBed.inject(LpReportConfigService) as jasmine.SpyObj<LpReportConfigService>;
    toastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    portfolioCompanyService = TestBed.inject(PortfolioCompanyService) as jasmine.SpyObj<PortfolioCompanyService>;
  });

  beforeEach(() => {
    lpReportConfigService.getLpReportTemplateConfig.and.returnValue(of(mockLpReportConfig));
    lpReportConfigService.getLpReportTemplateKpiConfigById.and.returnValue(of(mockTemplateConfig));
    lpReportConfigService.getLpReportTemplateKpiConfig.and.returnValue(of([])); // Add this line
    portfolioCompanyService.getSDGImages.and.returnValue(of({
      sdgImages: [
        { id: 1, name: 'SDG1', type: 'image/png', value: 'test-url-1' },
        { id: 2, name: 'SDG2', type: 'image/png', value: 'test-url-2' }
      ]
    }));
    fixture = TestBed.createComponent(LpReportConfigComponent);
    component = fixture.componentInstance;
    component.lpReportConfig = { ...mockLpReportConfig }; // Add this line
    service = {
      updateConfigModel: {
        mappingLpReportKpiSectionModel: [
          { sectionId: 1, mappingSectionId: 100, periodCompareIds: '1,2,3' },
          { sectionId: 1, mappingSectionId: 100, periodCompareIds: '2,3,4' },
          { sectionId: 2, mappingSectionId: 200, periodCompareIds: '5,6' }
        ]
      },
      lpReportConfig: {
        financialMetricsModel: [
          { moduleId: 1, lpReportMetricId: 1 },
          { moduleId: 1, lpReportMetricId: 2 },
          { moduleId: 2, lpReportMetricId: 3 }
        ]
      }
    };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form on ngOnInit', () => {
    component.ngOnInit();
    expect(component.form).toBeTruthy();
    expect(component.form.get('sections')).toBeTruthy();
  });

  it('should handle template config fetch error', fakeAsync(() => {
    // Suppress console.error for this test
    spyOn(console, 'error');

    lpReportConfigService.getLpReportTemplateConfig.and.returnValue(throwError(() => new Error('Error')));
    component.getTemplateConfig();
    tick();
    expect(component.lpReportConfig).toBeNull();
    expect(component.groupedCompanyList).toEqual([]);
    expect(console.error).toHaveBeenCalledWith('Error fetching templates:', jasmine.any(Error));
  }));

  it('should handle form submission for template update', fakeAsync(() => {
    component.id = '1';
    component.templateId = 1;
    component.templateName = 'Updated Template';
    component.selectedCompanyList = [{ companyId: 1 }];
    component.selectedCopyToFundList = [{ fundId: 1 }];
    const mockEvent = { submitter: { innerText: 'Save' } };

    lpReportConfigService.updateLpReportTemplateConfig.and.returnValue(of(true));
    
    component.onSubmit(mockEvent);
    tick();

    expect(lpReportConfigService.updateLpReportTemplateConfig).toHaveBeenCalled();
    expect(toastrService.success).toHaveBeenCalledWith('Template updated successfully', '', jasmine.any(Object));
  }));

  it('should handle template name change', fakeAsync(() => {
    component.templateName = 'Test Template';
    lpReportConfigService.isTemplateExist.and.returnValue(of(false));

    component.onTemplateNameChange();
    tick();

    expect(lpReportConfigService.isTemplateExist).toHaveBeenCalledWith('Test Template');
    expect(component.isTemplateExist).toBeFalse();
  }));

  it('should handle existing template name', fakeAsync(() => {
    component.templateName = 'Existing Template';
    lpReportConfigService.isTemplateExist.and.returnValue(of(true));

    component.onTemplateNameChange();
    tick();

    expect(component.isTemplateExist).toBeTrue();
    expect(toastrService.error).toHaveBeenCalledWith('Template already exists!', '', jasmine.any(Object));
  }));

  it('should add section', () => {
    const mockSection:TemplateSectionModel = {
      sectionId: 1,
      sectionName: LpTemplateConstants.STATIC_INFORMATION,
      moduleId: 0,
      isKpi: false,
      isStatic: true,
      isCommentary: false,
      kpiTypeId: 0,
      pageConfigAliasName: 'test',
      mappingSectionId: 0
    };

    component.addSection(mockSection);
    expect(component.sections.length).toBeGreaterThan(0);
  });

  it('should remove section', () => {
    const mockSection:TemplateSectionModel = {
      sectionId: 1,
      sectionName: LpTemplateConstants.STATIC_INFORMATION,
      moduleId: 0,
      pageConfigAliasName: 'test',
      isKpi: false,
      isStatic: true,
      isCommentary: false,
      kpiTypeId: 0,
      mappingSectionId: 0
    };

    component.addSection(mockSection);
    const initialLength = component.sections.length;
    component.removeSection(0);
    expect(component.sections.length).toBeLessThan(initialLength);
  });

  it('should handle fund selection', () => {
    component.lpReportConfig = mockLpReportConfig;
    component.selectedCopyToFundList = [mockLpReportConfig.fundList[0]];
    component.getFundSelected();
    expect(component.isApply).toBeFalse();
    expect(component.isEditing).toBeFalse();
  });

  it('should handle company selection', () => {
    component.lpReportConfig = mockLpReportConfig;
    component.selectedCopyToFundList = [mockLpReportConfig.fundList[0]];
    component.onCompanyClick();
    expect(component.isCheckedCompanyAll).toBeTrue();
    expect(component.selectedCompanyList.length).toBeGreaterThan(0);
  });

  it('should clear company selection', () => {
    component.selectedCompanyList = [{ companyId: 1 }];
    component.isCheckedCompanyAll = true;
    component.onClear();
    expect(component.selectedCompanyList.length).toBe(0);
    expect(component.isCheckedCompanyAll).toBeFalse();
  });

  it('should reset form', () => {
    spyOn(component.form, 'reset');
    spyOn(component, 'initializeForm');
    component.onReset();
    expect(component.form.reset).toHaveBeenCalled();
    expect(component.initializeForm).toHaveBeenCalled();
  });
  it('should return an empty array if sectionId is not INVESTMENT_COLUMN', () => {
    const result = component.addDefaultPeriod(999);
    expect(result).toEqual([]);
  });
  
  it('should return an empty array if periodModel is not found', () => {
    component.kpiPeriodList = {};
    const result = component.addDefaultPeriod(LpTemplateConstants.INVESTMENT_COLUMN);
    expect(result).toEqual([]);
  });
  
  it('should return an empty array if periodModel.items is not found', () => {
    component.kpiPeriodList = { 4: [{ valueTypeId: 4 }] };
    const result = component.addDefaultPeriod(LpTemplateConstants.INVESTMENT_COLUMN);
    expect(result).toEqual([]);
  });
  
  it('should return an array with "Last 1Q" item if found', () => {
    const mockPeriodModel = {
      valueTypeId: 4,
      items: [{ text: 'Last 1Q' }, { text: 'Next 1Q' }]
    };
    component.kpiPeriodList = { 4: [mockPeriodModel] };
    const result = component.addDefaultPeriod(LpTemplateConstants.INVESTMENT_COLUMN);
    expect(result).toEqual([{ text: 'Last 1Q' }]);
  });
  
  it('should return an empty array if "Last 1Q" item is not found', () => {
    const mockPeriodModel = {
      valueTypeId: 4,
      items: [{ text: 'Next 1Q' }]
    };
    component.kpiPeriodList = { 4: [mockPeriodModel] };
    const result = component.addDefaultPeriod(LpTemplateConstants.INVESTMENT_COLUMN);
    expect(result).toEqual([]);
  });
  it('should clear form and reset template-related properties after saving', () => {
    spyOn(component, 'onReset');
    component.templateName = 'Test Template';
    component.isTemplateExist = true;
  
    component.clearAfterSave();
  
    expect(component.onReset).toHaveBeenCalled();
    expect(component.templateName).toBe('');
    expect(component.isTemplateExist).toBeFalse();
  });
  
  it('should open Save As popup', () => {
    component.openSaveAs = false;
  
    component.openSaveAsPopUp();
  
    expect(component.openSaveAs).toBeTrue();
  });
  it('should open confirmation popup', () => {
    component.openConfirm = false;

    component.openConfirmPopUp();

    expect(component.openConfirm).toBeTrue();
  });
  
  it('should remove section at specified index', () => {
    const mockSection: TemplateSectionModel = {
      sectionId: 1,
      sectionName: LpTemplateConstants.STATIC_INFORMATION,
      moduleId: 0,
      pageConfigAliasName: 'test',
      isKpi: false,
      isStatic: true,
      isCommentary: false,
      kpiTypeId: 0,
      mappingSectionId: 0
    };
  
    component.addSection(mockSection);
    component.addSection(mockSection);
    const initialLength = component.sections.length;
    component.removeSection(0);
    expect(component.sections.length).toBe(initialLength - 1);
  });
  
  it('should not remove section if index is out of bounds', () => {
    const mockSection: TemplateSectionModel = {
      sectionId: 1,
      sectionName: LpTemplateConstants.STATIC_INFORMATION,
      moduleId: 0,
      pageConfigAliasName: 'test',
      isKpi: false,
      isStatic: true,
      isCommentary: false,
      kpiTypeId: 0,
      mappingSectionId: 0
    };
  
    component.addSection(mockSection);
    const initialLength = component.sections.length;
    component.removeSection(5); // Index out of bounds
    expect(component.sections.length).toBe(initialLength);
  });
  it('should handle empty KPI sections', () => {
    let fields= { 
      sectionId: 999, moduleId: 1,pageConfigAliasName:'',isCommentary:false,isKpi:false,
      isStatic:false,sectionName:"s",mappingSectionId:999,kpiTypeId:999
     };
    const result = component.getMatchingKpiSections(fields, 999);
    expect(result).toEqual([]);
  });

  it('should filter metrics by module and IDs', () => {
    const fields= { 
      sectionId: 1, moduleId: 1,pageConfigAliasName:'',isCommentary:false,isKpi:false,
      isStatic:false,sectionName:"s",mappingSectionId:999,kpiTypeId:999
     };
    const uniqueIds = [1, 2];
    const result = component.getSelectedMetrics(fields, uniqueIds);
    expect(result.length).toBe(2);
    expect(result[0].moduleId).toBe(1);
  });
  it('should group metrics by moduleId', () => {
    component.groupByPeriodComparison();
    
    expect(Object.keys(component.periodComparisonList).length).toBe(1);
  });

  // Add test for getSDGImageList
  it('should fetch SDG images successfully', fakeAsync(() => {
    component.getSDGImageList(1).subscribe(images => {
      expect(images.length).toBe(2);
      expect(images[0].id).toBe(1);
      expect(images[0].url).toBe('test-url-1');
    });
    tick();
    expect(portfolioCompanyService.getSDGImages).toHaveBeenCalledWith(1);
  }));

  it('should handle empty SDG images response', fakeAsync(() => {
    portfolioCompanyService.getSDGImages.and.returnValue(of({ sdgImages: [] }));
    component.getSDGImageList(1).subscribe(images => {
      expect(images).toEqual([]);
    });
    tick();
  }));

  it('should handle error in SDG images request', fakeAsync(() => {
    // Suppress console.error for this test
    spyOn(console, 'error');

    portfolioCompanyService.getSDGImages.and.returnValue(throwError(() => new Error('Error')));
    component.getSDGImageList(1).subscribe(images => {
      expect(images).toEqual([]);
    });
    tick();
  }));
  it('should set show property to false when onEscape is called', () => {
    // Arrange
    component.show = true;
  
    // Act
    component.onEscape();
  
    // Assert
    expect(component.show).toBeFalse();
  });
  
  it('should keep show property false when onEscape is called while already false', () => {
    // Arrange
    component.show = false; 
    // Act
    component.onEscape();
  
    // Assert
    expect(component.show).toBeFalse();
  });
  
  it('should return unique IDs from KPI sections for a given field', () => {
    const mockSections: MappingLpReportKpiSectionModel[] = [
      {
        sectionId: 1,
        mappingSectionId: 1,
        kpiIds: '1,2',
        periodCompareIds: '1,2',
        dataOrder: '2,4',
        companyId: 1,
      },
      {
        sectionId: 1,
        mappingSectionId: 1,
        kpiIds: '2,3',
        periodCompareIds: '2,3',
        dataOrder: '4,1',
        companyId: 2,
      }
    ];
    // Test dataOrder field
    const dataOrderIds = component.getUniqueIds(mockSections, 'dataOrder');
    expect(dataOrderIds).toEqual([2, 4, 1]);

    // Test periodCompareIds field
    const periodCompareIds = component.getUniqueIds(mockSections, 'periodCompareIds');
    expect(periodCompareIds).toEqual([1, 2, 3]);

    // Test kpiIds field
    const kpiIds = component.getUniqueIds(mockSections, 'kpiIds');
    expect(kpiIds).toEqual([1, 2, 3]);
  });
  it('should handle template kpi config fetch', fakeAsync(() => {
    component.selectedCompanyList = [{ companyId: 1, companyName: 'Test Company' }];
    component.getTemplateKpiConfig();
    tick();
    expect(lpReportConfigService.getLpReportTemplateKpiConfig).toHaveBeenCalled();
    expect(component.kpiItemList).toBeDefined();
  }));

  // Add cleanup in afterAll
  afterAll(() => {
    component.lpReportConfig = null; // Add this line
    TestBed.resetTestingModule();
  });
});

