@import "../../../../../_variables.scss";
@import "../../../../../assets/dist/css/font.scss";
.email-reminder-details {
    border-radius: 4px;

    .email-remainder-details-content{
        width: 100%;
    }
    .section-row {
        display: flex;
        align-items: flex-start;
        margin-bottom: 24px;
        padding-bottom: 5px;
        border-bottom: 1px solid $Neutral-Gray-10;
        flex-direction: column;

        &:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        &.no-border{
            border-bottom: none;
            margin-bottom: 0;
        }
    }

    .section-label {
        width: 100%;
        margin-top: 2px;
        flex-shrink: 0;
        padding-bottom: 5px;
    }

    .section-content {
        flex: 1;
        margin-left: 24px;
        &.no-margin{
            margin-left: 0;
            width: 100%;
        }
    }

    .email-chips-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .email-chip {
        background-color: $Primary-40;
        border: none;
        border-radius: 12px;
        padding: 6px 12px;
        line-height: 1;
        &.group-chip {
            background-color: $Primary-40;
            border: none;
        }
    }

    .subject-field {
        background-color: $Neutral-Gray-02;
        border-bottom: 1px solid $Neutral-Gray-10;
        border-radius: 4px;
        padding: 8px 12px;        
        min-height: 36px;
        display: flex;
        align-items: center;
        width: 100%;
    }

    .message-content {
        background-color: $Neutral-Gray-02;
        border: 1px solid $Neutral-Gray-05;
        border-radius: 4px;
        padding: 12px;
        min-height: 120px;       
    }
    .followup-content {
        border: 1px solid $Neutral-Gray-05;
        border-radius: 4px;
        padding: 16px;
        min-height: 80px;        
    }

    .active-status {
        color: $Primary-78;
    }
   
}

// Responsive adjustments
@media (max-width: 768px) {
    .email-reminder-details {
        .section-row {
            flex-direction: column;
            gap: 8px;
        }

        .section-label {
            min-width: auto;
            width: auto;
        }

        .section-content {
            margin-left: 0;
        }

        .email-chip {
            font-size: 13px;
            padding: 4px 10px;
        }

        .status-info {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }
    }
}
