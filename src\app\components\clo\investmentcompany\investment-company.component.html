<div class="row ml-4 mr-4 clo-container" *ngIf="gridData?.length > 0; else noInvestmentCompanies">
      <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0">
      <div class="card-table row mr-0 ml-0">
        <div class="c-table-header col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 Heading2-R TextTruncate">
          <img class="image-custom" src='assets/dist/images/Frame.svg' alt="Company Icon">
          Company Names ({{ gridData?.length }})
          <div class="add-company-button-container float-right">
            <button class="add-company-button" (click)="addRedirect()" id="addinvestmentform">
                <img src='assets/dist/images/addicon.svg' alt="add Icon"> Add Investment Company
            </button>
          </div>
        </div>
      
        <div class="clo-table-body col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 Heading2-R pr-0 pl-0">
          <div class="row mr-0 ml-0 clo-table-content">
            <ng-container *ngFor="let company of gridData; let i = index">
              <div class="company-card pl-3 pr-3">
                <div class="company-list-content">
                  <span class="company-card-header" (click)="redirectToCompData(company.id)" (keypress)="redirectToCompData(company.id)">{{ company.companyName }}</span>
                  <div id="delete-icon" class="delete-icon" (click)="showDeletePopup(company.id, company.companyName)">
                    <img src='assets/dist/images/delete-company.svg' alt="Delete Icon" >
                  </div>
                </div>
              </div>              
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>
  <ng-template #noInvestmentCompanies>
    <div class="row no-data mb-4 ml-4 mr-4 align-items-center justify-content-center">
      <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12">
        <div class="row text-center">
          <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12">
            <img src='assets/dist/images/WelcomeToFolioSure.svg' alt="Welcome to Foliosure">
          </div>
          <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 Caption-M welcome-text">
            Organise your investment and improve your performance with FolioSure.
          </div>
          <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 mt-60">
            <img src='assets/dist/images/Illustrations.svg' alt="No Companies Illustration">
          </div>
          <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 mt-8">
            <span class="info-text Body-R">Click On Below Button to Add Investment Company</span>
          </div>
          <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 mt-4">
            <button class="add-company-button Body-R" (click)="addRedirect()" id="addinvestmentform">
              <img src='assets/dist/images/addicon.svg' alt="add Icon"> Add Investment Company
            </button>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

  <div *ngIf="showPopup">
    <app-delete-confirmation-modal  [modalTitle]="'Delete Investment Company'" [deletedCloName]="investmentCompanyName" [deleteNoteType]="Company" (PrimaryButtonEventHandler)="deleteInvestmentCompany()" (SecondaryButtonEventHandler)="hideDeletePopup()"></app-delete-confirmation-modal>
  </div>
  <app-loader-component *ngIf="isLoading"></app-loader-component>