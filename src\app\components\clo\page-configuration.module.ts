import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PageConfigurationComponent } from './page-configuration/page-configuration.component';
import { RouterModule } from '@angular/router';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { MatMenuModule } from '@angular/material/menu';
import { SharedCloModule } from './shared-clo.module';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { FormsModule } from '@angular/forms';



@NgModule({
  declarations: [
    PageConfigurationComponent
  ],
  imports: [
    CommonModule,
    SharedComponentModule,
    KendoModule,
    MatMenuModule,
    FormsModule,
    RouterModule.forChild([
          { path: '', component: PageConfigurationComponent}
      ]),
           SharedCloModule
  ]
})
export class PageConfigurationModule { }
