@import "../../../../_variables.scss";

.automated-email-section {
    border-radius: $Spacing-8;
    border: 1px solid $Neutral-Gray-10;

    .section-header {
        border-bottom: 1px solid $Primary-90;
        cursor: pointer;
        border-radius: $Radius-4 $Radius-4 0 0;
    }

    .border-none {
        border: none !important;
    }

    .section-content {
        transition: all 0.3s ease;
    }

    .dropdown-container {
        width: 100%;
    }

    // Portfolio company dropdown (60%)
    .portfolio-company-dropdown {
        flex: 0 0 60%;
        max-width: 60%;
    }

    // Document type dropdown (40%)
    .document-type-dropdown {
        flex: 0 0 40%;
        max-width: 40%;
    }

    .inline-container {
        display: flex;
        align-items: center;
    }

    .doc-type-item {
        display: flex;
        width: 100%;
    }

    .item-container {
        display: flex;
        width: 100%;
    }

    // Multiselect styles
    .k-multiselect-custom {
        width: 100% !important;
    }
}

// For collapsing animation
.section-content {
    &.collapsed {
        height: 0;
        overflow: hidden;
        padding: 0 !important;
    }
}

.company-tag {
    padding: $content-padding-4 $padding-medium;
    border-radius: $Radius-4;
    display: inline-block;
}

.expanded-content {    
    border-radius: 0 0 $Radius-4 $Radius-4;
    padding: $Spacing-12;    
}

.reminder-item {
    transition: all 0.3s ease;
    box-shadow: $shadow-short;
    border-radius: $Radius-4;
}

.text-black {
    color: black;
}

.space-lr {
    margin-left: $padding-medium;
    margin-right: $padding-medium;
    cursor: pointer;
}

.space-l {
    margin-left: $padding-medium;
    cursor: pointer;
}

.toggle-container {
    display: flex;
    align-items: center;
}

.active-status {
    color: #4061C7;
}

.doctype-text {
    color: #1A1A1A;
}
.custom-cursor {
    cursor: pointer;
}