export interface CreateJobs {
  jobId: string;
  processId: string;
  tenantId: string;
  statusId: string;
  parentJobId?: string;
}

export interface DocumentDetailDto extends CreateJobs {
  documentId: string;
  companyName: string;
  companyId: number;
  createdOn: Date | null;
  status: string | null;
  acuityId: string | null;
  country: string | null;
  sector: string | null;
  isPublic: boolean | null;
  exchange: string | null;
  ticker: string | null;
  period: string | null;
  startDate: Date | null;
  CreatedBy: string | null;
  id: string | null;
  state: string | null;
  fundName: string | null;
  fundId: string | null;
  extractionType: string | null;
}

export interface UploadResponse {
  documentId: string;
  url: string;
  processId: string;
}
export interface S3UrlExtractedModel extends CreateJobs{
  filename: string;
  s3Url: string;
}

export interface CommonFileModel {
  file_name: string;
  s3_path: string;
  source: string;
  file_id?: string; 
}

export interface IngestionStartModel {
  files: CommonFileModel[];
}
export interface IngestConfigurationModel {
  app_name: string;
  output_version: string;
  client_id: string;
  session_id: string;
  job_type: string;
  notes_extraction: boolean;
  is_llm_agent: boolean;
  job_engine: string;
  company_id: string;
  company_name: string;
  country: string;
  sector: string;
  industry: string;
  ticker: string;
  template: boolean;
  template_id: string;
  as_reported: boolean;
  kpi_json: boolean;
  files: CommonFileModel[];
}
export interface FsFSClassifier{
  job_type: string;
}
export interface IngestionResponseModel {
  isError: boolean;
  timestamp: string;
  version: string;
  message: string;
  files: CommonFileModel[];
  job_id: string;
}
export interface StateAndStatus {
state: string;
name: string;
id: string;
}
export interface ProcessDetailsDto {
  processId: string; 
  documents: DocumentSummaryDto[];
  companyId: string;
  companyName: string;
  year: number;
  month: string;
  quarter: string;
  periodType: string;
  encryptedPortfolioCompanyId: string;
  status: string;
  state: string;
  jobId: string;
  tenantId: string;
  statusId: string;
  parentJobId?: string;
  isClassifiers?: boolean|false;
  fundName: string | null;
  fundId: number | null;
  extractionType: string | null;
  encryptedFundId: string | null;
}

export interface DocumentSummaryDto extends CreateJobs {
  s3Path: string;
  name: string;
  url: string;
  id: string; 
  documentTypeId:number;
  documentType: string | null;
  errors: string[];
  status: string;
  type: string;
  extension: string; 
}
export interface SpecificKpiIngestConfigurationModel extends IngestConfigurationModel {
  kpi_template_id: string;
  retry: boolean;
  job_start: boolean;
}
export interface SpecificKpiJobStartResponseModel extends IngestionResponseModel {
  status: string;
}
export interface RepositoryNode {
  name: string;
  path: string;
  children: RepositoryNode[] | null;
}

export interface FileConfigurationDetails {
  fileId: string;
  documentTypeId: number;
  documentType: string | null;
  periodType: string | null;
  year: number | null;
  month: string | null;
  quarter: string | null;
} 

export interface SpecificKPIsJsonModel {
    module_name: string;
    investment_name: string;
    kpis_list: any;
    issuer_kpis_list: any[];
} 