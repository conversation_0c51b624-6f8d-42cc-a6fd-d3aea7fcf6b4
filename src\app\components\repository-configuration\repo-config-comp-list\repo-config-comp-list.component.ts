
import { Component, EventEmitter, Output, Input } from '@angular/core';
import { PageConfigurationService } from 'src/app/services/page-configuration.service';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';

@Component({
  selector: 'app-repository-configuration-company-list',
  templateUrl: './repo-config-comp-list.component.html',
  styleUrls: ['./repo-config-comp-list.component.scss']
})

export class RepositoryConfigurationCompanyListComponent {
  resetInProgress: boolean = false;
  @Output() selectedCompanies = new EventEmitter<any>();
  @Output() selectedFunds = new EventEmitter<any>();
  @Output() isFundChange = new EventEmitter<boolean>();
  @Input() preSelectedCompanies: any[] = [];
  @Input() showSelectionContainer: boolean = true;
  searchTerm: string = '';
  inputValue: string = '';
  selectAll: boolean = false;
  selectAllCompanies: boolean = false;
  isCompaniesIndeterminate: boolean = false;
  selectAllFunds: boolean = false;
  isFundsIndeterminate: boolean = false;
 
  companies: { name: string, selected: boolean, companyId: string }[] = [];
  searchedCompanies: { name: string, selected: boolean, companyId: string }[] = [];
  searchedFunds: { fundName: string, selected: boolean, fundId: string }[] = [];
  isLoader: boolean = false;
  debounceTimeout: any;
  funds: { fundName: string, selected: boolean, fundId: string }[] = [];
  showFunds: boolean = false;
  fieldValueList: any[] = [];
  fundDisplayName: string = 'Funds';
  pcDisplayName: string = 'Portfolio Company';
  showFundSection: boolean = false;
  showPCSection: boolean = false;

  constructor(
    private repositoryConfigService: RepositoryConfigService,
    private pageConfigurationService: PageConfigurationService,
  ) { }

  ngOnInit() {
    this.repositoryConfigService.resetInProgress$.subscribe((state: boolean) => {
      this.resetInProgress = state;
    });
    this.getPageConfigSetting();
  }

  getPageConfigSetting(): void {
    this.pageConfigurationService.getPageConfigSettingById(12).subscribe(
      (result: any) => {
        if (result && result.fieldValueList) {
          this.fieldValueList = result.fieldValueList;
          // Find display names
          const fundObj = this.fieldValueList.find((f: any) => f.name === 'Funds');
          const pcObj = this.fieldValueList.find((f: any) => f.name === 'Portfolio Company');
          this.fundDisplayName = fundObj ? fundObj.displayName : 'Funds';
          this.pcDisplayName = pcObj ? pcObj.displayName : 'Portfolio Company';
          this.showFundSection = !!fundObj;
          this.showPCSection = !!pcObj;
          // If only one is present, show only that section and call the method
          if (this.showFundSection && !this.showPCSection) {
            this.showFunds = true;
            this.getFundListData();
          } else if (!this.showFundSection && this.showPCSection) {
            this.showFunds = false;
            this.getFundsAndPcs();
          } else if (this.showFundSection && this.showPCSection) {
            // Both present, default to Portfolio Company
            this.showFunds = false;
            this.getFundsAndPcs();
          }
        }
      }
    );
  }

  getFundListData() {
  this.showFunds = true;
  this.searchTerm = '';
  // Uncheck select all and clear all company selections when switching to funds
  this.selectAllCompanies = false;
  this.isCompaniesIndeterminate = false;
  if (this.companies && this.companies.length > 0) {
    this.companies.forEach(company => company.selected = false);
  }
  this.isFundChange.emit(this.showFunds);
  this.selectedCompanies.emit(null);
  this.isLoader = true;
  this.repositoryConfigService.getFunds().subscribe(
      (data: any) => {
        this.funds = data.map((fund: any) => ({
          fundId: fund.fundID, // Map fundID from API to fundId used in component
          fundName: fund.fundName,
          selected: false
        }));
        this.isLoader = false;
      },
      (error: any) => {
        console.error('Error fetching fund list:', error);
        this.isLoader = false;
      }
    );
  }

  getFundsAndPcs() {
  this.showFunds = false;
  this.searchTerm = '';
  // Uncheck select all and clear all fund selections when switching to Portfolio Company
  this.selectAllFunds = false;
  this.isFundsIndeterminate = false;
  if (this.funds && this.funds.length > 0) {
    this.funds.forEach(fund => fund.selected = false);
  }
  this.isFundChange.emit(this.showFunds);
  this.selectedFunds.emit(null);
  this.isLoader = true;
  this.repositoryConfigService.getPortfolioCompanies().subscribe(
      (data: any) => {
        this.companies = data.map((company: any) => ({
          name: company.companyName,
          companyId: company.portfolioCompanyID,
          selected: false
        }));
        this.applyPreselections();
        this.isLoader = false;
      },
      (error: any) => {
        console.error('Error fetching portfolio companies:', error);
        this.isLoader = false;
      }
    );
  }

  get filteredCompanies() {
    return this.companies.filter(company =>
      company.name.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  toggleSelectAll() {
    this.filteredCompanies.forEach(company => company.selected = this.selectAll);
    const selectedPortfolioCompanies = this.filteredCompanies.filter(company => company.selected);
    if (selectedPortfolioCompanies.length > 0) {
      this.selectedCompanies.emit(selectedPortfolioCompanies);
    } else {
      this.selectedCompanies.emit(null);
      this.repositoryConfigService.displayConfigurationConflict = false; // Set to false when deselected
    }
  }
 // Select All Companies logic
  toggleSelectAllCompanies() {
    // Always update selection on the full companies list
    this.companies.forEach(company => company.selected = this.selectAllCompanies);
    this.isCompaniesIndeterminate = false;
    this.checkSelection();
  }

  // Update selectAllCompanies: checked only if all are selected, otherwise unchecked (no indeterminate)
  updateSelectAllCompaniesState() {
    // Always check selection state based on the full companies list
    const total = this.companies.length;
    const selected = this.companies.filter(c => c.selected).length;
    this.selectAllCompanies = selected === total && total > 0;
    this.isCompaniesIndeterminate = false;
  }
  checkSelection() {
    this.updateSelectAllCompaniesState();
    const selectedPortfolioCompanies = this.companies.filter(company => company.selected);
    if (selectedPortfolioCompanies) {
      this.selectedCompanies.emit(selectedPortfolioCompanies);
    } else {
      this.selectedCompanies.emit(null);
    }
  }

  fundCheckSelection() {
    this.updateSelectAllFundsState();
    const selectedFunds = this.funds.filter(fund => fund.selected);
    if (selectedFunds) {
      this.selectedFunds.emit(selectedFunds);
    } else {
      this.selectedFunds.emit(null);
    }
  }

  debounce(func: Function, wait: number): Function {
    return (...args: any[]) => {
      clearTimeout(this.debounceTimeout);
      this.debounceTimeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  onInputChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value.trim().toLowerCase();
    if (this.showFunds) {
      this.searchedFunds = this.searchTerm
        ? this.funds.filter(fund => fund.fundName.toLowerCase().includes(this.searchTerm))
        : [];
    } else {
      this.searchedCompanies = this.searchTerm
        ? this.companies.filter(company => company.name.toLowerCase().includes(this.searchTerm))
        : [];
    }
  }

  // Add a method to apply pre-selections after companies are loaded
  private applyPreselections() {
    if (this.preSelectedCompanies && this.preSelectedCompanies.length > 0) {
      this.companies.forEach(company => {
        company.selected = this.preSelectedCompanies.some(
          selected => selected.companyId.toString() === company.companyId.toString()
        );
      });
      // Emit initially selected companies
      const selectedPortfolioCompanies = this.companies.filter(company => company.selected);
      if (selectedPortfolioCompanies.length > 0) {
        this.selectedCompanies.emit(selectedPortfolioCompanies);
      }
    }
  }
  
  private _cachedSelectedFunds: { fundName: string, selected: boolean, fundId: string }[] = [];
  private _cachedNonSelectedFunds: { fundName: string, selected: boolean, fundId: string }[] = [];
  private _lastFunds: { fundName: string, selected: boolean, fundId: string }[] = [];
  private _lastSearchTerm: string = '';
  private _lastShowFunds: boolean = false;

  private filterFunds(selected: boolean): { fundName: string, selected: boolean, fundId: string }[] {
    if (!this.funds) return [];
    // Only recalculate if dependencies changed
    if (
      this.funds !== this._lastFunds ||
      this.searchTerm !== this._lastSearchTerm ||
      this.showFunds !== this._lastShowFunds
    ) {
      const searchTerm = this.searchTerm ? this.searchTerm.toLowerCase() : '';
      this._cachedSelectedFunds = this.funds.filter(f => f.selected && (!this.showFunds || !searchTerm || f.fundName.toLowerCase().includes(searchTerm)));
      this._cachedNonSelectedFunds = this.funds.filter(f => !f.selected && (!this.showFunds || !searchTerm || f.fundName.toLowerCase().includes(searchTerm)));
      this._lastFunds = this.funds;
      this._lastSearchTerm = this.searchTerm;
      this._lastShowFunds = this.showFunds;
    }
    return selected ? this._cachedSelectedFunds : this._cachedNonSelectedFunds;
  }

  get selectedFundItems() {
    return this.filterFunds(true);
  }

  get nonSelectedFundItems() {
    return this.filterFunds(false);
  }
  toggleSelectAllFunds() {
    // Always update selection on the full funds list
    this.funds.forEach(fund => fund.selected = this.selectAllFunds);
    this.isFundsIndeterminate = false;
    this.fundCheckSelection();
  }

  // Update selectAllFunds: checked only if all are selected, otherwise unchecked (no indeterminate)
  updateSelectAllFundsState() {
    // Always check selection state based on the full funds list
    const total = this.funds.length;
    const selected = this.funds.filter(f => f.selected).length;
    this.selectAllFunds = selected === total && total > 0;
    this.isFundsIndeterminate = false;
  }
}