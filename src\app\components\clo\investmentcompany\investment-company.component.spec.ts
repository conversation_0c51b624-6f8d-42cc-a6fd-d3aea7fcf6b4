import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ToastrModule } from 'ngx-toastr';
import { RouterTestingModule } from '@angular/router/testing';
import { InvestmentCompanyComponent } from './investment-company.component';
import { InvestCompanyService } from './investmentcompany.service';
import { FormsModule } from '@angular/forms';
import { of } from 'rxjs';

describe('InvestmentCompanyComponent', () => {
    let component: InvestmentCompanyComponent;
    let fixture: ComponentFixture<InvestmentCompanyComponent>;
    let mockService: jasmine.SpyObj<InvestCompanyService>;

    beforeEach(async () => {
        mockService = jasmine.createSpyObj('InvestCompanyService', [
            'deleteInvestmentCompany',
            'getInvestCompanyList',
            'setGridData', // <-- Add this
        ]);
        mockService.getInvestCompanyList.and.returnValue(of([]));
        await TestBed.configureTestingModule({
            imports: [
                HttpClientTestingModule,
                RouterTestingModule,
                ToastrModule.forRoot(),
                FormsModule
            ],
            declarations: [InvestmentCompanyComponent],
            providers: [
                { provide: InvestCompanyService, useValue: mockService },
                { provide: 'BASE_URL', useValue: 'testBaseUrl' }
            ]
        }).compileComponents();
    });
    beforeEach(() => {
        fixture = TestBed.createComponent(InvestmentCompanyComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
        component.id = 1; // Set the id to the value you expect
    });

    it('should call DeleteInvestmentCompany when deleteInvestmentCompany is invoked', () => {
  mockService.deleteInvestmentCompany.and.returnValue(of({})); // Set up spy FIRST
  component.id = 1;
  component.showDeletePopup(1, 'Test Company');
  component.deleteInvestmentCompany();
  expect(mockService.deleteInvestmentCompany).toHaveBeenCalledWith(1);
});
});