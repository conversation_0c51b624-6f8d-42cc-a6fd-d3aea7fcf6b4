import { TestBed } from "@angular/core/testing";
import { BulkUploadModule } from "./bulk-upload.module";
import { MiscellaneousService } from "../../services/miscellaneous.service";
describe("BulkUploadModule", () => {
  let pipe: BulkUploadModule;

  beforeEach(() => {
    // Mock service
    const miscellaneousServiceStub = () => ({
    });

    TestBed.configureTestingModule({
      providers: [
        BulkUploadModule,
        { provide: MiscellaneousService, useFactory: miscellaneousServiceStub }
      ],
    });

    pipe = TestBed.inject(BulkUploadModule);
  });

  it("can load instance", () => {
    expect(pipe).toBeTruthy();
  });
});
