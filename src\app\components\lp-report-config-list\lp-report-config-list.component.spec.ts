import { LpReportConfigListComponent } from "./lp-report-config-list.component";
import { LpReportConfigService } from "src/app/services/lp-report-config.service";
import { Router } from "@angular/router";
import { of, throwError } from "rxjs";
import { GridModule } from "@progress/kendo-angular-grid";
import { NO_ERRORS_SCHEMA } from "@angular/core";
import { ComponentFixture, TestBed } from "@angular/core/testing";
import { HttpClientTestingModule } from "@angular/common/http/testing";
import { DialogAction } from "@progress/kendo-angular-dialog";
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

describe("LpReportConfigListComponent", () => {
  let component: LpReportConfigListComponent;
  let fixture: ComponentFixture<LpReportConfigListComponent>;
  let mockLpReportConfigService: any;
  let mockRouter: any;

  beforeEach(async () => {
    mockLpReportConfigService = jasmine.createSpyObj("LpReportConfigService", {
      getLpReportTemplates: of([{ templateName: "Template1" }]),
      deleteLpReportTemplates: of(true),
    });
    mockRouter = jasmine.createSpyObj("Router", ["navigate"]);

    await TestBed.configureTestingModule({
      declarations: [LpReportConfigListComponent],
      imports: [
        GridModule, 
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ToastrModule.forRoot()
      ],
      providers: [
        { provide: LpReportConfigService, useValue: mockLpReportConfigService },
        { provide: Router, useValue: mockRouter },
        ToastrService
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  });
  beforeEach(() => {
    fixture = TestBed.createComponent(LpReportConfigListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it("should create", () => {
    expect(component).toBeTruthy();
  });

  it("should fetch templates on init", () => {
    const mockData = [{ templateName: "Template1" }];
    mockLpReportConfigService.getLpReportTemplates.and.returnValue(
      of(mockData)
    );

    component.ngOnInit();

    expect(component.isLoader).toBe(false);
    expect(component.templateList).toEqual(mockData);
    expect(component.templateListClone).toEqual(mockData);
  });

  it("should handle error while fetching templates", () => {
    mockLpReportConfigService.getLpReportTemplates.and.returnValue(
      throwError("Error")
    );

    component.ngOnInit();

    expect(component.isLoader).toBe(false);
    expect(component.templateList).toEqual([]);
    expect(component.templateListClone).toEqual([]);
  });

  it("should filter grid based on input value", () => {
    component.templateListClone = [
      { templateName: "Template1" },
      { templateName: "Template2" },
    ];
    component.filterGrid("Template1");

    expect(component.templateList.length).toBe(1);
    expect(component.templateList[0].templateName).toBe("Template1");
  });

  it("should reset grid filter when input value is empty", () => {
    component.templateListClone = [
      { templateName: "Template1" },
      { templateName: "Template2" },
    ];
    component.filterGrid("");

    expect(component.templateList.length).toBe(2);
  });

  it("should navigate to new template creation page", () => {
    component.redirectToTemplate(null);

    expect(localStorage.getItem("headerName")).toBe("New Template");
    expect(mockRouter.navigate).toHaveBeenCalledWith(["/lp-report-template"]);
  });

  it("should navigate to existing template page", () => {
    const template = { templateName: "Template1", encryptedTemplateId: "123" };
    component.redirectToTemplate(template);

    expect(localStorage.getItem("headerName")).toBe("Template1");
    expect(mockRouter.navigate).toHaveBeenCalledWith([
      "/lp-report-template",
      "123",
    ]);
  });

  it("should open delete template dialog when template is provided", () => {
    const template = { templateName: "Template1", encryptedTemplateId: "123" };
    component.openDeleteTemplate(template);

    expect(component.deleteLpTemplate).toBe(true);
    expect(component.templateToDelete).toEqual(template);
  });

  it("should not open delete template dialog when template is null", () => {
    component.deleteLpTemplate = false;
    component.templateToDelete = {};
    
    component.openDeleteTemplate(null);

    expect(component.deleteLpTemplate).toBe(false);
    expect(component.templateToDelete).toEqual({});
  });

  it("should close delete template dialog", () => {
    component.deleteLpTemplate = true;
    component.templateToDelete = { templateName: "Template1" };
    
    component.closeDeleteTemplate("Cancel");

    expect(component.deleteLpTemplate).toBe(false);
    expect(component.templateToDelete).toEqual({});
  });

  it("should delete template when confirm action is clicked", () => {
    const template = { templateName: "Template1", encryptedTemplateId: "123" };
    component.templateToDelete = template;
    component.deleteLpTemplate = true;
    
    const confirmAction: DialogAction = { text: "Confirm" };
    spyOn(component, 'getTemplates');
    
    component.deleteTemplate(confirmAction);

    expect(mockLpReportConfigService.deleteLpReportTemplates).toHaveBeenCalledWith("123");
    expect(component.getTemplates).toHaveBeenCalled();
    expect(component.deleteLpTemplate).toBe(false);
    expect(component.templateToDelete).toEqual({});
  });

  it("should close delete dialog when cancel action is clicked", () => {
    const cancelAction: DialogAction = { text: "Cancel" };
    spyOn(component, 'closeDeleteTemplate');
    
    component.deleteTemplate(cancelAction);

    expect(component.closeDeleteTemplate).toHaveBeenCalledWith("Cancel");
  });

  it("should handle error when deleting template", () => {
    const template = { templateName: "Template1", encryptedTemplateId: "123" };
    component.templateToDelete = template;
    component.deleteLpTemplate = true;
    
    mockLpReportConfigService.deleteLpReportTemplates.and.returnValue(throwError("Error"));
    spyOn(console, 'error');
    
    const confirmAction: DialogAction = { text: "Confirm" };
    component.deleteTemplate(confirmAction);

    expect(component.isLoader).toBe(false);
  });
});
