import { Component, Input } from '@angular/core';
import { InvestCompanyService } from '../investmentcompany/investmentcompany.service';


@Component({
  selector: 'investment-company-review-form',
  templateUrl: './investment-company-review-form.component.html',
  styleUrls: ['./investment-company-review-form.component.scss']
})
export class InvestmentCompanyReviewFormComponent {
  @Input() InvestmentCompanyModel: any;
  formattedInvestmentCompanyModel: any;
  constructor(private investCompanyService: InvestCompanyService) {}
  ngOnInit(): void {
    this.formattedInvestmentCompanyModel = { ...this.InvestmentCompanyModel };
    
  }

  triggerGoToStep(step: number): void {
    this.investCompanyService.emitGoToStep(step); 
  }

}