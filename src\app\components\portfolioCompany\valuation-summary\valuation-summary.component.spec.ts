import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ValuationSummaryComponent } from './valuation-summary.component';
import { PortfolioCompanyService } from "../../../services/portfolioCompany.service";
import { ToastrService } from "ngx-toastr";
import { FormBuilder } from '@angular/forms';
import { of } from 'rxjs';
import { MatMenuModule } from '@angular/material/menu';
import { LabelModule } from '@progress/kendo-angular-label';
import { ComboBoxModule } from '@progress/kendo-angular-dropdowns';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ReactiveFormsModule } from '@angular/forms';

describe('ValuationSummaryComponent', () => {
  let component: ValuationSummaryComponent;
  let fixture: ComponentFixture<ValuationSummaryComponent>;
  let mockPortfolioCompanyService: any;
  let mockToastrService: any;

  beforeEach(async () => {
    mockPortfolioCompanyService = {
      getValuationSummaryData: jasmine.createSpy('getValuationSummaryData').and.returnValue(of([]))
    };
    mockToastrService = {
      success: jasmine.createSpy('success').and.callFake((msg, title, options) => {
        options.positionClass = 'toast-top-right';
      })
    };
    const mockMenuTrigger = {
      closeMenu: jasmine.createSpy('closeMenu')
    };

    await TestBed.configureTestingModule({
      imports: [MatMenuModule, LabelModule, ComboBoxModule, BrowserAnimationsModule, ReactiveFormsModule],
      declarations: [ ValuationSummaryComponent ],
      providers: [
        { provide: PortfolioCompanyService, useValue: mockPortfolioCompanyService },
        { provide: ToastrService, useValue: mockToastrService },
        FormBuilder
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ValuationSummaryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ValuationSummaryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component', () => {
    spyOn(component, 'initializeComponent');
    component.ngOnInit();
    expect(component.initializeComponent).toHaveBeenCalled();
  });

  it('should populate year options', () => {
    component.populateYearOptions();
    expect(component.yearOptions.length).toBe(102); // Update the expected length to match the actual length
  });

  it('should prepare valuation summary request', () => {
    component.selectedTab = { id: 1 };
    component.fundId = '1';
    component.pcId = 1;
    component.filterForm.setValue({
      periodType: { periodTypeId: 1 },
      fromQuarter: { value: 'Q1' },
      fromYear: { value: 2020 },
      toQuarter: { value: 'Q2' },
      toYear: { value: 2021 }
    });
    const request = component.prepareValuationSummaryRequest();
    expect(request).toEqual({
      valuationCategoryId: 1,
      fundId: '1',
      companyId: 1,
      fromQuarter: 'Q1',
      fromYear: 2020,
      toQuarter: 'Q2',
      toYear: 2021,
      periodId: 1
    });
  });

  it('should check if value is a number', () => {
    expect(component.isNumberCheck('123')).toBe(true);
    expect(component.isNumberCheck('abc')).toBe(false);
  });

  it('should fetch valuation summary', (done) => {
    const response = [{ quarter: 'Q1', year: 2020 }];
    mockPortfolioCompanyService.getValuationSummaryData.and.returnValue(of(response));
    spyOn(component, 'processValuationSummaryResponse');
    component.getValuationSummary();
    fixture.whenStable().then(() => {
      fixture.detectChanges();
      expect(mockPortfolioCompanyService.getValuationSummaryData).toHaveBeenCalled();
      expect(component.processValuationSummaryResponse).toHaveBeenCalledWith(response);
      expect(component.isLoading).toBe(false);
      done();
    });
  });

  it('should process valuation summary response', () => {
    const response = [{ quarter: 'Q1', year: 2020 }];
    spyOn(component, 'createRowForKpi').and.returnValue({});
    component.processValuationSummaryResponse(response);
    expect(component.tableColumnsHeaders.length).toBe(1);
    expect(component.valuationSummaryModel.length).toBe(component.kpiList.length);
  });

  it('should create row for KPI', () => {
    const response = [{ quarter: 'Q1', year: 2020, mean: 1, median: 2, adjustmentTypeId: 1, adjustmentFactor: 10, selectedComps: 'mean' }];
    const row = component.createRowForKpi('Mean Multiple', response);
    expect(row).toEqual({ Kpi: 'Mean Multiple', 'Q1 2020': 1 });
  });

  it('should calculate target multiple', () => {
    const element = { adjustmentTypeId: 100, mean: 1, median: 2, adjustmentFactor: 10, selectedComps: 'mean' };
    const result = component.calculateTargetMultiple(element, 'Target Multiple Mean/Median');
    expect(result).toBe(1);
  });

  it('should get prefix', () => {
    const rowData = { 'Q1 2020-Pre': 'prefix' };
    const prefix = component.getPrefix(rowData, 'Q1 2020');
    expect(prefix).toBe('prefix');
  });

  it('should get adjustment type', () => {
    expect(component.getAdjustmentType(100)).toBe('At Par');
    expect(component.getAdjustmentType(101)).toBe('Discount');
    expect(component.getAdjustmentType(102)).toBe('Premium');
  });

  it('should get field name', () => {
    expect(component.getFieldName('Mean Multiple')).toBe('mean');
    expect(component.getFieldName('Unknown')). toBe('');
  });

  it('should get period type', () => {
    component.getPeriodType();
    expect(component.periodType.length).toBe(2);
    expect(component.selectedPeriodType).toBe(component.periodType[0]);
  });

  it('should handle period change', () => {
    spyOn(component, 'updateValidation');
    component.onPeriodChange({ periodTypeId: 1 });
    expect(component.updateValidation).toHaveBeenCalledWith({ periodTypeId: 1 });
  });

  it('should get tabs', () => {
    component.valuationSummary = { subPageFieldList: [{ isActive: true, name: 'Trading Comps', displayName: 'Trading Comps', isChart: false }] };
    component.getTabs();
    expect(component.tabList.length).toBe(1);
    expect(component.selectedTab).toBe(component.tabList[0]);
  });

  it('should get valuation category ID', () => {
    expect(component.getValuationCategoryId('Trading Comps')).toBe(component.valuationType.TradingComps);
    expect(component.getValuationCategoryId('Unknown')).toBe(0);
  });

  it('should change tab type', () => {
    const tab = { isActive: false };
    spyOn(component, 'getValuationSummary');
    spyOn(component, 'resetForm');
    component.changeTabType(tab);
    expect(tab.isActive).toBe(true);
    expect(component.selectedTab).toBe(tab);
    expect(component.getValuationSummary).toHaveBeenCalled();
    expect(component.resetForm).toHaveBeenCalled();
  });

  it('should show success message', () => {
    component.showSuccessMessage('Success');
    expect(mockToastrService.success).toHaveBeenCalledWith('Success', '', { positionClass: 'toast-top-right' });
  });

  it('should reset form', () => {
    component.resetForm();
    expect(component.filterForm.get('periodType')?.value).toBe(component.periodType[0]);
  });

  it('should update validation', () => {
    spyOn(component, 'clearValidators');
    spyOn(component, 'setValidators');
    spyOn(component, 'updateValueAndValidity');
    component.updateValidation({ periodTypeId: 1 });
    expect(component.clearValidators).toHaveBeenCalled();
    component.updateValidation({ periodTypeId: 2 });
    expect(component.setValidators).toHaveBeenCalled();
    expect(component.updateValueAndValidity).toHaveBeenCalled();
  });
});
