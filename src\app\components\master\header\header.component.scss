@import "../../../../variables";
.header {
    .link {
        .circle {
            position: absolute;
            background-color: $Negative-100;
            border-radius: 100px;
            width: 6px;
            height: 6px;
            top: 13px;
            right: 143px;
        }
    }
   // width: 178px;
}

.mark-dot {
    width: 10px;
    height: 10px;
    opacity: 1;
    border-radius: 60%;
    cursor: pointer;
    display: inline-block;
    float: right;
    margin-top: 5px;
}

.mark-unread-dot {
    background: $nep-primary 0% 0% no-repeat padding-box;
}

.mark-read-dot {
    background: $nep-read-color 0% 0% no-repeat padding-box;
}

.mark-read-all {
    cursor: pointer;
    &:hover {
        text-decoration: underline;
    }
}

.sidebar-users {
    letter-spacing: 0px;
}

.sidebar-padding {
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
    padding-top: 16px;
    padding-bottom: 12px;
}

.notification-h1 {
    padding-top: 2px;
    color: $nep-black;
}
.circle-container
{
    overflow: hidden;
    position: relative;
}
.arrow-image {
    position: absolute;
    top: 50%;
    left: 50%;
    padding-top: 2px;
    transform: translate(-50%, -50%);
}
.expand-doc {
    a {
        padding-top: 2px;
    }
}
.excel-plugin-container {
    display: inline-flex;
    padding: 4px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 50%;
    border: 1px solid $Neutral-Gray-10;
    background: $Neutral-Gray-00;
    width: 1.5rem;
    height: 1.5rem;
    margin-right:0.75rem;
}
.opened {
    border-radius: 4px;
    background: $Neutral-Gray-00;
    box-shadow: 0px 2px 12px 0px rgba(113, 113, 113, 0.29);
}
.excelPlugin-menu {
    width: 132px !important;
}
@keyframes spin {
    0% { transform: rotate(Odeg);}
    100% { transform: rotate(360deg);}
}

.img-spinner {
    animation: spin 0.5s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to { 
        transform: rotate(360deg);
    }
}
 

 @-webkit-keyframes rotate {
    from {
        -webkit-transform: rotate(0deg);
    }
    to { 
        -webkit-transform: rotate(360deg);
    }
}

.excel-plugin-load {
	width: 14px;
    height: 14px;
    margin: 10px auto 0;
    border: solid 2px #4061C7;
    border-radius: 50%;
	border-right-color: transparent;
	border-bottom-color: transparent;
	 -webkit-transition: all 0.5s ease-in;
    -webkit-animation-name:             rotate; 
    -webkit-animation-duration:         1.0s; 
    -webkit-animation-iteration-count:  infinite;
    -webkit-animation-timing-function: linear;
    	
    	 transition: all 0.5s ease-in;
    animation-name:             rotate; 
    animation-duration:         1.0s; 
    animation-iteration-count:  infinite;
    animation-timing-function: linear;
    margin-top: 4px; 
}