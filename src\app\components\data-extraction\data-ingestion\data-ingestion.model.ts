export interface FileConfig {
    maxSize: number;
    maxFiles: number;
    allowedTypes: string[];
  }
export interface FileValidationResult {
    isValid: boolean;
    errors: string[];
  }

  export interface SelectedFile {
    id: string;
    file: File;
    name: string;
    size: number;
    type: string;
    status: 'valid' | 'invalid';
    errors:string[];
    documentType: string | null;
    selected?: boolean;
  }
export interface DealPCFund {
  dealId: number;
  companyId: number;
  fundId: number;
  companyName: string;
  fundName: string;
  encryptedPortfolioCompanyID:string;
}
export interface IngestionFormData {
  company:DealPCFund;
  module: Modules;
  monthQuarter: string;
  period: string;
  sourceType: SourceType;
  year: number;
  extractionType: string;
  funds?:FundModel,
  fundKpiDetails?: [],
  companyIssuers?:CompanyKpiTreeItem [],
}
export interface DocumentTypeOption {
  documentName: string;
  id: number;
}

export interface FileDetails {
  id: string;
  name: string;
}
export interface PortfolioLogoModel {
  imagePath: string;
}
export interface SourceType {
  id: number;
  name: string;
}
export interface StatusResponse {
  isSuccess: boolean;
  message: string;
  id: string;
}
export interface CompanyKpiTreeItem {
  id: number;
  fieldId: number;
  text: string;
  items: KpiTreeGroup[];
}

export interface KpiTreeGroup {
  id: number;
  itemId: number;
  text: string;
  name: string;
  items: KpiTreeNode[];
}

export interface KpiTreeNode {
  id: number;
  mappingId: number;
  companyId: number;
  kpiId: number;
  text: string;
  name: string;
  isHeader: boolean;
  isBoldKpi: boolean;
  moduleId: number;
  parentId: number;
  kpiInfo: string;
}
export interface FundModel{
  fundId: number;
  fundName: string;
}
export interface Modules extends SourceType {
}
export interface StatusCount {
  inProgressCount: number;
  completedCount: number;
  failedCount: number;
}