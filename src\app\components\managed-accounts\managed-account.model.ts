export class ManagedAccount {
  id: number;
  managedAccountName: string;
  domicile: string;
  commencementDate: string;
  investmentPeriodEndDate: string;
  maturityDate: string;
  commitmentOutstanding: string;
  baseCurrency: string;
  investmentManager: string;
  administrator: string;
  custodian: string;
  legalCounsel: string;
  lei: string;
  
  constructor(init?: Partial<ManagedAccount>) {
    Object.assign(this, init);
  }
}
