import { Injectable, Inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AuditLogModel, AuditType } from '../models/audit-log.model';

export interface AuditLogQuery {
  rowIdentity: string;
  columnIdentity: string;
  entity: string;
  companyId: string;
}

export interface AuditLogEntry {
  currentValue: string;
  oldValue: string;
  source: string;
  sourceFile: string;
  supportingEvidence: string;
  createdBy: string;
  dateTime: string;
}



@Injectable({
  providedIn: 'root'
})
export class AuditLogService {
  private readonly baseUrl: string;

  constructor(
    private readonly http: HttpClient,
    @Inject('BASE_URL') baseUrl: string
  ) {
    this.baseUrl = baseUrl;
  }

  getAuditHistory(query: AuditLogQuery): Observable<AuditLogModel[]> {
    return this.http
      .post<AuditLogModel[]>(`${this.baseUrl}api/audit-log/history`, query)
      .pipe(
        catchError(this.errorHandler)
      );
  }

  downloadDocument(identityName: string): Observable<Blob> {
    return this.http
      .get(`${this.baseUrl}api/audit-log/document/download/${identityName}`, 
      { responseType: 'blob' })
      .pipe(
        catchError(this.errorHandler)
      );
  }

  private errorHandler(error: any) {
    return throwError(error);
  }
}
