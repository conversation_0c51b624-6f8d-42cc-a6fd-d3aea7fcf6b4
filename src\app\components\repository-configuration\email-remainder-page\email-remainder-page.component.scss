@import "../../../../_variables.scss";
@import "../../../../assets/dist/css/font.scss";

.email-reminder-container {
  padding-left: 1rem;
  padding-bottom: 1rem;
  padding-right: 1rem;

  .form-control {
    padding: 0.375rem 0.75rem;
    border: 1px solid $Neutral-Gray-10;
    border-radius: 0.25rem;
  }

  .header-selection-container {
    border: 1px solid $Neutral-Gray-10;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;

    .portfolio-company-section {
      padding-right: 1.5rem;
      border-right: 1px solid $Neutral-Gray-10;

      @media (max-width: 768px) {
        border-right: none;
        border-bottom: 1px solid $Neutral-Gray-10;
        padding-right: 15px;
        padding-bottom: 1rem;
        margin-bottom: 1rem;
      }
    }

    .document-type-section {
      padding-left: 1.5rem;
    }

    .selection-group {
      .chips-container {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin: -0.25rem;
        
        .selection-chip {
          padding: 0.35em 0.65em;
          border-radius: 1.25rem;
          margin: 0.25rem;
          background-color: $cell-background-color;
        }
      }
    }
  }
  .action-button-section {
    border-top: 1px solid $Neutral-Gray-10;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
  }

  .form-core-section {
    height: calc(100vh - 300px);
    display: flex;
    flex-wrap: wrap;
    border-top: 1px solid $Neutral-Gray-10;
    
  }
  
  .email-box-section {
    flex: 2;
    margin: 1.25rem 0.625rem 1.25rem 1.25rem;
    overflow-y: auto;
    max-height: calc(100vh - 300px);
    
    @media (max-width: 991px) {
      flex: 1 0 100%;
      margin: 1.25rem;
      max-height: none;
    }
  }
  
  .email-remainder-section {
    margin: 1.25rem 1.25rem 1.25rem 0.625rem;
    overflow-y: auto;
    max-height: calc(100vh - 300px);
    
    @media (max-width: 991px) {
      flex: 1 0 100%;
      margin: 0 1.25rem 1.25rem 1.25rem;
      max-height: none;
    }
  }
     .card-header {
    background-color: $uploading-bg-color !important;
    display: flex;
    justify-content: center;
  }
 .header-actions {
        display: flex;
        gap: 10px;      
    }
  .card-body {
    overflow-y: auto;
    max-height: calc(100vh - 400px);
    padding-left: 1.25rem;
    padding-right: 1.25rem;    
    @media (max-width: 991px) {
      max-height: none;
    }
  }
 
  .reminder-number-input {
    width: 90px;
  }

  .email-input-container {
    position: relative;
    margin-bottom: 0.5rem;

    .input-with-chips {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 0.5rem;
      border: 1px solid $Neutral-Gray-05;
      border-radius: 0.25rem;

      input.form-control {
        flex: 1 1 200px;
        border: none;
        background: transparent;
        min-width: 150px;
        padding: 0.25rem 0.5rem;
      }
    }
  }
}

.custom-radio-btn {
  border-color: $primary-color-78;
}

.chips-containertocc {
  border: 1px solid $Neutral-Gray-10;
  border-radius: 4px;
  padding: 4px 16px;
}

.selection-chiptocc {
  padding: 0.35em 0.65em;
  border-radius: 1.25rem;
  background-color: $Neutral-Gray-05 !important;
  margin: 4px;
}

.remove-icon {
  cursor: pointer;
  &:hover {
    cursor: pointer;
  }
}
.custom-headertext{
  color: $Neutral-Gray-100 !important;
}
.Txt-label{
  color: $Neutral-Gray-90 !important;
}
.reminder-input{
  height: 2rem;
}