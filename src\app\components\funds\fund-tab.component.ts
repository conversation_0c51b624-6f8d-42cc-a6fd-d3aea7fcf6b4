import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { PermissionActions, PortfolioCompanyDetailConstants } from 'src/app/common/constants';
import { PageConfigurationDocumentPageDetails } from 'src/app/common/enums';
import { PageConfigurationService } from 'src/app/services/page-configuration.service';
import { FeaturesEnum, UserSubFeaturesEnum } from 'src/app/services/permission.service';
import { CommonSubFeaturePermissionService } from 'src/app/services/subPermission.service';
import { PCDocumentListComponent } from '../portfolioCompany/pcdocument-list/pcdocument-list.component';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-fund-tab',
  templateUrl: './fund-tab.component.html',
  styleUrls: ['./fund-tab.component.scss']
})
export class FundTabComponent implements OnInit {
  // Add component logic here
    tabList: ITab[] = [];
    tabName: string = 'Fund Details';
    id: any;
    documentsFieldPageConfig: any;
    @ViewChild('documentList') documentList: PCDocumentListComponent;
    canViewDocuments: boolean = false;
    canEditDocuments: boolean = false;
    subFeature: typeof UserSubFeaturesEnum = UserSubFeaturesEnum;
    pageConfigurationDocumentPageDetails=PageConfigurationDocumentPageDetails;
  
    constructor(
        private _avRoute: ActivatedRoute,
        private subPermissionService: CommonSubFeaturePermissionService,
        private pageConfigurationService: PageConfigurationService,
        private toastrService: ToastrService
    ) {
      if (this._avRoute.snapshot.params["id"]) {
        this.id = this._avRoute.snapshot.params["id"];
      }
    }
  
    ngOnInit(): void {
      this.getDocumentsPermissions();
    }
  
    switchTab(tab: ITab) {
        if(tab) {
          this.tabName = tab.aliasname;
        }
    }
  
    onFolderSelected(folderPath: string) {
      if (this.documentList) {
        this.documentList.setSelectedFolder(folderPath);
      }
    }
    
    /**
     * Checks if user has a specific permission access
     * @param permission Array of permissions to check
     * @param permissionType Type of permission (CAN_VIEW, CAN_EDIT, etc.)
     * @returns boolean indicating whether user has specified permission
     */
    checkPermissionAccess(permission: any[], permissionType: string): boolean {
      return permission.map((x) => x[permissionType]).includes(true);
    }
    
    /**
     * Gets Documents tab permissions from the CommonSubFeaturePermissionService
     */
    getDocumentsPermissions() {
      this.subPermissionService
        .getCommonSubFeatureAccessPermissions(this.id, FeaturesEnum.Fund)
        .subscribe({
          next: (result) => {
            if (result.length > 0) {
              this.canViewDocuments = this.checkPermissionAccess(
                result?.filter(
                  (x) => x.subFeature == PortfolioCompanyDetailConstants.Documents),
                PermissionActions.CAN_VIEW
              );
              this.canEditDocuments = this.checkPermissionAccess(
                result?.filter(
                  (x) => x.subFeature == PortfolioCompanyDetailConstants.Documents), PermissionActions.CAN_EDIT
              );
              this.updateTabList();
            }
          },
        });
    }
  
    /**
     * Updates the tab list based on permissions
     */
    updateTabList() {
      
      this.tabList = [
        { name: 'Fund Details', active: true,aliasname: 'Fund Details' },
      ];
      if (this.canViewDocuments) {
        this.getPageConfigSetting();    
     }
  
    }
  
      getPageConfigSetting = () => {
          this.pageConfigurationService.getPageConfigSettingById(2).subscribe(
              (result: any) => {
                  if (result) {
                    this.tabList = [
                      { name: 'Fund Details', active: true,aliasname: 'Fund Details' },
                    ];
                    var documentsPageConfig = result.subPageList?.find((x: any) => x.name == this.pageConfigurationDocumentPageDetails.Documents);
                    if(documentsPageConfig!=undefined && this.canViewDocuments){
                      this.tabList.push({ name: 'Documents', active: false,aliasname: 'Documents' });
                      this.documentsFieldPageConfig = result.fieldValueList?.filter((x: any) => x.subPageID == documentsPageConfig.id);
                      
                      this.tabList[1].name = documentsPageConfig?.displayName;
                      this.tabList[1].hidden = documentsPageConfig?.isActive ? false : true;
                    }                    
                  }
              },
              (_error) => { 
                // Handle error if needed
                this.toastrService.error('Failed to load page configuration settings', 'Error');
              }
          );
      };
  
    }
