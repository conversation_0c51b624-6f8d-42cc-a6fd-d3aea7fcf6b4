export interface GrowthReportKpiModule {
    companyId: number;
    moduleId: number;
    kpiIds: string;
  }
  
  export interface GrowthReportKpiModel {
    mappingId: number;
    companyId: number;
    kpiId: number;
    kpi: string;
    isHeader: boolean;
    isBoldKpi: boolean;
    moduleId: number;
  }
  export interface GrowthReportSectionModel {
    moduleId: number;
    name: string;
    aliasName: string;
  }
  export interface GrowthReportCompanyModel {
    companyName: string;
    companyId: number;
    fundName: string;
    fundID: number;
  }
  export interface GrowthReportConfigModel {
    companyIds: string;
    fundIds: string;
    growthId: number;
    companies: any;
  }
  export interface GrowthConfigListModel {
    growthReportConfigModel: GrowthReportConfigModel;
    rowHeaders: GrowthRowColumnKpiModel[];
    columnHeaders: GrowthRowColumnKpiModel[];
  }
  export interface GrowthRowColumnKpiModel {
    companyId: number;
    headerId: number;
    moduleId: number;
    kpiIds: string;
  }
  export interface DownloadGrowthReportModel {
    fundLevel: DownloadGrowthReportFundLevelModel;
    consolidatedLevel: DownloadGrowthReportConsolidatedLevelModel;
  }
  export interface DownloadGrowthReportFundLevelModel {
    columnMetrics: DownloadGrowthReportFundLevelColumnMetricModel[];
    rowMetrics: DownloadGrowthReportRowMetricModel[];
  }
  export interface DownloadGrowthReportConsolidatedLevelModel {
    columnMetrics: DownloadGrowthReportFundLevelColumnMetricModel[];
    rowMetrics: DownloadGrowthReportRowMetricModel[];
  }
  export interface DownloadGrowthReportFundLevelColumnMetricModel {
    kpiName: string;
    mappingId: number;
    kpiId: number;
    displayName: string;
    frequency: any;
    period: string[];
    year: number[];
    companyId: number;
    moduleId: number;
    periodDisabled: boolean;
    periodList: any[];
  }
  export interface DownloadGrowthReportRowMetricModel {
    id: number;
    displayName: string;
    scenario: any;
    frequency: any;
    period: string[];
    year: number[];
    periodDisabled: boolean;
    periodList: any[];
  }