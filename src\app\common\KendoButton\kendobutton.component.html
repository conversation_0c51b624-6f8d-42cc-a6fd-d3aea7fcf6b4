<button
  kendoButton
  look="outline"
  id="{{ name }}-btn"
  iconClass="{{ iconStyle }} {{ iconClass }}"
  [ngClass]="getMergedClass()"
  [disabled]="disabled"
  (click)="onClick.emit($event)"
  (focus)="onFocus.emit($event)"
  (blur)="onBlur.emit($event)"
  *ngIf="iconClass !== ''"
>
  <ng-content></ng-content>
</button>
<button
  kendoButton
  look="outline"
  id="{{ name }}-btn"
  icon="{{ icon }}"
  [ngClass]="getMergedClass()"
  [disabled]="disabled"
  (click)="onClick.emit($event)"
  (focus)="onFocus.emit($event)"
  (blur)="onBlur.emit($event)"
  *ngIf="icon !== ''"
>
  <ng-content></ng-content>
</button>
<button
  kendoButton
  look="outline"
  id="{{ name }}-btn"
  imageUrl="{{ imageUrl }}"
  [ngClass]="getMergedClass()"
  [disabled]="disabled"
  (click)="onClick.emit($event)"
  (focus)="onFocus.emit($event)"
  (blur)="onBlur.emit($event)"
  *ngIf="imageUrl !== ''"
>
  <ng-content></ng-content>
</button>
<button
  kendoButton
  look="outline"
  id="{{ name }}-btn"
  imageUrl="{{ imageUrl }}"
  [ngClass]="getMergedClass()"
  [disabled]="disabled"
  (click)="onClick.emit($event)"
  (focus)="onFocus.emit($event)"
  (blur)="onBlur.emit($event)"
  *ngIf="imageUrl === '' && iconClass === '' && icon === ''"
>
  <ng-content></ng-content>
</button>