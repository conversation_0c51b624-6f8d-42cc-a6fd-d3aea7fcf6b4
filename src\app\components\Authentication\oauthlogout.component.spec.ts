// import { ComponentFixture, TestBed } from "@angular/core/testing";
// import { NO_ERRORS_SCHEMA } from "@angular/core";
// import { OidcAuthService } from "src/app/services/oidc-auth.service";
// import { ActivatedRoute } from "@angular/router";
// import { HttpClientTestingModule } from "@angular/common/http/testing";
// import { of } from "rxjs";
// import { OauthLogoutComponent } from "./oauthlogout.component";
// import { AccountService } from "src/app/services/account.service";

// describe("OauthLogoutComponent", () => {
//   let component: OauthLogoutComponent;
//   let fixture: ComponentFixture<OauthLogoutComponent>;
//   let oidcAuthService: OidcAuthService;
//   let accountService: jasmine.SpyObj<AccountService>;
//   beforeEach(() => {
//     accountService = jasmine.createSpyObj("AccountService", [
//       "addupdateSessionId",
//     ]);
//     const oidcAuthServiceStub = {
//       logout: jasmine.createSpy("logout"),
//       signoutRedirectCallback: jasmine.createSpy("signoutRedirectCallback"),
//     };
//     const activatedRouteStub = {
//       snapshot: {
//         queryParams: {
//           action: "logout",
//         },
//       },
//     };
//     TestBed.configureTestingModule({
//       schemas: [NO_ERRORS_SCHEMA],
//       declarations: [OauthLogoutComponent],
//       providers: [
//         {
//           provide: AccountService,
//           useValue: accountService,
//         },
//         { provide: OidcAuthService, useValue: oidcAuthServiceStub },
//         { provide: ActivatedRoute, useValue: activatedRouteStub },
//         { provide: "BASE_URL", useValue: "http://example.com" },
//       ],
//       imports: [HttpClientTestingModule],
//     });
//     fixture = TestBed.createComponent(OauthLogoutComponent);
//     component = fixture.componentInstance;
//     oidcAuthService = TestBed.inject(OidcAuthService);
//   });
//   it("can load instance", () => {
//     expect(component).toBeTruthy();
//   });
// });
