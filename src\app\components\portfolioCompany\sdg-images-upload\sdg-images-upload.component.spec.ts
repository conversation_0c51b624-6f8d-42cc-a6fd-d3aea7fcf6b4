import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SDGImagesUploadComponent } from './sdg-images-upload.component';
import { PortfolioCompanyService } from "../../../services/portfolioCompany.service";
import { CellEditConstants, GlobalConstants } from "../../../common/constants";
import { ToastrService } from "ngx-toastr";
import { of, throwError } from 'rxjs';
import { ElementRef } from '@angular/core';

xdescribe('SDGImagesUploadComponent', () => {
  let component: SDGImagesUploadComponent;
  let fixture: ComponentFixture<SDGImagesUploadComponent>;
  let portfolioCompanyService: jasmine.SpyObj<PortfolioCompanyService>;
  let toastrService: jasmine.SpyObj<ToastrService>;

  beforeEach(async () => {
    const portfolioCompanyServiceSpy = jasmine.createSpyObj('PortfolioCompanyService', ['getSDGImages', 'uploadSDGImages']);
    const toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['error', 'success']);

    await TestBed.configureTestingModule({
      declarations: [ SDGImagesUploadComponent ],
      providers: [
        { provide: PortfolioCompanyService, useValue: portfolioCompanyServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(SDGImagesUploadComponent);
    component = fixture.componentInstance;
    portfolioCompanyService = TestBed.inject(PortfolioCompanyService) as jasmine.SpyObj<PortfolioCompanyService>;
    toastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;

    // Mock the fileDropEl
    component.fileDropEl = new ElementRef({ value: '' });
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should fetch SDG images on init', () => {
    const mockData = { sdgImages: [{ name: 'image1.png', type: 'image/png', value: 'url1', id: 1 }] };
    portfolioCompanyService.getSDGImages.and.returnValue(of(mockData));

    component.ngOnInit();

    expect(portfolioCompanyService.getSDGImages).toHaveBeenCalledWith(component.pcId);
    expect(component.sdgImages.length).toBe(1);
    expect(component.isLoading).toBeFalse();
  });

  it('should handle error when fetching SDG images', () => {
    portfolioCompanyService.getSDGImages.and.returnValue(throwError(() => new Error('error')));

    component.ngOnInit();

    expect(portfolioCompanyService.getSDGImages).toHaveBeenCalledWith(component.pcId);
    expect(component.isLoading).toBeFalse();
  });

  it('should create image object', () => {
    const image = { name: 'image1.png', type: 'image/png', value: 'url1', id: 1 };
    const imageObject = component.createImageObject(image);

    expect(imageObject.file.name).toBe(image.name);
    expect(imageObject.file.type).toBe(image.type);
    expect(imageObject.url).toBe(image.value);
    expect(imageObject.isExisting).toBeTrue();
    expect(imageObject.id).toBe(image.id);
  });

  it('should handle file change event', () => {
    const file = new File([''], 'image.png', { type: 'image/png' });
    const fileList = {
      0: file,
      length: 1,
      item: (index: number) => file
    } as FileList;

    spyOn(component, 'readFile');
    component.onBrowseImageChange(fileList);

    expect(component.readFile).toHaveBeenCalledWith(file);
    expect(component.uploadAndSavaEnabled).toBeFalse();
  });

  it('should handle file errors', () => {
    spyOn(component, 'showErrorMessage');

    component.handleFileErrors(['.exe'], []);
    expect(component.showErrorMessage).toHaveBeenCalledWith('.exe is incorrect file format. Please refer to allowed file formats list.');

    component.handleFileErrors([], [new File([''], 'large.png')]);
    expect(component.showErrorMessage).toHaveBeenCalledWith('1 image could not be uploaded. Please upload images up to 2 MB only.');

    component.handleFileErrors(['.exe'], [new File([''], 'large.png')]);
    expect(component.showErrorMessage).toHaveBeenCalledWith('Incorrect file format and size. Please upload images up to 2 MB only.');
  });

  it('should extract file extension', () => {
    const fileName = 'image.png';
    const extension = component.getFileExtension(fileName);

    expect(extension).toBe('.png');
  });

  it('should show error message', () => {
    const message = 'Error message';
    component.showErrorMessage(message);

    expect(toastrService.error).toHaveBeenCalledWith(message, '', { positionClass: CellEditConstants.ToasterMessagePosition });
  });

  it('should read file', () => {
    const file = new File([''], 'image.png', { type: 'image/png' });
    const reader = new FileReader();
    spyOn(reader, 'readAsDataURL');
    spyOn(window as any, 'FileReader').and.returnValue(reader);

    component.readFile(file);

    expect(reader.readAsDataURL).toHaveBeenCalledWith(file);
  });

  it('should remove image', () => {
    component.sdgImages = [{ file: new File([], 'image.png'), url: 'url', isExisting: true, id: 1 }];
    component.removeImage(0);

    expect(component.imagesTobeDeleted).toContain(1);
    expect(component.sdgImages.length).toBe(0);
    expect(component.uploadAndSavaEnabled).toBeFalse();
  });

  it('should upload and save images', () => {
    spyOn(component, 'createFormData').and.returnValue(new FormData());
    portfolioCompanyService.uploadSDGImages.and.returnValue(of({}));

    component.uploadAndSave();

    expect(component.isLoading).toBeTrue();
    expect(portfolioCompanyService.uploadSDGImages).toHaveBeenCalled();
    expect(toastrService.success).toHaveBeenCalledWith(GlobalConstants.SDGImageUploadSuccess, '', { positionClass: CellEditConstants.ToasterMessagePosition });
  });

  it('should handle error when uploading images', () => {
    spyOn(component, 'createFormData').and.returnValue(new FormData());
    portfolioCompanyService.uploadSDGImages.and.returnValue(throwError(() => new Error('error')));

    component.uploadAndSave();

    expect(component.isLoading).toBeFalse();
    expect(portfolioCompanyService.uploadSDGImages).toHaveBeenCalled();
    expect(toastrService.error).toHaveBeenCalledWith(GlobalConstants.SDGImageUploadFailure, '', { positionClass: CellEditConstants.ToasterMessagePosition });
    expect(component.isLoading).toBeFalse();
  });

  it('should create FormData with portfolio company ID', () => {
    component.pcId = 123;
    component.sdgImages = [];
    component.imagesTobeDeleted = [];

    const formData = component.createFormData();

    expect(formData.get('PortfolioCompanyId')).toBe('123');
  });

  it('should append new images to FormData', () => {
    component.pcId = 123;
    const testFile = new File([''], 'test.png');
    component.sdgImages = [
      { file: testFile, isExisting: false }
    ];
    component.imagesTobeDeleted = [];

    const formData = component.createFormData();
    const files = formData.getAll('Files');

    expect(files.length).toEqual(1);
  });

  it('should append images to be deleted to FormData', () => {
    component.pcId = 123;
    component.sdgImages = [];
    component.imagesTobeDeleted = [1, 2];

    const formData = component.createFormData();

    expect(formData.get('ImagesTobeDeleted[0]')).toBe('1');
    expect(formData.get('ImagesTobeDeleted[1]')).toBe('2');
  });

  it('should create FormData with both new images and images to be deleted', () => {
    component.pcId = 123;
    const testFile = new File([''], 'test.png');
    component.sdgImages = [
      { file: testFile, isExisting: false }
    ];
    component.imagesTobeDeleted = [1];

    const formData = component.createFormData();
    const files = formData.getAll('Files');

    expect(formData.get('PortfolioCompanyId')).toBe('123');
    expect(files.length).toBe(1);
  });

  it('should not include existing images in FormData', () => {
    component.pcId = 123;
    const testFile1 = new File([''], 'test1.png');
    const testFile2 = new File([''], 'test2.png');
    component.sdgImages = [
      { file: testFile1, isExisting: true },
      { file: testFile2, isExisting: false }
    ];
    component.imagesTobeDeleted = [];

    const formData = component.createFormData();
    const files = formData.getAll('Files');

    expect(files.length).toBe(1);
  });

  it('should create FormData with empty arrays', () => {
    component.pcId = 123;
    component.sdgImages = [];
    component.imagesTobeDeleted = [];

    const formData = component.createFormData();
    expect(formData.getAll('Files')).toEqual([]);
  });;

  it('should show success message', () => {
    const message = 'Success message';
    component.showSuccessMessage(message);

    expect(toastrService.success).toHaveBeenCalledWith(message, '', { positionClass: CellEditConstants.ToasterMessagePosition });
  });
});
