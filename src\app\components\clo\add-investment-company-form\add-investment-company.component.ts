import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup,Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { InvestmentCompany } from '../investment-company-model';
import { DatePipe } from '@angular/common';
import { InvestCompanyService } from '../investmentcompany/investmentcompany.service';
import { ToastrService } from 'ngx-toastr';
import { CloService } from 'src/app/services/clo.service';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';

@Component({
  selector: 'app-add-investment-company',
  templateUrl: './add-investment-company.component.html',
  styleUrls: ['./add-investment-company.component.scss'],
  providers: [DatePipe]
})
export class AddInvestmentCompanyComponent implements OnInit {
  companyForm: FormGroup;
  step: number = 1;
  savedData: InvestmentCompany; 
  isLoading:boolean = false;

  constructor(private readonly fb: FormBuilder, private readonly router: Router,private datePipe: DatePipe,private readonly investCompanyService: InvestCompanyService, private readonly toastrService: ToastrService, private sharedService: CloService, private route: ActivatedRoute,
    private breadcrumbService: BreadcrumbService
  ) {
    this.route.paramMap?.subscribe(params => {
      this.id = +params.get('id');       
    });
  }
  resetText: string = "Reset";
  cancelText: string = "Cancel";
  saveText: string = "Save";
  charCount: number = 0;
  isCompanyFactsSaved: boolean = false;
  isEdited: boolean = false;
  isFromReviewPage: boolean = false;
  isSummarySaved: boolean = false;
  isSummaryEdited: boolean = false;
  submitted: boolean = false;
  showPopup: boolean = false;
  resetSummaryOnly = false;
  errorMessage: string = "Please fill all the missing fields.";
  isUpdate: boolean = false;
  id: number = 0;

   ngOnInit(): void {
    const initialData = new InvestmentCompany();
    this.updateBreadcrumbs();
    this.companyForm = this.fb.group({
      companyName: ['', Validators.required],
      domicile: ['', Validators.required],
      incorporationDate: ['', Validators.required],
      firstClose: ['', Validators.required],
      investmentPeriodEndDate:['', Validators.required],
      maturityDate: ['', Validators.required],
      custodian: ['', Validators.required],
      legalCounsel:['', Validators.required],
      finalClose: ['', Validators.required],
      commitments: ['', Validators.required],
      administrator: ['', Validators.required],
      portfolioAdvisor: ['', Validators.required],
      baseCurrency: ['', Validators.required],
      listingAgent: ['', Validators.required],
      SummaryCount: ['', Validators.required],
    });

    this.companyForm.valueChanges.subscribe(() => {
      this.isEdited = true;
    });

    this.investCompanyService.goToStep$.subscribe(step => {      
      this.goToStep(step);
      this.isEdited = true;
      this.isFromReviewPage = true;
    });

    this.sharedService.currentStep.subscribe(step => {              
      if(this.id > 0 && step != null && step != 0) {
        this.step = step;    
        this.isUpdate = true;
      }
    });

    if(this.id != 0) {
      this.getInvestmentCompanyById(this.id);
    }
  }
  updateBreadcrumbs() {
    let newBreadcrumbs: any[] = [];
    let message= this.id === 0 ? 'Add Investment Company' : 'Update Investment Company'
   
        newBreadcrumbs.push( { label: 'Investment Company', url: '/investment-company' });
      newBreadcrumbs.push( { label: message });
      this.breadcrumbService.setBreadcrumbs(newBreadcrumbs);
  }

  getInvestmentCompanyById(id: number): void {
    this.isLoading = true;
    this.investCompanyService.getInvestCompanyById(id).subscribe({
      next: (data) => {    
        this.isLoading = false;                          
        if(data != null && data?.value?.code !== 'NoContent'){
            this.companyForm.patchValue({
            companyName: data.companyName,
            domicile: data.domicile,
            incorporationDate: this.parseDate(data.incorporationDate),
            firstClose: this.parseDate(data.firstClose),
            investmentPeriodEndDate: this.parseDate(data.investmentPeriodEndDate),
            maturityDate: this.parseDate(data.maturityDate),
            custodian: data.custodian,
            legalCounsel: data.legalCounsel,
            finalClose: this.parseDate(data.finalClose),
            commitments: data.commitments,
            administrator: data.administrator,
            portfolioAdvisor: data.portfolioAdvisor,
            baseCurrency: data.baseCurrency,
            listingAgent: data.listingAgent,
            SummaryCount: data.investmentSummary,
            });     
        }  
        else {
          this.isLoading = false
        }     
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error fetching investment company data', error);
      }
    });
  }

  goToStep(step: number): void {
    if(this.isSummarySaved){
      this.isEdited = true;
    }

    this.step = step;
  }

  onCancel(): void {
    this.router.navigate(['/investment-company']); 
  }

  onCloseDialog(): void {
    this.showPopup = false;
    this.router.navigate(['/investment-company']); 
  }
  
  updateCharCount(): void {
    const summaryCountValue = this.companyForm.get('SummaryCount')?.value || '';
    this.charCount = summaryCountValue.length;
    this.isSummaryEdited = true; 
  }
  setCurrentDate(controlName: string): void {
    const control = this.companyForm.get(controlName);
    if (control && !control.value) {
      control.setValue(new Date());
    }
  }

  onSubmit(): void {
    const controls = this.companyForm.controls;
    let allFieldsFilled = true;
    this.submitted = true;
    for (const name in controls) {
      if (name !== 'SummaryCount') {
        const control = controls[name];
        const value = control.value;
        const trimmedValue = (typeof value === 'string') ? value.trim() : value;
        control.setValue(trimmedValue);

        if (!trimmedValue) {
          allFieldsFilled = false;
          break;
        }
      }
    }
    if (!allFieldsFilled) {
      this.companyForm.markAllAsTouched();
      this.toastrService.error(this.errorMessage, "", { positionClass: "toast-center-center" });
      return;
    }

    this.savedData = this.mapFormToModel();     

    // this is to update the investment company record
    if(this.isUpdate) {  
      this.saveInvestmentCompany();
      return;
    }
    
    if (this.step === 1) {
      this.isCompanyFactsSaved = true;
      this.goToStep(this.isFromReviewPage ? 3 : 2);
      this.isFromReviewPage = false;
    } else if (this.step === 2) {
        this.isSummarySaved = true;
        this.goToStep(3);
    }

    this.isEdited = false;
  }
  
  saveInvestmentCompany(): void {
    this.isLoading = true;
    this.investCompanyService.saveInvestCompany(this.savedData).subscribe(
      (data) => {
        this.isLoading = false;
        if(data.status){           
          this.toastrService.success(data.message, "", { positionClass: "toast-center-center" });  
          this.router.navigate(['/investment-company']);                   
        }                
      },
      (error) => {
        this.isLoading = false;
        console.error('Error in saving investment company', error);
      }
    );
  }

  private mapFormToModel(): InvestmentCompany {
    const formValues = this.companyForm.value;
    const investmentCompanyModel = new InvestmentCompany();
    investmentCompanyModel.id = this.id;
    investmentCompanyModel.companyName = formValues.companyName;
    investmentCompanyModel.domicile = formValues.domicile;
    investmentCompanyModel.incorporationDate = this.formatDate(formValues.incorporationDate);
    investmentCompanyModel.firstClose = this.formatDate(formValues.firstClose);
    investmentCompanyModel.investmentPeriodEndDate = this.formatDate(formValues.investmentPeriodEndDate);
    investmentCompanyModel.maturityDate = this.formatDate(formValues.maturityDate);
    investmentCompanyModel.custodian = formValues.custodian;
    investmentCompanyModel.legalCounsel = formValues.legalCounsel;
    investmentCompanyModel.finalClose = this.formatDate(formValues.finalClose);
    investmentCompanyModel.commitments = formValues.commitments;
    investmentCompanyModel.administrator = formValues.administrator;
    investmentCompanyModel.portfolioAdvisor = formValues.portfolioAdvisor;
    investmentCompanyModel.baseCurrency = formValues.baseCurrency;
    investmentCompanyModel.listingAgent = formValues.listingAgent;
    investmentCompanyModel.investmentSummary = formValues.SummaryCount;
    return investmentCompanyModel;
  }

  private formatDate(date: any): string {
    return this.datePipe.transform(date, 'd MMMM yyyy');
  }

  private parseDate(dateString: string): Date {
    return new Date(Date.parse(dateString));
  }

  onResetinvestmentsummary(): void {
    this.companyForm.patchValue({
      SummaryCount: ''
    });
    this.isEdited = false;
    this.submitted = false;
  }
  onResetCompanyFacts(): void {
    Object.keys(this.companyForm.controls).forEach(key => {
      if (key !== 'SummaryCount') {
        this.companyForm.get(key)?.reset();
      }
    });
    this.isEdited = false;
    this.submitted = false;
  }
  onReset(): void {
    if (this.step === 1) {
      this.onResetCompanyFacts();
    } else {
      this.onResetinvestmentsummary();
    }
  }
}