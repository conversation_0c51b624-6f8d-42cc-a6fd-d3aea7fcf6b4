import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { RouterModule } from '@angular/router';
import { PortfolioDetailsComponent } from './portfolio-details.component';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { PortfolioCompanyDetailComponent } from './portfolioCompany-detail.component';
import { SharedPipeModule } from 'src/app/custom-modules/shared-pipe.module';
import { AngularResizeEventModule } from 'angular-resize-event';
import { QuillModule } from 'ngx-quill';
import { AngularEditorModule } from '@kolkov/angular-editor';
import { MasterKpiService } from 'src/app/services/master-kpi.service';
import { CurrencyService } from 'src/app/services/currency.service';
import { CashflowBetaService } from 'src/app/services/cashflow-beta.service';
import { BalanceSheetService } from 'src/app/services/balance-sheet.service';
import { ProfitLossService } from 'src/app/services/profit-loss.service';
import { FootNoteService } from 'src/app/services/foot-note.service';
import { CapTableService } from 'src/app/services/cap-table.service';
import { HttpServiceInterceptor } from 'src/app/interceptors/http-service-interceptor';
import { PortfolioCompanyImpactKPIComponent } from './portfolioCompany-ImpactKPI.component';
import { PortfolioCompanyKPIComponent } from './portfolioCompany-CompanyKPI.component';
import { ImpactuploadComponent } from '../impactupload/impactupload.component';
import { InvestmentKpiGraphComponent } from './investment-kpi-graph/investment-kpi-graph.component';
import { CompanyKpiGraphComponent } from './company-kpi-graph/company-kpi-graph.component';
import { CheckUserPermissionDirective } from 'src/app/directives/CheckUserPermission.directive';
import { HtmltopdfComponent } from '../htmltopdf/htmltopdf.component';
import { FinancialsBetaComponent } from './financials-beta/financials-beta.component';
import { SortDataDirective } from 'src/app/directives/kendo-sort.directive';
import { FootNoteComponent } from '../foot-note/foot-note.component';
import { ProfitLossBetaComponent } from './financials-beta/profit-loss-beta/profit-loss-beta.component';
import { BalanceSheetBetaComponent } from './financials-beta/balance-sheet-beta/balance-sheet-beta.component';
import { CashflowBetaComponent } from './financials-beta/cashflow-beta/cashflow-beta.component';
import { MasterKpiBetaComponent } from './master-kpi-beta/master-kpi-beta.component';
import { SDGImagesUploadComponent } from './sdg-images-upload/sdg-images-upload.component';
import { OperationalKpiBetaComponent } from './operational-kpi-beta/operational-kpi-beta.component';
import { MasterKpiGraphBetaComponent } from './master-kpi-graph-beta/master-kpi-graph-beta.component';
import { InvestmentKpiBetaComponent } from './investment-kpi-beta/investment-kpi-beta.component';
import { CapTableComponent } from './cap-table/cap-table.component';
import { OtherKpiComponent } from './master-kpi-beta/other-kpi.component';
import { ValuationSummaryComponent } from './valuation-summary/valuation-summary.component';
import { IconsModule, SVGIconModule } from '@progress/kendo-angular-icons';
import { MatMenuModule } from '@angular/material/menu';
import { SwitchModule } from '@progress/kendo-angular-inputs';
import { GridModule } from '@progress/kendo-angular-grid';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    HttpClientModule,
    RouterModule.forChild([
      { path: '', component: PortfolioDetailsComponent }
    ]),
    KendoModule,
    PrimeNgModule,
    MaterialModule,
    SVGIconModule,
    IconsModule,
    SharedComponentModule,
    SharedDirectiveModule,
    AngularEditorModule,
    SharedPipeModule,
    AngularResizeEventModule,
    QuillModule.forRoot(),
    MatMenuModule,
    SwitchModule,
    GridModule,
    DropDownsModule
  ],
  providers: [
    DatePipe,
    MasterKpiService,
    CurrencyService,
    CashflowBetaService,
    BalanceSheetService,
    ProfitLossService,
    FootNoteService,
    CapTableService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpServiceInterceptor,
      multi: true,
    }],
  declarations: [
    PortfolioDetailsComponent,
    PortfolioCompanyDetailComponent,
    PortfolioCompanyImpactKPIComponent,
    PortfolioCompanyKPIComponent,
    ImpactuploadComponent,
    InvestmentKpiGraphComponent,
    CompanyKpiGraphComponent,
    CheckUserPermissionDirective,
    SortDataDirective,
    HtmltopdfComponent,
    FootNoteComponent,
    FinancialsBetaComponent,
    ProfitLossBetaComponent,
    BalanceSheetBetaComponent,
    CashflowBetaComponent,
    MasterKpiBetaComponent,
    SDGImagesUploadComponent,
    OperationalKpiBetaComponent,
    MasterKpiGraphBetaComponent,
    InvestmentKpiBetaComponent,
    CapTableComponent,
    OtherKpiComponent,
    ValuationSummaryComponent,
  ],
  exports: [
    PortfolioCompanyDetailComponent,
  ],
  schemas: [
    CUSTOM_ELEMENTS_SCHEMA
  ]
})

export class PortfolioDetailsModule { }
