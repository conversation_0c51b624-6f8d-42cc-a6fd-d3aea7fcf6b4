import { ComponentFixture, TestBed, fakeAsync, tick } from "@angular/core/testing";
import { NO_ERRORS_SCHEMA, ChangeDetectorRef } from "@angular/core";
import { PortfolioCompanyListComponent } from "./portfolioCompany-list.component";
import { PortfolioCompanyService } from "src/app/services/portfolioCompany.service";
import { of } from "rxjs";

describe("PortfolioCompanyNewListComponent", () => {
  let component: PortfolioCompanyListComponent;
  let fixture: ComponentFixture<PortfolioCompanyListComponent>;
  let portfolioCompanyServiceMock: jasmine.SpyObj<PortfolioCompanyService>;

  beforeEach(() => {
    portfolioCompanyServiceMock = jasmine.createSpyObj('PortfolioCompanyService', ['getWorkFlowPageConfiguration']);
    
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [PortfolioCompanyListComponent],
      providers: [
        { provide: ChangeDetectorRef, useValue: {} },
        { provide: PortfolioCompanyService, useValue: portfolioCompanyServiceMock }
      ]
    });

    fixture = TestBed.createComponent(PortfolioCompanyListComponent);
    component = fixture.componentInstance;
  });

  it("should create the component", () => {
    expect(component).toBeTruthy();
  });

  it("should initialize with empty tabList", () => {
    expect(component.tabList).toEqual([]);
  });

  it("should fetch workflow configuration and call getTabList on ngOnInit", fakeAsync(() => {
    // Arrange
    portfolioCompanyServiceMock.getWorkFlowPageConfiguration.and.returnValue(of(true));
    spyOn(component, "getTabList").and.callThrough();
    
    // Act
    component.ngOnInit();
    tick();
    
    // Assert
    expect(portfolioCompanyServiceMock.getWorkFlowPageConfiguration).toHaveBeenCalled();
    expect(component.isWorkflowEnable).toBe(true);
    expect(component.getTabList).toHaveBeenCalled();
  }));

  it("should set isWorkflowEnable to false when service returns false on ngOnInit", fakeAsync(() => {
    // Arrange
    portfolioCompanyServiceMock.getWorkFlowPageConfiguration.and.returnValue(of(false));
    
    // Act
    component.ngOnInit();
    tick();
    
    // Assert
    expect(component.isWorkflowEnable).toBe(false);
  }));

  it("should always add 'Published' tab in getTabList", () => {
    // Act
    component.getTabList();
    
    // Assert
    expect(component.tabList.length).toBeGreaterThanOrEqual(1);
    expect(component.tabList[0]).toEqual({
      active: true,
      name: "Published"
    });
  });

  it("should add 'Active Drafts' tab when isWorkflowEnable is true in getTabList", () => {
    // Arrange
    component.isWorkflowEnable = true;
    
    // Act
    component.getTabList();
    
    // Assert
    expect(component.tabList.length).toBe(2);
    expect(component.tabList[1]).toEqual({
      active: false,
      name: "Active Drafts"
    });
  });

  it("should not add 'Active Drafts' tab when isWorkflowEnable is false in getTabList", () => {
    // Arrange
    component.isWorkflowEnable = false;
    
    // Act
    component.getTabList();
    
    // Assert
    expect(component.tabList.length).toBe(1);
    expect(component.tabList[0].name).toBe("Published");
  });

  it("should set tabName to the name of the first tab in getTabList", () => {
    // Act
    component.getTabList();
    
    // Assert
    expect(component.tabName).toBe("Published");
  });

  it("should activate the selected tab and deactivate others in selectTab", () => {
    // Arrange
    component.tabList = [
      { active: true, name: "Published" },
      { active: false, name: "Active Drafts" }
    ];
    
    // Act
    component.selectTab(component.tabList[1]);
    
    // Assert
    expect(component.tabList[0].active).toBeFalse();
    expect(component.tabList[1].active).toBeTrue();
  });

  it("should update tabName to the selected tab's name in selectTab", () => {
    // Arrange
    component.tabList = [
      { active: true, name: "Published" },
      { active: false, name: "Active Drafts" }
    ];
    
    // Act
    component.selectTab(component.tabList[1]);
    
    // Assert
    expect(component.tabName).toBe("Active Drafts");
  });

  it("should select second tab when isDraftOpen is true in changesModelevent", () => {
    // Arrange
    component.tabList = [
      { active: true, name: "Published" },
      { active: false, name: "Active Drafts" }
    ];
    spyOn(component, "selectTab").and.callThrough();
    
    // Act
    component.changesModelevent(true);
    
    // Assert
    expect(component.selectTab).toHaveBeenCalledWith(component.tabList[1]);
    expect(component.tabName).toBe("Active Drafts");
  });

  it("should not change tabs when isDraftOpen is false in changesModelevent", () => {
    // Arrange
    component.tabList = [
      { active: true, name: "Published" },
      { active: false, name: "Active Drafts" }
    ];
    spyOn(component, "selectTab").and.callThrough();
    
    // Act
    component.changesModelevent(false);
    
    // Assert
    expect(component.selectTab).not.toHaveBeenCalled();
    expect(component.tabList[0].active).toBeTrue();
    expect(component.tabName).toBe(undefined);
  });
});
