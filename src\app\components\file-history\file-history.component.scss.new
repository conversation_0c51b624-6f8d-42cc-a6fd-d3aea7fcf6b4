// filepath: d:\Acuity GIT\beat-foliosure-ui\src\app\components\file-history\file-history.component.scss
.file-history-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 350px;
  background-color: #fff;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #e0e0e0;
}

.file-history-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .file-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;
    background-color: white;
    position: sticky;
    top: 0;
    z-index: 10;
    
    h5 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      
      .view-all-btn {
        font-size: 12px;
        color: #444;
        padding: 0 8px;
        height: 30px;
        line-height: 30px;
        text-transform: none;

        &:hover {
          color: #1976d2;
        }
      }
      
      .close-btn {
        color: #666;
        width: 30px;
        height: 30px;
        line-height: 30px;
      }
    }
  }
  
  .file-history-content {
    flex: 1;
    overflow-y: auto;
    
    // Empty state
    .no-files {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
      color: #999;
      
      .empty-state {
        text-align: center;
        
        mat-icon {
          font-size: 48px;
          height: 48px;
          width: 48px;
          opacity: 0.5;
          margin-bottom: 10px;
        }
        
        p {
          font-size: 16px;
          margin: 0;
        }
      }
    }
    
    // Material Expansion Panel Overrides for matching the screenshot
    ::ng-deep {
      .mat-expansion-panel {
        border-radius: 0 !important;
        box-shadow: none !important;
        margin: 0 !important;
        
        &:not(:last-child) {
          border-bottom: 1px solid #f0f0f0;
        }
        
        .mat-expansion-panel-header {
          padding: 0 16px;
          height: 48px !important;
          background-color: #3f51b5 !important; // Blue header matching the screenshot
          
          &:hover {
            background-color: #3949ab !important;
          }
          
          &.mat-expanded {
            height: 48px !important;
          }
        }
        
        .mat-expansion-panel-header-title {
          color: white !important;
          font-weight: 500 !important;
          font-size: 14px;
          margin: 0;
        }
        
        .mat-expansion-panel-header-description {
          color: rgba(255, 255, 255, 0.8) !important;
          font-size: 12px;
          justify-content: flex-end !important;
          margin: 0;
        }
        
        .mat-expansion-panel-body {
          padding: 0 !important;
        }
      }
    }
    
    // File item styling
    .file-item {
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .file-row {
        display: flex;
        padding: 14px 16px;
        align-items: flex-start;
        
        .file-icon {
          margin-right: 14px;
          display: flex;
          align-items: center;
          min-width: 20px;
          
          mat-icon {
            font-size: 20px;
            height: 20px;
            width: 20px;
          }
          
          &.success {
            color: #4caf50;
          }
          
          &.in-progress {
            color: #ff9800;
          }
          
          &.failed {
            color: #f44336;
          }
        }
        
        .file-details {
          flex: 1;
          min-width: 0;
          
          .file-name {
            font-weight: 500;
            color: #333;
            font-size: 14px;
            margin-bottom: 2px;
            display: flex;
            align-items: center;
            
            .file-type-icon {
              margin-right: 6px;
              font-size: 16px;
              height: 16px;
              width: 16px;
              color: #757575;
            }
            
            .file-module {
              margin-left: 6px;
              font-size: 11px;
              color: #666;
              font-weight: normal;
            }
          }
          
          .file-meta {
            font-size: 12px;
            color: #757575;
            margin-bottom: 5px;
          }
          
          .progress-container {
            margin: 8px 0;
            position: relative;
            
            mat-progress-bar {
              height: 4px;
            }
            
            .progress-text {
              position: absolute;
              right: 0;
              top: -18px;
              font-size: 12px;
              color: #666;
            }
          }
          
          .file-message {
            font-size: 12px;
            color: #666;
          }
        }
        
        .file-actions {
          button {
            color: #666;
          }
        }
      }
    }
  }
}
