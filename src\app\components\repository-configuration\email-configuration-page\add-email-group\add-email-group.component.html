<div class="add-email-group-container">
    <div class="create-new-group-card">
        <div class="create-new-group-header pr-3 pl-3 ">
            <div class="Heading2-M pt-3 pb-3">{{ isEditMode ? 'Update Group' : 'Create New Group' }}</div>
            <div class="header-actions">
                <button kendoButton themeColor="primary" fillMode="outline" class="Body-R btn-custom-width"
                    (click)="cancel()">Cancel</button>
                <button kendoButton themeColor="primary" fillMode="solid" [disabled]="!isCreateEnabled()"
                    (click)="createEmailGroup()" class="Body-R btn-custom-width btn-create">{{ isEditMode ? 'Save' :
                    'Create' }}</button>
            </div>
        </div>

        <div class="create-new-group-content pt-4 pl-4 pr-3 pb-3">
            <div class="group-name-header mb-3">
                <label for="newGroupName" class="control-label Caption-M mb-1">Group Name <span
                        class="required-field">*</span></label>
                <input type="text" id="newGroupName" class="form-control Body-R" [(ngModel)]="newGroupName"
                    placeholder="Enter Name" (ngModelChange)="hasError = false"
                    [ngClass]="{'border-danger' : hasError}">
                <div *ngIf="hasError" class="d-flex justify-content-between">
                    <img alt="Error" src="assets/dist/images/exclamation-circle-icon.svg"  class="info-icon mr-1"/>
                    <div class="invalid-feedback d-block text-danger Caption-R">
                        {{errorMessage}}
                    </div>
                </div>
            </div>

            <div class="row">

                <div class="col-6">
                    <div class="email-list-container">
                        <div class="email-list-header pt-2 pb-2 pl-3 pr-3">
                            <span class="Body-M">Email List</span>
                            <button class="btn-add" (click)="addNewMember()">
                                <img class="custom-size" alt="" src="assets/dist/images/add-btn-outline.svg" />
                            </button>
                        </div>

                        <div class="email-list-content">
                            <!-- Kendo Grid -->
                            <kendo-grid [data]="newMembers" [scrollable]="'none'" [sortable]="false"
                                [filterable]="false" [pageable]="false" [navigable]="true"
                                class="email-grid noStyleGrid">

                                <!-- Name column -->
                                <kendo-grid-column field="Name" width="45%">
                                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                                        <div class="input-with-clear">
                                            <input type="text" class="form-control Caption-R"
                                                [(ngModel)]="dataItem.name" placeholder="Enter here">
                                            <span *ngIf="dataItem.name" class="btn-clear" (click)="dataItem.name = ''">
                                                <img alt="Clear text" src="assets/dist/images/end-icon.svg" />
                                            </span>
                                        </div>
                                    </ng-template>
                                </kendo-grid-column>

                                <!-- Email column -->
                                <kendo-grid-column field="Email" width="55%">
                                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                                        <div style="display: flex; align-items: center; position: relative;">
                                            <div class="input-with-clear" style="flex-grow: 1;">
                                                <input type="email" class="form-control Caption-R"
                                                    [(ngModel)]="dataItem.email" placeholder="Enter here">
                                                <span *ngIf="dataItem.email" class="btn-clear"
                                                    (click)="dataItem.email = ''">
                                                    <img alt="Clear text" src="assets/dist/images/end-icon.svg" />
                                                </span>
                                            </div>
                                            <button class="btn-remove ml-3" (click)="removeNewMember(rowIndex)"
                                                title="Remove">
                                                <img alt="Clear text" src="assets/dist/images/minus-circle.svg" />
                                            </button>
                                        </div>
                                    </ng-template>
                                </kendo-grid-column>

                            </kendo-grid>
                        </div>
                    </div>
                </div>


                <div class="col-6">
                    <div class="companies-list-container">
                        <app-repository-configuration-company-list class="company-list-component"
                            [preSelectedCompanies]="selectedCompanies"
                            [showSelectionContainer]="false"
                            (selectedCompanies)="onCompanySelected($event)">
                        </app-repository-configuration-company-list>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>