import { Component, Input, OnInit, ViewChild, ElementRef } from '@angular/core';
import { GlobalConstants, CellEditConstants, NumberDecimalConst } from "../../../common/constants";
import { ToastrService } from "ngx-toastr";
import { PortfolioCompanyService } from "../../../services/portfolioCompany.service";
import { ValuationType } from "../../Valuation-Model/shared/valuation-type.enum";
import { DropDownFilterSettings } from '@progress/kendo-angular-dropdowns';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AdjustmentType } from "../../Valuation-Model/shared/adjustment-types";
import { isNumeric } from "@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric";
import { MatMenu,MatMenuTrigger } from '@angular/material/menu';

const TOASTER_POSITION = CellEditConstants.ToasterMessagePosition;
const SUCCESS_MESSAGE = GlobalConstants.SDGImageUploadSuccess;
const FAILURE_MESSAGE = GlobalConstants.SDGImageUploadFailure;

@Component({
  selector: 'app-valuation-summary',
  templateUrl: './valuation-summary.component.html',
  styleUrls: ['./valuation-summary.component.scss']
})
export class ValuationSummaryComponent implements OnInit {

  @Input() pcId: number = 0;
  @Input() valuationSummary: any = null;
  @Input() reportingCurrencyCode: any = "";
  @Input() fundId: string = "";
    @ViewChild("menu") uiuxMenu!: MatMenu;
    @ViewChild("filterMenuTrigger") menuTrigger: MatMenuTrigger;
  vsTitle: string = '';
  numberDecimalConst = NumberDecimalConst;
  valuationType: typeof ValuationType = ValuationType;
  selectedTab: any = null;
  tabList: any = [];
  isValuationSummary: boolean = false;
  isLoading: boolean = false;
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: 'startsWith',
  };
  periodType: any[] = [];
  selectedPeriodType: any;
  public virtual: any = {
    itemHeight: 32,
    pageSize: 20
  };
  yearOptions: any = [];
  quarterOptions: any = [
    { value: "Q1", text: "Q1", number: 1 },
    { value: "Q2", text: "Q2", number: 2 },
    { value: "Q3", text: "Q3", number: 3 },
    { value: "Q4", text: "Q4", number: 4 },
  ];
  fromQuarter: any;
  fromYear: any;
  toQuarter: any;
  toYear: any;
  filterForm: FormGroup;
  valuationSummaryModel: any[] = [];
  frozenHeader: any = [{ field: "Kpi", header: "KPIs" }];
  tableFrozenColumns = [];
  tableColumnsHeaders: any[] = [];
  adjustmentType: typeof AdjustmentType = AdjustmentType;
  kpiList: string[] = [
    "Valuation Methodology",
    "Mean Multiple",
    "Median Mulitple",
    "Discount/Premium/Par",
    "Target Multiple Mean/Median",
    "Target Company Metric Value",
    "Enterprise Value at Period End",
    "Net Debt",
    "Preferred Equity",
    "Minority Interest",
    "Other",
    "Company Equity Value",
    "Fund Ownership (%)",
    "Fair Value"
  ];

  constructor(
    private _portfolioCompanyService: PortfolioCompanyService,
    private _toasterService: ToastrService,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      periodType: [null, Validators.required],
      fromQuarter: [null],
      fromYear: [null],
      toQuarter: [null],
      toYear: [null]
    });
  }

  /**
   * Lifecycle hook that is called after data-bound properties of a directive are initialized.
   */
  ngOnInit() {
    this.initializeComponent();
  }

  /**
   * Initializes the component by setting up initial values and fetching necessary data.
   */
  initializeComponent() {
    this.isValuationSummary = !!this.valuationSummary;
    this.vsTitle = this.valuationSummary?.displayName ?? '';
    this.getTabs();
    this.getPeriodType();
    this.populateYearOptions();
    this.updateValidation(this.periodType[0]);
    this.filterForm.get('periodType')?.setValue(this.periodType[0]); // Ensure validation is updated on init
    this.getValuationSummary();
  }

  /**
   * Populates the year options for the dropdown.
   */
  populateYearOptions() {
    for (let y = 2000; y <= 2050; y++) {
      this.yearOptions.push({ value: y, text: y.toString() });
    }
  }

  /**
   * Prepares the request object for fetching valuation summary data.
   * @returns The request object.
   */
  prepareValuationSummaryRequest() {
    return {
      valuationCategoryId: this.selectedTab?.id,
      fundId: this.fundId,
      companyId: this.pcId,
      fromQuarter: this.filterForm.get('fromQuarter')?.value?.value ?? '',
      fromYear: this.filterForm.get('fromYear')?.value?.value ?? 0,
      toQuarter: this.filterForm.get('toQuarter')?.value?.value ?? '',
      toYear: this.filterForm.get('toYear')?.value?.value ?? 0,
      periodId: this.filterForm.get('periodType')?.value?.periodTypeId ?? 0
    };
  }

  /**
   * Checks if the given value is a number.
   * @param str The value to check.
   * @returns True if the value is a number, false otherwise.
   */
  isNumberCheck(str: any) {
    return isNumeric(str);
  }

  /**
   * Fetches the valuation summary data based on the current filter settings.
   */
  getValuationSummary() {
    this.isLoading = true;
    const requestDto = this.prepareValuationSummaryRequest();
    this._portfolioCompanyService.getValuationSummaryData(requestDto).subscribe({
      next: (response) => {
        this.valuationSummaryModel = [];
        this.tableColumnsHeaders = [];
        if (response.length > 0) {
          this.processValuationSummaryResponse(response);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching valuation summary', error);
        this.isLoading = false;
      }
    });
  }

  /**
   * Processes the response from the valuation summary API and updates the component state.
   * @param response The response data.
   */
  processValuationSummaryResponse(response: any) {
    response.forEach((element: any) => {
      this.tableColumnsHeaders.push({ field: `${element.quarter} ${element.year}`, header: `${element.quarter} ${element.year}` });
    });
    this.kpiList.forEach((kpi: string) => {
      let row = this.createRowForKpi(kpi, response);
      this.valuationSummaryModel.push(row);
    });
    this.tableFrozenColumns = this.frozenHeader;
  }

  /**
   * Creates a row for the given KPI based on the response data.
   * @param kpi The KPI name.
   * @param response The response data.
   * @returns The row object.
   */
  createRowForKpi(kpi: string, response: any) {
    let row: any = { Kpi: kpi };
    response.forEach((element: any) => {
      if (kpi === "Target Multiple Mean/Median") {
        row[`${element.quarter} ${element.year}-Pre`] = element['selectedComps'];
        row[`${element.quarter} ${element.year}`] = this.calculateTargetMultiple(element, kpi);
      } else if (kpi === "Discount/Premium/Par") {
        row[`${element.quarter} ${element.year}-Pre`] = this.getAdjustmentType(element['adjustmentTypeId']);
        row[`${element.quarter} ${element.year}`] = this.getAdjustmentValue(element[this.getFieldName(kpi)], element['adjustmentTypeId']);
      } else {
        row[`${element.quarter} ${element.year}`] = element[this.getFieldName(kpi)];
      }
    });
    return row;
  }

  /**
   * Calculates the target multiple based on the adjustment type and selected comps.
   * @param element The response element.
   * @param kpi The KPI name.
   * @returns The calculated target multiple.
   */
  calculateTargetMultiple(element: any, kpi: string) {
    const adjustmentFactor = element["adjustmentFactor"];
    const selectedComps = element['selectedComps']?.toLowerCase();
    const mean = element["mean"];
    const median = element["median"];

    switch (element['adjustmentTypeId']) {
      case this.adjustmentType.AtPar:
        return selectedComps === "mean" ? mean : median;
      case this.adjustmentType.Discount:
        return selectedComps === "mean" ? mean * ((100 - adjustmentFactor) / 100) : median * ((100 - adjustmentFactor) / 100);
      case this.adjustmentType.Premium:
        return selectedComps === "mean" ? mean * ((100 + adjustmentFactor) / 100) : median * ((100 + adjustmentFactor) / 100);
      default:
        return null;
    }
  }

  /**
   * Gets the prefix for the given row data and column.
   * @param rowData The row data.
   * @param col The column name.
   * @returns The prefix value.
   */
  getPrefix(rowData: any, col: string) {
    return rowData[`${col}-Pre`];
  }

  /**
   * Gets the adjustment type name based on the adjustment type ID.
   * @param adjustmentTypeId The adjustment type ID.
   * @returns The adjustment type name.
   */
  getAdjustmentType(adjustmentTypeId: number) {
    switch (adjustmentTypeId) {
      case this.adjustmentType.AtPar:
        return "At Par";
      case this.adjustmentType.Discount:
        return "Discount";
      case this.adjustmentType.Premium:
        return "Premium";
      default:
        return "";
    }
  }

  /**
   * Calculates the adjusted value based on the provided adjustment type.
   *
   * @param value - The original value to be adjusted. It should be a number or a value that can be converted to a number.
   * @param adjustmentTypeId - The type of adjustment to be applied. It should correspond to one of the adjustment types defined in `this.adjustmentType`.
   * @returns The adjusted value based on the adjustment type. If the value is not a number, the function returns `undefined`.
   *
   * Adjustment types:
   * - AtPar: Returns the original value.
   * - Discount: Returns the value multiplied by -1.
   * - Premium: Returns the original value.
   * - Default: Returns the original value.
   */
  getAdjustmentValue(value: any, adjustmentTypeId: number) {
    if (!isNaN(Number(value))) {
      switch (adjustmentTypeId) {
        case this.adjustmentType.AtPar:
          return value;
        case this.adjustmentType.Discount:
          return value * -1;
        case this.adjustmentType.Premium:
          return value;
        default:
          return value;
      }
    }
    else {
      return value;
    }
  }

  /**
   * Gets the field name for the given KPI.
   * @param kpi The KPI name.
   * @returns The field name.
   */
  getFieldName(kpi: string) {
    const fieldMap: { [key: string]: string } = {
      "Valuation Methodology": "methodology",
      "Mean Multiple": "mean",
      "Median Mulitple": "median",
      "Discount/Premium/Par": "adjustmentFactor",
      "Target Multiple Mean/Median": "targetMetricValue",
      "Target Company Metric Value": "targetMetricValue",
      "Enterprise Value at Period End": "attributeValue",
      "Net Debt": "netDebt",
      "Preferred Equity": "preferredEquity",
      "Minority Interest": "minorityInterest",
      "Other": "other",
      "Company Equity Value": "companyEquityValue",
      "Fund Ownership (%)": "fundOwnership",
      "Fair Value": "fairValue"
    };
    return fieldMap[kpi] || "";
  }

  /**
   * Gets the period types and sets the default selected period type.
   */
  getPeriodType() {
    if (this.periodType.length === 0) {
      this.periodType.push(
        { name: "Latest 4 quarters", periodTypeId: 1 },
        { name: "Under date range", periodTypeId: 2 }
      );
      this.selectedPeriodType = this.periodType[0];
    }
  }

  /**
   * Handles the period type change event and updates validation accordingly.
   * @param event The event data.
   */
  onPeriodChange(event: any) {
    this.updateValidation(event);
  }

  /**
   * Gets the available tabs for the valuation summary and sets the default selected tab.
   */
  getTabs() {
    const availableTabs = this.valuationSummary?.subPageFieldList?.filter(x => x.isActive) || [];
    this.isValuationSummary = availableTabs.length > 0;
    availableTabs.forEach((element: any, index: number) => {
      this.tabList.push({
        name: element?.name,
        displayName: element?.displayName,
        id: this.getValuationCategoryId(element?.name),
        isActive: index === 0,
        isChart: element?.isChart,
        isDisplay: element?.isActive,
      });
    });
    this.selectedTab = this.tabList[0] ?? null;
  }

  /**
   * Gets the valuation category ID based on the tab name.
   * @param tabName The tab name.
   * @returns The valuation category ID.
   */
  getValuationCategoryId(tabName: string) {
    switch (tabName) {
      case "Trading Comps":
        return this.valuationType.TradingComps;
      case "Transaction Comps":
        return this.valuationType.TransactionComps;
      default:
        return 0;
    }
  }

  /**
   * Changes the selected tab and fetches the valuation summary data for the new tab.
   * @param tab The selected tab.
   */
  changeTabType(tab: any) {
    if (tab) {
      this.isLoading = true;
      this.tabList.forEach((row: { isActive: boolean }) => (row.isActive = false));
      tab.isActive = true;
      this.selectedTab = tab;
      this.isLoading = false;
      this.resetForm();
      this.getValuationSummary();
    }
  }

  /**
   * Shows a success message using the toaster service.
   * @param message The message to display.
   */
  showSuccessMessage(message: string) {
    this._toasterService.success(message, '', {
      positionClass: TOASTER_POSITION,
    });
  }

  /**
   * Submits the filter form and fetches the valuation summary data based on the filter settings.
   */
  onSubmitFilter() {
    if (this.filterForm.valid) {
      this.getValuationSummary();
      this.menuTrigger.closeMenu();
    }
  }

  /**
   * Resets the filter form to its initial state.
   */
  resetForm() {
    this.filterForm.reset();
    this.filterForm.get('periodType')?.setValue(this.periodType[0]);
  }

  /**
   * Updates the validation rules for the filter form based on the selected period type.
   * @param periodType The selected period type.
   */
  updateValidation(periodType: any) {
    const fields = ['fromQuarter', 'fromYear', 'toQuarter', 'toYear'];
    if (periodType?.periodTypeId === 1) {
      this.clearValidators(fields);
    } else if (periodType?.periodTypeId === 2) {
      this.setValidators(fields, Validators.required);
    }
    this.updateValueAndValidity(fields);
  }

  /**
   * Clears the validators for the given form fields.
   * @param fields The form fields.
   */
  clearValidators(fields: string[]) {
    fields.forEach(field => this.filterForm.get(field)?.clearValidators());
  }

  /**
   * Sets the validators for the given form fields.
   * @param fields The form fields.
   * @param validator The validator to set.
   */
  setValidators(fields: string[], validator: any) {
    fields.forEach(field => this.filterForm.get(field)?.setValidators(validator));
  }

  /**
   * Updates the value and validity of the given form fields.
   * @param fields The form fields.
   */
  updateValueAndValidity(fields: string[]) {
    fields.forEach(field => this.filterForm.get(field)?.updateValueAndValidity());
  }

  handleFilter(value, dataArray: any[], type: string): any[] {
    if(type === 'quarter') {
      return this.fromQuarter?.filter(
        (s) => s.text.toLowerCase().indexOf(value.toLowerCase()) !== -1
      ) || [];
    }
    else if(type === 'year') {
      return this.yearOptions?.filter(
        (s) => s.text.toLowerCase().indexOf(value.toLowerCase()) !== -1
      ) || [];
    }
    else{
      return this.periodType?.filter(
        (s) => s.name.toLowerCase().indexOf(value.toLowerCase()) !== -1
      ) || [];
    }
  }
}

