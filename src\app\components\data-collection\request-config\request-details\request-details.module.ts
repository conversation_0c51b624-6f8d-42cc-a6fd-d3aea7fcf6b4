import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpServiceInterceptor } from 'src/app/interceptors/http-service-interceptor';
import { KendoService } from 'src/app/services/kendo.service';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { AngularResizeEventModule } from 'angular-resize-event';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { RouterModule } from '@angular/router';
import { RequestDetailsComponent } from '../request-details/request-details.component';
import { DataRequestService } from 'src/app/services/data-request.service';
import { MaterialModule } from 'src/app/custom-modules/material.module';
@NgModule({
  declarations: [
    RequestDetailsComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedDirectiveModule,
    SharedComponentModule,
    AngularResizeEventModule,
    CommonModule,
    SharedComponentModule,
    KendoModule,
    MaterialModule,
    RouterModule.forChild([{ path: "", component: RequestDetailsComponent } ]),
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpServiceInterceptor,
      multi: true,
    },
    KendoService,
    DataRequestService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class RequestDetailsModule { }
