
export interface DataAuditDto {
    auditInfo: DataAuditModel;
    pcAudits: PcAuditLogModel[];
}
export interface PcAuditLogModel {
    auditId: number;
    attributeId: number;
    oldValue: string;
    newValue: string;
    source: string;
    documentId: string;
    supportingDocumentsId: string;
    documentName: string;
    extension: string;
    commentId: number;
    uploadedBy: string;
    createdOn: string;
}
export interface DataAuditModel {
    kpiId: number;
    kpi: string;
    currencyCode: string;
    moduleName: string;
    kpiInfo: string;
  }
  export interface PcAuditDocumentModel {
    documentId: string;
    documentName: string;
    extension: string;
  }
