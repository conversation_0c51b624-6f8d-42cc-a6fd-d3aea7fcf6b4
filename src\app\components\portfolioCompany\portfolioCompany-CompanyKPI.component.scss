@import "/src/variables";
.card {
    margin-top: -4px !important;
}

.financial-tab-header {
    text-align: left;
    font-size: 14px;
    letter-spacing: 1px;
    color: $nep-dark-black;
    font-family: "Helvetica Neue LT W05_65 Medium",Arial, Verdana, Tahoma,sans-serif;
}

.financial-page {
    background: $nep-white 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 12px $nep-shadow-color;
    border: 1px solid $nep-divider;
    border-radius: 4px;
    opacity: 1;
}

.allvalues{
  float:right;
  font-size:12px;
  margin-right:12px;
  color: $nep-icon-grey;
}

.tab-bg {
    background-color: $nep-white !important;
}

.outer-section {
    background: $nep-base-grey 0% 0% no-repeat padding-box;
    border: 1px solid $nep-divider;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    box-shadow: 0px 0px 12px $nep-shadow-color;
}

.nav-link {
    background-color: transparent !important;
    letter-spacing: 0px;
    color: $nep-text-grey;
    font-size: 0.9rem !important;
    padding-top: 9px;
    padding-bottom: 9px;
    &.active {
        background-color: $nep-base-grey !important;
        font-family: "Helvetica Neue LT W05_65 Medium",Arial, Verdana, Tahoma,sans-serif;
        color: $nep-primary !important;
        font-size: 0.9rem !important;
    }
}

.custom-tabs>.nav-tabs .nav-link.active,
.custom-tabs>.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
    color: $nep-nav-tab;
    background-color: $nep-white;
    border-color: $nep-nav-tab-border-color $nep-nav-tab-border-color $nep-white-secondary;
    border-bottom: none !important;
    top: 2px !important;
    position: relative !important;
}
.settingMoreThan25Chars{
    white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 25ch;
  }

  ::ng-deep  .panel{
    border:0px solid transparent !important;
    border-radius:0px
  }
  .QMY_Container{
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #DEDFE0;
    border-radius: 4px;
    opacity: 1;
    cursor: pointer;
}
.QMY_Text{
    text-align: left;
    font-size: 14px;
    letter-spacing: 0px;
    color: #55565A;
    opacity: 1;
}
.QMYStyle{
    position: relative;
    top: -0.25rem;
    padding: 0.2rem 0.75rem 0.2rem 0.75rem;
}
.YStyle{
    margin-right: 0.25rem !important;
}

.activeQMY{
    background: #F7F8FC 0% 0% no-repeat padding-box !important;
    color: #4061C7 !important;
    border-radius: 4px;
}
.headerSize {
    font-size: 1.5rem;
    padding-top:4px;
    padding-right: 12px;
}
.backgroundColor{
    background-color: $nep-white !important;
}
.topBorder{
    border-top: 1px solid #dee2e6 !important; 
}
.custom-padding{
    padding-left: 2px !important;
    padding-right: 2px !important;
}
.comm-footnote{
  position: relative !important;
  z-index: 1;
}