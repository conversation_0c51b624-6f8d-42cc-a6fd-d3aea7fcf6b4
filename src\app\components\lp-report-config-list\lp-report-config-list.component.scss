@import '../../../variables';
@import '../../../../src/assets/dist/css/font';
.lp-report-config-list-section{
    box-shadow: 0px 0px 8px 0px #0000001F;
    .search-section{
        background: #FAFAFA;
        border: 1px solid #E6E6E6;
        padding: 7px 16px;
        border-radius: 4px 4px 0px 0px;
    }
    .content-section{
        height: calc(100vh - 258px);
        .no-content-section{
            margin-top: 12.5%;
            .no-content-text{
                color: #2B2B33;
                @extend .Body-M;
                .template-link{
                    text-decoration: underline;
                }
             }
             a{
                 color: $nep-primary;
                 cursor: pointer;
             }
             .template-text{
                 color: #50505C;
             }
        }
        .template-content{
            width: 90%;
        }
    }
}
.tab-shadow {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000014;
    opacity: 1;
    margin-bottom: 8px;
    margin-top: -18.8px;
    margin-left: -20px;
    margin-right: -20px;
    padding-top: 12px;
    padding-left: 5px;
}
.mat-mdc-menu-panel.notifications-dropdown {
    max-width:none;
    width: 100vw; 
    margin-left: -8px;
    margin-top: 24px;
    overflow: visible;
}

.notification-row{
    width: 424px;
}
.p-label-padding {
    padding-left: 12px;
}   
.label-content-padding{
    padding-top: 20px !important;
}
.accordion-row {
    opacity: 1;
    margin-left: -20px;
    margin-right: -20px;
}