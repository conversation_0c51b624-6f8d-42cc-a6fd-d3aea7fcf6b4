import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { AuditService } from "src/app/services/audit.service";
import { ViewEsgAduitlogsComponent } from "./view-esg-aduitlogs.component";
import { HttpClientTestingModule} from '@angular/common/http/testing';
import { FormsModule } from '@angular/forms';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { EsgService } from 'src/app/services/esg.services';
import { ToastrService } from "ngx-toastr";

describe('ViewEsgAuditLogsComponent', () => {
  let component: ViewEsgAduitlogsComponent;
  let auditService: AuditService;
  let fixture: ComponentFixture<ViewEsgAduitlogsComponent>;  
  let esgService: EsgService;
  let toastrService: ToastrService;
  let mockAuditService: Partial<AuditService>;
  let mockMiscService: Partial<MiscellaneousService>;
  beforeEach(async () => {
   
    const toastrServiceStub = () => ({
      overlayContainer: {},
      success: (toasterMessage, string, object) => ({}),
      error: (toasterMessage, string, object) => ({})
    });
    mockMiscService = {
      downloadAllFormatFile: () => {}
    };
   await TestBed.configureTestingModule({
      imports: [FormsModule,HttpClientTestingModule],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [ AuditService, EsgService, MiscellaneousService,
        { provide: AuditService, useValue: jasmine.createSpyObj('AuditService', ['getSupportingDocs','exportDocFile','exportZipFile','exportSourceDocFile']) },
        { provide: 'BASE_URL', useValue: 'http://example.com' },
        {provide: MiscellaneousService, useValue: mockMiscService},
        { provide: EsgService, useValue: jasmine.createSpyObj('EsgService', ['getEsgKpiAuditLog', 'setEsgSelection']) },
        { provide: ToastrService, useFactory: toastrServiceStub }
      ]
    })
    .compileComponents();
    fixture = TestBed.createComponent(ViewEsgAduitlogsComponent);
    component = fixture.componentInstance;
    auditService = TestBed.inject(AuditService);
  });

  it("can load instance", () => {
    expect(component).toBeTruthy();
  });


  it('should get supporting documents', () => {
    const docIds = '1,2,3';
    const mockDocs = [{ id: 1 }, { id: 2 }, { id: 3 }];
    (auditService.getSupportingDocs as jasmine.Spy).and.returnValue(of(mockDocs));

    component.getSupportingDocuments(docIds);

    expect(component.isLoader).toBeFalse();
    expect(component.documentData).toEqual(mockDocs);
    expect(auditService.getSupportingDocs).toHaveBeenCalledWith([1, 2, 3]);
  });

  it('should handle error when getting supporting documents', () => {
    const docIds = '1,2,3';
    (auditService.getSupportingDocs as jasmine.Spy).and.returnValue(throwError(() =>'error'));

    component.getSupportingDocuments(docIds);

    expect(component.isLoader).toBeFalse();
    expect(auditService.getSupportingDocs).toHaveBeenCalledWith([1, 2, 3]);
  });
  it('should handle error when getting supporting documents', () => {
    const docIds = '1,2,3';
    (auditService.getSupportingDocs as jasmine.Spy).and.returnValue(throwError(() =>'error'));

    component.getSupportingDocuments(docIds);

    expect(component.isLoader).toBeFalse();
    expect(auditService.getSupportingDocs).toHaveBeenCalledWith([1, 2, 3]);
  });
  it('should handle unexpected service response', () => {
    const docIds = '1,2,3';
    (auditService.getSupportingDocs as jasmine.Spy).and.returnValue(of(null));
  
    component.getSupportingDocuments(docIds);
  
    expect(auditService.getSupportingDocs).toHaveBeenCalledWith([1, 2, 3]);
    expect(component.documentData).toBeNull();
  });
  it('should initialize with correct data and call GetAuditlog', () => {
    const mockData = {
      PortfolioCompanyID: undefined,
      SubPageId: undefined,
      EsgKpiRecordId: undefined,
      KpiId: undefined
    };
  
    // Mock the history.state object
    Object.defineProperty(window, 'history', {
      value: {
        state: {
          data: mockData
        }
      },
      writable: true
    });
  
    spyOn(component, 'GetAuditlog');
  
    component.ngOnInit();
  
    expect(component.esgAuditModel).toEqual({
      CompanyId: mockData.PortfolioCompanyID,
      EsgModuleId: mockData.SubPageId,
      EsgKpiRecordId: mockData.EsgKpiRecordId,
      KpiId:mockData.KpiId
    });
  
    expect(component.GetAuditlog).toHaveBeenCalled();
  });
  it('should handle null data in history state', () => {
    // Mock the history.state object
    Object.defineProperty(window, 'history', {
      value: {
        state: {
          data: null
        }
      },
      writable: true
    });
  
    spyOn(component, 'GetAuditlog');
  
    component.ngOnInit();
  
    expect(component.esgAuditModel).toEqual({
      CompanyId: undefined,
      EsgModuleId: undefined,
      EsgKpiRecordId: undefined,
      KpiId:undefined

    });
  
    expect(component.GetAuditlog).toHaveBeenCalled();
  });
  it('should initialize properties correctly and call getSupportingDocuments when documentIds is not null', () => {
    const documentIds = '123';
    spyOn(component, 'getSupportingDocuments');
  
    component.openDoc(documentIds);
  
    expect(component.documentData).toEqual([]);
    expect(component.isOpenPopup).toBeTrue();
    expect(component.isDocument).toBeTrue();
    expect(component.isComments).toBeFalse();
    expect(component.getSupportingDocuments).toHaveBeenCalledWith(documentIds);
  });
  it('should not call getSupportingDocuments when documentIds is null', () => {
    const documentIds = null;
    spyOn(component, 'getSupportingDocuments');
  
    component.openDoc(documentIds);
  
    expect(component.documentData).toEqual([]);
    expect(component.isOpenPopup).toBeTrue();
    expect(component.isDocument).toBeTrue();
    expect(component.isComments).toBeFalse();
    expect(component.getSupportingDocuments).not.toHaveBeenCalled();
  });
  it('should initialize properties correctly and call getComment when commentId is not null and greater than 0', () => {
    const commentId = 1;
    spyOn(component, 'getComment');
  
    component.openCommentsPopUp(commentId);
  
    expect(component.commentText).toEqual('');
    expect(component.isOpenPopup).toBeTrue();
    expect(component.isDocument).toBeFalse();
    expect(component.isComments).toBeTrue();
    expect(component.getComment).toHaveBeenCalledWith(commentId);
  });
  it('should not call getComment when commentId is null or less than or equal to 0', () => {
    const commentId = null;
    spyOn(component, 'getComment');
  
    component.openCommentsPopUp(commentId);
  
    expect(component.commentText).toEqual('');
    expect(component.isOpenPopup).toBeTrue();
    expect(component.isDocument).toBeFalse();
    expect(component.isComments).toBeTrue();
    expect(component.getComment).not.toHaveBeenCalled();
  });
  it('should return true when input is a number', () => {
    const input = '123';
    const result = component.isNumberCheck(input);
    expect(result).toBeTrue();
  });
  
  it('should return false when input is not a number', () => {
    const input = 'abc';
    const result = component.isNumberCheck(input);
    expect(result).toBeFalse();
  });
  it('should return number with commas and two decimal places when input is a number', () => {
    const input = '123456.789';
    const result = component.numberWithCommas(input);
    expect(result).toEqual('123,456.79');
  });
  
  it('should return original input when input is not a number', () => {
    const input = 'abc';
    const result = component.numberWithCommas(input);
    expect(result).toEqual('abc');
  });
  it('should handle error while downloading file', () => {
    const fileId = '1';
    const fileName = 'test-file.pdf';
    const error = 'Error message';
    const auditService = TestBed.inject(AuditService);
    (auditService.exportDocFile as jasmine.Spy).and.returnValue(throwError(() => error));

    component.downloadFile(fileId, fileName);

    expect(auditService.exportDocFile).toHaveBeenCalledWith({ 
      DocumentId: fileId,
      DocumentName: fileName,
      Period: '',
      KpiId: 0,
      ValueType: '',
      SubPageId: 0,
      IsSourceFile: false,
      SectionId: 0
    });
    expect(component.isDocSupportLoading).toBeFalse();
  });
  it('should download zip file', () => {
    const mockResponse = new Blob();
    const auditService = TestBed.inject(AuditService);
    const miscService = TestBed.inject(MiscellaneousService);
    (auditService.exportZipFile as jasmine.Spy).and.returnValue(of(mockResponse));
    spyOn(miscService, 'downloadAllFormatFile');

    component.downloadZip();

    expect(auditService.exportZipFile).toHaveBeenCalledWith(component.documentData);
    expect(miscService.downloadAllFormatFile).toHaveBeenCalled();
    expect(component.isDocLoading).toBeFalse();
  });
  it('should handle error while downloading zip file', () => {
    const error = 'Error message';
    (auditService.exportZipFile as jasmine.Spy).and.returnValue(throwError(() =>error));
  
    component.downloadZip();
  
    expect(auditService.exportZipFile).toHaveBeenCalledWith(component.documentData);
    expect(component.isDocLoading).toBeFalse();
  });
  it('should download source file', () => {
    const fileId = '1';
    const fileName = 'test-file.pdf';
    const mockResponse = new Blob();
    const auditService = TestBed.inject(AuditService);
    const miscService = TestBed.inject(MiscellaneousService);
    (auditService.exportSourceDocFile as jasmine.Spy).and.returnValue(of(mockResponse));
    spyOn(miscService, 'downloadAllFormatFile');

    component.downloadSourceFile(fileId, fileName);

    expect(auditService.exportSourceDocFile).toHaveBeenCalledWith({ 
        DocumentId: fileId, 
        DocumentName: fileName,
        Period: undefined,
        KpiId: undefined,
        SubPageId: undefined
    } as any); // Bypass TypeScript's type checking
    expect(miscService.downloadAllFormatFile).toHaveBeenCalled();
    expect(component.isDocSupportLoading).toBeFalse();
});

it('should handle error while downloading source file', () => {
  const fileId = '1';
  const fileName = 'test-file.pdf';
  const error = 'Error message';
  const auditService = TestBed.inject(AuditService);
  (auditService.exportSourceDocFile as jasmine.Spy).and.returnValue(throwError(() => error));

  component.downloadSourceFile(fileId, fileName);

  expect(auditService.exportSourceDocFile).toHaveBeenCalledWith({ 
      DocumentId: fileId, 
      DocumentName: fileName,
      Period: undefined,
      KpiId: undefined,
      SubPageId: undefined
  } as any); // Bypass TypeScript's type checking
  expect(component.isDocSupportLoading).toBeFalse();
});

afterEach(() => {
  fixture.destroy();
});
});
