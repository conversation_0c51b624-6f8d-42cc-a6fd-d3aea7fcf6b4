import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { CloService } from '../../../services/clo.service';
import { InvestmentCompany } from '../investment-company-model';
import { InvestCompanyService } from '../investmentcompany/investmentcompany.service';
import { TabStripComponent } from "@progress/kendo-angular-layout";
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { CommentaryPayload } from '../clo-commentries.model';
import { FeatureTableMapping, TOASTER_MSG } from 'src/app/common/constants';
import { CommonSubFeaturePermissionService } from 'src/app/services/subPermission.service';
import { CLOPermissionConstants, PermissionActions } from 'src/app/common/constants';
import { ErrorMessage } from 'src/app/services/miscellaneous.service';
import { FeaturesEnum } from 'src/app/services/permission.service';
import { PanelbarItemService } from '../shared/panelbar-item/panelbar-item.service';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-view-company-details',
  templateUrl: './view-company-details.component.html',
  styleUrls: ['./view-company-details.component.scss']
})
export class ViewCompanyDetailsComponent implements OnInit {
  public TAB_NAMES = {
    Investment_Page: 1,
    Performance_Data: 2,
    Commentaries: 3,
    NAV_Distribution: 7,
    PE_Performance_Indicators : 8,
    Return_Analysis : 9,
    Return_Composition :10,
    Currency_Exposure : 11,
  };
  @ViewChild('quillEditor', { static: false }) quillEditor: ElementRef;
  @ViewChild('tabStrip') public tabStrip: TabStripComponent;
  pageId:number=1;
  CAN_IMPORT=PermissionActions.CAN_IMPORT;
  CAN_EXPORT = PermissionActions.CAN_EXPORT;
  CAN_EDIT= PermissionActions.CAN_EDIT;
  Aggregate_CLO_Metrics=FeatureTableMapping.EDIT_DOMICILE_TABLES_NAME.Aggregate_CLO_Metrics[0];
  subscription: Subscription;
  isLoading:boolean = true;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private readonly toastrService: ToastrService,
    private sharedService: CloService,
    private investCompanyService: InvestCompanyService,
    private el: ElementRef,
    private sanitizer: DomSanitizer,
     private subPermissionService: CommonSubFeaturePermissionService,
     private pagePanelService: PanelbarItemService,
     private breadcrumbService: BreadcrumbService
  ) {
  }
  tabs:any=[];
  isCompanyFactsOpen: boolean = true;
  selectedTab: number = 0;
  compName: string = '';
  compData: InvestmentCompany;
  public companyName: any[] = [];
  id: number = -1;
  showTextbox: boolean = false;
  charCount: number = 0;
  isEdit: boolean = false;
  investmentPage:boolean=false;
  performancePage:boolean=false;
  selectedPerformanceTab: string = 'NAV_Distribution';
  editingCommentaryId: number | null = null;
  savedComments: { [key: string]: string } = {};

  commentarylist: {tableId: number,sequenceNo: number, id: number, name: string, newComment: string, commentaryType: string, glicommentry?: string, marketCommentry?: string, isExpanded: boolean, isEdit: boolean }[] = [
    {tableId: 24, id: 0,sequenceNo:1, name: 'GLI Commentary', newComment: '', commentaryType: 'GLI Commentary', glicommentry: '', marketCommentry: '', isExpanded: false, isEdit: false },
    {tableId: 25, id: 0,sequenceNo:2, name: 'Market Commentary - Global loans and CLOs', newComment: '', commentaryType: 'Market Commentary - Global loans and CLOs', glicommentry: '', marketCommentry: '', isExpanded: false, isEdit: false },
  ];
  performanceData: any[] = [];
  performanceColumns: any[] = [];

  public selectedTabIndex = 0;
  public performanceTabs :any[] = [];

  selectedTabData:any;

  companyFactsTableId=FeatureTableMapping.TABLES_NAME.Company_Facts;
  summaryTableId=FeatureTableMapping.TABLES_NAME.Investment_Summary;

  ngOnInit() {
    this.selectedTab = this.TAB_NAMES.Investment_Page;
    this.isLoading = true;
    this.subscription = this.sharedService.currentData?.subscribe(data => {
      this.compData = data;
      this.isLoading = false;
      this.updateBreadcrumbs(this.compData?.companyName);
      if (this.compData?.id) {
        this.loadCommentaries();
      }
    });
    this.route.paramMap.subscribe(params => {
      this.id = +params.get('id');
      this.getSubFeatureAccessPermissions();
      
      this.redirectToCompData(this.id);
    });
  }
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
  updateBreadcrumbs(companyName:string) {
    let newBreadcrumbs: any[] = [];
        newBreadcrumbs.push( { label: 'Investment Company', url: '/investment-company' });
        newBreadcrumbs.push( { label: companyName });
      this.breadcrumbService.setBreadcrumbs(newBreadcrumbs);
  }

  getConfiguration(){
    this.isLoading = true;
    this.investCompanyService.getTabList(this.pageId,this.id).subscribe((data:any)=>{
      let tempTabs=data;     
      this.tabs = tempTabs?.map((x) =>
        Object.assign(x, {
          onClick:"() => {}"
        })
      );
      this.tabs.forEach(page => {
        this.pagePanelService.updateTableVisibility(page.tableList,this.permissions);
        page.subTabList.forEach(tab => {
            this.pagePanelService.updateTableVisibility(tab.tableList,this.permissions);
        });
    });
    this.selectedTabData=this.tabs.find(x=>x.tabId==this.selectedTab);
      this.tabs?.forEach(element => {
        if(element.tabId==this.TAB_NAMES.Investment_Page){
          element.onClick="() => this.openCompanyFacts()";
          let usTable = element.tableList?.find(x=>x.tableId==FeatureTableMapping.EDIT_DOMICILE_TABLES_NAME.Aggregate_CLO_Metrics);
          let euTable = element.tableList?.find(x=>x.tableId==FeatureTableMapping.TOBE_CHANGE_DOMICILE_TABLES_NAME.Aggregate_CLO_Metrics);
          usTable.tableName=this.compData.domicile === 'US'?usTable.tableName:euTable.tableName;
        }
        else if(element.tabId==this.TAB_NAMES.Commentaries){
          element.onClick="() => this.openCommentaries()";
          element.tableList.forEach(el=>{
           this.commentarylist?.forEach(com=>{
            if(com.tableId==el.tableId){
              com.name=el.aliasName;
              com.sequenceNo=el.sequenceNo;
            }
           })
          })
        }
        else if(element.tabId==this.TAB_NAMES.Performance_Data){
          element.onClick="() => this.openPerformanceData()";
          this.performanceTabs=element.subTabList;          
        }
        
      });
      this.isLoading = false;
    });
  }

  redirectToCompData(id: number) {
    this.isLoading = true;
    this.investCompanyService.getInvestCompanyById(id).subscribe({
      next: (data) => {
        this.isLoading = false;
        if(data == null){
          this.toastrService.error(TOASTER_MSG.NOT_FOUND, "", { positionClass: TOASTER_MSG.POS_CENTER });
          this.router.navigate(['/investment-company']);
          return;
        }        

        this.companyName = data;
        this.id = id;
        this.sharedService.changeData(this.companyName);
        this.router.navigate(['/view-company-details', this.id]);
      },
    });
  }

  redirectToCompanyData() {
    this.router.navigate(['/investment-company']);
  }

  openCompanyFacts() {
    this.isCompanyFactsOpen = true;
  }

  openCommentaries() {
    this.loadCommentaries();
    this.isCompanyFactsOpen = false;
  }

  openPerformanceData() {
    this.isCompanyFactsOpen = false;
  }

  highlightTab(tab: number) {
    this.selectedTab = tab;
  }

  redirectToInvestmentPage(step: number) {
    if (!this.canEditCompanyFacts) {
      this.showNoAccessError();
    }else{
    this.sharedService.getStep(step);
    this.router.navigate(['/add-investment-company', this.id]);
    }
  }

  loadCommentaries() {
    this.isLoading = true;
    if (!this.compData?.id) {
      return;
    }
    this.investCompanyService.getCommentries(this.compData.id).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (Array.isArray(response)) {
          response.forEach(commentary => {
            if (commentary.glicommentry && this.canViewGLICommentry) {
              const gliComment = this.commentarylist.find(c => c.commentaryType === 'GLI Commentary');
              if (gliComment) {
                gliComment.newComment = commentary.glicommentry;
                gliComment.glicommentry = commentary.glicommentry;
                gliComment.id = commentary.id;
              }
            }
            if (commentary.marketCommentry && this.canViewMarketCommentry) {
              const marketComment = this.commentarylist.find(c => c.commentaryType === 'Market Commentary - Global loans and CLOs');
              if (marketComment) {
                marketComment.newComment = commentary.marketCommentry;
                marketComment.marketCommentry = commentary.marketCommentry;
                marketComment.id = commentary.id;
              }
            }
          });
        } else if (response && typeof response === 'object') {
          // Handle single object response
          if (response.glicommentry && this.canViewGLICommentry) {
            const gliComment = this.commentarylist.find(c => c.commentaryType === 'GLI Commentary');
            if (gliComment) {
              gliComment.newComment = response.glicommentry;
              gliComment.glicommentry = response.glicommentry;
              gliComment.id = response.id;
            }
          }
          if (response.marketCommentry && this.canViewMarketCommentry) {
            const marketComment = this.commentarylist.find(c => c.commentaryType === 'Market Commentary - Global loans and CLOs');
            if (marketComment) {
              marketComment.newComment = response.marketCommentry;
              marketComment.marketCommentry = response.marketCommentry;
              marketComment.id = response.id;
            }
          }
        }
        if(!this.isEmpty(this.commentarylist[0]?.newComment)){
          this.commentarylist[0].isExpanded=true;
        }
        if(!this.isEmpty(this.commentarylist[1]?.newComment)){
          this.commentarylist[1].isExpanded=true;
        }
        // Create a map for quick lookup
        const commentaryMap = new Map(this.commentarylist.map(com => [com.tableId, com]));
        const element= this.tabs.filter(x=>x.tabId==this.TAB_NAMES.Commentaries)
        // Iterate through the tableList and update the commentarylist
        element[0]?.tableList.forEach(el => {
          const com = commentaryMap.get(el.tableId);
          if (com) {
            com.name = el.aliasName;
            com.sequenceNo=el.sequenceNo;
          }
        });
        this.commentarylist.sort((a, b) => a.sequenceNo - b.sequenceNo);
        
      },
      error: (error) => {
        this.isLoading= false;
        this.toastrService.error('Failed to load commentaries.', "", { positionClass: "toast-center-center" });
      }
    });
  }
  
   isEmpty(val){
    val = val?.replace(/<.*?>/g, '');
    return (val === undefined || val == null || val.length <= 0) ? true : false;
}

  onSave(clo: any, commentaryType: string) {
    this.isLoading = true;
    // Fetch existing commentaries
    this.investCompanyService.getCommentries(this.compData.id).subscribe({
      next: (response) => {
        this.isLoading = false;
        // Initialize commentary fields with existing values
        let glicommentry = '';
        let marketCommentry = '';

        if (Array.isArray(response)) {
          response.forEach(commentary => {
            if (commentary.glicommentry) {
              glicommentry = commentary.glicommentry;
            }
            if (commentary.marketCommentry) {
              marketCommentry = commentary.marketCommentry;
            }
          });
        } else if (response && typeof response === 'object') {
          if (response.glicommentry) {
            glicommentry = response.glicommentry;
          }
          if (response.marketCommentry) {
            marketCommentry = response.marketCommentry;
          }
        }

        // Update the appropriate commentary based on the commentaryType
        const richTextComment = clo.newComment; // Keep the rich text with HTML tags
        if (commentaryType === 'GLI Commentary') {
          glicommentry = richTextComment;
        } else if (commentaryType === 'Market Commentary - Global loans and CLOs') {
          marketCommentry = richTextComment;
        }

        glicommentry = glicommentry || '';
        marketCommentry = marketCommentry || '';

        // Create the payload with the updated commentary
        const payload: CommentaryPayload = {
          id: clo.id || 0,
          glicommentry: glicommentry,
          marketCommentry: marketCommentry,
          investmentCompanyId: this.compData.id,
        };
this.isLoading = true;
        // Save the commentary
        this.investCompanyService.saveCommentries(payload).subscribe({
          next: (response) => {
            this.isLoading = false;
            const result = response; // Assuming the response contains the message
            this.toastrService.success(result.message, "", { positionClass: "toast-center-center" });
            clo.isEdit = false;
            this.loadCommentaries(); // Reload commentaries after saving
          },
          error: (error) => {
            this.isLoading = false;
            this.toastrService.error('Failed to update commentaries.', "", { positionClass: "toast-center-center" });
          },
        });
      },
      error: (error) => {
        this.isLoading = false;
        this.toastrService.error('Failed to load commentaries.', "", { positionClass: "toast-center-center" });
      }
    });
  }

  onCancel(clo: any): void {
    this.loadCommentaries();
    clo.isEdit = false;
  }

  selectTab(tab: any) {
    this.selectedTab = tab.tabId;
    this.selectedTabData = tab;
    if (this.selectedTab !== this.TAB_NAMES.Commentaries) {
      this.showTextbox = false;
    }
  }

  onTabSelect(e: any): void {
    this.selectedTabIndex = e.index;
    this.selectedPerformanceTab = this.performanceTabs[e.index].param;
  }

  toggleEdit(clo: any) {
    const hasEditPermission = clo.commentaryType === this.commentarylist ? this.canEditGLICommentry : this.canEditMarketCommentry;
                          
  if (!hasEditPermission) {
    this.toastrService.error(ErrorMessage.NoAccess, "", { positionClass: "toast-center-center" });
    return;
  }
  
  clo.isEdit = !clo.isEdit;
  }

  updateCharCount(event: any) {
    this.charCount = event.target.value.length;
  }

  expandPanel(clo: any) {
    this.commentarylist.forEach(item => {
      if (item !== clo) {
        item.isExpanded = false;
        item.isEdit = false;
      }
    });
    clo.isExpanded = !clo.isExpanded;
  }

  onReset(clo: any): void {
    this.charCount = 0;
    clo.newComment = '';
  }
  canViewCompanyFacts: boolean = false;
  canEditCompanyFacts: boolean = false;
  canViewInvestmentSummary: boolean = false;
  canAddInvestmentSummary: boolean = false;
  canViewGLIPortfolioComposition: boolean = false;
  canEditGLIPortfolioComposition: boolean = false;
  canExportGLIPortfolioComposition: boolean = false;
  canImportGLIPortfolioComposition: boolean = false;
  canViewAggregateCLOMetrics: boolean = false;
  canEditAggregateCLOMetrics: boolean = false;
  canExportAggregateCLOMetrics: boolean = false;
  canImportAggregateCLOMetrics: boolean = false;
  canViewPEPerformanceIndicators: boolean = false;
  canViewReturnAnalysis: boolean = false;
  canViewReturnComposition: boolean = false;
  canViewCurrencyExposure: boolean = false;
  canViewNAVDistribution: boolean = false;
  canViewGLICommentry: boolean = false;
  canEditGLICommentry: boolean = false;
  canViewMarketCommentry: boolean = false;
  canEditMarketCommentry: boolean = false;
  permissions:any=[];
  
  getSubFeatureAccessPermissions() {
    this.subPermissionService.getCommonSubFeatureAccessPermissions(this.id.toString(), FeaturesEnum.InvestmentCompany).subscribe({
      next: (result) => {
        if (result.length > 0) {
          this.permissions=result;
          this.getConfiguration();
         
          this.canViewCompanyFacts = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.CompanyFacts.FeatureId), PermissionActions.CAN_VIEW);
          this.canEditCompanyFacts = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.CompanyFacts.FeatureId), PermissionActions.CAN_EDIT);
          this.canViewInvestmentSummary = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.InvestmentSummary.FeatureId), PermissionActions.CAN_VIEW);
          this.canAddInvestmentSummary = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.InvestmentSummary.FeatureId), PermissionActions.CAN_EDIT);
          this.canViewGLIPortfolioComposition = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.GLIPortfolioComposition.FeatureId), PermissionActions.CAN_VIEW);
          this.canViewAggregateCLOMetrics = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.AggregateCloMetric.FeatureId), PermissionActions.CAN_VIEW);
          this.canViewNAVDistribution = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.NAVDistribution.FeatureId), PermissionActions.CAN_VIEW);
          this.canViewPEPerformanceIndicators = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.PEPerformanceIndicators.FeatureId), PermissionActions.CAN_VIEW);
          this.canViewReturnAnalysis = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.ReturnAnalysis.FeatureId), PermissionActions.CAN_VIEW);
          this.canViewReturnComposition = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.ReturnComposition.FeatureId), PermissionActions.CAN_VIEW);
          this.canViewCurrencyExposure = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.CurrencyExposure.FeatureId), PermissionActions.CAN_VIEW);
          this.canViewGLICommentry = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.GLICommentry.FeatureId), PermissionActions.CAN_VIEW);
          this.canEditGLICommentry = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.GLICommentry.FeatureId), PermissionActions.CAN_EDIT);
          this.canViewMarketCommentry = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.MarketCommentry.FeatureId), PermissionActions.CAN_VIEW);
          this.canEditMarketCommentry = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.MarketCommentry.FeatureId), PermissionActions.CAN_EDIT);
      
           const checkViewPermission = function() {
            return this.canViewCompanyFacts || this.canViewInvestmentSummary || 
            this.canViewGLIPortfolioComposition || this.canViewAggregateCLOMetrics
            };
            const checkPerformancePermission = () => {
              return this.canViewPEPerformanceIndicators || this.canViewReturnAnalysis || 
                     this.canViewNAVDistribution || this.canViewReturnComposition || this.canViewCurrencyExposure;
            };
            this.investmentPage = checkViewPermission.call(this);
            this.performancePage = checkPerformancePermission();

            if(!this.investmentPage){
              this.hideTab(this.TAB_NAMES.Investment_Page);
            }
            if (!this.performancePage) {
              this.hideTab(this.TAB_NAMES.Performance_Data);
            }
            if (this.compData?.id) {
              this.loadCommentaries();
            }
        }
      },
      error: (_error) => {
      }
    });
  }    
  checkPermissionAccess(permission:any[], permissionType): boolean {
    return permission.map(x => x[permissionType]).includes(true);
  }
  showNoAccessError() {
    this.toastrService.error(ErrorMessage.NoAccess, "", { positionClass: "toast-center-center" });
  }
  currentTab(tabId: number) {
    const tab = this.tabs.find(t => t.id === tabId && t.visible);
    if (tab) {
      this.selectedTab = tabId;
      tab.onClick();
    } else {
      this.selectNextVisibleTab();
    }
  }
  
  selectNextVisibleTab() {
    const nextVisibleTab = this.tabs.find(t => t.visible);
    if (nextVisibleTab) {
      this.selectedTab = nextVisibleTab.id;
      nextVisibleTab.onClick();
    }
  }
  
  hideTab(tabId: number) {
    const tab = this.tabs.find(t => t.tabId === tabId);
    if (tab) {
      tab.visible = false;
      if (this.selectedTab === tabId) {
        this.selectNextVisibleTab();
      }
    }
  }
  checkTabPermission(tabParam: number): boolean {
    switch (tabParam) {
      case this.TAB_NAMES.NAV_Distribution:
        return this.canViewNAVDistribution;
      case this.TAB_NAMES.PE_Performance_Indicators:
        return this.canViewPEPerformanceIndicators;
      case this.TAB_NAMES.Return_Analysis:
        return this.canViewReturnAnalysis;
      case this.TAB_NAMES.Return_Composition:
        return this.canViewReturnComposition;
      case this.TAB_NAMES.Currency_Exposure:
        return this.canViewCurrencyExposure;
      default:
        return false;
    }
  }
  checkTablePermissions(tableId:number,permissionType:string=null):boolean{
   return this.pagePanelService.checkTablePermissions(tableId,this.permissions,permissionType);
  }
}