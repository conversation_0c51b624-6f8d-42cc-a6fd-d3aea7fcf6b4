import { Directive, HostListener } from '@angular/core';

@Directive({
  selector: '[appNumbersCommaOnly]'
})
export class NumbersCommaOnlyDirective {
  private regex: RegExp = /^[0-9,]*$/; // Regular expression to allow only numbers and commas
  private specialKeys: string[] = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Delete'];

  constructor() {}

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    // Allow special keys
    if (this.specialKeys.includes(event.key)) {
      return;
    }

    // Prevent invalid characters
    const inputChar = event.key;
    if (!this.regex.test(inputChar)) {
      event.preventDefault();
    }
  }

  @HostListener('paste', ['$event'])
  onPaste(event: ClipboardEvent): void {
    const clipboardData = event.clipboardData || window['clipboardData'];
    const pastedInput = clipboardData.getData('text');
    if (!this.regex.test(pastedInput)) {
      event.preventDefault();
    }
  }
}