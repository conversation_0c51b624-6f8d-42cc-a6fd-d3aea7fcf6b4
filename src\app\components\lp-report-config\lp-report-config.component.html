<div class="row mr-0 ml-0 lp-template-section">
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 filter-section">
        <div class="row mr-0 ml-0">
            <div class="col-12 col-sm-12 col-lg-12 col-xl-12">
                <div class="d-inline-block pr-4 pt-3">
                    <label class="Caption-M mb-0 req-label">Fund Name</label>
                    <div class="filter-content">
                        <kendo-multiselect #multiFundSelect   [rounded]="'medium'" [fillMode]="'flat'"  [checkboxes]="true"
                            [ngClass]="{'k-multiselect-search-80':selectedCopyToFundList?.length>0}" [virtual]="virtualFund"
                            [kendoDropDownFilter]="filterSettings" name="copyToFund" 
                            [clearButton]="false"
                            class="k-multiselect-custom k-dropdown-width-300 k-multiselect-grey-chip k-multiselect-flat-custom"
                            [tagMapper]="tagMapper" [data]="lpReportConfig?.fundList"
                            [(ngModel)]="selectedCopyToFundList" [textField]="'fundName'"
                            (close)="clearSearch(multiFundSelect)" [valueField]="'fundId'" (removeTag)="onClear()"
                            (valueChange)="getFundSelected($event);" (filterChange)="onFundFilterChange($event)" [autoClose]="false" placeholder="Select Funds">
                            <ng-template kendoMultiSelectHeaderTemplate>
                                <div class="inline-container">
                                    <input name="fund-check-allhk" type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"
                                        kendoCheckBox [checked]="isCheckedCopyFundAll" [indeterminate]="isFundIndet()"
                                        (click)="onFundClick($event, filteredFunds);" />
                                    <kendo-label for="fund-check-all">Select All</kendo-label>
                                </div>
                            </ng-template>
                            <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                <span class="TextTruncate pl-1 Body-R" [title]="dataItem.fundName">{{ dataItem.fundName
                                    }}</span>
                            </ng-template>
                        </kendo-multiselect>
                    </div>
                </div>
                <div class="d-inline-block pr-4 pt-3">
                    <label class="Caption-M mb-0 req-label">Portfolio Company Name</label>
                    <div class="filter-content">
                        <kendo-multiselect [readonly]="selectedCopyToFundList?.length == 0" #multiCompanySelect [checkboxes]="true"
                            [rounded]="'medium'" [fillMode]="'flat'" [filterable]="true" (filterChange)="handleFilter($event)"
                            [ngClass]="{'k-multiselect-search-80':selectedCompanyList?.length>0}" [kendoDropDownFilter]="filterSettings"
                            name="companyList" [virtual]="virtualCompany" [clearButton]="false"
                            class="k-multiselect-custom k-dropdown-width-300 k-multiselect-grey-chip k-multiselect-flat-custom"
                            [tagMapper]="tagMapper" [data]="groupedCompanyList" [(ngModel)]="selectedCompanyList" [textField]="'companyName'"
                            (close)="clearSearch(multiCompanySelect)" [valueField]="'companyId'" (valueChange)="getCompanySelected();"
                            [autoClose]="false" placeholder="Select Companies">
                            <ng-template kendoMultiSelectHeaderTemplate>
                                <div class="inline-container">
                                    <input type="checkbox" name="company-check-all" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo" kendoCheckBox 
                                        [checked]="isCheckedCompanyAll" [indeterminate]="isCompanyIndet" (click)="onCompanyClick();" />
                                    <kendo-label for="company-check-all">Select All</kendo-label>
                                </div>
                            </ng-template>
                            <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                <div class="item-container TextTruncate">
                                    <div class="d-inline-block TextTruncate custom-width-100">
                                        <input id="company-{{dataItem.fundId}}-{{dataItem.companyId}}" type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md"
                                            kendoCheckBox [checked]="isSelected(dataItem)" (change)="onItemSelect(dataItem, $event)" />
                                        <kendo-label for="company-{{dataItem.fundId}}-{{dataItem.companyId}}" aria-label="" class="TextTruncate pl-1 Body-R"
                                            [title]="dataItem.companyName">{{
                                            dataItem.companyName }}</kendo-label>
                                    </div>
                                </div>
                            </ng-template>
                            <ng-template kendoMultiSelectGroupTemplate let-groupName>
                                <span class="TextTruncate Body-B" title="{{groupName}}">
                                    {{groupName}}
                                </span>
                            </ng-template>
                            <ng-template kendoMultiSelectFixedGroupTemplate let-groupName>
                                <span class="TextTruncate Body-B" title="{{groupName}}">
                                    {{groupName}}
                                </span>
                            </ng-template>
                        </kendo-multiselect>
                    </div>
                </div>
                <div class="d-inline-block pr-4 pt-3">
                    <label class="Caption-M mb-0 req-label">Template Name</label>
                    <div class="filter-content">
                        <kendo-textbox name="templateName" [placeholder]="'Template Name...'"
                            [readonly]="selectedCopyToFundList?.length == 0 && selectedCompanyList?.length == 0"
                            [(ngModel)]="templateName" maxlength="200" fillMode="flat" [style.width.px]="300"
                            (valueChange)="onTemplateNameChange();" [clearButton]="true">
                        </kendo-textbox>
                    </div>
                </div>
                <div class="d-inline-block filter-content pt-3">
                    <button id="lp-report-appy-changes" name="applyButton"  kendoTooltip
                        [title]="id!=undefined ? 'Making changes to Fund and Company details will change section details' : ''"
                        [disabled]="isButtonDisabled()"
                        kendoButton class="kendo-custom-button Body-R apply-btn" (click)=" id == undefined ? applyConfig() : openConfirmApply =  true"
                        fillMode="outline" themeColor="primary">Apply</button>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 Caption-I pt-2 note-section">
        Note : Making changes to Fund and Company details will change section details
    </div>
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0  content-section mt-3">
        <div class="row mr-0 ml-0">
            <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-3 pl-0  header-section-detail">
                <div class="float-right">
                    <kendo-splitbutton id="lp-report-add-new-section"
                        [disabled]="(id!=undefined ? false : !isApply) || (selectedCompanyList?.length == 0 || isTemplateExist || templateName == '' || templateName.length > 200 || selectedCopyToFundList.length == 0)"
                        (buttonClick)="openSplitButton($event)" #splitButton (itemClick)="addSection($event)"
                        class="split-button-custom" [data]="lpReportConfig?.templateSections"
                        [popupSettings]="{ popupClass:'popup-split-button', animate: true,popupAlign: { vertical: 'bottom', horizontal: 'left' } }">
                        Add New Section
                        <ng-template kendoSplitButtonItemTemplate let-dataItem>
                            <span class="TextTruncate Body-R" [fillMode]="'outline'" [size]="'medium'"
                                [themeColor]="'primary'" title="{{dataItem.pageConfigAliasName}}">{{
                                dataItem.pageConfigAliasName }}</span>
                        </ng-template>
                    </kendo-splitbutton>
                </div>
            </div>
            <form [formGroup]="form" (ngSubmit)="onSubmit($event)" #f="ngForm">
                <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                    <div cdkScrollable cdkDropListAutoScroll class="row mr-0 ml-0 section-list drag-boundary">
                        <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 section example-list"
                            formArrayName="sections" cdkDropList #toWatch="cdkDropList"
                            [cdkDropListData]="sectionDropList" (cdkDropListDropped)="drop($event)">
                            <ng-container *ngIf="sections.length > 0">
                                <div class="row mr-0 ml-0 example-box" cdkDragBoundary=".drag-boundary"
                                    cdkDragLockAxis="y" cdkDrag #elem="cdkDrag"
                                    *ngFor="let section of sections.controls; let i = index;" [formGroupName]="i">
                                    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 mb-3 section-detail">
                                        <div class="float-left section-drag">
                                            <div class="drag-bg">
                                                <a cdkDragHandle href="javascript:void">
                                                    <img src="assets/dist/images/drag-icon.svg" alt="drag-icon"
                                                        class="drag-icon">
                                                </a>
                                            </div>
                                        </div>
                                        <div class="d-inline-block drag-content-section">
                                            <ng-container [ngSwitch]="section.get('name')?.value?.sectionName">
                                                <ng-template #sectionTemplate>
                                                    <div class="d-inline-block p-3">
                                                        <kendo-label class="k-form custom-report-label"
                                                            text="Section Name">
                                                            <kendo-textbox class="custom-textbox-chip" [readonly]="true"
                                                                fillMode="flat" [style.width.px]="240"
                                                                formControlName="aliasName"
                                                                placeholder="Enter {{section.get('name')?.value?.pageConfigAliasName}}"
                                                                value>
                                                            </kendo-textbox>
                                                        </kendo-label>
                                                    </div>
                                                </ng-template>
                                                <ng-template #singleSelectGroupTemplate let-labelText="labelText" let-formControlName="formControlName" let-data="data"
                                                    let-textField="textField" let-valueField="valueField" let-required="required" let-placeholder="placeholder">
                                                    <div class="d-inline-block p-3">
                                                        <label [ngClass]="required ? 'req-label':''" class="Caption-M m-0" [text]="">{{labelText}} <span
                                                                *ngIf="formControlName" kendoTooltip   [tooltipWidth]="400"
                                                                [height]="'auto'" class="pl-1 custom-info" [title]="formControlName == 'periodComparison' ? 'Comparison will always be done with a difference of One between current and prior period' : ''">
                                                                <img src="assets/dist/images/file-info.svg" alt="info-icon" class="info-icon">
                                                            </span></label>
                                                        <div>
                                                        <kendo-multiselect #multiselect [checkboxes]="true"
                                                            [showStickyHeader]="false" [virtual]="virtual"
                                                            [rounded]="'medium'" [fillMode]="'flat'"[clearButton]="false"
                                                            [kendoDropDownFilter]="filterSettings" [name]="formControlName"
                                                            class=" k-dropdown-width-300 k-multiselect-custom k-multiselect-custom-filter  k-multiselect-custom-tree k-multiselect-chip-100 k-multiselect-grey-chip"
                                                            [tagMapper]="tagMapper" [data]="data[section.get('name')?.value?.moduleId]" 
                                                            [textField]="textField" [valueField]="valueField"
                                                            [placeholder]="placeholder"
                                                            [autoClose]="false"
                                                            [formControlName]="formControlName" [valuePrimitive]="false"
                                                            >
                                                            <ng-template kendoMultiSelectHeaderTemplate>
                                                                <div class="d-inline-block k-single-select-custom TextTruncate" *ngIf="data[section.get('name')?.value?.moduleId].length>0">
                                                                    <div kendoTooltip [tooltipWidth]="400"
                                                                    [height]="'auto'" class="d-inline-block custom-info"
                                                                        [title]="'If Financial Year End Month is not updated, December will be taken as default month'">
                                                                        <img src="assets/dist/images/file-info.svg" alt="info-icon" class="info-icon icon-img-tooltip">
                                                                    </div>
                                                                    <div kendoTooltip class="d-inline-block pl-1 Body-M custom-info"> Actual
                                                                    </div>
                                                                </div>
                                                            </ng-template>
                                                        </kendo-multiselect>
                                                        </div>
                                                    </div>
                                                </ng-template>
                                                <ng-template #comboBoxTemplate let-labelText="labelText"
                                                    let-formControlName="formControlName" let-data="data"
                                                    let-textField="textField" let-valueField="valueField"
                                                    let-required="required" let-placeholder="placeholder" let-hidden="hidden">
                                                    <div class="d-inline-block p-3" [ngClass]="hidden">
                                                        <label [ngClass]="required ? 'req-label':''"
                                                            class="Caption-M m-0" [text]="">{{labelText}} <span
                                                                *ngIf="formControlName == 'currencyModel' || formControlName == 'unitTypeModel'"
                                                                kendoTooltip class="pl-1 custom-info"
                                                                [title]="formControlName == 'unitTypeModel' ? 'Default selection is default unit type of every company' : 'Default selection is default currency type of every company'">
                                                                <img src="assets/dist/images/file-info.svg" alt="info-icon"
                                                                    class="info-icon"> 
                                                               </span></label>
                                                        <div>
                                                            <kendo-combobox
                                                                [ngClass]="section?.get(formControlName)?.value!=null ? 'kendo-select-chip':''"
                                                                [clearButton]="false"
                                                                [formControlName]="formControlName" [fillMode]="'flat'"
                                                                [name]="formControlName"
                                                                class="k-dropdown-width-200 k-select-medium k-select-flat-custom k-dropdown-height-32"
                                                                [size]="'medium'" [data]="data" [filterable]="true"
                                                                (filterChange)="handleFilterComboBox($event, textField, formControlName)"
                                                                [textField]="textField" [valueField]="valueField"
                                                                [placeholder]="placeholder" [valuePrimitive]="false"
                                                                (valueChange)="onPortfolioCompanyChange(section.get(formControlName)?.value, section)">
                                                            </kendo-combobox>
                                                        </div>

                                                    </div>
                                                </ng-template>
                                                <ng-template #multiSelectTemplate let-data="data"
                                                    let-placeholder="placeholder" let-list="list">
                                                    <div class="d-inline-block p-3">
                                                        <label class="Caption-M m-0 custom-report-label req-label">Sub
                                                            Sections</label>
                                                        <div>
                                                            <kendo-multiselect #multiselect   [checkboxes]="true" [virtual]="virtual"
                                                                [rounded]="'medium'" [fillMode]="'flat'"
                                                                formControlName="fieldStaticList"
                                                                [ngClass]="{'k-multiselect-search-80': section?.get('fieldStaticList')?.value?.length>0}"
                                                                [kendoDropDownFilter]="filterSettings"
                                                                name="fieldStaticList" 
                                                                [clearButton]="false"
                                                                class="k-multiselect-custom k-dropdown-width-300 k-multiselect-grey-chip"
                                                                [tagMapper]="tagMapper" [data]="list"
                                                                [textField]="'aliasName'"
                                                                (close)="clearSearch(multiselect)"
                                                                [valueField]="'fieldId'"
                                                                (valueChange)="getSelectedObjects(section?.get('fieldStaticList')?.value,list.length,'isCheckAll',i)"
                                                                [autoClose]="false" [placeholder]="placeholder">
                                                                <ng-template kendoMultiSelectHeaderTemplate>
                                                                    <div class="inline-container">
                                                                        <input type="checkbox"
                                                                            class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"
                                                                            kendoCheckBox formControlName="isCheckAll"
                                                                            [indeterminate]="isIndeterminate(section?.get('fieldStaticList')?.value,list)"
                                                                            (click)="onSelectAllCheckBoxClick(section?.get('isCheckAll')?.value,section?.get('fieldStaticList')?.value,list,i)" />
                                                                        <kendo-label for="chk">Select All</kendo-label>
                                                                    </div>
                                                                </ng-template>
                                                                <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                                                    <span class="TextTruncate pl-1 Body-R"
                                                                        [title]="dataItem.aliasName">{{
                                                                        dataItem.aliasName }}</span>
                                                                </ng-template>
                                                            </kendo-multiselect>
                                                        </div>
                                                    </div>
                                                </ng-template>
                                                <div class="d-inline-block" *ngSwitchCase="'CompanyName'">
                                                    <ng-container *ngTemplateOutlet="sectionTemplate"></ng-container>
                                                    <ng-container
                                                        *ngTemplateOutlet="comboBoxTemplate; context: {labelText: 'Alignment', formControlName: 'alignment', data: lpReportConfig.alignments, textField: 'alignment', valueField: 'alignmentId', placeholder: 'Select Alignment','required':true}"></ng-container>
                                                </div>
                                                <div class="d-inline-block" *ngSwitchCase="'CompanyLogo'">
                                                    <ng-container *ngTemplateOutlet="sectionTemplate"></ng-container>
                                                    <ng-container
                                                        *ngTemplateOutlet="comboBoxTemplate; context: {labelText: 'Alignment', formControlName: 'alignment', data: lpReportConfig.alignments, textField: 'alignment', valueField: 'alignmentId', placeholder: 'Select Alignment' ,'required':true}"></ng-container>
                                                </div>
                                                <div class="d-inline-block" *ngSwitchCase="'BusinessDescription'">
                                                    <ng-container *ngTemplateOutlet="sectionTemplate"></ng-container>
                                                </div>
                                                <div class="d-inline-block" *ngSwitchCase="'Static Information'">
                                                    <ng-container *ngTemplateOutlet="sectionTemplate"></ng-container>
                                                    <ng-container
                                                        *ngTemplateOutlet="multiSelectTemplate; context: {data: staticInformationList, placeholder: 'Select Fields', list: staticInformationList}"></ng-container>
                                                </div>
                                                <div class="d-inline-block" *ngSwitchCase="'Geographic Locations'">
                                                    <ng-container *ngTemplateOutlet="sectionTemplate"></ng-container>
                                                    <ng-container
                                                        *ngTemplateOutlet="multiSelectTemplate; context: {data: geographicLocationList, placeholder: 'Select Locations', list: geographicLocationList}"></ng-container>
                                                </div>
                                                <div class="d-inline-block" *ngSwitchCase="'Investment Professionals'">
                                                    <ng-container *ngTemplateOutlet="sectionTemplate"></ng-container>
                                                    <ng-container
                                                        *ngTemplateOutlet="multiSelectTemplate; context: {data: investmentProfessionalList, placeholder: 'Select Professionals', list: investmentProfessionalList}"></ng-container>
                                                </div>
                                                <div class="d-inline-block" *ngSwitchCase="'Commentary'">
                                                    <ng-container *ngTemplateOutlet="sectionTemplate"></ng-container>
                                                    <ng-container
                                                        *ngTemplateOutlet="multiSelectTemplate; context: {data: commentaryList, placeholder: 'Select Commentary', list: commentaryList}"></ng-container>
                                                    <ng-container
                                                        *ngTemplateOutlet="comboBoxTemplate; context: {labelText: 'Frequency', formControlName: 'commentaryFrequency', data: lpReportConfig.frequencyModel, textField: 'label', valueField: 'id', placeholder: 'Select Frequency','required':true}"></ng-container>
                                                </div>
                                                <div class="d-inline-block sdg-section mb-3" *ngSwitchCase="'SDG Logo'">
                                                    <ng-container *ngTemplateOutlet="sectionTemplate"></ng-container>
                                                    <ng-container
                                                        *ngTemplateOutlet="comboBoxTemplate; context: {labelText: 'Portfolio Company', formControlName: 'selectedCompany', data: lpReportConfig.companyModel, textField: 'companyName', valueField: 'companyId', placeholder: 'Select Portfolio Company','required':true}">
                                                    </ng-container>
                                                    <div class="d-inline-block p-3">
                                                        <label class="Caption-M m-0 custom-report-label req-label">Images</label>
                                                        <div>
                                                            <kendo-multiselect #multiselectimage [checkboxes]="true" [virtual]="virtual" [rounded]="'medium'"
                                                                [fillMode]="'flat'" formControlName="sdgLogoList"
                                                                [ngClass]="{'k-multiselect-search-80': section?.get('sdgLogoList')?.value?.length>0}"
                                                                [kendoDropDownFilter]="filterSettings" name="sdgLogoList" [clearButton]="false"
                                                                class="k-multiselect-custom k-dropdown-width-300 k-multiselect-grey-chip" [tagMapper]="tagMapper"
                                                                [data]="section?.get('sdgImageList')?.value" textField="name" (close)="clearSearch(multiselectimage)"
                                                                valueField="id"
                                                                (valueChange)="getSelectedObjects(section?.get('sdgLogoList')?.value,section?.get('sdgImageList')?.value?.length,'isCheckAllLogo',i); onChangeSDGImageList(section);"
                                                                [autoClose]="false" placeholder="Select Images">
                                                                <ng-template kendoMultiSelectHeaderTemplate>
                                                                    <div class="inline-container" *ngIf="section?.get('sdgImageList')?.value?.length > 0">
                                                                        <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo" kendoCheckBox
                                                                            formControlName="isCheckAllLogo"
                                                                            [indeterminate]="isIndeterminate(section?.get('sdgLogoList')?.value,section?.get('sdgImageList')?.value)"
                                                                            (click)="onSelectAllLogoCheckBoxClick(section?.get('isCheckAllLogo')?.value,section?.get('sdgLogoList')?.value,section?.get('sdgImageList')?.value,i); onChangeSDGImageList(section);" />
                                                                        <kendo-label for="chk">Select All</kendo-label>
                                                                    </div>
                                                                </ng-template>
                                                                <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                                                    <span class="TextTruncate pl-1 Body-R" [title]="dataItem.aliasName">{{
                                                                        dataItem.name }}</span>
                                                                </ng-template>
                                                            </kendo-multiselect>
                                                        </div>
                                                    </div>
                                                    <div
                                                        class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0 content-body-r-section panel-section sdg-panel">
                                                        <div class="row mr-3 ml-3 no-data-container" *ngIf="section?.get('sdgLogoOrderList')?.value?.length === 0">
                                                            <ng-container
                                                                *ngTemplateOutlet="noImageTemplate; context: { message: 'Select images from images dropdown and then drag and drop to reorder for LP Report' ,icon:'no-content-lp-report' }"></ng-container>
                                                        </div>
                                                        <div class="row mr-3 ml-3 sdg-image-container" *ngIf="section?.get('sdgLogoOrderList')?.value?.length > 0">
                                                            <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 image-drag-drop-header">
                                                                <span class="Caption-M">Drag and drop Images to reorder</span>
                                                            </div>
                                                            <div class="image-drag-group" cdkDropListGroup>
                                                                <div cdkDropList [cdkDropListData]="j" *ngFor="let image of section?.get('sdgLogoOrderList')?.value; let j = index;"
                                                                     cdkDragBoundary=".sdg-image-container" class="image-drag-drop-section">
                                                                    <div class="image-box" cdkDrag [cdkDragData]="j"
                                                                        (cdkDragEntered)="entered($event, section.get('sdgLogoOrderList').value, section.get('sdgLogoOrderList'))">
                                                                        <img src="assets/dist/images/cross-icon.svg" (click)="removeSDGImage(image?.id, section)" alt="No Content"
                                                                            class="remove-image-btn" id="remove-image-btn-{{j}}" />
                                                                        <img [src]="image?.url" [alt]="image?.file?.name" class="uploaded-image" id="uploaded-image-{{j}}" />
                                                                        <ng-template cdkDragPreview>
                                                                            <div class="image-box-preview" [style.height]="'145px !important'">
                                                                                <img [src]="image?.url" [alt]="image?.file?.name" class="uploaded-image-preview" [style.width]="'100%'" [style.height]="'100%'" />
                                                                            </div>
                                                                        </ng-template>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 mt-2">
                                                        <span class="sdg-image-note">Note : Images ordered here will appear as is on LP Report</span>
                                                    </div>
                                                </div>
                                                <div class="d-inline-block" *ngSwitchDefault>
                                                    <ng-container *ngTemplateOutlet="sectionTemplate"></ng-container>
                                                    <div class="d-inline-block p-3">
                                                        <label class="Caption-M m-0  req-label">KPI Name</label>
                                                        <div>
                                                            <kendo-multiselecttree #multiKpiSelect [rounded]="'medium'"
                                                                [checkableSettings]="checkableSettings"
                                                                class=" k-dropdown-width-300 k-multiselect-custom k-multiselect-custom-filter  k-multiselect-custom-tree k-multiselect-chip-100 k-multiselect-grey-chip"
                                                                [filterable]="true" [fillMode]="'flat'"
                                                                kendoMultiSelectTreeExpandable
                                                                [kendoMultiSelectTreeHierarchyBinding]="kpiLineItemList[section.get('name')?.value?.moduleId]"
                                                                name="fieldList" [clearButton]="false"
                                                                [tagMapper]="tagMapper" formControlName="fieldList"
                                                                [textField]="'text'" [checkAll]="true"
                                                                [valueField]="'id'" [autoClose]="false"
                                                                [expandOnFilter]="filterExpandSettings" 
                                                                [expandedKeys]="['0']" childrenField="items"
                                                                placeholder="Select Line Items">
                                                                <ng-template kendoMultiSelectTreeNodeTemplate
                                                                    let-dataItem>
                                                                    <span title="{{ dataItem.kpi }}"
                                                                        class="TextTruncate Body-R"> {{ dataItem.text
                                                                        }}</span>
                                                                </ng-template>
                                                            </kendo-multiselecttree>
                                                        </div>

                                                    </div>
                                                    <div class="d-inline-block p-3">
                                                        <label class="Caption-M m-0 req-label">Period</label>
                                                        <div>
                                                            <kendo-multiselecttree #multiKpiSelect [rounded]="'medium'"
                                                                [readonly]="section.get('name')?.value?.sectionId==investment_Colum"
                                                                [popupSettings]="{ popupClass: 'k-multiselect-group-tree' }"
                                                                (valueChange)="onSelectionChange(section.get('periodList')?.value,i)"
                                                                [checkableSettings]="checkableSettings"
                                                                class=" k-dropdown-width-300 k-multiselect-custom k-multiselect-custom-filter  k-multiselect-custom-tree k-multiselect-chip-100 k-multiselect-group-tree k-multiselect-grey-chip"
                                                                [filterable]="true" [fillMode]="'flat'"
                                                                kendoMultiSelectTreeExpandable name="periodList"
                                                                [kendoMultiSelectTreeHierarchyBinding]="kpiPeriodList[section.get('name')?.value?.moduleId]"
                                                                [clearButton]="false" [tagMapper]="tagMapper"
                                                                formControlName="periodList" [textField]="'text'"
                                                                [valueField]="'id'" [autoClose]="false"
                                                                [expandOnFilter]="filterExpandSettings"
                                                                [expandedKeys]="['0']" childrenField="items" 
                                                                placeholder="Select Period">
                                                                <ng-template kendoMultiSelectTreeNodeTemplate
                                                                    let-dataItem>
                                                                    <span title="{{ dataItem.kpi }}"
                                                                        class="TextTruncate Body-R"> {{ dataItem.text
                                                                        }}</span>
                                                                </ng-template>
                                                            </kendo-multiselecttree>
                                                        </div>
                                                    </div>
                                                    <!-- Use the template for Currency -->
                                                    <ng-container
                                                        *ngTemplateOutlet="comboBoxTemplate; context: {labelText: 'Currency', formControlName: 'currencyModel', data: lpReportConfig.currencies, textField: 'currencyCode', valueField: 'currencyId', placeholder: 'Select Currency','required':false,hidden:'currency-display-none'}"></ng-container>
                                                    <!-- Use the template for Unit Conversion -->
                                                    <ng-container
                                                        *ngTemplateOutlet="comboBoxTemplate; context: {labelText: 'Unit Conversion', formControlName: 'unitTypeModel', data: lpReportConfig.unitTypes, textField: 'unitType', valueField: 'unitTypeId', placeholder: 'Select Unit','required':false}"></ng-container>
                                                    <ng-container
                                                        *ngTemplateOutlet="singleSelectGroupTemplate; context: {labelText: 'Period Comparison', formControlName: 'periodComparison', data: periodComparisonList, textField: 'headerValue', valueField: 'lpReportMetricId', placeholder: 'Select Period','required':false}"></ng-container>
                                                    <ng-container *ngTemplateOutlet="dragTemplate; context: {labelText: 'Period Data Ordering', formControlName: 'dataOrder', placeholder: 'Select Order'}"></ng-container>
                                                    <ng-template #dragTemplate let-labelText="labelText" let-formControlName="formControlName" let-placeholder="placeholder">
                                                        <div class="d-inline-block p-3">
                                                            <label [ngClass]="required ? 'req-label':''" class="Caption-M m-0" [text]="">{{labelText}} <span *ngIf="formControlName"
                                                                    kendoTooltip [tooltipWidth]="350"
                                                                    [height]="'auto'" class="pl-1 custom-info"
                                                                    [title]="'Sequence selected by dragging line items in dropdown will appear as is on LP Report'">
                                                                    <img src="assets/dist/images/file-info.svg" alt="info-icon" class="info-icon">
                                                                </span></label>
                                                            <div class="k-order-button-focus">
                                                                <button kendoButton [attr.id]="'anchor-' + i" #anchor="kendoButton" (click)="toggle($event, i)" class="k-order-button clearfix">
                                                                    <span class="button-text">{{placeholder}}</span>
                                                                    <kendo-svg-icon class="button-icon" [size]="'small'" [icon]="chevronDown"></kendo-svg-icon>
                                                                </button>
                                                                <kendo-popup *ngIf="show && selectedSectionIndex === i" [anchor]="getAnchorElement(i)"
                                                                    (anchorViewportLeave)="toggle(false, i)" [animate]="false" [popupClass]="'k-data-order-popup'">
                                                                    <div cdkDropListGroup>
                                                                        <div cdkDropList [cdkDropListData]="section.get(formControlName)?.value" class="k-data-order-custom-popup data-order-list" (cdkDropListDropped)="onDataOrderDrop($event, i)" >
                                                                            <div *ngFor="let item of section.get(formControlName)?.value" cdkDrag  class="k-data-order-item" [cdkDragBoundary]="'.k-data-order-custom-popup'">
                                                                                <div class="drag-item-container">
                                                                                    <div class="drag-handle d-inline-block">
                                                                                        <img src="assets/dist/images/6dots.svg" alt="">
                                                                                    </div>
                                                                                    <div class="item-content d-inline-block">
                                                                                        {{item.label}}
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </kendo-popup>
                                                            </div>
                                                        </div>
                                                    </ng-template>
                                                    </div>
                                            </ng-container>
                                        </div>
                                        <div class="float-right close-section">
                                            <a href="javascript:void" (click)="removeSection(i)">
                                                <img src="assets/dist/images/close-drag.svg" alt="drag-icon"
                                                    class="drag-icon">
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </ng-container>
                            <div *ngIf="sections.length == 0" class="row mr-0 ml-0 no-data-container">
                                <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 no-content-section">
                                    <div class="text-center">
                                        <img src="assets/dist/images/no-content-lp-report.svg" alt="No Content"
                                            class="no-content-image">
                                    </div>
                                    <div class="text-center no-content-text pt-3 pb-2 Body-M">
                                        No template details found
                                    </div>
                                    <div class="text-center no-content-sub Caption-M">
                                        Select funds,portfolio companies from dropdowns and add new sections to begin
                                        creating LP Report template
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row  ml-4 mb-3 mt-3 mr-4">
                        <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                            <div class="float-right">
                                <button id="lp-report-reset" (click)="openConfirmPopUp()" [disabled]="sections.length == 0"
                                    kendoButton class="kendo-custom-button Body-R apply-btn mr-2" fillMode="outline"
                                    themeColor="primary">Reset</button>
                                <button id="lp-report-save" [disabled]="!form.valid || sections.length == 0 || templateName == '' || selectedCopyToFundList?.length == 0 || selectedCompanyList?.length == 0 || !isSaveEnabled" kendoButton
                                    class="kendo-custom-button Body-R apply-btn" themeColor="primary">Save</button>
                            </div>
                            <div class="float-left" *ngIf="this.id!=undefined">
                                <button id="lp-report-save-as" [disabled]="!form.valid"  (click)="openSaveAsPopUp()" [disabled]="sections.length == 0"
                                kendoButton class="kendo-custom-button Body-R apply-btn mr-2" fillMode="outline"
                                themeColor="primary">Save As</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<ng-template #noImageTemplate let-message="message" let-icon="icon">
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 no-content-section">
        <div class="text-center">
            <img src="assets/dist/images/{{icon}}.svg" alt="No Content" class="no-content-image">
        </div>
        <div class="text-center no-content-text pt-3 pb-2 Body-M no-comment-font">
            {{ message }}
        </div>
    </div>
</ng-template>
<confirm-modal [isCloseEnable]="true" (closeIconClick)="openConfirm = false" class="custom-confirm-model" *ngIf="openConfirm" [primaryButtonName]="'Confirm'" [secondaryButtonName]="'Cancel'"
    [modalTitle]="'Reset Changes'" (primaryButtonEvent)="onReset()" (secondaryButtonEvent)="openConfirm = false">
    <div class="modalBodyTextStyle">
        <div class="S-R">
            This will reset all section details. Are you sure you want
            to reset all changes?
        </div>
    </div>
</confirm-modal>
<confirm-modal [isCloseEnable]="true" (closeIconClick)="openConfirmApply = false" class="custom-confirm-model" *ngIf="openConfirmApply" [primaryButtonName]="'Confirm'" [secondaryButtonName]="'Cancel'"
    [modalTitle]="'Attention!'" (primaryButtonEvent)="applyConfig()" (secondaryButtonEvent)="openConfirmApply = false">
    <div class="modalBodyTextStyle">
        <div class="S-R">
            Making changes to Fund,Portfolio Company or Template Name details will reset the whole template and all changes made will be lost!
        </div>
    </div>
</confirm-modal>
<confirm-modal [isCloseEnable]="true" (closeIconClick)="openSaveAs = false;saveAsTemplateName = null" class="custom-confirm-model" *ngIf="openSaveAs" [primaryButtonName]="'Confirm'" [secondaryButtonName]="'Cancel'" [disablePrimaryButton]="saveAsTemplateName == '' ||  saveAsTemplateName == null || saveAsTemplateName.length == 0 || saveAsTemplateName?.length > 200"
    [modalTitle]="'Save as new template'" (primaryButtonEvent)="saveAsTemplate()" (secondaryButtonEvent)="openSaveAs = false;saveAsTemplateName = null" >
    <div class="modalBodyTextStyle">
        <div class="Body-R">
            <label class="Body-R mb-0 req-label save-as-label">Please enter name for new template below</label>
            <div class="filter-content">
            <kendo-textbox name="saveAsTemplateName" [placeholder]="'Template Name...'" 
                            [(ngModel)]="saveAsTemplateName" maxlength="200" fillMode="flat" [style.width.%]="100"
                             [clearButton]="true">
                        </kendo-textbox>
            </div>
        </div>
    </div>
</confirm-modal>
<app-loader-component *ngIf="isLoader"></app-loader-component>
<app-loader-component *ngIf="isSDGLoader"></app-loader-component>