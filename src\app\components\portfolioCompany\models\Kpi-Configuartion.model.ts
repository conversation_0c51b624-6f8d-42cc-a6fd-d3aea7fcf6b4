export class KpiConfig {
    public kpiType: string;
    public hasChart: boolean;
    public kpiConfigurationData: MSubFields[];
}

export class MSubFields{
    public sectionID : number;
    public fieldID : number;
    public aliasName : string;
    public subPageID : number;
    public options : string[];
    public chartValue : string[];
    public subFieldAliasName : string;
}

export class FinancialTypes{
    public tabID : number;
    public name :  string;
    public alias :  string;
    public active : boolean;
}