import {
  Component,
  ViewEncapsulation,
  Input,
  OnInit,
  OnDestroy,
  EventEmitter,
  Output,
  ViewChild,
} from "@angular/core";
import {
  FxRates,
  InvestorSections,
  MiscellaneousService,
} from "../../../services/miscellaneous.service";
import { ITab } from "projects/ng-neptune/src/lib/Tab/tab.model";
import { CurrencyService } from "src/app/services/currency.service";
import { DataAnalyticsService } from "src/app/services/data-analytics.service";
import { FilterService } from "src/app/services/filter.services";
import { AbstractFilterStrategy } from "src/app/components/reports/reports";
import { ToastrService } from "ngx-toastr";
import {
  FundEnum,
  Constants,
  DataAnalyticsConstants,
  GlobalConstants,
} from "src/app/common/constants";
import { MultiSelectTreeCheckableSettings } from "@progress/kendo-angular-dropdowns";
import { DropDownFilterSettings } from '@progress/kendo-angular-dropdowns';

import { Subscription } from "rxjs";
import { DealService } from "src/app/services/deal.service";
@Component({
  selector: "app-data-analytics-filter",
  templateUrl: "./data-analytics-filter.component.html",
  styleUrls: ["./data-analytics-filter.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class DataAnalyticsFilterComponent
  extends AbstractFilterStrategy
  implements OnInit, OnDestroy
{
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: "contains",
  };
  virtualMultiSelect: any = {
    itemHeight: 32,
    pageSize: 3100
  };
  collapsed = true;
  model: any = {};
  portfolioCompanyModel: any = {
    fundList: [],
    companyList: [],
    period: [],
    moduleList: [],
    kpiItems: [],
    currenceList: [],
    fxRates: [],
    isParentChildGraph: false,
  };
  fundModel: any = {
    fundList: [],
    moduleList: [],
    kpiItems: [],
    period: [],
  };
  dealModel: any = {
    fundList: [],
    companyList: [],
    period: [],
    moduleList: [],
    kpiItems: [],
  };
  investorModel: any = {
    fundList: [],
    investorList: [],
    companyList: [],
    period: [],
    investorSections: [],
  };
  portfolioCompanyList: any = {
    fundList: [],
    companyList: [],
    period: [],
    moduleList: [],
    kpiItems: [],
    currenceList: [],
    fxRates: [],
    investorSections: [],
  };
  ESGList: any = {
    fundList: [],
    companyList: [],
    period: [],
    moduleList: [],
    kpiItems: [],
    currenceList: [],
    fxRates: [],
    investorSections: [],
  };
  fundList: any = {
    fundList: [],
    moduleList: [],
    kpiItems: [],
    period: [],
  };
  dealList: any = {
    fundList: [],
    companyList: [],
    period: [],
    moduleList: [],
    kpiItems: [],
  };
  investorList: any = {
    fundList: [],
    investorList: [],
    companyList: [],
    period: [],
    investorSections: [],
  };
  ESGModel: any = {
    fundList: [],
    companyList: [],
    period: [],
    moduleList: [],
    kpiItems: [],
    currenceList: [],
    fxRates: [],
    isParentChildGraph: false,
  };
  FundEnum = FundEnum;
  yearRange: any = DataAnalyticsConstants.YearRange;
  dateRange: any[];
  @Output() onInputChanges: EventEmitter<any> = new EventEmitter();
  @Input() filterTabList: ITab[] = [];
  paginationFilterClone: any = {};
  selectedModule: any = DataAnalyticsConstants.Company;
  @Input() dataAnalyticsFilterdata: any = [];
  @Input() saveFiltersOptionsList: any = [];
  enabledPCControl: boolean = false;
  disabledControl: boolean = false;
  defaultControl: boolean = false;
  tabName: string = "";
  PortfolioCompany: string = DataAnalyticsConstants.PortfolioCompany;
  Fund: string = "Fund";
  Deal: string = "Deal";
  Investor: string = "Investor";
  ESG: string = "ESG";
  investorSectionsList: any = [];
  optionsList: any = [];
  dataAnalyticsFeatures: any[];
  SelectedReport: any = [];
  dataAnalyticsReportId = 999;
  confirmDelete: boolean = false;
  expandedKpiLineItems: any = [];
  typeMapping = {
    PCFunds: "fundList",
    PCCompany: "companyList",
    PCModule: "moduleList",
    FundsList: "fundList",
    FundModule: "moduleList",
    InvestorList: "investorList",
    InvestorFunds: "fundList",
    InvestorCompany: "companyList",
    DealModule: "moduleList",
    DealFunds: "fundList",
    DealCompany: "companyList",
    ESGModule: "moduleList",
    ESGFunds: "fundList",
    ESGCompany: "companyList",
  };
  typeMappingPC = {
    PCFunds: "fundList",
    PCCompany: "companyList",
    PCModule: "moduleList"
  };
  typeMappingFund = {
    FundsList: "fundList",
    FundModule: "moduleList",
  };
  typeMappingInvestor = {
    InvestorList: "investorList",
    InvestorFunds: "fundList",
    InvestorCompany: "companyList",
  };
  typeMappingDeal = {
    DealModule: "moduleList",
    DealFunds: "fundList",
    DealCompany: "companyList",
  };
  typeMappingESG = {
    ESGModule: "moduleList",
    ESGFunds: "fundList",
    ESGCompany: "companyList",
  };
  checkableSettings: MultiSelectTreeCheckableSettings = {
    checkChildren: false,
  };
  cloneCompanyList: any[];
  @ViewChild("myCalendar") myCalendar;
  virtual: any = {
    itemHeight: 36,
  };

  isPCFundChecked: boolean = false;
  isPCCompanyChecked: boolean = false;
  isPCModuleChecked: boolean = false;
  isFundFundChecked: boolean = false;
  isFundModuleChecked: boolean = false;
  isESGFundChecked: boolean = false;
  isESGCompanyChecked: boolean = false;
  isESGModuleChecked: boolean = false;
  isDealFundChecked: boolean = false;
  isDealCompanyChecked: boolean = false;
  isDealModuleChecked: boolean = false;
  isInvestorInvestorChecked: boolean = false;
  isInvestorFundChecked: boolean = false;
  isInvestorCompanyChecked: boolean = false;
  isInvestorListChecked: boolean = false;
  isKpiItemLoading: boolean = false;
  isEsgKpiItemLoading: boolean = false;
  isParentChildGraph: boolean = false;
  isEsgParentChildGraph: boolean = false;
  parentChildWarningNote: string = GlobalConstants.ParentChildWarningNote;
  parentChildError: string = GlobalConstants.ParentChildError;
  parentChildChkDisabled: string = GlobalConstants.ParentChildChkDisabled;
  chkPCParentChildGraphDisabled: boolean = false;
  chkEsgParentChildGraphDisabled: boolean = false;
  private filterDataSubscription: Subscription;
  constructor(
    private MiscellaneousService: MiscellaneousService,
    private toastrService: ToastrService,
    private filterService: FilterService,
    private _currencyService: CurrencyService,
    private dataAnalyticService: DataAnalyticsService,
    private _dealService: DealService,
  ) {
    super();
    this.getCurreny();
    this.GetAllFilterData();
    this.getESGModuleList();
    this.getFxRates();
    this.getInvestorsList();
    this.getInvestorSections();
  }

  ngOnInit(): void {
    if (
      this.filterTabList?.length > 0 &&
      this.tabName == "" &&
      this.dataAnalyticsFilterdata?.currentTabName == undefined
    ) {
      this.tabName = this.filterTabList[0]?.aliasname;
      this.model.currentTabName = this.tabName;
    }
    if (
      this.dataAnalyticsFilterdata != undefined &&
      this.dataAnalyticsFilterdata?.portfolioCompanyModel != undefined &&
      this.dataAnalyticsFilterdata?.portfolioCompanyModel != null
    ) {
      this.model = this.dataAnalyticsFilterdata;
      this.setFilterValue();
      this.setModelValue();
      this.tabName = this.model.currentTabName;
      this.activeTab();
    } else {
      this.resetObjects();
    }
    this.setSelectallValue();
    
   
  }

  ngOnDestroy(): void {
    if (this.filterDataSubscription) {
      this.filterDataSubscription.unsubscribe();
    }
  }

  /**
   * Clear all filters and reset to default state
   */
  clearAllFilters(): void {
    try {
      // Reset all models to their default state
      this.resetObjects();
      
      // Reset to default tab if needed
      if (!this.tabName) {
        this.tabName = this.PortfolioCompany; // Default tab
        this.model.currentTabName = this.PortfolioCompany;
        this.activeTab();
      }
      
      // Set unselected values to ensure proper initialization
      this.setUnSelectedValues();
      
      // Emit the changes
      this.onInputChanges.emit(this.model);
      
    } catch (error) {
      this.toastrService.error('Error populating portfolio company filters. Please try again.', 'Error', { positionClass: 'toast-center-center' });
    }
  }

  /**
   * Populate filters from visualization data body
   * @param data - The body data from handleVisualizationEditorOpening
   */
  populateFiltersFromVisualizationData(data: any): void {
    try {
      // Check if data.Url contains portfolio-company URL
      if (data && data.Url && data.Url.includes('portfolio-company')) {
        // Switch to portfolio company tab
        this.tabName = this.PortfolioCompany; // "PortfolioCompany"
        this.model.currentTabName = this.PortfolioCompany;
        
        // Update the active tab
        this.activeTab();
        
        // Populate portfolio company filters if data contains filter information
        this.populatePortfolioCompanyFilters(data.Body);
        
        // Emit the changes
        this.onInputChanges.emit(this.model);
      }
      
    } catch (error) {
      this.toastrService.error('Error populating portfolio company filters. Please try again.', 'Error', { positionClass: 'toast-center-center' });
    }
  }

  /**
   * Populate portfolio company filters from the incoming data
   * @param data - The data containing filter information
   */
  private async populatePortfolioCompanyFilters(data: any): Promise<void> {
    try {
      // Reset portfolio company model to ensure clean state
      this.portfolioCompanyModel = {
        fundList: [],
        companyList: [],
        period: [],
        moduleList: [],
        kpiItems: [],
        currenceList: null,
        fxRates: null,
        isParentChildGraph: false,
      };
      const dataObject=JSON.parse(data);
     this.portfolioCompanyModel.companyList = this.portfolioCompanyList.companyList.filter(x=>dataObject?.CompanyId?.includes(x.companyId));
     
    if (dataObject?.DataAnalyticsKpiFilter?.PageConfigFields?.length > 0){
      dataObject?.DataAnalyticsKpiFilter?.PageConfigFields?.forEach(element => {
        if (!dataObject?.ModuleIds?.includes(element.SubPageId)){
          dataObject?.ModuleIds?.push({moduleId:element.SubPageId,staticSection:true});
        }
      });
    }
    const newDataObject={...dataObject,ModuleIds:dataObject?.ModuleIds?.map(x=>{
      if(!x.moduleId && !x.staticSection){
        return {
          moduleId:x,
          staticSection:false
        }
      }
      else{
        return x;
      }
     
    })};
    
     this.portfolioCompanyModel.moduleList = this.portfolioCompanyList.moduleList.filter(x=>newDataObject?.ModuleIds?.some(y=>y.moduleId===x.moduleId && y.staticSection===x.staticSection));
     this.portfolioCompanyModel.fundList=this.portfolioCompanyList.fundList.filter(x=>newDataObject?.FundId?.includes(x?.fundID));
     this.GetKpisByModuleId(newDataObject);
     
     // Convert string dates to Date objects for the period range
     if (newDataObject?.StartDate && newDataObject?.EndDate) {
       const startYear = parseInt(newDataObject?.StartDate?.split('-')[0]);
       const endYear = parseInt(newDataObject?.EndDate?.split('-')[0]);
       
       // Create Date objects for the start and end of the year range
       const startDate = new Date(startYear, 0, 1); // January 1st of start year
       const endDate = new Date(endYear, 11, 31); // December 31st of end year
       
       this.portfolioCompanyModel.period = [startDate, endDate];
       this.model.portfolioCompanyModel.period = [startDate, endDate];
     } else {
       this.portfolioCompanyModel.period = [];
       this.model.portfolioCompanyModel.period = [];
     }
 
    // Filter fx rates from master list based on CurrencyRateSource
    const filteredFxRates = this.portfolioCompanyList.fxRates.filter(x => 
      x.type === newDataObject?.CurrencyRateSource
    );
    this.portfolioCompanyModel.fxRates = filteredFxRates.length > 0 ? filteredFxRates[0] : null;
    const filteredCurrenceList = this.portfolioCompanyList.currenceList.filter(x => 
      newDataObject?.ToCurrencyId===1?x.currencyID:null
    );
    this.portfolioCompanyModel.currenceList = filteredCurrenceList.length > 0 ? filteredCurrenceList[0] : null;
    // Set parent child graph before calling setUnSelectedValues
    if(newDataObject?.IsParentChildGraph){
      this.portfolioCompanyModel.isParentChildGraph = true;
      this.isParentChildGraph=true;
    }
    
      this.setUnSelectedValues();

      // Update the main model
      this.model.portfolioCompanyModel = this.portfolioCompanyModel;

     
      
      
    } catch (error) {
      this.toastrService.error('Error populating portfolio company filters. Please try again.', 'Error', { positionClass: 'toast-center-center' });
    }
  }
  setSelectallValue() {
    const checkListEquality = (modelA, modelB, propName) => {
      const lengthA = modelA?.[propName]?.length ?? 0;
      const lengthB = modelB?.[propName]?.length ?? 0;
      return lengthA > 0 && lengthB > 0 && lengthA === lengthB;
    };
    this.isPCFundChecked = checkListEquality(this.portfolioCompanyModel, this.portfolioCompanyList, 'fundList');
    this.isPCCompanyChecked = checkListEquality(this.portfolioCompanyModel, this.portfolioCompanyList, 'companyList');
    this.isPCModuleChecked = checkListEquality(this.portfolioCompanyModel, this.portfolioCompanyList, 'moduleList');
    this.isFundFundChecked = checkListEquality(this.fundModel, this.fundList, 'fundList');
    this.isFundModuleChecked = checkListEquality(this.fundModel, this.fundList, 'moduleList');
    this.isESGFundChecked = checkListEquality(this.ESGModel, this.ESGList, 'fundList');
    this.isESGCompanyChecked = checkListEquality(this.ESGModel, this.ESGList, 'companyList');
    this.isESGModuleChecked = checkListEquality(this.ESGModel, this.ESGList, 'moduleList');
    this.isDealFundChecked = checkListEquality(this.dealModel, this.dealList, 'fundList');
    this.isDealCompanyChecked = checkListEquality(this.dealModel, this.dealList, 'companyList');
    this.isDealModuleChecked = checkListEquality(this.dealModel, this.dealList, 'moduleList');
    this.isInvestorInvestorChecked = checkListEquality(this.investorModel, this.investorList, 'investorList');
    this.isInvestorFundChecked = checkListEquality(this.investorModel, this.investorList, 'fundList');
    this.isInvestorCompanyChecked = checkListEquality(this.investorModel, this.investorList, 'companyList');
    this.isInvestorListChecked = checkListEquality(this.investorModel, this.investorList, 'investorList');
    this.isParentChildGraph = this.portfolioCompanyModel.isParentChildGraph;
    this.isEsgParentChildGraph = this.ESGModel?.isParentChildGraph;
  }
  activeTab() {
    this.filterTabList = this.filterTabList.map((item) => {
      if (item.aliasname === this.tabName) {
        return { ...item, active: true };
      } else {
        return { ...item, active: false };
      }
    });
  }
  setModelValue() {
    if (
      this.portfolioCompanyModel?.moduleList?.length > 0 &&
      this.portfolioCompanyModel?.companyList?.length > 0
    ) {
      this.GetKpisByModuleId();
    }
    if (
      this.dealModel?.moduleList?.length > 0 &&
      this.dealModel?.companyList?.length > 0
    ) {
      this.GetDealKpisByModuleId();
    }
    if (this.fundModel?.moduleList?.length > 0) {
      this.GetFundKpisByModuleId();
    }
    if (
      this.ESGModel?.moduleList?.length > 0 &&
      this.ESGModel?.companyList?.length > 0
    ) {
      this.GetKpisByESGModuleId();
    }
    this.setInvestorModelValue();
    if (
      this.model.SelectedReport != null &&
      this.model.SelectedReport != undefined
    ) {
      this.SelectedReport = this.model.SelectedReport;
      this.OnFiltersSelected();
    } else {
      this.SelectedReport = [];
    }
    this.setCurrentTabName();
  }
  setCurrentTabName() {
    if (
      this.model.currentTabName != undefined &&
      this.model.currentTabName != "" &&
      this.filterTabList?.length > 0
    ) {
      this.filterTabList.forEach((element) => {
        if (element.name == this.model.currentTabName) element.active = true;
        else element.active = false;
      });
    }
  }
  setInvestorModelValue() {
    if (this.investorModel.investorList.length > 0) {
      this.GetFundsByInvestor("reset");

      setTimeout(() => {
        if (this.investorModel.fundList.length > 0)
          this.GetCompanies("InvestorCompany", "reset");
      }, 300);
    } else {
      this.investorList.fundList = [];
      this.investorModel.fundList = [];
    }
  }
  resetObjects() {
    this.model = [];
    this.model.currentTabName = this.tabName;
    this.model.portfolioCompanyModel = this.portfolioCompanyModel;
    this.model.ESGModel = this.ESGModel;
    this.model.dealModel = this.dealModel;
    this.model.fundModel = this.fundModel;
    this.model.investorModel = this.investorModel;
    this.portfolioCompanyModel.period = null;
    this.portfolioCompanyModel.isParentChildGraph = false;
    this.ESGModel.period = null;
    this.fundModel.period = null;
    this.dealModel.period = null;
    this.investorModel.period = null;
  }
  setPortfolioCompanyModel() {
    if (this.dataAnalyticsFilterdata.portfolioCompanyModel) {
      this.portfolioCompanyModel =
        this.dataAnalyticsFilterdata.portfolioCompanyModel;
      if (
        this.portfolioCompanyModel.kpiItems.length > 0 &&
        this.portfolioCompanyList.kpiItems.length === 0
      ) {
        this.portfolioCompanyList.kpiItems =
          this.portfolioCompanyModel.kpiItems.map((item, index) => {
            return { ...item, id: index + 1 };
          });
      }
      Object.keys(this.typeMappingPC).forEach((type) => {
        if (this.portfolioCompanyModel[this.typeMappingPC[type]]?.length > 0) {
          this.setUnSelectedValue(type, this.typeMappingPC, "PC");
        }
      });
    }
    this.setSelectallValue();
  }

  setUnSelectedValue(type: string, typeMapping, tabName: string) {
    if (!typeMapping[type]) return;

    // Simplify idProperty assignment using a mapping object
    const idPropertyMapping = {
      PCFunds: "fundID",
      FundsList: "fundID",
      ESGFunds: "fundID",
      DealFunds: "fundID",
      InvestorFunds: "fundID",
      PCCompany: "companyId",
      ESGCompany: "companyId",
      DealCompany: "companyId",
      InvestorCompany: "companyId",
      PCModule: "id",
      ESGModule: "id",
      DealModule: "moduleId",
      FundModule: "moduleId",
      InvestorList: "investorId",
    };
    const idProperty = Object.keys(idPropertyMapping).find((key) =>
      type.includes(key)
    )
      ? idPropertyMapping[type]
      : "";
    // Abstract the repetitive logic into a single function
    const updateCheckStatus = (list, model) => {
      list.forEach((item) => {
        const isAvailable = model.some(
          (x) => x[idProperty] === item[idProperty]
        );
        item.isChecked = isAvailable;
      });
    };
    // Use a mapping object for models to simplify the conditional logic
    const modelMapping = {
      PC: () =>
        updateCheckStatus(
          this.portfolioCompanyList[typeMapping[type]],
          this.portfolioCompanyModel[typeMapping[type]]
        ),
      Deal: () =>
        updateCheckStatus(
          this.dealList[typeMapping[type]],
          this.dealModel[typeMapping[type]]
        ),
      Fund: () =>
        updateCheckStatus(
          this.fundList[typeMapping[type]],
          this.fundModel[typeMapping[type]]
        ),
      ESG: () =>
        updateCheckStatus(
          this.ESGList[typeMapping[type]],
          this.ESGModel[typeMapping[type]]
        ),
      Investor: () =>
        updateCheckStatus(
          this.investorList[typeMapping[type]],
          this.investorModel[typeMapping[type]]
        ),
    };
    // Execute the function based on tabName
    if (modelMapping[tabName]) {
      modelMapping[tabName]();
    }
  }
  setESGModel() {
    if (this.dataAnalyticsFilterdata.ESGModel) {
      this.ESGModel = this.dataAnalyticsFilterdata.ESGModel;
      if (
        this.ESGModel.kpiItems.length > 0 &&
        this.ESGList.kpiItems.length == 0
      ) {
        let idCounter = 1;
        this.ESGModel.kpiItems = this.ESGModel.kpiItems.map((item, index) => {
          return { ...item };
        });
        this.ESGList.kpiItems = this.ESGModel.moduleList
          .map((element) => {
            const filterItems = this.ESGModel.kpiItems.filter(
              (item) => item.moduleId === element.subPageId && item.parentKpiId === 0
            );
            
            let filteredChieldItems = this.ESGModel.kpiItems.map((item) => {
              return { ...item, id: idCounter++ };
            }).filter((item) => item.moduleId === element.subPageId && item.parentKpiId !== 0);
            if (filterItems.length) {
              filterItems.forEach((element) => {
                element.items = filteredChieldItems.filter((item) => item.parentKpiId === element.kpiId);
              });
              return this.returnKpiItem(element, filterItems, idCounter++);
            }
          })
          .filter(Boolean);
      }

      Object.keys(this.typeMappingESG).forEach((type) => {
        if (this.ESGModel[this.typeMappingESG[type]]?.length > 0) {
          this.setUnSelectedValue(type, this.typeMappingESG, "ESG");
        }
      });
    }
    this.setSelectallValue();
  }

  setFundModel() {
    if (this.dataAnalyticsFilterdata.fundModel) {
      this.fundModel = this.dataAnalyticsFilterdata.fundModel;
      Object.keys(this.typeMappingFund).forEach((type) => {
        if (this.fundModel[this.typeMappingFund[type]]?.length > 0) {
          this.setUnSelectedValue(type, this.typeMappingFund, "Fund");
        }
      });
    }
    this.setSelectallValue();
  }

  setDealModel() {
    if (this.dataAnalyticsFilterdata.dealModel) {
      this.dealModel = this.dataAnalyticsFilterdata.dealModel;
      if (
        this.dealModel?.kpiItems.length > 0 &&
        this.dealList?.kpiItems.length == 0
      ) {
        this.dealList.kpiItems = this.dealModel.kpiItems.map((item, index) => {
          return { ...item, id: index + 1 };
        });
      }
      Object.keys(this.typeMappingDeal).forEach((type) => {
        if (this.dealModel[this.typeMappingDeal[type]]?.length > 0) {
          this.setUnSelectedValue(type, this.typeMappingDeal, "Deal");
        }
      });
    }
    this.setSelectallValue();
  }

  setInvestorModel() {
    if (this.dataAnalyticsFilterdata.investorModel) {
      this.investorModel = this.dataAnalyticsFilterdata.investorModel;
      Object.keys(this.typeMappingInvestor).forEach((type) => {
        if (this.investorModel[this.typeMappingInvestor[type]]?.length > 0) {
          this.setUnSelectedValue(type, this.typeMappingInvestor, "Investor");
        }
      });
    }
    this.setSelectallValue();
  }
  setFilterValue() {
    this.setPortfolioCompanyModel();
    this.setESGModel();
    this.setFundModel();
    this.setDealModel();
    this.setInvestorModel();
    this.setSelectallValue();
  }
isIndeterminate(control: string) {
  const controlMapping = {
    "PCCompany": { model: this.portfolioCompanyModel.companyList, list: this.portfolioCompanyList.companyList },
    "PCFunds": { model: this.portfolioCompanyModel.fundList, list: this.portfolioCompanyList.fundList },
    "PCModule": { model: this.portfolioCompanyModel.moduleList, list: this.portfolioCompanyList.moduleList },
    "DealFunds": { model: this.dealModel.fundList, list: this.dealList.fundList },
    "DealCompany": { model: this.dealModel.companyList, list: this.dealList.companyList },
    "DealModule": { model: this.dealModel.moduleList, list: this.dealList.moduleList },
    "FundsList": { model: this.fundModel.fundList, list: this.fundList.fundList },
    "FundModule": { model: this.fundModel.moduleList, list: this.fundList.moduleList },
    "InvestorList": { model: this.investorModel.investorList, list: this.investorList.investorList },
    "InvestorFunds": { model: this.investorModel.fundList, list: this.investorList.fundList },
    "InvestorCompany": { model: this.investorModel.companyList, list: this.investorList.companyList },
    "ESGFunds": { model: this.ESGModel.fundList, list: this.ESGList.fundList },
    "ESGCompany": { model: this.ESGModel.companyList, list: this.ESGList.companyList },
    "ESGModule": { model: this.ESGModel.moduleList, list: this.ESGList.moduleList }
  };

  const { model, list } = controlMapping[control] || {};
  return model && list && model.length !== 0 && model.length !== list.length;
}

  clearSearch($event: any) {
    $event.clearFilter();
    $event.filterChange.emit('');
}
  getCurreny() {
    this._currencyService.getAllCurrencies().subscribe({
      next: (result) => {
        let resp = result;
        if (resp != undefined && resp.code == "OK" && resp.body.length > 0) {
          let filterCurrencyList = result.body.filter(
            (x) => x.currencyCode == "USD");
          if (filterCurrencyList.length > 0) {
            this.portfolioCompanyList.currenceList = filterCurrencyList;
          }
        }
      },
      error(err) {},
    });
  }
  getInvestorsList() {
    this.dataAnalyticService.getInvestorsList().subscribe({
      next: (result) => {
        let resp = result;
        if (resp != undefined) {
          this.investorList.investorList = resp;
        }
      },
      error(err) {},
    });
  }
  getFxRates() {
    this.portfolioCompanyList.fxRates = [
      {
        id: FxRates.SystemAPI,
        type: FxRates[FxRates.SystemAPI],
      },
      {
        id: FxRates.BulkUpload,
        type: FxRates[FxRates.BulkUpload],
      },
    ];
  }
  createSection(id: string, type: string, aliasName: string) {
    return { id, type, aliasName };
  }

  getInvestorSections() {
    const initialSections = [
      this.createSection(
        InvestorSections.InvestorFundDetails,
        InvestorSections.InvestorFundDetails,
        InvestorSections.InvestorFundDetails
      ),
      this.createSection(
        InvestorSections.InvestorCompanyDetails,
        InvestorSections.InvestorCompanyDetails,
        InvestorSections.InvestorCompanyDetails
      ),
    ];

    const additionalSections =
      this.investorSectionsList?.length > 0
        ? this.investorSectionsList.map((element) =>
            this.createSection(
              element.investorSectionName,
              element.investorSectionName,
              element.aliasName
            )
          )
        : [];

    this.investorList.investorSections = [
      ...initialSections,
      ...additionalSections,
    ].sort((a, b) => a.id.localeCompare(b.id));
  }
  onTabClick(tab: ITab) {
    if (tab) {
      this.tabName = tab.aliasname;
      this.model.currentTabName = tab.aliasname;
      this.onInputChanges.emit(this.model);
    }
  }

  GetAllFilterData() {
    this.dataAnalyticService.GetAllFilterData().subscribe({
      next: (result) => {
        if (result != null) {
          this.assignModuleLists(result);
          this.assignFundLists(result);
          this.assignCompanyLists(result);
          this.dataAnalyticsFeatures = result?.dataAnalyticsFeatures;
          this.investorSectionsList = result?.investorsSections;
          this.getInvestorSections();
          this.checkAndResetCompanies();
          this.setUnSelectedValues();
           // Subscribe to filter data from visualization editor
    this.filterDataSubscription = this.dataAnalyticService.filterData$.subscribe(data => {
      if (data) {
        this.populateFiltersFromVisualizationData(data);
      } else {
        // Clear filters when null data is received (indicates filter reset)
        this.clearAllFilters();
      }
    });
        }
      },
      error: (error) => {
        // Handle error
      },
    });
  }
  assignModuleLists(result) {
    this.portfolioCompanyList.moduleList = this.processModuleList(
      result?.modules?.filter((x) => x.sectionName != Constants.DealTrackRecord)
    );
    this.fundList.moduleList = this.processModuleList(result?.fundModules);
    this.ESGList.moduleList = this.processModuleList(this.ESGList.moduleList);
    this.dealList.moduleList = this.processModuleList(
      result?.modules?.filter(
        (x) =>
          x.staticSection && x.sectionName != Constants.InvestmentProfessionals
      )
    );
  }

  processModuleList(moduleList) {
    return moduleList.map((item, index) => ({ ...item, id: index + 1 }));
  }

  assignFundLists(result) {
    this.portfolioCompanyList.fundList = JSON.parse(
      JSON.stringify(result?.funds)
    );
    this.ESGList.fundList = JSON.parse(JSON.stringify(result?.funds));
    this.dealList.fundList = JSON.parse(JSON.stringify(result?.funds));
    this.fundList.fundList = JSON.parse(JSON.stringify(result?.funds));
  }

  assignCompanyLists(result) {
    this.portfolioCompanyList.companyList = JSON.parse(JSON.stringify(result?.companies));
    this.ESGList.companyList = JSON.parse(JSON.stringify(result?.companies));
    this.cloneCompanyList = JSON.parse(JSON.stringify(result?.companies));
    this.dealList.companyList = JSON.parse(JSON.stringify(result?.companies));
  }

  checkAndResetCompanies() {
    if (this.portfolioCompanyModel?.fundList?.length > 0) {
      this.GetCompanies("companyList", "reset");
    }
    if (this.ESGModel?.fundList?.length > 0) {
      this.GetCompanies("companyList", "reset");
    }
    if (this.dealModel?.fundList?.length > 0) {
      this.GetCompanies("companyList", "reset");
    }
    if (this.investorModel?.fundList?.length > 0) {
      this.GetCompanies("companyList", "reset");
    }
  }
  setUnSelectedValues() {
    const mappings = [
      {
        model: this.portfolioCompanyModel,
        mapping: this.typeMappingPC,
        label: "PC",
      },
      { model: this.fundModel, mapping: this.typeMappingFund, label: "Fund" },
      { model: this.dealModel, mapping: this.typeMappingDeal, label: "Deal" },
      {
        model: this.investorModel,
        mapping: this.typeMappingInvestor,
        label: "Investor",
      },
      { model: this.ESGModel, mapping: this.typeMappingESG, label: "ESG" },
    ];

    mappings.forEach(({ model, mapping, label }) => {
      Object.keys(mapping).forEach((type) => {
        if (model[mapping[type]]?.length > 0) {
          this.setUnSelectedValue(type, mapping, label);
        }
      });
    });
  }
  filterFunds(module: string) {
    const moduleMapping = {
      PCCompany: this.portfolioCompanyModel.fundList,
      DealCompany: this.dealModel.fundList,
      InvestorCompany: this.investorModel.fundList,
      ESGCompany: this.ESGModel.fundList,
    };

    const selectedFundList = moduleMapping[module];

    if (!selectedFundList) {
      return "";
    }

    return selectedFundList.map((fund) => fund.fundID).toString();
  }
  setCompanies(result: any, module: string, type: string) {
    const mappings = {
      PCCompany: {
        list: this.portfolioCompanyList,
        model: this.portfolioCompanyModel,
      },
      DealCompany: { list: this.dealList, model: this.dealModel },
      InvestorCompany: { list: this.investorList, model: this.investorModel },
      ESGCompany: { list: this.ESGList, model: this.ESGModel },
    };
    const { list, model } = mappings[module] || {};
    if (list && model) {
      list.companyList = JSON.parse(JSON.stringify(result));
      if (type !== "reset") {
        model.companyList = [];
      }
    } 
  }
  getESGModuleList() {
    this.MiscellaneousService.getMethodologiesByGroup().subscribe({
      next: (result) => {
        if (result != null) {
          const moduleList = result.groupModel.filter((x) => x.name == "ESG")[0]
            .items;
          this.ESGList.moduleList = moduleList.map((item, index) => ({
            ...item,
            id: index + 1,
          }));
        }
      },
      error: (_error) => {},
    });
  }
  GetCompanies(module: string, type: string) {
    let filter = {
      FundIds:
        this.filterFunds(module).length != 0 ? this.filterFunds(module) : null,
    };
    this.dataAnalyticService.GetCompanies(filter).subscribe({
      next: (result) => {
        if (result != null) {
          this.setCompanies(result, module, type);
        }
      },
      error: (error) => {},
    });
  }
  GetFundsByInvestor(type: string) {
    let investorId = this.investorModel.investorList
      ?.map(function (investor) {
        return investor.investorId;
      })
      .toString();
    this.dataAnalyticService.GetFundsByInvestor([investorId]).subscribe({
      next: (result) => {
        if (result != null) {
          this.investorList.fundList = result;
          if (type != "reset")
            this.investorModel.fundList = [];
        }
      },
      error: (error) => {},
    });
  }
  GetKpisByModuleId(dataObject:any=null) {
    let CompanyIds = this.portfolioCompanyModel.companyList?.map(function (
      company
    ) {
      return company.companyId;
    });
    let ModuleIds = this.portfolioCompanyModel.moduleList
      ?.filter((module) => !module.staticSection)
      .map(function (module) {
        return module.moduleId;
      });
    const subPageIds = this.portfolioCompanyModel.moduleList
      ?.filter((module) => module.staticSection)
      .map(function (module) {
        return module.moduleId;
      });
    if (
      CompanyIds.length > 0 &&
      (ModuleIds.length > 0 || subPageIds.length > 0)
    ) {
      let filter = {
        CompanyIds: CompanyIds.toString(),
        ModuleIds: ModuleIds.toString(),
        SubPageIds: subPageIds.toString(),
      };
      this.dataAnalyticService.GetKpisByModuleId(filter).subscribe({
        next: (result) => {
          if (result?.length > 0) {
            result = result.map((item, index) => {
              return { ...item, id: index + 1 };
            });
            this.getKpiItemList(result,dataObject);
          } else if(result?.length == 0){
            this.getKpiItemList(result);
          }
          this.isKpiItemLoading = false;
        },
        error: (error) => {},
      });
    }
  }
  GetKpisByESGModuleId() {
    let CompanyIds = this.ESGModel.companyList?.map(function (company) {
      return company.companyId;
    });
    let ModuleIds = this.ESGModel.moduleList?.map(function (module) {
      return module.subPageId;
    });
    if (CompanyIds.length > 0 && ModuleIds.length > 0) {
      let filter = {
        CompanyIds: CompanyIds.toString(),
        ModuleIds: ModuleIds.toString(),
        SubPageIds: "",
      };
      let idCounter = 1;
      this.dataAnalyticService.GetESGKpisByModuleId(filter).subscribe({
        next: (result) => {
          if (result && result.length) {
            const expandedKpiLineItems = [];
            result = result.map((item, index) => {
              return { ...item, };
            });
            this.ESGList.kpiItems = this.ESGModel.moduleList
              .map((element) => {
                let filterItems = result.map((item) => {
                  return { ...item, id: idCounter++ };
                }).filter((item) => item.moduleId === element.subPageId && item.parentKpiId === 0);
                let filteredChieldItems = result.map((item) => {
                  return { ...item, id: idCounter++ };
                }).filter((item) => item.moduleId === element.subPageId && item.parentKpiId !== 0);
                if (filterItems.length) {
                  filterItems.forEach((element) => {
                    element.items = filteredChieldItems.filter((item) => item.parentKpiId === element.kpiId);
                  });
                  return this.returnKpiItem(element, filterItems, idCounter++);
                }
              })
              .filter(Boolean);
            this.ESGList.kpiItems.forEach((element, index) => {
              expandedKpiLineItems.push(index);
            });
            this.expandedKpiLineItems = expandedKpiLineItems.join(",");
          }
          this.isEsgKpiItemLoading = false;
        },
        error: (error) => {},
      });
    }
  }

  GetDealKpisByModuleId() {
    let CompanyIds = this.dealModel.companyList?.map(function (company) {
      return company.companyId;
    });
    let ModuleIds = [];
    const subPageIds = this.dealModel.moduleList
      ?.filter((module) => module.staticSection)
      .map(function (module) {
        return module.moduleId;
      });
    if (
      CompanyIds.length > 0 &&
      (ModuleIds.length > 0 || subPageIds.length > 0)
    ) {
      let filter = {
        CompanyIds: CompanyIds.toString(),
        ModuleIds: ModuleIds.toString(),
        SubPageIds: subPageIds.toString(),
      };
      this.dataAnalyticService.GetKpisByModuleId(filter).subscribe({
        next: (result) => {
          if (result != null && result.length > 0) {
            result = result.map((item, index) => {
              return { ...item, id: index + 1 };
            });
            this.getDealKpiItemList(result);
          }
        },
        error: (error) => {},
      });
    }
  }
  GetFundKpisByModuleId() {
    let ModuleIds = [];
    const subPageIds = this.fundModel.moduleList
      ?.filter((module) => module.staticSection)
      .map(function (module) {
        return module.moduleId;
      });
    if (ModuleIds.length > 0 || subPageIds.length > 0) {
      let filter = {
        CompanyIds: "",
        ModuleIds: ModuleIds.toString(),
        SubPageIds: subPageIds.toString(),
      };
      this.dataAnalyticService.GetKpisByModuleId(filter).subscribe({
        next: (result) => {
          if (result != null && result.length > 0) {
            result = result.map((item, index) => {
              return { ...item, id: index + 1 };
            });
            this.getFundKpiItemList(result);
          }
        },
        error: (error) => {},
      });
    }
  }
  getFundKpiItemList(result: any,dataObject:any=null) {
    const expandedKpiLineItems = [];
    let expandedId = 0;
    let idCounter = 1;
    this.fundList.kpiItems = [];

    this.fundModel.moduleList.forEach((module, index) => {
      const isStaticSection = module.staticSection;
      const filterCondition = (item) =>
        item.moduleId === module.moduleId &&
        ((isStaticSection &&
          item.moduleName === DataAnalyticsConstants.PageConfig &&
          item.kpiFieldName !== DataAnalyticsConstants.CompanyName &&
          item.kpiFieldName !== DataAnalyticsConstants.FundName) ||
          (!isStaticSection &&
            item.moduleName !== DataAnalyticsConstants.PageConfig));
      const filteredItems = result
        ?.filter(filterCondition)
        .map((item) => ({ ...item, id: idCounter++ }));
      if (filteredItems.length > 0) {
        this.fundList.kpiItems.push(
          this.returnKpiItem(module, filteredItems, idCounter++)
        );
        expandedKpiLineItems.push(expandedId++);
      }
    });
    this.expandedKpiLineItems = expandedKpiLineItems.join(",");
  }
  getDealKpiItemList(result: any) {
    const expandedKpiLineItems = [];
    let expandedId = 0;
    this.dealList.kpiItems = [];
    this.dealModel.moduleList.forEach((element, index) => {
      let filterItems = [];
      if (!element.staticSection)
        filterItems = result?.filter(
          (item) =>
            item.moduleId === element.moduleId &&
            item.moduleName !== DataAnalyticsConstants.PageConfig
        );
      if (element.staticSection)
        filterItems = result?.filter(
          (item) =>
            item.moduleId === element.moduleId &&
            item.moduleName === DataAnalyticsConstants.PageConfig &&
            item.kpiFieldName != DataAnalyticsConstants.CompanyName &&
            item.kpiFieldName != DataAnalyticsConstants.FundName
        );
      if (filterItems.length > 0) {
        this.dealList.kpiItems.push(
          this.returnKpiItem(element, filterItems, index)
        );
        expandedKpiLineItems.push(expandedId++);
      }
    });
    this.expandedKpiLineItems = expandedKpiLineItems.join(",");
  }

  getKpiItemList(result: any,dataObject:any=null) {
    const expandedKpiLineItems = [];
    let expandedId = 0;
    this.portfolioCompanyList.kpiItems = [];
    let idCounter = 1;   
    // Get kpiIds once before the loop
    let kpiIds = [];
    if (dataObject && dataObject.DataAnalyticsKpiFilter) {
      this.portfolioCompanyModel.kpiItems = [];
      kpiIds = Object.values(dataObject.DataAnalyticsKpiFilter).flatMap((x: Number[]) => x.length > 0 ? x : []);
      
    }
    
    this.portfolioCompanyModel.moduleList.forEach((module, index) => {
      const isStaticSection = module.staticSection;
      const filterCondition = (item) =>
        item.moduleId === module.moduleId &&
        ((isStaticSection &&
          item.moduleName === DataAnalyticsConstants.PageConfig &&
          item.kpiFieldName !== DataAnalyticsConstants.CompanyName &&
          item.kpiFieldName !== DataAnalyticsConstants.FundName) ||
          (!isStaticSection &&
            item.moduleName !== DataAnalyticsConstants.PageConfig)) && item.parentKpiId === 0;
        const filterConditionForChild = (item) =>
              item.moduleId === module.moduleId &&
              ((isStaticSection &&
                item.moduleName === DataAnalyticsConstants.PageConfig &&
                item.kpiFieldName !== DataAnalyticsConstants.CompanyName &&
                item.kpiFieldName !== DataAnalyticsConstants.FundName) ||
                (!isStaticSection &&
                  item.moduleName !== DataAnalyticsConstants.PageConfig)) && item.parentKpiId !== 0;
      let filteredItems = result?.filter(filterCondition).map((item) => ({ ...item, id: idCounter++ }));
      let filteredChieldItems = result?.filter(filterConditionForChild).map((item) => ({ ...item, id: idCounter++ }));
      filteredItems.forEach((element) => {
        element.items = filteredChieldItems.filter((item) => item.parentKpiId === element.kpiId);
      });
      if (filteredItems.length > 0) {
        this.portfolioCompanyList.kpiItems.push(
          this.returnPCKpiItem(module, filteredItems, index, idCounter++)
        );
        
        // Accumulate kpiItems across all modules
        if (dataObject && dataObject.DataAnalyticsKpiFilter && kpiIds.length > 0) {
          // Filter based on dataObject KPI IDs
          const moduleKpiItems = filteredItems.filter(x=>kpiIds.includes(x.kpiId)||kpiIds.includes(x.items.forEach(y=>y.kpiId)));
          this.portfolioCompanyModel.kpiItems.push(...moduleKpiItems);
          const moduleKpiItemsForChild=filteredChieldItems.filter(x=>kpiIds.includes(x.kpiId));
          this.portfolioCompanyModel.kpiItems.push(...moduleKpiItemsForChild);
        }
        
        expandedKpiLineItems.push(expandedId++);
      }
    });
    this.expandedKpiLineItems = expandedKpiLineItems.join(",");
  }

  returnPCKpiItem(element: any, filterItems: any, index, id) {
    return {
      moduleId: element.moduleId,
      isModuleHeader: true,
      kpi: element.aliasName,
      kpiDisplayName: element.aliasName,
      items: filterItems,
      id: id,
    };
  }
  returnKpiItem(element: any, filterItems: any, id: any) {
    return {
      moduleId: element.moduleId,
      isModuleHeader: true,
      kpiDisplayName: element.aliasName || element.pageConfigAliasName || element.name,
      kpi: element.aliasName || element.pageConfigAliasName || element.name,
      moduleName: element.aliasName || element.pageConfigAliasName || element.name,
      id: id,
      items: filterItems,
    };
  }
  private countKpiItems(items: any[]): number {
    let count = 0;
    items.forEach(x => {
      count++;
      if (x.items) {
        count += this.countKpiItems(x.items);
      }
    });
    return count;
  }
  switchForPC(event: any, control: string) {
    switch (control) {
      case "PCCompany":
        this.portfolioCompanyModel.companyList = event;
        this.model.portfolioCompanyModel.companyList = event;
        if (
          this.model.portfolioCompanyModel.companyList.length > 0 &&
          this.model.portfolioCompanyModel.moduleList.length > 0
        ) {
          this.isKpiItemLoading = true;
          this.GetKpisByModuleId();
        } else {
          this.portfolioCompanyList.kpiItems = [];
          this.portfolioCompanyModel.kpiItems = [];
          this.portfolioCompanyModel.moduleList = [];
          this.isPCCompanyChecked = false;
          this.isPCModuleChecked = false;
        }
        this.isPCCompanyChecked =
          this.portfolioCompanyModel.companyList.length ===
          this.portfolioCompanyList.companyList.length;
        break;
      case "PCFunds":
        this.model.portfolioCompanyModel.fundList = event;
        this.portfolioCompanyModel.moduleList = [];
        this.portfolioCompanyModel.kpiItems = [];
        this.portfolioCompanyList.kpiItems = [];
        if (this.model.portfolioCompanyModel.fundList.length > 0) {
          this.GetCompanies("PCCompany", "Change");
        } else {
          this.portfolioCompanyModel.companyList = [];
          this.isPCCompanyChecked = false;
          this.isPCModuleChecked = false;
          this.portfolioCompanyList.companyList = this.cloneCompanyList;
        }
        this.isPCFundChecked =
          this.portfolioCompanyModel.fundList.length ===
          this.portfolioCompanyList.fundList.length;
        break;
      case "PCModule":
        if (this.isParentChildGraph && event.length > 1) {
          this.toastrService.error(this.parentChildError, "", { positionClass: 'toast-center-center' });
          if(event?.length === this.portfolioCompanyList?.moduleList?.length){
            event = [];
            this.isPCModuleChecked = false;
          }
          else{
            event.pop();
          }
        }
        this.chkPCParentChildGraphDisabled = event.length > 1 && !this.isParentChildGraph;
        setTimeout(() => {
          this.portfolioCompanyModel.moduleList = [...event];
          this.model.portfolioCompanyModel.moduleList = [...event];
        });
        if (
          this.model.portfolioCompanyModel.companyList.length > 0 &&
          this.model.portfolioCompanyModel.moduleList.length > 0
        ) {
          this.isKpiItemLoading = true;
          this.GetKpisByModuleId();
          this.portfolioCompanyModel.kpiItems = [];
        } else {
          this.portfolioCompanyList.kpiItems = [];
          this.portfolioCompanyModel.kpiItems = [];
        }
        this.isPCModuleChecked =
          this.portfolioCompanyModel.moduleList.length ===
          this.portfolioCompanyList.moduleList.length;
        break;
      case "PCKpiItem":
        if (this.isParentChildGraph && event.length > 1) {
          this.toastrService.error(this.parentChildError, "", { positionClass: 'toast-center-center' });
          const totalKpiItems = this.countKpiItems(this.portfolioCompanyList.kpiItems);
          if (event.length === totalKpiItems) {
            event = [];
          } else {
            event.pop();
          }
        }
      
        this.chkPCParentChildGraphDisabled = event.length > 1 && !this.isParentChildGraph;
        setTimeout(() => {
          this.portfolioCompanyModel.kpiItems = [...event];
          this.model.portfolioCompanyModel.kpiItems = [...event];
        });
        break;
      case "PCCurrency":
        this.portfolioCompanyModel.currenceList = event;
        this.model.portfolioCompanyModel.currenceList = event;
        break;
      case "PCFxRates":
        this.portfolioCompanyModel.fxRates = event;
        this.model.portfolioCompanyModel.fxRates = event;
        break;
      case "PCPeriod":
        this.portfolioCompanyModel.period = event;
        this.model.portfolioCompanyModel.period = event;
        if (event.length > 0 && event[0] != null && event[1] != null) {
          this.myCalendar.hideOverlay();
        }
        break;
      case "PCParentChildGraph":
        this.isParentChildGraph = !event;
        this.portfolioCompanyModel.isParentChildGraph = this.isParentChildGraph;
        break;
    }
  }
  switchForESG(event: any, control: string) {
    switch (control) {
      case "ESGCompany":
        this.ESGModel.companyList = event;
        this.model.ESGModel.companyList = event;
        if (
          this.model.ESGModel.companyList.length > 0 &&
          this.model.ESGModel.moduleList.length > 0
        ) {
          this.isEsgKpiItemLoading = true;
          this.GetKpisByESGModuleId();
        } else {
          this.ESGList.kpiItems = [];
          this.ESGModel.kpiItems = [];
          this.ESGModel.moduleList = [];
        }
        if (this.model.ESGModel.companyList.length == 0 || this.model.ESGModel.moduleList.length == 0) {
          this.isESGCompanyChecked = false;
          this.isESGModuleChecked = false;
        }
        this.isESGCompanyChecked =
          this.ESGModel.companyList.length === this.ESGList.companyList.length;
        break;
      case "ESGFunds":
        this.ESGModel.fundList = event;
        this.model.ESGModel.fundList = event;
        this.ESGModel.moduleList = [];
        this.ESGModel.kpiItems = [];
        this.ESGList.kpiItems = [];
        this.isESGCompanyChecked = false;
        this.isESGModuleChecked = false;
        if (this.model.ESGModel.fundList.length > 0) {
          this.GetCompanies("ESGCompany", "Change");
        } else {
          this.ESGModel.companyList = [];
          this.ESGList.companyList = this.cloneCompanyList;
        }
        if (this.model.ESGModel.companyList.length == 0 || this.model.ESGModel.moduleList.length == 0) {
          this.isESGCompanyChecked = false;
          this.isESGModuleChecked = false;
        }
        this.isESGFundChecked =
          this.ESGModel.fundList.length === this.ESGList.fundList.length;
        break;
      case "ESGModule":
        if (this.isEsgParentChildGraph && event.length > 1) {
          this.toastrService.error(this.parentChildError, "", { positionClass: 'toast-center-center' });
          if(event?.length === this.ESGList?.moduleList?.length){
            event = [];
            this.isESGModuleChecked = false;
          }
          else{
            event.pop();
          }
        }
        this.chkEsgParentChildGraphDisabled = event.length > 1 && !this.isEsgParentChildGraph;
        setTimeout(() => {
          this.ESGModel.moduleList = [...event];
          this.model.ESGModel.moduleList = [...event];
        });
        if (
          this.model.ESGModel.companyList.length > 0 &&
          this.model.ESGModel.moduleList.length > 0
        ) {
          this.GetKpisByESGModuleId();
          this.ESGModel.kpiItems = [];
        } else {
          this.ESGList.kpiItems = [];
          this.ESGModel.kpiItems = [];
        }
        this.isESGModuleChecked =
          this.ESGModel.moduleList.length === this.ESGList.moduleList.length;
        break;
      case "ESGKpiItem":
        if (this.isEsgParentChildGraph && event.length > 1) {
          this.toastrService.error(this.parentChildError, "", { positionClass: 'toast-center-center' });
          const totalKpiItems = this.countKpiItems(this.ESGList?.kpiItems);
          if (event.length === totalKpiItems) {
            event = [];
          } else {
            event.pop();
          }
        }
      
        this.chkEsgParentChildGraphDisabled = event.length > 1 && !this.isParentChildGraph;
        setTimeout(() => {
          this.ESGModel.kpiItems = [...event];
          this.model.ESGModel.kpiItems = [...event];
        });
        break;
      case "ESGCurrency":
        this.ESGModel.currenceList = event;
        this.model.ESGModel.currenceList = event;
        break;
      case "ESGFxRates":
        this.ESGModel.fxRates = event;
        this.model.ESGModel.fxRates = event;
        break;
      case "ESGPeriod":
        this.ESGModel.period = event;
        this.model.ESGModel.period = event;
        if (event.length > 0 && event[0] != null && event[1] != null) {
          this.myCalendar.hideOverlay();
        }
        break;
      case "EsgParentChildGraph":
        this.isEsgParentChildGraph = !event;
        this.model.ESGModel.isParentChildGraph = this.isEsgParentChildGraph;
        break;
    }
  }
  switchForFund(event: any, control: string) {
    switch (control) {
      case "FundsList":
        this.fundModel.fundList = event;
        this.model.fundModel.fundList = event;
        this.isFundFundChecked =
          this.fundModel.fundList.length === this.fundList.fundList.length;
        break;
      case "FundModule":
        this.fundModel.moduleList = event;
        this.model.fundModel.moduleList = event;
        if (this.model.fundModel.moduleList.length > 0) {
          this.GetFundKpisByModuleId();
          this.fundModel.kpiItems = [];
        } else {
          this.fundList.kpiItems = [];
          this.fundModel.kpiItems = [];
        }
        this.isFundModuleChecked =
          this.fundModel.moduleList.length === this.fundList.moduleList.length;
        break;
      case "FundKpiItem":
        this.fundModel.kpiItems = event;
        this.model.fundModel.kpiItems = event;
        break;
      case "FundPeriod":
        this.fundModel.period = event;
        this.model.fundModel.period = event;
        if (event.length > 0 && event[0] != null && event[1] != null) {
          this.myCalendar.hideOverlay();
        }
        break;
    }
  }
  switchForDeal(event: any, control: string) {
    switch (control) {
      case "DealFunds":
        this.dealModel.fundList = event;
        this.model.dealModel.fundList = event;
        this.GetCompanies("DealCompany", "Change");
        this.isDealCompanyChecked = false;
        this.isDealFundChecked =
          this.dealModel.fundList.length === this.dealList.fundList.length;
        break;
      case "DealCompany":
        this.dealModel.companyList = event;
        this.model.dealModel.companyList = event;
        break;
      case "DealPeriod":
        this.dealModel.period = event;
        this.model.dealModel.period = event;
        if (event.length > 0 && event[0] != null && event[1] != null) {
          this.myCalendar.hideOverlay();
        }
        break;
      case "DealModule":
        this.dealModel.moduleList = event;
        this.model.dealModel.moduleList = event;
        if (
          this.model.dealModel.companyList.length > 0 &&
          this.model.dealModel.moduleList.length > 0
        ) {
          this.GetDealKpisByModuleId();
          this.dealModel.kpiItems = [];
        } else {
          this.dealList.kpiItems = [];
          this.dealModel.kpiItems = [];
        }
        this.isDealModuleChecked =
          this.dealModel.moduleList.length === this.dealList.moduleList.length;
        break;
      case "DealKpiItem":
        this.dealModel.kpiItems = event;
        this.model.dealModel.kpiItems = event;
        break;
    }
  }
  switchForInvestor(event: any, control: string) {
    switch (control) {
      case "InvestorList":
        this.investorModel.investorList = event;
        this.model.investorModel.investorList = event;
        if (this.investorModel.investorList.length > 0) {
          this.GetFundsByInvestor( "Change");
        } else {
          this.investorList.fundList = [];
          this.investorModel.fundList = [];
          this.isInvestorCompanyChecked = false;
        }
        this.isInvestorListChecked =
          this.investorModel.investorList.length ===
          this.investorList.investorList.length;
        break;
      case "InvestorFunds":
        this.investorModel.fundList = event;
        this.model.investorModel.fundList = event;
        if (this.model.investorModel.fundList.length > 0) {
          this.GetCompanies("InvestorCompany", "Change");
        } else {
          this.investorModel.companyList = [];
          this.investorList.companyList = this.cloneCompanyList;
        }
        this.isInvestorFundChecked =
          this.investorModel.fundList.length ===
          this.investorList.fundList.length;
        break;
      case "InvestorCompany":
        this.investorModel.companyList = event;
        this.model.investorModel.companyList = event;
        break;
      case "InvestorPeriod":
        this.investorModel.period = event;
        this.model.investorModel.period = event;
        if (event.length > 0 && event[0] != null && event[1] != null) {
          this.myCalendar.hideOverlay();
        }
        break;
      case "InvestorSections":
        this.investorModel.investorSections = event;
        this.model.investorModel.investorSections = event;
        break;
    }
  }
  onClose(event: any) {
    event.preventDefault();
  }
  onChangeModel(event: any, control: string) {
    switch (control) {
      case "PCCompany":
      case "PCFunds":
      case "PCModule":
      case "PCKpiItem":
      case "PCCurrency":
      case "PCFxRates":
      case "PCPeriod":
      case "PCParentChildGraph":
        this.switchForPC(event, control);
        break;
      case "DealFunds":
      case "DealCompany":
      case "DealPeriod":
      case "DealModule":
      case "DealKpiItem":
        this.switchForDeal(event, control);
        break;
      case "FundsList":
      case "FundModule":
      case "FundKpiItem":
      case "FundPeriod":
        this.switchForFund(event, control);
        break;
      case "InvestorList":
      case "InvestorFunds":
      case "InvestorCompany":
      case "InvestorPeriod":
      case "InvestorSections":
        this.switchForInvestor(event, control);
        break;
      case "ESGCompany":
      case "ESGFunds":
      case "ESGModule":
      case "ESGKpiItem":
      case "ESGCurrency":
      case "ESGFxRates":
      case "ESGPeriod":
      case "EsgParentChildGraph":
        this.switchForESG(event, control);
        break;
    }
    this.onInputChanges.emit(this.model);
  }

  OnFiltersSelected() {
    this.model.SelectedReport = this.SelectedReport;
    if (this.SelectedReport && this.SelectedReport?.userReportId) {
      this.filterService.getFilter(this.SelectedReport.userReportId).subscribe({
        next: (response) => {
          this.LoadSavedFilter(response);
        },
        error: (_error) => {},
      });
    }
  }
  LoadSavedFilter(res: any) {
    let reportFilters = res.reportFilters;
    this.setFilterPortfolioCompany(reportFilters);
    this.setFilterDeal(reportFilters);
    this.setFilterFund(reportFilters);
    this.setFilterInvestor(reportFilters);
    this.setFilterESG(reportFilters);
    setTimeout(() => {
      this.triggerApplyFunctions();
    }, 2000);
  }
  triggerApplyFunctions() {
    this.model.portfolioCompanyModel = this.portfolioCompanyModel;
    this.model.ESGModel = this.ESGModel;
    this.model.dealModel = this.dealModel;
    this.model.investorModel = this.investorModel;
    this.model.fundModel = this.fundModel;
    this.model.SelectedReport = this.SelectedReport;
    this.onInputChanges.emit(this.model);
  }
  setFilterPortfolioCompany(reportFilters: any) {
    this.portfolioCompanyModel.companyList =
      this.portfolioCompanyList.companyList.filter((s) => {
        return this.GetItems("PCCompany", reportFilters).some(
          (item) => item == s.companyId
        );
      });
    this.portfolioCompanyModel.fundList =
      this.portfolioCompanyList.fundList.filter((s) => {
        return this.GetItems("PCFunds", reportFilters).some(
          (item) => item == s.fundID
        );
      });
    this.portfolioCompanyModel.moduleList =
      this.portfolioCompanyList.moduleList.filter((s) => {
        return this.GetItems("PCModule", reportFilters).some(
          (item) => item == s.moduleId
        );
      });
    let kpiItemFilter = reportFilters.find((s) => s.filterName === "PCKpiItem");
    if (kpiItemFilter != undefined && kpiItemFilter.length != 0) {
      if (
        kpiItemFilter != undefined &&
        kpiItemFilter.filterValues != undefined &&
        kpiItemFilter.filterValues != null
      ) {
        if (
          kpiItemFilter?.filterValues.length > 0 &&
          kpiItemFilter?.filterValues.length === 0
        ) {
          this.portfolioCompanyList.kpiItems = kpiItemFilter?.filterValues.map(
            (item, index) => {
              return { ...item, id: index + 1 };
            }
          );
        }
      }
    }
    this.portfolioCompanyModel.currenceList =
      this.portfolioCompanyList.currenceList.filter((s) => {
        return this.GetItems("PCCurrency", reportFilters).some(
          (item) => item == s.currencyID
        );
      })[0];
    this.portfolioCompanyModel.fxRates =
      this.portfolioCompanyList.fxRates.filter((s) => {
        return this.GetItems("PCFxRates", reportFilters).some(
          (item) => item == s.id
        );
      })[0];
    this.portfolioCompanyModel.isParentChildGraph = this.isParentChildGraph;
    let pcPeriodFilter = reportFilters.find((s) => s.filterName === "PCPeriod");
    if (pcPeriodFilter != undefined && pcPeriodFilter.length > 0) {
      this.portfolioCompanyModel.period = [];
      this.portfolioCompanyModel.period.push(
        new Date(this.GetItems("PCPeriod", reportFilters)[0])
      );
      this.portfolioCompanyModel.period.push(
        new Date(this.GetItems("PCPeriod", reportFilters)[1])
      );
    } else {
      this.portfolioCompanyModel.period = null;
    }
  }
  setFilterESG(reportFilters: any) {
    this.ESGModel.companyList = this.ESGList.companyList.filter((s) => {
      return this.GetItems("ESGCompany", reportFilters).some(
        (item) => item == s.companyId
      );
    });
    this.ESGModel.fundList = this.ESGList.fundList.filter((s) => {
      return this.GetItems("ESGFunds", reportFilters).some(
        (item) => item == s.fundID
      );
    });
    this.ESGModel.moduleList = this.ESGList.moduleList.filter((s) => {
      return this.GetItems("ESGModule", reportFilters).some(
        (item) => item == s.moduleId
      );
    });
    let kpiItemFilter = reportFilters.find(
      (s) => s.filterName === "ESGKpiItem"
    );
    if (kpiItemFilter != undefined && kpiItemFilter.length != 0) {
      if (
        kpiItemFilter != undefined &&
        kpiItemFilter.filterValues != undefined &&
        kpiItemFilter.filterValues != null
      ) {
        this.ESGList.kpiItems = kpiItemFilter?.filterValues.map(
          (item, index) => {
            return { ...item, id: index + 1 };
          }
        );
      }
    }
    this.ESGModel.currenceList = this.ESGList.currenceList.filter((s) => {
      return this.GetItems("ESGCurrency", reportFilters).some(
        (item) => item == s.currencyID
      );
    })[0];
    this.ESGModel.fxRates = this.ESGList.fxRates.filter((s) => {
      return this.GetItems("ESGFxRates", reportFilters).some(
        (item) => item == s.id
      );
    })[0];
    let ESGPeriodFilter = reportFilters.find(
      (s) => s.filterName === "ESGPeriod"
    );
    this.ESGModel.isParentChildGraph = this.isEsgParentChildGraph;
    if (ESGPeriodFilter != undefined && ESGPeriodFilter.length > 0) {
      this.ESGModel.period = [];
      this.ESGModel.period.push(
        new Date(this.GetItems("ESGPeriod", reportFilters)[0])
      );
      this.ESGModel.period.push(
        new Date(this.GetItems("ESGPeriod", reportFilters)[1])
      );
    } else {
      this.ESGModel.period = null;
    }
  }
  setFilterDeal(reportFilters: any) {
    this.dealModel.companyList = this.dealList.companyList.filter((s) => {
      return this.GetItems("DealCompany", reportFilters).some(
        (item) => item == s.companyId
      );
    });
    this.dealModel.fundList = this.dealList.fundList.filter((s) => {
      return this.GetItems("DealFunds", reportFilters).some(
        (item) => item == s.fundID
      );
    });
    this.dealModel.moduleList = this.dealList.moduleList.filter((s) => {
      return this.GetItems("DealModule", reportFilters).some(
        (item) => item == s.moduleId
      );
    });
    let kpiItemFilter = reportFilters.find(
      (s) => s.filterName === "DealKpiItem"
    );
    if (kpiItemFilter != undefined && kpiItemFilter.length != 0) {
      if (
        kpiItemFilter != undefined &&
        kpiItemFilter.filterValues != undefined &&
        kpiItemFilter.filterValues != null
      ) {
        this.dealList.kpiItems = kpiItemFilter?.filterValues.map(
          (item, index) => {
            return { ...item, id: index + 1 };
          }
        );
      }
    }
    let dealPeriodFilter = reportFilters.find(
      (s) => s.filterName === "DealPeriod"
    );
    if (dealPeriodFilter != undefined && dealPeriodFilter.length > 0) {
      this.dealModel.period = [];
      this.dealModel.period.push(
        new Date(this.GetItems("DealPeriod", reportFilters)[0])
      );
      this.dealModel.period.push(
        new Date(this.GetItems("DealPeriod", reportFilters)[1])
      );
    } else {
      this.dealModel.period = null;
    }
  }
  setFilterFund(reportFilters: any) {
    this.fundModel.fundList = this.fundList.fundList.filter((s) => {
      return this.GetItems("FundsList", reportFilters).some(
        (item) => item == s.fundID
      );
    });
    this.fundModel.moduleList = this.fundList.moduleList.filter((s) => {
      return this.GetItems("FundModule", reportFilters).some(
        (item) => item == s.moduleId
      );
    });
    let kpiItemFilter = reportFilters.find(
      (s) => s.filterName === "FundKpiItem"
    );
    if (kpiItemFilter != undefined && kpiItemFilter.length != 0) {
      if (
        kpiItemFilter != undefined &&
        kpiItemFilter.filterValues != undefined &&
        kpiItemFilter.filterValues != null
      ) {
        this.fundList.kpiItems = kpiItemFilter?.filterValues.map(
          (item, index) => {
            return { ...item, id: index + 1 };
          }
        );
      }
    }
    let fundPeriodFilter = reportFilters.find(
      (s) => s.filterName === "FundPeriod"
    );
    if (fundPeriodFilter != undefined && fundPeriodFilter.length > 0) {
      this.fundModel.period = [];
      this.fundModel.period.push(
        new Date(this.GetItems("FundPeriod", reportFilters)[0])
      );
      this.fundModel.period.push(
        new Date(this.GetItems("FundPeriod", reportFilters)[1])
      );
    } else {
      this.fundModel.period = null;
    }
  }
  getFilterAvailableorNot(reportFilters: any, filterName: string) {
    return reportFilters.find((s) => s.filterName === filterName);
  }
  setFilterInvestor(reportFilters: any) {
    this.investorModel.investorList = this.investorList.investorList.filter(
      (s) => {
        return this.GetItems("InvestorList", reportFilters).some(
          (item) => item == s.investorId
        );
      }
    );
    if (this.investorModel.investorList.length > 0) {
      this.GetFundsByInvestor("reset");
    } else {
      this.investorList.fundList = [];
      this.investorModel.fundList = [];
    }
    setTimeout(() => {
      this.investorModel.fundList = this.investorList.fundList.filter((s) => {
        return this.GetItems("InvestorFunds", reportFilters).some(
          (item) => item == s.fundID
        );
      });
      if (
        this.investorModel.investorList.length > 0 &&
        this.investorModel.fundList.length > 0
      ) {
        this.GetCompanies("InvestorCompany", "Change");
        setTimeout(() => {
          this.investorModel.companyList = this.investorList.companyList.filter(
            (s) => {
              return this.GetItems("InvestorCompany", reportFilters).some(
                (item) => item == s.companyId
              );
            }
          );
        }, 500);
      }
    }, 500);
    this.investorModel.investorSections =
      this.investorList.investorSections.filter((s) => {
        return this.GetItems("InvestorSections", reportFilters).some(
          (item) => item == s.id
        );
      })[0];
    let investorPeriodFilter = reportFilters.find(
      (s) => s.filterName === "InvestorPeriod"
    );
    if (
      investorPeriodFilter != undefined &&
      investorPeriodFilter.filterValues != null
    ) {
      this.investorModel.period = [];
      this.investorModel.period.push(
        new Date(this.GetItems("InvestorPeriod", reportFilters)[0])
      );
      this.investorModel.period.push(
        new Date(this.GetItems("InvestorPeriod", reportFilters)[1])
      );
    } else {
      this.investorModel.period = null;
    }
  }
  OnDeleteFilter() {
    this.confirmDelete = true;
  }
  CancelDelete() {
    this.confirmDelete = false;
  }
  Delete() {
    this.confirmDelete = false;
    this.filterService
      .DeleteFilter(this.SelectedReport.userReportId)
      .subscribe({
        next: (_response) => {
          this.toastrService.success(
            this.FundEnum.FilterDeletedSuccesfully,
            "",
            { positionClass: DataAnalyticsConstants.ToastCenterCenter }
          );
          this.LoadFilters();
          this.resetDataAnalyticsModel();
        },
        error: (_error) => {},
      });
  }

  LoadFilters() {
    this.saveFiltersOptionsList = [];
    this.filterService.getFilters().subscribe((response) => {
      let filtersList = response.filter(
        (s) => s.reportID == this.dataAnalyticsReportId
      );
      this.saveFiltersOptionsList = filtersList;
    });
  }
  resetDataAnalyticsModel() {
    this.dataAnalyticsFilterdata.portfolioCompanyModel = {
      fundList: [],
      companyList: [],
      period: [],
      moduleList: [],
      kpiItems: [],
      currenceList: [],
      fxRates: [],
    };
    this.dataAnalyticsFilterdata.ESGModel = {
      fundList: [],
      companyList: [],
      period: [],
      moduleList: [],
      kpiItems: [],
      currenceList: [],
      fxRates: [],
    };
    this.dataAnalyticsFilterdata.fundModel = {
      fundList: [],
      moduleList: [],
      kpiItems: [],
      period: [],
    };
    this.dataAnalyticsFilterdata.dealModel = {
      fundList: [],
      companyList: [],
      period: [],
    };
    this.dataAnalyticsFilterdata.investorModel = {
      fundList: [],
      investorList: [],
      companyList: [],
      period: [],
      investorSections: [],
    };
  }
  ////////////final save filter//////////////////////
  toggleAllText(type: string) {
    const typeMapping = {
      PCFunds: this.isPCFundChecked,
      PCCompany: this.isPCCompanyChecked,
      PCModule: this.isPCModuleChecked,
      FundsList: this.isFundFundChecked,
      FundModule: this.isFundModuleChecked,
      InvestorList: this.isInvestorListChecked,
      InvestorFunds: this.isInvestorFundChecked,
      InvestorCompany: this.isInvestorCompanyChecked,
      DealModule: this.isDealModuleChecked,
      DealFunds: this.isDealFundChecked,
      DealCompany: this.isDealCompanyChecked,
      ESGModule: this.isESGModuleChecked,
      ESGFunds: this.isESGFundChecked,
      ESGCompany: this.isESGCompanyChecked,
    };
    return typeMapping[type] ? "Deselect All" : "Select All";
  }
  toggleCheckState(type: string) {
    switch (type) {
      case "PCFunds":
        this.isPCFundChecked = !this.isPCFundChecked;
        break;
      case "PCCompany":
        this.isPCCompanyChecked = !this.isPCCompanyChecked;
        break;
      case "PCModule":
        this.isPCModuleChecked = !this.isPCModuleChecked;
        break;
      case "InvestorFunds":
        this.isInvestorFundChecked = !this.isInvestorFundChecked;
        break;
      case "InvestorCompany":
        this.isInvestorCompanyChecked = !this.isInvestorCompanyChecked;
        break;
      case "InvestorList":
        this.isInvestorListChecked = !this.isInvestorListChecked;
        break;
      case "FundsList":
        this.isFundFundChecked = !this.isFundFundChecked;
        break;
      case "FundModule":
        this.isFundModuleChecked = !this.isFundModuleChecked;
        break;
      case "DealFunds":
        this.isDealFundChecked = !this.isDealFundChecked;
        break;
      case "DealCompany":
        this.isDealCompanyChecked = !this.isDealCompanyChecked;
        break;
      case "DealModule":
        this.isDealModuleChecked = !this.isDealModuleChecked;
        break;
      case "ESGFunds":
        this.isESGFundChecked = !this.isESGFundChecked;
        break;
      case "ESGCompany":
        this.isESGCompanyChecked = !this.isESGCompanyChecked;
        break;
      case "ESGModule":
        this.isESGModuleChecked = !this.isESGModuleChecked;
        break;
    }
  }
  
  createTypeConfig(isChecked: boolean, list: string, model: any, sourceList: any) {
    return { isChecked, list, model, sourceList };
  }

  createPCTypeConfig(isChecked: boolean, list: string) {
    return this.createTypeConfig(isChecked, list, this.portfolioCompanyModel, this.portfolioCompanyList);
  }

  createInvestorTypeConfig(isChecked: boolean, list: string) {
    return this.createTypeConfig(isChecked, list, this.investorModel, this.investorList);
  }

  createFundTypeConfig(isChecked: boolean, list: string) {
    return this.createTypeConfig(isChecked, list, this.fundModel, this.fundList);
  }

  createDealTypeConfig(isChecked: boolean, list: string) {
    return this.createTypeConfig(isChecked, list, this.dealModel, this.dealList);
  }

  createESGTypeConfig(isChecked: boolean, list: string) {
    return this.createTypeConfig(isChecked, list, this.ESGModel, this.ESGList);
  }
  onSelectedAll(type: string) {
  this.toggleCheckState(type);
    const typeMapping = {
      PCFunds: this.createPCTypeConfig(this.isPCFundChecked, "fundList"),
      PCCompany: this.createPCTypeConfig(this.isPCCompanyChecked, "companyList"),
      PCModule: this.createPCTypeConfig(this.isPCModuleChecked, "moduleList"),
      InvestorFunds: this.createInvestorTypeConfig(this.isInvestorFundChecked, "fundList"),
      InvestorCompany: this.createInvestorTypeConfig(this.isInvestorCompanyChecked, "companyList"),
      InvestorList: this.createInvestorTypeConfig(this.isInvestorListChecked, "investorList"),
      FundsList: this.createFundTypeConfig(this.isFundFundChecked, "fundList"),
      FundModule: this.createFundTypeConfig(this.isFundModuleChecked, "moduleList"),
      DealFunds: this.createDealTypeConfig(this.isDealFundChecked, "fundList"),
      DealCompany: this.createDealTypeConfig(this.isDealCompanyChecked, "companyList"),
      DealModule: this.createDealTypeConfig(this.isDealModuleChecked, "moduleList"),
      ESGFunds: this.createESGTypeConfig(this.isESGFundChecked, "fundList"),
      ESGCompany: this.createESGTypeConfig(this.isESGCompanyChecked, "companyList"),
      ESGModule: this.createESGTypeConfig(this.isESGModuleChecked, "moduleList"),
    };
    const mapping = typeMapping[type];
    if (mapping) {
      const list = mapping.sourceList[mapping.list];
      list.forEach((item) => (item.isChecked = mapping.isChecked));
      if(!mapping.isChecked ){
        mapping.model[mapping.list] = [];
      }
      const model = mapping.model;
      model[mapping.list] = mapping.isChecked ? list.slice() : [];
      this.onChangeModel(model[mapping.list], type);
    }
  }
  tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags];
  }
  public calculateLengthtagMapper(dataItems: any[]): number {
    return dataItems.filter(item => !item?.isModuleHeader).length;
  }
}
