<confirm-modal customwidth="500px"  isCustomFooterClass="true" primaryButtonIsDanger="true" primaryButtonName="{{confirmBtn}}" secondaryButtonName="{{cancelBtn}}"
(primaryButtonEvent)="OnPrimaryButtonEvent()" modalTitle="{{modalTitle}}" (secondaryButtonEvent)="OnSecondaryButtonEvent()">
  <div class="container p-0 mb-0">
    <div class="delete-confirmation-modal">
      <div class="delete-warning">
        <div *ngIf="!isTableLevel">
          <img
            alt="warning"
            src="assets/dist/images/clo_delete_warning.svg"
          />
        </div>
        <div class="warning-text" *ngIf="!isTableLevel">
          <ng-container class="Body-M" *ngIf="customEmailDeleteMsg; else defaultDeleteMsg">
            You wanted to delete an Active Email reminder.
          </ng-container>
          <ng-template #defaultDeleteMsg>
            {{Delete_Confirmation_Body_1}} <span class="Heading2-B">"{{deletedCloName}}"?</span>
            <br>
            {{Delete_Confirmation_Body_2}}
          </ng-template>
        </div>
        <div *ngIf="isTableLevel" class="tableContent">
          <div class="content Heading2-M table-warnings">{{Delete_Table_Note_1}} </div>
          <div class="Body-R table-warning-text">{{Delete_Table_Note_2}} </div>
        </div>
      </div>
      <div class="delete-warning-msg" *ngIf="!isTableLevel && !hideNote">
        <div class="note Heading2-M">{{Delete_Note}} <span class="text-dark Heading2-M">:</span></div>
        <div class="content Body-M">{{Delete_Note_1}} {{deleteNoteType}} {{Delete_Note_2}}<br> {{Delete_Note_3}}.</div>
      </div>
    </div>  
  </div>

</confirm-modal>