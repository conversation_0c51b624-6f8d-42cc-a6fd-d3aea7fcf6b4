import { Component, EventEmitter, Output } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-change-confirm-growth',
  templateUrl: './change-confirm-growth.component.html',
})
export class ChangeConfirmGrowthComponent {
    subject: Subject<boolean>;
    @Output() onSave = new EventEmitter<any>();
  
    constructor(public activeModal: NgbActiveModal) { }
  
    CloseModal(CloseModal: any) {
      if (CloseModal == 'Yes') {
        this.onSave.emit(true);
        this.activeModal.close();
      }
      else {
        this.onSave.emit(false);
        this.activeModal.close();
      }
    }

}
