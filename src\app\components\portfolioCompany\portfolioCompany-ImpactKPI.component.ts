import { Component, OnInit, ViewChild, AfterViewInit, EventEmitter, Input } from "@angular/core";
import { PortfolioCompanyService } from "../../services/portfolioCompany.service";
import { ToastContainerDirective, ToastrService } from "ngx-toastr";
import { Table } from "primeng/table";
import { MatMenu, MatMenuTrigger } from "@angular/material/menu";
import { filter } from "rxjs/operators";
import { KpiTypes, NumberDecimalConst, ImpactKPIConstants, PeriodType, GlobalConstants, FinancialsValueTypes, KpiTypesConstants } from "src/app/common/constants";
import { ITab } from "projects/ng-neptune/src/lib/Tab/tab.model";
import { feed, setCommonDefaultTypeTab, setCommonFilterOptionsKeys, commonConvertUnits } from "src/app/utils/kpi-utility";
import { ErrorMessage, FinancialValueUnitsEnum, MiscellaneousService, OrderTypesEnum, PeriodTypeQuarterEnum } from "src/app/services/miscellaneous.service";
import { isNumeric } from "@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric";
import { extractDateComponents } from "../file-uploads/kpi-cell-edit/cell-edit-utils";
import { Audit, MappedDocuments, TableHeader } from "../file-uploads/kpi-cell-edit/kpiValueModel";
import { AuditService } from "src/app/services/audit.service";
import { ActionsEnum, KPIModulesEnum, PermissionService, UserSubFeaturesEnum } from 'src/app/services/permission.service';
import { OidcAuthService } from "src/app/services/oidc-auth.service";
import { DatePipe } from '@angular/common';
@Component({
  selector: "portfolio-impact-kpi",
  templateUrl: "./portfolioCompany-ImpactKPI.component.html",
  styleUrls: ["./master-kpi-beta/master-kpi-beta.component.scss"],
})
export class PortfolioCompanyImpactKPIComponent
  implements OnInit, AfterViewInit {
  subFeature: typeof UserSubFeaturesEnum = UserSubFeaturesEnum;
  kpiModuleId = KPIModulesEnum.Impact;
  @ViewChild(ImpactKPIConstants.dtView) dt: Table | undefined;
  searchFilter: any = null;
  frozenCols: any = [
    { field: ImpactKPIConstants.KPI, header: ImpactKPIConstants.KPI },
  ];
  model: any = {};
  tableReload = false;
  infoUpdate: boolean = false;
  isUploadPopupVisible: boolean = false;
  uniqueModuleCompany: any;
  dataRow: object;
  dataColumns: TableHeader;
  globalFilter: string;
  NumberDecimalConst = NumberDecimalConst;
  @ViewChild(ToastContainerDirective, {})
  toastContainer: ToastContainerDirective;
  isLoader: boolean = false;
  @ViewChild(ImpactKPIConstants.menu) uiUxMenu!: MatMenu;
  @ViewChild(ImpactKPIConstants.ImpactMenuTrigger) menuTrigger: MatMenuTrigger;
  @Input() pageConfigData = [
    { kpiConfigurationData: [], hasChart: false, kpiType: "" },
  ];
  @Input() modelList: any;
  @Input() impactKPIPermissions: any = [];
  subSectionFields = [];
  pageConfigResponse = {
    kpiConfigurationData: [],
    hasChart: false,
    kpiType: "",
  };
  defaultType: string = ImpactKPIConstants.Monthly;
  tabValueTypeList: ITab[] = [];
  tabName: string = "";
  isMonthly: boolean = true;
  isQuarterly: boolean = false;
  isAnnually: boolean = false;
  loading = false;
  filterOptions: any[] = [];
  tableColumns = [];
  tableResult = [];
  tableResultClone = [];
  isPageLoad = true;
  modelImpactKpi: any = {};
  masterKpiValueUnit: any;
  tableFrozenColumns = [];
  financialKpiSearchFilter: any;
  kpiFilterCols: any = [];
  exportLoading: boolean = false;
  isValueUpdated: boolean = false;
  ErrorNotation: boolean = false;
  isToggleChecked: boolean = false;
  auditLogList: any = [];
  isYtd: boolean = false;
  isLtm: boolean = false;
  hasYtd: boolean = false;
  hasLtm: boolean = false;
  valueTypeString: string;
  isYtdPageLoad: boolean = true;
  isLtmPageLoad: boolean = true;
  message: any[];
  id: any;
  kpiCurrencyFilterModel: any = {};
  isValueConverted: boolean = false;
  auditLogErrorForConvertedValue: string = GlobalConstants.AuditLogErrorForConvertedValue;
  editErrorForConvertedValue: string = GlobalConstants.EditgErrorForConvertedValue;
  auditLogTitle: string = GlobalConstants.AuditLogTitle;
  currencyCode: string = "";
  currData: any;
  constructor(
    private portfolioCompanyService: PortfolioCompanyService,
    private toastrService: ToastrService,
    private auditService: AuditService,
    private miscService: MiscellaneousService,
    private permissionService: PermissionService,
    private identityService: OidcAuthService,
    private datePipe: DatePipe
  ) {
    this.modelImpactKpi.periodType = {
      type: PeriodTypeQuarterEnum.Last1Year,
    };
    this.modelImpactKpi.orderType = { type: OrderTypesEnum.LatestOnRight };
    this.masterKpiValueUnit = {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    };
  }
  /**
   * Lifecycle hook that is called after Angular has fully initialized the component's view.
   * It is called only once after the first `ngAfterContentChecked`.
   * Use this hook to perform any initialization logic that relies on the component's view or child views.
   */
  ngAfterViewInit() {
    if (this.uiUxMenu != undefined) {
      (this.uiUxMenu as any).closed = this.uiUxMenu.closed;
      this.configureMenuClose(this.uiUxMenu.closed);
    }
  }
  /**
   * Configures the menu close behavior.
   *
   * @param old - The original close function of the MatMenu.
   * @returns An updated EventEmitter that handles the close event.
   */
  configureMenuClose(old: MatMenu["close"]): MatMenu["close"] {
    const upd = new EventEmitter();
    feed(
      upd.pipe(
        filter((event) => {
          if (event === "click") {
            return false;
          }
          return true;
        })
      ),
      old
    );
    return upd;
  }
  /**
   * Initializes the component.
   */
  ngOnInit() {
    this.pageConfigResponse = this.pageConfigData.find(
      (x) => x.kpiType == KpiTypes.Impact.type
    );
    this.subSectionFields = this.pageConfigResponse?.kpiConfigurationData;
    this.getValueTypeTabList();
    this.currData = this.modelList?.reportingCurrencyDetail;
    this.currencyCode = this.modelList?.reportingCurrencyDetail?.currencyCode;
    if (window.location.host.includes('himera') || window.location.host.includes('himera-staging')  || window.location.host.includes('local') || window.location.host.includes('test') || window.location.host.includes('uat')) {
      this.currencyCode = this.modelList?.fundReportingCurrency?.currencyCode;
      this.currData = this.modelList?.fundReportingCurrency;
    }
  }
  /**
   * Checks if the given value is a number.
   *
   * @param str - The value to check.
   * @returns `true` if the value is a number, `false` otherwise.
   */
  isNumberCheck(str: any) {
    return isNumeric(str);
  }
  /**
   * Retrieves the value type tab list from the portfolio company service and sets the necessary properties.
   */
  getValueTypeTabList() {
    this.portfolioCompanyService.getfinancialsvalueTypes().subscribe((x) => {
      let tabList = x.body?.financialTypesModelList;
      let pageConfigTabs = this.subSectionFields;
      tabList = tabList?.filter((item: any) =>
        pageConfigTabs?.some((otherItem: any) =>
          otherItem.aliasName.includes(item.name)
        )
      );
      if (tabList != undefined && tabList.length > 0) {
        this.tabValueTypeList = tabList;
        this.tabValueTypeList[0].active = true;
        this.tabName = this.tabValueTypeList[0].name;
        PeriodType.filterOptions.forEach((x) => (x.key = false));
        this.setPeriodsOptions(pageConfigTabs);
      }
    });
  }
  /**
   * Sets the period options for the page configuration tabs.
   *
   * @param pageConfigTabs - An array of page configuration tabs.
   */
  setPeriodsOptions(pageConfigTabs: any[]) {
    let ltmYTDPeriodType = this.setLtmYtdPeriodType(this.tabName);
    let periodOptions = PeriodType.filterOptions;
    let activeTabData = pageConfigTabs?.find(
      (x) => x.aliasName == ltmYTDPeriodType
    );
    if (this.isLtm || this.isYtd) this.valueTypeString = ltmYTDPeriodType;
    else this.valueTypeString = undefined;
    if (activeTabData == undefined) {
      activeTabData = this.processPageLoadView(activeTabData, ltmYTDPeriodType);
    }
    this.filterOptions = periodOptions?.filter((item) =>
      activeTabData?.chartValue?.some((otherItem) => otherItem === item.field)
    );
    let periodType = this.filterOptions.find((x) => x.key);
    if (periodType == undefined && this.filterOptions.length > 0) {
      for (const element of periodOptions) {
        element.key = false;
      }
      this.filterOptions[0].key = true;
      periodType = this.filterOptions[0];
    }
    this.onChangePeriodOption(periodType);
  }
  /**
   * Handles the change event for the period option.
   * @param {any} type - The selected period option.
   */
  onChangePeriodOption(type) {
    this.filterOptions.forEach((x) => (x.key = false));
    if (type?.field == ImpactKPIConstants.Monthly) {
      type.key = this.isMonthly = true;
      this.isQuarterly = false;
      this.isAnnually = false;
    } else if (type?.field == ImpactKPIConstants.Quarterly) {
      this.isMonthly = false;
      type.key = this.isQuarterly = true;
      this.isAnnually = false;
    } else {
      this.isMonthly = false;
      this.isQuarterly = false;
      if (type != undefined) type.key = this.isAnnually = true;
    }
    this.setDefaultTypeTab();
    this.getPortfolioCompanyMasterKPIValues(null);
  }
  /**
   * Sets the default type tab based on the values of `isMonthly` and `isQuarterly`.
   */
  setDefaultTypeTab = () => {
    this.defaultType = setCommonDefaultTypeTab(
      this.isMonthly,
      this.isQuarterly
    );
  };
  /**
   * Selects a tab and performs necessary actions.
   * @param tab - The tab to be selected.
   */
  selectValueTab(tab: ITab) {
    this.tabValueTypeList.forEach((tab) => (tab.active = false));
    tab.active = true;
    this.tabName = tab.name;
    if (tab?.name == "IC") {
      this.isLtm = false;
      this.isYtd = false;
    }
    this.setPeriodsOptions(this.subSectionFields);
  }
  /**
   * Sets the filter options keys based on the provided result.
   * @param result - The result used to set the filter options keys.
   */
  SetFilterOptionsKeys(result: any) {
    this.filterOptions = setCommonFilterOptionsKeys(this.filterOptions, result);
    this.setDefaultTypeTab();
  }
  /**
   * Clears the data in the component.
   */
  clearData() {
    this.loading = false;
    this.isLoader = false;
    this.tableColumns = [];
    this.tableResult = [];
    this.tableResultClone = [];
    this.auditLogList = [];
    this.isPageLoad = false;
  }
  /**
   * Applies a global filter to the KPI table.
   *
   * @param event - The event object containing the filter information.
   */
  kpiTable_GlobalFilter(event) {
    this.masterKpiValueUnit =
      event?.UnitType == undefined
        ? {
          typeId: FinancialValueUnitsEnum.Millions,
          unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
        }
        : event?.UnitType;
    this.searchFilter = event;
    this.kpiCurrencyFilterModel = event;
    if(this.currencyCode  != null && event?.currencyCode != null && this.currencyCode  != event?.currencyCode){
      this.isValueConverted = true;
    }
    else{
      this.isValueConverted = false;
    }
    this.getPortfolioCompanyMasterKPIValues(null);
    this.menuTrigger.closeMenu();
  }
  /**
   * Retrieves the master KPI values for the portfolio company.
   * @param event - The event object.
   */
  getPortfolioCompanyMasterKPIValues(event: any) {
    this.isLoader = true;
    const defaultFilter = {
      periodType: this.modelImpactKpi.periodType.type,
    };
    let searchFilter = this.searchFilter || defaultFilter;

    if (searchFilter.periodType == ImpactKPIConstants.DateRange) {
      const periodSource = searchFilter.startPeriod
        ? searchFilter
        : this.modelImpactKpi;
      searchFilter.startPeriod = this.miscService.createDateFromPeriod(
        periodSource.startPeriod
      );
      searchFilter.endPeriod = this.miscService.createDateFromPeriod(
        periodSource.endPeriod
      );
    }
    this.financialKpiSearchFilter = searchFilter;
    this.getImpactKPIValues(searchFilter, event);
  }
  /**
   * Retrieves the impact KPI values based on the provided search filter and event.
   * @param searchFilter - The search filter to be applied.
   * @param event - The event object containing pagination information.
   */
  getImpactKPIValues(searchFilter: any, event: any) {
    this.portfolioCompanyService
      .getImpactKPIValues({
        portfolioCompanyID: this.modelList?.portfolioCompanyID,
        paginationFilter: event,
        searchFilter: searchFilter,
        valueType:
          this.valueTypeString != undefined
            ? this.valueTypeString
            : this.tabValueTypeList.length == 0
              ? ImpactKPIConstants.Actual
              : this.tabName,
        isMonthly: this.isMonthly,
        isQuarterly: this.isQuarterly,
        isAnnually: this.isAnnually,
        isPageLoad: this.isPageLoad,
        moduleID: this.modelList?.moduleId,
        companyId: this.modelList?.portfolioCompanyID?.toString(),
        kpiConfigurationData: this.pageConfigResponse?.kpiConfigurationData,
        isYtdPageLoad: this.isYtdPageLoad,
        isLtmPageLoad: this.isLtmPageLoad,
        isYtd: this.isYtd,
        isLtm: this.isLtm,
        IsSpotRate : this.kpiCurrencyFilterModel.isSpotRate == null ? false : this.kpiCurrencyFilterModel.isSpotRate,
        SpotRateDate:this.kpiCurrencyFilterModel.isSpotRate ? this.datePipe.transform(this.kpiCurrencyFilterModel.spotRateDate, 'yyyy-MM-dd') : null,
        currencyCode: this.kpiCurrencyFilterModel?.currencyCode,
        reportingCurrencyCode: this.currencyCode,
        currencyRateSource: this.kpiCurrencyFilterModel?.currencyRateSource,
      })
      .subscribe({
        next: (result) => {
          if (result != null) {
            this.loading = false;
            this.ErrorNotation = false;
            this.isLoader = false;
            this.tableReload = true;
            this.tableColumns = result?.headers || [];
            this.tableFrozenColumns = this.frozenCols;
            this.tableResult = result?.rows || [];
            this.tableResultClone = result?.rows || [];
            this.auditLogList = result?.companyKpiAuditLog || [];
            this.tableResult = commonConvertUnits(
              this.tableResult,
              this.masterKpiValueUnit,
              this.tableResultClone,
              this.tableColumns
            );
            this.kpiFilterCols = [
              ...this.tableFrozenColumns,
              ...this.tableColumns,
            ];
            this.isPageLoad = false;
            if (this.isYtd) this.isYtdPageLoad = false;
            if (this.isLtm) this.isLtmPageLoad = false;
            if (result != null) {
              this.isMonthly = result?.isMonthly;
              this.isQuarterly = result?.isQuarterly;
              this.isAnnually = result?.isAnnually;
              this.SetFilterOptionsKeys(result);
            }
          } else {
            this.clearData();
          }
        },
        error: (error) => {
          this.clearData();
        },
      });
  }
  /**
   * Handles the change event of the checkbox.
   * @param {Event} e - The event object.
   * @returns {void}
   */
  handleChange(e) {
    this.ErrorNotation = e;
    this.isToggleChecked = this.ErrorNotation;
  }
  /**
   * Redirects to the audit log page with the specified parameters.
   *
   * @param field - The field object.
   * @param attributeName - The attribute name.
   * @param data - The mapped documents data.
   * @param auditLogFilter - The audit log filter.
   */
  redirectToAuditLogPage(field: any, attributeName: any, data: MappedDocuments, auditLogFilter: Audit) {
    let params = {
      KPI: this.tabName,
      header: field.header,
      PortfolioCompanyID: this.modelList.portfolioCompanyID,
      AttributeName: attributeName,
      ModuleId: KPIModulesEnum.Impact,
      Comments: this.setLtmYtdPeriodType(this.tabName),
      currency: this.currencyCode ,
      AttributeId: data.valueId,
      isNewAudit: true,
      KpiId: auditLogFilter.kpiId,
      MappingId: auditLogFilter.mappingId,
    };
    sessionStorage.setItem(GlobalConstants.CurrentModule, KpiTypesConstants.IMPACT_KPI);
    sessionStorage.setItem(GlobalConstants.ImpactKpiAuditLocalStorage, JSON.stringify(params));
    let config = this.identityService.getEnvironmentConfig();
    if (config.redirect_uri != "") {
      let myAppUrl = config.redirect_uri.split("/in")[0] + ImpactKPIConstants.AuditLogRoute;
      window.open(myAppUrl, '_blank');
    }
  }
  /**
   * Handles the audit log functionality for a specific row in the portfolio company impact KPI component.
   * @param rowData - The data of the row.
   * @param field - The field of the row.
   */
  onAuditLog(rowData: any, field: any) {
    const ERROR_MESSAGE = GlobalConstants.AuditLogError;
    if (!this.ErrorNotation || rowData.IsHeader) {
      if (this.ErrorNotation) {
        this.showErrorToast(ERROR_MESSAGE);
      }
      return;
    }
    if (this.isConvertedValue(rowData)) {
      this.toastrService.warning(this.auditLogErrorForConvertedValue, '', { positionClass: ImpactKPIConstants.ToastCenterCenter });
      return;
    }
    const dateComponents = extractDateComponents(field.header);
    let auditLogFilter = this.getAuditLogFilter(rowData, dateComponents);
    this.auditService
      .getPortfolioEditSupportingCommentsData(auditLogFilter)
      .subscribe({
        next: (data: MappedDocuments) => {
          if (data?.auditLogId > 0 && data?.auditLogCount > 0) {
            let attributeName = rowData.Kpi;
            this.redirectToAuditLogPage(
              field,
              attributeName,
              data,
              auditLogFilter
            );
          } else if (data?.auditLogCount == 0) {
            this.showErrorToast(GlobalConstants.AuditLogNAMessage);
          }
        },
        error: (error: any) => {
          this.showErrorToast(ERROR_MESSAGE);
        },
      });
  }
  /**
   * Returns an Audit object representing the filter criteria for the audit log.
   * @param rowData - The data of the row.
   * @param dateComponents - The date components (year, month, quarter).
   * @returns An Audit object with the filter criteria.
   */
  getAuditLogFilter(
    rowData: any,
    dateComponents: { year: any; month: number; quarter: any }
  ) {
    return <Audit>{
      valueType: this.setLtmYtdPeriodType(this.tabName),
      kpiId: rowData["KpiId"],
      mappingId: rowData["MappingId"],
      quarter: dateComponents.quarter,
      year: dateComponents.year,
      month: dateComponents.month,
      moduleId: KPIModulesEnum.Impact,
      companyId: this.modelList.portfolioCompanyID,
    };
  }
  /**
   * Displays an error toast message.
   *
   * @param message - The error message to display.
   * @param title - The title of the error toast. (optional)
   * @param position - The position of the error toast. (optional)
   */
  showErrorToast(
    message: string,
    title: string = "",
    position: string = ImpactKPIConstants.ToastCenterCenter
  ): void {
    this.toastrService.error(message, title, { positionClass: position });
  }
  /**
   * Handles the LTM YTD.
   */
  setLtmYtdPeriodType(periodType: string) {
    const ltmType = `${periodType} ${FinancialsValueTypes.LTM}`;
    const ytdType = `${periodType} ${FinancialsValueTypes.YTD}`;
    this.hasLtm = this.checkExistence(ltmType);
    this.hasYtd = this.checkExistence(ytdType);
    if (this.hasLtm || this.hasYtd)
      periodType = this.getPeriodTypeWithSuffix(periodType);
    return periodType;
  }
  /**
   * Checks the existence of a specific type in the kpiConfigurationData array.
   * @param type - The type to check for existence.
   * @returns True if the type exists, false otherwise.
   */
  checkExistence(type: string) {
    const res = this.pageConfigResponse?.kpiConfigurationData?.filter(
      (x) => x.aliasName === type
    );
    return res?.length > 0;
  }
  /**
   * Returns the period type with the appropriate suffix based on certain conditions.
   * If the period type requires LTM (Last Twelve Months) suffix, it will be added.
   * If the period type requires YTD (Year-to-Date) suffix, it will be added.
   * If none of the conditions are met, the period flags will be reset.
   * @param periodType - The period type to be modified.
   * @returns The modified period type with the appropriate suffix.
   */
  getPeriodTypeWithSuffix(periodType: string) {
    if (this.shouldAddLtm(periodType)) {
      periodType = `${periodType} ${FinancialsValueTypes.LTM}`;
    } else if (this.shouldAddYtd(periodType)) {
      periodType = `${periodType} ${FinancialsValueTypes.YTD}`;
    } else {
      this.resetPeriodFlags();
    }
    return periodType;
  }
  /**
   * Determines whether to add LTM (Last Twelve Months) based on the period type.
   * @param periodType - The period type.
   * @returns A boolean value indicating whether to add LTM.
   */
  shouldAddLtm(periodType: string) {
    return (
      this.isLtm &&
      this.hasLtm &&
      !periodType.includes(FinancialsValueTypes.LTM)
    );
  }
  /**
   * Determines whether to add YTD (Year-to-Date) based on the period type.
   *
   * @param periodType - The period type to check.
   * @returns A boolean value indicating whether to add YTD.
   */
  shouldAddYtd(periodType: string) {
    return (
      this.isYtd &&
      this.hasYtd &&
      !periodType.includes(FinancialsValueTypes.YTD)
    );
  }
  /**
   * Resets the period flags.
   * Sets the `isLtm` and `isYtd` flags to `false`.
   */
  resetPeriodFlags() {
    this.isLtm = false;
    this.isYtd = false;
  }
  /**
   * Handles the change event for the value type option.
   *
   * @param type - The selected value type.
   */
  onChangeValueTypeOption(type) {
    if (this.tabName != FinancialsValueTypes.IC) {
      this.valueTypeString = this.tabName + " " + type;
      let isPageLoad = this.setPageLoad();
      if (isPageLoad) {
        this.isMonthly = true;
        this.isAnnually = false;
        this.isQuarterly = false;
        this.setDefaultTypeTab();
      }
      this.setLtmAndYtdFlags(type);
      if (!this.isLtm && !this.isYtd) {
        this.valueTypeString = undefined;
      }
      this.setPeriodsOptions(this.subSectionFields);
    }
  }
  /**
   * Sets the flags for LTM (Last Twelve Months) and YTD (Year-to-Date) based on the given type.
   * @param type - The type of financial value ('YTD' or 'LTM').
   */
  setLtmAndYtdFlags(type: string) {
    if (type == FinancialsValueTypes.YTD) {
      this.isYtd = !this.isYtd;
      this.isLtm = false;
    } else {
      this.isLtm = !this.isLtm;
      this.isYtd = false;
    }
  }
  /**
   * Processes the page load view.
   *
   * @param selectedPeriodTypeConfiguration - The selected period type configuration.
   * @param periodType - The period type.
   * @returns The selected period type configuration.
   */
  processPageLoadView(
    selectedPeriodTypeConfiguration: any,
    periodType: string
  ) {
    if (selectedPeriodTypeConfiguration == undefined) {
      this.updatePeriodTypeFlags();
      periodType = this.getPeriodTypeWithSuffix(periodType);
      selectedPeriodTypeConfiguration =
        this.findPeriodTypeConfiguration(periodType);
    }
    this.valueTypeString = periodType;
    return selectedPeriodTypeConfiguration;
  }
  /**
   * Updates the period type flags based on the current state of the component.
   * If `hasYtd` is true and `isYtd` is false, and either `hasLtm` is false or `isLtm` is false,
   * then `isYtd` is set to true and `isLtm` is set to false.
   * If `hasLtm` is true and `isLtm` is false, and either `hasYtd` is false or `isYtd` is false,
   * then `isLtm` is set to true and `isYtd` is set to false.
   */
  updatePeriodTypeFlags() {
    if (this.hasYtd && !this.isYtd && (!this.hasLtm || !this.isLtm)) {
      this.isYtd = true;
      this.isLtm = false;
    } else if (this.hasLtm && !this.isLtm && (!this.hasYtd || !this.isYtd)) {
      this.isLtm = true;
      this.isYtd = false;
    }
  }
  /**
   * Finds the period type configuration based on the given period type.
   * @param periodType - The period type to search for.
   * @returns The period type configuration if found, otherwise undefined.
   */
  findPeriodTypeConfiguration(periodType: string) {
    return this.pageConfigResponse?.kpiConfigurationData.find(
      (x) => x.aliasName === periodType
    );
  }
  /**
   * Sets the page load status based on the current state of the component.
   * If the component is in LTM mode and the LTM page load flag is set, it returns true and resets the flag.
   * If the component is in YTD mode and the YTD page load flag is set, it returns true and resets the flag.
   * Otherwise, it returns false.
   *
   * @returns {boolean} The page load status.
   */
  setPageLoad() {
    let isPagLoad = false;
    if (this.isLtm && this.isLtmPageLoad) {
      isPagLoad = true;
      this.isLtmPageLoad = false;
    } else if (this.isYtd && this.isYtdPageLoad) {
      isPagLoad = true;
      this.isYtdPageLoad = false;
    }
    return isPagLoad;
  }
  /**
   * Returns the sort order for the ImpactKPIs.
   * The sort order is determined based on the `orderType` property of the `modelImpactKpi` object.
   * If the `orderType` is `OrderTypesEnum.LatestOnRight`, the sort order is ascending (1).
   * Otherwise, the sort order is descending (-1).
   * The sort order is applied on the `Year` and `Month` fields of the ImpactKPIs.
   *
   * @returns An array of sort order objects, each containing a `field` and an `order` property.
   */
  getSortOrder() {
    const order =
      this.modelImpactKpi.orderType.type == OrderTypesEnum.LatestOnRight
        ? 1
        : -1;
    return [
      { field: ImpactKPIConstants.Year, order },
      { field: ImpactKPIConstants.Month, order },
    ];
  }
  /**
   * Exports the Impact KPI values for a portfolio company.
   */
  exportImpactKpiValues() {
    const canExport = this.impactKPIPermissions?.map(access => access.canExport);
    if (canExport.includes(true)) {
      let searchFilter = this.searchFilter || {
        sortOrder: this.getSortOrder(),
        periodType: this.modelImpactKpi.periodType.type,
      };

      if (searchFilter.periodType == ImpactKPIConstants.DateRange) {
        searchFilter.startPeriod = this.miscService.getDate(
          searchFilter.startPeriod || this.modelImpactKpi.startPeriod
        );
        searchFilter.endPeriod = this.miscService.getDate(
          searchFilter.endPeriod || this.modelImpactKpi.endPeriod
        );
      }

      this.isLoader = true;
      this.portfolioCompanyService
        .exportImpactKPIsList({
          companyId: this.modelList.portfolioCompanyID?.toString(),
          portfolioCompanyID: this.modelList.portfolioCompanyID?.toString(),
          paginationFilter: {
            first: 0,
            rows: 1000,
            globalFilter: null,
            sortField: ImpactKPIConstants.ImpactKPI,
            multiSortMeta: this.miscService.financialKPIMultiSortMeta,
            sortOrder: -1,
          },
          searchFilter: searchFilter,
          kPIFilter: {
            currency: this.currData?.currency,
            decimaPlace: this.modelImpactKpi.decimalPlaces?.type,
            valueType: this.masterKpiValueUnit?.typeId,
          },
          moduleId: KPIModulesEnum.Impact,
          Unit: this.masterKpiValueUnit.typeId,
          IsSpotRate : this.kpiCurrencyFilterModel.isSpotRate == null ? false : this.kpiCurrencyFilterModel.isSpotRate,
          SpotRateDate:this.kpiCurrencyFilterModel.isSpotRate ? this.datePipe.transform(this.kpiCurrencyFilterModel.spotRateDate, 'yyyy-MM-dd') : null,
          SpotRate:this.kpiCurrencyFilterModel.isSpotRate ? this.kpiCurrencyFilterModel.spotRate : null,
          currencyCode: this.kpiCurrencyFilterModel?.currencyCode,
          reportingCurrencyCode: this.currencyCode ,
          currencyRateSource: this.kpiCurrencyFilterModel?.currencyRateSource,
        })
        .subscribe({
          next: (response) => {
            this.miscService.downloadExcelFile(response);
            this.isLoader = false;
          },
          error: (_error) => {
            this.isLoader = false;
            this.message = this.miscService.showAlertMessages(
              ImpactKPIConstants.Error,
              ErrorMessage.SomethingWentWrong
            );
          },
        });
    }
    else {
      this.showErrorToast(ErrorMessage.NoAccess);
    }
  }
  /**
   * Initializes the edit operation for a specific row and column in the portfolioCompany-ImpactKPI component.
   * 
   * @param rowData - The data of the row being edited.
   * @param column - The column being edited.
   */
  onEditInit(rowData: any, column: any) {
    if (!this.canEdit()) {
      this.showErrorToast(ErrorMessage.NoAccess);
      return;
    }
    if (this.ErrorNotation) {
      return;
    }

    if (this.hasErrorNotationOrKPIField(column)) {
      return;
    }

    if (this.isConvertedValue(rowData)) {
      this.toastrService.warning(this.editErrorForConvertedValue, '', { positionClass: ImpactKPIConstants.ToastCenterCenter });
      return;
    }

    if (!this.hasPermissionToEdit(rowData)) {
      return;
    }

    if (this.shouldUpdateInfo(rowData)) {
      this.infoUpdate = true;
    } else if (!rowData.IsHeader) {
      this.showUploadPopup(rowData, column);
    } else {
      this.showErrorToast(GlobalConstants.CellEditError);
    }
  }
  
  /**
   * Determines if the user has edit permissions.
   *
   * This method checks the `KPIPermissions` array to see if any of the permissions
   * include the ability to edit. It returns `true` if at least one permission allows editing,
   * otherwise it returns `false`.
   *
   * @returns {boolean} `true` if the user can edit, `false` otherwise.
   */
  private canEdit(): boolean {
    const canEdit = this.impactKPIPermissions?.map(access => access.canEdit);
    return canEdit.includes(true);
  }

  /**
   * Checks if there is an error notation.
   *
   * @returns {boolean} - Returns `true` if there is an error notation, otherwise `false`.
   */
  private hasErrorNotationOrKPIField(column: any): boolean {
    return this.ErrorNotation || column?.field == ImpactKPIConstants.KPI;
  }

  /**
   * Checks if the value in the given row data is converted.
   * 
   * @param rowData - The data of the row to check.
   * @returns `true` if the value is converted and the 'KPI Info' field is '$', otherwise `false`.
   */
  private isConvertedValue(rowData: any): boolean {
    return this.isValueConverted && rowData['KPI Info'] === '$';
  }

  /**
   * Checks if the user has permission to edit the given row data.
   *
   * @param rowData - The data of the row to check permissions for.
   * @returns `true` if the user has permission to edit and the row is not a formula, otherwise `false`.
   */
  private hasPermissionToEdit(rowData: any): boolean {
    return this.permissionService.checkUserPermission(
      this.subFeature.InvestmentKPIs,
      ActionsEnum[ActionsEnum.canEdit],
      this.id
    ) && !rowData?.IsFormula;
  }

  /**
   * Determines whether the information should be updated based on the provided row data.
   *
   * @param rowData - The data of the row to be evaluated.
   * @returns `true` if the information should be updated; otherwise, `false`.
   */
  private shouldUpdateInfo(rowData: any): boolean {
    return Number(this.masterKpiValueUnit.typeId) != FinancialValueUnitsEnum.Absolute &&
      !this.ErrorNotation &&
      !rowData.IsHeader;
  }

  /**
   * Displays the upload popup with the provided row data and column information.
   *
   * @param rowData - The data for the selected row.
   * @param column - The column information for the selected row.
   * @private
   */
  private showUploadPopup(rowData: any, column: any): void {
    this.uniqueModuleCompany = {
      moduleId: this.kpiModuleId,
      companyId: this.modelList.portfolioCompanyID,
      valueType: this.setLtmYtdPeriodType(this.tabName),
    };
    this.dataRow = rowData;
    this.dataColumns = column;
    this.isUploadPopupVisible = true;
  }
  /**
   * Closes the information update.
   */
  CloseInfo() {
    this.infoUpdate = false;
  }
  /**
   * Handles the submit button event.
   * @param results - The results object containing the code and message.
   */
  onSubmitButtonEvent(results: any) {
    if (results.code != null && results.code.trim().toLowerCase() == ImpactKPIConstants.ok) {
      this.showSuccessToast(results.message);
      this.isUploadPopupVisible = false;
      this.isValueUpdated = !this.isValueUpdated;
    } else {
      this.showErrorToast(results.message);
      this.isUploadPopupVisible = false;
    }
    this.getPortfolioCompanyMasterKPIValues(null);
  }
  /**
   * Displays a success toast notification.
   * 
   * @param message - The message to be displayed in the toast.
   * @param title - The title of the toast (optional).
   * @param position - The position of the toast on the screen (optional, default: "toast-center-center").
   */
  showSuccessToast(
    message: string,
    title: string = "",
    position: string = ImpactKPIConstants.ToastCenterCenter
  ): void {
    this.toastrService.success(message, title, { positionClass: position });
  }
  /**
   * Handles the cancel button event.
   */
  cancelButtonEvent() {
    this.isUploadPopupVisible = false;
  }
}