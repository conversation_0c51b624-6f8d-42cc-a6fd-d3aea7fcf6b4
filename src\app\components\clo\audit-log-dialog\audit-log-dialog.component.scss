// Variables
$color-primary: #4061C7;
$color-white: #FFFFFF;
$color-black: #000000;
$color-background: #EBF3FF;
$color-light-blue: #F5F9FF;
$color-gray-100: #F2F2F2;
$color-gray-200: #E9ECEF;
$color-gray-300: #666666;
$color-gray-400: #333333;
$color-gray-500: #1A1A1A;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
}

@mixin ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    
    &-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &-thumb {
      background: #888;
      border-radius: 3px;
      
      &:hover {
        background: #555;
      }
    }
  }
}

// Dialog Layout
.audit-log-dialog {
  .header-section {
    padding: 15px;
    background-color: $color-background;
    border-radius: 4px;
  }

  .audit-header {
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    margin-bottom: 8px;
    color: $color-primary;
  }

  .audit-subtitle, .audit-col-header {
    font-size: 14px;
    color: $color-gray-400;
  }

  .audit-subtitle {
    font-weight: 500;
  }

  .audit-col-header {
    font-weight: 700;
  }
  
  .grid-content{
    height: 100%;
  }
}

// Grid Styles
.audit-grid, :host ::ng-deep .k-grid {
  height: 100% !important;
  position: relative;
  z-index: 1;

  .k-grid-header {
    background-color: #FAFAFA;
    
    .k-header {
      font-weight: 700;
      color: $color-gray-300;
    }
  }

  .k-grid-content {
    height: calc(100% - 32px) !important;
    
    td {
      background-color: $color-white;
      font-size: 14px;
      font-weight: 400;
      color: $color-gray-500;
    }
  }
}

// Source File Button
.source-file-button {
  @include flex-center;
  padding: 0;
  background: $color-light-blue;
  border: 0;
  height: 32px;
  border-radius: 8px;
  overflow: hidden;

  .file-section {
    @include flex-center;
    padding: 8px;
    flex-grow: 1;

    .excel-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }

    .file-name {
      @include ellipsis;
      color: $color-gray-500;
      font-size: 12px;
      font-weight: 500;
      max-width: 175px;
    }
  }

  .download-section {
    padding: 8px;
    cursor: pointer;
    border-left: 1px solid $color-gray-200;

    &:hover {
      background-color: #e6f0ff;
    }

    .download-icon {
      width: 30px;
      height: 30px;
    }
  }
}

// Comment Styles
.comment-container {
  position: relative;

  .comment-toggle-btn {
    @include flex-center;
    justify-content: space-between;
    height: 32px;
    width: 125px;
    padding: 8px 16px;
    color: $color-black;
    font-weight: 500;
    font-size: 12px;
    background-color: $color-light-blue;
    border: 1px solid $color-gray-100;
    border-width: 1px 0 0 0;
    border-radius: 4px 0 0 0;
    cursor: pointer;
    text-align: left;

    .toggle-text {
      @include ellipsis;
      max-width: calc(100% - 20px);
    }

    .toggle-arrow {
      font-size: 10px;
      color: $color-gray-300;
    }
  }

  .comment-popup {
    display: none;
    position: fixed;
    margin-top: 5px;
    width: 180px;
    max-height: 200px;
    background: $color-background;
    border: 1px solid $color-gray-200;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 8px 12px;
    z-index: 99999;
    overflow-y: auto;
    overflow-x: hidden;
    @include scrollbar;

    &.show-popup {
      display: block;
    }

    p {
      margin: 0;
      font-size: 12px;
      font-weight: 400;
      color: $color-black;
      white-space: normal;
      word-wrap: break-word;
      width: 100%;
    }
  }
}

// Utility Classes
.user-avatar {
  @include flex-center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(180deg, #021155 0%, #9c27b0 100%);
  color: $color-white;
}

.hide { display: none; }
.show { display: block; }

.supporting-container {
  display: flex;
}

.comment-tooltip {
  padding: 12px;
  background-color: $color-white;
  border: 1px solid $color-gray-200;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-width: 300px;
  white-space: pre-wrap;
  word-wrap: break-word;
  z-index: 1000;
}

.comment-icon {
  @include flex-center;
  &:hover { opacity: 0.8; }
}

// Dialog Actions
kendo-dialog-actions {
  padding: 15px;
  border-top: 1px solid $color-gray-200;
}



