import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, convertToParamMap, Router } from '@angular/router';
import { AddInvestmentCompanyComponent } from './add-investment-company.component';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';
import { InputsModule } from '@progress/kendo-angular-inputs';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DatePipe } from '@angular/common';
import { InvestCompanyService } from '../investmentcompany/investmentcompany.service';
import { ToastrService } from 'ngx-toastr';
import { of } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';

describe('AddInvestmentCompanyComponent', () => {
  let component: AddInvestmentCompanyComponent;
  let fixture: ComponentFixture<AddInvestmentCompanyComponent>;
  let router: Router;
  let investCompanyService: jasmine.SpyObj<InvestCompanyService>;
  let toastrService: jasmine.SpyObj<ToastrService>;

  beforeEach(async () => {
    const investCompanyServiceSpy = jasmine.createSpyObj('InvestCompanyService', ['saveInvestCompany']);
    investCompanyServiceSpy.goToStep$ = of(); // <-- Provide as observable
    const toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning']);

    await TestBed.configureTestingModule({
      declarations: [AddInvestmentCompanyComponent],
      imports: [
        ReactiveFormsModule,
        DateInputsModule,
        InputsModule,
        BrowserAnimationsModule,
        HttpClientTestingModule
      ],
      providers: [
        DatePipe,
        { provide: Router, useValue: { navigate: jasmine.createSpy('navigate') } },
         { 
      provide: ActivatedRoute, 
      useValue: { 
        paramMap: of(convertToParamMap({ id: null })), // <-- Fix here
        snapshot: { params: {} }, 
        queryParams: of({}) 
      } 
    },
        { provide: 'BASE_URL', useValue: 'http://localhost/' },
        { provide: InvestCompanyService, useValue: investCompanyServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy }
      ]
    }).compileComponents();

    investCompanyService = TestBed.inject(InvestCompanyService) as jasmine.SpyObj<InvestCompanyService>;
    toastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AddInvestmentCompanyComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form with empty values', () => {
    const formValues = component.companyForm.value;
    expect(formValues.companyName).toBe('');
    expect(formValues.domicile).toBe('');
    expect(formValues.incorporationDate).toBe('');
    expect(formValues.firstClose).toBe('');
    expect(formValues.investmentPeriodEndDate).toBe('');
    expect(formValues.maturityDate).toBe('');
    expect(formValues.custodian).toBe('');
    expect(formValues.legalCounsel).toBe('');
    expect(formValues.finalClose).toBe('');
    expect(formValues.commitments).toBe('');
    expect(formValues.administrator).toBe('');
    expect(formValues.portfolioAdvisor).toBe('');
    expect(formValues.baseCurrency).toBe('');
    expect(formValues.listingAgent).toBe('');
    expect(formValues.SummaryCount).toBe('');
  });

  it('should set isEdited to true when form values change', () => {
    component.companyForm.controls['companyName'].setValue('Test Company');
    expect(component.isEdited).toBeTrue();
  });

  it('should set isSummarySaved to true when saving step 2', () => {
  component.step = 2;
  // Set all required fields to valid values
  component.companyForm.controls['companyName'].setValue('Test Company');
  component.companyForm.controls['domicile'].setValue('Test Domicile');
  component.companyForm.controls['incorporationDate'].setValue(new Date());
  component.companyForm.controls['firstClose'].setValue(new Date());
  component.companyForm.controls['investmentPeriodEndDate'].setValue(new Date());
  component.companyForm.controls['maturityDate'].setValue(new Date());
  component.companyForm.controls['custodian'].setValue('Test Custodian');
  component.companyForm.controls['legalCounsel'].setValue('Test Legal');
  component.companyForm.controls['finalClose'].setValue(new Date());
  component.companyForm.controls['commitments'].setValue('Test Commitments');
  component.companyForm.controls['administrator'].setValue('Test Admin');
  component.companyForm.controls['portfolioAdvisor'].setValue('Test Advisor');
  component.companyForm.controls['baseCurrency'].setValue('USD');
  component.companyForm.controls['listingAgent'].setValue('Test Agent');
  component.companyForm.controls['SummaryCount'].setValue('Test Summary');

  component.onSubmit();
  expect(component.isSummarySaved).toBeTrue();
});

  it('should navigate to /investment-company when onCancel is called', () => {
    component.onCancel();
    expect(router.navigate).toHaveBeenCalledWith(['/investment-company']);
  });

  it('should update charCount and set isSummaryEdited to true when updateCharCount is called', () => {
    component.companyForm.controls['SummaryCount'].setValue('Test Summary');
    component.updateCharCount();
    expect(component.charCount).toBe(12);
    expect(component.isSummaryEdited).toBeTrue();
  });

  it('should set current date for a control when setCurrentDate is called', () => {
    component.setCurrentDate('incorporationDate');
    const controlValue = component.companyForm.controls['incorporationDate'].value;
    expect(controlValue).toEqual(jasmine.any(Date));
  });

  it('should show error message when form is incomplete on submit', () => {
    expect(component.companyForm.controls['companyName'].value).toBe('');
    component.onSubmit();
    expect(toastrService.error).toHaveBeenCalledWith(component.errorMessage, "", { positionClass: "toast-center-center" });
  });

  it('should call saveInvestCompany when saveInvestmentCompany is called', () => {
    investCompanyService.saveInvestCompany.and.returnValue(of({}));
    component.saveInvestmentCompany();
    expect(investCompanyService.saveInvestCompany).toHaveBeenCalled();
  });

  it('should reset investment summary when onResetinvestmentsummary is called', () => {
    component.companyForm.controls['SummaryCount'].setValue('Test Summary');
    component.onResetinvestmentsummary();
    expect(component.companyForm.controls['SummaryCount'].value).toBe('');
    expect(component.isEdited).toBeFalse();
    expect(component.submitted).toBeFalse();
  });

  it('should reset company facts when onResetCompanyFacts is called', () => {
    component.companyForm.controls['companyName'].setValue('Test Company');
    component.onResetCompanyFacts();
    expect(component.companyForm.controls['companyName'].value).toBeNull();
    expect(component.isEdited).toBeFalse();
    expect(component.submitted).toBeFalse();
  });

  it('should reset form based on step when onReset is called', () => {
    component.step = 1;
    component.onReset();
    expect(component.companyForm.controls['companyName'].value).toBeNull();

    component.step = 2;
    component.companyForm.controls['SummaryCount'].setValue('Test Summary');
    component.onReset();
    expect(component.companyForm.controls['SummaryCount'].value).toBe('');
  });
});
