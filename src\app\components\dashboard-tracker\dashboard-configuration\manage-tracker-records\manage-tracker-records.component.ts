import { Component, Input, OnChanges, OnInit, OnDestroy, <PERSON>ry<PERSON>ist, SimpleChanges, ViewChildren } from '@angular/core';
import {ExpansionPanelComponent, ExpansionPanelActionEvent} from "@progress/kendo-angular-layout";
import { chevronUpIcon, SVGIcon } from "@progress/kendo-svg-icons";
import { DashboardTrackerService } from 'src/app/services/dashboard-tracker.service';
import { ToastrService } from 'ngx-toastr';
import { DashboardConfigurationConstants, DashboardTrackerToasterMessages } from 'src/app/common/constants';
import { FeaturesEnum } from 'src/app/services/permission.service';
import { MaptoType } from '../../model/dashboard-tracker-config.model';

@Component({
  selector: 'app-manage-tracker-records',
  templateUrl: './manage-tracker-records.component.html',
  styleUrls: ['./manage-tracker-records.component.scss']
})
export class ManageTrackerRecordsComponent implements OnInit, OnChanges, On<PERSON>estroy {
  @Input() isNewColumnAdded: boolean = false;
  @Input() maptoFields: { text: string, value: number, type: MaptoType }[] = [];
  @ViewChildren(ExpansionPanelComponent)
  panels: QueryList<ExpansionPanelComponent>;
  arrowUpIcon: SVGIcon = chevronUpIcon;
  showDropdownValues: number = 2;
  dropdownValueMaxLength: number = 10;
  showEditDeleteButton: boolean = false;
  expandedPanel: number = null;
  data: any[] = [];
  confirmDelete: boolean = false;
  deleteIndex: number = null;
  modalTitle: string = 'Delete Confirmation';
  subPermissionService: any;
  canDeleteCOlumnRecord: any;
  private deleteColumnSub: any;
  constructor(
    private readonly dashboardTrackerService: DashboardTrackerService,
    private readonly toastr: ToastrService
  ) { }
  ngOnDestroy(): void {
    if (this.deleteColumnSub) {
      this.deleteColumnSub.unsubscribe();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
     if (changes['isNewColumnAdded'] && changes['isNewColumnAdded'].currentValue !== changes['isNewColumnAdded'].previousValue) {
        this.UpdateColumnsCollection();
     }
  }

ngOnInit(): void {
  this.UpdateColumnsCollection();
}
  private UpdateColumnsCollection() {
    this.dashboardTrackerService.getDashboardTrackerColumnData().subscribe({
      next: (res) => {
        this.data = res.map((item: any) => ({
          id: item.id,
          fieldType: item.fieldTypeName,
          dataType: item.dataTypeName,
          namePattern: item.name,
          createdDate: item.createdOn ? this.formatDate(item.createdOn) : '',
          mapTo: item.mapTo ? (this.maptoFields?.find(i => i.value === item.mapTo)?.text ?? '') : '',
          dropdownList: item.dropdownList ?? undefined,
          isEnable: item.isActive
        }));
      },
      error: (err) => {
        console.error('Error fetching column data', err);
      }
    });
  }

private formatDate(dateString: string): string {
  const date = new Date(dateString);
  return !dateString || isNaN(date.getTime())
    ? ''
    : `${date.getDate().toString().padStart(2, '0')}/${date.toLocaleString('en-US', { month: 'short' })}/${date.getFullYear().toString().slice(-2)}`;
}
  onToggleChange(event: any, item: any): void {
    item.isEnable = event;
  }

  onToggleClick(event: any, item: any): void {
    event.stopPropagation();
  }

  public onAction(ev: ExpansionPanelActionEvent, index: number): void {
    this.panels.forEach((panel, idx) => {
      if (idx !== index && panel.expanded) {
        panel.toggle();
      }
      if(idx === index && ev.action === 'expand'){
        this.showEditDeleteButton = true;
        this.expandedPanel = index;
      }
      if(idx === index && ev.action === 'collapse'){
        this.showEditDeleteButton = false;
      }
    });
  }

  onDeleteClick(index: number): void {
    this.confirmDelete = true;
    this.deleteIndex = index;
  }

  deleteColumnRecord(): void {
    const record = this.data?.[this.deleteIndex];
    if (record?.id) {
      if (this.deleteColumnSub) {
        this.deleteColumnSub.unsubscribe();
      }
      this.deleteColumnSub = this.dashboardTrackerService.deleteDashboardTrackerColumn(record.id).subscribe({
        next: () => {
          if (this.data) {
            this.data.splice(this.deleteIndex, 1);
          }
          this.deleteIndex = null;
          this.confirmDelete = false;
          this.toastr.success(
            DashboardTrackerToasterMessages.DeleteColumnSuccess,
            '',
            { positionClass: 'toast-center-center' }
          );
        },
        error: (err) => {
          console.error('Error deleting column record', err);
          this.confirmDelete = false;
          this.deleteIndex = null;
        }
      });
    }
  }

  cancelDelete(): void {
    this.confirmDelete = false;
    this.deleteIndex = null;
  }
}

