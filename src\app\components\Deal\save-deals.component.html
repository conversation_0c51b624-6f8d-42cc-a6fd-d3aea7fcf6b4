<div class="row main-row mb-4 save-deal" >
    <div class="col-md-12 pl-0 pr-0">
        <div class="card-body">
            <form name="form" class="mt-0 pt-0 pb-0 pl-0 pr-0" (ngSubmit)="f.form.valid && save(f);" #f="ngForm"
                novalidate>
                <div class="col-lg-12 col-md-12 col-sm-12 ">
                    <div class="pb-2 TextTruncate" title="{{pageDetails.displayName}}">
                        <strong>{{pageDetails.displayName}}</strong></div>
                    <div *ngIf="!loading && dealData !== undefined && dealData !== null && dealData.length > 0"
                        class="dealsContainer row d-flex dealDataContainer"
                        [style]="(customFieldsData !== undefined && customFieldsData.length > 12) ? 'overflow-y: auto' :''">
                        <ng-container *ngFor="let dealDetails of dealData">
                            <div class="col-sm-6 col-md-3 padding-rem" *ngIf="dealDetailsConstants.FundName == dealDetails.name">
                                <div class="mandatory-label TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                <kendo-combobox [required]="true" [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                    [(ngModel)]="selectedFundDetails" #Funds="ngModel" [fillMode]="'flat'" [filterable]="true"
                                    name="Funds" [virtual]="virtual"
                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                    [data]="fundList" [filterable]="true" [value]="selectedFundDetails" [valuePrimitive]="false"
                                    textField="fundName" placeholder="Select Fund" valueField="fundID">
                                    <ng-template kendoComboBoxItemTemplate let-fundList>
                                        <div title="{{fundList.fundName}}" class="TextTruncate"> {{fundList.fundName}}
                                        </div>
                                    </ng-template>
                                </kendo-combobox>
                                <div *ngIf="f.submitted && !fund.valid" class="text-danger">Fund is required
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-3 padding-rem" *ngIf="dealDetailsConstants.CompanyName == dealDetails.name">
                                <div class="mandatory-label TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                <kendo-combobox [required]="true" [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                    [(ngModel)]="selectedPortfolioCompanyDetails" #portfolioCompany="ngModel" [fillMode]="'flat'" [filterable]="true"
                                    name="portfolioCompany" [virtual]="virtual"
                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                    [data]="portfolioCompanyList" [filterable]="true" [value]="selectedPortfolioCompanyDetails" [valuePrimitive]="false"
                                    textField="companyName" placeholder="Select Company" valueField="portfolioCompanyID">
                                    <ng-template kendoComboBoxItemTemplate let-portfolioCompanyList>
                                        <div title="{{portfolioCompanyList.companyName}}" class="TextTruncate">
                                            {{portfolioCompanyList.companyName}} </div>
                                    </ng-template>
                                </kendo-combobox>
                                <div *ngIf="f.submitted && !portfolioCompany.valid" class="text-danger">Portfolio
                                    company is required
                                </div>
                            </div>

                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.Currency == dealDetails.name">
                                <div class="mandatory-label TextTruncate Caption-M label-color"
                                    title="{{dealDetails.displayName}}">{{dealDetails.displayName}}</div>
                                    <kendo-combobox [required]="true" [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                    [(ngModel)]="model.currencyDetail"  #currency="ngModel" [fillMode]="'flat'" [filterable]="true"
                                    name="currency" [virtual]="virtual"
                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                    [data]="masterModel.currencyList" [filterable]="true" [value]="model.currencyDetail" [valuePrimitive]="false"
                                    textField="currency" [placeholder]="'Select '+dealDetails.displayName" valueField="currencyID">
                                    <ng-template kendoComboBoxItemTemplate let-currencyList>
                                        <div title="{{currencyList.currency}}" class="TextTruncate">
                                            {{currencyList.currency}} </div>
                                    </ng-template>
                                </kendo-combobox>
                                    <div *ngIf="f.submitted && !currency.valid" class="text-danger">Currency is required
                                    </div>
                            </div>

                            <div class="col-sm-6 col-md-3 padding-rem "
                                *ngIf="dealDetailsConstants.InvestmentDate == dealDetails.name">
                                <div class="TextTruncate  Caption-M label-color"
                                    title="{{dealDetails.displayName}}">{{dealDetails.displayName}}</div>
                                    <kendo-datepicker calendarType="classic" class="k-picker-custom-flat k-datepicker-height-34" [format]="format"
                                        [fillMode]="'flat'" [placeholder]="'Select '+dealDetails.displayName" id="investmentDate" name="investmentDate"
                                        [(ngModel)]="model.investmentDate" [value]="getFormattedDate(model.investmentDate)"
                                        (valueChange)="onSelectDealInvestmentDate($event)" #investmentDate></kendo-datepicker>
                                <div *ngIf="investmentDate.invalid && (investmentDate.dirty ||f.submitted)"
                                    class="text-danger">
                                    <div *ngIf="f.submitted && !investmentDate.valid" class="text-danger">Investment
                                        Date is required
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.EntryMultiple == dealDetails.name">
                                <div class="TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                <input autocomplete="off" maxlength="10" type="number"
                                    class="form-control TextTruncate eachlabel-padding default-txt" name="entryMultiple"
                                    [(ngModel)]="model.entryMultiple" #entryMultiple="ngModel" validateRequired
                                    [placeholder]="'Enter '+dealDetails.displayName" />
                                <div *ngIf="entryMultiple.invalid && (entryMultiple.dirty ||f.submitted)"
                                    class="text-danger">
                                    <div *ngIf="f.submitted && !entryMultiple.valid" class="text-danger">Entry Multiple
                                        is required
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.DealCustomID == dealDetails.name">
                            <div class="TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                {{dealDetails.displayName}}</div>
                            <input autocomplete="off" maxlength="500" type="text"
                                class="form-control TextTruncate eachlabel-padding default-txt" name="dealCustomId"
                                [(ngModel)]="model.dealCustomID" #dealCustomId="ngModel" validateRequired
                                [placeholder]="'Enter '+dealDetails.displayName" [required]="isUpdate"/>
                            <div *ngIf="isUpdate && dealCustomId.invalid && (dealCustomId.dirty || f.submitted)" 
                                class="text-danger">
                            <div *ngIf="dealCustomId.errors?.required">{{dealDetails.displayName}} is required</div>
                            </div>
                            </div>

                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.EntryOwnershipPercent == dealDetails.name">
                                <div class="TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                <input autocomplete="off" type="number" [placeholder]="'Enter '+dealDetails.displayName"
                                    class="form-control TextTruncate eachlabel-padding default-txt" maxlength="6"
                                    name="entryOwnershipPercent" [(ngModel)]="model.entryOwnershipPercent"
                                    #entryOwnershipPercent="ngModel" validateRequired />
                                <div *ngIf="entryOwnershipPercent.invalid && (entryOwnershipPercent.dirty ||f.submitted)"
                                    class="text-danger">
                                    <div *ngIf="f.submitted && !entryOwnershipPercent.valid" class="text-danger">Entry
                                        Ownership Percent is required
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.CurrentExitOwnershipPercent == dealDetails.name">
                                <div class="TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                <input autocomplete="off" type="number" [placeholder]="'Enter '+dealDetails.displayName"
                                    class="form-control eachlabel-padding default-txt TextTruncate" maxlength="6"
                                    name="currentExitOwnershipPercent" [(ngModel)]="model.currentExitOwnershipPercent"
                                    #currentExitOwnershipPercent="ngModel" validateRequired />
                                <div *ngIf="currentExitOwnershipPercent.invalid && (currentExitOwnershipPercent.dirty ||f.submitted)"
                                    class="text-danger">
                                    <div *ngIf="f.submitted && !currentExitOwnershipPercent.valid" class="text-danger">
                                        Current/Exit Ownership Percent
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.EnterpriseValue == dealDetails.name">
                                <div class="TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                <input autocomplete="off" maxlength="15" numberOnly type="text"
                                    class="form-control TextTruncate eachlabel-padding default-txt"
                                    name="enterpriseValue" [(ngModel)]="model.enterpriseValue"
                                    #enterpriseValue="ngModel" validateRequired
                                    [placeholder]="'Enter '+dealDetails.displayName" />
                                <div *ngIf="enterpriseValue.invalid && (enterpriseValue.dirty ||f.submitted)"
                                    class="text-danger">
                                    <div *ngIf="f.submitted && !enterpriseValue.valid" class="text-danger">Enterprise
                                        Value is required
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.BoardSeat == dealDetails.name">
                                <div class="TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                    <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                    [(ngModel)]="model.dealBoardSeatDetail"   #dealBoardSeat="ngModel" [fillMode]="'flat'" [filterable]="true"
                                    name="dealBoardSeat" [virtual]="virtual"
                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                    [data]="masterModel.dealBoardSeatList" [filterable]="true" 
                                    textField="boardSeat" [placeholder]="'Select '+dealDetails.displayName" valueField="dealBoardSeatID">
                                </kendo-combobox>
                                <div *ngIf="dealBoardSeat.invalid && (dealBoardSeat.dirty ||f.submitted)"
                                    class="text-danger">
                                    <div *ngIf="f.submitted && !dealBoardSeat.valid" class="text-danger">Board Seat is
                                        required
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.ExitMethod == dealDetails.name">
                                <div class="TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                    <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="model.dealExitMethodDetail"
                                        #dealExitMethod="ngModel" [fillMode]="'flat'" [filterable]="true" name="dealExitMethod" [virtual]="virtual"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                        [data]="masterModel.dealExitMethodList" [filterable]="true" [value]="model.dealExitMethodDetail"
                                        [valuePrimitive]="false" textField="exitMethod" [placeholder]="'Select '+dealDetails.displayName"
                                        valueField="dealExitMethodID">
                                        <ng-template kendoComboBoxItemTemplate let-exit>
                                            <div title="{{exit.exitMethod}}" class="TextTruncate">
                                                {{exit.exitMethod}} </div>
                                        </ng-template>
                                    </kendo-combobox>
                                <div *ngIf="dealExitMethod.invalid && (dealExitMethod.dirty ||f.submitted)"
                                    class="text-danger">
                                    <div *ngIf="f.submitted && !dealExitMethod.valid" class="text-danger">Exit Method is
                                        required
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.InvestmentStage == dealDetails.name">
                                <div class="TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                    <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                        [(ngModel)]="model.dealInvestmentStageDetail" #dealInvestmentStage="ngModel" [fillMode]="'flat'" [filterable]="true"
                                        name="dealInvestmentStage" [virtual]="virtual"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                        [data]="masterModel.dealInvestmentStageList" [filterable]="true" [valuePrimitive]="false"
                                        textField="investmentStage" [placeholder]="'Select '+dealDetails.displayName" valueField="dealInvestmentStageID">
                                    </kendo-combobox>
                                <div *ngIf="dealInvestmentStage.invalid && (dealInvestmentStage.dirty ||f.submitted)"
                                    class="text-danger">
                                    <div *ngIf="f.submitted && !dealInvestmentStage.valid" class="text-danger">
                                        Investment Stage is required
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.SecurityType == dealDetails.name">
                                <div class="TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                    <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                        [(ngModel)]="model.dealSecurityTypeDetail" #dealSecurityType="ngModel" [fillMode]="'flat'" [filterable]="true"
                                        name="dealSecurityType" [virtual]="virtual"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                        [data]="masterModel.dealSecurityTypeList" [filterable]="true" [valuePrimitive]="false"
                                        textField="securityType" [placeholder]="'Select '+dealDetails.displayName" valueField="dealSecurityTypeID">
                                    </kendo-combobox>
                                <div *ngIf="dealSecurityType.invalid && (dealSecurityType.dirty ||f.submitted)"
                                    class="text-danger">
                                    <div *ngIf="f.submitted && !dealSecurityType.valid" class="text-danger">Security
                                        Type is required
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.DealSourcing == dealDetails.name">
                                <div class="TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                    <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="model.dealSourcingDetail"
                                        #dealSourcing="ngModel" [fillMode]="'flat'" [filterable]="true" name="dealSourcing" [virtual]="virtual"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                        [data]="masterModel.dealSourcingList" [filterable]="true" [valuePrimitive]="false" textField="dealSourcing"
                                        [placeholder]="'Select '+dealDetails.displayName" valueField="dealSourcingID">
                                    </kendo-combobox>
                                <div *ngIf="dealSourcing.invalid && (dealSourcing.dirty ||f.submitted)"
                                    class="text-danger">
                                    <div *ngIf="f.submitted && !dealSourcing.valid" class="text-danger">Deal Sourcing is
                                        required
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.TransactionRole == dealDetails.name">
                                <div class="TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                    <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                        [(ngModel)]="model.dealTransactionRoleDetail" #dealTransactionRole="ngModel" [fillMode]="'flat'" [filterable]="true"
                                        name="dealTransactionRole" [virtual]="virtual"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                        [data]="masterModel.dealTransactionRoleList" [filterable]="true" [valuePrimitive]="false"
                                        textField="transactionRole" [placeholder]="'Select '+dealDetails.displayName" valueField="dealTransactionRoleID">
                                    </kendo-combobox>
                                <div *ngIf="dealTransactionRole.invalid && (dealTransactionRole.dirty ||f.submitted)"
                                    class="text-danger">
                                    <div *ngIf="f.submitted && !dealTransactionRole.valid" class="text-danger">Currency
                                        is required
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.ValuationMethodology == dealDetails.name">
                                <div class="TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                    <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                    [(ngModel)]="model.dealValuationMethodologyDetail" #dealValuationMethodology="ngModel" [fillMode]="'flat'" [filterable]="true"
                                    name="dealValuationMethodology" [virtual]="virtual"
                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                    [data]="masterModel.dealValuationMethodologyList" [filterable]="true" [valuePrimitive]="false"
                                    textField="valuationMethodology" [placeholder]="'Select '+dealDetails.displayName" valueField="dealValuationMethodologyID">
                                </kendo-combobox>
                                <div *ngIf="dealValuationMethodology.invalid && (dealValuationMethodology.dirty ||f.submitted)"
                                    class="text-danger">
                                    <div *ngIf="f.submitted && !dealValuationMethodology.valid" class="text-danger">
                                        Valuation Methodology is required
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-6 col-md-3 padding-rem"
                                *ngIf="dealDetailsConstants.Customfield == dealDetails.name">
                                <div class="TextTruncate Caption-M label-color" title="{{dealDetails.displayName}}">
                                    {{dealDetails.displayName}}</div>
                                <input *ngIf="isUpdate" autocomplete="off" type="text" class="form-control TextTruncate"
                                    [value]="dealDetails.value == 'NA' ? '' : dealDetails.value"
                                    (blur)="AddOrUpdate($event,dealDetails)"
                                    [placeholder]="'Enter '+dealDetails.displayName" />
                                <input *ngIf="!isUpdate" autocomplete="off" type="text"
                                    class="form-control TextTruncate" [value]=""
                                    (blur)="AddOrUpdate($event,dealDetails)"
                                    [placeholder]="'Enter '+dealDetails.displayName" />
                            </div>
                        </ng-container>

                    </div>
                    <div class="loading-input-controls-manual dealLoader"
                        *ngIf="dealData === undefined || dealData === null || dealData.length === 0">
                        <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                    </div>
                </div>
                <div class="row mr-0 ml-0 fixed-footer" [ngStyle]="{'width': sideNavWidth}">
                    <div class="col-12 col-lg-12 col-md-12 col-sm-12  pr-0 pl-0">
                        <app-static-info-modification-message *ngIf="id !== undefined"></app-static-info-modification-message>
                        <div class="pull-right pt-2 pb-2 pr-3">
                            <div class="loading-input-controls-manual" *ngIf="loading"><i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i></div>
                            <input id="hiddenreloadButton btn-reset-deal"
type="button" value="{{resetText}}" title="{{resetText}}" (click)="formReset(f);" class="nep-button nep-button-secondary reset-update-portfolio-css" />
                            <button id="hiddenSaveButton btn-save-deal"
[disabled]="loading || !f.form.valid" class="btn btn-primary reset-update-portfolio-css ml-2" title="{{title}}">{{title}}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
