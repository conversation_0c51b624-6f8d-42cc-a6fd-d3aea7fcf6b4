@import "../../../../variables";
@import '../../../../assets/dist/css/font.scss';
@import '../../../../variables';
$color-black: #000000;
$color-white: #FFFFFF;
$color-light-blue: #EBF3FF;
$color-active-blue: #D9EDFF;
$color-border: #F2F2F2;
$color-border-active: #93B0ED;
$padding-small: 0.5rem 1rem;
$padding-medium: 10px 1rem;
$border-radius-small: 4px;

.search-text-company {
    font-size: 12px ;
    border-right: none;
    border-left:none;
}
.card-header {
    background: var(--Background-background-primary, #FFFFFF) 0% 0% no-repeat padding-box;
}

.card-header-main {
    font-size: 14px;
}

.card-body {
    margin-bottom: 0px;
}
.custom-height{
    height:48px;
}
strong {
    font-weight: bold;
  }
  
  .fasearchicon {
    cursor: pointer;
  }
  .investment-company-title {
    letter-spacing: normal; 
    align-items: center;
    display: flex;
     font-size: 20px;
     font-weight: 500;
     letter-spacing: normal; 
  }
.company-names-content {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left:10px;
}
.add-company-button-container {
    margin-left: auto;
    display: flex;
    align-items: center;
}
.company-names {
    background: var(--Color-Primary-Primary---40, #EBF3FF);
    height: 48px; 
    border-radius: 4px;
    display: flex; 
    font-size: 16px;
 
}

.company-list {
    display: block;
    flex-direction: column; 
    justify-content: center; 
    align-items: center;
    max-height: 550px; 
    overflow-y: auto;
    
}
.company-card {
    border-bottom: 0.5px solid var(--Border-border-disabled-accent, #93B0ED);
    height: $compnay-cell-height;
    margin-bottom: 0.375rem; 
    display: flex; 
    align-items: center;
    width:100%;
}
.no-companies {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px; 
    padding: 8%; 
    height: 500px;
}
.company-card-header {
    font-size: 14px;
    flex-grow: 1;
    padding: 6px 0px;
}

.welcome-text {
    color: $nep-dark-doc;
    margin-top: $space-12; 
}

.info-text {
    color: $nep-dark-grey-sub-h;
}

.no-data{
    height: 90vh;
}

.mt-60{
    margin-top: $margin-60;
}

.mt-8{
    margin-top: $margin-small;
}

.add-company-button {
    background: var(--Color-Primary-Primary---78, #4061C7);
    height: 32px; 
    border-radius: 4px;
    border: none; 
    cursor: pointer; 
    padding:6px 16px;
}

.add-company-button i {
    margin-right: 8px; 
}

.company-cards{
    margin:20px;
}

.company-card:hover {
    cursor:pointer;
}
.company-list-content{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0.25rem 0px;
}

::-webkit-scrollbar {
    width: $review-page-scroll-width !important;
    color: $review-page-scroll-color !important;  
}

.clo-container {
    .clo-title {
        color: $color-black;
        padding: $padding-small;
        padding-top: 24px;
        padding-bottom: 24px;
        align-items: center;
         display: flex;
    }

    .clo-subtitle {
        font-size: 1rem;
        /* Adjust the font size as needed */
        color: #666;
        /* Adjust the color as needed */
        margin-left: 5px;
        /* Adjust the spacing as needed */
    }

    .card-table {
        .c-table-header {
            background: $color-light-blue;
            padding: $padding-small;
            color: $color-black;
            border-top-right-radius: $border-radius-small;
            border-top-left-radius: $border-radius-small;
            display: flex;
            align-items: center;
        }
        .image-custom{
         padding-right: 8px;
        }
        .clo-table-body {
            .clo-table-content {
                max-height: calc(100vh - 225px);
                border-bottom-left-radius: $border-radius-small;
                border-bottom-right-radius: $border-radius-small;
                overflow-y: scroll;
            }
        }
            
    }

    .delete-icon{
        height: $icon-size;
        width: $icon-size;
        border-radius: 0.25rem;   
        display: flex;
        justify-content: center;
        align-items: center;   
        cursor: pointer;           
    }

    .delete-icon:hover{
        background-color: $delete-on-hover-color;
    }

    .delete-icon:active{
        background-color: $delete-on-click-color;
    }
}