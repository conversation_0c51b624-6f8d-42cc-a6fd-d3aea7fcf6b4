import { Compo<PERSON>, ElementRef, On<PERSON>nit, On<PERSON><PERSON>roy, ViewChild, Input, ChangeDetectorRef } from "@angular/core";
import { HttpClient } from '@angular/common/http';
import * as Highcharts from 'highcharts';
import HC_more from 'highcharts/highcharts-more';

// Initialize required Highcharts modules
HC_more(Highcharts);

declare global {
  interface Window {
    revealBridge: any;
    revealBridgeListener: any;
  }
}

@Component({
  selector: "app-waterfall-chart",
  templateUrl: "./waterfall-chart.component.html",
  styleUrls: ["./waterfall-chart.component.scss"],
})
export class WaterfallChartComponent implements OnInit, OnDestroy {
  // Data properties for revealBridge integration
  data: any = [];
  headers: any = [];
  defaultData: any[] = [];
  
  @ViewChild("container", { static: true }) chartContainer: ElementRef;
  @Input() chartData: any[] = [];
  @Input() apiUrl: string = '';
  @Input() chartTitle: string = '';
  
  Highcharts: typeof Highcharts = Highcharts;
  chart: Highcharts.Chart;
  chartOptions: Highcharts.Options;

  constructor(private http: HttpClient, private ref: ChangeDetectorRef) { }

  /**
   * Converts the provided data into an array of JSON objects suitable for waterfall charts.
   * 
   * @param data - The data to be converted, which should contain metadata and data arrays.
   * @returns An array of JSON objects where each object represents a waterfall data point.
   * 
   * The input data is expected to have the following structure:
   * {
   *   metadata: {
   *     columns: [
   *       { name: string, type: number },
   *       ...
   *     ]
   *   },
   *   data: [
   *     [value1, value2, ...],
   *     ...
   *   ]
   * }
   * 
   * For waterfall charts, the first column should be the category name,
   * and subsequent columns should be numeric values.
   */
  dataToJson(data: any): any[] {
    let propertyNames = [];

    if (!data.metadata.columns) {
      return [];
    }
    for (var c = 0; c < data.metadata.columns.length; c++) {
      var column = data.metadata.columns[c];
      propertyNames.push(column.name);
    }

    let dataObjects = [];
    for (var i = 0; i < data.data.length; i++) {
      var rowData = data.data[i];
      let dataObject: any = {};
      for (var j = 0; j < rowData.length; j++) {
        dataObject[propertyNames[j]] = rowData[j];
      }
      dataObjects.push(dataObject);
    }

    // Convert to waterfall format
    return this.convertToWaterfallFormat(dataObjects, propertyNames);
  }

  /**
   * Converts data objects to waterfall chart format.
   * Assumes first column is the name/category and second column is the value.
   */
  private convertToWaterfallFormat(dataObjects: any[], propertyNames: string[]): any[] {
    if (dataObjects.length === 0 || propertyNames.length < 2) {
      return [];
    }

    const nameColumn = propertyNames[0];
    const valueColumn = propertyNames[1];

    return dataObjects.map((obj, index) => {
      const waterfallItem: any = {
        name: obj[nameColumn],
        y: parseFloat(obj[valueColumn]) || 0
      };

      // You can add logic here to detect intermediate sums or totals
      // based on naming conventions or special markers in your data
      if (obj[nameColumn] && typeof obj[nameColumn] === 'string') {
        const name = obj[nameColumn].toLowerCase();
        if (name.includes('subtotal') || name.includes('intermediate')) {
          waterfallItem.isIntermediateSum = true;
        } else if (name.includes('total') || name.includes('sum')) {
          waterfallItem.isSum = true;
        }
      }

      return waterfallItem;
    });
  }

  fetchChartDataFromApi(apiUrl: string): void {
    console.log('Fetching data from API:', apiUrl);
    
    this.http.get<any[]>(apiUrl).subscribe({
      next: (response: any[]) => {
        console.log('API Response:', response);
        
        if (Array.isArray(response) && response.length > 0) {
          // Validate that the response has the required structure
          const isValidData = response.every(item => 
            item && typeof item === 'object' && 
            'name' in item && 
            (typeof item.y === 'number' || item.isIntermediateSum || item.isSum)
          );
          
          if (isValidData) {
            this.defaultData = response;
            console.log('API data successfully loaded:', this.defaultData);
            this.createChart();
          } else {
            console.error('API response data format is invalid.');
            this.showErrorState('Invalid data format from API');
          }
        } else {
          console.error('API response is empty or invalid.');
          this.showErrorState('No data received from API');
        }
      },
      error: (error: any) => {
        console.error('Error fetching chart data:', error);
        this.showErrorState('Failed to fetch data from API: ' + (error.message || 'Unknown error'));
      }
    });
  }
  
  ngOnInit(): void {
    window.revealBridge = {
      sendMessageToHost: function (data) {
        try {
          var iframe = document.createElement("IFRAME");
          var message = encodeURIComponent(JSON.stringify(data));
          iframe.setAttribute("src", "js-frame:" + message);
          document.documentElement.appendChild(iframe);
          iframe.parentNode.removeChild(iframe);
          iframe = null;
        } catch (e) { }

        try {
          if (window.top && window.top.location) {
            window.top.postMessage(data, window.top.location.origin);
          }
        } catch (e) { }
      },

      notifyExtensionIsReady: function (formatting) {
        if (formatting) {
          this.sendMessageToHost({ message: "ready", formatting: true });
        } else {
          this.sendMessageToHost({ message: "ready" });
        }
      },

      runAction: function (actionName, data) {
        this.sendMessageToHost({
          message: "runAction",
          action: actionName,
          rowData: data,
        });
      },

      openUrl: function (url) {
        this.sendMessageToHost({ message: "openURL", URL: url });
      },
    };

    const processMessageFromHost = (message): void => {
      const trustedOrigin = window.top.location.origin;
      if (message.origin !== trustedOrigin) {
        console.warn(
          `Rejected message from untrusted origin: ${message.origin}. Expected: ${trustedOrigin}`
        );
        return;
      }
      if (!message?.data?.metadata || message.data.message) {
        console.log('Invalid message or message contains a direct message.');
        return;
      }
    
      if (!window.revealBridgeListener) {
        console.log('revealBridgeListener is not defined.');
        return;
      }
    
      console.log('Processing message:', message);
      window.revealBridgeListener.dataReady(message.data);
    };

    window.addEventListener("message", processMessageFromHost, false);

    window.revealBridgeListener = {
      dataReady: (incomingData: any) => {
        this.data = this.dataToJson(incomingData);
        this.createChart();
        this.ref.detectChanges();
      },
    };
    
    window.revealBridge.notifyExtensionIsReady();
    
    // Fallback: Also support manual API URL and chartData for backward compatibility
    if (this.apiUrl && this.apiUrl.trim() !== '') {
      this.fetchChartDataFromApi(this.apiUrl);
    } else if (this.chartData && this.chartData.length > 0) {
      this.createChart();
    } else {
      // Show empty state - waiting for data from revealBridge or manual input
      console.log('Waterfall chart initialized. Waiting for data from revealBridge or manual input.');
      this.createEmptyChart();
    }
  }

  public createChart(): void {
    // Priority: revealBridge data > chartData input > defaultData (from API)
    let data: any[] = [];
    
    if (this.data && Array.isArray(this.data) && this.data.length > 0) {
      // Use data from revealBridge (highest priority)
      data = this.data;
      console.log('Using revealBridge data:', data);
    } else if (this.chartData && this.chartData.length > 0) {
      // Use chartData input (medium priority)
      data = this.chartData;
      console.log('Using input chartData:', data);
    } else if (this.defaultData && this.defaultData.length > 0) {
      // Use defaultData from API (lowest priority)
      data = this.defaultData;
      console.log('Using API defaultData:', data);
    }
    
    console.log('Creating chart with data:', data);
    
    // Check if we have any data to display
    if (!data || data.length === 0) {
      console.warn('No data available for chart creation');
      this.createEmptyChart();
      return;
    }

    // Map input data to Highcharts waterfall format
    const seriesData = data.map(point => {
      const mapped: any = {
        name: point.name,
      };
      if (typeof point.y === 'number') {
        mapped.y = point.y;
      }
      if (point.isIntermediateSum) {
        mapped.isIntermediateSum = true;
      }
      if (point.isSum) {
        mapped.isSum = true;
        mapped.color = '#544fc5'; // Optional: set color for total
      }
      return mapped;
    });

    this.chartOptions = {
      chart: {
        type: 'waterfall'
      },
      title: {
        text: this.chartTitle || 'Waterfall Chart'
      },
      credits: {
        enabled: false
      },
      xAxis: {
        type: 'category'
      },
      yAxis: {
        title: {
          text: 'Value'
        }
      },
      legend: {
        enabled: false
      },
      tooltip: {
        pointFormat: '<b>{point.name}</b><br>{point.y:,.0f}'
      },
      plotOptions: {
        waterfall: {
          dataLabels: {
            enabled: true,
            formatter: function() {
              return Highcharts.numberFormat(this.y / 1000, 0) + 'k';
            }
          }
        }
      },
      series: [{
        type: 'waterfall',
        name: 'Waterfall',
        upColor: Highcharts.getOptions().colors[2],
        color: Highcharts.getOptions().colors[3],
        data: seriesData
      }],
      exporting: {
        enabled: true
      },
      accessibility: {
        enabled: true
      }
    };

    this.chart = Highcharts.chart(this.chartContainer.nativeElement, this.chartOptions);
  }

  /**
   * Creates an empty chart with a message when no data is available
   */
  private createEmptyChart(): void {
    const emptyChartOptions: Highcharts.Options = {
      chart: {
        type: 'column'
      },
      title: {
        text: 'No Data Available',
        style: {
          color: '#999',
          fontSize: '18px'
        }
      },
      subtitle: {
        text: 'Please provide an API URL to load dynamic data',
        style: {
          color: '#666',
          fontSize: '14px'
        }
      },
      credits: {
        enabled: false
      },
      xAxis: {
        categories: []
      },
      yAxis: {
        title: {
          text: 'Value'
        }
      },
      series: [],
      plotOptions: {
        series: {
          dataLabels: {
            enabled: false
          }
        }
      }
    };

    // Destroy existing chart if it exists
    if (this.chart) {
      this.chart.destroy();
    }

    this.chart = Highcharts.chart(this.chartContainer.nativeElement, emptyChartOptions);
  }

  /**
   * Shows error state with custom message
   */
  private showErrorState(errorMessage: string): void {
    const errorChartOptions: Highcharts.Options = {
      chart: {
        type: 'column'
      },
      title: {
        text: 'Error Loading Data',
        style: {
          color: '#ff6b6b',
          fontSize: '18px'
        }
      },
      subtitle: {
        text: errorMessage,
        style: {
          color: '#666',
          fontSize: '14px'
        }
      },
      credits: {
        enabled: false
      },
      xAxis: {
        categories: []
      },
      yAxis: {
        title: {
          text: 'Value'
        }
      },
      series: [],
      plotOptions: {
        series: {
          dataLabels: {
            enabled: false
          }
        }
      }
    };

    // Destroy existing chart if it exists
    if (this.chart) {
      this.chart.destroy();
    }

    this.chart = Highcharts.chart(this.chartContainer.nativeElement, errorChartOptions);
  }

  /**
   * Method to manually update chart data
   */
  updateChartData(newData: any[]): void {
    console.log('Updating chart with new data:', newData);
    this.defaultData = newData;
    this.createChart();
  }

  /**
   * Method to refresh chart data from API
   */
  refreshFromApi(): void {
    if (this.apiUrl && this.apiUrl.trim() !== '') {
      this.fetchChartDataFromApi(this.apiUrl);
    } else {
      console.warn('No API URL provided for refresh');
    }
  }


  ngOnDestroy(): void {
    if (this.chart) {
      this.chart.destroy();
    }
  }
}