<div class="clo-page-header">
<div class="kpi-history-container">
<div class="page-header"> 
  <span class="heder-text">{{kpiTab.aliasName}}</span> 
  <a href="javascript:void(0)" class="text-link" (click)="redirecttoCloSummary()">Back</a>
</div>
<div *ngFor="let table of kpiTab.tableList">
  <div *ngIf="table.isShow" class="table-container" >
    <app-flat-table [isShouldFetchDataOnLoad]="true"
                    [isStaticTable] = "table.isStaticTable"
                    [companyID]="cloId"
                    [tableType]="table.tableType"
                    [tableTitle]="table.aliasName"
                    [tableName]="table.tableName"
                    [isCompositeRowFilterRequired]="kPISummary==table.tableId?false:true"
                    [tableId]="table.tableId"
                    [canImport] = "checkTablePermissions(table.tableId,CAN_IMPORT)"
                    [canExport] = "checkTablePermissions(table.tableId,CAN_EXPORT)"
                    [canEdit] = "checkTablePermissions(table.tableId,CAN_EDIT)"
                    [isCLO]="true"
                  >
    </app-flat-table>
  </div>
</div>
</div>
</div>
