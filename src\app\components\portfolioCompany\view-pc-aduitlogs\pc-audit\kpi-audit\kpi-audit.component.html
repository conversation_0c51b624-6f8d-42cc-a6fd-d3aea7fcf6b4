<div class="row mr-0 ml-0 kpi-audit-component">
  <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0">
    <div class="row mr-0 ml-0 audit-header">
      <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0">
        <div class="float-left audit-kpi-title">
          <div class="d-inline-block">
            <div class="pt-1">
              <img src="assets/dist/images/kpi-detail.svg" alt="audit-icon" class="audit-icon">
            </div>
          </div>
          <div class="d-inline-block title-w-block">
            <div title="{{auditInfo?.kpi}}" class="Heading1-M TextTruncate title-w">{{auditInfo?.kpi}}</div>
            <div title="{{auditInfo?.moduleName}}" class="S-R sub-title-block">{{auditInfo?.moduleName}}</div>
          </div>
        </div>
        <div class="float-right audit-sub-section" *ngIf="!isModuleIdInCapTableRange">
          <div class="d-inline-block audit-pr">
            <div class="audit-sub-header XS-R">
              Period
            </div>
            <div class="Heading2-R sub-section-title S-B">
              {{kpiAuditModel.Period}}
            </div>
          </div>
          <div class="d-inline-block audit-pr" *ngIf="auditInfo?.kpiInfo == kpiInfoCurrency">
            <div class="audit-sub-header XS-R">
              Reporting Currency
            </div>
            <div class="Heading2-R sub-section-title S-B">
              {{currency}}
            </div>
          </div>
          <div class="d-inline-block">
            <div class="audit-sub-header XS-R">
              Value Type
            </div>
            <div class="Heading2-R sub-section-title S-B">
              {{kpiAuditModel.ValueType}}
            </div>
          </div>
        </div>
        <div class="float-right audit-sub-section" *ngIf="isModuleIdInCapTableRange">
          <div class="d-inline-block audit-pr">
            <div class="audit-sub-header XS-R">
              As Of Period
            </div>
            <div class="Heading2-R sub-section-title S-B">
              {{kpiAuditModel.AsOfPeriod}}
            </div>
          </div>
          <div class="d-inline-block audit-pr" *ngIf="auditInfo?.kpiInfo == kpiInfoCurrency">
            <div class="audit-sub-header XS-R">
              Reporting Currency
            </div>
            <div class="Heading2-R sub-section-title S-B">
              {{auditInfo?.currencyCode}}
            </div>
          </div>
          <div class="d-inline-block" *ngIf="kpiAuditModel.ColumnKpiId == null || kpiAuditModel.ColumnKpiId == 0">
            <div class="audit-sub-header XS-R">
              Period
            </div>
            <div class="Heading2-R sub-section-title S-B">
              {{kpiAuditModel.Period}}
            </div>
          </div>
          <div class="d-inline-block column-kpi-w" *ngIf="kpiAuditModel.ColumnKpiId!=null && kpiAuditModel.ColumnKpiId > 0">
            <div class="audit-sub-header XS-R">
              Column Name
            </div>
            <div title="{{kpiAuditModel.ColumnKpi}}" class="Heading2-R sub-section-title S-B TextTruncate">
              {{kpiAuditModel.ColumnKpi}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0 pc-audit-table">
    <div class="row mr-0 ml-0">
      <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pc-audit-desc border-bottom">
        <div class="content">
          Audit Log
        </div>
        <div class="Caption-R TextTruncate sub-title-audit">
          All activity history is listed as individual items, starting with the most recent
        </div>
      </div>
      <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0">
        <kendo-grid [kendoGridBinding]="auditLogData" scrollable="virtual" [rowHeight]="44" [resizable]="true"
          class="k-grid-border-right-width k-grid-outline-none custom-kendo-cab-table-grid audit-log-grid">
          <kendo-grid-column [width]="200" title="Current Value">
            <ng-template kendoGridHeaderTemplate>
              <span class="S-M" [ngClass]="auditInfo?.kpiInfo == 'Text'?'float-left':'float-right'">Current
                Value</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
              <span class="TextTruncate" [ngClass]="auditInfo?.kpiInfo == kpiInfo.Text ? 'text-align-l float-left':'text-align-r float-right'" *ngIf="rowData.newValue!=null;else empty_Text">
                <container-element [ngSwitch]="auditInfo?.kpiInfo">
                  <span [title]="rowData.newValue" *ngSwitchCase="kpiInfo.Text" [innerHtml]="rowData.newValue"
                    class="TextTruncate"></span>
                  <span [title]="rowData.newValue" *ngSwitchCase="kpiInfo.Number"
                    [innerHtml]="isNumberCheck(rowData.newValue) ? (rowData.newValue | number:NumberDecimalConst.noDecimal | minusSignToBrackets) : rowData.newValue"
                    class="TextTruncate"></span>
                  <span [title]="rowData.newValue" *ngSwitchCase="kpiInfo.Currency" class="TextTruncate"
                    [innerHtml]="isNumberCheck(rowData.newValue) ? (rowData.newValue | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : rowData.newValue"></span>
                  <span [title]="rowData.newValue" *ngSwitchCase="kpiInfo.Multiple" class="TextTruncate"
                    [innerHtml]="isNumberCheck(rowData.newValue) ? (rowData.newValue | number: NumberDecimalConst.multipleDecimal)+'x': rowData.newValue"></span>
                  <span [title]="rowData.newValue" *ngSwitchCase="kpiInfo.Percentage" class="TextTruncate"
                    [innerHtml]="isNumberCheck(rowData.newValue ) ? (rowData.newValue  | number: NumberDecimalConst.percentDecimal | minusToBracketsWithPercentage: kpiInfo.Percentage): rowData.newValue"></span>
                  <container *ngSwitchDefault>
                  </container>
                </container-element>
              </span>
              <ng-template #empty_Text class="detail-sec">
                <span [ngClass]="auditInfo?.kpiInfo ==kpiInfo.Text ? 'text-align-l float-left':'text-align-r float-right'">NA</span>
            </ng-template>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column [width]="200" title="Old Value">
            <ng-template kendoGridHeaderTemplate>
              <span class="S-M" [ngClass]="auditInfo?.kpiInfo == 'Text'?'float-left':'float-right'">Old Value</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
              <span class="TextTruncate" [ngClass]="auditInfo?.kpiInfo == kpiInfo.Text ? 'text-align-l float-left':'text-align-r float-right'" *ngIf="rowData.oldValue!=null;else empty_Text">
                <container-element [ngSwitch]="auditInfo?.kpiInfo">
                  <span [title]="rowData.oldValue" *ngSwitchCase="kpiInfo.Text" [innerHtml]="rowData.oldValue"
                    class="TextTruncate"></span>
                  <span [title]="rowData.oldValue" *ngSwitchCase="kpiInfo.Number"
                    [innerHtml]="isNumberCheck(rowData.oldValue) ? (rowData.oldValue | number:NumberDecimalConst.noDecimal | minusSignToBrackets) : rowData.oldValue"
                    class="TextTruncate"></span>
                  <span [title]="rowData.oldValue" *ngSwitchCase="kpiInfo.Currency" class="TextTruncate"
                    [innerHtml]="isNumberCheck(rowData.oldValue) ? (rowData.oldValue | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : rowData.oldValue"></span>
                  <span [title]="rowData.oldValue" *ngSwitchCase="kpiInfo.Multiple" class="TextTruncate"
                    [innerHtml]="isNumberCheck(rowData.oldValue) ? (rowData.oldValue | number: NumberDecimalConst.multipleDecimal)+'x': rowData.oldValue"></span>
                  <span [title]="rowData.oldValue" *ngSwitchCase="kpiInfo.Percentage" class="TextTruncate"
                    [innerHtml]="isNumberCheck(rowData.oldValue ) ? (rowData.oldValue  | number: NumberDecimalConst.percentDecimal | minusToBracketsWithPercentage: kpiInfo.Percentage): rowData.oldValue"></span>
                  <container *ngSwitchDefault>
                  </container>
                </container-element>
              </span>             
              <ng-template #empty_Text class="detail-sec">
                <span [ngClass]="auditInfo?.kpiInfo ==kpiInfo.Text ? 'text-align-l float-left':'text-align-r float-right'">NA</span>
            </ng-template>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column [width]="200" title="Source">
            <ng-template kendoGridHeaderTemplate>
              <span class="S-M">Source</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
              {{rowData.source}}
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column [width]="400" title="Source File">
            <ng-template kendoGridHeaderTemplate>
              <div class="doc-support TextTruncate S-M">
                Source File <span> <img tooltipPosition="top" tooltipStyleClass="bg-tooltip-fund-color"
                    [pTooltip]="'Click on file name for individual file download'" class="all-download-icon"
                    src="assets/dist/images/info-icon.svg" alt="info-icon" /></span>
                <span class="pl-2 upload-loader" *ngIf="isDocSupportLoading">
                  <i aria-hidden="true" class="download-circle-btn-loader fa fa-circle-o-notch fa-1x fa-fw"></i>
                </span>
              </div>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
              <div class="float-left circle-doc" title="{{rowData.documentName}}" *ngIf="rowData.documentName!=null">
                <div class="source-doc TextTruncate">
                  <a (click)="downloadFile(rowData.documentId,rowData.documentName, true)">
                    <ng-container [ngSwitch]="rowData.extension">
                      <img *ngSwitchCase="fileExtension.XLSX" [src]="'assets/dist/images/FaRegFileExcel.svg'"
                        alt="xlsx file">
                      <img *ngSwitchCase="fileExtension.XLS" [src]="'assets/dist/images/FaRegFileExcel.svg'"
                        alt="xlsx file">
                      <img *ngSwitchCase="fileExtension.PDF" [src]="'assets/dist/images/Left Icon.svg'"
                        alt="pdf file">
                      <img *ngSwitchCase="fileExtension.ZIP" [src]="'assets/dist/images/FaRegFileArchive.svg'"
                        alt="zip file">
                      <img *ngSwitchCase="fileExtension.PNG" [src]="'assets/dist/images/FaRegFileImage.svg'"
                        alt="image file">
                      <img *ngSwitchCase="fileExtension.JPG" [src]="'assets/dist/images/FaRegFileImage.svg'"
                        alt="image  file">
                      <img *ngSwitchCase="fileExtension.TXT" [src]="'assets/dist/images/FaRegFileWord.svg'"
                        alt="doc file">
                      <img *ngSwitchCase="fileExtension.DOCX" [src]="'assets/dist/images/FaRegFileWord.svg'"
                        alt="doc file">
                      <img *ngSwitchCase="fileExtension.PPTX" [src]="'assets/dist/images/FaRegFilePowerpoint.svg'"
                        alt="ppt file">
                        <img *ngSwitchCase="fileExtension.PPT" [src]="'assets/dist/images/FaRegFilePowerpoint.svg'"
                        alt="ppt file">
                      <img *ngSwitchDefault [src]="'assets/dist/images/FaRegFileArchive.svg'" alt="default file">
                    </ng-container> {{rowData.documentName}}
                  </a>
                </div>
              </div>
              <div class="float-left S-I source-doc-t" *ngIf="rowData.documentName==null || rowData.documentName==''">
                No source file uploaded
              </div>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column [width]="200" title="Supporting Evidence">
            <ng-template kendoGridHeaderTemplate>
              <span class="S-M">Supporting Evidence</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
              <div class="float-left">

                <span class="pr-2" title="Supporting Documents"><a
                    (click)="openDoc(rowData.supportingDocumentsId)"><img src="assets/dist/images/fi-text.svg"
                      alt="text-image" /></a></span>
                <span title="Comments"><a (click)="openCommentsPopUp(rowData.commentId)"><img
                      src="assets/dist/images/fi-message.svg" alt="message-image" /></a></span>
              </div>

            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column [width]="200" title="Created By">
            <ng-template kendoGridHeaderTemplate>
              <span class="S-M">Created By</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
              <div title="{{rowData.uploadedBy}}">
                <div class="text-above-image XS-R">
                  {{rowData.uploadedBy.split(' ')[0].charAt(0).toUpperCase()}}{{rowData.uploadedBy.split('
                  ')[1].charAt(0).toUpperCase()}}
                </div>
                <img src="assets/dist/images/profile-icon.svg" alt="text-image" />
              </div>

            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column [width]="300" title="Date Time">
            <ng-template kendoGridHeaderTemplate>
              <div class="doc-time S-M">Date Time
              </div>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowData>
        <span  title="{{rowData.createdOn}}" class="doc-time">{{rowData.createdOn}}
        </span>
            </ng-template>
          </kendo-grid-column>
        </kendo-grid>
      </div>
    </div>
  </div>
</div>
<div *ngIf="isOpenPopup" class="nep-modal nep-modal-show kpi-audit-page">
  <div class="nep-modal-mask"></div>
  <div class="nep-card nep-card-shadow nep-modal-panel nep-modal-default"
    [ngStyle]="{'width': sideNavWidth,'left': leftWidth}">
    <div class="nep-card-header nep-modal-title M-M">
      <span class="M-M evidence-header">Supporting Evidence</span> <a class="float-right  cursor-filter cursor-pointer" (click)="isOpenPopup = false;">
        <i class="pi pi-times custom-close-icon"></i>
      </a>
    </div>
    <div class="nep-card-body audit-body">
      <div class="row mr-0 ml-0 support-body">
        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0">
          <div class="row mr-0 ml-0">
            <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 support-header">
              <div class="float-left S-B">
                <span *ngIf="isDocument">Supporting Document(s)</span> <span *ngIf="isComments">Comments</span> <span
                  *ngIf="isDocument" class="pl-1"> <img tooltipPosition="top" tooltipStyleClass="bg-tooltip-fund-color"
                    [pTooltip]="'Click on file name for individual file download'" class="all-download-icon"
                    src="assets/dist/images/info-icon.svg" alt="info-icon" /> </span>
                <span class="pl-2 upload-loader" *ngIf="isDocSupportLoading">
                  <i aria-hidden="true" class="download-circle-btn-loader fa fa-circle-o-notch fa-1x fa-fw"></i>
                </span>
              </div>
              <div class="float-right" *ngIf="isDocument && documentData.length > 0">
                <span class="pr-2 upload-loader" *ngIf="isDocLoading">
                  <i aria-hidden="true" class="download-circle-btn-loader fa fa-circle-o-notch fa-1x fa-fw"></i>
                </span>
                <span class="pr-1"> <a><img class="all-download-icon" src="assets/dist/images/all-download.svg"
                      alt="info-icon" /></a></span>
                <span>
                  <a class="S-R download-link" href="javascript:void" (click)="downloadZip()">Consolidated Download</a>
                </span>
              </div>
            </div>
            <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 support-body-content">
              <div class="row mr-0 ml-0">
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0"
                  *ngIf="documentData.length > 0  && isDocument">
                  <div class="float-left circle-doc circle-doc-all" *ngFor="let document of documentData">
                    <div class="source-doc TextTruncate">
                      <a class="S-R source-link" (click)="downloadFile(document.documentId,document.documentName, false)">
                        <ng-container [ngSwitch]="document.extension">
                          <img *ngSwitchCase="fileExtension.XLSX" [src]="'assets/dist/images/FaRegFileExcel.svg'"
                            alt="xlsx file">
                          <img *ngSwitchCase="fileExtension.XLS" [src]="'assets/dist/images/FaRegFileExcel.svg'"
                            alt="xlsx file">
                          <img *ngSwitchCase="fileExtension.PDF" [src]="'assets/dist/images/Left Icon.svg'"
                            alt="pdf file">
                          <img *ngSwitchCase="fileExtension.ZIP" [src]="'assets/dist/images/FaRegFileArchive.svg'"
                            alt="zip file">
                          <img *ngSwitchCase="fileExtension.PNG" [src]="'assets/dist/images/FaRegFileImage.svg'"
                            alt="image file">
                          <img *ngSwitchCase="fileExtension.JPG" [src]="'assets/dist/images/FaRegFileImage.svg'"
                            alt="image file">
                          <img *ngSwitchCase="fileExtension.TXT" [src]="'assets/dist/images/FaRegFileWord.svg'"
                            alt="doc file">
                          <img *ngSwitchCase="fileExtension.DOCX" [src]="'assets/dist/images/FaRegFileWord.svg'"
                            alt="doc file">
                          <img *ngSwitchCase="fileExtension.PPTX" [src]="'assets/dist/images/FaRegFilePowerpoint.svg'"
                            alt="ppt file">
                            <img *ngSwitchCase="fileExtension.PPT" [src]="'assets/dist/images/FaRegFilePowerpoint.svg'"
                            alt="ppt file">
                          <img *ngSwitchDefault [src]="'assets/dist/images/FaRegFileArchive.svg'" alt="defaultfile">
                        </ng-container>
                        {{document.documentName}}</a>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0"
                  *ngIf="documentData.length  == 0 && isDocument">
                  <div class="text-center">
                    <img src="assets/dist/images/empty-state-doc.svg" alt="empty-state" />                  
                  </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0"
                  *ngIf="(commentText == null || commentText == '') && isComments">
                  <div class="text-center empty-comments">
                    <img src="assets/dist/images/empty-state-comments.svg" alt="empty-state" />
                  </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0"
                  *ngIf="isComments && commentText != null">
                  <div class="text-comments S-R">
                    {{commentText}}

                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<app-loader-component *ngIf="isLoading"></app-loader-component>