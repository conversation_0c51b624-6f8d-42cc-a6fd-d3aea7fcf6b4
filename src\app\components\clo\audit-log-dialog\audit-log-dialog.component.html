<kendo-dialog [width]="1242" [height]="550" title="Audit Logs" class="audit-log-dialog" (close)="closeModal()">
<div class="content-dialog">
  <div class="header-section mb-4 d-flex justify-content-between align-items-center">
    <div>
      <h2 class="audit-header">{{rowHeader}}</h2>
      <p class="audit-subtitle">{{tableName}}</p>
    </div>
    <div class="text-center audit-col-header">
      <p>{{columnHeader}}</p>
    </div>
  </div>

  <div class="grid-container">
    <div class="grid-content">
      <kendo-grid [data]="displayData" [resizable]="true">
        <kendo-grid-column [width]="210" field="currentValue" title="Current Value"></kendo-grid-column>
        <kendo-grid-column [width]="210" field="oldValue" title="Old Value">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span>{{ dataItem.oldValue ? dataItem.oldValue : 'NA' }}</span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column [width]="210" field="source" title="Source"></kendo-grid-column>
        <kendo-grid-column [width]="292" field="sourceFile" title="Source File">
          <ng-template kendoGridCellTemplate let-dataItem>
            <div class="{{dataItem.source === 'Manual' ? 'hide' : 'show'}}">
              <div class="source-file-button d-flex align-items-center w-100">
                <div class="file-section">
                  <img src="assets/dist/images/Excel_file.svg" alt="Excel file" class="excel-icon" />
                  <span class="file-name" [title]="dataItem.sourceFile">{{dataItem.sourceFile}}</span>
                </div>
                <div class="download-section" (click)="downloadFile(dataItem)">
                  <img src="assets/dist/images/btn-download.svg" class="download-icon" alt="Download">
                </div>
              </div>
  
            </div>
            <div class="{{dataItem.source === 'Manual' ? 'show' : 'hide'}}">
              <span>No Source file Uploaded</span>
            </div>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column [width]="382" field="supportingEvidence" title="Supporting Evidence">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span [class]="!dataItem.supportingEvidence && !dataItem.comment ? 'show' : 'hide'">NA</span>
            <div [class]="dataItem.source === 'Manual' ? 'show float-left supporting-container' : 'hide'">
              <span class="pr-2">
                  <div [class]="dataItem.supportingEvidence ? 'show' : 'hide'">
                  <div class="source-file-button d-flex align-items-center w-100"
                    aria-label="Download supporting evidence">
                    <div class="file-section">
                    <img src="assets/dist/images/doc-icon.svg" class="excel-icon" />
                    <span class="file-name" [title]="dataItem.supportingEvidence">{{dataItem.supportingEvidence}}</span>
                    </div>
                    <div class="download-section" (click)="downloadFile(dataItem)">
                    <img src="assets/dist/images/btn-download.svg" class="download-icon" alt="Download">
                    </div>
                  </div>
                  </div>
  
              </span>
              <div [class]="dataItem.comment ? 'show comment-container' : 'hide'" >
                <button class="comment-toggle-btn" (click)="toggleComments(dataItem, $event)">
                  <span class="toggle-text">Comments</span>
                  <span class="toggle-arrow">▼</span>
                </button>
                <div class="comment-popup" [class.show-popup]="dataItem.showComments">
                  <p>{{dataItem.comment || 'No comments'}}</p>
                </div>
              </div>
            </div>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column [width]="210"  field="createdBy" title="Created By">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span [title]="dataItem.userName" class="clo-fs-user-info">
              <p class="user-avatar">{{dataItem.userNameSymbol}}</p>
            </span>
          </ng-template>
  
        </kendo-grid-column>
        <kendo-grid-column [width]="210" field="dateTime" title="Date Time"></kendo-grid-column>
      </kendo-grid>
    </div>
    
  </div>
</div>
  
</kendo-dialog>