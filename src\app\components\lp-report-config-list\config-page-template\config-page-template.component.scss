@import "../../../../variables";

.section-container {
  box-shadow: 0px 3px 6px #00000015;
  border: 1px solid $nep-divider;
  border-radius: 4px;

  .section-header {
    border-bottom: 0px;
    box-shadow: 0px 0px 0px;

  }

  .section-content {
    .row.page-section {
      border: 1px solid $nep-divider;
      border-radius: 4px;
      background: $nep-white 0% 0% no-repeat padding-box;
    }

    .page-section:last-child {
      margin-bottom: 20px !important;

    }

  }
}

.section-container:first-child {
  margin-top: 20px !important;
}

.section-container:last-child {
  margin-bottom: 32px !important;
}

.subpage-name-col {
  border-right: 1px solid $nep-nav-tab-border-color;
  color: $nep-icon-grey !important;
}

.field-text {
  border-left: none !important;
  border-top: none !important;
  border-right: none !important;
  border-radius: 0px !important;
  border-bottom: 1px solid #ced4da;
  font-size: 0.875rem;
  color: $nep-black;
  padding-left: 8px !important;
}

.border-right {
  border-right: 1px solid $nep-nav-tab-border-color;
}

.field-text:hover {
  border-color: $nep-text-border-bottom !important;
}

.field-text::placeholder {
  color: $nep-text-placeholder-grey !important;
}

.field-text:focus {
  color: $nep-primary;
  border-bottom: 1px solid $nep-primary !important;
}


.FCinfoIconImg-24 {
  height: 24px;
  width: 24px;
  margin-left: px;
  margin-top: 0px;
}

.boxshadow {
  box-shadow: 0px 5px 8px #00000014;
}


.bg-grey-color {
  background-color: $nep-text-grey !important;
}

.displayName-topCard {
  color: $nep-icon-grey;
  padding-left: 10px;
  padding-top: 1px;
}

.displayName {
  color: $nep-icon-grey;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: inherit !important;
  cursor: not-allowed !important;
}

.dot-img {
  position: absolute;
  cursor: pointer;
}

.dot-img-SubCard {
  margin-top: -3px !important;
}

.section {
  border-radius: 4px;
}

.image-upload-label {
  color: #000000;
}

.fund-name-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
  padding: 0.38rem 0.75rem;
  padding-left: 0px;
}

.fund-name-drp-1 {
  flex: 1;
  text-align: left;
  padding: 0.38rem 0;
}

.fund-name-drp-3 {
  flex: 3;
  text-align: left;
  padding: 10px;
}

.dashed-box {
  border: 2px dashed #DEDFE0 !important;
  border-radius: 5px;
}

.info-icon{
  height: 16px;
  width: 16px;
  margin-bottom: 1px;
  margin-left: -12px;
}

.upload-image{
  width:100% !important;
}

.sticky-button{
  display: flex;
  gap: 10px;
  flex-direction: row;
  justify-content: flex-end;
  position: sticky;
}

.color-picker{
  margin-top:0.35rem !important;
}

.fundOption {
  margin-left: -2px;
}

#logoButton input, #bgButton input{
  opacity: 0;
  position: absolute;
}

.uploaded-image {
  width: 100%;
  height: 9vh;
  border: dashed 1px #DEDFE0;
  border-radius: 4px;
}