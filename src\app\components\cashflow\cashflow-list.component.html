﻿<div class="row">
    <div class="col-lg-12">
        <div class="add-user-component">
            <div class=" card-main">
                <div class=" card-header-main p-0">
                    <div class="row mr-0 ml-0 fundlist-header">
                        <div class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                            <div class="float-left">
                                <div class="fundlist-title TextTruncate" title="Funds - Cashflows">
                                    Funds - Cashflows
                                </div>
                            </div>
                            <div class="float-right">
                                <div class="d-inline-block search">
                                    <span class="fa fa-search fasearchicon p-1"></span>
                                    <input  (input)="search()" type="text" class="search-text-company companyListSearchHeight TextTruncate" placeholder="Search Cashflow" [(ngModel)]="globalFilter">
                                </div>
                                <div id="upload-cashflow" class="d-inline-block" [hideIfUnauthorized]='{featureId:feature.Cashflow,action:"import"}'>
                                    <div class="add-icon p-add-padding">
                                        <a href="javascript:void" (click)="isOpenUpload=true" title="Upload Cashflow">
                                            <img class="" title="Upload Cashflow" src="assets/dist/images/plus.svg" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                    <kendo-treelist class="kendo-cashflow-list grid-row-no-padding" kendoTreeListExpandable [data]="uploadedFileArray"
                        [fetchChildren]="fetchChildren" [hasChildren]="hasChildren">
                        <kendo-treelist-span-column [expandable]="true">
                            <kendo-treelist-column [width]="180">
                            </kendo-treelist-column>
                            <ng-template kendoTreeListCellTemplate let-dataItem>
                                                <span *ngIf="dataItem.fileName == undefined" class="custom-cell-heading" >{{ dataItem.name }}</span>
                                                <div class="row ml-0 mr-0 flex-fill"  *ngIf="dataItem.fileName != undefined">
                                                    <div class="col-4 col-sm-4 col-md-4 col-lg-4  pr-0 pl-0 custom-cell-first cashflow-list">
                                                        <a id="view-funds" class="click-view company-name" (click)="setHeaderName(dataItem)" href="javascript:void(0);" [routerLink]="['/cashflow', dataItem.encryptedCashflowFileID]" title="View Details" [hideIfUnauthorized]='{featureId:feature.Cashflow,action:"view"}'>{{dataItem.name }}</a>
                                                    </div>
                                                    <div class="col-4 col-sm-4 col-md-4 col-lg-4  pr-0 pl-0 custom-cell-date"*ngIf="dataItem.createdOn != undefined && dataItem.createdOn !=''" title="{{(dataItem.createdOn | date:'MM/dd/yyyy HH:mm:ss')||'NA'}}">
                                                        {{(dataItem.createdOn | date:'MM/dd/yyyy HH:mm:ss')||"NA"}}
                                                    </div>
                                                    <div  id="download-cashflow" *ngIf="dataItem.fileName != undefined" class="col-4 col-sm-4 col-md-4 col-lg-4   pr-0 pl-0 custom-cell-action">
                                                      <img id="download-cashflow-report" class="showHandIcon" title="Export Cashflow (Excel file)" *ngIf="dataItem.createdOn != undefined && dataItem.createdOn !=''" (click)="exportCashflowFile(dataItem.fileUploadDetails)" [hideIfUnauthorized]='{featureId:feature.Cashflow,action:"export"}' src="assets/dist/images/cashflow-download.svg" />
                                                    </div>
                                                </div>
                            </ng-template>
                    
                    
                        </kendo-treelist-span-column>
                        <ng-template kendoTreeListNoRecordsTemplate>
                            <app-empty-state class="finacials-beta-empty-state"  [imageHeight]="'calc(100vh - 183px) !important'" [isGraphImage]="false"></app-empty-state>
                          </ng-template>
                    </kendo-treelist>
            </div>
        </div>
    </div>
</div>
<app-cashflow-upload *ngIf="isOpenUpload" (onClosePopUpClick)="closePopup($event)"></app-cashflow-upload>
<app-loader-component *ngIf="isLoading"></app-loader-component>