import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ManageTrackerFieldsComponent } from './manage-tracker-fields.component';
import { ToastrService } from 'ngx-toastr';
import { DashboardTrackerService } from 'src/app/services/dashboard-tracker.service';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of } from 'rxjs';

describe('ManageTrackerFieldsComponent', () => {
  let component: ManageTrackerFieldsComponent;
  let fixture: ComponentFixture<ManageTrackerFieldsComponent>;
  let toastrServiceSpy: jasmine.SpyObj<ToastrService>;
  let dashboardTrackerServiceSpy: jasmine.SpyObj<DashboardTrackerService>;

  beforeEach(() => {
    toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['success', 'error']);
    dashboardTrackerServiceSpy = jasmine.createSpyObj('DashboardTrackerService', [
      'saveDashboardTrackerConfig',
      'saveTrackerDropdownValues',
      'getAvailableCustomFieldsForMapTo'
    ]);
    // Return a mock observable for getAvailableCustomFieldsForMapTo
    dashboardTrackerServiceSpy.getAvailableCustomFieldsForMapTo.and.returnValue(of([]));
    TestBed.configureTestingModule({
      declarations: [ManageTrackerFieldsComponent],
      providers: [
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: DashboardTrackerService, useValue: dashboardTrackerServiceSpy }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    });
    fixture = TestBed.createComponent(ManageTrackerFieldsComponent);
    component = fixture.componentInstance;
    (component as any).dashboardTrackerService = dashboardTrackerServiceSpy;
    (component as any).toastrService = toastrServiceSpy;
    fixture.detectChanges();
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should add a dropdown value if not duplicate or empty', () => {
    component.dropdownInput = 'Value1';
    const event = { preventDefault: () => {} } as Event;
    spyOn(event, 'preventDefault');
    component.addDropdownValue(event);
    expect(event.preventDefault).toHaveBeenCalled();
    expect(component.dropdownValues).toContain('Value1');
    expect(component.dropdownInput).toBe('');
  });

  it('should not add a dropdown value if it is duplicate', () => {
    component.dropdownInput = 'Value1';
    component.dropdownValues = ['Value1'];
    const event = { preventDefault: () => {} } as Event;
    component.addDropdownValue(event);
    expect(component.dropdownValues.length).toBe(1);
  });

  it('should not add a dropdown value if it is empty or whitespace', () => {
    component.dropdownInput = '   ';
    const event = { preventDefault: () => {} } as Event;
    component.addDropdownValue(event);
    expect(component.dropdownValues.length).toBe(0);
  });

  it('should remove a dropdown value by index', () => {
    component.dropdownValues = ['A', 'B', 'C'];
    component.removeDropdownValue(1);
    expect(component.dropdownValues).toEqual(['A', 'C']);
  });
});