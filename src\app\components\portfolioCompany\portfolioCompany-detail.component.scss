@import "../../../variables";
@import "../../../assets/dist/css/font";
@import '~quill/dist/quill.bubble.css';
@import '~quill/dist/quill.snow.css';
.portfolio-company-detail-section {
  margin-top:-16px !important;
  .static-highlight {
    @extend .Body-I;
    color:#9C27B0 !important;
    text-decoration: underline;
  }

    .performance-section {
        .performance-header {
            text-align: left;
            font-size: 14px;
            font-family: "Helvetica Neue LT W05_65 Medium",Arial, Verdana, Tahoma,sans-serif;
            letter-spacing: 0px;
            color: $nep-dark-black;
            opacity: 1;
        }
        .outer-section {
            background: $nep-base-grey 0% 0% no-repeat padding-box;
            border: 1px solid $nep-divider;
            border-radius: 2px 2px 2px 2px;
            opacity: 1;
            box-shadow: 0px 0px 12px $nep-shadow-color;
        }
        .nav-link {
            background-color: transparent !important;
            letter-spacing: 0px;
            color: $nep-text-grey;
            font-size: 0.9rem !important;
            padding-top: 9px;
            padding-bottom: 9px;
            &.active {
                background-color: #FAFAFB !important;
                font-family: "Helvetica Neue LT W05_65 Medium",Arial, Verdana, Tahoma,sans-serif;
                color: $nep-primary !important;
                font-size: 0.9rem !important;
            }
        }
        .tab-bg {
            background-color: $nep-base-grey !important;
        }
        .content-bg {
            background-color: $nep-white !important;
        }
        
    }
    .custom-static-label-padding{
      padding-left: 32px !important;
    }
    .custom-static-label-padding:nth-child(odd){
      padding-left: 0px !important;
    }
    .custom-static-value-padding{
      padding-left: 12px !important;
    } 
    .static-table-border {
      table tbody>tr>td {
        border-right: 1px solid #dee2e6 !important;
      }

      table tbody>tr>td:last-child {
        border-right: none !important;
      }
    }
    
}
.custom-label-company
{
    width: 140px !important;
    margin-bottom: 0px !important;
}
.text-align-left {
    text-align: left !important;
    padding: 12px 16px !important;
}
.static-pc-section
{
    .static-bg
    {
        // background: $nep-white;
    }
    .pc-section-header
    {
        background: #FAFAFB 0% 0% no-repeat padding-box;
        display: inline-block;
        width: 100%;
    }
    .showHandIcon
    {
        padding-top: 3px;
        padding-bottom: 3px;
    }
}
.static-card
{
    border-radius: 0px 0px 4px 4px !important;
    opacity: 1;
    border: 1px solid #DEDFE0 !important;
    box-shadow: 0px 0px 12px #00000014;
}
  .line-wrapper {
    display: flex;
    align-items: center;
    span{
        letter-spacing: 0px;
        color: #212121;
        font-family: "Helvetica Neue LT W05_65 Medium",Arial, Verdana, Tahoma,sans-serif !important;
    }
    .line {
        border-top: 1px solid #DEDFE0;
        flex-grow: 1;
        margin: 0 0px;
      }
  }
  .statit-desc 
  {
    letter-spacing: 0px;
    color: #212121;
    opacity: 1;
  }

.static-info-table th {
    border-left: none !important;
    border-top: none !important;
    letter-spacing: 0.17px;
    color: #212121;
    font-family: "Helvetica Neue LT W05_65 Medium",Arial, Verdana, Tahoma,sans-serif !important;
}
  .static-info-table th:last-child {
    border-right: none !important;
  }
  .static-info-table tr td:first-child{
    border-left: none !important;
  }
  .static-info-table tr:last-child td {
    border-bottom: none !important;
  }
  .static-info-table tr td{
    letter-spacing: 0.17px;
    color: #212121;
    opacity: 1;
  }
  .card-border
  {
    border: 1px solid #DEDFE0;
border-radius: 4px;
opacity: 1;
overflow-x: hidden;
  }
  .static-label
  {
    letter-spacing: 0px;
color: #75787B !important;
opacity: 1;
  }
  .static-field-value
  {
    letter-spacing: 0px;
    color: #212121 !important;
    opacity: 1;
  }
  .invest-section-pb
  {
      padding-bottom: 20px !important;
 }
 .static-section-pl
 {
  padding-left: 32px !important;
 }
 .field-w
 {
  width: 100%;
 }
.list-padding {
  padding-bottom: 2px !important;
  list-style-type: none !important;
}
.sub-feature-section
{
background: #FFFFFF 0% 0% no-repeat padding-box;
border: 1px solid #75787B;
border-radius: 4px;
opacity: 1;
}
.qsearchbar
{
display: flex;
top: 1409px;
left: 325px;
width: 145px;
height: 32px;
}
.quarterser
{
background: #FFFFFF 0% 0% no-repeat padding-box;
border: 1px solid #75787B;
border-radius: 4px;
opacity: 1;

}
.rowpadding
{
  padding-bottom: 8px;
}
.searchbarmargin
{
  width: 210px;
  margin-left: -15px;
}
.stleeeee
{
  top: 1353px;
  left: 210px;
  width: 1070px;
  height: 48px;
}

[data-id='previewEditorWrapper']{
 ul, ol{
  list-style-position:inside;
 }
}
.previewEditorWrapper{
  margin-bottom: 0 !important;
  user-select: none;
}

.clickable-area {
  width: 100%;
  height: 100%;
  padding: 1rem !important;
  box-sizing: border-box;
  cursor: pointer;
}
.settingMoreThan25Chars{
  white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 25ch;
}
.edit-icon-padding{
  padding-top: 3px;
  padding-bottom: 3px;
}
.commentary-container {
  align-items: center;
  padding: 0.5rem;
}
.comm-per-cel{
  height: 2rem;
  bottom: 3.5px;
  kendo-combobox {
    background: white;
    border-radius: 0;
  }
}
.comm-cel-Q{
  height: 2rem;
}
.comm-cel{
  height: 2rem;
  width: 15rem;
  kendo-datepicker {
    background: white;
    height: 32px;
    border-radius: 0;
  }
}
.custom-text{
  overflow: visible !important;
}
.text-truncate-width {
  max-width: 320px;
}
quarter-year-control .querter-year-button-style .drop-toggle {
  border-radius: 0 !important;
}
.commentary-section {
  // background: #FAFAFC 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.0784313725);
  border: 1px solid #DEDFE0;
  border-radius: 4px;
  opacity: 1;
  .c-header{
    border: 1px solid #E1E1E8;
    border-top: none;
    border-right: none;
    border-left: none;
    margin: 10px 0px 0px 0px;
  }
  .comment-editor-h{
    .no-data-container{
      padding: 1rem !important;
    }
  }
  .c-p-header{
    padding: 5px 15px;
    border-bottom: 1px solid #E1E1E8;
    background: #FAFAFA;
  }
  .only-text {
    height: auto;
    background: #ffffff;
  }
  .button-footer{
    height: 3rem;
    border-bottom: 1px solid #E1E1E8;
  }
  .conditional-button-footer{
    height: 3rem;
    border-bottom: 0px;
  }
  .btn-comment-section{
    padding: 0.5rem 0.9375rem;
  }
  .no-comment-font{
    color: #666666;
  }
}
.button-action{
  width: 74px !important;
}
.comment-editor .ql-toolbar.ql-snow, .comment-editor .ql-container.ql-snow {
  border-right: 0px !important;
  border-left: 0px !important;
}
.allvalues{
  float:right;
  font-size:12px;
  margin-right:12px;
  color: $nep-icon-grey;
  margin-top: 0.825rem;
}
.rcalcLegend {
  background: #D2EDFD 0% 0% no-repeat padding-box;
  border: 1px solid #55565A;
  border-radius: 4px;
  height: 16px;
  width: 16px;
  padding-left: 16px;
}
.p-rcalcLegend{
  padding-top: 1rem;
}
.pdf-icon {
  color: #DE3139; 
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.spin {
  animation: spin 2s linear infinite;
}
.split-button-custom{
  width: 140px !important;
}
@each $width in 140,164 {
  .split-button-width-#{$width} {
      width: #{$width}px !important;
  }
}
.no-select {
  user-select: none;
}
.no-boarder{
  border-bottom: none !important;
}
.default-text-color {
  color: #212121 !important;
}
.comm-footnote{
  position: relative !important;
  z-index: 1;
}
