import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChangeDetectorRef, ElementRef } from '@angular/core';
import { StackedPercentageChartComponent } from './stacked-percentage-chart.component';
import * as Highcharts from 'highcharts';

describe('StackedPercentageChartComponent', () => {
    let component: StackedPercentageChartComponent;
    let fixture: ComponentFixture<StackedPercentageChartComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [StackedPercentageChartComponent],
            providers: [ChangeDetectorRef]
        }).compileComponents();
    });

    beforeEach(() => {
        window.revealBridge = {
            notifyExtensionIsReady: jasmine.createSpy('notifyExtensionIsReady')
        };
        fixture = TestBed.createComponent(StackedPercentageChartComponent);
        component = fixture.componentInstance;
        component.chartContainer = new ElementRef(document.createElement('div'));
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize revealBridge and revealBridgeListener on window', () => {
        component.ngOnInit();
        expect(window.revealBridge).toBeDefined();
        expect(window.revealBridgeListener).toBeDefined();
    });

    it('should convert data to JSON correctly', () => {
        const inputData = {
            metadata: {
                columns: [{ name: 'col1' }, { name: 'col2' }]
            },
            data: [
                [1, 2],
                [3, 4]
            ]
        };
        const expectedOutput = [
            { col1: 1, col2: 2 },
            { col1: 3, col2: 4 }
        ];
        expect(component.dataToJson(inputData)).toEqual(expectedOutput);
    });

    it('should return 0 percentage if total is 0', () => {
        const value = 10;
        const dataObject = { series1: 0, series2: 0 };
        const seriesNames = ['series1', 'series2'];
        expect(component.getPercentage(value, dataObject, seriesNames)).toBe(0);
    });

    it('should calculate percentage correctly', () => {
        const value = 10;
        const dataObject = { series1: 10, series2: 20 };
        const seriesNames = ['series1', 'series2'];
        expect(component.getPercentage(value, dataObject, seriesNames)).toBe(33.33);
    });

    it('should create chart with correct options', () => {
        const dataObjects = [
            { category: 'A', series1: 10, series2: 20 },
            { category: 'B', series1: 30, series2: 40 }
        ];
        const data = {
            metadata: {
                columns: [{ name: 'category', type: 0 }, { name: 'series1' }, { name: 'series2' }]
            }
        };
        spyOn(Highcharts, 'chart');
        component.createChart(dataObjects, data);
        expect(Highcharts.chart).toHaveBeenCalledWith(
            component.chartContainer.nativeElement,
            jasmine.objectContaining({
                chart: { type: 'column' },
                xAxis: { categories: ['A', 'B'] },
                series: jasmine.arrayContaining([
                    jasmine.objectContaining({ name: 'series1' }),
                    jasmine.objectContaining({ name: 'series2' })
                ])
            })
        );
    });
});