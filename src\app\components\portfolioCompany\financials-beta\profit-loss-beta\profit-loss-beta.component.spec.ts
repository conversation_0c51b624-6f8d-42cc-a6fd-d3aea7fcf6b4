/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from "@angular/core/testing";
import { ProfitLossBetaComponent } from "./profit-loss-beta.component";
import { ActivatedRoute, Router } from "@angular/router";
import { ToastrService, ToastrModule } from "ngx-toastr";
import {
  FinancialValueUnitsEnum,
  MiscellaneousService,
} from "../../../../services/miscellaneous.service";
import { ProfitLossService } from "../../../../services/profit-loss.service";
import { HttpClientTestingModule } from "@angular/common/http/testing";
import { CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { of, throwError } from "rxjs";
import { TableHeader } from "src/app/components/file-uploads/kpi-cell-edit/kpiValueModel";
import { AuditService } from "src/app/services/audit.service";
import { OidcAuthService } from "src/app/services/oidc-auth.service";
import { DatePipe } from "@angular/common";
describe("ProfitLossBetaComponent", () => {
  let component: ProfitLossBetaComponent;
  let fixture: ComponentFixture<ProfitLossBetaComponent>;
  let mockActivatedRoute = {
    snapshot: {
      params: {
        id: "45",
      },
    },
  };
  const dataRow = {
    LineItemId: 608,
    MappingId: 10493,
    ParentId: 0,
    DisplayOrder: 257,
    Kpi: "AK LAST DATE",
    KpiInfo: "$",
    IsBoldKpi: false,
    IsHeader: false,
    IsFormula: false,
    "Jan 2023": "400866",
    "Feb 2023": "226252",
    "Mar 2023": "31979",
    "Q1 2023": "31979",
    "2023": "2023",
  };
  const tableColumns = {
    header: "Jan 2023",
    field: "Jan 2023",
    year: 2023,
  };
  const quarterColumns = {
    header: "Q1 2023",
    field: "Q1 2023",
    year: 2023,
  };
  const yearColumns = {
    header: "Q1 2023",
    field: "Q1 2023",
    year: 2023,
  };
  let mockRouter = jasmine.createSpyObj("Router", ["navigate"]);
  let mockToastrService = jasmine.createSpyObj("ToastrService", [
    "success",
    "error",
  ]);
  let profitLossService: ProfitLossService;
  let auditService: jasmine.SpyObj<AuditService>;
  let miscService: MiscellaneousService;
  let oidcAuthService: jasmine.SpyObj<OidcAuthService>;
  const spyOidcAuthService = jasmine.createSpyObj("OidcAuthService", [
    "getEnvironmentConfig",
  ]);
  const auditServiceSpy = jasmine.createSpyObj("AuditService", [
    "getPortfolioEditSupportingCommentsData",
  ]);
  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [FormsModule, HttpClientTestingModule, ToastrModule.forRoot()],
      declarations: [ProfitLossBetaComponent],
      providers: [
        DatePipe,
        { provide: OidcAuthService, useValue: spyOidcAuthService },
        { provide: AuditService, useValue: auditServiceSpy },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        {
          provide: Router,
          useValue: { navigate: jasmine.createSpy("navigate") },
        },
        {
          provide: MiscellaneousService,
          userValue: jasmine.createSpyObj("MiscellaneousService", [
            "downloadExcelFile",
          ]),
        },
        {
          provide: ProfitLossService,
          useValue: jasmine.createSpyObj("ProfitLossService", [
            "getPCProfitAndLossValues",
            "exportCompanyProfitAndLoss",
          ]),
        },
        { provide: ToastrService },
        { provide: "BASE_URL", useValue: "http://localhost:5000/" },
        {
          provide: AuditService,
          userValue: jasmine.createSpyObj("AuditService", [
            "getPortfolioEditSupportingCommentsData",
          ]),
        },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProfitLossBetaComponent);
    component = fixture.componentInstance;
    component.auditLogList = [
      {
        acutalAuditLog: false,
        budgetAuditLog: false,
        month: 1,
        year: 2023,
        quarter: null,
        mappingId: 10463,
        kpiValueId: 56016,
      },
      {
        acutalAuditLog: false,
        budgetAuditLog: false,
        month: 0,
        year: 2023,
        quarter: "Q1",
        mappingId: 10463,
        kpiValueId: 56017,
      },
      {
        acutalAuditLog: true,
        budgetAuditLog: false,
        month: 0,
        year: 2023,
        quarter: null,
        mappingId: 10463,
        kpiValueId: 56021,
      },
    ];
    fixture.detectChanges();
    mockRouter = TestBed.inject(Router);
    profitLossService = TestBed.inject(ProfitLossService);
    miscService = TestBed.inject(MiscellaneousService);
  });

  it("should create", () => {
    expect(component).toBeTruthy();
  });
  it("should emit valueTypes when setValueType is called", () => {
    spyOn(component.onChangeValueType, "emit");

    component.setValueType(true, false, true);

    expect(component.onChangeValueType.emit).toHaveBeenCalledWith({
      isMonthly: true,
      isQuarterly: false,
      isAnnually: true,
    });
  });
  it("should return true when isNumberCheck is called with a number", () => {
    const result = component.isNumberCheck(123);
    expect(result).toBeTrue();
  });

  it("should return false when isNumberCheck is called with a non-number", () => {
    const result = component.isNumberCheck("abc");
    expect(result).toBeFalse();
  });
  it("should return acutalAuditLog when printColumn is called with rowData and column", () => {
    const result = component.printColumn(dataRow, tableColumns);
    expect(result).toBeFalse();
  });

  it("should return false when printColumn is called with rowData and column and result is empty", () => {
    const mockResult = [];
    spyOn(component, "getFilterAuditValue").and.returnValue(mockResult);
    const result = component.printColumn("rowData", "column");
    expect(result).toBeFalse();
  });
  it("should filter by month when filterAuditValue is called with a monthValue", () => {
    const mockAuditList = [
      { month: 1, year: 2022, mappingId: 1, kpiValueId: 1 },
      { month: 2, year: 2022, mappingId: 1, kpiValueId: 1 },
      { month: 3, year: 2022, mappingId: 1, kpiValueId: 1 },
      { month: 4, year: 2022, mappingId: 1, kpiValueId: 1 },
    ];
    const mockRowData = { MappingId: 1 };
    const result = component.filterAuditValue(
      2022,
      1,
      mockAuditList,
      null,
      mockRowData
    );
    expect(result).toEqual([mockAuditList[0]]);
  });

  it("should filter by year and mappingId when filterAuditValue is called with a null monthValue and non-quarter periodHeader", () => {
    const mockAuditList = [
      { month: null, year: 2022, mappingId: 1, kpiValueId: 1, Quarter: null },
      { month: null, year: 2022, mappingId: 2, kpiValueId: 1, Quarter: null },
      { month: null, year: 2022, mappingId: 3, kpiValueId: 1, Quarter: null },
      { month: null, year: 2022, mappingId: 4, kpiValueId: 1, Quarter: null },
    ];
    const mockRowData = { MappingId: 1 };
    const result = component.filterAuditValue(
      2022,
      null,
      mockAuditList,
      "FY",
      mockRowData
    );
    expect(result).toEqual([mockAuditList[0]]);
  });
  it("should filter by quarter when filterAuditValue is called with a quarter periodHeader", () => {
    const mockAuditList = [
      { quarter: "Q1", year: 2022, mappingId: 1, kpiValueId: 1 },
      { quarter: "Q2", year: 2022, mappingId: 1, kpiValueId: 1 },
      { quarter: "Q3", year: 2022, mappingId: 1, kpiValueId: 1 },
      { quarter: "Q4", year: 2022, mappingId: 1, kpiValueId: 1 },
    ];
    const mockRowData = { MappingId: 1 };

    const result = component.filterAuditValue(
      2022,
      null,
      mockAuditList,
      "Q1",
      mockRowData
    );

    expect(result).toEqual([mockAuditList[0]]);
  });
  it("should return empty array when getFilterAuditValue is called and calccolumn is defined", () => {
    const mockRowData = { "Calc field": true };
    const mockColumn = { field: "field" };

    const result = component.getFilterAuditValue(mockRowData, mockColumn);

    expect(result).toEqual([]);
  });
  it("should return true when printCalcColumn is called and calColumn is defined", () => {
    const mockRowData = { "Calc field": true };
    const mockColumn = { field: "field" };

    const result = component.printCalcColumn(mockRowData, mockColumn);

    expect(result).toBeTrue();
  });

  it("should return false when printCalcColumn is called and calColumn is not defined", () => {
    const mockRowData = { "Calc field": undefined };
    const mockColumn = { field: "field" };

    const result = component.printCalcColumn(mockRowData, mockColumn);

    expect(result).toBeFalse();
  });
  it("should set isUploadPopupVisible to false when cancelButtonEvent is called", () => {
    component.isUploadPopupVisible = true;

    component.cancelButtonEvent();

    expect(component.isUploadPopupVisible).toBeFalse();
  });
  it("should return true when checkCalcColumn is called and calColumn is defined", () => {
    const mockRowData = { "Calc field": true };
    const mockColumn = { field: "field" };

    const result = component.checkCalcColumn(mockRowData, mockColumn);

    expect(result).toBeTrue();
  });

  it("should return false when checkCalcColumn is called and calColumn is not defined", () => {
    const mockRowData = { "Calc field": undefined };
    const mockColumn = { field: "field" };

    const result = component.checkCalcColumn(mockRowData, mockColumn);

    expect(result).toBeFalse();
  });
  it('should show success toast and hide upload popup when onSubmitButtonEvent is called and code is "ok"', () => {
    const mockResults = { code: "OK", message: "Success" };
    spyOn(component, "showSuccessToast");
    spyOn(component, "getProfitLossData");

    component.onSubmitButtonEvent(mockResults);

    expect(component.showSuccessToast).toHaveBeenCalledWith("Success");
    expect(component.isUploadPopupVisible).toBeFalse();
    expect(component.getProfitLossData).toHaveBeenCalled();
  });

  it('should show error toast and hide upload popup when onSubmitButtonEvent is called and code is not "ok"', () => {
    const mockResults = { code: "ERROR", message: "Error" };
    spyOn(component, "showErrorToast");
    spyOn(component, "getProfitLossData");

    component.onSubmitButtonEvent(mockResults);

    expect(component.showErrorToast).toHaveBeenCalledWith("Error");
    expect(component.isUploadPopupVisible).toBeFalse();
    expect(component.getProfitLossData).toHaveBeenCalled();
  });
  it("should reset properties when resetTable is called", () => {
    component.isLoader = true;
    component.tableResult = ["result"];
    component.tableResultClone = ["result"];
    component.tableFrozenColumns = ["column"];
    component.auditLogList = ["log"];

    component.resetTable();

    expect(component.isLoader).toBeFalse();
    expect(component.tableResult).toEqual([]);
    expect(component.tableResultClone).toEqual([]);
    expect(component.tableFrozenColumns).toEqual([]);
    expect(component.auditLogList).toEqual([]);
  });
  it("should call getPCProfitAndLossValues and handle result when getProfitLossValues is called", () => {
    component.model = {
      portfolioCompany: {
        portfolioCompanyID: "testID",
        companyCurrency: "testCurrency",
      },
      currencyCode: {
        currencyCode: "testCurrencyCode",
      },
    };
    component.currencyRateSource = "USD";
    component.valueType = "Actual";
    component.periodType = 1;
    component.isPageLoad = true;
    component.pageConfigData = {
      kpiType: "testData",
      hasChart: true,
      kpiConfigurationData: [],
    };
    const mockEvent = { first: 0, rows: 10 };
    const mockSearchFilter = { globalFilter: "test" };
    const getDataModel = component.getDataModel(mockEvent, mockSearchFilter);
    const mockResult = {
      rows: ["row"],
      headers: ["header"],
      financialKpiAuditlog: ["log"],
      isMonthly: true,
      isQuarterly: false,
      isAnnually: false,
    };
    spyOn(component, "getDataModel").and.returnValue(getDataModel);
    spyOn(component, "setValueType");
    spyOn(component, "resetTable");
    (profitLossService.getPCProfitAndLossValues as jasmine.Spy).and.returnValue(
      of(mockResult)
    );

    component.getProfitLossValues(mockEvent, mockSearchFilter, "1", "USD");

    expect(component.getDataModel).toHaveBeenCalledWith(
      mockEvent,
      mockSearchFilter
    );
    expect(profitLossService.getPCProfitAndLossValues).toHaveBeenCalledWith(
      getDataModel
    );
    expect(component.setValueType).toHaveBeenCalledWith(true, false, false);
    expect(component.isPageLoad).toBeFalse();
    expect(component.isLoader).toBeFalse();
  });

  it("should call getPCProfitAndLossValues and handle error when getProfitLossValues is called", () => {
    const mockEvent = { first: 0, rows: 10 };
    const mockSearchFilter = { globalFilter: "test" };
    component.model = {
      portfolioCompany: {
        portfolioCompanyID: "testID",
        companyCurrency: "testCurrency",
      },
    };
    component.pageConfigData = {
      kpiType: "testData",
      hasChart: true,
      kpiConfigurationData: [],
    };
    const getDataModel = component.getDataModel(mockEvent, mockSearchFilter);
    spyOn(component, "getDataModel").and.returnValue(getDataModel);
    spyOn(component, "resetTable");
    (profitLossService.getPCProfitAndLossValues as jasmine.Spy).and.returnValue(
      throwError("error")
    );

    component.getProfitLossValues(mockEvent, mockSearchFilter, "1", "usd");

    expect(component.getDataModel).toHaveBeenCalledWith(
      mockEvent,
      mockSearchFilter
    );
    expect(profitLossService.getPCProfitAndLossValues).toHaveBeenCalledWith(
      getDataModel
    );
    expect(component.resetTable).toHaveBeenCalled();
    expect(component.isLoader).toBeFalse();
  });
});
