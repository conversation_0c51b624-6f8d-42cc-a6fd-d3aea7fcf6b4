<div class="email-reminder-details" >
    <div class="email-remainder-details-content">
        <!-- TO Section -->
        <div class="section-row">
            <div class="section-label Body-R">To</div>
            <div class="section-content">
                <div class="email-chips-container">
                    <span *ngFor="let recipient of toRecipients"
                          class="email-chip Body-R"
                          [ngClass]="{'group-chip': isGroupRecipient(recipient)}">
                        {{ getRecipientDisplayText(recipient) }}
                    </span>
                </div>
            </div>
        </div>

        <!-- CC Section -->
        <div class="section-row">
            <div class="section-label Body-R">CC</div>
            <div class="section-content">
                <div class="email-chips-container">
                    <span *ngFor="let recipient of ccRecipients"
                          class="email-chip Body-R"
                          [ngClass]="{'group-chip': isGroupRecipient(recipient)}">
                        {{ getRecipientDisplayText(recipient) }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Subject Section -->
        <div class="section-row no-border">
            <div class="section-label Body-R">Subject</div>
            <div class="section-content no-margin">
                <div class="subject-field Body-R">{{ subject }}</div>
            </div>
        </div>

        <!-- Message Section -->
        <div class="section-row no-border">
            <div class="section-label Body-R">Message</div>
            <div class="section-content no-margin">
                <div class="message-content Body-R">
                    <div [innerHTML]="emailBody"></div>
                </div>
            </div>
        </div>  
        <!-- follow-up reminder section-->
        <div class="section-row no-border">
            <div class="section-label Body-R">Follow-Up Reminder</div>
            <div class="section-content no-margin">
                <div class="followup-content">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="Body-M">Total Number of Reminders : {{totalReminders}}</div>
                        <div class="Body-M text-center">Reminders Sent : {{remindersSent}}</div>
                        <div class="skip-schedule d-flex align-items-center gap-2" [class.disabledNoOfCasesDiv]="(totalReminders - remindersSent - totalErrorandSkips) === 0 || remindersSent === 0 || isSkipCycleEnabled">
                            <span class="Body-M btn btn-sm active-status">
                                Skip Next Reminders
                            </span>
                            <kendo-switch
                                [onLabel]="' '"
                                [offLabel]="' '"
                                [disabled]="(totalReminders - remindersSent - totalErrorandSkips) === 0 || remindersSent === 0 || isSkipCycleEnabled"
                                [(ngModel)]="isSkipCycleEnabled"
                                (valueChange)="onSkipCycleChange($event)">
                            </kendo-switch>
                        </div>
                    </div>
                    <div class="d-flex mt-1 justify-content-between align-items-center">
                         <div class="Caption-I">Last Reminder Sent On: {{lastReminderDate}}</div>
                         <div class="Caption-I mr-2">Next Reminder Date: {{nextReminderDate}}</div>
                    </div>             
                </div>
            </div>
        </div>  
    </div>
</div>
