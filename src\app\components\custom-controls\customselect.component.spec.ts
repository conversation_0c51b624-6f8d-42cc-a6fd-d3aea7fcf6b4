import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA,Renderer2 } from '@angular/core';
import { CustomSelectComponent } from './customselect.component';

describe('CustomSelectComponent', () => {
  let component: CustomSelectComponent;
  let fixture: ComponentFixture<CustomSelectComponent>;

  beforeEach(() => {
    const renderer2Stub = () => ({});
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [CustomSelectComponent],
      providers: [{ provide: Renderer2, useFactory: renderer2Stub }]
    });
    fixture = TestBed.createComponent(CustomSelectComponent);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  it(`openOption has default value`, () => {
    expect(component.openOption).toEqual(false);
  });

  it(`isSearch has default value`, () => {
    expect(component.isSearch).toEqual(true);
  });

  it(`isMultiSearch has default value`, () => {
    expect(component.isMultiSearch).toEqual(true);
  });

  it(`placeholderText has default value`, () => {
    expect(component.placeholderText).toEqual(`Search and Select`);
  });

  it(`keyword has default value`, () => {
    expect(component.keyword).toEqual(`name`);
  });

  it(`data has default value`, () => {
    expect(component.data).toEqual([]);
  });

  it(`selectList has default value`, () => {
    expect(component.selectList).toEqual([]);
  });

  it(`lpreportSelect has default value`, () => {
    expect(component.lpreportSelect).toEqual(false);
  });

  it('#openSelect should toggle openOption', () => {
    component.openOption = false;

    component.openSelect();

    expect(component.openOption).toBe(true);

    component.openSelect();

    expect(component.openOption).toBe(false);
  });

  it('#registerOnChange should set onChange to the function passed as a parameter', () => {
    const fn = () => void 0;

    component.registerOnChange(fn);

    // @ts-ignore
    expect(component.onChange).toBe(fn);
  });

  it('#registerOnTouched should set onTouched to the function passed as a parameter', () => {
    const fn = () => void 0;

    component.registerOnTouched(fn);

    // @ts-ignore
    expect(component.onTouched).toBe(fn);
  });

  
  it('#onDocumentClick should set openOption to false if the clicked element is not contained within the component and does not have the id "customSelectComponent"', () => {
    const event = {
      target: document.createElement('div'),
      currentTarget: {
        activeElement: {
          id: 'otherElement'
        }
      }
    };

    component.onDocumentClick(event);

    expect(component.openOption).toBe(false);
  });

});
