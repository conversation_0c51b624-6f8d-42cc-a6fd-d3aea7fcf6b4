@import "../../../variables";
.header-performance
{
  padding-top:20px;
}
.tab-bg {
    background: #FAFAFA 0% 0% no-repeat padding-box;
}
.trackrecord-section
{
    background: #FAFAFB 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000014;
    border: 1px solid #DEDFE0;
    border-radius: 4px;
    opacity: 1;
}
.nav-link {
    background-color: transparent !important;
    letter-spacing: 0px;
    font-size: 0.9rem !important;
    padding-top: 9px;
    padding-bottom: 9px;
    &.active {
        background-color: $nep-white !important;
        color: $nep-primary !important;
    }
}

.custom-tabs>.nav-tabs .nav-link.active,
.custom-tabs>.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
    background-color: $nep-white;
    border-color: $nep-nav-tab-border-color $nep-nav-tab-border-color $nep-white-secondary;
    border-bottom: none !important;
    top: 0px !important;
    position: relative !important;
}
.investorcontainer{
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000014;
    border: 1px solid #DEDFE0;
    border-radius: 4px;
    opacity: 1;
    padding: 16px;
}
.text-box-shadow{
 box-shadow: none !important;   
}
.description{
    padding-top: 16px;
}
.BusinessDescriptionlabel{
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000014;
    border: 1px solid #DEDFE0;
    border-radius: 4px;
}
.header-bottom{
    padding-bottom: 1.25rem;
}
.message::-webkit-input-placeholder {
    padding: 16px;
 }
 
 .message::-moz-input-placeholder {
    padding: 16px;
 }
 
 .message:-moz-input-placeholder {
    padding: 16px;
 }
 
 .message:-ms-input-placeholder {
    padding: 16px;
 }
 .Addinvestorstaticinfo-scroll{
    overflow-y: auto
 }
 .isheadquater {
    margin-top: 2px;
    position: absolute;
}
.fund-header {
    letter-spacing: 0px;
    color: #000000;
    opacity: 1;
    font-family: "Helvetica Neue LT W05_65 Medium",Arial, Verdana, Tahoma,sans-serif;
  }
.form-group
{
margin: 0px !important;
}
.form-control {
    letter-spacing: 0px !important;
    color: #212121 !important;
    opacity: 1 !important;
}
label{
    margin-top: 0px !important;
    margin-bottom: 0px !important;
}
.pr-control-fund
{
    padding-right: 40px;
    padding-bottom: 20px;
}
.text-desc
{
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000014;
    border: 1px solid #DEDFE0 !important;
    border-radius: 4px;
    padding: 16px !important;
}
.fund-static
{
    margin-bottom: 60px !important;
}
.pr-control-fund:nth-child(4n)
{
    padding-right: 0px !important;
}
 input::-webkit-input-placeholder,
input:-ms-input-placeholder,
input::placeholder {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    max-width: 100% !important;
}
 .mandatory-label:after {
    content: "*";
    color: red;
}
.investor-active
{
color:$nep-text-color !important;
}
.Caption-M{
    color: #666666;
}