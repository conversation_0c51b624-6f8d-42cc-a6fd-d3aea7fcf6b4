import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { CloService } from '../../../services/clo.service';
import { AddClo } from '../add-clo-list.model';
import { FormBuilder,FormControl, FormGroup } from '@angular/forms';
import { DatePipe } from '@angular/common';
import { CloListService } from '../clo-list/clo-list.service';
import { ToastrService } from 'ngx-toastr';
import { TITLES } from 'src/app/common/constants';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';

@Component({
  selector: 'add-clo-list',
  templateUrl: './add-clo-list.components.html',
  styleUrls: ['./add-clo-list.component.scss'],
  providers: [DatePipe]
})
export class AddCloComponent implements OnInit {
  cloForm: FormGroup;
  savedData: AddClo; 
  clolistvalues: any[] = [];
  companyName: string = '';
  companyId: number = 0;
  isEdited:boolean = false;
  isCloForm: boolean = true;
  title:string = '';
  isUpdate: boolean = false;
  uniqueId: string = '';
  cLO_Id: number = 0;
  domicileList:any[]=["US","EU"];
  isLoading:boolean = false;

  constructor(private readonly route: ActivatedRoute,
    private readonly datePipe: DatePipe,
    private readonly router: Router,
    private readonly cloListService: CloListService,
    private readonly toastrService: ToastrService,
    private readonly cloService: CloService,
    private breadcrumbService: BreadcrumbService  ) {
    this.getCompanyName();
  };


 getCompanyName(){
  this.route.queryParams.subscribe(params => {
    this.companyName = params['name'];
    // localStorage.setItem("headerName", this.companyName);
    this.companyId = +params['id']; 
    this.uniqueId = params['uniqueId']; 
  });  

  this.title = this.uniqueId === '' ? TITLES.ADD_CLO : TITLES.UPDATE_CLO;
  this.isUpdate = this.uniqueId !== '';
 }

  ngOnInit(): void {
    this.updateBreadcrumbs();
    this.cloForm = new FormGroup({
    companyName: new FormControl(this.companyName),
    domicile: new FormControl(null), // Initialize with null or ''
    issuer: new FormControl(''),
    arranger: new FormControl(''),
    trustee: new FormControl(''),
    priced: new FormControl(''),
    closed: new FormControl(''),
    lastRefiDate: new FormControl(''),
    lastResetDate: new FormControl(''),
    callEndDate: new FormControl(''),
    originalEndOfReinvestmentDate: new FormControl(''),
    currentEndOfReinvestmentDate: new FormControl(''),
    currentMaturityDate: new FormControl(''),
    lastRefiResetArranger: new FormControl(''),
    });
    this.cloForm.valueChanges.subscribe(() => {
      this.isEdited = true;
    });
   
    if(this.isUpdate) {
      this.fetchCloData();
    }
}

updateBreadcrumbs() {
  this.isLoading=true;
  let newBreadcrumbs: any[] = [];
      this.isLoading=false;
      newBreadcrumbs.push( { label: 'Collateral Loan Obligation(CLO)', url: '/clo-list' },);
    newBreadcrumbs.push( { label: this.title +"(CLO)" });
    this.breadcrumbService.setBreadcrumbs(newBreadcrumbs);
    
}

mapFormToModel(): AddClo {
  const formValues = this.cloForm.value;
  const addCloModel = new AddClo();
  addCloModel.cLO_Id = this.cLO_Id;
  addCloModel.uniqueID = this.uniqueId;
  addCloModel.companyId = this.companyId;
  addCloModel.companyName = this.companyName;
  addCloModel.domicile = formValues.domicile;
  addCloModel.issuer = formValues.issuer;
  addCloModel.arranger = formValues.arranger;
  addCloModel.trustee = formValues.trustee;
  addCloModel.priced = this.formatDate(formValues.priced);
  addCloModel.closed = this.formatDate(formValues.closed);;
  addCloModel.lastRefiDate = this.formatDate(formValues.lastRefiDate);;
  addCloModel.lastResetDate = this.formatDate(formValues.lastResetDate);
  addCloModel.callEndDate = this.formatDate(formValues.callEndDate);;
  addCloModel.originalEndOfReinvestmentDate = this.formatDate(formValues.originalEndOfReinvestmentDate);;
  addCloModel.currentEndOfReinvestmentDate = this.formatDate(formValues.currentEndOfReinvestmentDate);;
  addCloModel.currentMaturityDate = this.formatDate(formValues.currentMaturityDate);
  addCloModel.lastRefiResetArranger = formValues.lastRefiResetArranger;

  return addCloModel;
}

private formatDate(date: any): string {
  return this.datePipe.transform(date, 'd MMMM yyyy');
}

private parseDate(dateString: string): Date {
  return new Date(Date.parse(dateString));
}

onCancel(): void {
  this.router.navigate(['/clo-list']); 
}

onReset(): void {
  this.cloForm.reset();
  this.isEdited = false;
}

setCurrentDate(controlName: string): void {
  const control = this.cloForm.get(controlName);
  if (control && !control.value) {
    control.setValue(new Date());
  }
}

getValue(control:string){
  return this.cloForm.get(control);
}

setValue(control:string){
  const controls = this.cloForm.controls;
    let allFieldsFilled = true;
    for (const name in controls) {
        const control = controls[name];
        const value = control.value;
        const trimmedValue = (typeof value === 'string') ? value.trim() : value;
        control.setValue(trimmedValue);
    }
}

onSave() {
  this.isLoading=true;
  const controls = this.cloForm.controls;
  const values = {};
  for (const name in controls) {
    const control = controls[name];
    const value = control.value;
     control.setValue(value);
    values[name] = control.value;
  }
  this.clolistvalues.push(values);
  this.isEdited = false;
  this.savedData = this.mapFormToModel();

  if(this.isUpdate) {  
    this.saveCLO();
    return;
  }

  this.isCloForm = false;
}

  saveCLO() {
    this.cloListService.saveClo(this.savedData).subscribe(
      response => {     
        this.isLoading=false;
        if(response.status){
          this.toastrService.success(response.message, "", { positionClass: "toast-center-center" });          
          this.router.navigate(['/clo-list']);
        }        
      },
      error => {
        this.isLoading=false;
        this.toastrService.error("Error saving CLO", "", { positionClass: "toast-center-center" });   
        console.error('Error saving CLO', error);
      }
    );
  }

  handleCloData(cloData: AddClo): void {
    this.isEdited = true;
    this.cloForm.setValue({            
      companyName: cloData.companyName,
      domicile: cloData.domicile,
      issuer: cloData.issuer,
      arranger: cloData.arranger === 'N/A' ? '' : cloData.arranger,
      trustee: cloData.trustee === 'N/A' ? '' : cloData.trustee,
      priced: cloData.priced && cloData.priced !== 'N/A' ? this.parseDate(cloData.priced) : '',
      closed: cloData.closed && cloData.closed !== 'N/A' ? this.parseDate(cloData.closed) : '',
      lastRefiDate: cloData.lastRefiDate && cloData.lastRefiDate !== 'N/A' ? this.parseDate(cloData.lastRefiDate) : '',
      lastResetDate: cloData.lastResetDate && cloData.lastResetDate !== 'N/A' ? this.parseDate(cloData.lastResetDate) : '',
      callEndDate: cloData.callEndDate && cloData.callEndDate !== 'N/A' ? this.parseDate(cloData.callEndDate) : '',
      originalEndOfReinvestmentDate: cloData.originalEndOfReinvestmentDate && cloData.originalEndOfReinvestmentDate !== 'N/A' ? this.parseDate(cloData.originalEndOfReinvestmentDate) : '',
      currentEndOfReinvestmentDate: cloData.currentEndOfReinvestmentDate && cloData.currentEndOfReinvestmentDate !== 'N/A' ? this.parseDate(cloData.currentEndOfReinvestmentDate) : '',
      currentMaturityDate: cloData.currentMaturityDate && cloData.currentMaturityDate !== 'N/A' ? this.parseDate(cloData.currentMaturityDate) : '',
      lastRefiResetArranger: cloData.lastRefiResetArranger === 'N/A' ? '' : cloData.lastRefiResetArranger,
    });
  }

  fetchCloData(): void {
    this.isLoading=true;
    this.cloService.getCloById(this.uniqueId).subscribe({
      next: (data) => {  
        this.isLoading=false;   
        if(data != null){
          this.handleCloData(data);
          this.cLO_Id = data.clO_ID;
        }     
      },
      error: (error) => {
        this.isLoading =  false;
        console.error('Error fetching CLO details', error);
      }
    });
  }  

}