@import "../../../../variables.scss";

// Variables
$primary-color: #4061c7;
$border-color: #e6e6e6;
$header-bg: #fafafa;
$text-dark: #1a1a1a;
$text-gray: #666666;
$border-radius: 4px;

// Layout & Container Styles
.row.mr-0.ml-0 {
  width: 100%;
}

.table-container {
  min-height: 200px;
}

// Kendo Grid Core Styles
.k-grid {
  max-height: 1000px;

  tr {
    font-size: 14px; // Adjusted from 48px for better readability
  }
}

// Kendo Grid Custom Styles
:host ::ng-deep .k-grid {
  .k-grid-header {
    .k-header {
      background-color: $header-bg;

      .k-column-title {
        font-size: 14px;
        font-weight: 700;
        color: $text-gray;
        white-space: initial !important;
        word-wrap: break-word;
        min-height: 28px;
        display: flex;
        align-items: center;
      }
    }
  }

  .k-grid-content {
    td {
      background-color: #ffffff;
      font-size: 14px;
      font-weight: 400;
      color: $text-dark;
    }
  }

  .k-grid-header-table,
  .k-grid-table {
    width: 100% !important;
  }
}

// Cell Content Styles
.cell-content {
  height: 1.5rem;

  .edit-icon-img {
    display: none;
  }

  &:hover .edit-icon-img {
    display: block;
  }
}

// Static Form Container Styles
.static-form-container {
  padding: 20px;
  background-color: #ffffff;

  .form-label {
    color: $text-gray;
    font-size: 14px;
    font-weight: 400;
  }

  .form-value {
    color: $text-gray;
    font-size: 14px;
    font-weight: 700;
  }

  .content-height {
    height: 1.5rem;
  }

  .content-padding {
    padding: 0.5rem 1rem;
  }
}

// Button Styles
.btn-warning-export:not([disabled]) {
  border: 1px solid $primary-color !important;
  border-radius: $border-radius;
  color: $primary-color;
}

.btn-save-clo {
  height: 32px;
  padding: 6px 16px;
}

// TabStrip Styles
:host ::ng-deep {
  .k-tabstrip {
    width: 100%;

    .k-item {
      color: $text-gray;

      &.k-active {
        color: $primary-color;
        border-bottom: 2px solid $primary-color;
      }
    }
  }

  .k-tabstrip-top {
    > .k-content,
    > .k-tabstrip-content {
      padding: 0px;
    }
  }
}

// Utility Classes
.edit-icon {
  cursor: pointer;
  padding-left: 5px;
}

.nodata-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  margin: 20px 0;
  text-align: center;

  img {
    display: block;
    max-width: 100%;
    height: auto;
  }
}

.flat-table-container {
  width: 100%;
  overflow-x: auto;

  .flat-table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
      padding: 8px;
      border: 1px solid #ddd;
      text-align: left;
    }

    th {
      background-color: #f4f4f4;
      font-weight: bold;
    }

    tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    tr:hover {
      background-color: #f5f5f5;
    }
  }
}

.clo-table-content {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.k-button.k-button-md.k-rounded-md.k-button-solid.k-button-solid-primary:hover {
  background-color: #021155;
  cursor: pointer;
}

.custom-btn {
  border: 1px solid #4061c7;
  background: white;
  color: #4061c7;
}

.me-2 {
  padding-bottom: 4px;
}

.clo-table-body {
  .clo-table-content {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  .clo-active {
    padding: 10px 1rem;

    .title {
      color: #000000;
      width: calc(100% - 100px) !important;
    }
  }

  .custom-size {
    padding-right: 32px;
  }

  .clo-in-active {
    padding: 10px 1rem;

    .title {
      color: #000000;
      width: calc(100% - 100px) !important;
    }
  }

  .btn-save-clo {
    height: 32px;
    padding: 6px 16px;
  }

  .clo-item {
    background-color: #f5f9ff;
    margin-top: 11px;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;

    &:last-child {
      border-bottom: none !important;
    }
  }
}

.custom-kendo-cab-table-grid .k-grid-content {
  height: 100%;
}

.custom-kendo-cab-table-grid .k-grid-table {
  height: 100%;
}

.custom-kendo-cab-table-grid .k-grid-row {
  height: 44px; /* Adjust this value as needed */
}

.custom-kendo-cab-table-grid .k-grid-cell {
  height: 100%;
  display: flex;
  align-items: center;
}

.btn-reset-container {
  display: flex;
  justify-content: flex-start;
}

.char-count {
  position: absolute;
  right: 10px;
}

.empty-text {
  border: 1px solid #e6e6e6;
  height: 56px;
}

.custom-quillcontainer {
  border-bottom: 1px solid #dedfe0;
  border-left: 1px solid #dedfe0;
  border-right: 1px solid #dedfe0;
  align-items: center;
}

.headerCustomClass {
  font-size: 60px;
}

.k-grid-custom {
  .k-grid-content {
    td {
      font-size: 48px;
    }

    tr:last-child > td {
      border-bottom: 1px solid #dee2e6;
    }
  }

  .k-grid-table {
    border-bottom: 1px solid #dee2e6;
  }
}

:host ::ng-deep .foot-editor-section {
  border: 1px solid #dedfe0;
  background: #ffffff;
}

:host ::ng-deep .k-tabstrip {
  width: 100%;

  .k-item {
    color: #666666;

    &.k-active {
      color: #4061c7;
      border-bottom: 2px solid #4061c7;
    }
  }
}

:host ::ng-deep .k-tabstrip-top > .k-content,
:host ::ng-deep .k-tabstrip-top > .k-tabstrip-content {
  padding: 0px;
}

.cell-content .edit-icon-img {
  display: none;
}

.cell-content:hover .edit-icon-img {
  display: block;
}

.cell-content {
  height: 1.5rem;
}

:host ::ng-deep .k-grid {
  .k-grid-header {
    .k-header {
      .k-column-title {
        white-space: initial !important;
        word-wrap: break-word;
        min-height: 28px;
        display: flex;
        align-items: center;
      }
    }
  }
}

:host ::ng-deep .k-grid {
  .k-grid-header {
    .k-header {
      background-color: #fafafa;

      .k-column-title {
        font-size: 14px;
        font-weight: 700;
        color: #666666;
        white-space: initial !important;
        word-wrap: break-word;
        min-height: 28px;
        display: flex;
        align-items: center;
      }
    }
  }

  .k-grid-content {
    td {
      background-color: #ffffff;
      font-size: 14px;
      font-weight: 400;
      color: #1a1a1a;
    }
  }
}

.btn-download-template {
  border-left: 2px solid #ffffff;
}
.btn-download-template:focus {
  border-left: 2px solid #ffffff !important;
}
.btn-download-template:active {
  border-left: 2px solid #ffffff !important;
}
.btn-import-template {
  border-right: 2px solid #ffffff;
}
.btn-import-template:active {
  border-right: 2px solid #ffffff !important;
}
.btn-import-template:focus {
  border-right: 2px solid #ffffff !important;
}

::ng-deep .k-grid td:hover {
  border-bottom: $cell-bottom-border-blue !important;
}

::ng-deep .k-grid td:active {
  background-color: $cell-background-color !important;
  border-bottom: $default-cell-bottom-border !important;
}

.border-primary:hover {
  border-bottom: $cell-bottom-border-blue !important;
}

.form-value:hover .edit-icon {
  display: block !important;
}

.bottom-border {
  border-bottom: $cell-bottom-border-white !important;
}

.border-primary:active {
  background-color: $cell-background-color !important;
  border-bottom: $cell-bottom-border-white !important;
}

.domicile-dropdown {
  width: 100px;
  height: 32px;
  ::ng-deep .k-dropdown-wrap {
    background-color: #ffffff;
    border: 1px solid $border-color;
  }
}

.domicile-label {
  color: #4061c7;
  background-color: #f5f9ff;
  border-radius: 2px;
  font-size: 14px;
  font-weight: 400;
  padding: 8px;
  height: 32px;
}

.table-border {
  border-bottom: 1px solid #00000014;
}
.export-btn:hover:not([disabled]) {
  background-color: $nep-light-h-bg;
}
.threeDots-btn:hover:not([disabled]) {
  background-color: $nep-light-h-bg;
}
.text-link {
  color: #93b0ed;
  padding-right: 16px;
  cursor: pointer;
  text-decoration: underline;
}

.static-table-header {
  background-color: #fafafa;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  color: #666666;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

::ng-deep .custom-grid,
::ng-deep .custom-pivot-grid {
  td{
    padding: 4px 8px !important;
  }
 th {
    padding: 4px 8px !important;
  }
}

/* app.component.css */
.icon-container {
  position: relative;
  display: inline-block;
}

.k-popup {
  z-index: 1000;
}

.kebab-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color:$primary-color;
}
.threeDots-btn{
  width: 24px;
}