@import '../../../../assets/dist/css/font.scss';
@import '../../../../variables';
$color-black: #000000;
$color-white: #FFFFFF;
$color-light-blue: #EBF3FF;
$color-active-blue: #D9EDFF;
$color-border: #F2F2F2;
$color-border-active: #93B0ED;
$padding-small: 0.5rem 1rem;
$padding-medium: 10px 1rem;
$border-radius-small: 4px;
$border-bottom:1px solid #E6E6E6;

.company-facts {
    display: flex;
    align-items: center;
    padding-right:20px;
    padding-left:20px;
    gap: 16px;
    height: 52px;
    justify-content: space-between;
    border-bottom: 1px solid #e6e6e6;
}
.key-value-pair {
    display: flex;
    flex-direction: row;
    gap: 14px;
    margin-bottom: 4px;
}

  .value {
    margin-left: 10px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

.company-details{
    height: 48px;
    padding-top: 12px;
    padding-bottom: 12px;
    padding-right: 40px;
    padding-left: 40px;
    border-bottom: 1px solid #e6e6e6;
    width: webkit-fill-available;
    margin-left: 0px;
    // background-color:#fafafa;
}
.investment-company{
    color:#666666;
    padding-right:16px;
    letter-spacing:normal;
}
.investment-company:hover{
    cursor:pointer
}
.company-names{
    color:#4061C7;
    padding-left:16px;
    font-size:14px;
    font-weight: 500;
    letter-spacing: normal;
}
.company-name-detail{
    height: 52px;
    padding-top: 8px;
    padding-bottom: 8px;
    padding-right: 20px;
    padding-left: 20px;
    background: var(--Color-Primary-Primary---43, #E3EEFF);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    margin-top: 12px;
    color: #1A1A1A;
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    margin-left:20px;
    margin-right:20px;
}
.k-tabstrip-top > .k-tabstrip-items-wrapper .k-item.k-active {
border-right: none;
border-bottom: 2px solid #4061C7 !important;
}
.company-facts-tab{
    height: 42px;
    padding-left: 12px;
    background-color: #F5F9FF;
    display: flex;
    align-items: center;
    margin-left:20px;
    margin-right:20px;
}
.clo-fs-tab {
    /* Default styles */
    background-color: transparent;
    color: #666666;
    padding: 10px;
    cursor: pointer;
    height: 60px;
    display: flex;
    align-items: center;
    border:none;
  }
  
  .clo-fs-tab.highlight {
    color:#4061C7;
    border-bottom: 2px solid #4061C7;
    height:42px
  }
.company-facts-invest-summary{
   height:495px;
   margin:25px;
}

.company-facts-staticDataContainer{
  border:1px solid #E6E6E6;
   border-radius:4px;
}

.company-facts-glo-container{
  border:1px solid #E6E6E6;
  border-radius:4px;
   margin-top:25px;

}

.company-facts-section{
    font-size: 16px;
    font-weight: 600;
    color: #000000;
}
.section-parameters{
    display: flex;
    justify-content: space-between;
    height: 246px;
    padding: 20px;
}
.key-value-section{
    width:579px;
}
.key{
    color:#666666;
    size:14px;
    width:228px;
    padding-top:4px;
    padding-bottom: 4px;
    letter-spacing: normal;
}
.value{
    width:335px;
    padding-top:4px;
    padding-bottom: 4px;
    letter-spacing: normal;
}
.investment-summary{
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    color: #333333;
    size: 14px;
    word-break: break-word;
    letter-spacing: normal;
}
@media (max-width: 768px) {
    .key-value-pair {
      flex-direction: column;
      align-items: flex-start;
    }
  
    .value {
      margin-left: 0;
      margin-top: 5px;
      max-width: 100%; 
    }
  }



  .clo-table-body {
    .clo-table-content {
        border-bottom-left-radius: $border-radius-small;
        border-bottom-right-radius: $border-radius-small;
    }

    .clo-active {
        padding: $padding-medium;
       .title {
            color: $color-black;
            width: calc(100% - 100px) !important;
        }
    }
    .custom-size{
    padding-right: 32px;
    }
    .clo-in-active {
        padding: $padding-medium;
       .title {
            color: $color-black;
            width: calc(100% - 100px) !important;
        }
    }

    .clo-item {
        background-color:#F5F9FF ;
        margin-top: 11px;
        border-top-right-radius: $border-radius-small;
        border-top-left-radius: $border-radius-small;
        &:last-child {
            border-bottom: none !important;
            
        }
    }
}
.btn-warning {
    background: #ffffff 0% 0% no-repeat padding-box !important;
    border: 1px solid #4061c7 !important;
    border-radius: 4px;
    opacity: 1;
    color: var(--Color-Primary-Primary---78, #4061c7);
  
    &:focus,
    &:active {
      outline: none;
      box-shadow: none;
      border: 1px solid #4061c7 !important;
    }
  }
  .btn-save-clo {
    height: 32px;
    padding: 6px 16px;
  }
  .btn-reset-container {
    display: flex;
    justify-content: flex-start;
  }
  .char-count{
    position: absolute;
    right: 10px;
  }
  .empty-text{
    height: 56px;
  }
  
  .custom-quillcontainer{
    border-top: 1px solid #DEDFE0;
    align-items: center;
  }

:host ::ng-deep {
  .k-tabstrip {
    width: 100%;
    
    
      
      .k-item {
        color: #666666;
        
        &.k-active {
          color: #4061C7;
          border-bottom: 2px solid #4061C7;
        }
      }
    
  }
}

:host ::ng-deep .k-tabstrip-top > .k-content, 
:host ::ng-deep .k-tabstrip-top > .k-tabstrip-content {
    padding: 0px;
}

.edit-icon{
  cursor: pointer;
}
.custom-box{
  border: 1px solid #E6E6E6;
}
.custom-border{
  border-bottom: 1px solid #E6E6E6;
}
.company-facts-no-borders{
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.investment-summary-no-borders{
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}