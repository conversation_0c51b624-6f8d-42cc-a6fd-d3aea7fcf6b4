<form [formGroup]="managedAccountForm" class="mr-4 ml-4 form-container">
  <div class="row mr-0 ml-0 main-Container-height">
    <div class="col-md-12 pr-4 pl-4 custom-Fixedheight pb-4">
      <div class="row mr-0 ml-0">
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('managedAccountName').invalid && submitted}">
            Managed Account Name
          </div>
          <kendo-textbox formControlName="managedAccountName" placeholder="Type here" id="managedAccountName"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('domicile').invalid && submitted}">
            Domicile
          </div>
          <kendo-textbox formControlName="domicile" placeholder="Type here" id="domicile"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('commencementDate').invalid && submitted}">
            Commencement date
          </div>          
          <kendo-datepicker formControlName="commencementDate" placeholder="Type here" (focus)="setCurrentDate('commencementDate')" id="commencementDate"
            format="d MMMM yyyy" class="custom-input"></kendo-datepicker>
        </div>

        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('investmentPeriodEndDate').invalid && submitted}">
            Investment period end date
          </div>
          <kendo-textbox formControlName="investmentPeriodEndDate" placeholder="Type here" id="investmentPeriodEndDate"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('maturityDate').invalid && submitted}">
            Maturity date
          </div>
          <kendo-datepicker formControlName="maturityDate" placeholder="Type here" (focus)="setCurrentDate('maturityDate')" id="maturityDate"
            format="MMMM yyyy" class="custom-input"></kendo-datepicker>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('commitmentOutstanding').invalid && submitted}">
            Commitment Outstanding
          </div>
          <kendo-textbox formControlName="commitmentOutstanding" placeholder="€ 148.00m" id="commitmentOutstanding"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('baseCurrency').invalid && submitted}">
            Base Currency
          </div>
          <kendo-textbox formControlName="baseCurrency" placeholder="Type here" id="baseCurrency"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('investmentManager').invalid && submitted}">
            Investment Manager
          </div>
          <kendo-textbox formControlName="investmentManager" placeholder="Type here" id="investmentManager"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('administrator').invalid && submitted}">
            Administrator
          </div>
          <kendo-textbox formControlName="administrator" placeholder="Type here" id="administrator"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('custodian').invalid && submitted}">
            Custodian
          </div>
          <kendo-textbox formControlName="custodian" placeholder="Type here" id="custodian"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('legalCounsel').invalid && submitted}">
            Legal Counsel
          </div>
          <kendo-textbox formControlName="legalCounsel" placeholder="Type here" id="legalCounsel"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('lei').invalid && submitted}">
            LEI
          </div>
          <kendo-textbox formControlName="lei" placeholder="Type here" id="lei"
            class="custom-input"></kendo-textbox>
        </div>
      </div>

      <div class="row">
        <div class="pl-4 pb-3 pr-4 footer">
          <div class="btn-reset-container">
            <button id="btn-reset" (click)="onReset()" class="btn TextTruncate btn-warning mr-2 TextTruncate" 
              [ngClass]="{'disabled-btn': !isEdited}" [disabled]="!isEdited">Reset</button>
          </div>
          <div>
            <button id="btn-cancel" (click)="onCancel()"
              class="btn TextTruncate btn-warning mr-2 TextTruncate">Cancel</button>
            <button id="btn-save" class="btn-save" (click)="onSubmit()" 
              [ngClass]="{'disabled-save-btn': !isEdited}" [disabled]="!isEdited">Save</button>
          </div>
        </div>
      </div>

      <div *ngIf="showPopup">
        <confirm-modal IsInfoPopup="true" customwidth="600px" modalTitle="Added a Managed Account" primaryButtonName="OK" (primaryButtonEvent)="onCloseDialog()">
          <div>
            <div class="oprtnkpi-lh d-flex align-items-center justify-content-start">
              <div>
                <img src="assets/dist/images/greencheck.svg" alt="Saved Image" class="mr-2">
              </div>
              <div class="popup-text">
                You have successfully added <b>{{savedData.managedAccountName}}</b> 
              </div>
            </div>
          </div>
        </confirm-modal>
      </div>
    </div>
  </div>
</form>

<app-loader-component *ngIf="isLoading"></app-loader-component>
