<div class="clo-container">
  <div class="card clo-card">
    <div class="card-body">
      <form [formGroup]="cloForm" class="card-container">
        <h5 *ngIf="isCloForm" class="mb-4 header-section">CLO Summary</h5>
        <div *ngIf="isCloForm" class="clo-section">
          <div class="form-row">
            <div class="col-6">
              <label for="companyName">Company Name</label>
              <input
                type="text"
                class="form-control input-container"
                id="companyName"
                [placeholder]="this.companyName"
                readonly
              />
            </div>
            <div class="col-6 domocile-selection">
              <label for="domicile"
                >Domicile <span class="required">*</span></label
              >                 
                <kendo-combobox  id="domicile-clo-selection" [data]="domicileList" class="domicile-combobox"
                  [placeholder]="'Select here'" formControlName="domicile" [clearButton]="false">
                </kendo-combobox>
          </div>
          </div>
          <div class="form-row">
            <div class="form-group col-md-3">
              <label for="issuer"
                >Issuer <span class="required">*</span></label
              >        
              <kendo-textbox
                formControlName="issuer"
                placeholder="Type here"
                id="issuer"
                class="custom-input row-height"
              ></kendo-textbox>
            </div>
            <div class="form-group col-md-3">
              <label for="arranger">Arranger</label>
              <kendo-textbox
                formControlName="arranger"
                placeholder="Type here"
                id="arranger"
                class="row-height"
              ></kendo-textbox>
            </div>
            <div class="form-group col-md-3">
              <label for="trustee">Trustee</label>
              <kendo-textbox
                formControlName="trustee"
                placeholder="Type here"
                id="trustee"
                class="row-height"
                style="opacity: 1"
              ></kendo-textbox>
            </div>
            <div class="form-group col-md-3">
              <label for="priced">Priced</label>
              <kendo-datepicker
                placeholder="Select here"
                (focus)="setCurrentDate('priced')"
                formControlName="priced"
                id="priced"
                format="d MMMM yyyy"
                class="custom-input"
              ></kendo-datepicker>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group col-md-3">
              <label for="closed">Closed</label>
              <kendo-datepicker
                placeholder="Select here"
                (focus)="setCurrentDate('closed')"
                formControlName="closed"
                id="closed"
                format="d MMMM yyyy"
                class="custom-input"
              ></kendo-datepicker>
            </div>
            <div class="form-group col-md-3">
              <label for="lastRefi">Last Refi Date</label>
              <kendo-datepicker
                placeholder="Select here"
                (focus)="setCurrentDate('lastRefiDate')"
                formControlName="lastRefiDate"
                id="lastRefi"
                format="d MMMM yyyy"
                class="custom-input"
              ></kendo-datepicker>
            </div>
            <div class="form-group col-md-3">
              <label for="lastReset">Last Reset Date</label>
              <kendo-datepicker
                placeholder="Select here"
                (focus)="setCurrentDate('lastResetDate')"
                formControlName="lastResetDate"
                id="lastReset"
                format="d MMMM yyyy"
                class="custom-input"
              ></kendo-datepicker>
            </div>
            <div class="form-group col-md-3">
              <label for="lastRefiResetArranger">Last Refi/Reset Arranger</label>
              <kendo-textbox
                formControlName="lastRefiResetArranger"
                placeholder="Type here"
                id="lastRefiResetArranger"
                class="row-height"
              ></kendo-textbox>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group col-md-3">
              <label for="callEndDate">Call End Date</label>
              <kendo-datepicker
                placeholder="Select here"
                (focus)="setCurrentDate('callEndDate')"
                formControlName="callEndDate"
                id="callEndDate"
                format="d MMMM yyyy"
                class="custom-input"
              ></kendo-datepicker>
            </div>
            <div class="form-group col-md-3">
              <label for="originalReinvestment"
                >Original End of Reinvestment Date</label
              >
              <kendo-datepicker
                placeholder="Select here"
                (focus)="setCurrentDate('originalEndOfReinvestmentDate')"
                formControlName="originalEndOfReinvestmentDate"
                id="curr-reinvestment-Date"
                format="d MMMM yyyy"
                class="custom-input"
              ></kendo-datepicker>
            </div>
            <div class="form-group col-md-3">
              <label for="currentReinvestment"
                >Current End of Reinvestment Date</label
              >
              <kendo-datepicker
                placeholder="Select here"
                (focus)="setCurrentDate('currentEndOfReinvestmentDate')"
                formControlName="currentEndOfReinvestmentDate"
                id="currentEndOfReinvestmentDate"
                format="d MMMM yyyy"
                class="custom-input"
              ></kendo-datepicker>
            </div>
            <div class="form-group col-md-3">
              <label for="currentMaturity">Current Maturity Date</label>
              <kendo-datepicker
                placeholder="Select here"
                (focus)="setCurrentDate('currentMaturityDate')"
                formControlName="currentMaturityDate"
                id="currentMaturityDate"
                format="d MMMM yyyy"
                class="custom-input"
              ></kendo-datepicker>
            </div>
            <div class="form-group col-md-3"></div>
          </div>
        </div>

        <div *ngIf="!isCloForm" id="clo-review-from">
          <app-clo-review-form [CLOModel] = "savedData" [companyName] = "companyName" (isCLOForm)="isCloForm = $event" (cloData)="handleCloData($event)" ></app-clo-review-form>
        </div>

        <!-- Buttons -->
        <div
        
          class="d-flex"
          style="padding-right: 10px; padding-left: 10px"
          [ngClass]="{'justify-content-between': isCloForm, 'justify-content-end': !isCloForm}"
        >
          <button
            id="btn-reset-clo"
            class="btn TextTruncate btn-warning mr-2 TextTruncate"
            (click)="onReset()"
            [disabled]="!isEdited"
            [ngClass]="{'disabled-btn': !isEdited}"
            *ngIf="isCloForm"
          >
            Reset
          </button>
          <div class="btn-controls">
            <button
              id="btn-cancel-clo"
              (click)="onCancel()"
              class="btn TextTruncate btn-warning mr-2 TextTruncate"
            >
              Cancel
            </button>
            <button
              id="btn-save-clo"              
			  *ngIf="isCloForm"
              class="btn-save-clo btn btn-primary text-center"
              (click)="onSave()"
                [ngClass]="{'disabled-save-btn': !isEdited || !cloForm.get('issuer').value.trim() || !cloForm.get('domicile').value.trim(), 'save-enabled': isEdited && cloForm.get('issuer').value.trim() && cloForm.get('domicile').value.trim()}"
                [disabled]="!isEdited || !cloForm.get('issuer').value.trim() || !cloForm.get('domicile').value.trim()"
            >
              Save
            </button>
            <button
              id="btn-add-clo"
              *ngIf="!isCloForm"
              class="btn-save-clo btn btn-primary"
              (click)="saveCLO()"
            >
              Add CLO
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
<app-loader-component *ngIf="isLoading"></app-loader-component>