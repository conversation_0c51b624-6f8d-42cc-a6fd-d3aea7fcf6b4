import { Component, Input, OnInit, Output, EventEmitter } from "@angular/core";

@Component({
  selector: "app-kendo-button",
  templateUrl: "./kendobutton.component.html",
  styleUrls: ["./kendobutton.component.scss"],
})
export class KendoButton implements OnInit {
  @Input() type: String = "";
  @Input() name: String = "";
  @Input() iconClass: String = "";
  @Input() icon: String = "";
  @Input() imageUrl: String = "";
  @Input() disabled: boolean = false;
  @Input() passedClass: any;
  @Output() onClick: EventEmitter<any> = new EventEmitter();
  @Output() onFocus: EventEmitter<any> = new EventEmitter();
  @Output() onBlur: EventEmitter<any> = new EventEmitter();
  iconStyle: string = 'icon-primary';
  ngOnInit(): void {
    switch (this.type) {
      case "Primary": this.iconStyle = 'button-icon-primary'; break;
      case "Secondary": this.iconStyle = 'button-icon-secondary'; break;
    }
  }

  getStyleClass() {
    return {
      "app-kendo-button": true,
      "app-button-primary": this.type === "Primary",
      "app-button-secondary": this.type === "Secondary",
      "app-button-danger":this.type =="Danger",
    };
  }
  getMergedClass() {
    const base = this.getStyleClass();
    if (!this.passedClass) return base;
    if (typeof this.passedClass === 'string') {
      // Split string by space and set true for each class
      this.passedClass.split(' ').forEach((cls: string) => { if (cls) base[cls] = true; });
      return base;
    }
    if (Array.isArray(this.passedClass)) {
      this.passedClass.forEach((cls: string) => { if (cls) base[cls] = true; });
      return base;
    }
    if (typeof this.passedClass === 'object') {
      return { ...base, ...this.passedClass };
    }
    return base;
  }
}
