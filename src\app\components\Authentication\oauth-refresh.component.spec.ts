import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { OidcAuthService } from 'src/app/services/oidc-auth.service';
import { OauthRefreshComponent } from './oauth-refresh.component';
import { AccountService } from 'src/app/services/account.service';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { HttpClientTestingModule } from '@angular/common/http/testing';
class MockAccountService {}
describe('OauthRefreshComponent', () => {
  let component: OauthRefreshComponent;
  let fixture: ComponentFixture<OauthRefreshComponent>;

  beforeEach(() => {
    const oidcAuthServiceStub = () => ({ signinSilentCallback: () => ({}) });
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      imports: [HttpClientTestingModule,ToastrModule.forRoot()],
      declarations: [OauthRefreshComponent],
      providers: [
        { provide: OidcAuthService, useFactory: oidcAuthServiceStub },
        { provide: AccountService, useClass: MockAccountService },
        { provide: "BASE_URL", useValue: "http://localhost" },
        ToastrService,
        { provide: "ToastConfig", useValue: {} },
      ]
    });
    fixture = TestBed.createComponent(OauthRefreshComponent);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });
});
