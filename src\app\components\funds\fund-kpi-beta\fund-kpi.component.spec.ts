import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of } from 'rxjs';
import { FundKpiComponent } from './fund-kpi.component';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { ToastrService } from 'ngx-toastr';
import { FundService } from 'src/app/services/funds.service';
import { AuditService } from 'src/app/services/audit.service';
import { OidcAuthService } from 'src/app/services/oidc-auth.service';
import { Router, ActivatedRoute } from '@angular/router';

describe('FundKpiComponent', () => {
  let component: FundKpiComponent;
  let fixture: ComponentFixture<FundKpiComponent>;

  beforeEach(async () => {
    const mockMiscService = jasmine.createSpyObj('MiscellaneousService', ['getMessageTimeSpan']);
    const mockPortfolioCompanyService = jasmine.createSpyObj('PortfolioCompanyService', ['getPortfolioCompany', 'getfinancialsvalueTypes']);
    mockPortfolioCompanyService.getfinancialsvalueTypes.and.returnValue(of([]));
    const mockToastrService = jasmine.createSpyObj('ToastrService', ['success', 'error']);
    const mockFundService = jasmine.createSpyObj('FundService', ['getFundKPIValues']);
    const mockAuditService = jasmine.createSpyObj('AuditService', ['getPortfolioEditSupportingCommentsData']);
    const mockOidcAuthService = jasmine.createSpyObj('OidcAuthService', ['addUpdateUserSessionState']);
    const mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    const mockActivatedRoute = jasmine.createSpyObj('ActivatedRoute', [], { snapshot: { params: {} } });

    mockMiscService.getMessageTimeSpan.and.returnValue(5000);
    mockFundService.getFundKPIValues.and.returnValue(of({}));
    mockAuditService.getPortfolioEditSupportingCommentsData.and.returnValue(of({}));

    await TestBed.configureTestingModule({
      declarations: [ FundKpiComponent ],
      providers: [
        { provide: MiscellaneousService, useValue: mockMiscService },
        { provide: PortfolioCompanyService, useValue: mockPortfolioCompanyService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: FundService, useValue: mockFundService },
        { provide: AuditService, useValue: mockAuditService },
        { provide: OidcAuthService, useValue: mockOidcAuthService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FundKpiComponent);
    component = fixture.componentInstance;
    // Set required inputs to prevent errors
    component.modelList = { currencyDetail: { currencyCode: 'USD' } };
    component.fundId = 1;
    component.moduleId = 1;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});