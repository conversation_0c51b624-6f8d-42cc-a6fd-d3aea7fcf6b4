export interface GroupModel {
    id: number;
    groupName: string;
    frequency: any;
    companyList: any[];
    fundList: any[];
    recipientList: any[];
    submitterList: any[];
    attachmentList: any[];
    deletedAttachments: number[];
    reminderList: ReminderModel[];
    expanded: boolean;
    deleted: boolean;
  }

  export interface ReminderModel {
    id: number;
    noOfDays: number;
    period: string;
    deleted: boolean;
  }
  
  export interface DataRequestAddOrUpdateModel {
    id: number,
    requestId: string,
    name: string
    isAutomatic: boolean,
    isActive: boolean,
    groupList: AddOrUpdateGroupModel[]
  }
  export interface AddOrUpdateGroupModel {
    id: number;
    name: string;
    frequency: string;
    companyIds: string;
    fundIds: string;
    receiverIds: string;
    submitterIds: string;
    attachments: any[];
    externalUserIds: string;
    reminderIds: string;
    reminderList: ReminderModel[];
    requestedPeriod: string;
    dueDate: string;
    externalUserList: any[];
  }