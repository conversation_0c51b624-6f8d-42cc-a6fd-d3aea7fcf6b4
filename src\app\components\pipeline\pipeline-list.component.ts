﻿import { ChangeDetectorRef, Component, ElementRef, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { LazyLoadEvent } from 'primeng/api';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { AccountService } from '../../services/account.service';
import { MiscellaneousService } from '../../services/miscellaneous.service';
import { FeaturesEnum } from '../../services/permission.service';
import { PipelineService } from '../../services/pipeline.service';
import { KendoService } from 'src/app/services/kendo.service';
import { Observable, of } from 'rxjs';
import { GridDataResult } from '@progress/kendo-angular-grid';
import { SortDescriptor, State } from "@progress/kendo-data-query";
@Component({
  selector: "pipeline-list",
  styleUrls: ["./pipeline-list.component.scss"],
  templateUrl: "./pipeline-list.component.html",
})
export class PipelineListComponent implements OnInit {
  feature: typeof FeaturesEnum = FeaturesEnum;
  pipelineList: any = [];
  closeResult: string;
  pagerLength: any;
  dataTable: any;
  blockedTable: boolean = false;
  totalRecords: number;
  totalPage: number;
  globalFilter: string = "";
  paginationFilterClone: any = {};
  tabName: string = "Pipeline Dashboard";
  tabList: ITab[] = [];
  isLoader: boolean = false;
  public view: Observable<GridDataResult>;
  public state: State = {
    skip: 0,
    take: 50,
  };
  sort: SortDescriptor[] = [];
  constructor(
    private kendoService: KendoService,
    private elementRef: ElementRef,
    private router: Router,
    private _pipelineService: PipelineService,
    protected changeDetectorRef: ChangeDetectorRef,
    private spinner: NgxSpinnerService,
    private miscService: MiscellaneousService
  ) {
    this.pagerLength = this.miscService.getPagerLength();
    localStorage.setItem("headerName", "Pipeline Dashboard");
  }

  ngOnInit() {
    this.getTabList();
  }
  onTabClick(tab: ITab) {
    if (document.getElementById("HeaderNameID")) {
      this.miscService.getTitle(tab?.name);
    }
    if (tab != null || tab != undefined) {
      this.tabName = tab.name;
      if (this.tabName == "Pipeline Details") {
        this.getPipelineList(null);
      }
    }
  }
  getPipelineList(event: any) {
    if (event == null) {
      event = {
        first: 0,
        rows: 50,
        globalFilter: null,
        sortField: null,
        sortOrder: 1,
      };
    }
    this.paginationFilterClone = JSON.parse(JSON.stringify(event));
    this.blockedTable = true;
    this.isLoader = true;
    this._pipelineService
      .getPipelineList({ paginationFilter: event })
      .subscribe({
        next: (result) => {
          if (result != null) {
            this.pipelineList = result?.pipelineList;
            this.totalRecords = result?.totalRecords;
            if (this.totalRecords > 50) {
              this.totalPage = Math.ceil(this.totalRecords / event.rows);
            } else {
              this.totalPage = 1;
            }
          } else {
            this.pipelineList = [];
            this.totalRecords = 0;
          }
          this.blockedTable = false;
          this.isLoader = false;
          this.view = of<GridDataResult>({
            data: this.pipelineList,
            total: this.totalRecords,
          });
        },
        error: (error) => {
          this.blockedTable = false;
          this.isLoader = false;
        },
      });
  }
  exportPipelineList() {
    let event = JSON.parse(JSON.stringify(this.paginationFilterClone));
    event.globalFilter = this.globalFilter;
    event.filterWithoutPaging = true;
    this._pipelineService
      .exportPipelineList({ paginationFilter: event })
      .subscribe((response) => this.miscService.downloadExcelFile(response));
  }
  loadPipelinesLazy(event: LazyLoadEvent) {
    this.getPipelineList(event);
  }
  getTabList() {
    this.tabList = [
      {
        active: true,
        name: "Pipeline Dashboard",
      },
      {
        active: false,
        name: "Pipeline Details",
      },
    ];
  }
  setHeaderName(pipeline: any) {
    localStorage.setItem("headerName", pipeline.name);
  }
  redirectToPipeline(pipeline: any) {
    this.router.navigate(["/pipeline-details", pipeline.encryptedPipelineId]);
  }
  searchLoadPCLazy() {
    let params: any = {
      first: 0,
      rows: 50,
      globalFilter: this.globalFilter != "" ? this.globalFilter : null,
      sortField: null,
      sortOrder: 1,
    };

    if (this.pipelineList.length != 0) {
      let result = this.kendoService.getHeaderValue(
        params,
        null,
        this.getHeaders(),
        this.sort
      );
      params = result.params;
      this.sort = result.parentSort;
    }
    this.getPipelineList(params);
  }
  getHeaders() {
    return [
      { sortFieldName: "name" }
    ];
  }
dataStateChange($event)
{
    this.state.skip = $event.skip;
    this.state.take = $event.take;
    let params:any = {
      first: $event.skip,
      rows: $event.take,
      globalFilter: this.globalFilter != "" ? this.globalFilter : null,
      sortField: null,
      sortOrder: 1,
    }
   let result = this.kendoService.getHeaderValue(params, $event, this.getHeaders(), this.sort);
   params = result.params;
    this.sort = result.parentSort;
  this.getPipelineList(params);
}
}
