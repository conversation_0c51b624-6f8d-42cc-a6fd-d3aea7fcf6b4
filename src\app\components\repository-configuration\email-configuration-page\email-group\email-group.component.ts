import { Component, OnInit } from '@angular/core';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { EmailGroupDto, EmailMemberDto } from "../../../repository-configuration/model/config-model";
import { ToastrService } from 'ngx-toastr';
import { Router } from "@angular/router";

@Component({
  selector: 'app-email-group',
  templateUrl: './email-group.component.html',
  styleUrls: ['./email-group.component.scss']
})
export class EmailGroupComponent implements OnInit {
  emailGroups: EmailGroupDto[] = [];
  showPortfolioCompanies: number = 3;
  companynameMaxLength: number = 10;
  selectedGroupEmails: (EmailMemberDto & { isSelected?: boolean })[] = [];
  expandedGroupId?: number;
  isAllSelected: boolean = false;
  showDeletePopup: boolean = false;
  showEmailGroupDeletePopup : boolean = false;
  modalTitle: string = 'Delete Confirmation';
  confirmationDialogContent: string = 'Are You Sure ?';
  deleteEmail: boolean = false;
  groupToDelete?: EmailGroupDto;

  constructor(
    private emailGroupService: RepositoryConfigService,
    private toastrService: ToastrService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadEmailGroups();
  }

  loadEmailGroups(): void {
    this.emailGroupService.getEmailGroups().subscribe({
      next: (groups) => {
        this.emailGroups = groups;
      },
    });
  }

  onGroupClick(groupId: number): void {
    if (this.expandedGroupId === groupId) {
      this.expandedGroupId = undefined;
      this.selectedGroupEmails = [];
      return;
    }

    this.expandedGroupId = groupId;
    this.updateEmailMembers(groupId);
  }

  private updateEmailMembers(groupId: number) {
    this.emailGroupService.getEmailListByGroupId(groupId).subscribe({
      next: (members) => {
        this.selectedGroupEmails = members.map(member => ({ ...member, isSelected: false }));
      },
    });
  }

  toggleSelectAll(checked: boolean): void {
    this.isAllSelected = !this.isAllSelected;
    this.selectedGroupEmails.forEach(email => email.isSelected = this.isAllSelected);
  }

  toggleEmailItem(data: any) {
    data.isSelected = !data.isSelected;
    this.isAllSelected = this.selectedGroupEmails.every(email => email.isSelected);
    this.showDeletePopup = this.CanShowDeletePopup();
    if(this.showDeletePopup)
      this.confirmationDialogContent = "You want to delete the selected User Details."
  }
  CanShowDeletePopup() :boolean {
    return this.selectedGroupEmails.filter(i=> i.isSelected).length > 0
  }

  getSelectedContacts(): (EmailMemberDto & { isSelected?: boolean })[] {
    return this.selectedGroupEmails.filter(i => i.isSelected);
  }
  closeSelectionPopup(): void {
    this.showDeletePopup = false;
  }

  handleDeleteSelectedClick(): void {
    this.showDeletePopup = false;
    this.showEmailGroupDeletePopup = true;
    this.confirmationDialogContent = "You want to delete the selected User Details."
    this.deleteEmail = true;
  }

  // delete email group section

  deleteEmailGroup(dataItem: EmailGroupDto): void {
    this.groupToDelete = dataItem;
    this.showEmailGroupDeletePopup = true;
    this.confirmationDialogContent = "Deleting of Group will also delete all associated Emails to it."
    this.deleteEmail = false;
  }

  triggerDeleteAction(): void {
    if (this.deleteEmail) {
      // Handle deletion of selected email members
      this.deleteSelectedEmails();
    } else if (this.groupToDelete) {
      // Handle deletion of email group
      this.deleteGroup();
    }
  }

  private deleteSelectedEmails(): void {
    const selectedMembers = this.getSelectedContacts();
    const remainingMembersCount = this.selectedGroupEmails.length - selectedMembers.length;

    // Validate that at least 2 members will remain in the group
    if (remainingMembersCount < 2) {
      this.toastrService.error(
        'Cannot delete selected members. At least 2 email members must remain in the group.',
        '',
        {
          positionClass: 'toast-center-center'
        }
      );
      this.showEmailGroupDeletePopup = false;
      return;
    }

    // Prepare the DTO model
    const deleteRequest = {
      memberIds: selectedMembers.map(member => member.memberId)
    };

    // Make API call to delete selected email members
    if (this.expandedGroupId) {
      this.emailGroupService.deleteEmailMembers(this.expandedGroupId, deleteRequest).subscribe({
        next: (response) => {
          this.toastrService.success(
            `Successfully deleted ${selectedMembers.length} email member(s)`,
            '',
            {
              positionClass: 'toast-center-center'
            }
          );
          this.showEmailGroupDeletePopup = false;
          // Reload the email members for the current group
          this.updateEmailMembers(this.expandedGroupId);
          // Reset selection state
          this.isAllSelected = false;
          this.showDeletePopup = false;
        },
        error: (error) => {
          this.toastrService.error(
            'Failed to delete selected email members',
            '',
            {
              positionClass: 'toast-center-center'
            }
          );
          this.showEmailGroupDeletePopup = false;
        }
      });
    }
  }

  private deleteGroup(): void {
    if (!this.groupToDelete) return;

    this.emailGroupService.deleteEmailGroup(this.groupToDelete.groupId).subscribe({
      next: (response) => {
        this.toastrService.success('Email group deleted successfully!', '', {
          positionClass: 'toast-center-center'
        });
        // Optimistically remove the deleted group from the list
        this.emailGroups = this.emailGroups.filter(
          group => group.groupId !== this.groupToDelete!.groupId
        );
        this.showEmailGroupDeletePopup = false;
        this.groupToDelete = undefined;
        // Reset selected emails if the deleted group was expanded
        this.selectedGroupEmails = [];
        // Reload the email groups list
        this.loadEmailGroups();
      },
      error: (error) => {
        this.toastrService.error(error.error?.message ||'Failed to delete email group', '', {
          positionClass: 'toast-center-center'
        });
        this.showEmailGroupDeletePopup = false;
        this.groupToDelete = undefined;
      }
    });
  }
  cancelDelete(): void {
    this.showEmailGroupDeletePopup = false;
    this.groupToDelete = undefined;
  }

  // edit email group actions..
  editEmailGroup(dataItem: EmailGroupDto): void {
    this.router.navigate(['/edit-email-group/' + dataItem.groupId]);
  }
}
