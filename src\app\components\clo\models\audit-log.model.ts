export interface BaseModel {
    createdOn: Date;
    createdBy: number;
    modifiedOn?: Date;
    modifiedBy?: number;
}

export enum AuditType {
    FileUpload = 0,
    Manual = 1
}

export interface AuditLogModel extends BaseModel {
    auditId: number;
    companyId: string;
    oldValue: string;
    newValue: string;
    auditType: AuditType;
    isDeleted: boolean;
    documentId: string;
    supportingDocumentId: string;
    supportingDocumentName:string;
    comment: string;
    rowIdentity: string;
    columnIdentity: string;
    tableIdentifier: string;
    userName:string;
}

export type { AuditLogModel as default };
