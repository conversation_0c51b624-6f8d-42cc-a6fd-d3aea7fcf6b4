<div class="row tab-shadow mb-0">
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 col-xs-12 pr-0 pl-0">
        <nep-tab id="neptab" class="custom-pipeline-tab" [tabList]=tabList (OnSelectTab)="onTabClick($event)">
        </nep-tab>
    </div>
</div>
<div *ngIf="tabName=='Cover Page Template'" class="" id="th-cover-tab">
    <app-config-page-template> </app-config-page-template>
</div>
<div *ngIf="tabName=='Template List'" class="mt-4" id="th-template">
    <div class="row mr-0 ml-0 lp-report-config-list-section">
        <div class="col-12 col-sm-12 col-lg-12 col-xl-12 search-section">
            <div class="float-left">
                <kendo-textbox (input)="filterGrid($event.target.value)" size="medium" [fillMode]="'solid'" class="k-input-width-680 k-input-custom" selectOnFocus="false"
                    placeholder="Search Template...">
                    <ng-template kendoTextBoxSuffixTemplate>
                        <button class="text-search-button" kendoButton [svgIcon]="searchSVG"></button>
                      </ng-template>
                </kendo-textbox>
            </div>
            <div class="float-right">
                <button id="lp-create-template" kendoButton class="kendo-custom-button" (click)="redirectToTemplate(null)" themeColor="primary">Create New
                    Template</button>
            </div>
        </div>
        <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 content-section">
            <div class="row mr-0 ml-0">
                <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0"> 
                    <kendo-grid [filter]="gridFilter" [data]="templateList" scrollable="virtual" [rowHeight]="44" [sortable]="true"
                        [sort]="sort" class="custom-kendo-lp-report-grid lp-report-grid k-grid-outline-none">
                        <kendo-grid-column field="templateName" [width]="300">
                            <ng-template kendoGridHeaderTemplate>
                                <div class="header-icon-wrapper wd-100">
                                    <span class="TextTruncate S-M">
                                        Template Name
                                    </span>
                                </div>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-rowData>
                                <div class="content float-left TextTruncate template-content" [title]="rowData.templateName">{{
                                    rowData.templateName }}</div>
                            </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="actions" [width]="35">
                            <ng-template kendoGridHeaderTemplate>
                                <div class="header-icon-wrapper float-right">
                                    <span class="TextTruncate S-M">
                                        Actions
                                    </span>
                                </div>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-rowData>
                                <div id="lp-report-edit" class="float-right">
                                    <a id="lp-report-delete-template" title="delete" class="pr-2" (click)="openDeleteTemplate(rowData)"
                                        href="javascript:void" target="_self"><img src="assets/dist/images/delete.svg"
                                            alt="DeleteContent"></a>
                                    <a id="lp-report-edit-template" title="Edit" (click)="redirectToTemplate(rowData)"
                                        href="javascript:void" target="_self"><img src="assets/dist/images/EditIconPageConfig.svg"
                                            alt="EditContent"></a>
                                </div>
                            </ng-template>
                        </kendo-grid-column>
                        <ng-template kendoGridNoRecordsTemplate>
                            <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 no-content-section">
                                <div class="text-center">
                                    <img src="assets/dist/images/no-content-lp-report.svg" alt="No Content" class="no-content-image">
                                </div>
                                <div class="text-center no-content-text pt-3 pb-2">
                                    No template details found
                                </div>
                                <div class="text-center no-content-text">
                                    <a class="S-U template-link" href="javascript:void" (click)="redirectToTemplate(null)"
                                        target="_self">Click Here</a> <span class="Caption-M pl-2 template-text">to create new
                                        template</span>
                                </div>
                            </div>
                        </ng-template>
                    </kendo-grid>
                </div>
            </div>
            <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 footer-section"></div>
        </div>
    </div>
</div>
<kendo-dialog *ngIf="deleteLpTemplate" [actions]="deleteActions" [actionsLayout]="deletePopupLayout"
  (action)="deleteTemplate($event)" title="Delete Template" (close)="closeDeleteTemplate('cancel')">
  <div class="action-layout-config">
    <p class="action-body">Template once deleted cannot be retrieved. Are you 
         <br> sure you want to delete this template?</p>
  </div>
</kendo-dialog>

<app-loader-component *ngIf="isLoader"></app-loader-component>