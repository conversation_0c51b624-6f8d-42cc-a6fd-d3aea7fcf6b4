@import "../../../../variables";
.view-esg-audits-component {
    .TextTruncate
    {
        width: 100%;
    }
    .audit-header {
        padding: 1.25rem;
        border-radius: 0.25rem;
        border: 1px solid $nep-light-grey-b;
        background: $nep-white-secondary;
        height: 6.125rem;
          .audit-kpi-title
          {
            width: calc(100% - 21.5rem);
            .audit-icon
            {
                vertical-align: bottom;
            }
            .title-w-block
            {
                width: 85%;
                padding: 0rem 1.25rem;
            }
            .title-w
            {
                width: 120%;
                color:$nep-dark-grey-title;
            }
            .sub-title-block{
                color:$nep-dark-grey-sub-title
            }
          }
    }
    .audit-sub-section {
        padding: 0.5rem 1.25rem;
        .audit-sub-header
        {
            color: $nep-dark-grey-sub-h;
            font-size: 0.75rem;
            font-style: normal;
            font-weight: 400;
            line-height: 1.125rem;
        }
        .sub-section-title
        {
            color: $nep-dark-doc;  
            padding-top: 0.375rem;        
        }
        .audit-pr{
            padding-right: 2.5rem;
        }
    }
    .esg-audit-table
    {
        border: 1px solid $nep-light-grey-b;
        margin-top: 1.25rem;
        border-radius: 0.25rem;
        .esg-audit-desc
        {
            padding:1.25rem;
            .content
            {
                color:$nep-dark-black;
            }
            .sub-title-audit
            {
                width: 100%;
                padding-top: 0.25rem;
                color:$nep-dark-light;
            }
        }
    }
}
.kpi-audit-page{
    display: block; 
    background: $nep-dark-bg;
    .nep-card{
        left: 1.75rem;
        top: 1.25rem;
        right: 1.25rem;
        position: relative; 
        display: inline-flex;
    }
    .custom-close-icon {
        color: $nep-dark-black;      
    }
    .cursor-filter 
    {
        line-height: 1rem;
    }
    .support-body{
        border:1px solid $nep-light-grey-b;
        border-radius: 0.25rem;
        .support-header{
            border-bottom: 1px solid $nep-dark-b-title;
            background: $nep-light-bg;
            padding: 0.625rem 1rem;
            color: $nep-dark-grey-sub-h;
        }
        .download-link
        {
            text-decoration: underline;
            color:$nep-dark-blue-link;
            &:hover
            {
                color: $nep-primary;
            }
        }
        .support-body-content
        {
            padding: 0.75rem 1rem 0px 0.75rem;
            max-height: calc(100vh - 11.5rem);
            overflow-y: auto;
            min-height: 15rem;
        }
    }
}
.source-doc
{
    padding: 0.25rem 0.5rem 0.25rem 0.75rem;
    border-radius: 1.25rem;
    background-color: $nep-light-bg;
    width: 100%;
    color: $nep-dark-doc;
}
.circle-doc{
    max-width: calc(100%);
    img{
        vertical-align: text-top;
    }
}
.source-link
{
    color: $nep-dark-doc;
    img{
        width: 1.063rem;
        height: 1.063rem;
    }
}
.circle-doc-all{
    max-width: 15rem;
    margin-right: 1rem;
    margin-bottom: 1rem;
}
.audit-body{
    padding-bottom: 2.5rem !important;
}
.text-comments
{
    color: $nep-dark-black;
    padding: 0.5rem 0.25rem 0px 0.5rem;
}
.empty-comments
{
    padding-top:0.5rem;
}
.text-above-image {
    position: absolute;
    color: $nep-white-secondary;
    text-align: center;
    padding-top: 0.375rem;
    padding-left: 0.25rem;
    width: 1rem;
    height: 1.125rem;
  }
  .doc-support
  {
    width: 25rem !important;
  }
 .doc-time {
     width: 15rem !important;
 }
  .source-doc-t
  {
    color: $nep-dark-grey-sub-h;
  }
  .all-download-icon
  {
      vertical-align: text-top;
  }
  .text-align-r{
    text-align: right;
  }
  .text-align-l{
    text-align: left;
  }
  .enlargedRow {
    width: 142px;
    height: 142px;
    resize: none;
    word-wrap: break-word;
    background: #fff;
    text-wrap: wrap;
}
.data-td-span{
    max-height: 95px;
    max-width: 100%;
    width: 100%;
    overflow: auto;
    float: inline-start;
}
.align-top-left{
    vertical-align: top;
}
.esg-audit-table-content{
    margin-bottom: -1px;
}
.value-column-width{
    width: 12.5rem;
}