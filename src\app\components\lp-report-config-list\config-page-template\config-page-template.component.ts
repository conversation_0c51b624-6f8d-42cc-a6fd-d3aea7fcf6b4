import { Component } from "@angular/core";
import { Form<PERSON>uilder, FormGroup, Validators } from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import { CellEditConstants, ImageExtensions } from "src/app/common/constants";
import { LpReportConfigService } from "src/app/services/lp-report-config.service";

const EMPTY_FILE_ID = 0;
const FILE_NAME_SPLIT_CHAR = ".";
const FILE_EXTENSION_PREFIX = ".";
const MAX_FILE_SIZE_MB = 2;
const INCORRECT_FILE_FORMAT = "is incorrect file format";
const BYTE_SIZE: number = 1024;
const INCORRECT_FILE_FORMATS = "are incorrect file formats";
const TOASTER_POSITION = CellEditConstants.ToasterMessagePosition;
const ALLOWED_FILE_FORMATS_MESSAGE =
  "Please refer to allowed file formats list.";
const UPLOAD_IMAGES_UP_TO = "Please upload images up to";
const MB_ONLY = "MB only.";
const INCORRECT_FILE_FORMAT_AND_SIZE = "Incorrect file format and size.";

@Component({
  selector: "app-config-page-template",
  templateUrl: "./config-page-template.component.html",
  styleUrls: ["./config-page-template.component.scss"],
})
export class ConfigPageTemplateComponent {
  form: FormGroup;
  displayFund: any;
  logoDisabled: boolean = false;
  bgDisabled: boolean = false;
  isLogoUploaded = false;
  isBgUploaded = false;
  fundNameOption: any;
  allowCustom = true;
  logoImage: any = [];
  bgImage: any = [];
  existingSDGImages: any = [];
  isVisible: boolean = true;
  useDefaultSignUpPage: boolean = true;
  displayName: any;
 alignmentValues = ["Center", "Left", "Right"];
  selectedOption: string = "option1";
  selectedFundAlignment: any;
  selectedFundColor: any;
  selectedReportColor: any;
  selectedFundFontSize: any;
  selectedFundFontType: any;
  selectedReportAlignment: any;
  selectedReportFontSize: any;
  selectedReportFontType: any;
  selectedLogoAlignment: any;
  defaultSetUp: any;
  imagesTobeDeleted: number[] = [];
  fontType = [
    "sans-serif",
    "Helvetica",
    "HelveticaMedium",
    "Arial",
    "TimesNewRoman",
    "Garamond",
    "PalatinoLinotype",
    "monospace",
    "serif",
    "Georgia",
    "Cambria",
    "Calibri",
    "Verdana",
    "Corbel",
    "FranklinGothic",
  ];
  fontSize = [
    "9px",
    "8px",
    "10px",
    "12px",
    "14px",
    "16px",
    "18px",
    "20px",
    "22px",
    "24px",
    "26px",
    "28px",
    "30px",
    "32px",
  ];
coverPageId:number=0;
initialFormValue:any;
  constructor(
    private lpReportConfigService: LpReportConfigService,
    private fb: FormBuilder,
    private _toasterService: ToastrService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadTemplateData();
    this.form.valueChanges.subscribe(() => {
      this.checkEnableSaveButton();
    });
  }

  loadTemplateData() {
    this.lpReportConfigService.getLpReportCoverPageTemplate().subscribe({
      next: (data) => {
        if (data?.length > 0) {
          const template = data[0];
          this.coverPageId=template.id;
          // Set selected values
          this.selectedFundAlignment = template.fundAlignment;
          this.selectedFundFontSize = template.fundFontSize;
          this.selectedFundFontType = template.fundFontType;
          this.selectedFundColor = template.fundColor;
          this.selectedReportAlignment = template.reportAlignment;
          this.selectedReportFontSize = template.reportFontSize;
          this.selectedReportFontType = template.reportFontType;
          this.selectedReportColor = template.reportColor;
          this.selectedLogoAlignment = template.logoAlignment;
          this.defaultSetUp = template.defaultSetUp;
          this.logoImage=[];
          this.bgImage=[];
          this.displayFund = template.displayFund;
          if(template.logoImageId != null)
          this.logoImage.push( this.createImageObject(template.logoImageId));
          if(template.backgroundImageId != null)
          this.bgImage .push( this.createImageObject(template.backgroundImageId));
          // Update form values
          this.form.patchValue({
            displayName: template.displayName,
            fundNameOption: template.fundNameOption,
            fundAlignment: template.fundAlignment,
            fundFontSize: template.fundFontSize,
            fundFontType: template.fundFontType,
            fundColor: template.fundColor,
            reportAlignment: template.reportAlignment,
            reportFontSize: template.reportFontSize,
            reportFontType: template.reportFontType,
            reportColor: template.reportColor,
            logoAlignment: template.logoAlignment,
            defaultSetUp: template.defaultSetUp,
            defaultSignUpPage: template.defaultSignUpPage,
            displayFund: template.displayFund,        
    
          });
          this.initialFormValue = this.form.getRawValue();
          this.checkEnableSaveButton();
        }
      },
      error: (error) => {
        console.error('Error loading template data:', error);
      },
      complete: () => {
      }
    });
  }
  extractMimeType(dataUrl: string): string {
    const parts = dataUrl.split(',');
    if (parts.length > 1) {
        const mimeType = parts[0].split(':')[1].split(';')[0];
        return mimeType;
    }
    return '';
 }
 generateDummyName(mimeType: string): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  return `${mimeType.replace('/', '_')}_${timestamp}`;
}

  createImageObject(image: string) {
    const mimeType = this.extractMimeType(image);
    return {
      file: new File([], this.generateDummyName(mimeType), { type: mimeType }),
      url: image,
      isExisting: true,
      id: 0
    };
  }
  initForm(): void {
    this.form = this.fb.group({
      displayName: ["LP Report Template", Validators.required], // Input field
      fundNameOption: ["asPerFundName"], // Radio button group
      fundAlignment: [this.selectedFundAlignment], // Dropdown
      fundFontSize: [this.selectedFundFontSize], // Dropdown
      fundFontType: [this.selectedFundFontType], // Dropdown
      fundColor: [this.selectedFundColor], // Color picker
      reportAlignment: [this.selectedReportAlignment], // Dropdown
      reportFontSize: [this.selectedReportFontSize], // Dropdown
      reportFontType: [this.selectedReportFontType], // Dropdown
      reportColor: [this.selectedReportColor], // Color picker
      logoAlignment: [this.selectedLogoAlignment], // Dropdown
      defaultSetUp: [this.defaultSetUp], // Toggle switch
      defaultSignUpPage: [null], // File upload
      logoImage: [null], // File upload
      bgImage: [null],
      displayFund: this.displayFund,
    });
  }
  isSaveEnabled: boolean = false;
  checkEnableSaveButton(): void {
    const currentFormValue = this.form.getRawValue();
    this.isSaveEnabled = JSON.stringify(currentFormValue) !== JSON.stringify(this.initialFormValue);
    this.lpReportConfigService.isSaveEnabled = this.isSaveEnabled;
  }
  onControlValueChange(value: string, controlName: string): void {
    this.form.get(controlName).setValue(value);
    this.checkEnableSaveButton();
  }
  onColorChange(value: string, controlName: string): void {
    this.form.get(controlName).setValue(value);
    this.checkEnableSaveButton();
  }

  onSwitchDefaultSetup(value: boolean): void {
    this.form.get("defaultSetUp").setValue(value);
    this.defaultSetUp = this.form.get("defaultSetUp")?.value;

    this.checkEnableSaveButton();
  }


  // File upload handlers
  onDefaultSignUpPageChange(event: any): void {
    const file = event.target.files[0];
    this.form.get("defaultSignUpPage").setValue(file);
    this.checkEnableSaveButton();
  }

  onLogoImageChange(event: any): void {
    const file = event.target.files[0];
    this.form.get("logoImage").setValue(file);
    this.checkEnableSaveButton();
  }

  // Submit handler
  onSubmit(): void {
    if(this.logoImage.length > 0 && this.isLogoUploaded && !this.logoImage[0].isExisting){
      this._toasterService.success("Upload selected Logo Image", "", {
        positionClass: "toast-center-center",
      });  
    }
    if(this.bgImage.length > 0 && this.isBgUploaded && !this.bgImage[0].isExisting){
      this._toasterService.success("Upload selected Background Image", "", {
        positionClass: "toast-center-center",
      });  
    }
    let model = this.updateFormModel();
    if (this.form.valid) {
      this.lpReportConfigService.saveLpReportCoverTemplate(model).subscribe({
        next: (result: any) => {
          this._toasterService.success("Template saved successfully", "", {
            positionClass: "toast-center-center",
          });
          this.loadTemplateData();
        },
        error: (err) => {
          throw err;
        },
      });
    }
  }

  updateFormModel() {
    let formData = new FormData();
    formData.append("Id", this.coverPageId.toString());
    formData.append("DisplayName", "LP Report Template");
    formData.append("FundNameOption", this.form.get("fundNameOption").value);
    formData.append(
      "FundAlignment",
      this.form.get("fundAlignment").value != null
        ? this.form.get("fundAlignment").value
        : ""
    );
    formData.append(
      "FundFontSize",
      this.form.get("fundFontSize").value != null
        ? this.form.get("fundFontSize").value
        : ""
    );
    formData.append(
      "FundFontType",
      this.form.get("fundFontType").value != null
        ? this.form.get("fundFontType").value
        : ""
    );
    formData.append(
      "FundColor",
      this.form.get("fundColor").value != null
        ? this.form.get("fundColor").value
        : ""
    );
    formData.append(
      "ReportAlignment",
      this.form.get("reportAlignment").value != null
        ? this.form.get("reportAlignment").value
        : ""
    );
    formData.append(
      "ReportFontSize",
      this.form.get("reportFontSize").value != null
        ? this.form.get("reportFontSize").value
        : ""
    );
    formData.append(
      "ReportFontType",
      this.form.get("reportFontType").value != null
        ? this.form.get("reportFontType").value
        : ""
    );
    formData.append(
      "ReportColor",
      this.form.get("reportColor").value != null
        ? this.form.get("reportColor").value
        : ""
    );
    formData.append(
      "LogoAlignment",
      this.form.get("logoAlignment").value != null
        ? this.form.get("logoAlignment").value
        : ""
    );
    formData.append(
      "DefaultSetUp",
      this.form.get("defaultSetUp").value != null
        ? this.form.get("defaultSetUp").value
        : ""
    );
    formData.append("DefaultSignUpPage", "false");

    this.logoImage
      .filter((x) => !x.isExisting)
      .forEach((image) => {
        formData.append("LogoImage", image.file, image.file.name);
      });

    this.bgImage
      .filter((x) => !x.isExisting)
      .forEach((image) => {
        formData.append("BgImage", image.file, image.file.name);
      });

    formData.append(
        "DisplayFund",
        this.form.get("displayFund").value != null
          ? this.form.get("displayFund").value
          : ""
      );

    return formData;
  }

  checkAnyDataChange(event?: any) {
    this.displayName = event?.value;
  }

  disableButtons(){
    if(this.logoImage.length>0){
      this.logoDisabled = true;
    }
    if(this.bgImage.length>0){
      this.bgDisabled = true;
    }
  }

  uploadFile(name:string){
    if(name == 'logo'){
      this.isLogoUploaded = true;
    }
    if(name == 'bg'){
      this.isBgUploaded = true;
    }
    this._toasterService.success("Image Uploaded successfully", "", {
      positionClass: "toast-center-center",
    });

  }
  

  onBrowseImageChange(files: FileList, type: string) {
    const largeFiles: File[] = [];
    const invalidExtensionFiles: string[] = [];

    Array.from(files).forEach((file) => {
      const fileExtension = this.getFileExtension(file.name);
      const fileSizeMB = file.size / (BYTE_SIZE * BYTE_SIZE);

      if (!ImageExtensions.includes(fileExtension)) {
        invalidExtensionFiles.push(fileExtension);
      } else if (fileSizeMB > MAX_FILE_SIZE_MB) {
        largeFiles.push(file);
      } else {
        this.readFile(file, type);
      }
    });

    this.handleFileErrors(invalidExtensionFiles, largeFiles);
    this.checkEnableSaveButton();
    this.disableButtons();
  }

  getFileExtension(fileName: string) {
    return fileName !== null
      ? FILE_EXTENSION_PREFIX +
          fileName.split(FILE_NAME_SPLIT_CHAR).pop().toLowerCase()
      : "";
  }

  readFile(file: File, type: string) {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      if (type == "logo"){
        this.logoImage.push({
          file,
          url: e.target.result,
          isExisting: false,
          id: EMPTY_FILE_ID,
        });
        this.isSaveEnabled = true;
        this.lpReportConfigService.isSaveEnabled = true;
        this.disableButtons();
      }

      if (type == "bg"){
        this.bgImage.push({
          file,
          url: e.target.result,
          isExisting: false,
          id: EMPTY_FILE_ID,
        });
        this.isSaveEnabled = true;
        this.lpReportConfigService.isSaveEnabled = true;
        this.disableButtons();
      }
    };
    reader.readAsDataURL(file);
  }

  handleFileErrors(invalidExtensionFiles: string[], largeFiles: File[]) {
    if (invalidExtensionFiles !== null || largeFiles !== null) {
      if (invalidExtensionFiles.length > 0 && largeFiles.length === 0) {
        const invalidExtensionFilesMessage =
          invalidExtensionFiles.length > 1
            ? INCORRECT_FILE_FORMATS
            : INCORRECT_FILE_FORMAT;
        this.showErrorMessage(
          `${invalidExtensionFiles.join(
            ", "
          )} ${invalidExtensionFilesMessage}. ${ALLOWED_FILE_FORMATS_MESSAGE}`
        );
      } else if (largeFiles.length > 0 && invalidExtensionFiles.length === 0) {
        const largerFileErrorMessage =
          largeFiles.length > 1 ? "images" : "image";
        this.showErrorMessage(
          `${largeFiles.length} ${largerFileErrorMessage} could not be uploaded. ${UPLOAD_IMAGES_UP_TO} ${MAX_FILE_SIZE_MB} ${MB_ONLY}`
        );
      } else if (largeFiles.length > 0 && invalidExtensionFiles.length > 0) {
        this.showErrorMessage(
          `${INCORRECT_FILE_FORMAT_AND_SIZE} ${UPLOAD_IMAGES_UP_TO} ${MAX_FILE_SIZE_MB} ${MB_ONLY}`
        );
      }
    }
  }

  showErrorMessage(message: string) {
    this._toasterService.error(message, "", {
      positionClass: TOASTER_POSITION,
    });
  }
  /**
   * Removes an image from the sdgImages array.
   * @param index The index of the image to remove.
   */
  removeImage(index: number, type: string) {
    if (type == "logo") {
      const image = this.logoImage[index];
      const isExisting = image.isExisting;
      if (isExisting) {
        this.imagesTobeDeleted.push(image.id);
      }
      this.logoImage.splice(index, 1);
      this.isLogoUploaded = false;
      this.logoDisabled = false;
    }
    if (type == "bg") {
      const image = this.bgImage[index];
      const isExisting = image.isExisting;
      if (isExisting) {
        this.imagesTobeDeleted.push(image.id);
      }
      this.bgImage.splice(index, 1);
      this.bgDisabled = false;
      this.logoDisabled = false;
    }
  }
  onReset(){
    this.form.reset();
    this.initForm();
    this.loadTemplateData();
  }
}
