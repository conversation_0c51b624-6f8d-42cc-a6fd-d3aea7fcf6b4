
@import '../../../../variables';

.company-logo {
  width: 24px;
  height: 24px;
  object-fit: contain;
  margin-right: 8px;
  vertical-align: middle;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
}

.dashboard-tab-bar{
  background-color: $Neutral-Gray-02 !important;
  border-radius: $Radius-4;
}
.tab-container{
  border: $border-color-light;
  border-radius: 6px;
}
.dashboard-configuration-body{
  display: flex;
  flex-direction: row;
}
.dashboard-table-section {
  width: calc(100% - 49px);
  height: auto;
}
.add-column-section {
  width: 49px;
  height: 75vh;
  border: $border-color-light;
  border-left: none;
  border-radius: 0 $Radius-4 $Radius-4 0;
  background: $Neutral-Gray-00;
  filter: drop-shadow(0px 0px 8px $Neutral-Gray-03);
  & .add-column-header {
    height: 44.5px;
    border-bottom: 1px solid $Neutral-Gray-10;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

