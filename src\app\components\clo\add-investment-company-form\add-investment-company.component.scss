@import "../../../../variables.scss";

.step {
  cursor: pointer;
}
button {
  background: var(--Color-Primary-Primary---78, #4061C7);
  color: white;
  border: none;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
}
.step-sidebar {
  border-right: 1px solid #E6E6E6;

}


.btn-save {
 height: 32px !important;
  padding: 6px 16px !important;
}
.btn-save:disabled {
  opacity: 0.5;
}
.heading {
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  text-align: left;
  padding: 24px;
}

.form-container {
  border: 1px solid #E6E6E6; 
  border-radius: 8px; 
  height: 580px;
}
.footer {
  display: flex;
    justify-content: space-between;
    padding: 10px 0;
    background-color: #fff;
    position: absolute;
    bottom: -37px;
    width :-webkit-fill-available;
}
button[kendoButton] {
  margin-right: 10px;
}
.active-step {
  color: #4061C7;
}

.step-container {
  display: flex;
  justify-content: flex-start; 
  align-items: center;
}
.btn-reset-container {
  display: flex;
  justify-content: flex-start; /* Align reset button to the left */
}

.button-content {
  display: flex;
  justify-content: flex-end; /* Align save and cancel buttons to the right */
}
.step-title {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.text-content {
  display: flex;
  flex-direction: column;
}

.step-image {
  width: 36px;
  height: 36px; 
  margin: 5px;
}
.btn-warning{
  background: #FFFFFF 0% 0% no-repeat padding-box !important;
  border: 1px solid #4061C7;
  border-radius: 4px;
  opacity: 1;
  color: var(--Color-Primary-Primary---78, #4061C7);

}
.custom-margintop{
  margin-top: 188px;
}
.custom-Fixedheight{
   height: 540px;
   padding-top:2.5rem;
  
}
.custom-Fixedheight-Step-3{
  height: 100%;
}

.char-count {
  text-align: right;
  font-size: 0.875rem;
  color: #6c757d;
}
.step-subtitle {
  font-size: 16px;
  font-weight: 500;
}
.texts-align{
  margin-bottom: 200px;
}
.bordered-image-container {
  position: relative;
  display: inline-block;
  width: 48px; /* Adjust width as needed */
  height: 48px; /* Adjust height as needed */
  border: 3px solid var(--Color-Primary-Primary---78, #4061C7);
  border-radius: 50%; /* Make the border circular */
  opacity: 15%;
  padding: 3px;
  box-sizing: border-box; /* Include padding and border in the element's total width and height */
}
.bordered-image-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%; /* Make the border circular */
  background: var(--Color-Primary-Primary---78, #4061C7);
  opacity: 0.15; /* Apply opacity to the background */
  z-index: 0; /* Ensure the pseudo-element is behind the image */
}

.top-border-line {
  position: absolute;
  width: 1px;
  left: 14%;
  transform: translateX(-50%);
  border: 1px solid var(--Border-border-disabled-accent, #93B0ED);
  top: 50px;
  height: calc(50% - 192px);
}
.lower-border-line,.bottom-border-line {
  position: absolute;
  width: 1px;
  left: 14%;
  border: 1px solid var(--Border-border-disabled-accent, #93B0ED);
}
.bottom-border-line {
  top: 170px;
  height: calc(50% - 74px);
}
.lower-border-line {
  
  height: calc(50% - 192px);
}
.border-container {
  position: relative;
  padding: 10px 0;
}
.disabled-save-btn {
  background: var(--Color-Primary-Primary---60, #93B0ED) !important;
  color: white !important; 
}
.border-container::before,
.border-container::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 2px; 
  background-color: #4061C7; 
}

.border-container::before {
  top: 0;
}

.border-container::after {
  bottom: 0;
}
.vertical-line {
  width: 2px;
  height: 100%; 
  background-color: #4061C7; 
  
}
.disabled-btn {
  border-radius: 4px;
  color: #93B0ED !important; 
  border-color: #93B0ED !important; 
  background-color: transparent !important;
}


.stepper-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 40px;
  padding-left: 5px;
  
}
.stepper {
  display: flex;
  align-items: center;
  flex: 1;
  cursor: pointer;
  
}
.stepper-line {
  border: 1px solid var(--Border-border-disabled-accent, #93B0ED);
 flex-grow: 1;
}
.imageIcon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;
  text-align: center;
}
.image {
  margin: 10px 0;
}
.custom-marginbottom{
  margin-bottom:4px;
}

.step-title{
  font-size: 12px;;
}

.review-page-form-container {
  border: $review-page-form-container-border solid #E6E6E6; 
  border-radius: $review-page-form-container-border-radius; 
  height: 100%;
  margin: 0px 1rem 1rem 1rem;
}

.review-page-custom-Fixedheight {
  max-height: $review-page-max-height;
  padding: $review-page-custom-fixedheight-padding;
  overflow-y: auto;
}

.popup-text {  
  font-weight: 400;
  line-height: $popup-text-line-height;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.review-page-custom-footer {
  display: flex;
  justify-content: space-between;  
  background-color: #fff;    
  width :-webkit-fill-available;
}

.label-error {
  color: #DE3139;
}

.main-Container-height{
  height: 100%;
}

::-webkit-scrollbar {
  width: $review-page-scroll-width !important;
  color: $review-page-scroll-color !important;  
}
