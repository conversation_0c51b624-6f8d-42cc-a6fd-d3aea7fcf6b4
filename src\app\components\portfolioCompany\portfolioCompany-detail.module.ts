import { MaterialModule } from './../../custom-modules/material.module';
import { PrimeNgModule } from './../../custom-modules/prime-ng.module';
import { PortfolioCompanyDetailComponent } from './portfolioCompany-detail.component';
import { SharedComponentModule } from './../../custom-modules/shared-component.module';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { CommonModule, DatePipe } from '@angular/common';
import { RouterModule } from '@angular/router';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpServiceInterceptor } from 'src/app/interceptors/http-service-interceptor';
import { PortfolioCompanyImpactKPIComponent } from './portfolioCompany-ImpactKPI.component';
import { PortfolioCompanyKPIComponent } from './portfolioCompany-CompanyKPI.component';
import { ImpactuploadComponent } from '../impactupload/impactupload.component';
import { InvestmentKpiGraphComponent } from './investment-kpi-graph/investment-kpi-graph.component';
import { CompanyKpiGraphComponent } from './company-kpi-graph/company-kpi-graph.component';
import { CheckUserPermissionDirective } from 'src/app/directives/CheckUserPermission.directive';
import { MasterKpiService } from 'src/app/services/master-kpi.service';
import { CurrencyService } from 'src/app/services/currency.service';
import { HtmltopdfComponent} from '../htmltopdf/htmltopdf.component';
import { AngularEditorModule } from '@kolkov/angular-editor';
import { SharedPipeModule } from './../../custom-modules/shared-pipe.module';
import { AngularResizeEventModule } from 'angular-resize-event';
import { FootNoteComponent } from '../foot-note/foot-note.component';
import { QuillModule } from 'ngx-quill';
import { FootNoteService } from 'src/app/services/foot-note.service';
import {FinancialsBetaComponent} from 'src/app/components/portfolioCompany/financials-beta/financials-beta.component';
import { ProfitLossBetaComponent } from './financials-beta/profit-loss-beta/profit-loss-beta.component';
import { BalanceSheetBetaComponent } from './financials-beta/balance-sheet-beta/balance-sheet-beta.component';
import { CashflowBetaComponent } from './financials-beta/cashflow-beta/cashflow-beta.component';
import{ProfitLossService} from '../../services/profit-loss.service';
import{BalanceSheetService} from '../../services/balance-sheet.service';
import { OtherKpiComponent } from './master-kpi-beta/other-kpi.component';
import{CashflowBetaService} from '../../services/cashflow-beta.service';
import { MasterKpiBetaComponent } from './master-kpi-beta/master-kpi-beta.component';
import { SDGImagesUploadComponent } from './sdg-images-upload/sdg-images-upload.component';
import { OperationalKpiBetaComponent } from './operational-kpi-beta/operational-kpi-beta.component';
import { MasterKpiGraphBetaComponent } from './master-kpi-graph-beta/master-kpi-graph-beta.component';
import { InvestmentKpiBetaComponent } from './investment-kpi-beta/investment-kpi-beta.component';
import { CapTableComponent } from './cap-table/cap-table.component';
import { CapTableService } from 'src/app/services/cap-table.service';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { IconsModule,SVGIconModule } from '@progress/kendo-angular-icons';
import { SortDataDirective } from 'src/app/directives/kendo-sort.directive';
import { ValuationSummaryComponent } from './valuation-summary/valuation-summary.component';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        PrimeNgModule,
        MaterialModule,
        HttpClientModule,
        SVGIconModule,
        IconsModule,
        SharedComponentModule,
        SharedDirectiveModule,
        KendoModule,
        AngularEditorModule,
        SharedPipeModule,
        AngularResizeEventModule,
        QuillModule.forRoot()
    ], 
    providers: [
        DatePipe,
        MasterKpiService,
        CurrencyService,
        CashflowBetaService,
        BalanceSheetService,
        ProfitLossService,
        FootNoteService,
        CapTableService,
        {
        provide: HTTP_INTERCEPTORS,
        useClass: HttpServiceInterceptor,
        multi: true,
    }],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    declarations: [
        PortfolioCompanyDetailComponent, 
        PortfolioCompanyImpactKPIComponent,
        PortfolioCompanyKPIComponent,
        ImpactuploadComponent,
        InvestmentKpiGraphComponent,
        CompanyKpiGraphComponent,
        CheckUserPermissionDirective,  
        SortDataDirective, 
        HtmltopdfComponent,
        FootNoteComponent,
        FinancialsBetaComponent,
        ProfitLossBetaComponent,
        BalanceSheetBetaComponent,
        CashflowBetaComponent,
        MasterKpiBetaComponent,
        SDGImagesUploadComponent,
        OperationalKpiBetaComponent,
        MasterKpiGraphBetaComponent,
        InvestmentKpiBetaComponent,
        CapTableComponent,
        OtherKpiComponent,
        ValuationSummaryComponent
    ]
    
})
export class PCDetailModule { }