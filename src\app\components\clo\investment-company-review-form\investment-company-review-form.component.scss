@import "../../../../variables.scss";

.label-field {
    color:  #666666;
    //styleName: Common/Body-R;
    font-family: Helvetica Neue;
    font-size: 14px !important;
    font-weight: 400 !important;
    line-height: 20px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }
  
  .input-text {
    color: #000;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }
  
  .card-body .form-control {
    border: 0px solid #ced4da !important;
    border-radius: 0rem !important;
    border-bottom: $company-content-border-bottom !important;
    font-size: inherit !important;
    padding: 0 !important;
  }
  
  .header-field {
    //width: 196px;
    height: 28px;
    gap: 0px;
    opacity: 0px;
    //styleName: Common/Heading1-M;
    //font-family: Helvetica Neue;
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #4061C7;
  }
  
  .main-container{
    padding-top: 0px !important;
  }
  
  .no-pointer {
    pointer-events: none;
  }

  .edit-Button{
    cursor:pointer;
  }

  .investment-summary-content{
    border-bottom: $company-content-border-bottom !important;   
    width: 100%; 
  }