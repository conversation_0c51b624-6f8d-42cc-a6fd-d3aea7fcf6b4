import { TestBed, ComponentFixture } from "@angular/core/testing";
import { OauthLoginComponent } from "./oauthlogin.component";
import { OidcAuthService } from "src/app/services/oidc-auth.service";
import { of } from "rxjs";
import { AccountService } from "src/app/services/account.service";
import { HttpClientTestingModule } from "@angular/common/http/testing";
import { ToastrModule, ToastrService } from "ngx-toastr";

class MockOidcAuthService {
  signinRedirectCallback = jasmine
    .createSpy("signinRedirectCallback")
    .and.returnValue(of(true));
}
class MockAccountService {}
describe("OauthLoginComponent", () => {
  let component: OauthLoginComponent;
  let fixture: ComponentFixture<OauthLoginComponent>;
  let oidcAuthService: OidcAuthService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [OauthLoginComponent],
      imports: [HttpClientTestingModule, ToastrModule.forRoot()],
      providers: [
        { provide: OidcAuthService, useClass: MockOidcAuthService },
        { provide: AccountService, useClass: MockAccountService },
        { provide: "BASE_URL", useValue: "http://localhost" },
        ToastrService,
        { provide: "ToastConfig", useValue: {} },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(OauthLoginComponent);
    component = fixture.componentInstance;
    oidcAuthService = TestBed.inject(OidcAuthService);
    fixture.detectChanges();
  });

  it("should create", () => {
    expect(component).toBeTruthy();
  });
});
