import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LpReportConfigListComponent } from './lp-report-config-list.component';
import { RouterModule } from '@angular/router';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpServiceInterceptor } from 'src/app/interceptors/http-service-interceptor';
import { LpReportConfigListService } from 'src/app/services/lp-report-config-list.service';
import { ConfigPageTemplateComponent } from './config-page-template/config-page-template.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {PopupModule } from'@progress/kendo-angular-popup';
import { CanDeactivateGuardGrowth } from 'src/app/unsaved-changes/can-deactivate/can-deactivate-growth.guard';
import { ChangeConfirmGrowthComponent } from '../growth-report/change-growth-confirm/change-confirm-growth.component';
@NgModule({
  declarations: [
    LpReportConfigListComponent,
    ConfigPageTemplateComponent
  ],
  imports: [
    CommonModule,
    HttpClientModule,
    SharedComponentModule,
    KendoModule,
    FormsModule,
    ReactiveFormsModule,
    PopupModule,
    RouterModule.forChild([
      { path: '', component: LpReportConfigListComponent, canDeactivate: [CanDeactivateGuardGrowth] }
    ])
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [LpReportConfigListService, {
    provide: HTTP_INTERCEPTORS,
    useClass: HttpServiceInterceptor,
    multi: true,
  } , CanDeactivateGuardGrowth, ChangeConfirmGrowthComponent]
})
export class LpReportConfigListModule { }
