<div class="container-fluid">
    <div class="row">
        <!-- Left side: Email Groups -->
        <div class="col-12 col-md-8 mb-3 mb-md-0 pr-md-3 pl-0">
            <div class="card border email-list-container p-3">
            <ng-container *ngFor="let dataItem of emailGroups">
                <div class="email-group-container mb-3 px-3 cursor-pointer" (click)="onGroupClick(dataItem.groupId)" [ngClass]="{'active': expandedGroupId === dataItem.groupId}">
                    <div class="email-group-item d-flex align-items-center justify-content-between border-bottom py-2">
                        <span class="Heading2-M .vColor">{{dataItem.groupName}}</span>
                        <div class="d-flex align-items-center">
                            <button class="btn btn-icon p-1" (click)="editEmailGroup(dataItem)">
                                <img src="assets/dist/images/FiEditBlack.svg" alt="Edit">
                            </button>
                            <button class="btn btn-icon px-2 ml-3" (click)="deleteEmailGroup(dataItem)">
                                <img src="assets/dist/images/delete-company.svg" alt="Delete">
                            </button>
                        </div>
                    </div>
                    <div class="d-flex py-2 mb-1">
                        <div class="d-grid flex-item-27">
                            <span class="Caption-R dataitem-label">Created by</span>
                            <span class="Body-M">{{dataItem.uploadedBy}}</span>
                        </div>
                        <div class="d-grid flex-item-27">
                            <span class="Caption-R dataitem-label">Created on</span>
                            <span class="Body-M">{{dataItem.createdOn | date:'dd/MMM/yy'}}</span>
                        </div>
                        <div class="d-grid flex-item-45">
                            <span class="Caption-R dataitem-label">Portfolio Company</span>
                            <div class="d-flex">
                                <div *ngFor="let company of dataItem.companyNames.slice(0, showPortfolioCompanies)">
                                    <span title="{{company}}" class="company-pill px-2 py-1 mr-2">
                                        {{company.length > companynameMaxLength ? company.substring(0, companynameMaxLength) + '...' : company}}
                                    </span>
                                </div>
                                <div *ngIf="dataItem.companyNames.length > showPortfolioCompanies">
                                    <span class="company-pill px-2 py-1" kendoPopoverAnchor [popover]="myPopover" themeColor="primary">
                                        +{{ dataItem.companyNames.length - showPortfolioCompanies }}
                                    </span>
                                    <kendo-popover #myPopover [width]="250" position="left">
                                        <ng-template kendoPopoverBodyTemplate>
                                            <div *ngFor="let company of dataItem.companyNames.slice(showPortfolioCompanies)" class="doc-tooltip-item">
                                                {{ company }}
                                            </div>
                                        </ng-template>
                                    </kendo-popover>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-container>

            <div class="text-center py-5 mt-5" *ngIf="emailGroups.length === 0">
                <img src="assets/dist/images/Illustrations.svg" alt="No company" class="mb-3">
                <p class="mb-0">No Email Groups</p>
            </div>
            
            </div>
        </div>

        <!-- Right side: Email List -->
        <div class="col-12 col-md-4 pl-0 pr-0">
            <div class="card border email-list-container">                
                <div class="email-list-header border-bottom" *ngIf="selectedGroupEmails.length > 0">
                    <div class="p-3 d-flex align-items-center">                       
                        <input type="checkbox"
                            class="k-checkbox k-checkbox-md k-rounded-md mr-2 chkbx-border"
                            kendoCheckBox
                            [checked]="isAllSelected"
                            (click)="toggleSelectAll($event)" />
                        <span class="Body-M mr-2">Common Email Listing</span>
                        <span class="Body-B">({{selectedGroupEmails.length}})</span>
                    </div>
                </div>

                <div class="email-list-body">
                    <kendo-grid [hideHeader]="true" [data]="selectedGroupEmails" [gridLines]="'horizontal'"
                        scrollable="none">
                        <kendo-grid-column field="name" title="Name" [width]="200">
                            <ng-template kendoGridCellTemplate let-dataItem>
                                <div class="d-flex align-items-center">                                    <input type="checkbox"
                                        class="k-checkbox k-checkbox-md k-rounded-md mr-2 chkbx-border"
                                        kendoCheckBox
                                        (click)="toggleEmailItem(dataItem)"
                                        [(ngModel)]="dataItem.isSelected" />
                                    <span class="Body-R">{{dataItem.name}}</span>
                                </div>
                            </ng-template>
                        </kendo-grid-column>

                        <kendo-grid-column field="email" title="Email" [width]="200">
                            <ng-template kendoGridCellTemplate let-dataItem>
                                <span class="Body-I text-muted">{{dataItem.email}}</span>
                            </ng-template>
                        </kendo-grid-column>
                    </kendo-grid>
                </div>

                 <!-- Floating Selection Popup -->
        
            </div>
        </div>
    </div>

    <div *ngIf="showDeletePopup" class="selection-popup">
        <div class="selection-content">
            <div class="selected-count Body-M">{{ getSelectedContacts().length }} {{ getSelectedContacts().length === 1 ? 'contact' : 'contacts' }} selected</div>
            <div class="delete-selected-btn Body-R pr-4" [disabled]="isDeleteDisabled" (click)="handleDeleteSelectedClick()" id="delete-selected-btn">
                Delete
            </div>
            <div class="close-selection-btn" (click)="closeSelectionPopup()" id="close-selection-btn">
                <span><img src="assets/dist/images/close-icon.svg" alt="Close" /></span>
            </div>
        </div>
    </div>    
    <div *ngIf="showEmailGroupDeletePopup" id="confirm-modal">
      <confirm-modal
      customwidth="500px"
      isCustomFooterClass="true"
      primaryButtonIsDanger="true"
      primaryButtonName="Yes, Delete"
      secondaryButtonName="No, keep it" 
      (primaryButtonEvent)="triggerDeleteAction()"
      modalTitle="{{ modalTitle }}"
      (secondaryButtonEvent)="cancelDelete()"
      isDeleteConfirmModal="true"
      isCloseEnable="true"
      (closeIconClick)="cancelDelete()"
      >
        <div class="container px-2">
        <div class="d-flex">
          <div class="mr-13">
            <img src="assets/dist/images/exclamation-circle-delete.svg" alt="Exclamation Circle" />
          </div>
          <div>
            <div class="Heading2-M mb-1">Are You Sure ?</div>
            <div class="Caption-R content-secondary">{{confirmationDialogContent}}</div>
          </div>
        </div>
      </div>
      </confirm-modal>
    </div>
</div>