import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EmailConfigurationPageComponent } from './email-configuration-page.component';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { ActivatedRoute } from '@angular/router';
import { BehaviorSubject, of } from 'rxjs';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';

describe('EmailConfigurationPageComponent', () => {
  let component: EmailConfigurationPageComponent;
  let fixture: ComponentFixture<EmailConfigurationPageComponent>;
  let repositoryConfigServiceSpy: jasmine.SpyObj<RepositoryConfigService>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    // Mock ActivatedRoute with BehaviorSubject for queryParams
    mockActivatedRoute = {
      queryParams: new BehaviorSubject({})
    };

    const repoServiceSpy = jasmine.createSpyObj('RepositoryConfigService', 
      ['getPortfolioCompanies'], 
      { resetInProgress$: of(false) }
    );

    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      declarations: [EmailConfigurationPageComponent],
      providers: [
        { provide: RepositoryConfigService, useValue: repoServiceSpy },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
    }).compileComponents();

    repositoryConfigServiceSpy = TestBed.inject(RepositoryConfigService) as jasmine.SpyObj<RepositoryConfigService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(EmailConfigurationPageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct default tab configuration', () => {
    // Initial tab state verification
    expect(component.emailConfigTabs.length).toBe(2);
    expect(component.emailConfigTabs[0].name).toBe('User Info by Company');
    expect(component.emailConfigTabs[0].active).toBeTrue();
    expect(component.emailConfigTabs[1].name).toBe('Email Groups');
    expect(component.emailConfigTabs[1].active).toBeFalse();
    expect(component.activeTab).toBe('User Info by Company');
  });

  it('should update active tab when onTabClick is called', () => {
    // Create mock tab object
    const emailGroupTab: ITab = { 
      name: 'Email Groups', 
      active: false, 
      hidden: false 
    };
    
    // Call tab click handler
    component.onTabClick(emailGroupTab);
    
    // Verify tab state changes
    expect(component.activeTab).toBe('Email Groups');
  });

  it('should update selectedCompanies when onCompanySelected is called', () => {
    // Mock selected companies data
    const mockCompanies = [
      { name: 'Company A', companyId: 1, selected: true },
      { name: 'Company B', companyId: 2, selected: true }
    ];
    
    // Call company selection handler
    component.onCompanySelected(mockCompanies);
    
    // Verify companies were stored
    expect(component.selectedCompanies).toEqual(mockCompanies);
    expect(component.selectedCompanies.length).toBe(2);
  });

  it('should show User Info content when "User Info by Company" tab is active', () => {
    // Set the active tab
    component.activeTab = 'User Info by Company';
    fixture.detectChanges();
    
    // Simplified test verification
    expect(component.activeTab).toBe('User Info by Company');
  });

  it('should show Email Groups content when "Email Groups" tab is active', () => {
    // Set the active tab
    component.activeTab = 'Email Groups';
    fixture.detectChanges();
    
    // Simplified test verification
    expect(component.activeTab).toBe('Email Groups');
  });

  it('should activate the tab specified in query params on init', () => {
    mockActivatedRoute.queryParams.next({ activeTab: 'Email Groups' });
    component.ngOnInit();
    expect(component.activeTab).toBe('Email Groups');
    expect(component.emailConfigTabs[1].active).toBeTrue();
    expect(component.emailConfigTabs[0].active).toBeFalse();
  });

  it('should not change activeTab if query param is missing', () => {
    component.activeTab = 'User Info by Company';
    mockActivatedRoute.queryParams.next({});
    component.ngOnInit();
    expect(component.activeTab).toBe('User Info by Company');
  });

  it('should not activate any tab if query param is invalid', () => {
    mockActivatedRoute.queryParams.next({ activeTab: 'Nonexistent Tab' });
    component.ngOnInit();
    // Should not throw, and activeTab should be set to the invalid value
    expect(component.activeTab).toBe('Nonexistent Tab');
    // But no tab in emailConfigTabs should be active
    expect(component.emailConfigTabs.every(tab => !tab.active)).toBeTrue();
  });

  it('should handle onTabClick with a tab not in emailConfigTabs', () => {
    const fakeTab: ITab = { name: 'Fake Tab', active: false, hidden: false };
    component.onTabClick(fakeTab);
    expect(component.activeTab).toBe('Fake Tab');
    // No tab in emailConfigTabs should be active
    expect(component.emailConfigTabs.every(tab => !tab.active)).toBeTrue();
  });

  it('should handle onCompanySelected with null/undefined/empty', () => {
    component.onCompanySelected(null as any);
    expect(component.selectedCompanies).toBeNull();

    component.onCompanySelected(undefined as any);
    expect(component.selectedCompanies).toBeUndefined();

    component.onCompanySelected([]);
    expect(component.selectedCompanies).toEqual([]);
  });

  it('should not throw if emailConfigTabs is empty and onTabClick is called', () => {
    component.emailConfigTabs = [];
    expect(() => component.onTabClick({ name: 'Any', active: false, hidden: false })).not.toThrow();
  });

  it('should not throw if emailConfigTabs is empty and ngOnInit is called', () => {
    component.emailConfigTabs = [];
    mockActivatedRoute.queryParams.next({ activeTab: 'User Info by Company' });
    expect(() => component.ngOnInit()).not.toThrow();
  });

  it('should keep the same tab active if onTabClick is called with the current active tab', () => {
    const currentTab = component.emailConfigTabs[0];
    component.onTabClick(currentTab);
    expect(component.activeTab).toBe(currentTab.name);
    expect(component.emailConfigTabs[0].active).toBeTrue();
  });
});