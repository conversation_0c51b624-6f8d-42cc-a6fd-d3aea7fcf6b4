<div class="border-grey-tab esg-tbl">
    <div class="row mr-0 ml-0">
        <div class=" col-lg-12 col-md-12 col-sm-12 pl-0 pr-0 border-bottom">
            <div class="allvalues"></div>
            <div class="float-right tr-all-values-l-right">
                <div class="d-inline-block search input-with-icon">
                    <span class="fa fa-search fasearchicon"></span>
                    <input #gb pInputText type="text" class="search-text-company search-text-fxrates TextTruncate"
                        placeholder="Search"
                        [appApplyFilter]="{ data: tableResultClone, columns: Columns,IsFreezeColumn:true,freezeColumns:'kpiName'}"
                        (filtered)="esgPeriodData = $event" [(ngModel)]="globalFilter" id="searchInput">
                </div>
                <div class="d-inline-block pr-1 headerfontsize">
                    <div class="d-inline-block table-pref" title="Logs">Logs</div>
                    <div class="d-inline-block pr-2 pl-1" id="toggleSwitch" title="Switch to view cell based audit trails">
                        <kendo-switch size="small" [(ngModel)]="isToggleChecked" id="toggleSwitch" [onLabel]="' '" [offLabel]="' '"> </kendo-switch>
                    </div>
                </div>
                <div class="d-inline border-right divider-position"></div>
                <div class="d-inline-block cloud_download">
                    <div id="downlaodDeals" class="d-inline-block pr-2">
                        <img class="p-action-padding download-excel cursor-filter" style="margin-top: -2px;" title="Export ESG (Excel file)"
                            id="dropdownMenuButton" #dropdownMenuButton [matMenuTriggerFor]="exportMenu" alt=""
                            src="assets/dist/images/Cloud-download.svg" #iExportMenuTrigger="matMenuTrigger" />
                    </div>
                    <popover predecessorId="downlaodDeals" [show]="isExportLoading" marginLeft="-260px"
                        marginTop="35px">
                    </popover>
                    <div class="fund-splitButton d-inline-block">
                        <a class="loading-input-controls2" *ngIf="isExportLoading">
                            <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                        </a>
                        <mat-menu #exportMenu="matMenu" class="custom-esg-pop">
                            <div class="download-dropdown-content TextTruncate" title="Current KPI"> <a
                                    (click)="downloadCurrentKpiDataExcel()" class="export-option" id="downloadCurrentKpi">Current KPI</a>
                            </div>
                            <div class="download-dropdown-content TextTruncate" title="All KPIs"> <a
                                    (click)="downloadAllKpiDataExcel()" class="export-option" id="downloadAllKpi">All KPIs</a>
                            </div>
                        </mat-menu>
                    </div>
                    <div class="d-inline textsplit">
                    </div>
                    <div class="d-inline-block pl-2 " style="padding-right: 12px;"><img id="dropdownMenuButton" style="margin-top: -2px;" [matMenuTriggerFor]="menu"
                            src="assets/dist/images/ConfigurationWhite.svg" class="cursor-filter" alt=""
                            #esgMenuTrigger="matMenuTrigger" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mr-0 ml-0" *ngIf="!ifNoDataAvailable">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xl-12 pl-0 pr-0">
            <kendo-grid id="esg-kpi-data-grid"
                class="esg-f-table k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cab-table-grid grid-row-no-padding"
                [kendoGridBinding]="esgPeriodData" scrollable="virtual" [rowHeight]="35" [resizable]="true">
                <ng-container *ngIf="esgPeriodData.length > 0">
                    <kendo-grid-column [sticky]="true" [minResizableWidth]="200" [maxResizableWidth]="800" [width]="400"
                        *ngFor="let col of frozenCols;" [field]="col.field">
                        <ng-template kendoGridHeaderTemplate>
                            <div class="header-icon-wrapper wd-100 header-left-padding">
                                <span class="TextTruncate S-M">
                                    {{col.header}}
                                </span>
                            </div>
                        </ng-template>
                        <ng-template kendoGridCellTemplate let-rowData>
                            <div #cellText class="cell-padding" [ngClass]="rowData.isExpanded ? 'auto-adjust':'TextTruncate'">               
                                <span  class="pl-2 pr-2" *ngIf="isTruncatedAny">
                                    <a *ngIf="rowData?.isTruncated"  (click)="expandText(rowData)" id="expandKpi">
                                        <img  alt="" src="assets/dist/images/{{rowData.isExpanded ? 'chevron-down-i.svg' : 'chevron-left-i.svg'}}"
                                            class="">
                                    </a>
                                </span>
                                <span [ngClass]="!rowData?.isTruncated ? 'pl-3':''" class="esg-icon-container" (click)="openEsgStaticInfoPopUpModel(rowData)">
                                    <a class="esg-custom-arrow "  id="openStaticInfo">
                                        <img _ngcontent-ng-c2007195344="" alt="" src="assets/dist/images/all-upload.svg"
                                            class="process-icon">
                                    </a>
                                </span>
                                <span class="content" [ngClass]="rowData?.isTruncated ?'expand-content-text':'pl-2'">
                                    <span  *ngIf="col.header =='KPI'" title={{rowData[col.field]}}
                                        [ngClass]="[rowData.isExpanded? 'auto-adjust':'TextTruncate', (rowData.isHeader||rowData.isBold) ? 'showToolTip bold-text' :'showToolTip', rowData.isHeader ? 'bold-text pl-0' : rowData['IsBoldKPI'] ? 'bold-text': '',((rowData.ParentId !==0||rowData.ParentId ==0)&&!rowData.isHeader)?'pl-0':'',rowData.isTruncated ? 'pl-3':'']">
                                        <span *ngIf="!rowData.parentKpiId == false">- </span>{{rowData[col.field]}}
                                        <span *ngIf="rowData['kpiInfo'] =='#'">{{'('+rowData['kpiInfo'] +')'}}</span>
                                    </span>
                                </span>
                            </div>
                        </ng-template>
                    </kendo-grid-column>
                </ng-container>
                <ng-container *ngIf="esgPeriodData.length > 0">
                    <kendo-grid-column [minResizableWidth]="200" *ngFor="let col of Columns; index as i"
                        [maxResizableWidth]="800" [width]="200" title="{{col.header}}">
                        <ng-template kendoGridHeaderTemplate>
                            {{col.header}}
                        </ng-template>
                        <ng-template kendoGridCellTemplate let-rowData>
                            <div class="peer-padding" tabindex="{{i}}" (click)="onAuditLog(rowData,col)" (dblclick)="onTableDataEditInit(rowData, col)" id="auditLog">
                               
                                <textarea #textareaEle tabindex="{{i}}" data-column="{{col.field}}"
                                    *ngIf="rowData[col.field + ' editable'] && rowData['kpiInfo'] =='Text' && !rowData.isHeader"
                                    class="openDocInputText fontStyle14 companyText enlargedRow" type="text"
                                    [value]="rowData[col.field]" [(ngModel)]="rowData[col.field]" [readonly]="true"    
                                    (change)="onColumnEditComplete(i, col, rowData, $event)" id="columnEditComplete"></textarea>
                                <div class="TextTruncate" [ngClass]="rowData['kpiInfo']=='Text' ? (!rowData[col.field + ' editable'] ? 'cell-padding' : '') :'cell-padding'"
                                    *ngIf="rowData[col.field]!= undefined && rowData[col.field]!=null && rowData[col.field]!=='' && !rowData.isHeader;else empty_Text">
                                    <span *ngIf="rowData['kpiInfo']=='Text' && !rowData.isHeader && !rowData[col.field + ' editable']" class="TextTruncate"
                                        [title]="rowData[col.field]" [innerHtml]="rowData[col.field]">
                                    </span>
                                    <ng-container
                                        *ngIf="isNumberCheck(rowData[col.field]) && rowData['kpiInfo']!=='Text' && !rowData.isHeader"
                                        [ngSwitch]="rowData['kpiInfo']">
                                        <span [title]="rowData[col.field]" *ngSwitchCase="kpiInfo.Currency"
                                            [innerHtml]="rowData[col.field] | number : decimalPlace  | minusSignToBrackets"></span>
                                        <span [title]="rowData[col.field]" *ngSwitchCase="kpiInfo.Multiple"
                                            [innerHtml]="(rowData[col.field] | number: decimalPlace)+kpiInfo.Multiple"></span>
                                        <span [title]="rowData[col.field]" *ngSwitchCase="kpiInfo.Percentage"
                                            [innerHtml]="(rowData[col.field] | number: decimalPlace | minusSignToBrackets)+kpiInfo.Percentage"></span>
                                        <span [title]="rowData[col.field]" *ngSwitchCase="kpiInfo.Number"
                                            [innerHtml]="(rowData[col.field] | number: decimalPlace | minusSignToBrackets)"></span>
                                    </ng-container>
                                </div>
                                <ng-template #empty_Text class="detail-sec">
                                    <span *ngIf="!rowData.isHeader" title="Not Applicable" class="cell-padding">NA</span>
                                    <span *ngIf="rowData.isHeader" title="Not Applicable" class="cell-padding"></span>
                                </ng-template>
                            </div>

                        </ng-template>
                    </kendo-grid-column>
                </ng-container>
                <ng-template kendoGridNoRecordsTemplate>
                    <app-empty-state class="finacials-beta-empty-state" [imageHeight]="'41vh'" [isGraphImage]="false"
                        *ngIf="tableResult.length == 0"></app-empty-state>
                </ng-template>
            </kendo-grid>
        </div>
        <div *ngIf="infoUpdatePopUpVisible">
            <confirm-modal IsInfoPopup="true" customwidth="400px" modalTitle="Change Values in Selection"
                primaryButtonName="OK" (primaryButtonEvent)="closeInfoPopUp()" id="closeInfoPopUp">
                <div>
                    <div class="invskpi-lh">
                        To edit cell data please select numbers in <b><i>'Absolute'</i></b> under <b><i>Values
                                in</i></b>
                        dropdown
                    </div>
                </div>
            </confirm-modal>
        </div>
        <div *ngIf="showKpiStaticInfoModal" class="esg-static-info">
            <confirm-modal class="custom-confirm-model" customwidth="812px" modalTitle="Details" primaryButtonName="Update" [isCloseEnable]="true" (closeIconClick)="onEsgStaticInfoUpdateCancel($event)"
                [disablePrimaryButton]="disablePrimaryButton" secondaryButtonName="Cancel"
                (primaryButtonEvent)="onEsgStaticInfoUpdate($event)"
                (secondaryButtonEvent)="onEsgStaticInfoUpdateCancel($event)" id="esgStaticInfoUpdateCancel">
                <div class="row ml-0 mr-0">
                    <div class="col-4 pl-0 pr-0 esgKpistaticitemContainer upload-list">
                        <ul class="mb-0">
                            <ng-container *ngFor="let item of esgKpiStaticList; index as i">
                                <li class="esgKpistaticitem" [ngClass]="selectedStaticItem.index == i? 'active' : ''"
                                    tabindex="{{i}}" (click)="onStaticItemClick(item,i)" role="presentation" id="staticItemClick">
                                    <a class="nav-link nav-custom TextTruncate  " title="{{item.aliasName}}"
                                        id="home-tab" data-bs-toggle="tab" type="button" role="tab" aria-controls="home"
                                        aria-selected="true">
                                        {{item.aliasName}}
                                    </a>
                                </li>

                            </ng-container>
                        </ul>
                    </div>
                    <div class="col-8  pl-3 pr-4  pb-3 ">
                        <div class="esg-modal-header pt-2 pb-2 TextTruncate">
                            <span title="{{selectedEsgKpiItem?.kpiName}}" class="img-pad TextTruncate">
                                {{selectedEsgKpiItem?.kpiName}}
                            </span>
                        </div>
                        <div class="esg-modal-content">
                            <textarea [(ngModel)]="selectedStaticItem.value" name="{{selectedStaticItem?.name}}"
                                type="text" class="BusinessDescription-css form-control" rows="10"
                                placeholder="Enter the text here..." autocomplete="off" maxlength="1500"
                                (keyup)="onStaticInfoDescChange()" id="staticInfoDescChange"></textarea>
                        </div>
                    </div>
                </div>
            </confirm-modal>
        </div>
    </div>
</div>
<div *ngIf="confirmUpdatePopUp" class="nep-modal nep-modal-show kpi-add-edit-modal nep-kpi-bg">
    <div class="nep-modal-mask"></div>
    <div class="nep-card nep-card-shadow nep-modal-panel nep-modal-default nep-all-upload-file">
        <app-kpi-cell-edit [dataRow]="newRowData" [tableColumns]="dataColumns"
            [moduleCompanyModel]="uniqueModuleCompany" (cancelButtonEvent)="cancelButtonEvent()" id="cancelButtonEvent"
            (confirmButtonEvent)="onSubmitButtonEvent($event)" id="submitButtonEvent"></app-kpi-cell-edit>
    </div>
</div>
<mat-menu #menu="matMenu" [hasBackdrop]="true" class="fixed-menu">
    <app-kpitablefilters (decimalChange)="onDecimalChange($event)" id="decimalChange" (Kpifilter)="kpiTable_GlobalFilter($event)" id="kpiTableFilter"
        [defaultUnitType]="defaultFinancialValueUnit" [currencyCode]="currencyCode" [tabname]="ESG_TAB_NAME"></app-kpitablefilters>
</mat-menu>
<div class="no-data-div data-table-height" *ngIf="ifNoDataAvailable">
    <app-empty-state class="empty-state pt-3" [isGraphImage]="ifNoDataAvailable"></app-empty-state>
</div>
<app-loader-component *ngIf="isLoading"></app-loader-component>