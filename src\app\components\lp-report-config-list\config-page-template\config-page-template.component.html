<form name="form" [formGroup]="form">
<div class="section-container page-section row mr-0 ml-0 mt-3">
    <div class="row page-section p-2 mr-0 ml-0 section-header w-100">
        <div class="col-10 pr-0 pl-0">
            <div class="row mr-0 ml-0">
                <div class="subpage-name-col col-4 pt-2 pr-0 pl-0 pb-0">
                    <div class="displayName-topCard">
                        {{ form.get('displayName').value }}
                    </div>
                </div>
                <div class="col-5 pt-0 pb-0">
                    <input type="text" class="form-control field-text eachlabel-padding default-txt"
                    formControlName="displayName"
                       required
                        (keyup)="checkAnyDataChange($event)" autocomplete="off" maxlength="100"
                        placeholder="Click here to enter field name" />
                </div>

            </div>
        </div>
    </div>
    <div class="section-content w-100" *ngIf="isVisible">
        <!-- Fund Name Header Section -->
        <div class="page-section p-2 m-3 section border p-3">
            <div class=" pt-1 row w-50">
                <div class="col- 5 pr-0 pl-3 pr-1">Fund Name Header</div>            
                <span class="col-7" tooltipPosition="top" tooltipStyleClass="bg-grey-color">
                    <img class="info-icon" title="Mandatory to select display header name" src="assets/dist/images/InfoGrey.svg" alt="" />
                </span>
            </div>
            <div class="mt-2  w-75">
                <ul class="k-radio-list k-list-horizontal">
                    <li class="k-radio-item">
                        <label class="container mb-0"> Display for as per fund name
                            <input type="radio"  id="fundName1" formControlName="fundNameOption" value="asPerFundName">
                            <span class="checkmark"></span>
                    </label>
                    </li>
        
                    <li class="k-radio-item">
                        <label class="container mb-0"> Display for as per fund name
                            <input type="radio"  id="fundName2" formControlName="fundNameOption" value="sameForAllFunds">
                            <span class="checkmark"></span>
                        </label>
                      <input type="text" formControlName="displayFund" (keyup)="checkAnyDataChange($event)" autocomplete="off"
                      maxlength="100" placeholder="Enter display name..." 
                      class="mt-1 pl-0 field-text" />
                    </li>
                  </ul>
            </div>
            <div class="form-group row mt-3">
                <div class="col-sm pl-0">
                    <kendo-label text="Alignment">   
                    <kendo-combobox [clearButton]="false"
                                    [fillMode]="'flat'" [filterable]="false"
                                    formControlName="fundAlignment" [virtual]="virtual"
                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                    [data]="alignmentValues" [filterable]="true" [value]="fundAlignment" [valuePrimitive]="true"
                                    [value]="fundAlignment"
                                     placeholder="Select Alignment">
                                    <ng-template kendoComboBoxItemTemplate let-fundList>
                                        <div title="{{fundList}}" class="TextTruncate mt-2"> {{fundList}}
                                        </div>
                                    </ng-template>
                    </kendo-combobox>
                    </kendo-label>
                </div>
                <div class="col-sm">
                    <kendo-label text="Font Size">
                        <kendo-combobox [clearButton]="false"
                                    [fillMode]="'flat'" [filterable]="false"
                                    formControlName="fundFontSize" [virtual]="virtual"
                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                    [data]="fontSize" [filterable]="true" [value]="fundFontSize" [valuePrimitive]="true"
                                    [value]="fundFontSize"
                                     placeholder="Select Font Size">
                                    <ng-template kendoComboBoxItemTemplate let-fundList>
                                        <div title="{{fundList}}" class="TextTruncate mt-2"> {{fundList}}
                                        </div>
                                    </ng-template>
                        </kendo-combobox>
                    </kendo-label>
                </div>
                <div class="col-sm">
                    <kendo-label text="Font Type">
                        <kendo-combobox [clearButton]="false"
                                    [fillMode]="'flat'" [filterable]="false"
                                    formControlName="fundFontType" [virtual]="virtual"
                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                    [data]="fontType" [filterable]="true" [value]="fundFontType" [valuePrimitive]="true"
                                    [value]="fundFontType"
                                     placeholder="Select Font Type">
                                    <ng-template kendoComboBoxItemTemplate let-fundList>
                                        <div title="{{fundList}}" class="TextTruncate mt-2"> {{fundList}}
                                        </div>
                                    </ng-template>
                        </kendo-combobox>
                    </kendo-label>
                </div>
                <div class="col-sm">
                    <kendo-label text="Font Colour"></kendo-label><br />
                    <kendo-colorpicker formControlName="fundColor" [format]="'hex'"
                        class="color-picker"></kendo-colorpicker>
                </div>
            </div>
        </div>

        <!-- Report Name Header Section -->
        <div class="page-section p-2 m-3 section border p-3">
            <div class=" pt-1 row w-50 pb-2">
                <div class="col-sm border-right pb-2">Report Name Header</div>
                <div class="col-sm field-text ml-2">Portfolio Update</div>
            </div>
            <div class="form-group row mt-3">
                <div class="col-sm pl-0">
                    <kendo-label text="Alignment"> 
                        <kendo-combobox [clearButton]="false"
                        [fillMode]="'flat'" [filterable]="false"
                        formControlName="reportAlignment" [virtual]="virtual"
                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                        [data]="alignmentValues" [filterable]="true" [value]="reportAlignment" [valuePrimitive]="true"
                        [value]="reportAlignment"
                         placeholder="Select Alignment">
                        <ng-template kendoComboBoxItemTemplate let-fundList>
                            <div title="{{fundList}}" class="TextTruncate mt-2"> {{fundList}}
                            </div>
                        </ng-template>
                        </kendo-combobox>
                </kendo-label>
                </div>
                <div class="col-sm">
                    <kendo-label text="Font Size">
                        <kendo-combobox [clearButton]="false"
                                    [fillMode]="'flat'" [filterable]="false"
                                    formControlName="reportFontSize" [virtual]="virtual"
                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                    [data]="fontSize" [filterable]="true" [value]="reportFontSize" [valuePrimitive]="true"
                                    [value]="reportFontSize"
                                     placeholder="Select Font Size">
                                    <ng-template kendoComboBoxItemTemplate let-fundList>
                                        <div title="{{fundList}}" class="TextTruncate mt-2"> {{fundList}}
                                        </div>
                                    </ng-template>
                        </kendo-combobox>
                    </kendo-label>
                </div>
                <div class="col-sm">
                    <kendo-label text="Font Type">
                        <kendo-combobox [clearButton]="false"
                                    [fillMode]="'flat'" [filterable]="false"
                                    formControlName="reportFontType" [virtual]="virtual"
                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                    [data]="fontType" [filterable]="true" [value]="reportFontType" [valuePrimitive]="true"
                                    [value]="reportFontType"
                                     placeholder="Select Font Type">
                                    <ng-template kendoComboBoxItemTemplate let-fundList>
                                        <div title="{{fundList}}" class="TextTruncate mt-2"> {{fundList}}
                                        </div>
                                    </ng-template>
                        </kendo-combobox>
                    </kendo-label>
                </div>
                <div class="col-sm">
                    <kendo-label text="Font Colour"></kendo-label><br />
                    <kendo-colorpicker formControlName="reportColor" [format]="'hex'"
                        class="color-picker"></kendo-colorpicker>
                </div>
            </div>
        </div>

        <!-- Logo Section -->       
        <div class="page-section p-2 m-3 section border p-3">
            <div class=" pt-1 row w-50">
                <div class="col-sm border-right">Logo</div>
            </div>
            <div class="form-group row mt-3 w-25">   
                <div class="col-sm pl-0">
                    <kendo-label text="Logo Alignment">
                        <kendo-combobox [clearButton]="false"
                        [fillMode]="'flat'" [filterable]="false"
                        formControlName="logoAlignment" [virtual]="virtual"
                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                        [data]="alignmentValues" [filterable]="true" [valuePrimitive]="true"
                        [value]="logoAlignment"
                         placeholder="Select Alignment">
                        <ng-template kendoComboBoxItemTemplate let-fundList>
                            <div title="{{fundList}}" class="TextTruncate mt-2"> {{fundList}}
                            </div>
                        </ng-template>
                        </kendo-combobox>
                    </kendo-label>
                </div>         
            </div>
        </div>

        <!-- Image Upload Section -->
        <div class="page-section m-3 p-3 pt-1 mt-3 mb-5 section border">
            <div class="row pb-2">
                <div class="col-9 pl-0">
                    <div class="pl-3">Image Upload</div>
                </div>
                <div class="col-3 custom-togglep d-flex flex-column align-items-end">
                    <kendo-label text="Use Default Set Up Page">
                        <kendo-switch formControlName="defaultSetUp" [onLabel]="' '" [offLabel]="' '"
                            (valueChange)="onSwitchDefaultSetup($event)"></kendo-switch>
                    </kendo-label>
                </div>
            </div>
            <div class="row">
                <div class="col-3">
                    <label class="image-upload-label">Logo Image (Upto 2 MB)</label>
                    <div class=" fund-name-container dashed-box p-3">
                        <div class="fund-name-drp-1 image-background-icon">
                            <div *ngIf="logoImage?.length > 0 && !defaultSetUp">
                                <div>
                                    <div *ngFor="let image of logoImage; let i = index">
                                        <img src="assets/dist/images/cross-icon.svg" alt="No Content" class="remove-image-btn" id="remove-image-btn" (click)="removeImage(i,'logo')" />
                                        <img [src]="image?.url" [alt]="image?.file?.name" class="uploaded-image no-image" id="uploaded-image" />
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="logoImage?.length == 0 && !defaultSetUp">
                                <div>                                    
                                    <img src="assets/dist/images/NoImage.svg" alt="No Content" class="no-image" />
                                 </div>
                            </div>
                            <div *ngIf="defaultSetUp">
                                <div>                                 
                                    <img src="assets/dist/images/new-acuity-logo.svg" [alt]="image?.file?.name" class="uploaded-image no-image" id="uploaded-image" />
                                </div>
                            </div>
                        </div>
                        <div class="fund-name-drp-3">
                            <button id="logoButton" kendoButton [disabled]="logoDisabled || defaultSetUp || logoImage?.length>0" class="kendo-custom-button Body-R apply-btn mr-2 upload upload-image" fillMode="outline" themeColor="primary">
                            <input type="file" name="file" (change)="onBrowseImageChange($event.target.files,'logo')"
                            multiple accept="image/*" />Browse</button><br />
                            <button [disabled]="isLogoUploaded || defaultSetUp || logoImage?.length"  (click)="uploadFile('logo')" kendoButton class="kendo-custom-button Body-R apply-btn mt-2 upload-image" themeColor="primary">Upload</button>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <label>Background Image (Upto 8 MB)</label>
                    <div class=" fund-name-container dashed-box p-3">
                        <div class="fund-name-drp-1 image-background-icon">
                            <div *ngIf="bgImage?.length > 0 && !defaultSetUp">
                                    <div *ngFor="let image of bgImage; let i = index">
                                        <img src="assets/dist/images/cross-icon.svg" alt="No Content" class="remove-image-btn" id="remove-image-btn" (click)="removeImage(i,'bg')" />
                                        <img [src]="image?.url" [alt]="image?.file?.name" class="uploaded-image no-image" id="uploaded-image" />
                                    </div>
                            </div>
                            <div *ngIf="bgImage?.length === 0 && !defaultSetUp">
                                <div>                                    
                                    <img src="assets/dist/images/NoImage.svg" alt="No Content" class="no-image" />
                                 </div>
                            </div>
                            <div *ngIf="defaultSetUp">
                                <div>                                 
                                    <img src="assets/dist/images/new-acuity-logo.svg" [alt]="image?.file?.name" class="uploaded-image no-image" id="uploaded-image" />
                                </div>
                            </div>
                        </div>
                        <div class="fund-name-drp-3">
                             <button id="bgButton" kendoButton  [disabled]="bgDisabled || defaultSetUp || logoImage?.length>0" class="kendo-custom-button Body-R apply-btn mr-2 upload upload-image" fillMode="outline" themeColor="primary">
                                <input type="file" name="file" (change)="onBrowseImageChange($event.target.files,'bg')"
                                multiple accept="image/*" />Browse
                            </button><br />
                            <button [disabled]="isBgUploaded || defaultSetUp || logoImage?.length>0" (click)="uploadFile('bg')" kendoButton class="kendo-custom-button Body-R apply-btn mt-2 upload-image" themeColor="primary">Upload</button>
                        </div>
                    </div>
                </div>
                <div class="col-6"></div>
            </div>            
        </div>
    </div>
</div>
<div class="form-actions mt-4 sticky-button mb-4">
    <button kendoButton type="button" class="kendo-custom-button Body-R apply-btn mr-2 upload" fillMode="outline" themeColor="primary" (click)="onReset()">Reset</button>
    <button type="submit" [disabled]="!isSaveEnabled" id="saveCover" (click)="onSubmit()" kendoButton
        class="kendo-custom-button Body-R apply-btn" themeColor="primary">Save</button>
</div>
</form>