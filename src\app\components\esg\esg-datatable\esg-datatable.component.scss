@import "../../../../variables";
.border-grey-tab {
    border-bottom: 1px solid #DEDFE0;
    border-top: 1px solid #DEDFE0;
    background: $nep-white 0% 0% no-repeat padding-box;
    position: relative;
}

:host ::ng-deep .headerKpi {
    padding-left:  0.5rem !important;
}

.esg-icon-container {
    width: 18px;
    cursor: pointer;

}

.no-data-div app-empty-state.empty-state {
    font-weight: 600 !important;
    font-size: 1rem !important;

    display: flex;
    justify-content: center;
}

.esg-f-table .frozen-header {
    width: 340px !important;
}

.esg-f-table {
    tr{
         th:first-child,td:first-child{
            border-left: none !important;
           
            
        }
        
    }


    .frozen-header {
        width: 340px !important;

        .esg-custom-arrow {
            position: absolute;
            top: 10px;
        }
    }
}

.enlargedRow {
    width: 142px;
    height: 142px;
    overflow: auto;
    resize: none;
    word-wrap: break-word;
    background: #fff;
    padding: 12px 5px 12px 16px !important;
}

.esg-modal-header {
    color: #000;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    /* 150% */
}

.esg-modal-content {   
    height: 173px;
    padding: 16px 0px 0px 16px;   
    border-radius: 4px;
    border: 1px solid var(--Border-border-dark, #E6E6E6);
    textarea {
        max-height: 150px;
    }
}

.esgKpistaticitemContainer {
    overflow-y: auto;    
    border-right: 1px solid #E6E6E6 !important;
    max-height: 230px;
}
.upload-list li {   
    position: relative;
    width: 100%;
    border-bottom: 1px solid #E6E6E6 !important;
    display: flex;
    padding: 8px 8px;
    outline: none !important;
    a {
        color: #1A1A1A !important;
        width: 100%;
    }
    background: #FFFFFF;
}

.upload-list li.active {     
    background: #FAFAFC !important;   
    a {
        color: #4061C7 !important;
    } 
}
.upload-list li:hover {     
    background: #FAFAFC !important;   
}


.esg-border-right{
   border-top: 1px solid #cac9c7;
}

.download-dropdown-content{
    width: 9rem;
    cursor: pointer !important;
}
.download-dropdown-content .export-option{
    display: block;
    width: 100%;
    height: 50px;
    padding: 1rem;
}
.download-dropdown-content:hover{
    background: #F7F8FC 0% 0% no-repeat padding-box;
    cursor: pointer !important;
}

.download-dropdown-content a {
    color: black;
}
.expand-content-text{
    padding-left: 42px;
    margin-top: -18px;
    display: flex;
}
