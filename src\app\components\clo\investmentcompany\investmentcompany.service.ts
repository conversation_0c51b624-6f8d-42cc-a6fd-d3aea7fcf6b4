import { Router } from "@angular/router";
import { HttpClient } from "@angular/common/http";
import { Observable, throwError } from "rxjs";
import { Inject, Injectable } from "@angular/core";
import { map, catchError } from "rxjs/operators";
import { Subject, BehaviorSubject } from 'rxjs';
import { TableCellUpdate } from '../models/table-cell-update.model';
import { TableFootnoteModel } from '../models/table-footnote.model';
import { ToastrService } from "ngx-toastr";

@Injectable({
  providedIn: 'root'
})
export class InvestCompanyService {
  myAppUrl: string = "";
  router: Router;
  private goToStepSubject = new Subject<number>();
  goToStep$ = this.goToStepSubject.asObservable();
  private gridData = new BehaviorSubject<any[]>([]);

  constructor(private readonly http: HttpClient, @Inject("BASE_URL") baseUrl: string, private toastrService: ToastrService) {
    this.myAppUrl = baseUrl;
  }
  setGridData(data: any) {
    this.gridData.next(data);
  }

  getGridData(): Observable<any[]> {
    return this.gridData.asObservable();
  }

  emitGoToStep(step: number): void {
    this.goToStepSubject.next(step);
  }

  getInvestCompanyList(filter: any): Observable<any> {
    return this.http
      .get<any>(this.myAppUrl + "api/v1/InvestmentCompany/investment-company/get")
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }
  getInvestCompanyListForClo(filter: any): Observable<any> {
    return this.http
      .get<any>(this.myAppUrl + "api/v1/CLO/investment-company/get")
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }


  saveInvestCompany(investcompany: any): Observable<any> {
    return this.http
      .post<any>(this.myAppUrl + "api/v1/InvestmentCompany/investment-company/add", investcompany)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }
  saveCommentries(CommentaryPayload: any): Observable<any> {

    return this.http
      .post<any>(this.myAppUrl + "api/v1/InvestmentCompany/investment-company/commentries", CommentaryPayload)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }
  getCommentries(companyId: number): Observable<any> {
    return this.http
      .get<any>(`${this.myAppUrl}api/v1/InvestmentCompany/commentaries/get/${companyId}`)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }
  getInvestCompanyById(id: number): Observable<any> {
    return this.http
      .get<any>(`${this.myAppUrl}api/v1/InvestmentCompany/investment-company/get/${id}`)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  uploadExcelFile(file: File, identifier: string, companyID: string): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('identifier', identifier);
    formData.append('companyID', companyID);
    return this.http
      .post<any>(`${this.myAppUrl}api/v1/DataHandler/upload-excel`, formData)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  getTableData(tableName: string, companyID: string): Observable<{ data: any[], columns: any[] }> {
    const payload = {
      TableIdentifier: tableName,
      CompanyID: companyID
    };
    return this.http
      .post<any>(`${this.myAppUrl}api/v1/DataHandler/get-table-data`, payload)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  downloadTemplate(identityName: string): Observable<Blob> {
    return this.http
      .get(`${this.myAppUrl}api/v1/DataHandler/download-template/${identityName}`,
        { responseType: 'blob' })
      .pipe(
        catchError(this.errorHandler)
      );
  }

  updateTableCell(payload: TableCellUpdate): Observable<any> {
    return this.http
      .post<any>(`${this.myAppUrl}api/v1/DataHandler/edit-data`, payload)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }



  updateTableCellValue(payload: TableCellUpdate): Observable<any> {
    const formData = new FormData();

    // Match exact property names from C# model
    formData.append('tableName', payload.tableName);
    if (payload.rowId instanceof Array) {
      payload.rowId.forEach(id => formData.append('rowId', id));
    } else {
      formData.append('rowId', payload.rowId);
    }
    formData.append('field', payload.field);
    formData.append('oldValue', payload.oldValue);
    formData.append('newValue', payload.newValue);
    formData.append('companyID', payload.companyID);
    formData.append('parent', payload.parent);
    formData.append('supportingDocument', payload.supportingDocuments);
    formData.append('comment', payload.comment);
    return this.http
      .post<any>(`${this.myAppUrl}api/v1/DataHandler/edit-data`, formData)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  saveFootnote(footnoteModel: TableFootnoteModel): Observable<any> {
    return this.http
      .post<any>(`${this.myAppUrl}api/v1/InvestmentCompany/footnotes/save`, footnoteModel)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  getFootnote(tableName: string, companyID: string): Observable<any> {
    const footnoteModel: TableFootnoteModel = {
      footenote_ID: 0,
      footnoteMapping: tableName,
      footnoteIdentifier: String(companyID),
      footnoteContent: ''
    };

    return this.http
      .post<any>(`${this.myAppUrl}api/v1/InvestmentCompany/footnotes/get`, footnoteModel)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  errorHandler(error: any) {
    return throwError(error);
  }
  exportTableData(tableName: string, companyId: string): Observable<Blob> {
    const url = `${this.myAppUrl}api/v1/DataHandler/download-uploaded-data/${tableName}/${companyId}`;
    return this.http.get(url, {
      responseType: 'blob'
    }).pipe(
      catchError(this.errorHandler)
    );
  }

  deleteInvestmentCompany(companyId: number,tableName:string=null): Observable<any> {
    let url="";
    if(tableName==null){
      url=`${this.myAppUrl}api/v1/InvestmentCompany/investment-company/delete/${companyId}`;      
    }
    else{
      url=`${this.myAppUrl}api/v1/InvestmentCompany/investment-company/delete/${companyId}/${tableName}`;
    }
    return this.http
      .delete<any>(url)
      .pipe(
        catchError(this.errorHandler)
      );
  }

  getTabList(pageId: number, companyId: number, cloId: number = 0): Observable<any> {
        return this.http
          .get<any>(`${this.myAppUrl}api/v1/InvestmentCompany/get/${pageId}/${companyId}/${cloId}`)
          .pipe(
            map((response: any) => response),
            catchError(this.errorHandler)
          );
      }
}