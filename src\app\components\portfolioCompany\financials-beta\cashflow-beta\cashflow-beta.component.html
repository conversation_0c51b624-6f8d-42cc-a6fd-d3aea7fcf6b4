<div class="shadow-sm chart-area w-100 " [ngClass]="tableResult?.length > 0 ?'border-top':''">
  <div class="tbl-sec">
    <kendo-grid id="kpi-grid" class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cab-table-grid" 
            [kendoGridBinding]="tableResult" scrollable="virtual" [rowHeight]="44" [resizable]="true">
            <ng-container *ngIf="tableResult.length > 0">
                <kendo-grid-column [sticky]="true" [minResizableWidth]="200" [maxResizableWidth]="800"  [width]="300" 
                *ngFor="let col of tableFrozenColumns;" [field]="col.field">
                    <ng-template kendoGridHeaderTemplate>
                        <div class="header-icon-wrapper wd-100 header-left-padding" >
                          <span class="TextTruncate S-M">
                           {{col.header}}
                          </span>
                        </div>
                      </ng-template>
                    <ng-template kendoGridCellTemplate let-rowData>
                        <div class="content header-left-padding" >
                            <span *ngIf="col.header =='KPI'" title={{rowData[col.field]}}
                            [ngClass]="[(rowData.IsHeader||rowData.IsBoldKpi) ? 'showToolTip TextTruncate bold-text' :'showToolTip TextTruncate',rowData.IsHeader ? 'headerKpi bold-text' : rowData['IsBoldKpi'] ? 'bold-text': '',((rowData.ParentId !==0||rowData.ParentId ==0)&&!rowData.IsHeader)?'pl-3':'']">
                            <span *ngIf="rowData.ParentId !== 0">- </span>{{rowData[col.field]}}
                            <span *ngIf="rowData['KPI Info'] =='#'">{{'('+rowData['KPI Info'] +')'}}</span>
                        </span>
                        </div>
                    </ng-template>
                </kendo-grid-column>
            </ng-container>
            <ng-container *ngIf="tableResult.length > 0">
                <kendo-grid-column [minResizableWidth]="200" *ngFor="let col of tableColumns; index as i" [maxResizableWidth]="800"
                [width]="200" title="{{col.header}}">
                <ng-template kendoGridHeaderTemplate>
                    <div class="header-icon-wrapper wd-100">
                      <span class="TextTruncate S-M">{{col.header}}</span>
                    </div>
                  </ng-template>
                <ng-template kendoGridCellTemplate let-rowData> 
                    <div tabindex="0" class="prtcmny-det-o cell-padding" 
                    [class.table-data-right]="col.field !='Kpi'" [class.table-data-right]="col.field !='Kpi'" [ngClass]="[(col.header !=='Kpi' && isErrorLog && printColumn(rowData,col)  && !rowData.IsHeader)?'kpi-set-bgcolor':'kpi-unset-bgcolor',(col.header !=='Kpi' && printCalcColumn(rowData,col)  && !rowData.IsHeader)?'kpi-set-calc-bgcolor':'kpi-unset-bgcolor']"                                           
                    (click)="onAuditLog(rowData,col) " (dblclick)="onEditInit(rowData,col)" id="cashflow-value">
                    <div class="content">
                        <div *ngIf="col.header !='KPI'" [ngClass]="rowData.IsBoldKpi ? 'bold-text': ''" class="showToolTip TextTruncate">
                            <div [title]="rowData['KpiInfo']=='Text'? rowData[col.field]:''"  *ngIf="rowData[col.field]!= undefined && rowData[col.field]!=null && rowData[col.field]!=''&& !rowData.IsHeader;else empty_Text">
                                <container-element [ngSwitch]="rowData['KpiInfo']">
                                    <container *ngSwitchCase="'#'">
                                        <span [title]="rowData[col.field]"  [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number:'1.0-0' | minusSignToBrackets) : rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchCase="'Text'">
                                        <span class="float-left TextTruncate tr-width-v1 text-align-left" [title]="rowData[col.field]" [innerHtml]="rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchCase="'%'">
                                        <span [title]="rowData[col.field]"  [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.percentDecimal | minusToBracketsWithPercentage: '%'): rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchCase="'x'">
                                        <span [title]="rowData[col.field]" *ngIf="rowData[col.field] != 'NA' && rowData[col.field] != ''" [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.multipleDecimal)+'x': rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchCase="'$'">
                                        <span [title]="rowData[col.field]" *ngIf="rowData[col.field] != 'NA' && rowData[col.field] != ''" [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchDefault>
                                    </container>
                                </container-element>                                   
                            </div>
                            <ng-template #empty_Text class="detail-sec">
                                <span [ngClass]="rowData['KpiInfo']=='Text'? 'float-left':'float-right'" *ngIf="!rowData[col.field + ' editable'] && !rowData.IsHeader">NA</span>
                                <div class="cell-padding"  *ngIf="rowData.IsHeader">
                                    <div></div>
                                </div>
                            </ng-template>
                        </div>
                    </div>
                </div>
                </ng-template>
            </kendo-grid-column> 
            </ng-container>
            <ng-template kendoGridNoRecordsTemplate>
                <app-empty-state class="finacials-beta-empty-state" [imageHeight]="'41vh'" [isGraphImage]="false"
                *ngIf="tableResult.length == 0"></app-empty-state>
            </ng-template>
    </kendo-grid>
  </div>
</div>
<app-loader-component id="companyKpiPop" *ngIf="isLoader"></app-loader-component>
    <app-foot-note *ngIf="tableResult.length > 0" [moduleId]="kpiModuleId" [companyId]="model?.portfolioCompany?.portfolioCompanyID" class="comm-footnote custom-quill-editor"></app-foot-note>

    <div *ngIf="showEditPopup" class="nep-modal nep-modal-show kpi-add-edit-modal nep-kpi-bg">
        <div class="nep-modal-mask"></div>
        <div class="nep-card nep-card-shadow nep-modal-panel nep-modal-default nep-all-upload-file">
            <app-kpi-cell-edit id="cashflow-value-edit" [dataRow]="dataRow" [tableColumns]="dataColumns" [moduleCompanyModel]="uniqueModuleCompany" (cancelButtonEvent)="cancelButtonEvent()" (confirmButtonEvent)="onSubmitButtonEvent($event)"></app-kpi-cell-edit>
        </div>
    </div>
    <div *ngIf="infoUpdate">
        <confirm-modal IsInfoPopup="true" customwidth="400px" modalTitle="Change Values in Selection" primaryButtonName="OK"
            (primaryButtonEvent)="infoUpdate = false">
            <div>
                <div class="oprtnkpi-lh">
                    To edit cell data please select numbers in <b><i>'Thousands'</i></b> under <b><i>Values in</i></b>
                    dropdown
                </div>
            </div>
        </confirm-modal>
    </div>