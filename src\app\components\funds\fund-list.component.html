﻿<div class="row">
    <div class="col-lg-12">
        <div class="add-user-component">
            <div class="card card-main">
                <div class="card-header card-header-main p-0 border-bottom-0">
                    <div class="row mr-0 ml-0 fundlist-header border-bottom">
                        <div class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                            <div class="float-right">
                                <div class="d-inline-block search">
                                    <span class="fa fa-search fasearchicon p-1"></span>
                                    <input #gb pInputText type="text" (input)="searchLoadPCLazy()"
                                        class="search-text-company companyListSearchHeight TextTruncate"
                                        placeholder="Search fund" [(ngModel)]="globalFilter">
                                </div>
                                <div class="d-inline-block"
                                    [hideIfUnauthorized]='{featureId:feature.Fund,action:"export"}'>
                                    <img id="fund-list-download" class="p-action-padding download-excel" title="Export Fund (Excel file)" alt=""
                                        (click)="exportFundList()" src="assets/dist/images/Cloud-download.svg" />
                                </div>
                                <div class="d-inline" [hideIfUnauthorized]='{featureId:feature.Fund,action:"export"}'>
                                    <span class="col-divider">
                                    </span>
                                </div>
                                <div class="d-inline-block"
                                    [hideIfUnauthorized]='{featureId:feature.Fund,action:"add"}'>
                                    <div class="add-icon p-add-padding">
                                        <a id="fund-list-add-fund" (click)="addRedirect()" tooltipPosition="top" title="Add fund">
                                            <img class="" title="Add Fund" src="assets/dist/images/plus.svg"
                                                alt="" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div *ngIf="funds==undefined">
                        <img alt="" src="assets/dist/images/loading.gif" class="loading-img" />
                    </div>
                    <kendo-grid id="fund-list-grid"  [data]="view | async" [pageSize]="state.take" [skip]="state.skip" 
                        [sortable]="true" [sort]="sort" [pageable]="{
                                buttonCount: 10,
                                info: true,
                                type: 'numeric',
                                pageSizes: [100,200,300,400,500],
                                previousNext: true  }" (dataStateChange)="dataStateChange($event)"
                        class="custom-kendo-list-grid k-grid-border-right-width k-grid-outline-none">
                        <ng-container *ngFor="let item of headers">
                            <kendo-grid-column class="TextTruncate"
                                *ngIf="item.sortFieldName.toLowerCase()==('ValueColumn1.Value').toLowerCase()"
                                field="ValueColumn1.Value">
                                <ng-template kendoGridHeaderTemplate>
                                    <div class="header-icon-wrapper wd-98">
                                        <span class="TextTruncate S-M">
                                            {{item.header}}
                                        </span>
                                    </div>
                                </ng-template>
                                <ng-template kendoGridCellTemplate let-fund>
                                    <a id="fund-list-redirect-fund" (click)="redirectToFund(fund.valueColumn1)"  *ngIf="fund.valueColumn1.link != ''" class="click-view TextTruncate"
                                        title="{{fund.valueColumn1.value}}" href="javascript:void(0);"
                                        [routerLink]="[fund.valueColumn1.link, fund.valueColumn1.encryptedId]"
                                        title="View Details"
                                        [hideIfUnauthorized]='{featureId:feature.Fund,action:"view"}'>{{fund.valueColumn1.value}}</a>

                                    <div *ngIf="fund.valueColumn1.link == ''" class="row mr-0 ml-0 fundl-cd">
                                        <div class="col-12 pr-0 pl-0">
                                            <div class="  TextTruncate"><span
                                                    class="TextTruncate">{{fund.valueColumn1.value}}</span></div>
                                        </div>
                                    </div>
                                </ng-template>
                            </kendo-grid-column>
                            <kendo-grid-column class="TextTruncate"
                                *ngIf="item.sortFieldName.toLowerCase()==('ValueColumn2.Value').toLowerCase()"
                                field="ValueColumn2.Value">
                                <ng-template kendoGridHeaderTemplate>
                                    <div class="header-icon-wrapper wd-98">
                                        <span class="TextTruncate S-M">
                                            {{item.header}}
                                        </span>
                                    </div>
                                </ng-template>
                                <ng-template kendoGridCellTemplate let-fund>
                                    <a id="fund-list-click-view" (click)="redirectToFund(fund.valueColumn2)" *ngIf="fund.valueColumn2.link != ''" class="click-view TextTruncate"
                                        title="{{fund.valueColumn2.value}}" href="javascript:void(0);"
                                        [routerLink]="[fund.valueColumn2.link, fund.valueColumn2.encryptedId]"
                                        title="View Details"
                                        [hideIfUnauthorized]='{featureId:feature.Fund,action:"view"}'>{{fund.valueColumn2.value}}</a>

                                    <div *ngIf="fund.valueColumn2.link == ''" class="row mr-0 ml-0 flist-cd">
                                        <div class="col-12 pr-0 pl-0">
                                            <div class="  TextTruncate"><span
                                                    class="TextTruncate">{{fund.valueColumn2.value}}</span></div>
                                        </div>
                                    </div>
                                </ng-template>
                            </kendo-grid-column>
                            <kendo-grid-column class="TextTruncate"
                                *ngIf="item.sortFieldName.toLowerCase()==('ValueColumn3.Value').toLowerCase()"
                                field="ValueColumn3.Value">
                                <ng-template kendoGridHeaderTemplate>
                                    <div class="header-icon-wrapper wd-98">
                                        <span class="TextTruncate S-M">
                                            {{item.header}}
                                        </span>
                                    </div>
                                </ng-template>
                                <ng-template kendoGridCellTemplate let-fund>
                                    <a id="fund-list-redirect-to-fund" (click)="redirectToFund(fund.valueColumn3)" *ngIf="fund.valueColumn3.link != ''" class="click-view TextTruncate"
                                        title="{{fund.valueColumn3.value}}" href="javascript:void(0);"
                                        [routerLink]="[fund.valueColumn3.link, fund.valueColumn3.encryptedId]"
                                        title="View Details"
                                        [hideIfUnauthorized]='{featureId:feature.Fund,action:"view"}'>{{fund.valueColumn3.value}}</a>

                                    <div *ngIf="fund.valueColumn3.link == ''" class="row mr-0 ml-0 fundlist-cd">
                                        <div class="col-12 pr-0 pl-0">
                                            <div class="TextTruncate">{{fund.valueColumn3.value}}</div>
                                        </div>
                                    </div>
                                </ng-template>
                            </kendo-grid-column>
                            <kendo-grid-column class="TextTruncate"
                                *ngIf="item.sortFieldName.toLowerCase()==('ValueColumn4.Value').toLowerCase()"
                                field="ValueColumn4.Value">
                                <ng-template kendoGridHeaderTemplate>
                                    <div class="header-icon-wrapper wd-98">
                                        <span class="TextTruncate S-M ">
                                            {{item.header}}
                                        </span>
                                    </div>
                                </ng-template>
                                <ng-template kendoGridCellTemplate let-fund>
                                    <a id="fund-list-view-details" (click)="redirectToFund(fund.valueColumn4)" *ngIf="fund.valueColumn4.link != ''" class="click-view TextTruncate"
                                        title="{{fund.valueColumn4.value}}" href="javascript:void(0);"
                                        [routerLink]="[fund.valueColumn4.link, fund.valueColumn4.encryptedId]"
                                        title="View Details"
                                        [hideIfUnauthorized]='{featureId:feature.Fund,action:"view"}'>{{fund.valueColumn4.value}}</a>

                                    <div *ngIf="fund.valueColumn4.link == ''" class="row mr-0 ml-0 fl-cursord">
                                        <div class="col-12 pr-0 pl-0">
                                            <div class="  TextTruncate">{{fund.valueColumn4.value}}</div>
                                        </div>
                                    </div>
                                </ng-template>
                            </kendo-grid-column>
                        </ng-container>
                        <ng-template kendoGridNoRecordsTemplate>
                            <app-empty-state class="finacials-beta-empty-state" [imageHeight]="'76vh'" [isGraphImage]="false"
                            ></app-empty-state>
                        </ng-template>
                    </kendo-grid>
                </div>
            </div>
        </div>
    </div>
</div>
<app-loader-component *ngIf="isLoader"></app-loader-component>