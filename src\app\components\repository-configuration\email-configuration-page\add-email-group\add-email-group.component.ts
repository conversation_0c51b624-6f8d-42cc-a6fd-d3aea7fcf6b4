import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import {EmailGroupCreateDto, EmailGroupUpdateDto, EmailGroupDetailDto, EmailMemberDto  } from '../../../repository-configuration/model/config-model';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { Router, ActivatedRoute } from "@angular/router";
import { group } from 'console';

interface Member {
  id?: number;
  name?: string;
  email: string;
}

@Component({
  selector: 'app-add-email-group',
  templateUrl: './add-email-group.component.html',
  styleUrls: ['./add-email-group.component.scss'],
})

export class AddEmailGroupComponent implements OnInit {
  // Properties - keeping only what's used in HTML
  newGroupName: string = '';
  newMembers: Member[] = [];
  selectedCompanies: any[] = [];
  id: number = 0;
  isEditMode: boolean = false;
  errorMessage: string = 'This Name already exists';
  hasError: boolean = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private toastrService: ToastrService,
    private emailGroupService: RepositoryConfigService,
  ) { }

  ngOnInit(): void {
     this.route.params.subscribe(params => {
      this.id = Number(params['id']);
     });
     if(this.id > 0) {
      this.isEditMode = true;
      this.getEmailGroupDetails();
    } else {
      // Initialize with 3 empty member rows for add mode
      this.newMembers = [{ name: '', email: '' }, { name: '', email: '' }, { name: '', email: '' }];
    }
  }

  onCompanySelected(companies: any[]): void {
    this.selectedCompanies = companies;
  }

  getEmailGroupDetails(): void {
    this.emailGroupService.getEmailGroupDetails(this.id).subscribe({
      next: (response: EmailGroupDetailDto) => {
        this.newGroupName = response.groupName;

        // Map EmailMemberDto to Member interface
        this.newMembers = response.emailMembers.map(member => ({
          id: member.memberId,
          name: member.name,
          email: member.email
        }));

        // Set selected companies - assuming you have a way to map company IDs to company objects
        // You might need to fetch company details separately or modify this based on your company selection component
        this.selectedCompanies = response.companyAssociations.map(id => ({ companyId: id.companyId }));
      },
      error: (error) => {
        this.toastrService.error(error.message || 'Failed to load email group details');
      }
    });
  }

  // Add a new member row to the email list
  addNewMember(): void {
    this.newMembers.push({ name: '', email: '' });
  }

  // Remove a member from the email list
  removeNewMember(index: number): void {
    if (this.newMembers.length > 1) {
      // Create a new array without the member at the specified index
      this.newMembers = this.newMembers.filter((_, i) => i !== index);
    } else {
      this.toastrService.warning('At least one member is required');
      // Clear the fields instead of removing
      this.newMembers[0] = { name: '', email: '' };
    }
  }

  // Reset the form fields
  resetForm(): void {
    this.newGroupName = '';
    this.newMembers = [{ name: '', email: '' }, { name: '', email: '' }, { name: '', email: '' }];
    this.selectedCompanies = [];
  }

 cancel(): void {
  this.resetForm();
  this.router.navigate(['/email-configuration'], {
    queryParams: { activeTab: 'Email Groups' }
  });
}
  isCreateEnabled(): boolean {
  return this.newGroupName.trim().length > 0;
}
createEmailGroup(): void {
    if (this.isEditMode) {
        this.updateEmailGroup();
    } else {
        this.createNewEmailGroup();
    }
}

public createNewEmailGroup(): void {
    // Filter out empty members
    const validMembers = this.newMembers.filter(member =>
        member.email?.trim().length > 0 || member.name?.trim().length > 0
    );

    let emailGroup = {
        groupName: this.newGroupName.trim(),
        groupId: 0
    };

    this.emailGroupService.checkDuplicateName(emailGroup).subscribe({
        next: (response) => {
            if (response && response.isDuplicateName) {
                this.hasError = true;
                return;
            } else {
                this.hasError = false;
                const emailGroup: EmailGroupCreateDto = {
                    groupName: this.newGroupName.trim(),
                    emailList: validMembers.map(member => ({
                        name: member.name?.trim() || '',
                        email: member.email?.trim()
                    })),
                    companyIds: this.selectedCompanies.map(company => (company.id || company.companyId).toString())
                };
                this.emailGroupService.createEmailGroup(emailGroup).subscribe({
                    next: (response) => {
                        if (response && response.groupId) {
                            this.toastrService.success("You have successfully created email group", "", {
                                positionClass: "toast-center-center",
                            });
                            this.resetForm();
                            this.router.navigate(['/email-configuration'], {
                                queryParams: { activeTab: 'Email Groups' }
                            });
                        } else {
                            this.toastrService.error('Failed to create email group');
                        }
                    },
                    error: (error) => {
                        this.toastrService.error(error.message || 'Failed to create email group');
                    }
                });
            }
        },
        error: () => {
            this.toastrService.error('Failed to check for duplicate group name');
        }
    });
}

    public updateEmailGroup(): void {
    // Filter out empty members
    const validMembers = this.newMembers.filter(member =>
        member.email?.trim().length > 0 || member.name?.trim().length > 0
    );

    let emailGroup = {
        groupName: this.newGroupName.trim(),
        groupId: this.id
    };

    this.emailGroupService.checkDuplicateName(emailGroup).subscribe({
        next: (response) => {
            if (response && response.isDuplicateName) {
                this.hasError = true;
                return;
            } else {
                this.hasError = false;
                const emailGroupUpdate: EmailGroupUpdateDto = {
                    groupName: this.newGroupName.trim(),
                    emailMembers: validMembers.map(member => ({
                        memberId: member.id || 0,
                        name: member.name?.trim() || '',
                        email: member.email?.trim() || '',
                        isActive: true
                    })),
                    companyIds: this.selectedCompanies.map(company => company.id || company.companyId)
                };
                this.emailGroupService.updateEmailGroup(this.id, emailGroupUpdate).subscribe({
                    next: () => {
                        this.toastrService.success("You have successfully updated email group", "", {
                            positionClass: "toast-center-center",
                        });
                        this.router.navigate(['/email-configuration'], {
                            queryParams: { activeTab: 'Email Groups' }
                        });
                    },
                    error: (error) => {
                        this.toastrService.error(error.message || 'Failed to update email group');
                    }
                });
            }
        },
        error: () => {
            this.toastrService.error('Failed to check for duplicate group name');
        }
    });
}
}

