<div class="mr-0 ml-0 data-request-section">
  <div class="mr-0 ml-0 card card-main static-card">
    <div class="row mr-0 ml-0">
      <div class="col-12 pr-0 pl-0">
        <kendo-label class="text-form S-R label-text" text="Request Name">
          <kendo-textbox text="Large" class="custom-kendo-text-box kendo-flat-text" fillMode="flat" [clearButton]="true"
            [(ngModel)]="requestModel.name" (valueChange)="valicateRequestName()" placeholder="Request Name">
          </kendo-textbox>
        </kendo-label>
      </div>

    </div>
    <div class="row pl-3 pr-0 pt-3">
      <div class="col-sm-6 col-md-6 col-lg-6 pl-0 pr-3">
        <div class="text-form S-R is-automatic label-text float-left">Automatic Trigger
          <img class="info pl6 pb-1 bottom" position="bottom" kendoTooltip
            title="Switching this toggle on will enable automatic trigger of request. To send manual requests, toggle this off."
            alt="" [src]="'assets/dist/images/function-Icon.svg'" />
        </div>
        <div class="float-right">
          <kendo-switch size="small" (valueChange)="validateAutoRequest()"
            [(ngModel)]="requestModel.isAutomatic" [onLabel]="' '" [offLabel]="' '"></kendo-switch>
        </div>
      </div>
      <div class="col-sm-6 col-md-6 col-lg-6 pl-0 pr-3">
        <div class="text-form S-R is-active label-text float-left">Activate Request</div>
        <div class="float-right">
          <kendo-switch size="small" (valueChange)="validateAutoRequest()"
            [(ngModel)]="requestModel.isActive" [onLabel]="' '" [offLabel]="' '"></kendo-switch>
        </div>
      </div>
    </div>
  </div>

  <div class="mr-0 ml-0 card card-main static-card2">
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 col-xs-12 pr-0 pl-0">
      <div
        class="tab-section-div panel-heading pr-0 pl-0 custom-tab-panel-heading pb-0 border-bottom-0 panel-title custom-tabs custom-mat-tab">
        <nav mat-tab-nav-bar [tabPanel]="tabPanel" *ngIf="tabList?.length > 0">
          <a mat-tab-link [disableRipple]="true" *ngFor="let subTab of tabList" (click)="onTabClick($event);"
            [active]="subTab.active" class="TextTruncate" title="{{subTab.alias}}">
            {{subTab.name}} </a>
        </nav>
      </div>
      <mat-tab-nav-panel #tabPanel>
        <div class="row mr-0 ml-0 details-search-header">
          <div class="col-sm-9 col-md-9 col-lg-9 pr-3 search-box">
            <kendo-textbox size="large"
              class="request-details-search custom-kendo-text dr-search-width kendo-text-search" selectOnFocus="false"
              placeholder="Search">
              <ng-template kendoTextBoxSuffixTemplate>
                <button class="kendo-text-button" kendoButton [svgIcon]="searchSVG"></button>
              </ng-template>
            </kendo-textbox>
          </div>
          <div class="col-sm-3 col-md-3 col-lg-3 pr-3 add-req-button-box">
            <button [disabled]="disableAddGroup" kendoButton (click)="onGroupAdd()" class="add-req-button float-right"
              [svgIcon]="plusIcon" fillMode="outline" themeColor="primary">
              Add Group
            </button>
          </div>
        </div>


        <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 col-xs-12 pr-0 pl-0 pt-4 pb-4 no-group-div"
          *ngIf="groupModelList.length == 0">
          <div class="row mr-0 ml-0 pt-3 pb-3 no-group-div-body" [style.height]="109">
            <div
              class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0 d-flex flex-column justify-content-center">
              <div class="text-center">
                <img alt="" src="assets/dist/images/no-groups.svg" class="showHandIcon pr-1 mt-0" />
              </div>
              <div class="text-center pt-2 empty-state-text">
              </div>
              <div class="text-center pt-0 empty-state-text">
                No groups available. Please click on 'Add Group'.
              </div>
            </div>
          </div>
        </div>

        <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 col-xs-12 pr-0 pl-0 pb-4 extention-div">
          <kendo-expansionpanel *ngFor="let groupModel of getGroupModelList(); index as i" [svgExpandIcon]="arrowDown"
            [svgCollapseIcon]="arrowUp" [expanded]="groupModel.expanded" (action)="onAction(i, $event)">
            <ng-template kendoExpansionPanelTitleDirective *ngIf="!groupModel.deleted"
              (click)="$event.stopPropagation()">
              <div class="header-content TextTruncate Body-B">
                {{groupModel?.groupName}}
              </div>
              <span class="float-right actions exp-head-op">
                <img class="info pl6" (click)="onEditGroup(groupModel)" alt=""
                  [src]="'assets/dist/images/EditIcon.svg'" />
                <img class="info pl6" (click)="openGroupDelete(groupModel)" alt=""
                  [src]="'assets/dist/images/delete.svg'" />
              </span>
            </ng-template>
            <div class="content" *ngIf="!groupModel.deleted">
              <div class="row mr-0 ml-0 pb-4">
                <div class="col-4 col-sm-4 col-lg-4 col-xl-4 col-md-4 col-xs-4 pr-4 pl-0">
                  <kendo-label class="label-text" text="Frequency">
                    <kendo-combobox [fillMode]="'flat'" [virtual]="virtual" placeholder="Select Frequency"
                      [(ngModel)]="groupModel.frequency" name="Frequency"
                      class="access-subfeature k-outline-none dropdown-custom-width k-input-flat frequency-drpd"
                      [data]="frequencyList" textField="text" valueField="text" (valueChange)="valicateRequest()">
                      <ng-template kendoDropDownListItemTemplate let-dataItem>
                        <span title="{{ dataItem.text }}" class="template TextTruncate">{{ dataItem.text }}</span>
                      </ng-template>
                    </kendo-combobox>
                  </kendo-label>
                </div>
                <div class="col-4 col-sm-4 col-lg-4 col-xl-4 col-md-4 col-xs-4 pr-4 pl-0">
                  <kendo-label text="Portfolio Company Name">
                    <kendo-multiselect #pcmultiSelect class="custom-multiselect-dr" placeholder="Select Company Name"
                      [autoClose]="false" [(ngModel)]="groupModel.companyList" [fillMode]="'flat'" [virtual]="virtual"
                      [data]="companiesList" [checkboxes]="true" [kendoDropDownFilter]="filterSettings"
                      textField="companyName" valueField="companyId" [tagMapper]="tagMapper"
                      [kendoMultiSelectSummaryTag]="1" (valueChange)="validateCompanies(groupModel, pcmultiSelect)" (close)="onPcDropdownClose(groupModel)">
                      <ng-template kendoMultiSelectGroupTagTemplate let-dataItems>
                        + {{ dataItems.length }}
                      </ng-template>
                    </kendo-multiselect>
                  </kendo-label>
                </div>
                <div class="col-4 col-sm-4 col-lg-4 col-xl-4 col-md-4 col-xs-4 pr-0 pl-0">
                  <kendo-label text="Fund Name">
                    <kendo-multiselect #fmultiSelect [autoClose]="false" placeholder="Select Fund Name" class="custom-multiselect-dr"
                      [(ngModel)]="groupModel.fundList" [fillMode]="'flat'" [virtual]="virtual"
                      [kendoDropDownFilter]="filterSettings" [data]="fundsList" [checkboxes]="true" textField="fundName"
                      valueField="fundId" [tagMapper]="tagMapper" [kendoMultiSelectSummaryTag]="1"
                      (valueChange)="validateFunds(groupModel, fmultiSelect)"  (focus)="onFundDropdownClose(groupModel)" (close)="onFundDropdownClose(groupModel)">
                      <ng-template kendoMultiSelectGroupTagTemplate let-dataItems>
                        + {{ dataItems.length }}
                      </ng-template>
                    </kendo-multiselect>
                  </kendo-label>
                </div>
              </div>
              <div class="row mr-0 ml-0">
                <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 col-xs-12 pr-0 pl-0 pb-4">
                  <kendo-label class="label-text" text="Submitter(s)">
                    <kendo-multiselect #submittermultiSelect [autoClose]="false" placeholder="Select Submitter" class="custom-multiselect-dr"
                      [(ngModel)]="groupModel.submitterList" [fillMode]="'flat'" [kendoMultiSelectSummaryTag]="100000"
                      [data]="submitterList" [kendoDropDownFilter]="filterSettings" textField="emailId"
                      valueField="emailId" [tagMapper]="tagMapper" (valueChange)="validateSubmitters(groupModel, submittermultiSelect)" (focus)="onSubmitterDropdownClose(groupModel)" (close)="onSubmitterDropdownClose(groupModel)">
                      <!-- Custom tag template -->
                      <ng-template kendoMultiSelectTagTemplate let-dataItem>
                        <span>{{ dataItem.fullName }}</span>
                      </ng-template>
                    </kendo-multiselect>
                  </kendo-label>
                </div>
                <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 col-xs-12 pr-0 pl-0 pb-4">
                  <kendo-label class="label-text" text="Recipient(s)">
                    <kendo-multiselect #recipientmultiSelect [autoClose]="false" placeholder="Select Recipient" class="custom-multiselect-dr"
                      [allowCustom]="true" [(ngModel)]="groupModel.recipientList" [fillMode]="'flat'"
                      [kendoMultiSelectSummaryTag]="100000" [data]="recipientList"
                      [kendoDropDownFilter]="filterSettings" textField="emailId" valueField="emailId"
                      [tagMapper]="tagMapper" (valueChange)="validateRecipients(groupModel, recipientmultiSelect)" (focus)="onRecipientDropdownClose(groupModel)" (close)="onRecipientDropdownClose(groupModel)" [valueNormalizer]="recipientNormalizer">
                      <!-- Custom tag template -->
                      <ng-template kendoMultiSelectTagTemplate let-dataItem>
                        <span>{{ dataItem.fullName }}</span>
                      </ng-template>
                    </kendo-multiselect>
                  </kendo-label>
                </div>
              </div>
              <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12 pr-0 pl-0 supporting-documents">
                <div class="row mr-0 ml-0 supporting-documents-header">
                  <div class="col-md-12 col-lg-12 col-12 col-xl-12 col-sm-12 pr-0 pl-0">
                    <div class="float-left supporting-documents-title TextTruncate">Supporting
                      Document(s)</div>
                    <div class="float-right supporting-documents-title TextTruncate">
                      <label for="fileDropRef{{i}}" class="mb-0"><img class="uploadFile"
                          [src]="'assets/dist/images/MdOutlineAttachFile.svg'" alt="" /></label>
                      <label title="{{i}}" for="fileDropRef{{i}}" class="cursor-pointer mb-0">Attach</label>
                    </div>
                  </div>
                </div>
                <div class="row mr-0 ml-0 supporting-documents-body">
                  <div class="col-md-12 col-lg-12 col-12 col-xl-12 col-sm-12 pr-0 pl-0 repository-custom-header">
                    <div class="left-container modal-default-height left-c-image-height all-popup-bulk-upload"
                      fileDragDrop>
                      <div class="top-left-container">
                        <input type="file" name="groupModel.groupName" #fileDropRef{{i}} id="fileDropRef{{i}}" multiple
                          class="multiple-file-drop" (change)="onFileChange(groupModel, $event.target.files,$event)"
                          hidden />
                        <div class="centered" *ngIf="groupModel.attachmentList.length==0">
                          <div class="flex-container height-input-drop-file">
                            <div class="centered">
                              <label class="mb-0"><img class="uploadFile" [src]="getIcons('uploadFile')"
                                  alt="" /></label>
                            </div>
                            <div class="centered">
                              <label class="multiple-file-browse mb-0">Upload supporting documents here</label>
                            </div>
                          </div>
                        </div>
                        <div class="top-left-container" *ngIf="groupModel.attachmentList.length>0">
                          <div class="chips-container multiple-supporting-documents">
                            <mat-chip-list class="chip-text-align multiple-file-upload">
                              <mat-chip *ngFor="let file of groupModel.attachmentList; let i = index" removable>
                                <div
                                  class="multiple-chip-content TextTruncate d-flex align-items-center justify-content-between">
                                  <div class="d-flex align-items-left ">
                                    <ng-container [ngSwitch]="file.extension">
                                      <img *ngSwitchCase="FileExtension.XLSX"
                                        [src]="'assets/dist/images/FaRegFileExcel.svg'" alt="xlsxfile">
                                      <img *ngSwitchCase="FileExtension.PDF" [src]="'assets/dist/images/Left Icon.svg'"
                                        alt="pdffile">
                                      <img *ngSwitchCase="FileExtension.ZIP"
                                        [src]="'assets/dist/images/FaRegFileArchive.svg'" alt="zipfile">
                                      <img *ngSwitchCase="FileExtension.PNG"
                                        [src]="'assets/dist/images/FaRegFileImage.svg'" alt="imagefile">
                                      <img *ngSwitchCase="FileExtension.JPG"
                                        [src]="'assets/dist/images/FaRegFileImage.svg'" alt="imagefile">
                                      <img *ngSwitchCase="FileExtension.TXT"
                                        [src]="'assets/dist/images/FaRegFileWord.svg'" alt="docfile">
                                      <img *ngSwitchCase="FileExtension.DOCX"
                                        [src]="'assets/dist/images/FaRegFileWord.svg'" alt="docfile">
                                      <img *ngSwitchCase="FileExtension.PPTX"
                                        [src]="'assets/dist/images/FaRegFilePowerpoint.svg'" alt="pptfile">
                                      <img *ngSwitchCase="FileExtension.PPT"
                                        [src]="'assets/dist/images/FaRegFilePowerpoint.svg'" alt="ppt file">
                                      <img *ngSwitchDefault [src]="'assets/dist/images/FaRegFileArchive.svg'"
                                        alt="defaultfile">
                                    </ng-container>
                                  </div>
                                  <div class="chip-file-name TextTruncate mx-auto align-items-center pl-1"
                                    title="{{ file.name }}">{{ file.name }}</div>
                                  <img [src]="'assets/dist/images/FiX.svg'" class="cursor-pointer ml-1"
                                    (click)="removeSupportDocumentFile(groupModel, file, i)" alt="cancel" />
                                </div>
                              </mat-chip>
                            </mat-chip-list>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12 pr-0 pl-0 group-reminders">
                <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12 pr-0 pl-0 user-header">
                  <div class="float-left group-reminders-title TextTruncate S-M">Add Reminder(s)
                  </div>
                  <div class="float-left group-reminders-title TextTruncate float-right">
                    <span class="add-reminder" (click)="onAddReminder(groupModel)">Add Reminder</span>
                  </div>
                </div>
                <div *ngFor="let reminder of groupModel.reminderList">
                  <div class="row mr-0 ml-0 pb-4 reminder" *ngIf="!reminder.deleted">
                    <div class="col-6 col-sm-6 col-lg-6 col-xl-6 col-md-6 col-xs-6 pr-4 pl-0">
                      <kendo-label class="text-form S-R label-text" text="Number of days before/after due date">
                        <kendo-numerictextbox (valueChange)="validateNumberOfDays(reminder)" [min]="0" [max]="90"
                          class="custom-kendo-text-box days-drpd" fillMode="flat" format="##"
                          [(ngModel)]="reminder.noOfDays" placeholder="Number of days before/after due date">
                        </kendo-numerictextbox>
                      </kendo-label>
                    </div>
                    <div class="col-5 col-sm-5 col-lg-5 col-xl-5 col-md-5 col-xs-5 pr-4 pl-0">
                      <kendo-label class="label-text" text="Period">
                        <kendo-dropdownlist [fillMode]="'flat'" [virtual]="virtual" name="Period"
                          class="access-subfeature k-outline-none dropdown-custom-width k-input-flat period-drpd"
                          [valuePrimitive]="true" [(ngModel)]="reminder.period" [data]="periodList" textField="text"
                          valueField="value" placeholder="Select Period" (valueChange)="valicateRequest()">
                          <ng-template kendoDropDownListItemTemplate let-dataItem>
                            <span title="{{ dataItem.text }}" class="template TextTruncate">{{ dataItem.text }}</span>
                          </ng-template>
                        </kendo-dropdownlist>
                      </kendo-label>
                    </div>
                    <div class="col-1 col-sm-1 col-lg-1 col-xl-1 col-md-1 col-xs-1 pr-0 pl-4">
                      <img class="info pl6 delete-reminder" (click)="openReminderDelete(reminder, groupModel)" alt=""
                        [src]="'assets/dist/images/delete.svg'" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </kendo-expansionpanel>
        </div>

        <div class="nep-card-footer nep-card-right nep-modal-footer fixed-details-footer">
          <button kendoButton (click)="onCancelRequest()" [style.width.px]="76"
            class="custom-kendo-outline-btn-size  mr-2" fillMode="outline" themeColor="primary">
            Cancel
          </button>
          <button kendoButton (click)="onSaveRequest()" [style.width.px]="64" [disabled]="disableSaveRequest"
            class="custom-kendo-outline-btn-size" themeColor="primary">
            Save
          </button>
        </div>
      </mat-tab-nav-panel>
    </div>
  </div>
</div>
<app-loader-component *ngIf="isLoader"></app-loader-component>
<kendo-dialog *ngIf="deleteReminder" [actions]="myActions" [actionsLayout]="deletePopupLayout"
  (action)="onReminderDeleteAction($event)" title="Changes Found" (close)="closeReminderDelete('cancel')">
  <div class="action-layout-config">
    <p class="action-body">You are about to delete this reminder. Are you sure you <br> want to perform this action?</p>
  </div>
</kendo-dialog>
<kendo-dialog *ngIf="deleteGroup" [actions]="myActions" [actionsLayout]="deletePopupLayout"
  (action)="onGroupDeleteAction($event)" title="Changes Found" (close)="closeGroupDelete('cancel')">
  <div class="action-layout-config">
    <p class="action-body">You are about to delete this group. Are you sure you <br> want to perform this action?</p>
  </div>
</kendo-dialog>
<kendo-dialog *ngIf="cancelRequest" [actions]="myActions" [actionsLayout]="deletePopupLayout"
  (action)="onCancelRequestAction($event)" title="Changes Found" (close)="closeCancelRequest('cancel')">
  <div class="action-layout-config">
    <p class="action-body">You have unsaved changes. Do you wish to proceed?</p>
  </div>
</kendo-dialog>