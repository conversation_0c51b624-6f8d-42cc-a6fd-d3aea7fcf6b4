import { Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';
import { DecimalPipe } from '@angular/common';
import { M_Datatypes, NumberDecimalConst } from '../common/constants';

@Pipe({
  name: 'formatFieldValue'
})
export class FormatFieldValuePipe implements PipeTransform {
  constructor(private datePipe: DatePipe, private decimalPipe: DecimalPipe) {}

  transform(value: any, fieldName: string, dataTypeId: number): string {
    if (value === null || value === undefined || value === 'NA') {
      return 'NA';
    }

    // Handle specific field names first
    switch (fieldName) {
      case 'RealizedValue':
      case 'UnrealizedValue':
      case 'TotalValue':
      case 'InvestmentCost':
        return this.decimalPipe.transform(value, NumberDecimalConst.currencyDecimal) || 'NA';
      
      case 'Dpi':
      case 'Rvpi':
      case 'GrossMultiple':
        return `${this.decimalPipe.transform(value, NumberDecimalConst.multipleDecimal) || 'NA'}x`;
      
      case 'GrossIRR':
        return `${this.decimalPipe.transform(value, NumberDecimalConst.percentDecimal) || 'NA'}%`;
    }

    // Handle by data type if not handled by specific field name
    switch (dataTypeId) {
      case M_Datatypes.Date:
        return this.datePipe.transform(value, 'MM/dd/yyyy') || 'NA';
      
      case M_Datatypes.CurrencyValue:
        return this.decimalPipe.transform(value, NumberDecimalConst.currencyDecimal) || 'NA';
      
      case M_Datatypes.Multiple:
        return `${this.decimalPipe.transform(value, NumberDecimalConst.multipleDecimal) || 'NA'}x`;
      
      case M_Datatypes.Percentage:
        return `${this.decimalPipe.transform(value, NumberDecimalConst.percentDecimal) || 'NA'}%`;
      
      case M_Datatypes.Number:
        return this.decimalPipe.transform(value, NumberDecimalConst.noDecimal) || 'NA';
      
      default:
        return value || 'NA';
    }
  }
} 