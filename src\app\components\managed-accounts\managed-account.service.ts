import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError, Subject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { ManagedAccount } from './managed-account.model';

@Injectable({
  providedIn: 'root'
})
export class ManagedAccountService {
  private myAppUrl: string;
  private goToStepSubject = new Subject<number>();
  goToStep$ = this.goToStepSubject.asObservable();

  constructor(private http: HttpClient) {
    this.myAppUrl = environment.apiBaseUrl;
  }

  emitGoToStep(step: number): void {
    this.goToStepSubject.next(step);
  }

  saveManagedAccount(managedAccount: ManagedAccount): Observable<any> {
    return this.http
      .post<any>(`${this.myAppUrl}api/v1/ManagedAccount/save`, managedAccount)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  getManagedAccountById(id: number): Observable<any> {
    return this.http
      .get<any>(`${this.myAppUrl}api/v1/ManagedAccount/${id}`)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  getAllManagedAccounts(): Observable<any> {
    return this.http
      .get<any>(`${this.myAppUrl}api/v1/ManagedAccount/all`)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  updateManagedAccount(managedAccount: ManagedAccount): Observable<any> {
    return this.http
      .put<any>(`${this.myAppUrl}api/v1/ManagedAccount/update`, managedAccount)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  deleteManagedAccount(id: number): Observable<any> {
    return this.http
      .delete<any>(`${this.myAppUrl}api/v1/ManagedAccount/${id}`)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  errorHandler(error: any) {
    return throwError(error);
  }
}
