import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';
import { ViewCompanyDetailsComponent } from './view-company-details.component';
import { CloService } from '../../../services/clo.service';
import { InvestCompanyService } from '../investmentcompany/investmentcompany.service';
import { ToastrService } from 'ngx-toastr';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { PanelbarItemService } from '../shared/panelbar-item/panelbar-item.service';
import { CommonSubFeaturePermissionService } from '../../../services/subPermission.service';
import { BreadcrumbService } from '../../../services/breadcrumb-service.service';
import { DomSanitizer } from '@angular/platform-browser';


describe('ViewCompanyDetailsComponent', () => {
  let component: ViewCompanyDetailsComponent;
  let fixture: ComponentFixture<ViewCompanyDetailsComponent>;
  let mockCloService: jasmine.SpyObj<CloService>;
  let mockInvestCompanyService: jasmine.SpyObj<InvestCompanyService>;
  let router: Router;
  let pagePanelService: jasmine.SpyObj<PanelbarItemService>; 

  beforeEach(async () => {
    mockCloService = jasmine.createSpyObj('CloService', ['currentData', 'currentStep', 'changeData']);
    mockCloService.currentData = of(null);
    mockCloService.currentStep = of(null);
    mockInvestCompanyService = jasmine.createSpyObj('InvestCompanyService', ['filterInvestCompany', 'getInvestCompanyById', 'getTabList']);
    mockInvestCompanyService.getInvestCompanyById.and.returnValue(of({ id: 1, companyName: 'Test Company' }));
    mockInvestCompanyService.getTabList.and.returnValue(of([]));
    pagePanelService = jasmine.createSpyObj('PanelbarItemService', ['checkTablePermissions', 'updateTableVisibility']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      declarations: [ViewCompanyDetailsComponent],
      imports: [RouterTestingModule,HttpClientTestingModule],
      providers: [
        { provide: 'BASE_URL', useValue: 'http://localhost:4200' },
        { provide: CloService, useValue: mockCloService },
        { provide: InvestCompanyService, useValue: mockInvestCompanyService },
         { provide: ToastrService, useValue: { success: jasmine.createSpy(), error: jasmine.createSpy(), warning: jasmine.createSpy() } },
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({ get: (key: string) => '1' })
          }
        },
        { provide: PanelbarItemService, useValue: pagePanelService },
        { provide: Router, useValue: routerSpy },
        { provide: CommonSubFeaturePermissionService, useValue: jasmine.createSpyObj('CommonSubFeaturePermissionService', ['getSubFeaturePermissions']) },
        { provide: BreadcrumbService, useValue: jasmine.createSpyObj('BreadcrumbService', ['setBreadcrumbs']) },
        { provide: DomSanitizer, useValue: jasmine.createSpyObj('DomSanitizer', ['sanitize', 'bypassSecurityTrustHtml']) }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ViewCompanyDetailsComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    // Prevent ngOnInit from running automatically to avoid subscription issues
    spyOn(component, 'ngOnInit').and.callFake(() => {});
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  
    it('should set selectedTab to the provided tab', () => {
      const tab = 1;
      component.highlightTab(tab);
      expect(component.selectedTab).toBe(tab);
    });
    it('should call checkTablePermissions with correct parameters', () => {
      const tableId = 1;
      const permissionType = 'CAN_VIEW';
      component.permissions = [{ subFeatureId: 1, CAN_VIEW: true }];
  
      pagePanelService.checkTablePermissions.and.returnValue(true);
  
      const result = component.checkTablePermissions(tableId, permissionType);
  
      expect(pagePanelService.checkTablePermissions).toHaveBeenCalledWith(tableId, component.permissions, permissionType);
      expect(result).toBe(true);
    });
  
    it('should call checkTablePermissions with default permissionType when not provided', () => {
      const tableId = 1;
      component.permissions = [{ subFeatureId: 1, CAN_VIEW: true }];
  
      pagePanelService.checkTablePermissions.and.returnValue(true);
  
      const result = component.checkTablePermissions(tableId);
  
      expect(pagePanelService.checkTablePermissions).toHaveBeenCalledWith(tableId, component.permissions, null);
      expect(result).toBe(true);
    });
    
});