@import "../../../../variables.scss";

// General Styles
.kpi-name {
  color: $primary-color;
}

.input-color {
  color: $secondary-color-dg;
}

.label-color {
  color: $tertiary-color-lg;
}

.ta-in-padding::placeholder {
  color: $placeholder-color;
}

.cell-color {
  background-color: $background-cell-color;
}

.black-color{
  color:$content-value-color;
}

.input-border::placeholder {
  color: $placeholder-color;
  opacity: 1;
}

.left-icon {
  color: $primary-color;
  cursor: pointer;
}

.progress-bar-style {
  background-color: $primary-color;
  height: $progress-bar-height;
}

.uploading {
  background-color: $uploading-bg-color;
  border-radius: $border-radius;
}

// Margin and Padding Utilities
:host ::ng-deep .nep-card-body {
  padding: $padding-medium $nep-body-line-height;
}

.cell-padding {
  padding: $padding-small $padding-large !important;
}

.ta-in-padding {
  padding: $padding-small $padding-large !important;
}

.ta-padding {
  padding: $padding-medium $padding-medium $padding-medium 0px !important;
}

.plr-25{
  padding-right: $progressbar-right-padding;
}

.ptb-8 {
  padding-right: $padding-medium;
}

.mb-8 {
  margin-bottom: $margin-small;  
}

.mt-11 {
  margin-top: $margin-large;
}

.m-10 {
  margin: $margin-medium;
}

.ptb-1 {
  padding: 1px 0px;
}

.mtb-10 {
  margin: $margin-medium 0px;
}

.mb-16 {
  margin-bottom: 1rem;
}

.mt-label-4 {
  margin-top: $margin-top-label;
}

.mlt-45{
  margin-left: $file-margin-tp;
  margin-right: $file-margin-tp;
}

.input-border {
  border: $border-neutral-grey;
  padding: 0.375rem 1rem !important;
  margin-top: 0.25rem !important;
}

.border-rad {
  border-radius: $border-radius;
  border: $border-neutral-grey !important;
}

.doc-bg {
  background-color: $uploading-bg-color;
  border-radius: $border-radius;
}

.textarea-border {
  border: $border-neutral-grey !important;
}

.div-1 {
  border-bottom: $border-neutral-grey;
  border-right: $border-neutral-grey;
}

.div-2 {
  border-bottom: $border-neutral-grey;
}

.btn-bg {
  background-color: $bg-color-white;
  border: $border-primary;
}

.invalid-file {
  outline: 1px solid $invalid-file-color;
}

.w-file-name{
  width: $file-name-width;
}

.current-value {
  background-color: $bg-color-white;
  border-radius: $current-value-border-radius;
  padding: $current-value-padding; 
  text-align: center;
  width: auto; 
  height: $current-value-height; 
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-small;
  font-weight: 500;
  line-height: $current-value-line-height;
  letter-spacing: 0.5px;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #4061c7;
}

.vector-icon {
  width: $vector-icon-height;
  height: $vector-icon-width;
  top: 1px;
  left: 1px;
  gap: 0px;
  opacity: 0px;
  color: black;
}

.file-name-font{
  font-size: $font-size-medium;
  font-weight: 500;
}

.file-size-font{
  font-size: $font-size-medium;
  font-weight: 400;
}

.clo-word-wrap{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}





