<div class="row performance-section mr-0 ml-0" *ngIf="isEnableView">
    <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 outer-section pl-0 pr-0">
        <div class="content-bg">
            <div class="portfolio-company-table">
                <div class="filter-bg border-bottom">
                    <div class="row mr-0 ml-0">
                        <div class=" col-lg-12 col-md-12 col-sm-12 pl-0 pr-0">
                            <div class="pull-right headerfontsize">
                                <div class="d-inline-block search">
                                    <span class="fa fa-search fasearchicon"></span>
                                    <input #gb pInputText type="text" [appApplyFilter]="{ data: fxrateslistResultsClone, columns: fxrateslistColumns,IsFreezeColumn:true,freezeColumns:'fromCurrencyCode,toCurrencyCode'}"
                                    (filtered)="fxrateslistResults = $event"
                                        class="search-text-company search-text-fxrates TextTruncate"
                                        placeholder="Search">
                                </div>
                                <div class="d-inline-block pr-1">
                                    <div class="d-inline-block table-pref TextTruncate" title="Logs">Logs</div>
                                    <div class="d-inline-block pr-2 pl-1"
                                        title="Switch to view cell based audit trails">
                                        <kendo-switch size="small" [onLabel]="' '" [offLabel]="' '"></kendo-switch>
                                    </div>
                                </div>
                                <div class="d-inline textsplit">
                                </div>
                                <div class="d-inline-block cloud_download" id="div-download-fxrates">
                                    <div class="d-inline-block pr-2">
                                        <img
                                            id="btn-download-fxrates"
                                            src="assets/dist/images/Cloud-download.svg" class="cursor-filter"
                                            title="Export KPI (Excel file)" alt="" /><span class="excel-load">

                                        </span>
                                    </div>
                                    <div class="d-inline textsplit">
                                    </div>

                                    <div class="d-inline-block pl-2 pr-1"><img id="dropdownMenuButton"
                                            src="assets/dist/images/ConfigurationWhite.svg" class="cursor-filter"
                                            alt="" />
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="">
                    <kendo-grid class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-fxrates-table-grid" 
                    [kendoGridBinding]="fxrateslistResults" scrollable="virtual" [rowHeight]="44" [resizable]="true" [sortable]="true">
                        <kendo-grid-column [sticky]="true" [minResizableWidth]="200" [maxResizableWidth]="800"  [width]="200" *ngFor="let col of frozenCols;" [field]="col.field">
                            <ng-template kendoGridHeaderTemplate>
                                <div class="header-icon-wrapper wd-100" >
                                  <span class="TextTruncate S-M">
                                   {{col.header}}
                                  </span>
                                </div>
                              </ng-template>
                            <ng-template kendoGridCellTemplate let-rowData>
                                <div class="content" >
                                    <span>{{rowData[col.field]}}</span>
                                </div>
                            </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column [minResizableWidth]="200" *ngFor="let col of fxrateslistColumns; index as i" [maxResizableWidth]="800" 
                        [width]="200" title="{{col.header}}" [field]="col.header">
                        <ng-template kendoGridHeaderTemplate>
                            <div class="header-icon-wrapper wd-100">
                              <span class="TextTruncate S-M">{{col.header}}</span>
                            </div>
                          </ng-template>
                        <ng-template kendoGridCellTemplate let-rowData>            
                            <span tabindex="0" class="prtcmny-det-o">
                                <span class="content">
                                    <span title="{{rowData[col.field] | minusSignToBrackets|formatNumbers}}">{{rowData[col.field]|minusSignToBrackets|formatNumbers}}</span>
                                </span>
                            </span>
                        </ng-template>
                    </kendo-grid-column>
                    <ng-template kendoGridNoRecordsTemplate>
                        <app-empty-state class="finacials-beta-empty-state" [imageHeight]="'calc(100vh - 210px)'"  [isGraphImage]="false"></app-empty-state>
                    </ng-template>
                    </kendo-grid>
                </div>
            </div>
        </div>
    </div>
</div>