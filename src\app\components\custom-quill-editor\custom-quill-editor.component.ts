import { Component, ChangeDetectorRef, forwardRef, Input, ViewChild, Output, EventEmitter, OnInit, SimpleChanges } from '@angular/core';
import Quill from 'quill';
import {
  ControlValueAccessor,
  NG_VALUE_ACCESSOR,
} from "@angular/forms";
import { ListExt } from './extension';
Quill.register(ListExt, true);

const FontAttributor = Quill.import('attributors/class/font');
FontAttributor.whitelist = [
  'Helvetica',
  'HelveticaMedium',
  'Arial',
  'TimesNewRoman',
  'Garamond',
  'PalatinoLinotype',
  'monospace',
  'sans-serif',
  'serif',
  'Georgia',
  'Cambria',
  'Calibri',
  'Verdana',
  'Corbel',
  'FranklinGothic'
];
Quill.register(FontAttributor, true);
const FontSize = Quill.import('attributors/class/size');
FontSize.whitelist = [
  '9px',
  '8px',
  '10px',
  '12px',
  '14px',
  '16px',
  '18px',
  '20px',
  '22px',
  '24px',
  '26px',
  '28px',
  '30px',
  '32px'
];
Quill.register(FontSize, true);

@Component({
  selector: 'app-custom-quill-editor',
  templateUrl: './custom-quill-editor.component.html',
  styleUrls: ['./custom-quill-editor.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CustomQuillEditorComponent),
      multi: true
    }
  ]
})
export class CustomQuillEditorComponent implements ControlValueAccessor, OnInit {
  @Input() noteText: string = "";
  value: string = '';
  @Input() editorPlaceholder: string = '';
  @Input() showCharCount: boolean = false;
  @Input() commentIndex: number;
  @Output() onEditorValueChange: EventEmitter<any> = new EventEmitter();
  @Output() ngModelChange = new EventEmitter<string>();
  maxCharCount: number = 6000;
  @Input() charCount: number = 0;
  @Output() onKeydownChange: EventEmitter<any> = new EventEmitter();
  @Output() commentIndextrigger: EventEmitter<number> = new EventEmitter();
  noteModel: any = null;
  
  @ViewChild('editor', { static: true }) editor;

  private _readOnly: boolean = false;
  @Input()
  set readOnly(value: boolean) {
    this._readOnly = value;
    this.updateQuillConfig();
  }
  get readOnly(): boolean {
    return this._readOnly;
  }

  quillConfig: any;
  @Input() useDivAsBlockTag: boolean = false;

  constructor(protected changeDetectorRef: ChangeDetectorRef) {}

  ngOnInit() {    
  if (this.useDivAsBlockTag) {
      this.overrideBlockTag();
    }
    this.updateQuillConfig();
    if (this.noteText) {
      this.onContentChanged(this.noteText);
    } else {
      this.charCount = 0;
    }
  }
  ngOnChanges(changes: SimpleChanges) {
    if (changes.noteText?.currentValue) {
      const text = this.stripHtml(changes.noteText.currentValue);
      this.charCount = text.length;
    }
  }
  updateQuillConfig() {
    this.quillConfig = {
      toolbar: this.readOnly ? false : [
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote'],
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'header': 1 }, { 'header': 2 }],
        [{ 'color': ['red', 'black', '#4061C7', '#75787B', '#55565A', '#eee', 'green', 'orange', '#4061C7'] }, { 'background': ['red', 'black', '#4061C7', '#75787B', '#55565A', '#eee', 'green', 'orange', '#4061C7'] }],
        [{ 'align': [false, 'center', 'right', 'justify'] }],
        [{ 'indent': '-1' }, { 'indent': '+1' }],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }],
        [{ 'font': ['sans-serif', 'Helvetica', 'HelveticaMedium', 'Arial', 'TimesNewRoman', 'Garamond', 'PalatinoLinotype', 'monospace', 'sans-serif', 'serif', 'Georgia', 'Cambria', 'Calibri', 'Verdana', 'Corbel', 'FranklinGothic'] }],
        [{ 'size': ['9px', '8px', '10px', '12px', '14px', '16px', '18px', '20px', '22px', '24px', '26px', '28px', '30px', '32px'] }],
        ['link']
      ]
    };
  }

  private _onChange = (_: any) => { };
  private _onTouched = () => { };

  writeValue(obj: any): void {
    this.value = obj;
  }
  registerOnChange(fn: any): void {
    this._onChange = fn;
  }
  registerOnTouched(fn: any): void {
    this._onTouched = fn;
  }

  onContentChanged = (event) => {
    let htmlContent = event?.html || event || '';
    let plainText = event || '';

    // Calculate character count from HTML content
    this.charCount = htmlContent ? this.stripHtml(htmlContent).replace(/\s+/g, ' ').trim().length : 0;

    if (plainText.length > this.maxCharCount) {
      plainText = plainText.substring(0, this.maxCharCount);
      this.value = plainText;
    } else {
      this.value = plainText;
    }
    
    this._onChange(this.value);
      this.ngModelChange.emit(this.value);
      this.onEditorValueChange.emit(event);
      this.commentIndextrigger.emit(this.commentIndex);
    }
    onKeydown(event: KeyboardEvent) {
        event["commentIndex"] = this.commentIndex;
        this.onKeydownChange.emit(event);
    }

  stripHtml(html: string): string {
    if (!html) return '';
    const div = document.createElement('div');
    div.innerHTML = html;
    const text = div.textContent || div.innerText || '';
    return text;
  }

  onFocus = () => {
  }
  onBlur = () => {
  }
  onSelectionChanged = (event) => {
    if (event.oldRange == null) {
      this.onFocus();
    }
    if (event.range == null) {
      this.onBlur();
    }
  }
  overrideBlockTag() {
    const Block = Quill.import('blots/block');
    if (Block.tagName !== 'div') {
      Block.tagName = 'div';
      Quill.register(Block, true);
    }
  }

}