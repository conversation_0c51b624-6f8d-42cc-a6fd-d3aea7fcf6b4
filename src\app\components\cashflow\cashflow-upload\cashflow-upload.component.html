<div class="nep-modal nep-modal-show nepshow-d-bg">
    <div class="nep-modal-mask"></div>
    <div class="nep-card nep-card-shadow nep-modal-panel nep-modal-default nepshadow-po-d">
        <div class="nep-card-header nep-modal-title">
            <div class="row mr-0 ml-0 ">
                <div class="col-md-12 user-header pr-0 pl-0">
                    <div class="float-left TextTruncate M-M pt-1" title="Download excel template and bulk upload cashflow data">Download excel template and bulk upload cashflow data</div>
                    <div class="float-right close-icon" (click)="onClose()">
                        <i class="pi pi-times"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="nep-card-body">
            <div class="row mr-0 ml-0 ">
                <div class="col-md-12 user-body user-upload">
                    <div class="add-user-component">
                        <div class="card card-main">
                            <div class="card-body mb-0">
                                <div class="col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12 user-upload pr-0 pl-0">
                                    <div class="row mr-0 ml-0 pb-3">
                                        <div class="col-md-6 col-sm-6 col-lg-6 col-xs-6 col-xl-6 pr-0 pl-0">
                                            <div class="row mr-0 ml-0">
                                                <div class="col-12 pr-0 pl-0">
                                                    <label for="fund" class="TextTruncate" title="Fund">Fund</label>
                                                </div>
                                                <div class="col-12 pr-0 pl-0">
                                                    <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="selectedFund" #fund="ngModel"
                                                        [fillMode]="'flat'" [filterable]="true" name="fund" [virtual]="virtual"
                                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                                        [data]="fundList"  textField="fundName" valueField="fundID" [placeholder]="'Select fund'"
                                                        (valueChange)="onFundChange($event)" required>
                                                    </kendo-combobox>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6 col-lg-6 col-xs-6 col-xl-6 pr-0 pl-0">
                                            <div class="row mr-0 ml-0">
                                                <div class="col-12 pr-0 pl-0">
                                                </div>
                                                <div class="col-12 pr-0 pl-0 download-icon" *ngIf="isPageConfig!=''">
                                                    <div id="download-fund-excel" class="download-fund-excel float-right" (click)="DownloadTemplate()">
                                                        <img src="assets/dist/images/cashflow-download.svg" class="showHandIcon pr-1" /> Template
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mr-0 ml-0">
                                        <div class="col-md-12 pr-0 pl-0 upload-desc desc-header TextTruncate" title="Note:-">
                                            Note:-
                                        </div>
                                    </div>
                                    <div class="row mr-0 ml-0">
                                        <div class="col-md-12 pr-0 pl-0 upload-desc TextTruncate" title="1. Template includes sample data. Replace the same with relevant data.">
                                            1. Template includes sample data. Replace the same with relevant data.
                                        </div>
                                    </div>
                                    <div class="row mr-0 ml-0">
                                        <div class="col-md-12 pr-0 pl-0 upload-desc TextTruncate" title="2. Follow the format laid out in the template. Do not modify any headers.">
                                            2. Follow the format laid out in the template. Do not modify any headers.
                                        </div>
                                    </div>
                                    <div class="row mr-0 ml-0">
                                        <div class="col-md-12 pr-0 pl-0 upload-desc TextTruncate" title="3. File size limit is 20MB.">
                                            3. File size limit is 20MB.
                                        </div>
                                    </div>
                                    <div class="row mr-0 ml-0">
                                        <div class="col-md-12 pr-0 pl-0 upload-desc TextTruncate" title="4. Transaction Date is MMM/dd/yyyy,MM/dd/yyyy format.">
                                            4. Transaction Date is MMM/dd/yyyy,MM/dd/yyyy format.
                                        </div>
                                    </div>
                                    <div class="row mr-0 ml-0 mt-4" *ngIf="errorUploadDetails.length>0">
                                        <div class="col-md-12 pr-0 pl-0 table-responsive card-border">
                                            <kendo-grid  [kendoGridBinding]="errorUploadDetails"  scrollable="virtual" [rowHeight]="44"
                                            [resizable]="true"
                                            class="k-grid-border-right-width k-grid-border-bottom-width  custom-kendo-cab-table-grid kendo-deal-tr-grid analytics-grid">
                                            <kendo-grid-column 
                                                 title="Errors">
                                                <ng-template kendoGridHeaderTemplate>
                                                    <span class="errorColor pr-2">
                                                        <img class="error-info-icon"
                                                            [src]="'assets/dist/images/red_info_icon.svg'"
                                                            alt="">
                                                    </span><span
                                                        class="error-title errorColor pl-1">Errors</span>
                                                </ng-template>
                                                <ng-template kendoGridCellTemplate let-message>
                                                    <div title="{{message.statusDescription}}" class="row mr-0 ml-0 cell-padding TextTruncate">
                                                        <div class="col-md-1 pl-0 pr-0">
                                                            <span class="errorColor" *ngIf="message.row>0">{{message.cellCode}}</span>
                                                        </div>
                                                        <div class="col-md-11 pl-1 pl-0 custom-white-space TextTruncate">                                                                   
                                                            <span class="pl-0" title="{{message.statusDescription}}">{{message.statusDescription}}</span>
                                                        </div>
                                                    </div>
                                                </ng-template>
                                            </kendo-grid-column>
                                            </kendo-grid>    
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="nep-card-footer  nep-modal-footer user-upload">
            <div *ngIf="displayUpdateConfirmationDialog && cashflowCalculationData.length > 0">
                <div class="row mr-0 ml-0">
                    <div class="col-md-12 pr-0 pl-0  desc-header alert-message">
                        Alert
                    </div>
                </div>
                <div class="row mr-0 ml-0">
                    <div class="col-md-12 pr-0 pl-0 upload-desc">
                        Fund holding data of quarter {{currentQuarter}} for following companies will be overwritten:
                    </div>
                </div>
                <div class="row mr-0 ml-0 pb-3">
                    <div class="col-md-12 pr-0 pl-0 upload-desc error-companies" *ngFor="let item of cashflowCalculationData; index as i">
                        {{i+1}}.{{item.portfolioCompanyName}}
                    </div>
                </div>
            </div>
            <div class="float-right">
                <div class="loading-input-controls-manual" *ngIf="loading"><i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i></div>
                <div class="upload-sec d-inline-block">
                    <div class="upload-desc">
                        <div class="uploadButton d-flex" [ngStyle]="{'padding-right': browseicon ? '9px' : '6px' }">
                            <div id="browse-cashflow" class="textEllipsis uploadLogoIcon" (click)="file.click()" title={{uploadFilePlaceholder}}>
                                <input class="hidefile" #file (click)="file.value = null" value="" accept=".xlsx,.xls" (change)="onSelect($event.target.files)" type="file">
                                <img *ngIf="browseicon" class="browseIcon" [src]="'assets/dist/images/Iconmaterial-computer.svg'" alt="">
                                <span class="beatColor browseButton">{{uploadFilePlaceholder}}</span>
                            </div>
                            <div *ngIf="!browseicon" class="icon">
                                <img *ngIf="ProgressCancel" class="pull-right" (click)="deleteIconClick(filename)" [src]="'assets/dist/images/ClearIcon.svg'" alt="">
                                <i *ngIf="value == 1" aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <nep-button id="upload-cashflow" Type="Primary" class="nepbt-p" *ngIf="!displayUpdateConfirmationDialog" [disabled]="files.length==0 || selectedFund==undefined" (click)="onUpload()">
                    Upload
                </nep-button>
                <nep-button Type="Primary" class="nepbt-pl" *ngIf="displayUpdateConfirmationDialog && cashflowCalculationData.length > 0" (click)="isOverwriteHoldings=true;saveDataFlag()">
                    Update
                </nep-button>
            </div>

        </div>
    </div>
</div>
<app-loader-component *ngIf="fundsLoading"></app-loader-component>