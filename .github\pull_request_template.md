**Title:** 
*_[Feature] Brief Description of the Feature*

**Description:**
- **What:** *Provide a detailed description of the feature being added.*
- **Why:** *Provide the reason for adding this feature*


**Related Issues:**
- *Link to any related issues or tasks (e.g., `Closes #123`).*

 **Screenshots (if applicable):**
- *Include before and after screenshots to illustrate the changes.*

**Changes:**
- *List the main changes made in this merge request.*
- eg:
  - New files added
  - Existing files modified
  - Any other significant changes*

**Testing:**
- Describe the tests that were run to verify the changes.
  - *No of Unit tests added*
  - *Manual testing steps*

**Checklist:**
- [ ] Code follows the project's coding standards
- [ ] Documentation has been updated (if necessary)
- [ ] Is proper test cases added
- [ ] All tests have passed
- [ ] Related issues have been linked

**Additional Notes:**
- *Any additional information that reviewers should be aware of*
