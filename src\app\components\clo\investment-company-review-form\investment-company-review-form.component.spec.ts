import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormGroup, FormControl } from '@angular/forms';
import { InvestmentCompanyReviewFormComponent } from './investment-company-review-form.component';
import { InvestCompanyService } from '../investmentcompany/investmentcompany.service';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('InvestmentCompanyReviewFormComponent', () => {
  let component: InvestmentCompanyReviewFormComponent;
  let fixture: ComponentFixture<InvestmentCompanyReviewFormComponent>;
  let investCompanyService: jasmine.SpyObj<InvestCompanyService>;

  beforeEach(() => {
    const investCompanyServiceSpy = jasmine.createSpyObj('InvestCompanyService', ['emitGoToStep']);

    TestBed.configureTestingModule({
      declarations: [InvestmentCompanyReviewFormComponent],
      providers: [{ provide: InvestCompanyService, useValue: investCompanyServiceSpy }]
    });

    fixture = TestBed.createComponent(InvestmentCompanyReviewFormComponent);
    component = fixture.componentInstance;
    investCompanyService = TestBed.inject(InvestCompanyService) as jasmine.SpyObj<InvestCompanyService>;
    component.InvestmentCompanyModel = { companyName: 'Test Company' };
    fixture.detectChanges();
  });

  it('should create', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should initialize formattedInvestmentCompanyModel on ngOnInit', () => {
    component.InvestmentCompanyModel = { companyName: 'Test Company' };
    component.ngOnInit();
    expect(component.formattedInvestmentCompanyModel).toEqual({ companyName: 'Test Company' });
  });

  it('should call emitGoToStep with correct parameter', () => {
    const step = 2;
    component.triggerGoToStep(step);
    expect(investCompanyService.emitGoToStep).toHaveBeenCalledWith(step);
  });

});
