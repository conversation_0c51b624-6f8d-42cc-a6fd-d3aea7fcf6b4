﻿import { ChangeDetectorRef, Component, ElementRef, OnInit } from '@angular/core';
import { LazyLoadEvent } from 'primeng/api';
import { AccountService } from '../../services/account.service';
import { FirmService } from '../../services/firm.service';
import { MiscellaneousService } from '../../services/miscellaneous.service';
import { FeaturesEnum } from '../../services/permission.service';
import { Observable, of } from 'rxjs';
import { GridDataResult } from '@progress/kendo-angular-grid';
import { SortDescriptor, State } from "@progress/kendo-data-query";
import { KendoService } from 'src/app/services/kendo.service';
@Component({
    selector: 'firm',
    templateUrl: './firm-list.component.html',
	styleUrls: ["./firm-list.component.scss"]
})

export class FirmListComponent implements OnInit{
	isLoader: boolean = false;
    feature: typeof FeaturesEnum = FeaturesEnum;
    public firms: any=[];
    pagerLength: any;
    dataTable: any;
    blockedTable: boolean = false;
	totalRecords: number;
	totalPage: number;
	globalFilter: string = "";
	paginationFilterClone: any = {};
	public view: Observable<GridDataResult>;
	public state: State = {
	  skip: 0,
	  take: 100,
	};
	sort: SortDescriptor[] = [];
    constructor(private miscService: MiscellaneousService,private elementRef:ElementRef,private accountService: AccountService, private firmService: FirmService, protected changeDetectorRef: ChangeDetectorRef,private kendoService:KendoService) {
        this.pagerLength = this.miscService.getPagerLength();
		localStorage.setItem("headerName", '');  
    }
	ngOnInit(): void {
		this.getFirmList(null);
	}
	getFirmList(event: any) {
		this.isLoader = true;
		if (event == null) {
			event = { first: 0, rows: 100, globalFilter: null, sortField: null, sortOrder: 1 };
        }
        if (event.multiSortMeta == undefined) {
            event.multiSortMeta = [{ field: "FirmName", order: 1 }];
            event.sortField = "FirmName";
        }
		this.firmService.getFirmList({ paginationFilter: event }).subscribe({next:result => {

            let resp = result["body"] ;
			if (resp != null && result.code == "OK") {
				this.firms = resp.firmList;
				this.totalRecords = resp.totalRecords;
				if(this.totalRecords > 100){
					this.totalPage = Math.ceil(this.totalRecords / event.rows);
				}else{
					this.totalPage = 1;
				}
			}
			else {
				this.firms = [];
				this.totalRecords = 0;
			}
			this.blockedTable = false;
			this.view = of<GridDataResult>({
				data: this.firms,
				total: this.totalRecords,
			  });
			  this.isLoader = false;
        }, error:error => {
            this.blockedTable = false;
			this.isLoader = false;
        }});
	}

	exportFirmList() {
		let event = JSON.parse(JSON.stringify(this.paginationFilterClone));
		event.globalFilter = this.globalFilter;
		event.filterWithoutPaging = true;
		this.firmService.exportFirmList({ paginationFilter: event }).subscribe(response => this.miscService.downloadExcelFile(response));
	}
	setHeaderName(firmName: any) {
        localStorage.setItem("headerName", firmName);    
    }
	  searchLoadPCLazy() {
		let params: any = {
			first: 0,
			rows: 100,
			globalFilter: this.globalFilter != "" ? this.globalFilter : null,
			sortField: null,
			sortOrder: 1,
		  };
	  
		  if (this.firms.length != 0) {
			let result = this.kendoService.getHeaderValue(
			  params,
			  null,
			  [{sortFieldName:'FirmName'},{sortFieldName:'Website'}],
			  this.sort
			);
			params = result.params;
			this.sort = result.parentSort;
		  }
		  this.getFirmList(params);
	}
dataStateChange($event)
{
    this.state.skip = $event.skip;
    this.state.take = $event.take;
    let params:any = {
      first: $event.skip,
      rows: $event.take,
      globalFilter: this.globalFilter != "" ? this.globalFilter : null,
      sortField: null,
      sortOrder: 1,
    }
   let headers= this.getHeaders();
   let result = this.kendoService.getHeaderValue(params, $event, headers, this.sort);
   params = result.params;
    this.sort = result.parentSort;
  this.getFirmList(params);
}
getHeaders()
{
	return [{sortFieldName:'FirmName'},{sortFieldName:'Website'},{sortFieldName:'headQuarter'}];
}
}

