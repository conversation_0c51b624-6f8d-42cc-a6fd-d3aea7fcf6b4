
<div class="portfolio-detail-component" (resized)="onResized($event)">
    <div class="row mr-0 ml-0">
        <div class="col-lg-12 col-sm-12 pl-0 pr-0">
            <div class="chart-area section1-height chart-section-height">
                <div class="row mr-0 ml-0">
                    <div class="col-4 col-lg-4 col-sm-4 col-md-4 col-xl-4 p-3" [hideIfUserUnAuthorized]='{subFeatureId:subFeature.InvestmentKPIs,action:actions[actions.canView],id:id}'>
                <kendo-combobox
                    [data]="ddlmodelList.investmentKPIList" #investmentkpi="ngModel" textField="displayName" [fillMode]="'solid'" valueField="kpiid" name="investmentkpi" 
                    [(ngModel)]="ddlmodelList.selectedInvestmentKPI" (valueChange)="OnInvestmentKPIChange()" [filterable]="true"  [size]="'medium'"  [kendoDropDownFilter]="filterSettings"
                    class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [clearButton]="false"
                >
                <ng-template kendoComboBoxItemTemplate let-object>
                    <div tooltipPosition="top" tooltipStyleClass="bg-tooltip-report"
                        [pTooltip]="object?.parentkpi"
                        class="custom-ui-label custom-delete-hover custom-zindex-parent">
                        <span title="{{object?.displayName}}" class="img-pad TextTruncate">
                            {{object.displayName}}
                        </span>
                    </div>
                </ng-template>
                </kendo-combobox>
                    </div>
                    <div class="col-8"></div>
                </div>
                <div class="chart-bg">
                    <div class="row mr-0 ml-0" *ngIf="InvestmentKPIChartData==null || InvestmentKPIChartData.length==0">
                        <div class="col-sm-12 pl-0 pr-0" [hideIfUserUnAuthorized]='{subFeatureId:subFeature.InvestmentKPIs,action:actions[actions.canView],id:id}'>
                            <div class="text-info mt-3">
                                <app-empty-state  [isGraphImage] ="true"></app-empty-state>  
                            </div>
                            <br>
                        </div>
                    </div>
                    <!----Chart space---->
                    <div class="row mr-0 ml-0 pt-3" [hideIfUserUnAuthorized]='{subFeatureId:subFeature.InvestmentKPIs,action:actions[actions.canView],id:id}' *ngIf="InvestmentKPIChartData!=null && InvestmentKPIChartData.length>0">
                        <div class="col-sm-12 pl-0 pr-0">
                            <app-lineBar-chart [isDisplay]="width" [data]="InvestmentKPIChartData" 
                            [xField]="InvestmentKPIChartCol[1]" 
                            [yBarFields]="['Actual Value']" [yLineFields]="this.ddlmodelList.selectedInvestmentKPI['kpiInfo']!= '%' && this.ddlmodelList.selectedInvestmentKPI['kpiInfo']!= 'x'? ['% Change In Actual Value']:[]" 
                            [unit]="investKpiModuleCurrency">
                            </app-lineBar-chart>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<app-loader-component *ngIf="loading"></app-loader-component>