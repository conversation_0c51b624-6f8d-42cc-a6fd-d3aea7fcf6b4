<div *ngIf="data.length > 0">
<div class="Heading2-R text-black mb-3">Column Data Record</div>
<div *ngFor="let item of data; let i = index">
    <kendo-expansionpanel [title]="null" [subtitle]="null" [svgCollapseIcon]="arrowUpIcon" class="manage-tracker-records-panel mb-3" (action)="onAction($event, i)">
        <!-- Panel Header Start -->
        <ng-template kendoExpansionPanelTitleDirective>
            <div class="d-flex justify-content-between align-items-center w-100">
            <div>
                <span class="Caption-R mr-2">Column Type</span>
                <span class="Caption-R data-pill">{{item.fieldType}}</span>
            </div>
            <div>
                <app-kendo-button *ngIf="showEditDeleteButton && expandedPanel === i" type="Secondary" imageUrl="assets/dist/images/fiEditPrimary.svg"></app-kendo-button>
                <app-kendo-button *ngIf="showEditDeleteButton && expandedPanel === i" type="Secondary" imageUrl="assets/dist/images/fiTrashPrimary.svg" class="mx-3" (click)="onDeleteClick(i)" (keydown.enter)="onDeleteClick(i)"></app-kendo-button>
                <span *ngIf="item.fieldType !== 'Data'">
                <span class="Body-M">
                    Active
                </span>
                <kendo-switch [(ngModel)]="item.isEnable" [onLabel]="' '" [offLabel]="' '"
                    (valueChange)="onToggleChange($event, item)" (click)="onToggleClick($event, item)">
                </kendo-switch>
                </span>
            </div>
            </div>
        </ng-template>
        <!-- Panel Header End -->
        <!-- Panel Body Start -->
        <div class="row px-3">
            <div class="col-12 d-flex px-1">
                <div class="flex-fill">
                <div class="Caption-R text-muted mb-1">Name Pattern</div>
                <div class="Body-M">{{item.namePattern}}</div>
                </div>
                <div class="flex-fill" *ngIf="item.mapTo">
                <div class="Caption-R text-muted mb-1">Map To</div>
                <div class="Body-M">{{item.mapTo}}</div>
                </div>
                <div class="flex-fill">
                <div class="Caption-R text-muted mb-1">Data Type</div>
                <div class="Body-M">{{item.dataType}}</div>
                </div>
                <div class="flex-fill" *ngIf="item.dropdownList && item.dropdownList.length > 0">
                    <div class="Caption-R text-muted mb-1">Dropdown</div>
                    <div class="d-flex">
                        <div *ngFor="let dataItem of item.dropdownList.slice(0, showDropdownValues)">
                            <span title="{{dataItem}}" class="Caption-R data-pill px-2 py-1 mr-2">
                                {{dataItem.length > dropdownValueMaxLength ? dataItem.substring(0, dropdownValueMaxLength) + '...' : dataItem}}
                            </span>
                        </div>
                        <div *ngIf="item.dropdownList.length > showDropdownValues">
                            <span class="Caption-R data-pill px-2 py-1 cursor-pointer" kendoPopoverAnchor [popover]="myPopover" themeColor="primary">
                                +{{ item.dropdownList.length - showDropdownValues }}
                            </span>
                            <kendo-popover #myPopover [width]="250" position="left">
                                <ng-template kendoPopoverTitleTemplate>
                                    <div class="doc-tooltip-item py-3" *ngFor="let dataItem of item.dropdownList.slice(showDropdownValues)">
                                        {{ dataItem }}
                                    </div>
                                </ng-template>
                            </kendo-popover>
                        </div>
                    </div>
                </div>
                <div class="flex-fill">
                <div class="Caption-R text-muted mb-1">Created Date</div>
                <div class="Body-M">{{item.createdDate}}</div>
                </div>
            </div>
        </div>
        <!-- Panel Body End -->
    </kendo-expansionpanel>
</div>
</div>
<div *ngIf="confirmDelete" id="confirm-modal">
    <confirm-modal
        customwidth="500px"
        isCustomFooterClass="true"
        primaryButtonIsDanger="true"
        primaryButtonName="Yes, Delete"
        secondaryButtonName="No, keep it" 
        (primaryButtonEvent)="deleteColumnRecord()"
        modalTitle="{{ modalTitle }}"
        (secondaryButtonEvent)="cancelDelete()"
        isDeleteConfirmModal="true"
        isCloseEnable="true"
        (closeIconClick)="cancelDelete()"
    >
        <div class="container px-2">
            <div class="d-flex">
                <div class="mr-13">
                <img src="assets/dist/images/exclamation-circle-delete.svg" alt="Exclamation Circle" />
                </div>
                <div>
                <div class="Heading2-M mb-1">Are You Sure ?</div>
                <div class="Caption-R content-secondary">Deleting the Column record will permanently delete data !.</div>
                </div>
            </div>
        </div>
    </confirm-modal>
</div>



