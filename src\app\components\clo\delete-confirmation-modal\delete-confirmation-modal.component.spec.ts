import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DeleteConfirmationModalComponent } from './delete-confirmation-modal.component';
import { ConfirmModalComponent } from 'projects/ng-neptune/src/lib/confirm-modal-component/confirm-modal.component';
import { ToastrService } from 'ngx-toastr';
import { Button } from 'protractor';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';

describe('DeleteConfirmationModalComponent', () => {
  let component: DeleteConfirmationModalComponent;
  let fixture: ComponentFixture<DeleteConfirmationModalComponent>;
  let mockToastrService: ToastrService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DeleteConfirmationModalComponent,ConfirmModalComponent],
      imports:[SharedComponentModule],
      providers:[{ provide: ToastrService, useValue: { success: jasmine.createSpy(), error: jasmine.createSpy(), warning: jasmine.createSpy() } }]
    });
    fixture = TestBed.createComponent(DeleteConfirmationModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
