import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FlatTableComponent } from './flat-table/flat-table.component';
import { EditCellDialogComponent } from './flat-table/edit-cell-dialog.component';
import { CloCommentriesComponent } from './clo-commentries/clo-commentries.component';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { GridModule } from '@progress/kendo-angular-grid';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { QuillModule } from 'ngx-quill';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import {  PanelbarItemComponent1 } from './shared/panelbar-item/panelbar-item.component';
import { CdkDrag, CdkDragHandle, CdkDropList } from '@angular/cdk/drag-drop';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
// import { MustMatchDirective } from 'src/app/pipes/MustMatch-directive';

@NgModule({
  declarations: [
    FlatTableComponent,
    EditCellDialogComponent,
    CloCommentriesComponent,
    PanelbarItemComponent1,
    // MustMatchDirective
    
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MaterialModule,
    PrimeNgModule,
    KendoModule,
    GridModule,
    NgbModule,
    QuillModule,
     CdkDrag,
              CdkDragHandle,
              CdkDropList,
    SharedComponentModule
    
  ],
  exports: [
    FlatTableComponent,
    EditCellDialogComponent,
    PanelbarItemComponent1
  ]
})
export class SharedCloModule { }