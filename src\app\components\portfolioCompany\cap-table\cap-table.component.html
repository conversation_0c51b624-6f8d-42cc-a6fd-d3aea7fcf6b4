<div id="pc-cap-table" class="row cap-table-section pt-3 mr-0 ml-0">
    <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0">
        <div class="cap-header pt-1 pb-2 TextTruncate Heading2-M" title="{{config?.capTableSection}}">{{config?.capTableSection}}</div>
    </div>
    <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 outer-section pl-0 pr-0">
        <div class="panel panel-default tab-bg">
            <div class="panel-heading pl-0 pr-0 border-0 pb-0">
                <div class="panel-title custom-tabs custom-mat-tab cab-table-tab">
                    <nav mat-tab-nav-bar [tabPanel]="tabPanel">

                        <a mat-tab-link [disableRipple]="true" *ngFor="let tab of tabList" id="pc-cap-table-tab"
                             [active]="tab.isActive" (click)="changeTabType(tab)" id="{{tab.aliasName}}"
                            class="TextTruncate">
                            {{tab.aliasName}} </a>
    
                    </nav>
                    <mat-tab-nav-panel #tabPanel>
                        <div class="row mr-0 ml-0">
                            <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12  period-bg">
                                <div class="float-left Caption-M period-title">
                                    Period : {{selectedPeriod == null ? 'NA':selectedPeriod}}
                                </div>
                                <div class="float-right" *ngIf="filterOptions.length > 0">
                                    <div class="pull-right headerSize pb-1">
                                        <div class="d-inline  QMY_Container">
                                            <div class="d-inline custom-padding">
                                                <div class="d-inline QMY_Text MStyle QMYStyle" *ngFor="let opt of filterOptions"
                                                    (click)="onChangePeriod(opt)" [ngClass]="opt.key ?'activeQMY':''" id="pc-cap-table-period">
                                                    {{opt.field}}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pr-0 pl-0">
                                <div class="portfolio-company-table master-kpi-table">
                                    <div class="border-top filter-bg"
                                        [ngClass]="tableResult != null && tableResult.length > 0 ?'pc-border-bottom':''">
                                        <div class="row mr-0 ml-0 pl-3 pr-3">
                                            <div class="col-lg-12 col-md-12 col-sm-12 pl-0 pr-0">
                                                <div class="allvalues float-left">All values in:
                                                   {{modelList?.reportingCurrencyDetail?.currencyCode}}
                                                    ({{kpiValueUnit}})
                                                </div>
                                                <div class="float-right tr-all-values-l-right">
                                                    <div class="d-inline-block search">
                                                        <span class="fa fa-search fasearchicon"></span>
                                                        <input #gb pInputText [appApplyFilter]="{ data: tableResultClone, columns: tableColumns,IsFreezeColumn:true,freezeColumns:'Kpi,text'}"
                                                        (filtered)="tableResult = $event" type="text" id="cap-table-search"
                                                            class="search-text-company" placeholder="Search" [(ngModel)]="globalFilter">
                                                    </div>
                                                    <div class="d-inline-block pr-1 headerfontsize" >
                                                        <div class="d-inline-block table-pref pr-1">Logs</div>
                                                        <div class="d-inline-block pr-1 pl-0" title="Switch to view cell based audit trails">

                                                            <kendo-switch id="pc-cap-table-log" size="small" [(ngModel)]="errorNotation" (valueChange)="handleChange($event)" [onLabel]="' '" [offLabel]="' '"></kendo-switch>
                                                        </div>
                                                    </div>
                                                    <div class="d-inline border-right divider-position"></div>
                                                    <div class="d-inline  pr-1 pl-2">
                                                        <a (click)="exportCapTableValues()" id="pc-cap-table-download">
                                                            <img src="assets/dist/images/Cloud-download.svg" class="cursor-filter" 
                                                            title="Export KPI (Excel file)" alt="" id="pc-cap-table-download1" /><span class="excel-load" >
                                                            <i *ngIf="exportLoading" aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                                        </span>
                                                        </a>
                                                    </div>
                                                    <div class="d-inline border-right divider-position" *ngIf="hasDownloadPermission"></div>
                                                    <div class="d-inline pr-0 pl-2 headerfontsize"><img  id="dropdownMenuButton" [matMenuTriggerFor]="menu" id="pc-cap-table-filter"
                                                            src="assets/dist/images/ConfigurationWhite.svg"  class="cursor-filter" alt=""
                                                            #masterMenuTrigger="matMenuTrigger" /> </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pr-0 pl-0">
                                <div class="content-bg" id="pc-cap-table-data">
                            <kendo-grid class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cab-table-grid" [kendoGridBinding]="tableResult" scrollable="virtual" [rowHeight]="44"
                                [resizable]="true" >
                                <kendo-grid-column *ngIf="tableResult.length > 0" [sticky]="true" [minResizableWidth]="200" [maxResizableWidth]="800"  [width]="400"  field="Kpi">
                                    <ng-template kendoGridHeaderTemplate>
                                        <div *ngIf="tableColumns.length > 0" class="header-icon-wrapper wd-100" >
                                          <span class="TextTruncate S-M">
                                            Instrument
                                          </span>
                                        </div>
                                      </ng-template>
                                    <ng-template kendoGridCellTemplate let-rowData>
                                        <div class="content" >
                                            <span
                                                [ngClass]="[(rowData.IsHeader||rowData.IsBoldKpi) ? 'showToolTip TextTruncate bold-text' :'showToolTip TextTruncate',rowData.IsHeader ? 'headerKpi bold-text' : rowData['IsBoldKpi'] ? 'bold-text': '',((rowData.ParentKpiId !==0||rowData.ParentKpiId ==0)&&!rowData.IsHeader)?'pl-3':'']">
                                                <span *ngIf="rowData.ParentKpiId !== 0">- </span>{{rowData["Kpi"]}}
                                                <span *ngIf="rowData['KPI Info'] =='#'">{{'('+rowData['KPI Info'] +')'}}</span>
                                            </span>
                                        </div>
                                    </ng-template>
                                </kendo-grid-column>
                                <kendo-grid-column [minResizableWidth]="200" *ngFor="let col of tableColumns; index as i" [maxResizableWidth]="800" 
                                    [width]="200" title="{{col.header}}">
                                    <ng-template kendoGridHeaderTemplate>
                                        <div class="header-icon-wrapper wd-100">
                                          <span class="TextTruncate S-M" *ngIf="col.kpiInfo==null">
                                            {{col.header}}
                                          </span>
                                          <span title="{{col.header}}{{'('+col['kpiInfo'] +')'}}" class="TextTruncate S-M" *ngIf="col.kpiInfo!=null && col.kpiInfo =='#'">
                                            {{col.header}} {{'('+col['kpiInfo'] +')'}}
                                          </span>
                                          <span title="{{col.header}}" class="TextTruncate S-M" *ngIf="col.kpiInfo!=null && col.kpiInfo !='#'">
                                            {{col.header}}
                                          </span> 
                                            <span *ngIf="col.dir === 'asc'">
                                                <kendo-svg-icon class="k-sort-order ml-2" size="xsmall" [icon]="icons.sortAscSmallIcon"></kendo-svg-icon>
                                            </span>
                                         
                                            <span *ngIf="col.dir === 'desc'" >
                                                <kendo-svg-icon class="k-sort-order ml-2" size="xsmall" [icon]="icons.sortDescSmallIcon"></kendo-svg-icon>
                                            </span>
                                        </div>
                                      </ng-template>
                                    <ng-template kendoGridCellTemplate let-rowData>
                            
                                        <div tabindex="0" class="prtcmny-det-o" [attr.title]="col.header !=='Kpi' && ErrorNotation ? 'Click to view this cell logs' : ''"  [class.table-data-right]="col.field !='KPI'"
                                            [attr.title]="col.header !=='Kpi' && ErrorNotation ? 'Click to view this cell logs' : ''"  (click)="onAuditLog(rowData,col)" (dblclick)="onEditInit(rowData,col)">
                                            <div class="content">
                                                <div *ngIf="col.header !='Kpi'"
                                                    [ngClass]="[col['isBoldKpi'] == true && col['kpiInfo']!=null ? 'bold-text': '',  rowData.IsBoldKpi && col['kpiInfo']==null ? 'bold-text': '']"
                                                    class="showToolTip TextTruncate" >
                                                    <div [title]="rowData['KPI Info']=='Text'? rowData[col.field]:''" 
                                                        *ngIf="rowData[col.field]!= undefined && rowData[col.field]!=null && rowData[col.field]!=''&& !rowData.IsHeader;else empty_Text">
                                                        <ng-container
                                                            [ngSwitch]="col['kpiInfo']!=null && !rowData['IsOverrule'] ? col['kpiInfo'] :rowData['KPI Info']">
                            
                                                            <container *ngSwitchCase="kpiInfo.Number">
                            
                                                                <span class="float-right TextTruncate w100Percent" [title]="rowData[col.field]" 
                                                                    *ngIf="!rowData[col.field + ' editable']"
                                                                    [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number:'1.0-0' | minusSignToBrackets) : rowData[col.field]"></span>
                                                            </container>
                                                            <container *ngSwitchCase="kpiInfo.Text">
                                                                <span class="float-left left-align TextTruncate w100Percent drop-above" 
                                                                    [title]="rowData[col.field]" *ngIf="!rowData[col.field + ' editable']"
                                                                    [innerHtml]="rowData[col.field]"></span>
                                                            </container>
                                                            <container *ngSwitchCase="kpiInfo.Percentage">
                                                                <span class="float-right TextTruncate w100Percent" [title]="rowData[col.field]" 
                                                                    *ngIf="!rowData[col.field + ' editable']"
                                                                    [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.percentDecimal | minusToBracketsWithPercentage: '%'): rowData[col.field]"></span>
                                                            </container>
                                                            <container *ngSwitchCase="kpiInfo.Multiple">
                                                                <span class="float-right TextTruncate w100Percent" [title]="rowData[col.field]" 
                                                                    *ngIf="rowData[col.field] != 'NA' && rowData[col.field] != '' && !rowData[col.field + ' editable']"
                                                                    [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.multipleDecimal)+'x': rowData[col.field]"></span>
                                                            </container>
                                                            <container *ngSwitchCase="kpiInfo.Currency">
                                                                <span class="float-right TextTruncate w100Percent" [title]="rowData[col.field]" 
                                                                    *ngIf="rowData[col.field] != 'NA' && rowData[col.field] != '' && !rowData[col.field + ' editable']"
                                                                    [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : rowData[col.field]"></span>
                                                            </container>
                                                            <container *ngSwitchDefault>
                                                            </container>
                                                        </ng-container>
                                                    </div>
                                                    <ng-template #empty_Text class="detail-sec">
                                                        <div [ngClass]="(col['kpiInfo']!=null && !rowData['IsOverrule'] ? col['kpiInfo'] :rowData['KPI Info'])=='Text'? 'float-left':'float-right'"
                                                            *ngIf="!rowData[col.field + ' editable'] && !rowData.IsHeader">NA</div>
                                                        <div *ngIf="rowData.IsHeader">
                                                            <div>&nbsp;</div>
                                                        </div>
                                                    </ng-template>
                                                </div>
                                            </div>
                                        </div>
                                    </ng-template>
                                </kendo-grid-column>
                                <ng-template kendoGridNoRecordsTemplate>
                                    <app-empty-state class="finacials-beta-empty-state" [isGraphImage]="false"
                                    *ngIf="tableResult.length == 0"></app-empty-state>
                                </ng-template>
                            </kendo-grid>
                                </div>
                            </div>
                        </div>
                        <app-foot-note [moduleId]="activeTab.moduleId" [companyId]="modelList?.portfolioCompanyID" [isCapTable]="true"
    class="comm-footnote custom-quill-editor" *ngIf="tableResult.length > 0"></app-foot-note>
                    </mat-tab-nav-panel>
                </div>
            </div>
        </div>
    </div>
</div>

<mat-menu #menu="matMenu" [hasBackdrop]="true" class="fixed-menu cap-table-menu">
    <form name="form" #form="ngForm" (ngSubmit)="form.form.valid && onSubmit($event)" (click)="$event.stopPropagation()">
        <div class="row mr-0 ml-0">
            <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0 filter-first">
                    <div class="row m-0 ">
                        <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pb-1  label-align">
                            Period
                        </div>
                        <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-3 pr-3">
                            <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="selectedPeriodType"
                                [fillMode]="'solid'" [filterable]="true" name="Period" [virtual]="virtual"
                                class="k-dropdown-width-200 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="periodType"
                                [filterable]="true" [valuePrimitive]="false" textField="period" placeholder="Select Period" valueField="periodId">
                            </kendo-combobox>
                        </div>
                    </div>
                    <div class="row m-0 ">
                        <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12  pb-1  label-align">
                            Values in:
                        </div>
                        <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-3 pr-3">
                            <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="capTableValueUnit"
                            [fillMode]="'solid'" [filterable]="true" name="Unit" [virtual]="virtual"
                            class="k-dropdown-width-200 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="unitTypeList"
                            [filterable]="true" [valuePrimitive]="false" textField="unitType" placeholder="Select Unit" valueField="typeId">
                        </kendo-combobox>
                        </div>
                    </div>
            </div>
            <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0 filter-first">
                <div class="filter-footer pr-3 pb-3">
                    <div class="d-inline ">
                        <button type="submit" name="Reset" class="btn btn-reset ">Reset</button>
                    </div>
                    <div class="d-inline ">
                        <button type="submit" name="Save"  class="btn btn-light btn-app  pt-0 pb-0">Apply</button>
                    </div>
                </div>
            </div>
          </div>
    </form>
</mat-menu>
<app-loader-component *ngIf="isLoader"></app-loader-component>
<div *ngIf="isUploadPopupVisible" class="nep-modal nep-modal-show kpi-add-edit-modal nep-kpi-bg">
    <div class="nep-modal-mask"></div>
    <div class="nep-card nep-card-shadow nep-modal-panel nep-modal-default nep-all-upload-file">
        <app-kpi-cell-edit [kpiType]="componentName" [dataRow]="dataRow" [tableColumns]="dataColumns"
            [moduleCompanyModel]="uniqueModuleCompany" (cancelButtonEvent)="cancelButtonEvent()" id="pc-cap-table-edit"
            (confirmButtonEvent)="onSubmitButtonEvent($event)"></app-kpi-cell-edit>
    </div>
</div>
<div *ngIf="infoUpdate">
    <confirm-modal IsInfoPopup="true" customwidth="400px" modalTitle="Change Values in Selection"
        primaryButtonName="OK" (primaryButtonEvent)="CloseInfo()" id="pc-cap-table-info">
        <div>
            <div class="invskpi-lh">
                To edit cell data please select numbers in <b><i>'Absolute'</i></b> under <b><i>Values in</i></b>
                dropdown
            </div>
        </div>
    </confirm-modal>
</div>  
