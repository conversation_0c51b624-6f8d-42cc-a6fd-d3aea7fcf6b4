import { ComponentFixture, TestBed } from '@angular/core/testing';
import { KpiCellEditComponent } from './kpi-cell-edit.component';
import { HelperService } from "src/app/services/helper.service";
import { AuditService } from "src/app/services/audit.service";
import { ToastrService, ToastrModule } from "ngx-toastr";
import { of } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DocumentModel, MappedDocuments, UpdatedFiles, DataAuditLogValueModel } from './kpiValueModel';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CellEditConstants, FinancialsSubTabs } from 'src/app/common/constants';
import { ModelToFormData, getCellEditTabName, validateMaxLength, validateNumber } from './cell-edit-utils';

describe('KpiCellEditComponent', () => {
  let component: KpiCellEditComponent;
  let fixture: ComponentFixture<KpiCellEditComponent>;
  let mockHelperService: HelperService;
  let mockAuditService: AuditService;
  let mockToastrService = jasmine.createSpyObj('ToastrService', ['success', 'error']);

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FormsModule, HttpClientTestingModule, ToastrModule.forRoot()],
      declarations: [KpiCellEditComponent],
      providers: [
        { provide: HelperService },
        { provide: AuditService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: 'BASE_URL', useValue: 'http://localhost:5000/' } // provide a mock BASE_URL
      ], schemas: [
        CUSTOM_ELEMENTS_SCHEMA
      ]
    })
      .compileComponents();
    mockHelperService = TestBed.inject(HelperService);
    mockAuditService = TestBed.inject(AuditService);
    mockToastrService = TestBed.inject(ToastrService);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(KpiCellEditComponent);
    component = fixture.componentInstance;
    component.moduleCompanyModel = { valueType: 'Actual', moduleId: 1, companyId: 1, subPageId : 0 };
    component.dataRow = {
      "LineItemId": 608,
      "MappingId": 10493,
      "ParentId": 0,
      "DisplayOrder": 257,
      "Kpi": "AK LAST DATE",
      "KpiInfo": "$",
      "IsBoldKpi": false,
      "IsHeader": false,
      "IsFormula": false,
      "Jan 2023": "400866",
      "Feb 2023": "226252",
      "Mar 2023": "31979"
    };
    component.tableColumns = {
      header: "Jan 2023",
      field: "Jan 2023",
      isBold: true,
      year: 2023,
      quarter: null,
      cell: null,
    };
    const mockDocumentModels: DocumentModel[] = [
      {
        id: 1,
        documentId: 'doc1',
        documentName: 'Document 1',
        extension: '.pdf',
        isExisting: true
      },
      {
        id: 2,
        documentId: 'doc2',
        documentName: 'Document 2',
        extension: '.docx',
        isExisting: false
      },
      {
        id: 3,
        documentId: 'doc3',
        documentName: 'Document 3',
        extension: '.xlsx',
        isExisting: true
      }
    ];
    const mockData: MappedDocuments = {
      documentId: 1,
      commentId: 1,
      valueId: 1,
      comments: 'Test',
      supportingDocumentsId: '1,2,3',
      documentModels: mockDocumentModels,
      auditLogId : 1,
      mappingId : 1,
      auditLogCount: 0,
    };
    const mockExistingDocumentModels: DocumentModel[] = [
      {
        id: 1,
        documentId: 'doc1',
        documentName: 'Document 1',
        extension: '.pdf',
        isExisting: true
      },
      {
        id: 2,
        documentId: 'doc2',
        documentName: 'Document 2',
        extension: '.docx',
        isExisting: false
      },
      {
        id: 3,
        documentId: 'doc3',
        documentName: 'Document 3',
        extension: '.xlsx',
        isExisting: true
      }
    ];
    const updateDocumentModels: UpdatedFiles[] = [
      {
        documentId: 'doc4', file: null
      },
      {
        documentId: 'doc5', file: null
      }
    ];
    component.existingFiles = mockExistingDocumentModels;
    component.updatedFiles = updateDocumentModels;
    const updateDataAuditLogValueModel: DataAuditLogValueModel = {
      moduleId: 0,
      attributeID: 0,
      mappingId: 0,
      portfolioCompanyId: 0,
      oldValue: '',
      newValue: '487.14',
      kpiId: 0,
      valueType: '',
      description: '',
      year: 0
    };
    component.updateDataAuditLogValueModel = updateDataAuditLogValueModel
    spyOn(mockAuditService, 'getPortfolioEditSupportingCommentsData').and.returnValue(of(mockData));
    component.initializeAuditLogFilter();
    fixture.detectChanges();
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('should initialize audit log filter', () => {
    const mockDocumentModels: DocumentModel[] = [
      {
        id: 1,
        documentId: 'doc1',
        documentName: 'Document 1',
        extension: '.pdf',
        isExisting: true
      },
      {
        id: 2,
        documentId: 'doc2',
        documentName: 'Document 2',
        extension: '.docx',
        isExisting: false
      },
      {
        id: 3,
        documentId: 'doc3',
        documentName: 'Document 3',
        extension: '.xlsx',
        isExisting: true
      }
    ];
    const mockData: MappedDocuments = {
      documentId: 1,
      commentId: 1,
      valueId: 1,
      comments: 'Test',
      supportingDocumentsId: '1,2,3',
      documentModels: mockDocumentModels,
      auditLogId : 1,
      mappingId : 1,
      auditLogCount: 0,
    };
    component.initializeAuditLogFilter();
    expect(mockAuditService.getPortfolioEditSupportingCommentsData).toHaveBeenCalled();
    expect(component.updateDataAuditLogValueModel.attributeID).toEqual(mockData.valueId);
    expect(component.updateDataAuditLogValueModel.commentId).toEqual(mockData.commentId);
    expect(component.updateDataAuditLogValueModel.documentId).toEqual(mockData.documentId);
    expect(component.kpiModel.comments).toEqual(mockData.comments);
    expect(component.updateDataAuditLogValueModel.supportingDocumentsId).toEqual(mockData.supportingDocumentsId);
  });
  it('should initialize KPI info', () => {
    component.dataRow = {
      "KpiInfo": "Test KPI Info",
      "Kpi": "Test KPI"
    };
    component.initializeKpiInfo();

    expect(component.kpiInfo).toEqual("Test KPI Info");
    expect(component.kpi).toEqual("Test KPI");
  });

  it('should initialize KPI info with empty strings if dataRow properties are not defined', () => {
    component.dataRow = {};
    component.initializeKpiInfo();
    expect(component.kpiInfo).toEqual("");
    expect(component.kpi).toEqual("");
  });
  it('should initialize updateDataAuditLogValueModel', () => {
    component.dataRow = {
      "LineItemId": 608,
      "MappingId": 10493,
      "ParentId": 0,
      "DisplayOrder": 257,
      "Kpi": "AK LAST DATE",
      "KpiInfo": "$",
      "IsBoldKpi": false,
      "IsHeader": false,
      "IsFormula": false,
      "Jan 2023": "400866",
      "Feb 2023": "226252",
      "Mar 2023": "31979"
    };
    component.tableColumns = {
      header: "Jan 2023",
      field: "Jan 2023",
      isBold: true,
      year: 2023,
      quarter: null,
      cell: null,
    };
    component.initializeUpdateDataAuditLogValueModel();
    expect(component.updateDataAuditLogValueModel.oldValue).toEqual("400866");
    expect(component.updateDataAuditLogValueModel.moduleId).toEqual(1);
    expect(component.updateDataAuditLogValueModel.kpiId).toEqual(608);
    expect(component.updateDataAuditLogValueModel.mappingId).toEqual(0);
    expect(component.updateDataAuditLogValueModel.description).toEqual(CellEditConstants.Manual);
    expect(component.updateDataAuditLogValueModel.portfolioCompanyId).toEqual(1);
    expect(component.updateDataAuditLogValueModel.oldCurrencyType).toEqual("$");
  });
  it('should get icons', () => {
    const iconName = "pdf";
    const iconPath = "assets/dist/images/Adobe-acrobat.svg";
    spyOn(mockHelperService, 'getstaticIconPath').and.returnValue(iconPath);
    const result = component.getIcons(iconName);
    expect(mockHelperService.getstaticIconPath).toHaveBeenCalledWith(iconName);
    expect(result).toEqual(iconPath);
  });
  it('should emit cancelButtonEvent on close', () => {
    spyOn(component.cancelButtonEvent, 'emit');
    component.onClose();
    expect(component.cancelButtonEvent.emit).toHaveBeenCalled();
  });
  it('should remove file', () => {
    const documentId = "doc1";
    spyOn(component, 'findIdByDocumentId');
    component.removeFile(documentId);

    expect(component.findIdByDocumentId).toHaveBeenCalledWith(documentId);
    expect(component.existingFiles.length).toEqual(2);
    expect(component.updatedFiles.length).toEqual(2);
  });
  it('should find id by document id', () => {
    const documentId = "doc1";
    component.removeSupportingDocument = [];
    component.findIdByDocumentId(documentId);
    expect(component.removeSupportingDocument).toEqual(["1"]);
  });

  it('should not find id by document id if documentId is not found', () => {
    const documentId = "doc9";
    component.removeSupportingDocument = [];
    component.findIdByDocumentId(documentId);
    expect(component.removeSupportingDocument).toEqual([]);
  });
  it('should remove existing supporting document by id', () => {
    component.removeSupportingDocument = ["1", "3"];
    component.removeExistingSupportingDocumentById();

    expect(component.updateDataAuditLogValueModel.supportingDocumentsId).toEqual("2");
  });

  it('should not remove existing supporting document by id if removeSupportingDocument is empty', () => {
    component.removeSupportingDocument = [];
    component.removeExistingSupportingDocumentById();
    expect(component.updateDataAuditLogValueModel.supportingDocumentsId).toEqual("1,2,3");
  });
  it('should submit', () => {
    const result = "result";
    spyOn(mockAuditService, 'OnSubmitKpiAuditFilesUpload').and.returnValue(of(result));
    spyOn(component.confirmButtonEvent, 'emit');
    component.onSubmit();
    expect(component.updateDataAuditLogValueModel.oldValue).toEqual("400866");
    expect(component.updateDataAuditLogValueModel.newValue).toEqual(component.kpiModel.newValue);
    expect(component.updateDataAuditLogValueModel.comments).toEqual(component.kpiModel.comments);
    expect(mockAuditService.OnSubmitKpiAuditFilesUpload).toHaveBeenCalled();
    expect(component.confirmButtonEvent.emit).toHaveBeenCalledWith(result);
  });
  it('should change file', () => {
    const file = { name: "file.txt" };
    const value = [file];
    component.onFileChange(value);
    expect(component.files).toContain(file);
    expect(component.existingFiles).toContain(jasmine.objectContaining({ documentName: file.name, extension: "txt", isExisting: false }));
    expect(component.updatedFiles).toContain(jasmine.objectContaining({ file: file }));
    expect(component.fileDropEl.nativeElement.value).toEqual('');
  });

  it('should handle exe file', () => {
    const file = { name: "file.exe" };
    const value = [file];
    component.onFileChange(value);
    expect(component.files).not.toContain(file);
    expect(component.existingFiles).not.toContain(jasmine.objectContaining({ documentName: file.name, extension: "exe", isExisting: false }));
    expect(component.updatedFiles).not.toContain(jasmine.objectContaining({ file: file }));
    expect(mockToastrService.error).toHaveBeenCalledWith(CellEditConstants.ValidateSupportingFileFormat, '', { positionClass: CellEditConstants.ToasterMessagePosition });
    expect(component.fileDropEl.nativeElement.value).toEqual('');
  });
  describe('ModelToFormData', () => {
    it('should convert model to FormData', () => {
      const model: DataAuditLogValueModel = {
        kpiId: 1,
        portfolioCompanyId: 1,
        attributeID: 521,
        supportingDocumentsId: '1,2,3',
        moduleId: 7,
        mappingId: 80,
        oldValue: '',
        newValue: '',
        valueType: '',
        description: '',
        year: 0
      };

      const formData = ModelToFormData(model);

      expect(formData.get('kpiId')).toEqual('1');
      expect(formData.get('portfolioCompanyId')).toEqual('1');
      expect(formData.get('attributeID')).toEqual('521');
      expect(formData.get('moduleId')).toEqual('7');
    });
  });
  describe('validateMaxLength', () => {
    it('should return false if value includes dot and length is 21', () => {
      const event = new KeyboardEvent('keypress', {});
      Object.defineProperty(event, 'target', { writable: true, value: { value: '1.2345678901234567890' } });

      const result = validateMaxLength(event);

      expect(result).toBe(false);
    });

    it('should return true if value includes dot and length is less than 21', () => {
      const event = new KeyboardEvent('keypress', {});
      Object.defineProperty(event, 'target', { writable: true, value: { value: '1.234567890123456789' } });

      const result = validateMaxLength(event);

      expect(result).toBe(true);
    });

    it('should return false if value does not include dot and length is 16', () => {
      const event = new KeyboardEvent('keypress', {});
      Object.defineProperty(event, 'target', { writable: true, value: { value: '1234567890123456' } });

      const result = validateMaxLength(event);

      expect(result).toBe(false);
    });

    it('should return true if value does not include dot and length is less than 16', () => {
      const event = new KeyboardEvent('keypress', {});
      Object.defineProperty(event, 'target', { writable: true, value: { value: '123456789012345' } });

      const result = validateMaxLength(event);

      expect(result).toBe(true);
    });
  });
  describe('validateNumber', () => {
    it('should not modify target value if it matches the regex', () => {
      const event = new KeyboardEvent('keypress', { which: 10 });
      Object.defineProperty(event, 'target', { writable: true, value: { value: '123.456' } });

      const target = event.target as HTMLInputElement; // Typecast event.target to HTMLInputElement after defining it

      validateNumber(event);

      expect(target.value).toBe('123.456');
    });

    it('should round target value to 6 decimal places if it does not match the regex', () => {
      const event = new KeyboardEvent('keypress', { which: 10 });
      Object.defineProperty(event, 'target', { writable: true, value: { value: '123.456789123' } });

      const target = event.target as HTMLInputElement; // Typecast event.target to HTMLInputElement

      validateNumber(event);

      expect(target.value).toBe('123.456789');
    });

    it('should not modify target value if event.which is 15', () => {
      const event = new KeyboardEvent('keypress', { which: 10 });
      Object.defineProperty(event, 'target', { writable: true, value: { value: '123.456789123' } });

      validateNumber(event);

      const target = event.target as HTMLInputElement; // Typecast event.target to HTMLInputElement after defining it

      expect(target.value).toBe('123.456789');
    });
  });
  describe('getCellEditTabName', () => {
    it('should return "actual" for FinancialSubTabs.Actual', () => {
      const result = getCellEditTabName(FinancialsSubTabs.Actual);
      expect(result).toBe('actual');
    });

    it('should return "budget" for FinancialSubTabs.Budget', () => {
      const result = getCellEditTabName(FinancialsSubTabs.Budget);
      expect(result).toBe('budget');
    });

    it('should return "forecast" for FinancialSubTabs.Forecast', () => {
      const result = getCellEditTabName(FinancialsSubTabs.Forecast);
      expect(result).toBe('forecast');
    });

    it('should return "ic" for FinancialSubTabs.IC', () => {
      const result = getCellEditTabName(FinancialsSubTabs.IC);
      expect(result).toBe('ic');
    });

    it('should return null for an unknown tab name', () => {
      const result = getCellEditTabName('unknown');
      expect(result).toEqual("unknown");
    });
    it('should show error message for non-numeric paste data', () => {
      const event = {
        clipboardData: {
          getData: () => 'abc'
        },
        preventDefault: jasmine.createSpy('preventDefault')
      };
      spyOn(component, 'showErrorMessage');
      const result = component.validateInputMaxLengthPaste(event);
      expect(component.showErrorMessage).toHaveBeenCalledWith("Only numeric values allowed");
      expect(event.preventDefault).toHaveBeenCalled();
      expect(result).toBeFalse();
    });

    it('should update input value for valid numeric paste data', () => {
      const event = {
        clipboardData: {
          getData: () => '123'
        },
        target: {
          value: '456',
          selectionStart: 0,
          selectionEnd: 3
        },
        preventDefault: jasmine.createSpy('preventDefault')
      };
      spyOn(component, 'validateInputValue').and.returnValue(true);
      const result = component.validateInputMaxLengthPaste(event);
      expect(event.preventDefault).toHaveBeenCalled();
      expect(event.target.value).toBe('123');
      expect(result).toBeTrue();
    });

    it('should call validateInputValue with input value', () => {
      const event = {
        target: {
          value: '123'
        }
      };
      spyOn(component, 'validateInputValue');
      component.validateInput(event);
      expect(component.validateInputValue).toHaveBeenCalledWith('123', event);
    });

    it('should show error message for multiple minus signs', () => {
      const event = {
        preventDefault: jasmine.createSpy('preventDefault')
      };
      spyOn(component, 'showErrorMessage');
      const result = component.validateInputValue('--123', event);
      expect(component.showErrorMessage).toHaveBeenCalledWith("Only one minus sign is allowed and it must be at the beginning");
      expect(event.preventDefault).toHaveBeenCalled();
      expect(result).toBeFalse();
    });

    it('should show error message for minus sign not at the beginning', () => {
      const event = {
        preventDefault: jasmine.createSpy('preventDefault')
      };
      spyOn(component, 'showErrorMessage');
      const result = component.validateInputValue('123-', event);
      expect(component.showErrorMessage).toHaveBeenCalledWith("Only one minus sign is allowed and it must be at the beginning");
      expect(event.preventDefault).toHaveBeenCalled();
      expect(result).toBeFalse();
    });

    it('should show error message for value exceeding length limit', () => {
      const event = {
        preventDefault: jasmine.createSpy('preventDefault')
      };
      spyOn(component, 'showErrorMessage');
      const result = component.validateInputValue('12345678901234567', event);
      expect(component.showErrorMessage).toHaveBeenCalledWith("Entered value exceeds the limit");
      expect(event.preventDefault).toHaveBeenCalled();
      expect(result).toBeFalse();
    });

    it('should clear error message for valid input', () => {
      const event = {
        preventDefault: jasmine.createSpy('preventDefault')
      };
      spyOn(component, 'clearErrorMessage');
      const result = component.validateInputValue('123', event);
      expect(component.clearErrorMessage).toHaveBeenCalled();
      expect(result).toBeTrue();
    });

    it('should set error message and disable confirm', () => {
      component.showErrorMessage('Error');
      expect(component.errorMessage).toBe('Error');
      expect(component.isConfirmDisabled).toBeTrue();
    });

    it('should clear error message and enable confirm', () => {
      component.clearErrorMessage();
      expect(component.errorMessage).toBe('');
      expect(component.isConfirmDisabled).toBeFalse();
    });
  });
});