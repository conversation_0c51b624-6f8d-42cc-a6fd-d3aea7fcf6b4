import { ViewCloSummaryComponent } from './view-clo-summary/view-clo-summary.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { AngularResizeEventModule } from 'angular-resize-event';
import { QuillModule } from 'ngx-quill';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { SharedCloModule } from 'src/app/components/clo/shared-clo.module';

@NgModule({
  declarations: [
    ViewCloSummaryComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MaterialModule,
    PrimeNgModule,
    AngularResizeEventModule,
    QuillModule,
    SharedDirectiveModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule.forChild([
      { path: '', component: ViewCloSummaryComponent }
  ]),
  KendoModule,
  SharedCloModule
],

})
export class ViewCloSummaryModule { }