import { Component, Input, OnInit, Query<PERSON>ist, ViewChildren, ElementRef, ViewChild, } from '@angular/core';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { SVGIcon, plusIcon, searchIcon, chevronDownIcon, chevronUpIcon } from "@progress/kendo-svg-icons";
import { ExpansionPanelComponent } from '@progress/kendo-angular-layout';
import { DropDownFilterSettings } from '@progress/kendo-angular-dropdowns';
import { DataRequestService } from 'src/app/services/data-request.service';
import { FileExtension } from 'src/app/common/enums';
import { HelperService } from "src/app/services/helper.service";
import { BulkuploadConstants } from "src/app/common/constants";
import { ToastrService } from "ngx-toastr";
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ActivatedRoute, Router } from '@angular/router';
import { GroupModel, ReminderModel, DataRequestAddOrUpdateModel } from '../../interface/request-inteface';
import { DialogAction } from "@progress/kendo-angular-dialog";

@Component({
  selector: 'app-request-details',
  templateUrl: './request-details.component.html',
  styleUrls: ['./request-details.component.scss']
})
export class RequestDetailsComponent implements OnInit {
  @ViewChild("fileDropRef") fileDropEl: ElementRef;
  public arrowDown: SVGIcon = chevronDownIcon;
  public arrowUp: SVGIcon = chevronUpIcon;
  @ViewChildren(ExpansionPanelComponent)
  panels: QueryList<ExpansionPanelComponent>;
  isAutomaticTrigger: boolean = true;
  isActivateRequest: boolean = true;
  isCreateRequest: boolean = true;
  disableAddGroup: boolean = false;
  isLoader: boolean = true;
  disableSaveRequest: boolean = true;
  searchSVG: SVGIcon = searchIcon;
  plusIcon: SVGIcon = plusIcon;
  tabName: string = "Request Details";
  tabList: ITab[] = [];
  submitterList: any[] = [];
  masterSubmitterList: any[] = [];
  fundsList: any[] = [];
  masterFundsList: any[] = [];
  companiesList: any[] = [];
  masterCompaniesList: any[] = [];
  recipientList: any[] = [];
  masterRecipientList: any[] = [];
  reminderList: any[] = [];
  requestCreateOrUpdateModel: any;
  groupModelList: GroupModel[] = [];
  public deleteReminder = false;
  public deleteGroup = false;
  public cancelRequest = false;
  FileExtension = FileExtension;
  requestId: string = "0";
  deletePopupLayout: string = "end";
  groupToDelete: GroupModel;
  expandedPanelIndex: number | null = null;
  requestModel: DataRequestAddOrUpdateModel = {
    id: 0,
    requestId: "",
    name: "",
    isAutomatic: true,
    isActive: true,
    groupList: []
  };
  reminderToDelete: { reminder: ReminderModel, group: GroupModel } = { reminder: null, group: null };
  public virtual: any = {
    itemHeight: 36,
  };
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: 'contains',
  };
  frequencyList: Array<{ text: string; value: number }> = [{ text: "Quarterly", value: 1 }, { text: "Monthly", value: 2 }, { text: "Annual", value: 3 }];
  periodList: Array<{ text: string; value: string }> = [{ text: "Before Due Date", value: "Before Due Date" }, { text: "After Due Date", value: "After Due Date" }];
  
  public myActions: DialogAction[] = [
    { text: "Cancel" },
    { text: "Confirm", themeColor: "primary" },
  ];

  constructor(private helperService: HelperService,
    private _avRoute: ActivatedRoute,
    private dataRequestService: DataRequestService,
    public toastrService: ToastrService,
    public router: Router) {
    if (this._avRoute.snapshot.params["id"]) {
      this.requestId = this._avRoute.snapshot.params["id"];
    }
  }

  /**
   * Initializes the component.
   * This method is called after the component has been created and initialized.
   * It is a lifecycle hook that is called by Angular when the component is being initialized.
   */
  ngOnInit(): void {
    this.getTabList();
    this.getRequestConfig();
  }

  /**
   * Closes the reminder delete dialog.
   * 
   * @param status - The status of the reminder delete dialog.
   */
  public closeReminderDelete(status: string): void {
    this.deleteReminder = false;
  }

  /**
   * Closes the group delete dialog and updates the deleteGroup property.
   * 
   * @param status - The status of the group delete operation.
   */
  public closeGroupDelete(status: string): void {
    this.deleteGroup = false;
  }

  /**
   * Closes the cancel request and sets the cancelRequest flag to false.
   * 
   * @param status - The status of the cancel request.
   */
  public closeCancelRequest(status: string): void {
    this.cancelRequest = false;
  }

  /**
   * Opens the delete reminder dialog and sets the reminder and group to be deleted.
   * @param reminder - The reminder to be deleted.
   * @param group - The group to which the reminder belongs.
   */
  public openReminderDelete(reminder: ReminderModel, group: GroupModel): void {
    this.reminderToDelete.reminder = reminder;
    this.reminderToDelete.group = group;
    this.deleteReminder = true;
  }

  /**
   * Opens the delete confirmation dialog for a group.
   * 
   * @param group - The group to be deleted.
   */
  public openGroupDelete(group: GroupModel): void {
    this.groupToDelete = group;
    this.deleteGroup = true;
  }

  /**
   * Cancels the request.
   */
  public onCancelRequest(): void {
    this.cancelRequest = true;
  }

  /**
   * Handles the action when a reminder is deleted.
   * @param action - The dialog action triggered by the user.
   * @returns void
   */
  onReminderDeleteAction(action: DialogAction): void {
    if (action.text === "Confirm") {
      if (this.reminderToDelete.group.id === 0) {
        this.groupModelList.forEach((group) => {
          if (group.groupName === this.reminderToDelete.group.groupName) {
            group.reminderList = group.reminderList.filter((reminder) => reminder !== this.reminderToDelete.reminder);
          }
        });
      }
      else {
        this.groupModelList.forEach((group) => {
          if (group.id === this.reminderToDelete.group.id && this.reminderToDelete.reminder.id === 0) {
            group.reminderList = group.reminderList.filter((reminder) => reminder !== this.reminderToDelete.reminder);
          }
          else {
            group.reminderList.forEach((reminder) => {
              if (reminder.id === this.reminderToDelete.reminder.id) {
                reminder.deleted = true;
                this.isLoader = true;
                this.dataRequestService.deleteReminder(this.reminderToDelete.reminder.id).subscribe({
                  next:
                    (response: boolean) => {
                      if (response) {
                        this.toastrService.success("Reminder deleted successfully", "", { positionClass: BulkuploadConstants.ToasterMessagePossition });
                        group.reminderList = group.reminderList.filter((reminder) => reminder !== this.reminderToDelete.reminder);
                      }
                      this.isLoader = false;
                    },
                  error: (error) => {
                    this.isLoader = false;
                  }
                });
              }
            });
          }
        });
      }
    }
    this.deleteReminder = false;
  }

  /**
   * Handles the action when a group is deleted.
   * @param action - The dialog action triggered by the user.
   */
  onGroupDeleteAction(action: DialogAction): void {
    if (action.text === "Confirm") {
      if (this.groupToDelete.id === 0) {
        this.groupModelList = this.groupModelList.filter((group) => group !== this.groupToDelete);
      }
      else {
        this.groupModelList.forEach((group) => {
          if (group.id === this.groupToDelete.id) {
            group.deleted = true;
            this.isLoader = true;
            this.dataRequestService.deleteGroup(this.groupToDelete.id).subscribe({
              next:
                (response: boolean) => {
                  if (response) {
                    this.toastrService.success("Group deleted successfully", "", { positionClass: BulkuploadConstants.ToasterMessagePossition });
                    this.groupModelList = this.groupModelList.filter((group) => group !== this.groupToDelete);
                  }
                  this.isLoader = false;
                },
              error: (error) => {
                this.isLoader = false;
              }
            });
          }
        });
      }
    }
    if (this.groupModelList.length === 0) {
      this.disableAddGroup = false;
    }
    this.deleteGroup = false;
    this.valicateRequest();
  }

  /**
   * Handles the cancel request action.
   * @param action - The dialog action.
   */
  onCancelRequestAction(action: DialogAction): void {
    if (action.text === "Confirm") {
      this.router.navigate(['/request-configuration']);
    }
    this.cancelRequest = false;
  }
  
  /**
   * Retrieves the request configuration data.
   */
  getRequestConfig() {
    this.dataRequestService.getDataRequestConfig(this.requestId).subscribe({
      next:
        (configuration: any) => {
          if (configuration != null) {
            this.submitterList = configuration?.userList?.filter((user: { IsExternal: boolean; }) => !user.IsExternal).map(user => ({
              ...user,
              isSelected: false
            })) || [];
            this.recipientList = configuration?.userList?.map(user => ({
              ...user,
              isSelected: false
            })) || [];
            this.fundsList = configuration?.fundList.map(fund => ({
              ...fund,
              isSelected: false
            })) || [];
            this.companiesList = configuration?.companyList.map(company => ({
              ...company,
              isSelected: false
            })) || [];
            this.maintainAllMasterList(configuration);
            if (configuration.requestId != null && configuration.requestId !== "") {
              this.requestModel.requestId = configuration.requestId;
              this.requestModel.name = configuration.name;
              this.requestModel.isAutomatic = configuration.isAutomatic;
              this.requestModel.isActive = configuration.isActive;
              configuration.dataRequestGroupList.forEach((group) => {
                let groupModel = {
                  id: group.id,
                  groupName: group.name,
                  frequency: this.frequencyList.find(x => x.text == group.frequency),
                  companyList: this.companiesList.filter(x => group.companyIds.split(",").map((pcId) => parseInt(pcId)).contains(x.companyId)),
                  fundList: this.fundsList.filter(x => group.fundIds.split(",").map((fId) => parseInt(fId)).contains(x.fundId)),
                  recipientList: this.recipientList.filter(x => group.receiverIds.split(",").contains(x.emailId)),
                  submitterList: this.submitterList.filter(x => group.submitterIds.split(",").contains(x.emailId)),
                  attachmentList: [],
                  reminderList: [],
                  deletedAttachments: [],
                  expanded: false,
                  deleted: false
                };
                const extUser = group.externalUserIds?.split(",");
                if (extUser != null && extUser.length > 0) {
                  extUser.forEach((ext) => {
                    if (this.recipientList.filter(x => x.emailId === ext).length === 0) {
                      this.recipientList.push({ emailId: ext, fullName: ext, isExternal: true });
                    }
                    if (groupModel.recipientList.filter(x => x.emailId === ext).length === 0) {
                      groupModel.recipientList.push({ emailId: ext, fullName: ext, isExternal: true });
                    }
                  });
                }
                group.reminderList.forEach((reminder) => {
                  groupModel.reminderList.push({ id: reminder.id, noOfDays: reminder.noOfDays, period: reminder.period, deleted: reminder.deleted });
                });
                groupModel.attachmentList = group.attachments;
                this.groupModelList.push(groupModel);
              });

            }
            this.isLoader = false;
          }
        },
      error: (error) => { }
    });
  }

  /**
   * Updates the master lists based on the provided configuration.
   * @param configuration - The configuration object containing userList, fundList, and companyList.
   */
  maintainAllMasterList(configuration: any){
    this.masterSubmitterList = configuration.userList?.filter((user: { IsExternal: boolean; }) => !user.IsExternal).map(user => ({
      ...user,
      isSelected: false
    })) || [];
    this.masterRecipientList = configuration.userList?.map(user => ({
      ...user,
      isSelected: false
    })) || [];
    this.masterFundsList = configuration.fundList.map(fund => ({
      ...fund,
      isSelected: false
    })) || [];
    this.masterCompaniesList = configuration.companyList.map(company => ({
      ...company,
      isSelected: false
    })) || [];
  }

  /**
   * Retrieves the list of group models, excluding any deleted models.
   * @returns {GroupModel[]} The filtered list of group models.
   */
  getGroupModelList() {
    return this.groupModelList.filter(x => !x.deleted);
  }

  /**
   * Retrieves the list of tabs for the request details component.
   * Each tab object contains an 'active' boolean property and a 'name' string property.
   * The 'active' property indicates whether the tab is currently active or not.
   * The 'name' property represents the name of the tab.
   * 
   * @returns An array of tab objects.
   */
  getTabList() {
    this.tabList = [
      {
        active: true,
        name: "Request Details",
      },
      {
        active: false,
        name: "Email Template",
      },
    ];
  }
  
  /**
   * Handles the click event of a tab.
   * @param tab - The tab object that was clicked.
   */
  onTabClick(tab: ITab) {
    if (tab != null || tab != undefined) {
      this.tabName = tab.name;
    }
  }

  /**
   * Adds a new group to the groupModelList array.
   * The new group will have default values for its properties.
   * Additionally, it expands the newly added group, adds a reminder to it,
   * and disables the "Add Group" and "Save Request" buttons.
   */
  onGroupAdd() {
    this.getGroupModelList().forEach(group => {
      group.expanded = false;
    });
    this.groupModelList.push({
      id: 0,
      groupName: "Group " + this.numberToWords(this.groupModelList.length + 1),
      frequency: null,
      companyList: [],
      fundList: [],
      recipientList: [],
      submitterList: [],
      attachmentList: [],
      reminderList: [],
      deletedAttachments: [],
      expanded: true,
      deleted: false
    });
    this.addReminder(this.groupModelList[this.groupModelList.length - 1]);
    this.disableAddGroup = true;
    this.disableSaveRequest = true;
    if(this.groupModelList.length > 1) {
      const allCompanyIds = this.groupModelList.flatMap(group => group.companyList.map(company => company.companyId));
      this.companiesList = this.masterCompaniesList.filter(x => !allCompanyIds.includes(x.companyId));
    }
    this.panelExpantion(this.groupModelList.length);
  }

  /**
   * Converts a number to its word representation.
   * @param num - The number to convert.
   * @returns The word representation of the number.
   */
  numberToWords(num) {
    const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine', 'Ten'];
    const teens = ['Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
    const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
    const thousands = ['', 'Thousand'];

    if (num === 0) return 'Zero';
    if (num < 0) return 'negative ' + this.numberToWords(-num);
    if (num <= 10) return ones[num];
    if (num < 20) return teens[num - 11];
    if (num < 100) return tens[Math.floor(num / 10)] + (num % 10 !== 0 ? ' ' + ones[num % 10] : '');
    if (num < 1000) return ones[Math.floor(num / 100)] + ' Hundred' + (num % 100 !== 0 ? ' ' + this.numberToWords(num % 100) : '');
    for (let i = 1; i < thousands.length; i++) {
      let tempNum = Math.pow(1000, i);
      if (num < tempNum * 1000) return this.numberToWords(Math.floor(num / tempNum)) + ' ' + thousands[i] + (num % tempNum !== 0 ? ' ' + this.numberToWords(num % tempNum) : '');
    }
  }

  /**
   * Handles the action when a panel is clicked.
   * Closes all other expanded panels except the one at the specified index.
   *
   * @param index - The index of the panel that was clicked.
   */
  public onAction(index: number,event): void {
    if(event?.action === "expand"){
      const allCompanyIds = this.groupModelList.filter(fc => fc.groupName !== this.groupModelList[index].groupName).flatMap(group => group.companyList.map(company => company.companyId));
      this.companiesList = this.masterCompaniesList.filter(x => !allCompanyIds.includes(x.companyId));
    }
    this.onPcDropdownClose(this.groupModelList[index]);
    this.onFundDropdownClose(this.groupModelList[index]);
    this.onSubmitterDropdownClose(this.groupModelList[index]);
    this.onRecipientDropdownClose(this.groupModelList[index]);
    this.panelExpantion(index);
  }

  /**
   * Expands or collapses the panel at the specified index and collapses all other panels.
   * @param index - The index of the panel to expand or collapse.
   */
  panelExpantion(index: number) {
    this.panels.forEach((panel, idx) => {
      if (idx !== index && panel.expanded) {
        panel.toggle();
      }
    });
  }

  /**
   * Maps an array of tags to a new array.
   * If the input array is empty, it returns an empty array.
   * Otherwise, it wraps the input array in another array and returns it.
   * 
   * @param tags - The array of tags to be mapped.
   * @returns The mapped array of tags.
   */
  public tagMapper(tags: any[]): any[] {
    return tags.length < 1 ? tags : [tags];
  }

  /**
   * Removes a support document file from the files array at the specified index.
   * @param index - The index of the file to be removed.
   */
  removeSupportDocumentFile(group: GroupModel, file: any, index: number) {
    group.deletedAttachments.push(file.id);
    group.attachmentList?.splice(index, 1);
    this.valicateRequest();
  }

  /**
 * Retrieves the static icon path for the given name.
 * 
 * @param name - The name of the icon.
 * @returns The static icon path.
 */
  getIcons(name: string) {
    return this.helperService.getstaticIconPath(name);
  }
  
  /**
   * Handles the file change event and updates the attachment list of the specified group.
   * @param group - The group model to update the attachment list for.
   * @param value - The selected file(s) to be added to the attachment list.
   * @param $event - The event object representing the file change event.
   */
  onFileChange(group: GroupModel, value: any, $event: any) {
    for (let file of value) {
      const fileExtension = file?.name?.split(".").pop();
      file.extension = fileExtension.toLowerCase();
      if (fileExtension == BulkuploadConstants.exe) {
        this.toastrService.error(BulkuploadConstants.validateSupportingFileFormat, "", {
          positionClass: BulkuploadConstants.ToasterMessagePossition,
        });
      } else {
        group.attachmentList.push(file);
      }
    }
    $event.target.value = '';
    this.valicateRequest();
  }

  /**
   * Handles the edit action for a group.
   * 
   * @param group - The group model to be edited.
   */
  onEditGroup(group: GroupModel) {
  }

  /**
   * Marks the specified group as deleted.
   * @param group - The group to be deleted.
   */
  onDeleteGroup(group: GroupModel) {
    group.deleted = true;
  }

  /**
   * Adds a reminder for the specified group.
   * 
   * @param group - The group for which the reminder needs to be added.
   */
  onAddReminder(group: GroupModel) {
    this.addReminder(group);
  }

  /**
   * Marks the given reminder as deleted.
   * @param reminder - The reminder to be marked as deleted.
   * @param group - The group associated with the reminder.
   */
  onDeleteReminder(reminder: ReminderModel, group: GroupModel) {
    reminder.deleted = true;
  }

  /**
   * Adds a reminder to the specified group.
   * @param group - The group to add the reminder to.
   */
  addReminder(group: GroupModel) {
    group.reminderList.push({
      id: 0,
      noOfDays: null,
      period: "",
      deleted: false
    });
    this.validateGroup(group);
  }

  /**
   * Normalizes the recipient input by matching it with the submitter list.
   * If a matching item is found, it returns the matching item.
   * If no matching item is found, it creates a new external user and adds it to the submitter list.
   * @param text$ - An observable that emits the user input as a string.
   * @returns An observable that emits the matching item from the submitter list or a new external user.
   */
  recipientNormalizer = (text$: Observable<string>): any =>
    text$.pipe(
      map((userInput: string) => {
        const matchingItem = this.recipientList.find(x => x.emailId.toLowerCase() === userInput.toLocaleLowerCase());
        if (matchingItem) {
          return matchingItem;
        }
        else {
          const externalUser = {
            emailId: userInput,
            fullName: userInput,
            isExternal: true
          };
          this.recipientList.push(externalUser);
          return externalUser;
        }
      })
  );

  /**
   * Validates the request name and updates the `disableSaveRequest` flag accordingly.
   */
  valicateRequestName() {
    if (this.groupModelList.length === 0 && this.requestModel.name.trim() !== ""){
      this.disableSaveRequest = true;
    }
    else {
      this.valicateRequest();
      if(this.groupModelList.length === 0){
        this.disableAddGroup = false;
      }
    }
  }

  /**
   * Validates the number of days in a reminder.
   * If the number of days is greater than 0 and less than 91, it calls the `valicateRequest` method.
   * Otherwise, it sets the number of days to 0 and calls the `valicateRequest` method.
   * 
   * @param reminder - The reminder model containing the number of days.
   */
  validateNumberOfDays(reminder: ReminderModel) {
    if (reminder.noOfDays > 0 && reminder.noOfDays < 91) {
      this.valicateRequest();
    }
    else {
      reminder.noOfDays = 0;
      this.valicateRequest();
    }
  }

  /**
   * Validates the auto request.
   * 
   * Checks if the `groupModelList` is not empty and the `requestModel.name` is not an empty string,
   * then calls the `valicateRequest` method.
   */
  validateAutoRequest() {
    if (this.groupModelList.length > 0 && this.requestModel.name.trim() !== "") {
      this.valicateRequest();
    }
  }

  /**
   * Validates a group.
   * @param group - The group to validate.
   * @returns Returns `false` if the group is valid, otherwise `true`.
   */
  validateGroup(group: GroupModel) {
    let isValidReminders = group.reminderList.every((reminder) => reminder.noOfDays > 0 && reminder.period !== "");
    if ((group.frequency !== null || group.frequency != "") && group.submitterList.length !== 0 && group.recipientList.length !== 0 && group.reminderList.length !== 0 && isValidReminders) {
      return false;
    }
    else {
      return true;
    }
  }

  /**
   * Validates the request and updates the state of the component accordingly.
   */
  valicateRequest() {
    const flags = [];
    this.groupModelList?.forEach((group) => {
      flags.push(this.validateGroup(group));
    });
    if (flags?.includes(true)) {
      this.disableAddGroup = true;
      this.disableSaveRequest = true;
    }
    else {
      if (this.requestModel.name.trim() === "") {
        this.disableAddGroup = true;
        this.disableSaveRequest = true;
      }
      else {
        this.disableAddGroup = false;
        this.disableSaveRequest = false;
      }
    }
  }

  /**
   * Saves the data request by calling the addOrUpdateDataRequest method of the dataRequestService.
   * If the request is saved successfully, it updates the requestId, clears the groupModelList,
   * displays a success message, navigates to the request configuration details page,
   * disables the save request button, enables the add group button, and reinitializes the component.
   * If there is an error, it sets the isLoader flag to false.
   */
  onSaveRequest() {
    this.isLoader = true;
    this.dataRequestService.addOrUpdateDataRequest(this.prepareAddOrUpdateRequestModel()).subscribe({
      next:
        (response: any) => {
          if (response.requestId != null && response.requestId !== "") {
            this.requestId = response.requestId;
            this.groupModelList = [];
            this.toastrService.success("Request saved successfully", "", {
              positionClass: BulkuploadConstants.ToasterMessagePossition,
            });
            this.isLoader = false;
            this.router.navigate(['/request-configuration-details/' + response.requestId]);
            this.disableSaveRequest = true;
            this.disableAddGroup = false;
            this.ngOnInit();
          }
        },
      error: (error) => {
        this.isLoader = false;
      }
    });
  }

  /**
   * Toggles the panel at the specified index.
   * @param index - The index of the panel to toggle.
   */
  togglePanel(index: number): void {
    this.groupModelList[index].expanded = !this.groupModelList[index].expanded;
  }

  /**
   * Prepares the add or update request model by populating the necessary data.
   * @returns {FormData} The prepared FormData object.
   */
  prepareAddOrUpdateRequestModel() {
    this.requestCreateOrUpdateModel = this.requestModel;
    this.requestCreateOrUpdateModel.groupList = [];
    this.groupModelList.forEach((group) => {
      let groupModel = {
        id: group.id,
        name: group.groupName,
        frequency: group.frequency.text,
        companyIds: group.companyList?.map((com) => com.companyId).join(","),
        fundIds: group.fundList?.map((fund) => fund.fundId).join(","),
        receiverIds: group.recipientList?.map((recipient) => recipient.emailId).join(","),
        submitterIds: group.submitterList?.map((submitter) => submitter.emailId).join(","),
        attachments: group.attachmentList,
        externalUserIds: group.recipientList?.filter((recipient) => recipient.isExternal).map((recipient) => recipient.emailId).join(","),
        reminderIds: "",
        reminderList: group.reminderList,
        requestedPeriod: "",
        dueDate: "",
        deletedAttachments: group.deletedAttachments,
        externalUserList: group.recipientList?.filter((recipient) => recipient.isExternal)
      };
      this.requestCreateOrUpdateModel.groupList.push(groupModel);
    });

    const formData = new FormData();

    // Append all non-File properties as JSON
    for (let property in this.requestCreateOrUpdateModel) {
      if (this.requestCreateOrUpdateModel.hasOwnProperty(property) && property !== 'groupList') {
        formData.append(property, this.requestCreateOrUpdateModel[property]);
      }
    }

    // Append all properties of each group object
    for (let i = 0; i < this.requestCreateOrUpdateModel.groupList.length; i++) {
      formData.append(`groupList[${i}].name`, this.requestCreateOrUpdateModel.groupList[i].name);
      formData.append(`groupList[${i}].dataRequestId`, this.requestCreateOrUpdateModel.requestId);
      formData.append(`groupList[${i}].id`, this.requestCreateOrUpdateModel.groupList[i].id.toString());
      formData.append(`groupList[${i}].frequency`, this.requestCreateOrUpdateModel.groupList[i].frequency.toString());
      formData.append(`groupList[${i}].companyIds`, this.requestCreateOrUpdateModel.groupList[i].companyIds);
      formData.append(`groupList[${i}].fundIds`, this.requestCreateOrUpdateModel.groupList[i].fundIds);
      formData.append(`groupList[${i}].receiverIds`, this.requestCreateOrUpdateModel.groupList[i].receiverIds);
      formData.append(`groupList[${i}].submitterIds`, this.requestCreateOrUpdateModel.groupList[i].submitterIds);
      formData.append(`groupList[${i}].externalUserIds`, this.requestCreateOrUpdateModel.groupList[i].externalUserIds);
      formData.append(`groupList[${i}].deletedAttachments`, this.requestCreateOrUpdateModel.groupList[i]?.deletedAttachments.join(',') || '');
      let fileList: FileList = this.requestCreateOrUpdateModel.groupList[i].attachments.filter(attachment => 'size' in attachment);
      if(fileList.length > 0){
        for (let j = 0; j < fileList.length; j++) {
          formData.append(`groupList[${i}].Attachments`, fileList[j]);
        }
      }
      for (let k = 0; k < this.requestCreateOrUpdateModel.groupList[i].reminderList.length; k++) {
        formData.append(`groupList[${i}].reminderList[${k}].id`, this.requestCreateOrUpdateModel.groupList[i].reminderList[k].id.toString());
        formData.append(`groupList[${i}].reminderList[${k}].noOfDays`, this.requestCreateOrUpdateModel.groupList[i].reminderList[k].noOfDays.toString());
        formData.append(`groupList[${i}].reminderList[${k}].period`, this.requestCreateOrUpdateModel.groupList[i].reminderList[k].period);
        formData.append(`groupList[${i}].reminderList[${k}].deleted`, this.requestCreateOrUpdateModel.groupList[i].reminderList[k].deleted);
      }
    }
    return formData;
  }

  /**
   * Validates the companies in the group model and updates the selection status of each company.
   * @param groupModel - The group model containing the company list.
   * @param multiSelect - The multi-select component used for filtering.
   */
  validateCompanies(groupModel: GroupModel, multiSelect) {
    multiSelect.clearFilter();
    this.valicateRequest();
    let selectedId = groupModel.companyList.map(company => company.companyId);
    this.companiesList.forEach(company => {
      company.isSelected =  selectedId.includes(company.companyId)
    });
    if(groupModel.companyList.length > 0) {
      this.onPcDropdownClose(groupModel);
    }
    else {
      const allCompanyIds = this.groupModelList.filter(fc => fc.groupName !== groupModel.groupName).flatMap(group => group.companyList.map(company => company.companyId));
      this.companiesList = this.masterCompaniesList.filter(x => !allCompanyIds.includes(x.companyId));
    }
  } 

  /**
   * Handles the event when the PC dropdown is closed.
   * Sorts the companies list based on the selected items.
   * @param groupModel - The group model containing the company list.
   */
  onPcDropdownClose(groupModel: GroupModel) {
    // Create a copy of the companiesList to avoid directly mutating the bound data
    const sortedCompanies = [...this.companiesList];
    let selectedId = groupModel.companyList.map(company => company.companyId);
    // Sort the companies list: selected items first
    sortedCompanies.sort((a, b) => {
      const aSelected = selectedId.includes(a.companyId);
      const bSelected = selectedId.includes(b.companyId);
      if (aSelected && !bSelected) {
        return -1; // a comes before b
      }
      if (!aSelected && bSelected) {
        return 1; // b comes before a
      }
      return 0; // no change in order
    });
  
    // Update the companiesList with the sorted array
    this.companiesList = sortedCompanies;
  }

  /**
   * Validates the funds based on the provided group model and multi-select component.
   * @param groupModel - The group model containing the fund list.
   * @param multiSelect - The multi-select component used to clear the filter.
   */
  validateFunds(groupModel: GroupModel, multiSelect) {
    multiSelect.clearFilter();
    this.valicateRequest();
    let selectedId = groupModel.fundList.map(f => f.fundId);
    this.fundsList.forEach(f => {
      f.isSelected =  selectedId.includes(f.fundId)
    });
    if(groupModel.fundList.length > 0) {
      this.onFundDropdownClose(groupModel);
    }
    else {
      this.fundsList = this.masterFundsList;
    }
  } 
  
  /**
   * Handles the event when the fund dropdown is closed.
   * Sorts the funds list based on the selected items.
   * @param groupModel - The group model containing the fund list.
   */
  onFundDropdownClose(groupModel: GroupModel) {
    if(groupModel.fundList.length > 0) {
      // Create a copy of the fundsList to avoid directly mutating the bound data
      const sortedFunds = [...this.fundsList];
      let selectedId = groupModel.fundList.map(f => f.fundId);
      // Sort the companies list: selected items first
      sortedFunds.sort((a, b) => {
        const aSelected = selectedId.includes(a.fundId);
        const bSelected = selectedId.includes(b.fundId);
        if (aSelected && !bSelected) {
          return -1; // a comes before b
        }
        if (!aSelected && bSelected) {
          return 1; // b comes before a
        }
        return 0; // no change in order
      });
    
      // Update the fundsList with the sorted array
      this.fundsList = sortedFunds;
    }
    else {
      this.fundsList = this.masterFundsList;
    }
  }

  /**
   * Validates the submitters in the group model and updates the isSelected property of each submitter.
   * @param groupModel - The group model containing the submitter list.
   * @param multiSelect - The multi-select component used to clear the filter.
   */
  validateSubmitters(groupModel: GroupModel, multiSelect) {
    multiSelect.clearFilter();
    this.valicateRequest();
    let selectedId = groupModel.submitterList.map(s => s.emailId);
    this.submitterList.forEach(s => {
      s.isSelected =  selectedId.includes(s.emailId)
    });
    if(groupModel.submitterList.length > 0) {
      this.onSubmitterDropdownClose(groupModel);
    }
    else {
      this.submitterList = this.masterSubmitterList;
    }
  } 
  
  /**
   * Handles the event when the submitter dropdown is closed.
   * Sorts the submitter list based on the selected items.
   * @param groupModel - The group model containing the submitter list.
   */
  onSubmitterDropdownClose(groupModel: GroupModel) {
    if(groupModel.submitterList.length > 0) {
      // Create a copy of the submitterList to avoid directly mutating the bound data
      const sortedSubmitters = [...this.submitterList];
      let selectedId = groupModel.submitterList.map(s => s.emailId);
      // Sort the companies list: selected items first
      sortedSubmitters.sort((a, b) => {
        const aSelected = selectedId.includes(a.emailId);
        const bSelected = selectedId.includes(b.emailId);
        if (aSelected && !bSelected) {
          return -1; // a comes before b
        }
        if (!aSelected && bSelected) {
          return 1; // b comes before a
        }
        return 0; // no change in order
      });
    
      // Update the submitterList with the sorted array
      this.submitterList = sortedSubmitters;
    }
    else {
      this.submitterList = this.masterSubmitterList;
    }
  }

  /**
   * Validates the recipients in the group model and updates the recipient list accordingly.
   * @param groupModel - The group model containing the recipient list.
   * @param multiSelect - The multi-select component used to clear the filter.
   */
  validateRecipients(groupModel: GroupModel, multiSelect) {
    multiSelect.clearFilter();
    this.valicateRequest();
    let selectedId = groupModel.recipientList.map(r => r.emailId);
    this.recipientList.forEach(r => {
      r.isSelected =  selectedId.includes(r.emailId)
    });
    if(groupModel.recipientList.length > 0) {
      this.onRecipientDropdownClose(groupModel);
    }
    else {
      this.recipientList = this.masterRecipientList;
    }
  } 
  
  /**
   * Handles the event when the recipient dropdown is closed.
   * It creates a copy of the recipientList to avoid directly mutating the bound data.
   * Sorts the recipientList based on the selected items, with selected items appearing first.
   * Updates the recipientList with the sorted array.
   * 
   * @param groupModel - The GroupModel containing the recipientList.
   */
  onRecipientDropdownClose(groupModel: GroupModel) {
    if(groupModel.recipientList.length > 0) {
      // Create a copy of the recipientList to avoid directly mutating the bound data
      const sortedRecipients = [...this.recipientList];
      let selectedId = groupModel.recipientList.map(r => r.emailId);
      // Sort the companies list: selected items first
      sortedRecipients.sort((a, b) => {
        const aSelected = selectedId.includes(a.emailId);
        const bSelected = selectedId.includes(b.emailId);
        if (aSelected && !bSelected) {
          return -1; // a comes before b
        }
        if (!aSelected && bSelected) {
          return 1; // b comes before a
        }
        return 0; // no change in order
      });
    
      // Update the recipientList with the sorted array
      this.recipientList = sortedRecipients;
    }
    else {
      this.recipientList = this.masterRecipientList;
    }
  }
}