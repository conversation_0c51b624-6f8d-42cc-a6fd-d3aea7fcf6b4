<nav aria-label="breadcrumb" class="breadcrumb-container">
    <ol class="breadcrumb">
        <li class="breadcrumb-item" *ngIf="breadcrumbs.length > 0">
           <span><img src="assets/dist/images/breadcrumb-home.svg" alt="home" class="pr-1"/></span> 
           <a *ngIf="breadcrumbs[0].url!=''" [routerLink]="breadcrumbs[0].url">{{breadcrumbs[0].label}}</a>
           <a *ngIf="breadcrumbs[0].url==''" class="cursor-disable" href="javascript:void">{{breadcrumbs[0].label}}</a>
        </li>
        <li class="breadcrumb-item" *ngIf="breadcrumbs.length > 1" [ngClass]="breadcrumbs.length == 2 ? 'active':''">
            <span class="breadcrumb-separator"><img src="assets/dist/images/fi-chevron-right.svg" alt="separator"/></span>
            <a *ngIf="breadcrumbs[1].url!=''" [routerLink]="breadcrumbs[1].url" [title]="breadcrumbs[1].label" [queryParams]="breadcrumbs[1].queryParams">
                {{breadcrumbs[1].label}}
            </a>
            <a *ngIf="breadcrumbs[1].url==''" class="cursor-disable" href="javascript:void" [title]="breadcrumbs[1].label">
                {{breadcrumbs[1].label}}
            </a>
        </li>
        <li class="breadcrumb-item" aria-current="page" *ngIf="breadcrumbs.length > 2" [ngClass]="breadcrumbs.length == 3 ? 'active':''">
            <span class="breadcrumb-separator"><img src="assets/dist/images/fi-chevron-right.svg" alt="separator"/></span>
            <a [routerLink]="breadcrumbs[2].url" *ngIf="breadcrumbs[2].url!=''" [title]="breadcrumbs[2].label">
                {{breadcrumbs[2].label == null ? getBreadcrumbValue(breadcrumbs[2]) : breadcrumbs[2].label}}
            </a>
            <a  *ngIf="breadcrumbs[2].url==''" class="cursor-disable" href="javascript:void" [title]="breadcrumbs[2].label">
                {{breadcrumbs[2].label == null ? getBreadcrumbValue(breadcrumbs[2]) : breadcrumbs[2].label}}
            </a>
        </li>
    </ol>
</nav>