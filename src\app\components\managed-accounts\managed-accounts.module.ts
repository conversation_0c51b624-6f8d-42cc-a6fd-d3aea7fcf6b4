import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ManagedAccountsComponent } from './managed-accounts.component';
import { RouterModule } from '@angular/router';
import { NoDataContainerComponent } from './no-data-container/no-data-container.component';

@NgModule({
  declarations: [
    ManagedAccountsComponent,
    NoDataContainerComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild([
        { path: '', component: ManagedAccountsComponent}
    ]),
  ],
  exports: [
    ManagedAccountsComponent
  ]
})
export class ManagedAccountsModule { }
