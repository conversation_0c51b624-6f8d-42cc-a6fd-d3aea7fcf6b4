
.clo-table-body {
  .clo-table-content {
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
  }

  .clo-active {
      padding: 10rem 1px;
     .title {
          color: #000000;
          width: calc(100% - 100px) !important;
      }
  }
  .custom-size{
  padding-right: 32px;
  }
  .clo-in-active {
      padding: 10rem 1px;
     .title {
          color: #000000;
          width: calc(100% - 100px) !important;
      }
  }

  .clo-item {
      background-color:#F5F9FF ;
      border-top-right-radius: 4px;
      border-top-left-radius: 4px;
      &:last-child {
          border-bottom: none !important;
          
      }
  }
}
.btn-warning {
  background: #ffffff 0% 0% no-repeat padding-box !important;
  border: 1px solid #4061c7 !important;
  border-radius: 4px;
  opacity: 1;
  color: var(--Color-Primary-Primary---78, #4061c7);

  &:focus,
  &:active {
    outline: none;
    box-shadow: none;
    border: 1px solid #4061c7 !important;
  }
}
.btn-save-clo {
  height: 32px;
  padding: 6px 16px;
}
.btn-reset-container {
  display: flex;
  justify-content: flex-start;
}
.char-count{
  position: absolute;
  right: 10px;
}
.empty-text{
  border: 1px solid #E6E6E6;
  height: 56px;
}

.custom-quillcontainer{
  border-bottom: 1px solid #DEDFE0;
  border-left: 1px solid #DEDFE0;
  border-right: 1px solid #DEDFE0;
  align-items: center;
}


:host ::ng-deep {
.k-tabstrip {
  width: 100%;
  
  
    
    .k-item {
      color: #666666;
      
      &.k-active {
        color: #4061C7;
        border-bottom: 2px solid #4061C7;
      }
    }
  
}
}
.nodata-container{
text-align: center;
}
:host ::ng-deep .k-tabstrip-top > .k-content, 
:host ::ng-deep .k-tabstrip-top > .k-tabstrip-content {
  padding: 0px;
}

.edit-icon{
cursor: pointer;
}