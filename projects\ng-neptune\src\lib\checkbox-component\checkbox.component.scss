/* Customize the label (the container) */
.container {
    display: block;
    position: relative;
    padding-left: 28px;
    margin-bottom: 12px;
    font-size: 22px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    padding-right: 0px;
  }
  
  /* Hide the browser's default checkbox */
  .container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }
  
  /* Create a custom checkbox */
  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: #fff;
    border: 1px solid #cac9c7;
    border-radius: 4px;
  }
  
  /* On mouse-over, add a grey background color */
  .container:hover input ~ .checkmark {
    background-color: #fff;
    border: 1px solid #4061C7;
  }
  
  /* When the checkbox is checked, add a blue background */
  .container input:checked ~ .checkmark {
    background-color: #4061C7;
    border: 1px solid #4061C7;
  }
  
  /* Create the checkmark/indicator (hidden when not checked) */
  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }
  
  /* Show the checkmark when checked */
  .container input:checked ~ .checkmark:after {
    display: block;
  }
  
  /* Style the checkmark/indicator */
  .container .checkmark:after {
    top: 20%;
    left: 2px;
    width: 80%;
    height: 40%;
    border: 2px solid transparent;
    border-width: 0 0 2px 2px;
    position: absolute;
    z-index: 10;
    display: none;
    content: " ";
    border-color: #fff;
    transform: rotate(-45deg);
  }

  .disable{
    cursor: not-allowed !important;
    opacity: 40%;
  }

  .enable{
    cursor: pointer;
  }