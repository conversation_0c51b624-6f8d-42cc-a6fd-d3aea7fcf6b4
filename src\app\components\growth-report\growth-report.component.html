<div class="row mr-0 ml-0 growth-report-section">

    <div class="col-6 col-md-6 col-lg-6 col-xl-6 col-sm-6 pl-0 pr-0 left-section">
        <div class="content-section">
            <div class="row mr-0 ml-0">
                <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 content-header">
                    <div class="float-right btn-section">
                        <button [matMenuTriggerFor]="menu" #tRecordTrigger="matMenuTrigger"  kendoButton 
                            class="kendo-custom-button k-btn-white Body-R apply-btn" fillMode="outline"
                            themeColor="primary">
                            Add Portfolio Company
                            <span class="pl-1">
                                <kendo-icon name="chevron-down"></kendo-icon>
                            </span>
                        </button>
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 "
                    [ngClass]="isNoData ? 'content-body-r-no-section':''">
                    <div class="row mr-0 ml-0 no-data-container" *ngIf="isNoData">
                        <ng-container
                            *ngTemplateOutlet="noDataTemplate; context: { message: 'No company details found', subMessage: 'Add companies and funds from “Add Portfolio Company” button to start creating template' ,icon:'no-content-lp-report' }"></ng-container>
                    </div>
                    <div class="row mr-0 ml-0" *ngIf="!isNoData">
                        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0">
                            <kendo-textbox [(ngModel)]="searchText" (input)="filterGrid($event.target.value)"
                                size="medium" [fillMode]="'solid'"
                                class="k-input-width-100 k-input-custom input-k-search-flat" selectOnFocus="false"
                                placeholder="Search fund and company here..">
                                <ng-template kendoTextBoxSuffixTemplate>
                                    <button class="text-search-button" kendoButton>
                                        <img src="assets/dist/images/search-icon-n.svg" alt="Search" />
                                    </button>
                                </ng-template>
                            </kendo-textbox>
                        </div>
                        <div
                            class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0 content-body-r-section panel-section">
                            <div class="row mr-0 ml-0 no-data-container" *ngIf="panelList.length == 0">
                                <ng-container
                                    *ngTemplateOutlet="noDataTemplate; context: { message: 'No details found!', subMessage: 'Asset that you searched for, does not exist. You can add asset by clicking on “Add Portfolio Company” button.' ,icon:'no-content-lp-report' }"></ng-container>
                            </div>
                            <div class="row mr-0 ml-0 panel-row">
                                <div appScrollBorder
                                    class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 g-panel-header pr-0 pl-0"
                                    [ngClass]="panel.isExpanded ? 'panel-h-auto' :'panel-h'"
                                    *ngFor="let panel of panelList">
                                    <div class="d-inline-block TextTruncate S-M header-block"
                                        [ngClass]="panel.isExpanded ? 'panel-h-margin' :''" [style.width.%]="100">
                                        <div class="d-inline">
                                            <a (click)="expandPanel(panel)"> <img
                                                    src="assets/dist/images/{{panel.isExpanded ? 'arrow-down.svg' :'arrow-left.svg'}}"
                                                    alt="Sort left" /> </a>
                                        </div>
                                        <div title="{{panel.fundName}}" class="d-inline pl-2 S-M">{{panel.fundName}}
                                        </div>
                                    </div>
                                    <div class="row mr-0 ml-0"
                                        *ngIf="panel?.items!=undefined && panel.items.length > 0 && panel.isExpanded">
                                        <div appScrollBorder title="{{company.text}}"
                                            class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 TextTruncate header-sub-block"
                                            *ngFor="let company of panel.items">

                                            <div (click)="setCompanyState(company)" title="{{company.text}}"
                                                [ngClass]="company.isActive ? 'company-section-active' :'company-section'"
                                                class="TextTruncate">
                                                {{
                                                company.text
                                                }}
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-6 col-md-6 col-lg-6 col-xl-6 col-sm-6 pl-0 right-section pr-0">
        <form [formGroup]="form" (ngSubmit)="onSubmitForm($event)" #f="ngForm">
            <div [formGroup]="selectedCompanySection" class="content-section">
                <div class="row mr-0 ml-0">
                    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 content-header">
                        <div class="float-left">
                            <nep-tab *ngIf="!isNoData" id="nepGrowthTab" class="custom-pipeline-tab" [tabList]=tabList
                                (OnSelectTab)="onTabClick($event.name)">
                            </nep-tab>
                        </div>
                        <div class="float-right pt-1 pb-1 pl-3 button-section" *ngIf="!isNoData">
                            <ng-container [ngSwitch]="tabName">
                                <button *ngSwitchCase="'Row Headers'" kendoButton
                                    class="kendo-custom-button k-btn-white Body-R apply-btn" fillMode="outline"
                                    themeColor="primary" (click)="openHeaderPopUp = true;">Add Header</button>
                                <button *ngSwitchCase="'Column KPI'" kendoButton [disabled]="selectedCompany == null"
                                    class="kendo-custom-button k-btn-white Body-R apply-btn" fillMode="outline" 
                                    themeColor="primary" (click)="addColumnKpi()">Add KPI</button>
                            </ng-container>
                        </div>
                    </div>
                    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 content-body-l-section">
                        <div class="row mr-0 ml-0 no-data-container" *ngIf="isNoData">
                            <ng-container
                                *ngTemplateOutlet="noDataTemplate; context: { message: 'No template details available', subMessage: 'Add companies and funds from “Add Portfolio Company” button to start seeing template details',icon:'no-content-lp-report' }"></ng-container>
                        </div>
                        <div class="row mr-0 ml-0 no-data-container"
                            *ngIf="!isNoData && (selectedCompanySection.value.length == 0 || selectedCompany==null || rowHeaderList.length == 0) && tabName == 'Row Headers'">
                            <ng-container
                                *ngTemplateOutlet="noDataTemplate; context: { message: 'Start adding new headers', subMessage: 'Select companies from Funds & click  on “Add Header” button to start mapping headers',icon:'plus-header' }"></ng-container>
                        </div>
                        <div class="row mr-0 ml-0 no-data-container"
                            *ngIf="!isNoData &&  tabName == 'Column KPI' && (selectedCompanySection?.get('columnHeaders')?.get('kpiSections')?.value?.length  == 0 || selectedCompanySection.value.length == 0 || selectedCompany==null)">
                            <ng-container
                                *ngTemplateOutlet="noDataTemplate; context: { message: 'Start adding new headers', subMessage: 'Select companies from Funds & click  on “Add KPI button to start mapping kpi',icon:'plus-header' }"></ng-container>
                        </div>
                        <ng-container [ngSwitch]="tabName">
                        <ng-container *ngIf="rowHeaderList.length > 0"> 
                            <div formArrayName="rowHeaders" class="row mr-0 ml-0" *ngSwitchCase="'Row Headers'" [ngClass]="(selectedCompanySection?.get('rowHeaders')?.get('kpiSections')?.value?.length  == 0 || selectedCompanySection?.value?.length == 0 || selectedCompany==null) ? '' :'row-h-section'">
                                <div
                                    class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 row-header-section" *ngIf="selectedCompanySection?.get('rowHeaders')!=undefined && selectedCompanySection?.get('rowHeaders')?.controls.length > 0">
                                    <div class="row mr-0 ml-0"
                                        *ngFor="let rowHeader of selectedCompanySection?.get('rowHeaders')?.controls; let i = index"
                                        [formGroupName]="i">
                                        <div
                                            class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 row-header-content">
                                            <div class="pl-3 float-left TextTruncate kpi-header pr-3 S-M">
                                                {{rowHeader.value.headerName}}</div>
                                            <div class="float-right pt-1 pb-1">
                                                <a class="icon-header" (click)="editHeader(rowHeader.value)" position="bottom" title="Edit Header" kendoTooltip> <img
                                                        src="assets/dist/images/edit-icon-k.svg"
                                                        alt="edit header" /></a>
                                                <a class="icon-header" (click)="addKpiSection(rowHeader,i)" position="bottom" title="Add KPI" kendoTooltip> <img
                                                        src="assets/dist/images/plus-icon-k.svg" alt="add kpi" /></a>
                                            </div>

                                        </div>
                                        <div *ngIf="rowHeader?.value?.kpiSections?.length > 0"
                                            class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                                            <div formArrayName="kpiSections" class="row mr-0 ml-0 r-section">
                                                <div
                                                    class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 column-kpi-section">
                                                    <div class="row mr-0 ml-0 pb-3 pt-3 card-section">
                                                        <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 column-row-section"
                                                            *ngFor="let kpiSection of rowHeader?.get('kpiSections')?.controls; let k = index"
                                                            [formGroupName]="k">
                                                            <div class="float-left content-kpi-section">
                                                                <div class="d-inline-block pr-4 kpi-type-section">
                                                                    <label
                                                                        class="Caption-M m-0 custom-report-label req-label">KPI
                                                                        Type</label>
                                                                    <div>
                                                                        <kendo-combobox formControlName="kpiTypeId"
                                                                            name="kpiTypeId" [clearButton]="false"
                                                                            [fillMode]="'flat'" [filterable]="true"
                                                                            class="k-dropdown-width-240 k-select-medium k-select-flat-custom k-dropdown-height-32"
                                                                            [size]="'medium'" [data]="kpiModules"
                                                                            [filterable]="false"
                                                                            (valueChange)="getKpiList($event,kpiSection)"
                                                                            textField="aliasName" valueField="moduleId"
                                                                            placeholder="Select Kpi Type"
                                                                            [valuePrimitive]="true">
                                                                        </kendo-combobox>
                                                                    </div>
                                                                </div>
                                                                <div class="d-inline-block">
                                                                    <label
                                                                        class="Caption-M m-0 custom-report-label req-label">KPI
                                                                        Line Item</label>
                                                                    <div>
                                                                        <kendo-multiselect name="lineItemId"
                                                                        
                                                                            [rounded]="'medium'" [fillMode]="'flat'"
                                                                            [checkboxes]="true"
                                                                            formControlName="lineItemId" [ngClass]="{'k-multiselect-search-80': kpiSection?.get('lineItemId')?.value?.length>0}"
                                                                            [kendoDropDownFilter]="filterSettings"
                                                                            [clearButton]="false"
                                                                            class="k-multiselect-custom k-dropdown-width-240 k-multiselect-grey-chip k-multiselect-flat-custom"
                                                                            [tagMapper]="tagMapper"
                                                                            [data]="kpiLineItemList[kpiSection.get('kpiTypeId').value]"
                                                                            [textField]="'kpi'"
                                                                            [valueField]="'mappingId'"
                                                                             [autoClose]="false" (valueChange)="getSelectedObjects(kpiSection?.get('lineItemId')?.value,kpiLineItemList[kpiSection.get('kpiTypeId').value].length,'isCheckAll',i,kpiSection)"
                                                                            [valuePrimitive]="true"
                                                                            placeholder="Select KPI Line Item">
                                                                            <ng-template kendoMultiSelectHeaderTemplate>
                                                                                <div class="inline-container" >
                                                                                    <input name="kpi-check-allhk"
                                                                                        type="checkbox"
                                                                                        class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"
                                                                                        kendoCheckBox [indeterminate]="isIndeterminate(kpiSection?.get('lineItemId')?.value,kpiLineItemList[kpiSection.get('kpiTypeId').value])"
                                                                                        [checked]="isCheAllchecked(kpiSection)"
                                                                                        (click)="onSelectAllCheckBoxClick(kpiSection?.get('isCheckAll')?.value,kpiSection?.get('lineItemId')?.value,kpiLineItemList[kpiSection.get('kpiTypeId').value],i,kpiSection)" />
                                                                                    <kendo-label
                                                                                        for="fund-check-all">Select
                                                                                        All</kendo-label>
                                                                                </div>
                                                                            </ng-template>
                                                                            <ng-template kendoMultiSelectItemTemplate
                                                                                let-dataItem>
                                                                                <span class="TextTruncate pl-1 Body-R" 
                                                                                    [title]="dataItem.kpi">{{
                                                                                    dataItem.kpi
                                                                                    }}</span>
                                                                            </ng-template>
                                                                        </kendo-multiselect>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="float-right close-section">
                                                                <a href="javascript:void"
                                                                    (click)="removeRowKpiSection(i,k)">
                                                                    <img src="assets/dist/images/close-drag.svg"
                                                                        alt="drag-icon" class="drag-icon">
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mr-0 ml-0" *ngSwitchCase="'Column KPI'" [ngClass]="(selectedCompanySection?.get('columnHeaders')?.get('kpiSections')?.value?.length  == 0 || selectedCompanySection?.value?.length == 0 || selectedCompany==null) ? '' :'row-h-section'">
                                <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 row-header-section"
                                    formGroupName="columnHeaders">
                                    <div class="row mr-0 ml-0" formArrayName="kpiSections">
                                        <div *ngIf="selectedCompanySection?.get('columnHeaders')?.get('kpiSections')?.value?.length > 0"
                                            class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                                            <div class="row mr-0 ml-0">
                                                <div
                                                    class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 column-kpi-section">
                                                    <div class="row mr-0 ml-0 pt-3 pb-3 card-section">
                                                        <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 column-row-section"
                                                            *ngFor="let kpiSection of selectedCompanySection.get('columnHeaders').get('kpiSections').controls; let k = index"
                                                            [formGroupName]="k">
                                                            <div class="float-left content-kpi-section">
                                                                <div class="d-inline-block pr-4 kpi-type-section">
                                                                    <label
                                                                        class="Caption-M m-0 custom-report-label req-label">KPI
                                                                        Type</label>
                                                                    <div>
                                                                        <kendo-combobox formControlName="kpiTypeId"
                                                                            name="kpiTypeId" [clearButton]="false"
                                                                            [fillMode]="'flat'" [filterable]="true"
                                                                            class="k-dropdown-width-240 k-select-medium k-select-flat-custom k-dropdown-height-32"
                                                                            [size]="'medium'" [data]="kpiModules"
                                                                            [filterable]="false"
                                                                            (valueChange)="getKpiList($event,kpiSection)"
                                                                            textField="aliasName" valueField="moduleId"
                                                                            placeholder="Select Kpi Type"
                                                                            [valuePrimitive]="true">
                                                                        </kendo-combobox>
                                                                    </div>
                                                                </div>
                                                                <div class="d-inline-block">
                                                                    <label
                                                                        class="Caption-M m-0 custom-report-label req-label">KPI
                                                                        Line Item</label>
                                                                    <div>
                                                                        
                                                                        <kendo-multiselect name="lineItemId"
                                                                            [rounded]="'medium'" [fillMode]="'flat'"
                                                                            [checkboxes]="true"
                                                                            formControlName="lineItemId"  [ngClass]="{'k-multiselect-search-80': kpiSection?.get('lineItemId')?.value?.length>0}"
                                                                            [kendoDropDownFilter]="filterSettings"
                                                                            [clearButton]="false"
                                                                            class="k-multiselect-custom k-dropdown-width-240 k-multiselect-grey-chip k-multiselect-flat-custom"
                                                                            [tagMapper]="tagMapper"
                                                                            [data]="kpiLineItemList[kpiSection.get('kpiTypeId').value]"
                                                                            [textField]="'kpi'"
                                                                            [valueField]="'mappingId'"
                                                                            [autoClose]="false" [valuePrimitive]="true" (valueChange)="getSelectedObjects(kpiSection?.get('lineItemId')?.value,kpiLineItemList[kpiSection.get('kpiTypeId').value].length,'isCheckAll',i,kpiSection)"
                                                                            placeholder="Select KPI Line Item">
                                                                            <ng-template kendoMultiSelectHeaderTemplate>
                                                                                <div class="inline-container">
                                                                                    <input name="kpi-check-allhk"
                                                                                        type="checkbox"
                                                                                        class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"
                                                                                        kendoCheckBox
                                                                                        kendoCheckBox [indeterminate]="isIndeterminate(kpiSection?.get('lineItemId')?.value,kpiLineItemList[kpiSection.get('kpiTypeId').value])"
                                                                                        (click)="onSelectAllCheckBoxClick(kpiSection?.get('isCheckAll')?.value,kpiSection?.get('lineItemId')?.value,kpiLineItemList[kpiSection.get('kpiTypeId').value],i,kpiSection)"
                                                                                        [checked]="isCheAllchecked(kpiSection)"
                                                                                         />
                                                                                    <kendo-label
                                                                                        for="fund-check-all">Select
                                                                                        All</kendo-label>
                                                                                </div>
                                                                            </ng-template>
                                                                            <ng-template kendoMultiSelectItemTemplate
                                                                                let-dataItem>
                                                                                <span class="TextTruncate pl-1 Body-R"
                                                                                    [title]="dataItem.kpi">{{
                                                                                    dataItem.kpi
                                                                                    }}</span>
                                                                            </ng-template>
                                                                        </kendo-multiselect>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="float-right close-section">
                                                                <a href="javascript:void" (click)="removeKpiSection(k)">
                                                                    <img src="assets/dist/images/close-drag.svg"
                                                                        alt="drag-icon" class="drag-icon">
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mr-0 ml-0 fixed-bottom-g" *ngIf="rowHeaderList.length > 0 && selectedCompany!=null">
                                <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 p-3">
                                    <div class="float-right">
                                        <button id="lp-report-reset" [disabled]="selectedCompany == null"
                                            kendoButton class="kendo-custom-button Body-R apply-btn mr-2"
                                            fillMode="outline" themeColor="primary">Reset</button>
                                            <button id="lp-report-save"  kendoButton
                                            class="kendo-custom-button Body-R apply-btn"
                                            themeColor="primary">Save</button>
                                            <!-- To do later  -->
                                        <!-- <button id="lp-report-save" [disabled]="!form.valid || (!isSaveEnabled && !isFundChange)" kendoButton
                                            class="kendo-custom-button Body-R apply-btn"
                                            themeColor="primary">Save</button> -->
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                        </ng-container>
                    </div>
                </div>
            </div>
        </form>
    </div>

</div>

<ng-template #noDataTemplate let-message="message" let-subMessage="subMessage" let-icon="icon">
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 no-content-section">
        <div class="text-center">
            <img src="assets/dist/images/{{icon}}.svg" alt="No Content" class="no-content-image">
        </div>
        <div class="text-center no-content-text pt-3 pb-2 Body-M">
            {{ message }}
        </div>
        <div class="text-center no-content-sub Caption-M template-text break-word">
            {{ subMessage }}
        </div>
    </div>
</ng-template>
<mat-menu xPosition="before" #menu="matMenu" [hasBackdrop]="false" class="growth-report-pop-up">
    <div class="row mr-0 ml-0 pop-up-section" (click)="$event.stopPropagation()">
        <div class="col-12 col-md-12 col-lg-12 col-xl-12 col-sm-12 pr-0 pl-0">
            <div class="sub-section">
                <span>
                    <a> <img src="assets/dist/images/arrow-up-n.svg" alt="Sort Ascending" /></a>
                </span> <span> Sort Ascending</span>
            </div>
        </div>
        <div class="col-12 col-md-12 col-lg-12 col-xl-12 col-sm-12 pr-0 pl-0">
            <div class="sub-section">
                <span>
                    <a> <img src="assets/dist/images/down-icon-n.svg" alt="Sort Desc" /></a>
                </span> <span> Sort Descending</span>
            </div>
        </div>
        <div class="v-line"></div>
        <div class="col-12 col-md-12 col-lg-12 col-xl-12 col-sm-12 pr-0 pl-0">
            <div class="sub-section">
                <span>
                    <a> <img src="assets/dist/images/filter-icon-n.svg" alt="Filter" /></a>
                </span> <span> Filter</span>
            </div>
        </div>
        <div class="col-12 col-md-12 col-lg-12 col-xl-12 col-sm-12 pr-0 pl-0">
            <div class="sub-section">
                <label class="Caption-M mb-0 req-label">Funds</label>
                <div class="filter-content">
                    <kendo-multiselect  [(ngModel)]="selectedFundList" #multiFundSelect [rounded]="'medium'"
                        [fillMode]="'flat'" [checkboxes]="true"
                        [ngClass]="{'k-multiselect-search-150':selectedFundList.length > 0}"
                        [kendoDropDownFilter]="filterSettings" name="selectedFundList" [clearButton]="false"
                        class="k-custom-flat-p k-multiselect-custom k-dropdown-width-272 k-multiselect-grey-chip k-multiselect-flat-custom"
                        [tagMapper]="tagMapper" [data]="fundList" [textField]="'fundName'"
                        (close)="clearSearch(multiFundSelect)" [valueField]="'fundID'" (removeTag)="onClear()"
                        (valueChange)="getFundSelected($event)" [autoClose]="false" placeholder="Select Funds">
                        <ng-template kendoMultiSelectHeaderTemplate>
                            <div class="inline-container">
                                <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"
                                    kendoCheckBox [(ngModel)]="isCheckedCopyFundAll" [indeterminate]="isFundIndet"
                                    (click)="onFundClick();" />
                                <kendo-label for="chk">Select All</kendo-label>
                            </div>
                        </ng-template>
                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                            <span class="TextTruncate pl-1 Body-R" id="funds-{{dataItem.fundID}}"
                                [title]="dataItem.fundName">{{ dataItem.fundName }}</span>
                        </ng-template>
                    </kendo-multiselect>

                </div>
            </div>
        </div>
        <div class="col-12 col-md-12 col-lg-12 col-xl-12 col-sm-12 pr-0 pl-0">
            <div class="sub-section">
                <label class="Caption-M mb-0 req-label">Company</label>
                <div class="filter-content">
                    <kendo-multiselecttree  [loadOnDemand]="false"
                        [disabled]="selectedFundList.length == 0 || groupedCompanyList?.length == 0"
                        [(ngModel)]="selectedCompanyList" #multiKpiSelect [rounded]="'medium'"
                        class="k-custom-flat-p k-dropdown-width-272 k-multiselect-custom k-multiselect-custom-filter  k-multiselect-custom-tree k-multiselect-chip-100 k-multiselect-grey-chip"
                        [filterable]="true" [fillMode]="'flat'" kendoMultiSelectTreeExpandable
                        [kendoMultiSelectTreeHierarchyBinding]="groupedCompanyList" name="selectedCompanyList"
                        [clearButton]="false" [tagMapper]="tagMapper" [textField]="'text'" [checkAll]="true"
                        [valueField]="'id'" [expandOnFilter]="filterExpandSettings" [expandedKeys]="['0']"
                        childrenField="items" placeholder="Select Companies">
                        <ng-template kendoMultiSelectTreeNodeTemplate let-dataItem>
                            <span title="{{ dataItem.text }}" class="TextTruncate Body-R"> {{ dataItem.text
                                }}</span>
                        </ng-template>
                    </kendo-multiselecttree>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-12 col-lg-12 col-xl-12 col-sm-12 pr-0 pl-0" *ngIf="growthId > 0">
            <div class="">
                <div class="pb-2 disclaimer-text Caption-R"><img class="pl-1" src="assets/dist/images/info-circle.svg" alt="info" /> You are about to make changes
                    to your template</div>
                
            </div>
        </div>
        <div class="col-12 col-md-12 col-lg-12 col-xl-12 col-sm-12 pr-0 pl-0">
            <div class="float-right btn-form-section pt-2">
                <button (click)="clearFields()" kendoButton
                    class="kendo-custom-button Body-R apply-btn mr-2 btn-width-102" fillMode="outline"
                    themeColor="primary">Clear</button>
                <button (click)="onSubmit(true)"
                    [disabled]="selectedFundList.length == 0 || selectedCompanyList.length == 0" kendoButton
                    class="kendo-custom-button Body-R apply-btn btn-width-102" themeColor="primary">Apply</button>
            </div>
        </div>
    </div>
</mat-menu>
<confirm-modal [isCloseEnable]="true" (closeIconClick)="openHeaderPopUp = false;rowHeader = null;isExistHeader = false;isEditHeader = false;"
    class="custom-confirm-model" *ngIf="openHeaderPopUp" [primaryButtonName]="'Confirm'" [IsDeleteEnable]="isEditHeader ? true : false"
    [secondaryButtonName]="'Cancel'"
    [disablePrimaryButton]="rowHeader == '' ||  rowHeader == null || rowHeader.length == 0 || rowHeader?.length > 200"
    [modalTitle]="isEditHeader ? 'Edit Header' : 'Add Header'" (primaryButtonEvent)="saveItem()" (deleteButtonEvent)="deleteHeaderConfirm()"
    (secondaryButtonEvent)="openHeaderPopUp = false;rowHeader = null;isExistHeader = false;isEditHeader = false;">
    <div class="modalBodyTextStyle">
        <div class="Body-R">
            <label class="Caption-M mb-0 req-label save-as-label">Header Name</label>
            <div class="filter-content" [ngClass]="isExistHeader ? 'exist-text-box' :''">
                <kendo-textbox (inputFocus)="headerFocus()" class="custom-k-text" name="rowHeader"
                    [placeholder]="'Add Header Name'" [(ngModel)]="rowHeader" maxlength="200" fillMode="flat"
                    [style.width.%]="100" [clearButton]="true">
                </kendo-textbox>
                <span *ngIf="isExistHeader" class="Caption-R exist-label pt-1">Duplicate header names not allowed</span>
            </div>

            <div class=" pt-4 pb-4" *ngIf="isEditHeader">
                <div class="pb-2 disclaimer-text Caption-R">Disclaimer  <img class="pl-1" src="assets/dist/images/info-circle.svg" alt="info" /> </div>
                <div class=" Caption-R delete-msg">Header Once deleted cannot be restored back.</div>
                
            </div>
        </div>
    </div>
</confirm-modal>
<confirm-modal [isCloseEnable]="true" (closeIconClick)="deleteHeaderPopUp = false;" class="custom-confirm-model"
    *ngIf="deleteHeaderPopUp" [primaryButtonName]="'Delete'" [secondaryButtonName]="'Cancel'"
    [modalTitle]="'Delete Header'" (primaryButtonEvent)="deleteHeaderConfirm()"
    (secondaryButtonEvent)="deleteHeaderPopUp = false;">
    <div class="modalBodyTextStyle">
        <div class="S-R">
            Header Once deleted cannot be restored back.
        </div>
    </div>
</confirm-modal>

<div *ngIf="isSet || isCancel">
    <confirm-modal customwidth="489px" [isCloseEnable]="true" [modalTitle]="'Unsaved Changes Found'" class="repository-modal" primaryButtonName="Confirm" secondaryButtonName="Cancel"
        (primaryButtonEvent)="onConfirmClick($event)" (secondaryButtonEvent)="onDiscard($event)"
        (closeIconClick)="onDiscard($event)">
        <div class="body-color">
            <div class="col-lg-12 col-md-12 col-xs-12 col-md-12 col-xl-12 col-sm-12 p-0">
                You have unsaved changes. If you leave without saving, your changes will be lost. Do you confirm?
        </div>
        </div>
</confirm-modal>
</div>
<app-loader-component *ngIf="isLoader"></app-loader-component>