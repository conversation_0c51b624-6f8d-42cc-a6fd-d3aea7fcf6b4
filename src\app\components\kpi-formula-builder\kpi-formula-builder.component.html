<div class="nep-modal nep-modal-show formula-builder-model nep-mdl-shw-d-bg">
    <div class="nep-modal-mask"></div>
    <div   class="nep-card nep-card-shadow nep-modal-panel nep-modal-default nep-crd-pr-d">
      <div class="nep-card-header nep-modal-title">
        <div class="float-left M-M">
            {{isMapping && kpiModel?.mappingKpiId > 0 ?'Company Formula':'Standard Formula'}}
        </div>
        <div class="float-right close-icon"(click)="closePopUp('')">
            <i class="pi pi-times"></i>
        </div>
      </div>
      <div class="nep-card-body">
        <div class="row mr-0 ml-0 formula-body">
            <div class="col-6 col-xs-6 col-sm-6 col-md-6 col-lg-6 col-xl-6 pr-0 pl-0 border-right">
                <div class="row mr-0 ml-0 formula-section">
                    <div class=" col-12 col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0 pb-2 formula-column">
                       <div title="{{kpiModel?.kpi || kpiModel?.profitAndLossLineItem || kpiModel?.balanceSheetLineItem || kpiModel?.cashFlowLineItem}}"  class="formula-line-item  TextTruncate d-inline-block">
                        {{kpiModel?.kpi || kpiModel?.profitAndLossLineItem || kpiModel?.balanceSheetLineItem || kpiModel?.cashFlowLineItem}}
                       </div>
                       <div class="d-inline-block pl-2 equals-op">=</div>
                    </div>
                    <div class="col-12 col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0 formula-builder-section">
                            <div   tabindex=1000  #contentDivElement  (keyup)="validateFormula($event);applyEvent($event)"  
                             (keypress)="setFormulaLogic($event);"  id="contentBox" contenteditable="true" class="border-1 custom-text-area">                           
                            </div>                         
                        <div class="d-none">
                            {{formulaKPIModel}}
                        </div>
                        <div [ngClass]="isDisplay && kpiDropDownList.length > 0 ? 'd-block':'d-none'" class="custom-formula-ul-select" #ulDropDown>
                            <ul>
                                <li *ngFor="let item of kpiDropDownList" title="{{item.kpi}}" class="TextTruncate" (click)="selectedKPI(item)">
                                    {{item.kpi}}
                                </li>
                            </ul>
                        </div>
                        <div [ngClass]="isError && kpiDropDownList.length == 0 ? 'd-block':'d-none'" class="custom-formula-ul-select error-select" #ulErrorDropDown>
                            <ul>
                                <li title="{{errorUlMessage}}"  class="TextTruncate" >
                                    {{errorUlMessage}}
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-xs-6 col-sm-6 col-md-6 col-lg-6 col-xl-6 pr-0 pl-0">
                <div class="row mr-0 ml-0 formula-kpi-section">
                    <div class="col-12 col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12  kpi-type">
                        <div class="row mr-0 ml-0">
                            <div class="col-12 col-sm-5 col-xs-12 col-md-5 col-lg-5 col-xl-5 pr-0 pl-0">
                                <label class="pl-2 pr-2 mb-0 pb-0">
                                    Available KPIs
                                </label>
                        <kendo-dropdownlist id="kpi-select-dropdown"
                            class="kpi-list k-dropdown-width-200 k-custom-solid-dropdown k-dropdown-height-36 k-dropdown-no-border kpi-dropdown-background"
                            [data]="formulaKPITypes" textField="pageConfigAliasName" valueField="field" [(ngModel)]="selectedFormulaType"
                            (valueChange)="GetSelectedKpiData($event)" [filterable]="true" [groupField]="'aliasName'" [clearButton]="false"
                            (filterChange)="handleFilter($event)">
                            <ng-template kendoDropDownListGroupTemplate let-dataItem>
                                <div title="{{ dataItem }}" class="flex align-items-center">
                                    <span class="TextTruncate" title="{{ dataItem }}">{{ dataItem }}</span>
                                </div>
                            </ng-template>
                            <ng-template kendoDropDownListItemTemplate let-dataItem>
                                <span id="kpi-type-name" class="TextTruncate" title="{{ dataItem.pageConfigAliasName }}">
                                    {{ dataItem.pageConfigAliasName }}
                                </span>
                            </ng-template>
                        </kendo-dropdownlist>
                            </div>
                            <div class="col-6 col-sm-7 col-xs-12 col-md-7 col-lg-7 col-xl-7 pr-0 pl-3">
                                    <label class="mb-0 pb-0">
                                        Available Operators
                                    </label>
                                <div class="formula-btns pt-2">
                                    <div class="formula-btn text-center mr-2" (click)="applyFormula('(')">
                                        (
                                    </div>
                                    <div class="formula-btn text-center mr-2"(click)="applyFormula(')')">
                                        )
                                    </div>
                                    <div class="formula-btn text-center mr-2"(click)="applyFormula('+')">
                                        +
                                    </div>
                                    <div class="formula-btn text-center mr-2"(click)="applyFormula('-')">
                                        -
                                    </div>
                                    <div class="formula-btn text-center mr-2" (click)="applyFormula('*')">
                                        x
                                    </div>
                                    <div class="formula-btn text-center mr-2 d-none"(click)="applyFormula('%')">
                                        %
                                    </div>
                                    <div class="formula-btn text-center"(click)="applyFormula('/')">
                                        /
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0">
                        <div class="search">
                            <span class="fa fa-search fasearchicon p-1"></span>
                            <input #gb pInputText type="text"
                                class="search-text-company companyListSearchHeight"
                                placeholder="Search KPI"  [appApplyFilter]="{ data: kpiListClone, columns: kpiHeaders,IsFreezeColumn:false,freezeColumns:'Kpi,text'}"
                                (filtered)="kpiList = $event"/>
                        </div>
                    </div>
                    <div class="col-12 col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0">
                        <kendo-grid [kendoGridBinding]="kpiList" [resizable]="true" [sortable]="true" scrollable="virtual"
                            [rowHeight]="46"
                            class="kendo-grid-formula-builder k-grid-border-right-width k-grid-outline-none k-grid-border-bottom-width">
                            <kendo-grid-column *ngFor="let col of kpiHeaders" class="TextTruncate"  [field]="col.field" [title]="col.header">
                                <ng-template kendoGridHeaderTemplate>
                                    <div class="header-icon-wrapper wd-98">
                                        <span class="TextTruncate S-M">
                                            {{col.header}}
                                        </span>
                                    </div>
                                </ng-template>
                                <ng-template kendoGridCellTemplate let-rowData>
                                    <div (click)="applyFormulaKPI(rowData)" title="{{rowData['kpi']}}" id="{{$index+1|number}}"[ngClass]="rowData['isActive']?'active-kpi':''" class="TextTruncate" (click)="setIsActive(rowData)">
                                        <span class="text-truncate d-inline-block kpi-title"  [ngClass]="(rowData.isBoldKPI || rowData.isHeader) ? 'font-weight-bold':''"> {{rowData['kpi']}}</span>
                                        <span *ngIf="rowData.isHeader" title="Header KPI" class="pl-3 pr-3 kpi-header" >
                                            <img *ngIf="rowData.isHeader" class="img-header" src="assets/dist/images/kpi-header.svg" alt="kpi-header.svg"
                                           />                
                                        </span>
                                    </div>
                                </ng-template>
                            </kendo-grid-column>
                            <ng-template kendoGridNoRecordsTemplate>
                                <app-empty-state class="finacials-beta-empty-state" [imageHeight]="'calc(100vh - 418px)'"
                                    [isGraphImage]="false"></app-empty-state>
                            </ng-template>
                        </kendo-grid>
                    </div>
                </div>
            </div>
        </div>
      </div>
      <div class="nep-card-footer nep-card-right nep-modal-footer">
       <div class="float-right">
        <nep-button Name="clear-formula" [disabled]="isDisabledBtn" Type="Secondary" (click) ="clearFormula()" >
            Clear
        </nep-button>
        <nep-button Name="save-formula" [disabled]="isDisabledBtn" class="pr-nep-btn" Type="Primary" (click)="saveFormula()">
            Save
        </nep-button>
       </div>
      </div>
    </div>
  </div>

  <app-loader-component *ngIf="isLoader"></app-loader-component>