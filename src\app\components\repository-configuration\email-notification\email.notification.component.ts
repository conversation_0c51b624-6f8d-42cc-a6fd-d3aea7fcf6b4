import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { DataIngestionService } from 'src/app/services/data-ingestion.service';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { DocumentTypeOption } from '../../data-extraction/data-ingestion/data-ingestion.model';
import { CreateEmailRemainderDto, EmailReminderListResponseDto, ReminderModel } from '../model/config-model';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { FeaturesEnum } from 'src/app/services/permission.service';

@Component({
    selector: 'app-email-notification',
    templateUrl: './email.notification.component.html',
    styleUrls: ['./email.notification.component.scss']
})
export class EmailNotificationComponent implements OnInit {
    public portfolioCompanies: any[] = [];
    public documentTypes: DocumentTypeOption[] = [];
    public selectedPortfolioCompanies: any[] = [];
    public selectedDocumentTypes: any[] = [];
    public expanded: boolean = true;
    public virtual = {
        itemHeight: 40
    };
    public filterSettings: any = {
        caseSensitive: false,
        operator: 'contains'
    };

    // For "Select All" functionality
    public isPortfolioCompanyCheckAll: boolean = false;
    public isDocumentTypeCheckAll: boolean = false;

    // For loading state
    public isLoading: boolean = false;

    // For Recent Reminders section
    public reminderData: ReminderModel[] = [];
    public reminderStatus: { [key: number]: boolean } = {};
    public expandedReminders: { [key: number]: boolean } = {};
    public filterPeriods: { text: string, value: string }[] = [];
    public selectedFilterPeriod: string = 'lastMonth';
    public showFilter: boolean = false;

    // For confirmation modal
    public confirmDelete: boolean = false;
    public deletedCloName: string = '';
    public modalTitle: string = 'Delete Confirmation';
    public activeRemindersLeft: number = 0;
    public reminderIdToDelete: number | null = null;

    constructor(
        private formBuilder: FormBuilder,
        private repositoryConfigService: RepositoryConfigService,
        private readonly dataIngestion: DataIngestionService,
        private toastrService: ToastrService,
        private router: Router,
    ) { }

    ngOnInit(): void {
        // Fetch data from backend services
        this.fetchPortfolioCompanies();
        this.loadDocumentTypes();

        // Load email reminders
        this.loadEmailReminders();
    }

    /**
     * Load email reminders from API
     */
    loadEmailReminders(): void {
        // Show loading state
        this.isLoading = true;

        this.repositoryConfigService.getEmailReminders().subscribe({
            next: (reminders: EmailReminderListResponseDto[]) => {
                // Map API response to component model
                this.reminderData = reminders.map((reminder, index) => {
                    const formattedDate = new Date(reminder.createdOn).toLocaleDateString('en-US', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric'
                    });

                    // Initialize the first document type or create empty one if none exist
                    const documentType = reminder.documentTypes.length > 0 ?
                        reminder.documentTypes[0] :
                        { id: 0, name: 'N/A' };
                      return {
                        id: index + 1, // Using index as temp ID since we need a number
                        reminderId: reminder.reminderId, // Store the actual UUID
                        companies: reminder.companies,
                        documentType: documentType,
                        documentTypes: reminder.documentTypes,
                        subject: reminder.subject,
                        date: formattedDate,
                        isActive: reminder.isActive,
                        isExpanded: false
                    };
                });

                // Initialize reminder status object
                this.reminderData.forEach(reminder => {
                    this.reminderStatus[reminder.id] = reminder.isActive;
                    this.expandedReminders[reminder.id] = false;
                });

                this.isLoading = false;
            },
            error: (error) => {
                console.error('Error fetching email reminders:', error);
                this.toastrService.error('Failed to load email reminders');
                this.isLoading = false;
            }
        });
    }

    // Toggle reminder expanded state
    public toggleReminderExpand(reminderId: number): void {
        this.expandedReminders[reminderId] = !this.expandedReminders[reminderId];

        // If expanding, fetch detailed data for this reminder
        if (this.expandedReminders[reminderId]) {
            const reminder = this.reminderData.find(r => r.id === reminderId);
            if (reminder && reminder.reminderId) {
                this.fetchReminderDetails(reminder.reminderId);
            }
        }
    }

    // Fetch detailed reminder data when expanding
    public fetchReminderDetails(reminderId: string): void {
        this.repositoryConfigService.getEmailReminderDetails(reminderId).subscribe({
            next: (details) => {
                // Store the detailed data for the expanded reminder
                // This will be passed to the detail component
                const reminder = this.reminderData.find(r => r.reminderId === reminderId);
                if (reminder) {
                    reminder.detailsData = details;
                }
            },
            error: (error) => {
                console.error('Error fetching reminder details:', error);
                this.toastrService.error('Failed to load reminder details');
            }
        });
    }

    // Handle status change
    public onStatusChange(reminderId: number): void {
        const newStatus = this.reminderStatus[reminderId];
        const reminder = this.reminderData.find(r => r.id === reminderId);

        if (reminder && reminder.reminderId) {
            // In a real implementation, this would call an API to update the status
            this.toastrService.info(`Reminder ${reminderId} status changed to ${newStatus ? 'Active' : 'Inactive'}`);
        }
    }

    /**
     * Fetch portfolio companies from the backend
     */
    fetchPortfolioCompanies(): void {
        this.repositoryConfigService.getPortfolioCompanies().subscribe({
            next: (data: any) => {
                this.portfolioCompanies = data.map((company: any) => ({
                    companyId: company.portfolioCompanyID,
                    companyName: company.companyName,
                    selected: false
                }));
            },
            error: (error: any) => {
                console.error('Error fetching portfolio companies:', error);
            }
        });
    }

    loadDocumentTypes(): void {
        this.dataIngestion.getDataExtractionTypes(FeaturesEnum.PortfolioCompany).subscribe({
            next: (data: DocumentTypeOption[]) => {
                this.documentTypes = data.map((docType: any) => ({
                    id: docType.id,
                    documentName: docType.documentName,
                    selected: false
                }));
            },
            error: (error) => {
                this.documentTypes = [];
            },
        });
    }

    // Portfolio company selection change handler
    public onPortfolioCompanySelectionChange(): void {
        // Update the "Check All" state based on selection
        this.isPortfolioCompanyCheckAll = this.selectedPortfolioCompanies?.length === this.portfolioCompanies?.length;
    }

    // Tag mapper for multiselect
    public tagMapper = (text: string): string => {
        return text;
    }

    // Toggle expanded state
    public toggleExpanded(): void {
        this.expanded = !this.expanded;
    }

    // Select All Portfolio Companies
    public onSelectAllPortfolioCompanies(): void {
        this.isPortfolioCompanyCheckAll = !this.isPortfolioCompanyCheckAll;
        this.selectedPortfolioCompanies = this.isPortfolioCompanyCheckAll ? [...this.portfolioCompanies] : [];
    }

    // Check if some but not all portfolio companies are selected
    public isPortfolioCompanyIndet(): boolean {
        return this.selectedPortfolioCompanies?.length > 0 &&
            this.selectedPortfolioCompanies?.length < this.portfolioCompanies?.length;
    }

    // Select All Document Types
    public onSelectAllDocumentTypes(): void {
        this.isDocumentTypeCheckAll = !this.isDocumentTypeCheckAll;
        this.selectedDocumentTypes = this.isDocumentTypeCheckAll ? [...this.documentTypes] : [];
    }

    // Check if some but not all document types are selected
    public isDocumentTypeIndet(): boolean {
        return this.selectedDocumentTypes?.length > 0 &&
            this.selectedDocumentTypes?.length < this.documentTypes?.length;
    }

    // Cancel form
    public cancel(): void {
        this.selectedPortfolioCompanies = [];
        this.selectedDocumentTypes = [];
        this.isPortfolioCompanyCheckAll = false;
        this.isDocumentTypeCheckAll = false;
    }

    public setupEmailReminder(): void {
        const selectedPortfolioCompanyIds = this.selectedPortfolioCompanies.map(company => company.companyId);
        const selectedDocumentTypeIds = this.selectedDocumentTypes.map(docType => docType.id);

        const remainder: CreateEmailRemainderDto = {
            portfolioCompanyIds: selectedPortfolioCompanyIds,
            documentTypeIds: selectedDocumentTypeIds
        };

        this.repositoryConfigService.createEmailRemainder(remainder).subscribe({
            next: (response) => {
                if (response && response.reminderId) {
                    this.router.navigate(['/email-reminder/' + response.reminderId]);
                } else {
                    this.toastrService.error('Failed to create email remainder');
                }
            },
            error: (error) => {
                this.toastrService.error(error.message || 'Failed to create email remainder');
            }
        })
    }

    // Edit reminder - navigate to edit page
    public editReminder(reminderId: number): void {
        const reminder = this.reminderData.find(r => r.id === reminderId);
        if (reminder && reminder.reminderId) {
           this.router.navigate(['/email-reminder-edit/' + reminder.reminderId ]);
        }
    }

    // Delete reminder (show confirmation modal)
    public deleteReminder(reminderId: number): void {
        const reminder = this.reminderData.find(r => r.id === reminderId);
        if (reminder) {
            this.reminderIdToDelete = reminderId;
            this.activeRemindersLeft = this.reminderData.filter(r => r.isActive && r.id !== reminderId).length;
            this.confirmDelete = true;
        }
    }

    // Called when user confirms deletion in modal
    public deleteEmailReminder(): void {
        if (this.reminderIdToDelete !== null) {
            const reminder = this.reminderData.find(r => r.id === this.reminderIdToDelete);
            if (reminder && reminder.reminderId) {
                this.repositoryConfigService.deleteEmailReminder(reminder.reminderId).subscribe({
                    next: () => {
                        this.reminderData = this.reminderData.filter(r => r.id !== this.reminderIdToDelete);
                        this.toastrService.success('Email reminder deleted successfully!', '', {
                            positionClass: 'toast-center-center'
                        });
                        this.cancelDelete();
                    },
                    error: (error) => {
                        this.toastrService.error('Failed to delete email reminder', '', {
                            positionClass: 'toast-center-center'
                        });
                        this.cancelDelete();
                    }
                });
            } else {
                this.cancelDelete();
            }
        }
    }

    // Called when user cancels deletion in modal
    public cancelDelete(): void {
        this.confirmDelete = false;
        this.reminderIdToDelete = null;
        this.deletedCloName = '';
        this.activeRemindersLeft = 0;
    }
}