import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AllPopupBulkUploadComponent } from './all-popup-bulk-upload.component';
import { FileUploadService } from 'src/app/services/file-upload.service';
import { ToastrService,ToastrModule } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { FormsModule } from '@angular/forms';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { BulkuploadConstants, ModuleList } from 'src/app/common/constants';
import { HelperService } from 'src/app/services/helper.service';

describe('AllPopupBulkUploadComponent', () => {
  let component: AllPopupBulkUploadComponent;
  let fixture: ComponentFixture<AllPopupBulkUploadComponent>;
  let fileUploadService: FileUploadService;
  let toastrService: ToastrService;
  let helperService: HelperService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, ToastrModule.forRoot(), FormsModule], 
      declarations: [AllPopupBulkUploadComponent],
      providers: [FileUploadService, ToastrService, HelperService, { provide: 'BASE_URL', useValue: 'http://localhost:4200/' }],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  });
  beforeEach(() => {
    fixture = TestBed.createComponent(AllPopupBulkUploadComponent);
    component = fixture.componentInstance;
    fileUploadService = TestBed.inject(FileUploadService);
    toastrService = TestBed.inject(ToastrService);
    helperService = TestBed.inject(HelperService);
    fixture.detectChanges();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AllPopupBulkUploadComponent);
    component = fixture.componentInstance;
    fileUploadService = TestBed.inject(FileUploadService);
    toastrService = TestBed.inject(ToastrService);
    fixture.detectChanges();
  });
 
  it('should emit toasterMessageEvent with error message when single file format is not valid', () => {
    // Arrange
    spyOn(component.toasterMessageEvent, 'emit');
    const file: File[] = [new File([''], 'invalidFile.txt')];

    // Act
    component.onSingleFileChange(file);

    // Assert
    expect(component.toasterMessageEvent.emit).toHaveBeenCalledWith({ message: BulkuploadConstants.ValidateExcelFormat, type: BulkuploadConstants.error });
    expect(component.singleUploadFiles).toEqual([]);
  });


  it('should emit toasterMessageEvent with error message when singleUploadFiles length is 0', () => {
    // Arrange
    spyOn(component.toasterMessageEvent, 'emit');
    component.singleUploadFiles = [];

    // Act
    component.onSubmit();

    // Assert
    expect(component.toasterMessageEvent.emit).toHaveBeenCalledWith({
      message: BulkuploadConstants.ValidateEmptyFile,
      type: BulkuploadConstants.error,
    });
  });

  it('should call onSubmitESG when moduleName is ESG', () => {
    // Arrange
    spyOn(component, 'onSubmitESG');
    component.moduleName = ModuleList.ESG;
    component.singleUploadFiles = [new File([''], 'validFile.xlsx')];
    component.companyId = 1;
    component.comments = 'comments';

    // Act
    component.onSubmit();

    // Assert
    expect(component.onSubmitESG).toHaveBeenCalled();
  });

  it('should call onSubmitFinancials when moduleName is not ESG', () => {
    // Arrange
    spyOn(component, 'onSubmitFinancials');
    component.moduleName = 'SomeOtherModule';
    component.singleUploadFiles = [new File([''], 'validFile.xlsx')];
    component.companyId = 1;
    component.comments = 'comments';

    // Act
    component.onSubmit();

    // Assert
    expect(component.onSubmitFinancials).toHaveBeenCalled();
  });

  describe('onSubmitESG', () => {
    it('should call fileUploadService.UploadESG and emit confirmButtonEvent with result when UploadESG succeeds', () => {
      // Arrange
      const formData = new FormData();
      const mockResult = 'mock result';
      spyOn(fileUploadService, 'UploadESG').and.returnValue(of(mockResult));
      spyOn(component.confirmButtonEvent, 'emit');
      component.singleUploadFiles = [new File([''], 'validFile.xlsx')];
      component.isLoader = false;

      // Act
      component.onSubmitESG(formData);

      // Assert
      expect(fileUploadService.UploadESG).toHaveBeenCalledWith(formData);
      expect(component.isLoader).toBe(false);
      expect(component.confirmButtonEvent.emit).toHaveBeenCalledWith(mockResult);
    });

    it('should emit confirmButtonEvent with error when UploadESG throws an error', () => {
      // Arrange
      const formData = new FormData();
      const mockError = 'mock error';
      spyOn(fileUploadService, 'UploadESG').and.returnValue(throwError(mockError));
      spyOn(component.confirmButtonEvent, 'emit');
      component.singleUploadFiles = [new File([''], 'validFile.xlsx')];
      component.isLoader = false;

      // Act
      component.onSubmitESG(formData);

      // Assert
      expect(fileUploadService.UploadESG).toHaveBeenCalledWith(formData);
      expect(component.isLoader).toBe(false);
      expect(component.confirmButtonEvent.emit).toHaveBeenCalledWith(mockError);
    });
  });

  describe('onSubmitFinancials', () => {
    it('should call fileUploadService.OnSubmitAllFilesUpload and emit confirmButtonEvent with result when the service call succeeds', () => {
      // Arrange
      const formData = new FormData();
      const mockResult = ['result'];
      spyOn(fileUploadService, 'OnSubmitAllFilesUpload').and.returnValue(of(mockResult));
      spyOn(component.confirmButtonEvent, 'emit');
      component.singleUploadFiles = [new File([''], 'validFile.xlsx')];
      component.moduleName = ModuleList.Financials;
      component.isLoader = false;

      // Act
      component.onSubmitFinancials(formData);

      // Assert
      expect(fileUploadService.OnSubmitAllFilesUpload).toHaveBeenCalledWith(formData);
      expect(component.isLoader).toBe(false);
      expect(component.confirmButtonEvent.emit).toHaveBeenCalledWith(mockResult);
    });

    it('should emit confirmButtonEvent with error when fileUploadService.OnSubmitAllFilesUpload throws an error', () => {
      // Arrange
      const formData = new FormData();
      const mockError = 'error';
      spyOn(fileUploadService, 'OnSubmitAllFilesUpload').and.returnValue(throwError(mockError));
      spyOn(component.confirmButtonEvent, 'emit');
      component.singleUploadFiles = [new File([''], 'validFile.xlsx')];
      component.moduleName = ModuleList.Financials;
      component.isLoader = false;

      // Act
      component.onSubmitFinancials(formData);

      // Assert
      expect(fileUploadService.OnSubmitAllFilesUpload).toHaveBeenCalledWith(formData);
      expect(component.isLoader).toBe(false);
      expect(component.confirmButtonEvent.emit).toHaveBeenCalledWith(mockError);
    });
  });

  describe('removeSingleDocumentFile', () => {
    it('should clear the singleUploadFiles array and reset the input value', () => {
      // Arrange
      component.singleUploadFiles = [new File([''], 'validFile.xlsx')];
      const nativeElement = fixture.nativeElement;
      const fileInput = nativeElement.querySelector('input[type="file"]');

      // Act
      component.removeSingleDocumentFile();

      // Assert
      expect(component.singleUploadFiles.length).toBe(0);
      expect(fileInput.value).toBe('');
    });
  });

  describe('removeSupportDocumentFile', () => {
    it('should remove the file at the specified index from the files array', () => {
      // Arrange
      component.files = ['file1', 'file2', 'file3'];

      // Act
      component.removeSupportDocumentFile(1);

      // Assert
      expect(component.files).toEqual(['file1', 'file3']);
    });

    it('should not throw an error if the index is out of range', () => {
      // Arrange
      component.files = ['file1', 'file2', 'file3'];

      // Act
      const removeFile = () => component.removeSupportDocumentFile(3);

      // Assert
      expect(removeFile).not.toThrowError();
      expect(component.files).toEqual(['file1', 'file2', 'file3']);
    });
  });
  it('should emit toasterMessageEvent with error message when single file format is not valid', () => {
    // Arrange
    spyOn(component.toasterMessageEvent, 'emit');
    const file: File[] = [new File([''], 'invalidFile.txt')];

    // Act
    component.onSingleFileChange(file);

    // Assert
    expect(component.toasterMessageEvent.emit).toHaveBeenCalledWith({
      message: BulkuploadConstants.ValidateExcelFormat,
      type: BulkuploadConstants.error
    });
    expect(component.singleUploadFiles).toEqual([]);
  });

  it('should set singleUploadFiles when single file format is valid', () => {
    // Arrange
    const file: File[] = [new File([''], 'validFile.xlsx')];

    // Act
    component.onSingleFileChange(file);

    // Assert
    expect(component.singleUploadFiles).toEqual(file);
  });

  it('should add valid files to the files array', () => {
    // Arrange
    const files = [
      { name: 'file1.xlsx' },
      { name: 'file2.xlsx' },
      { name: 'file3.docx' }
    ];

    // Act
    component.onFileChange(files);

    // Assert
    expect(component.files).toEqual(files);
  });

  it('should emit an error message when an exe file is selected', () => {
    // Arrange
    const files = [
      { name: 'file1.exe' }
    ];
    spyOn(component.toasterMessageEvent, 'emit');

    // Act
    component.onFileChange(files);

    // Assert
    expect(component.toasterMessageEvent.emit).toHaveBeenCalledWith({
      message: BulkuploadConstants.validateSupportingFileFormat,
      type: BulkuploadConstants.error
    });
    expect(component.files).toEqual([]);
  });
  it('should call helperService.getstaticIconPath with the provided name', () => {
    // Arrange
    const name = 'sample-icon';

    spyOn(helperService, 'getstaticIconPath');

    // Act
    component.getIcons(name);

    // Assert
    expect(helperService.getstaticIconPath).toHaveBeenCalledWith(name);
  });
});