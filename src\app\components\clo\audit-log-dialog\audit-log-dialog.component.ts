import { Component, Input, OnInit, HostListener } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { GridModule } from '@progress/kendo-angular-grid';
import { DialogModule } from '@progress/kendo-angular-dialog';
import { ButtonModule } from '@progress/kendo-angular-buttons';
import { AuditLogModel } from '../models/audit-log.model';
import { AuditLogService } from '../services/audit-log.service';
import { ToastrService } from 'ngx-toastr';

interface AuditLogEntry {
  currentValue: string;
  oldValue: string;
  source: string;
  sourceFile: string;
  sourceFileId:string;
  supportingEvidenceId:string;
  supportingEvidence: string;
  createdBy: string;
  userName:string;
  userNameSymbol:string;
  dateTime: string;
  comment: string;
}

@Component({
  selector: 'app-audit-log-dialog',
  templateUrl: './audit-log-dialog.component.html',
  styleUrls: ['./audit-log-dialog.component.scss'],
  standalone: true,
  imports: [DialogModule, GridModule, ButtonModule]
})
export class AuditLogDialogComponent implements OnInit {
  @Input() rowHeader: string = '';
  @Input() tableName: string = '';
  @Input() columnHeader: string = '';
  @Input() gridData: AuditLogModel[] = [];
  
  public displayData: AuditLogEntry[] = [];

  constructor(
    public activeModal: NgbActiveModal,
    private auditLogService: AuditLogService,
    private readonly toastrService: ToastrService
  ) {}

  ngOnInit(): void {
    // Transform the data when component initializes
    if (this.gridData) {
      this.displayData = this.gridData.map(item => this.transformToAuditLogEntry(item));
    }
  }

  private transformToAuditLogEntry(model: AuditLogModel): AuditLogEntry {
    return {
      currentValue: model.newValue,
      oldValue: model.oldValue,
      source: model.auditType === 0 ? 'File Upload' : 'Manual',
      sourceFile: model.auditType === 0 ? model.supportingDocumentName :'' ,
      sourceFileId:model.supportingDocumentId,
      supportingEvidence: model.auditType === 1 ?model.supportingDocumentName:'',
      supportingEvidenceId:model.supportingDocumentId,
      createdBy: `User ID: ${model.createdBy}`,
      comment: model.comment || '',
      userName: model.userName,
      userNameSymbol: model.userName.split(' ')
                     .filter(name => name.length > 0)
                     .map(name => name.charAt(0).toUpperCase())
                     .join(''),
      dateTime: new Date(model.modifiedOn).toLocaleString()
    };
  }

  closeModal(): void {
    this.activeModal.close();
  }

 downloadFile(downloadableItem: AuditLogEntry, isSupportingEvidence: boolean) {
    const documentId = isSupportingEvidence ? downloadableItem.supportingEvidenceId : downloadableItem.sourceFileId;
    const fileName = isSupportingEvidence ? downloadableItem.supportingEvidence : downloadableItem.sourceFile;
    
    if (!documentId) {
        this.toastrService.warning('No file available for download', '', { positionClass: 'toast-center-center' });
        return;
    }

    this.auditLogService.downloadDocument(documentId).subscribe({
        next: (response: Blob) => {
            const url = window.URL.createObjectURL(response);
            const link = document.createElement('a');
            link.href = this.sanitizeUrl(url);
            link.download = this.sanitizeFileName(fileName);
            
            link.click();
            window.URL.revokeObjectURL(url);
        },
        error: (error) => {
            console.error('Error downloading file', error);
            this.toastrService.error('Error downloading file', '', { positionClass: 'toast-center-center' });
        }
    });
  }

  private sanitizeUrl(url: string): string {
      // Use a URL object to ensure the URL is properly formatted
      const sanitizedUrl = new URL(url);
      return sanitizedUrl.href;
  }

  private sanitizeFileName(fileName: string): string {
      // Replace any character that is not a letter, number, underscore, hyphen, or dot
      return fileName.replace(/[^a-zA-Z0-9_\-\.]/g, '_');
  }
  
  openComments(dataItem: any) {
    // Implement comments opening logic
    console.log('Opening comments for:', dataItem);
  }

  toggleComments(dataItem: any, event: any) {
    // Prevent event bubbling
    event.stopPropagation();

    // Close all other open comments first
    this.displayData.forEach((item: any) => {
      if (item !== dataItem) {
        item.showComments = false;
      }
    });
    
    // Toggle the clicked item
    dataItem.showComments = !dataItem.showComments;

    // Position the popup
    if (dataItem.showComments) {
      setTimeout(() => {
        const button = event.target.closest('.comment-toggle-btn');
        const popup = event.target.closest('.comment-container').querySelector('.comment-popup');
        if (button && popup) {
          const rect = button.getBoundingClientRect();
          popup.style.left = `${rect.left}px`;
          popup.style.top = `${rect.bottom}px`;
        }
      });
    }
  }

  // Add click outside handler to close popup
  @HostListener('document:click', ['$event'])
  clickOut(event: any) {
    if (!event.target.closest('.comment-container')) {
      this.displayData.forEach((item: any) => {
        item.showComments = false;
      });
    }
  }
}
