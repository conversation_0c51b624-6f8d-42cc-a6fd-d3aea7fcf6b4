<div>
    <div class="email-container d-flex">
        <nep-tab id="email-config-tabs" class="repository-tabs flex-grow-1" [tabList]="emailConfigTabs"
            (OnSelectTab)="onTabClick($event)">
        </nep-tab>
        <div class="d-flex align-items-center" *ngIf="activeTab === 'Email Groups'">
            <button class="btn btn-primary pl-2 pr-2 d-flex align-items-center justify-content-center"
                id="id-create-email-group" kendoButton themeColor="primary" [routerLink]="['/add-email-group']">
                <div class="d-flex align-items-center justify-content-center">
                    <img src="assets/dist/images/add-portfolio.svg" alt="Add Icon" class="mr-2">
                    <span class="text-center Body-R"> {{createEmailGroupText}}</span>
                </div>
            </button>
        </div>
    </div>
    <div class="content-container mt-3">
        <ng-container *ngIf="activeTab === 'User Info by Company'">
            <div class="row">
                <div class="col-md-3 pr-0">
                    <app-email-configuration-company-list
                        (selectedCompanies)="onCompanySelected($event)"></app-email-configuration-company-list>
                </div>
                <div class="col-md-9 pl-0"> <app-email-configuration-users  [selectedCompanies]="selectedCompanies"></app-email-configuration-users></div>
            </div>
        </ng-container>
        <ng-container *ngIf="activeTab === 'Email Groups'">
            <div class="row mr-1 ml-1 mb-1">
                <div class="col-md-12 pl-0 pr-0"> <app-email-group [selectedCompanies]="selectedCompanies"></app-email-group></div>
            </div>
        </ng-container>
    </div>
</div>