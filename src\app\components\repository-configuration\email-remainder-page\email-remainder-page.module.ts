import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

// Kendo UI imports
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { ButtonsModule } from '@progress/kendo-angular-buttons';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';

import { InputsModule } from "@progress/kendo-angular-inputs";
import { LabelModule } from "@progress/kendo-angular-label";
// Quill Editor
import { QuillModule } from 'ngx-quill';

// Custom modules
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';

// Component
import { EmailRemainderPageComponent } from './email-remainder-page.component';

@NgModule({
  declarations: [
    EmailRemainderPageComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    SharedComponentModule,
    KendoModule,
    DropDownsModule,
    ButtonsModule,
    InputsModule,
    DateInputsModule,
    LabelModule,
    QuillModule.forRoot(),
    RouterModule.forChild([
      {
        path: '',
        component: EmailRemainderPageComponent
      }
    ])
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [
    EmailRemainderPageComponent
  ]
})
export class EmailRemainderPageModule { }
