import { Component, Input } from '@angular/core';
import { ManagedAccountService } from '../managed-account.service';

@Component({
  selector: 'managed-account-review-form',
  templateUrl: './managed-account-review-form.component.html',
  styleUrls: ['./managed-account-review-form.component.scss']
})
export class ManagedAccountReviewFormComponent {
  @Input() ManagedAccountModel: any;
  formattedManagedAccountModel: any;
  
  constructor(private managedAccountService: ManagedAccountService) {}
  
  ngOnInit(): void {
    this.formattedManagedAccountModel = { ...this.ManagedAccountModel };
  }

  triggerGoToStep(step: number): void {
    this.managedAccountService.emitGoToStep(step); 
  }
}
