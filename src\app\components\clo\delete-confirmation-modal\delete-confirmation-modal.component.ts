import { Component, Input, Output, EventEmitter} from '@angular/core';
import { CLOConstants } from 'src/app/common/constants';

@Component({
  selector: 'app-delete-confirmation-modal',
  templateUrl: './delete-confirmation-modal.component.html',
  styleUrls: ['./delete-confirmation-modal.component.scss']
})
export class DeleteConfirmationModalComponent {
@Input() deletedCloName:string ="";
@Output() PrimaryButtonEventHandler = new EventEmitter<any>();
@Output() SecondaryButtonEventHandler = new EventEmitter<any>();
@Input() modalTitle: string = "";
@Input() deleteNoteType: string ="";
@Input() isTableLevel: boolean =false;
@Input() confirmBtn: string ="Delete";
@Input() cancelBtn: string ="Cancel";
@Input() hideNote: boolean = false;
@Input() customEmailDeleteMsg: boolean = false;
@Input() activeRemindersLeft: number = 0;
Delete_Confirmation_Body_1: string = CLOConstants.Delete_Confirmation_Body_1;
Delete_Confirmation_Body_2: string = CLOConstants.Delete_Confirmation_Body_2;
Delete_Note: string = CLOConstants.Delete_Note;
Delete_Note_1: string = CLOConstants.Delete_Note_1;
Delete_Note_2: string = CLOConstants.Delete_Note_2;
Delete_Note_3: string = CLOConstants.Delete_Note_3;
Delete_Table_Note_1: string = CLOConstants.Delete_Table_Note_1;
Delete_Table_Note_2: string = CLOConstants.Delete_Table_Note_2;


OnPrimaryButtonEvent(){
  this.PrimaryButtonEventHandler.emit();
}

OnSecondaryButtonEvent(){
  this.SecondaryButtonEventHandler.emit();
}
}
