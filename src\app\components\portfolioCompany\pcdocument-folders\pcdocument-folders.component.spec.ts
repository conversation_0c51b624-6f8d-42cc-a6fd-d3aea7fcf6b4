import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { PcdocumentFoldersComponent } from './pcdocument-folders.component';
import { ToastrService } from 'ngx-toastr';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { of, throwError } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('PcdocumentFoldersComponent', () => {
  let component: PcdocumentFoldersComponent;
  let fixture: ComponentFixture<PcdocumentFoldersComponent>;
  let toastrServiceSpy: jasmine.SpyObj<ToastrService>;
  let repositoryConfigServiceSpy: jasmine.SpyObj<RepositoryConfigService>;

  beforeEach(async () => {
    toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning']);
    repositoryConfigServiceSpy = jasmine.createSpyObj('RepositoryConfigService', ['getRepositoryStructureData']);

    await TestBed.configureTestingModule({
      declarations: [PcdocumentFoldersComponent],
      providers: [
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: RepositoryConfigService, useValue: repositoryConfigServiceSpy }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(PcdocumentFoldersComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should fetch tree data on ngOnInit if PortfolioCompanyId is set', fakeAsync(() => {
    component.documentsFieldPageConfig = [];
    component.PortfolioCompanyId = '123';
    repositoryConfigServiceSpy.getRepositoryStructureData.and.returnValue(of({ isSuccess: true, data: [{ name: 'Folder1', isExpanded: false }] }));
    component.ngOnInit();
    tick();
    expect(component.isLoading).toBeFalse();
    expect(component.treeData.length).toBe(1);
    expect(component.filteredTreeData.length).toBe(1);
  }));

  it('should show error if getRepositoryStructureData fails', fakeAsync(() => {
    component.documentsFieldPageConfig = [];
    component.PortfolioCompanyId = '123';
    repositoryConfigServiceSpy.getRepositoryStructureData.and.returnValue(of({ isSuccess: false, message: 'Error' }));
    component.ngOnInit();
    tick();
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Error');
  }));

  it('should show error on getRepositoryStructureData error', fakeAsync(() => {
    component.documentsFieldPageConfig = [];
    component.PortfolioCompanyId = '123';
    repositoryConfigServiceSpy.getRepositoryStructureData.and.returnValue(throwError(() => ({})));
    component.ngOnInit();
    tick();
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Error while fetching data');
  }));

  it('should filter folders by search term', () => {
    component.treeData = [
      { name: 'Alpha', isExpanded: false },
      { name: 'Beta', isExpanded: false }
    ];
    component.searchTerm = 'alp';
    component.filterFolders();
    expect(component.filteredTreeData.length).toBe(1);
    expect(component.filteredTreeData[0].name).toBe('Alpha');
  });

  it('should reset filteredTreeData if searchTerm is empty', () => {
    component.treeData = [
      { name: 'Alpha', isExpanded: false },
      { name: 'Beta', isExpanded: false }
    ];
    component.searchTerm = '';
    component.filterFolders();
    expect(component.filteredTreeData.length).toBe(2);
  });

  it('should emit folderSelected with correct path and isFromUnconfig', () => {
    spyOn(component.folderSelected, 'emit');
    const node = { name: 'Un-Configured/Folder', isExpanded: false, path: 'Un-Configured/Folder' };
    component.unconfigured = 'Un-Configured';
    component.toggleFolder(node as any);
    expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 'Folder', isFromUnconfig: true });
  });

  it('should emit folderSelected with isFromUnconfig false for normal folder', () => {
    spyOn(component.folderSelected, 'emit');
    const node = { name: 'Folder', isExpanded: false, path: 'Folder' };
    component.toggleFolder(node as any);
    expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 'Folder', isFromUnconfig: false });
  });

  describe('toggleSort', () => {
    beforeEach(() => {
      // Setup test data with children nodes
      component.filteredTreeData = [
        {
          name: 'Folder A',
          isExpanded: false,
          children: [
            { name: 'Subfolder Z', isExpanded: false },
            { name: 'Subfolder A', isExpanded: false },
            { name: 'Subfolder M', isExpanded: false }
          ]
        },
        {
          name: 'Folder B',
          isExpanded: false,
          children: [
            { name: 'Subfolder Y', isExpanded: false },
            { name: 'Subfolder B', isExpanded: false },
            { name: 'Subfolder N', isExpanded: false }
          ]
        }
      ];
    });

    it('should toggle sort mode from desc to asc', () => {
      spyOn(component.folderSelected, 'emit');
      expect(component.sortMode).toBe('desc');
      component.toggleSort();
      expect(component.sortMode).toBe('asc');
      expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 0, isFromUnconfig: true });
    });

    it('should toggle sort mode from asc to desc', () => {
      spyOn(component.folderSelected, 'emit');
      component.sortMode = 'asc';
      component.toggleSort();
      expect(component.sortMode).toBe('desc');
      expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 0, isFromUnconfig: true });
    });

    it('should sort children in ascending order when starting with sortMode desc and calling toggleSort', () => {
      spyOn(component.folderSelected, 'emit');
      component.sortMode = 'desc';
      component.toggleSort();
      expect(component.sortMode).toBe('asc');
      expect(component.filteredTreeData[0].children![0].name).toBe('Subfolder A');
      expect(component.filteredTreeData[0].children![1].name).toBe('Subfolder M');
      expect(component.filteredTreeData[0].children![2].name).toBe('Subfolder Z');
      
      expect(component.filteredTreeData[1].children![0].name).toBe('Subfolder B');
      expect(component.filteredTreeData[1].children![1].name).toBe('Subfolder N');
      expect(component.filteredTreeData[1].children![2].name).toBe('Subfolder Y');
      
      expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 0, isFromUnconfig: true });
    });

    it('should sort children in descending order when starting with sortMode asc and calling toggleSort', () => {
      spyOn(component.folderSelected, 'emit');
      component.sortMode = 'asc';
      component.toggleSort();
      expect(component.sortMode).toBe('desc');
      expect(component.filteredTreeData[0].children![0].name).toBe('Subfolder Z');
      expect(component.filteredTreeData[0].children![1].name).toBe('Subfolder M');
      expect(component.filteredTreeData[0].children![2].name).toBe('Subfolder A');
      
      expect(component.filteredTreeData[1].children![0].name).toBe('Subfolder Y');
      expect(component.filteredTreeData[1].children![1].name).toBe('Subfolder N');
      expect(component.filteredTreeData[1].children![2].name).toBe('Subfolder B');
      
      expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 0, isFromUnconfig: true });
    });

    it('should handle nodes without children', () => {
      spyOn(component.folderSelected, 'emit');
      component.filteredTreeData = [
        { name: 'Folder A', isExpanded: false },
        { name: 'Folder B', isExpanded: false }
      ];
      
      expect(() => component.toggleSort()).not.toThrow();
      expect(component.sortMode).toBe('asc');
      expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 0, isFromUnconfig: true });
    });

    it('should handle empty children array', () => {
      spyOn(component.folderSelected, 'emit');
      component.filteredTreeData = [
        {
          name: 'Folder A',
          isExpanded: false,
          children: []
        }
      ];
      
      expect(() => component.toggleSort()).not.toThrow();
      expect(component.sortMode).toBe('asc');
      expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 0, isFromUnconfig: true });
    });

    it('should handle mixed nodes with and without children', () => {
      spyOn(component.folderSelected, 'emit');
      component.filteredTreeData = [
        {
          name: 'Folder A',
          isExpanded: false,
          children: [
            { name: 'Subfolder Z', isExpanded: false },
            { name: 'Subfolder A', isExpanded: false }
          ]
        },
        { name: 'Folder B', isExpanded: false },
        {
          name: 'Folder C',
          isExpanded: false,
          children: [
            { name: 'Subfolder Y', isExpanded: false },
            { name: 'Subfolder B', isExpanded: false }
          ]
        }
      ];
      
      component.toggleSort();
      expect(component.sortMode).toBe('asc');
      expect(component.filteredTreeData[0].children![0].name).toBe('Subfolder A');
      expect(component.filteredTreeData[0].children![1].name).toBe('Subfolder Z');
      expect(component.filteredTreeData[2].children![0].name).toBe('Subfolder B');
      expect(component.filteredTreeData[2].children![1].name).toBe('Subfolder Y');
      
      expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 0, isFromUnconfig: true });
    });

    it('should create a new array reference to trigger change detection', () => {
      spyOn(component.folderSelected, 'emit');
      const originalArray = component.filteredTreeData;
      component.toggleSort();
      expect(component.filteredTreeData).not.toBe(originalArray);
      expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 0, isFromUnconfig: true });
    });

    it('should handle case-insensitive sorting', () => {
      spyOn(component.folderSelected, 'emit');
      component.filteredTreeData = [
        {
          name: 'Folder A',
          isExpanded: false,
          children: [
            { name: 'subfolder z', isExpanded: false },
            { name: 'Subfolder A', isExpanded: false },
            { name: 'SUBFOLDER M', isExpanded: false }
          ]
        }
      ];
      
      component.toggleSort();

      expect(component.sortMode).toBe('asc');
      expect(component.filteredTreeData[0].children![0].name).toBe('Subfolder A');
      expect(component.filteredTreeData[0].children![1].name).toBe('SUBFOLDER M');
      expect(component.filteredTreeData[0].children![2].name).toBe('subfolder z');
      
      expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 0, isFromUnconfig: true });
    });

    it('should sort children by index property when available', () => {
      spyOn(component.folderSelected, 'emit');
      component.filteredTreeData = [
        {
          name: 'Folder A',
          isExpanded: false,
          children: [
            { name: 'Subfolder Z', isExpanded: false, index: 3 },
            { name: 'Subfolder A', isExpanded: false, index: 1 },
            { name: 'Subfolder M', isExpanded: false, index: 2 }
          ]
        }
      ];
      
      component.toggleSort();
      
      // Should sort by index in ascending order
      expect(component.sortMode).toBe('asc');
      expect(component.filteredTreeData[0].children![0].name).toBe('Subfolder A');
      expect(component.filteredTreeData[0].children![0].index).toBe(1);
      expect(component.filteredTreeData[0].children![1].name).toBe('Subfolder M');
      expect(component.filteredTreeData[0].children![1].index).toBe(2);
      expect(component.filteredTreeData[0].children![2].name).toBe('Subfolder Z');
      expect(component.filteredTreeData[0].children![2].index).toBe(3);
      
      expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 0, isFromUnconfig: true });
    });

    it('should sort children by index in descending order', () => {
      spyOn(component.folderSelected, 'emit');
      component.filteredTreeData = [
        {
          name: 'Folder A',
          isExpanded: false,
          children: [
            { name: 'Subfolder Z', isExpanded: false, index: 3 },
            { name: 'Subfolder A', isExpanded: false, index: 1 },
            { name: 'Subfolder M', isExpanded: false, index: 2 }
          ]
        }
      ];
      
      component.sortMode = 'asc'; // Start with asc so toggleSort changes to desc
      component.toggleSort();
      
      // Should sort by index in descending order
      expect(component.sortMode).toBe('desc');
      expect(component.filteredTreeData[0].children![0].name).toBe('Subfolder Z');
      expect(component.filteredTreeData[0].children![0].index).toBe(3);
      expect(component.filteredTreeData[0].children![1].name).toBe('Subfolder M');
      expect(component.filteredTreeData[0].children![1].index).toBe(2);
      expect(component.filteredTreeData[0].children![2].name).toBe('Subfolder A');
      expect(component.filteredTreeData[0].children![2].index).toBe(1);
      
      expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 0, isFromUnconfig: true });
    });

    it('should sort children by type and index (quarterly and monthly only)', () => {
      component.filteredTreeData = [
        {
          name: 'Folder A',
          isExpanded: false,
          children: [
            {
              name: 'Year 2023',
              isExpanded: false,
              children: [
                { name: 'Q1', isExpanded: false, type: 'quarterly', index: 1 },
                { name: 'Q2', isExpanded: false, type: 'quarterly', index: 2 },
                { name: 'Q3', isExpanded: false, type: 'quarterly', index: 3 },
                { name: 'Q4', isExpanded: false, type: 'quarterly', index: 4 },
                { name: 'Jan', isExpanded: false, type: 'monthly', index: 1 },
                { name: 'Feb', isExpanded: false, type: 'monthly', index: 2 },
                { name: 'Mar', isExpanded: false, type: 'monthly', index: 3 }
              ]
            }
          ]
        }
      ];
      
      spyOn(component.folderSelected, 'emit');
      component.toggleSort();
      
      // Should group by type and sort by index within each group
      expect(component.sortMode).toBe('asc');
      const children = component.filteredTreeData[0].children![0].children!;
      
      // First 4 should be quarterly (Q1-Q4)
      expect(children[0].name).toBe('Q1');
      expect(children[0].type).toBe('quarterly');
      expect(children[1].name).toBe('Q2');
      expect(children[1].type).toBe('quarterly');
      expect(children[2].name).toBe('Q3');
      expect(children[2].type).toBe('quarterly');
      expect(children[3].name).toBe('Q4');
      expect(children[3].type).toBe('quarterly');
      
      // Next 3 should be monthly (Jan-Mar)
      expect(children[4].name).toBe('Jan');
      expect(children[4].type).toBe('monthly');
      expect(children[5].name).toBe('Feb');
      expect(children[5].type).toBe('monthly');
      expect(children[6].name).toBe('Mar');
      expect(children[6].type).toBe('monthly');
      
      // Should emit folderSelected event
      expect(component.folderSelected.emit).toHaveBeenCalledWith({ folderPath: 0, isFromUnconfig: true });
    });
  });
});
