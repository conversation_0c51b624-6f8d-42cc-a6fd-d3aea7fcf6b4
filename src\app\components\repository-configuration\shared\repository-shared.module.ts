import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RepositoryConfigurationCompanyListComponent } from '../repo-config-comp-list/repo-config-comp-list.component';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { DropDownsModule, ComboBoxModule, DropDownListModule } from '@progress/kendo-angular-dropdowns';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { LabelModule } from "@progress/kendo-angular-label";
import { InputsModule } from "@progress/kendo-angular-inputs";

@NgModule({
  declarations: [
    RepositoryConfigurationCompanyListComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    KendoModule,
    DropDownsModule,
    ComboBoxModule,
    DropDownListModule,
    LabelModule,
    InputsModule,
    SharedComponentModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [
    RepositoryConfigurationCompanyListComponent
  ]
})
export class RepositorySharedModule { }
