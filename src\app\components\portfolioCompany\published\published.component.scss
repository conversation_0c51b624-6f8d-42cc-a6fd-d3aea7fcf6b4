    @import "../../../../variables";
    .portfolio-company-list-table {
        .companyListSearchHeight {
            height: 42px !important;
            width: 400px !important;
            padding-top: 0px !important;
            padding-bottom: 0px !important;
        }
        .filter-bg {
            margin-top: -2px !important;
        }
        .nav-link {
            font-size: 14px !important;
            .fa {
                padding-right: 4px !important;
            }
        }
        .fa-globe {
            color: $nep-primary !important;
            font-size: 18px !important;
        }
        .textsplit {
            margin: 0px 12px;
        }
        a {
            color: $nep-black;
        }
        .cloud-icon {
            border-right: 1px solid $nep-divider;
        }
        .showHandIcon {
            margin-top: -4px !important;
            padding: 9px 12px;
        }
    }
    
    .nep-modal {
        .nep-card {
            width: 452px !important;
            top: 15% !important;
            .nep-card-header {
                .user-header {
                    .TextTruncate{
                        color: #1A1A1A !important;
                    }
                }
            }
            .nep-card-body {
                padding: 20px !important;
                label {
                    margin-top: 0px;
                    letter-spacing: 0px;
                    color: #55565A;
                    opacity: 1;
                    margin-bottom: 4px !important;
                }
                .control-padding {
                    padding-top: 16px;
                }
                .form-group {
                    margin: 0px !important;
                }
                .form-group.required .control-label:after {
                    content: "*";
                    color: red;
                }
            }
        }
    }
    .fund-list-table
{
    tr{
        td:first-child,th:first-child
        {
            max-width: 200px !important;
        }
        th{
            border:none !important;
            padding: 12px 16px !important;
        }
    }
    thead{
        tr{
            border-bottom: 1px solid $nep-divider !important;
        }
    }
}

.sector-width
{
    width:75% !important;
}

