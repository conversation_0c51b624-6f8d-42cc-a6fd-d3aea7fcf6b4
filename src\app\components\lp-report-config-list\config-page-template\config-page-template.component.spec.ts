import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { of } from 'rxjs';
import { LpReportConfigService } from 'src/app/services/lp-report-config.service';
import { ConfigPageTemplateComponent } from './config-page-template.component';
import { NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { LabelModule } from '@progress/kendo-angular-label';
import { InputsModule } from '@progress/kendo-angular-inputs';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { LayoutModule } from '@progress/kendo-angular-layout';
import { ButtonsModule } from '@progress/kendo-angular-buttons';
import { UploadModule } from '@progress/kendo-angular-upload';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('ConfigPageTemplateComponent', () => {
  let component: ConfigPageTemplateComponent;
  let fixture: ComponentFixture<ConfigPageTemplateComponent>;
  let mockLpReportConfigService: jasmine.SpyObj<LpReportConfigService>;
  let mockFormBuilder: FormBuilder;
  let mockToastrService: jasmine.SpyObj<ToastrService>;

  const mockTemplateData = [{
    id: 1,
    displayName: 'LP Report Template',
    fundNameOption: 'asPerFundName',
    fundAlignment: 'Left',
    fundFontSize: '12px',
    fundFontType: 'Arial',
    fundColor: '#000000',
    reportAlignment: 'Center',
    reportFontSize: '14px',
    reportFontType: 'Helvetica',
    reportColor: '#333333',
    logoAlignment: 'Right',
    defaultSetUp: true,
    defaultSignUpPage: false,
    displayFund: true,
    logoImageId: null,
    backgroundImageId: null
  }];

  beforeEach(() => {
    mockLpReportConfigService = jasmine.createSpyObj('LpReportConfigService', 
      ['getLpReportCoverPageTemplate', 'saveLpReportCoverTemplate']);
    mockToastrService = jasmine.createSpyObj('ToastrService', ['success', 'error']);
    
    mockLpReportConfigService.getLpReportCoverPageTemplate.and.returnValue(of(mockTemplateData));
    mockLpReportConfigService.saveLpReportCoverTemplate.and.returnValue(of({}));

    TestBed.configureTestingModule({
      declarations: [ConfigPageTemplateComponent],
      imports: [
        ReactiveFormsModule,
        LabelModule,
        InputsModule,
        DropDownsModule,
        LayoutModule,
        ButtonsModule,
        UploadModule,
        NoopAnimationsModule  // Add NoopAnimationsModule for testing
      ],
      providers: [
        FormBuilder,
        { provide: LpReportConfigService, useValue: mockLpReportConfigService },
        { provide: ToastrService, useValue: mockToastrService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
    });
    
    fixture = TestBed.createComponent(ConfigPageTemplateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.form).toBeDefined();
    expect(component.form.get('displayName')).toBeDefined();
    expect(component.form.get('fundNameOption')).toBeDefined();
  });

  it('should load template data on init', () => {
    expect(mockLpReportConfigService.getLpReportCoverPageTemplate).toHaveBeenCalled();
    expect(component.coverPageId).toBe(1);
    expect(component.selectedFundAlignment).toBe('Left');
  });

  it('should update form model correctly', () => {
    const formData = component.updateFormModel();
    expect(formData instanceof FormData).toBeTruthy();
  });

  it('should handle file upload errors correctly', () => {
    const invalidExtensions = ['.exe', '.bat'];
    const largeFiles: File[] = [];
    component.handleFileErrors(invalidExtensions, largeFiles);
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  it('should toggle save button state when form value changes', () => {
    component.initialFormValue = component.form.getRawValue();
    component.form.get('displayName').setValue('New Name');
    component.checkEnableSaveButton();
    expect(component.isSaveEnabled).toBeTrue();
  });

  it('should call save service when form is submitted', () => {
    component.form.get('displayName').setValue('New Template');
    component.onSubmit();
    expect(mockLpReportConfigService.saveLpReportCoverTemplate).toHaveBeenCalled();
  });
});
