@import "../../../variables.scss";

.lpTemplateConfigContainer {
    height: 92vh;
    margin: -20px;
}

.pr12 {
    padding-right: 12px;
}

.hfContainer {
    background: $nep-white 0% 0% no-repeat padding-box;
    border-top: 1px solid $nep-divider;
    padding: 12px 20px;
}

.bContainer {
    background: $nep-white 0% 0% no-repeat padding-box;
    height: 83%;
    margin-top: 10px !important;
}

.ph {
    padding: 0.5rem 0rem !important;
}

.bborder {
    border-bottom: 1px solid $nep-divider;
}

.paddingtop {
    padding-top: 1.5vh !important;
}

.nep-button {
    color: red !important
}

.justify {
    padding: 3px 12px 6px 12px !important;
    background-color: white;
    border: 1px solid $nep-primary;
    border-radius: 4px 0px 0px 4px;
}

.group {
    padding: 3px 12px 6px 12px !important;
    background-color: $nep-primary;
    border: 1px solid $nep-primary;
    border-radius: 0px 4px 4px 0px;
}

.plus {
    position: relative;
    top: 1px;
}

.preview {
    position: relative;
    top: -2px;
}

.fixed-footer {
    position: fixed;
    bottom: 0;
    right: 0;
}

.drag-icon {
    position: relative;
}

.drag-icon img {
    cursor: move;
}

.box {
    border: solid 1px $nep-panel-border;
    // margin-bottom: 8px;
    border-radius: 4px !important;
}

.box-border-right {
    border-right: solid 1px $nep-panel-border;
    padding: 22px 8px 22px 8px;
}

.align {
    vertical-align: -32% !important;

    img,
    em {
        cursor: pointer;
    }
}

.box-border-left {
    border-left: solid 1px $nep-panel-border;
    padding: 22px 8px 22px 8px;

    img,
    em {
        cursor: pointer;
    }
}

.cdk-drag-preview {
    box-sizing: border-box;
    border-radius: 4px;
    //box-shadow: 0px 2px 8px $nep-shadow;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
    background: #FAFAFC;
}

.main-row {
    padding: 20px !important;
}

.fullwidthselector {
    padding: 22px 40px !important;
    letter-spacing: 0px;
    color: $nep-fullwidth-selector;
    opacity: 1;
}

.cdk-drag-placeholder {
    opacity: 0;
}

.cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.p-left {
    padding-left: 8px !important;
}

.p-all {
    padding: 12px 0px 12px 12px !important
}

.p-all-row {
    padding: 20px 20px 0px 20px !important
}

.mat-radio-group {
    display: flex;
    flex-flow: column nowrap;
    padding-top: 20px;
}

.ff {
    color: $nep-button-primary;
}

.disabledNoOfCasesDiv {
    pointer-events: none;
    opacity: 0.4;
}

.padding-radio {
    padding-top: 20px;
    color: #B7B8B9;

    span {
        padding-left: 8px;
        color: #55565A;
        // opacity: 40%;
        cursor: pointer;
    }
}

.padding-radio-save {
    padding-top: 8px;

    span {
        padding-left: 8px;
        cursor: pointer;
    }

    p-radiobutton {
        .p-radiobutton-box {
            border-color: $nep-button-primary;
            background: $nep-button-primary;
        }
    }
}

.all-padding {
    padding: 22px 0px 12px 22px !important;
}

.ff-inactive {
    color: #75787B;
}

.kpis-custom-select {
    input {
        padding-left: 0px !important;
    }
}

.p-all-row:last-child {
    margin-bottom: 80px !important;
}

.p-all-row:first-child {
    padding-top: 10px !important;
}

.boxshadow {
    box-shadow: 0px 5px 8px #00000014;
}

.lptemplate-footer {
    box-shadow: 0px -3px 6px #00000014 !important;
}

.disabled-div {
    pointer-events: none;
    background: #FFFFFF 0% 0% no-repeat padding-box;  
    opacity: 0.4;
}

.alignment-comp-name{
    position: absolute;
    top: 0.781rem;
}

.fs12{
    font-size: 12px!important;
}

.comp-name-align-icons{
    top: 4px;
    position: relative;
}

.comp-logo{
    padding-left: 4rem !important;
}