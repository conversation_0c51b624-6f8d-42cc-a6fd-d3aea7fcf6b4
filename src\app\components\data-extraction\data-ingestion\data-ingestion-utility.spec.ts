import { DataIngestionUtility } from './data-Ingestion-utility';
import { DataIngestionConstants } from 'src/app/common/constants';
import { FileConfig, SelectedFile } from './data-ingestion.model';
import { 
  filePdfIcon, 
  fileExcelIcon, 
  fileWordIcon, 
  fileImageIcon 
} from "@progress/kendo-svg-icons";

describe('DataIngestionUtility', () => {
  let mockFileConfig: FileConfig;
  let mockExistingFiles: Set<string>;

  beforeEach(() => {
    mockFileConfig = {
      maxSize: 10 * 1024 * 1024, // 10MB
      allowedTypes: ['pdf'],
      maxFiles: 25
    };
    mockExistingFiles = new Set<string>();
  });

  describe('getMonthsList', () => {
    it('should return 12 months in English', () => {
      const months = DataIngestionUtility.getMonthsList();
      expect(months.length).toBe(12);
      expect(months[0]).toBe('January');
      expect(months[11]).toBe('December');
    });
  });

  describe('getYearsList', () => {
    it('should return correct year range', () => {
      const currentYear = new Date().getFullYear();
      const startYear = currentYear - 5;
      const years = DataIngestionUtility.getYearsList(startYear);
      
      expect(years.length).toBe(6); // Current year + 5 previous years
      expect(years[0]).toBe(currentYear);
      expect(years[years.length - 1]).toBe(startYear);
    });
  });
  describe('getQuartersList', () => {
    it('should return 4 quarters', () => {
      const quarters = DataIngestionUtility.getQuartersList();
      expect(quarters).toEqual(['Q1', 'Q2', 'Q3', 'Q4']);
    });
  });

  describe('getPeriodText', () => {
    it('should return correct period text for different periods', () => {
      expect(DataIngestionUtility.getPeriodText('Quarter')).toBe(DataIngestionConstants.QUARTER_SELECTION);
      expect(DataIngestionUtility.getPeriodText('Month')).toBe(DataIngestionConstants.MONTH_SELECTION);
      expect(DataIngestionUtility.getPeriodText('Year')).toBe(DataIngestionConstants.YEAR_SELECTION);
      expect(DataIngestionUtility.getPeriodText(null)).toBe(DataIngestionConstants.DEFAULT_PERIOD_SELECTION);
    });
  });

  describe('sortFilesByStatusAndName', () => {
    it('should sort files by status and name correctly', () => {
      const mockFiles: SelectedFile[] = [
        { id: '1', name: 'B.pdf', status: DataIngestionConstants.VALID } as SelectedFile,
        { id: '2', name: 'A.pdf', status: DataIngestionConstants.INVALID } as SelectedFile,
        { id: '3', name: 'C.pdf', status: DataIngestionConstants.VALID } as SelectedFile,
        { id: '4', name: 'document (1).pdf', status: DataIngestionConstants.VALID } as SelectedFile,
        { id: '5', name: 'document (2).pdf', status: DataIngestionConstants.VALID } as SelectedFile
      ];

      const sortedFiles = DataIngestionUtility.sortFilesByStatusAndName(mockFiles);
      
      expect(sortedFiles[0].status).toBe(DataIngestionConstants.INVALID);
      expect(sortedFiles[1].name).toBe('B.pdf');
      expect(sortedFiles[2].name).toBe('C.pdf');
      expect(sortedFiles[3].name).toBe('document (1).pdf');
      expect(sortedFiles[4].name).toBe('document (2).pdf');
    });
  });

  describe('validateFile', () => {
    it('should validate PDF file correctly', () => {
      const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
      Object.defineProperty(mockFile, 'size', { value: 5 * 1024 * 1024 }); // 5MB

      const result = DataIngestionUtility.validateFile(mockFile, mockFileConfig, mockExistingFiles);
      expect(result.isValid).toBeTruthy();
      expect(result.errors.length).toBe(0);
    });

    it('should detect duplicate files', () => {
      mockExistingFiles.add('test.pdf');
      const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
      
      const result = DataIngestionUtility.validateFile(mockFile, mockFileConfig, mockExistingFiles);
      expect(result.isValid).toBeFalsy();
      expect(result.errors).toContain(DataIngestionConstants.DUPLICATE_FILE_ERROR);
    });

    it('should validate file size', () => {
      const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
      Object.defineProperty(mockFile, 'size', { value: 20 * 1024 * 1024 }); // 20MB
      
      const result = DataIngestionUtility.validateFile(mockFile, mockFileConfig, mockExistingFiles);
      expect(result.isValid).toBeFalsy();
      expect(result.errors).toContain(DataIngestionConstants.FILE_SIZE_ERROR(10));
    });
  });

  describe('processFiles', () => {
    it('should process multiple files correctly', () => {
      const mockFiles = [
        new File([''], 'valid.pdf', { type: 'application/pdf' }),
        new File([''], 'invalid.txt', { type: 'text/plain' })
      ];
      
      Object.defineProperty(mockFiles[0], 'size', { value: 5 * 1024 * 1024 });
      Object.defineProperty(mockFiles[1], 'size', { value: 1 * 1024 * 1024 });

      const { newFiles, totalFiles } = DataIngestionUtility.processFiles(
        mockFiles,
        mockFileConfig,
        mockExistingFiles
      );

      expect(newFiles.length).toBe(2);
      expect(totalFiles).toBe(2);
      expect(newFiles[0].status).toBe(DataIngestionConstants.VALID);
      expect(newFiles[1].status).toBe(DataIngestionConstants.INVALID);
    });
  });

  describe('extractCompanyInitials', () => {
    it('should extract correct initials', () => {
      expect(DataIngestionUtility.extractCompanyInitials('Apple Inc')).toBe('AI');
      expect(DataIngestionUtility.extractCompanyInitials('Microsoft Corporation')).toBe('MC');
      expect(DataIngestionUtility.extractCompanyInitials('International Business Machines')).toBe('IB');
    });
  });

  describe('getFileIcon', () => {
    it('should return correct icons for different file types', () => {
      expect(DataIngestionUtility.getFileIcon('document.pdf')).toBe(filePdfIcon);
      expect(DataIngestionUtility.getFileIcon('sheet.xlsx')).toBe(fileExcelIcon);
      expect(DataIngestionUtility.getFileIcon('document.docx')).toBe(fileWordIcon);
      expect(DataIngestionUtility.getFileIcon('image.jpg')).toBe(fileImageIcon);
      expect(DataIngestionUtility.getFileIcon('unknown.txt')).toBe(filePdfIcon);
    });

    it('should return default icon for empty filename', () => {
      expect(DataIngestionUtility.getFileIcon('')).toBe(filePdfIcon);
    });
  });
});
