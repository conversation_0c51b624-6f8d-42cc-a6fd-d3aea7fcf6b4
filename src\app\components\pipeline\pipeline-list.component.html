﻿<div class="row tab-shadow">
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 col-xs-12 pr-0 pl-0">
        <nep-tab id="neptab" class="custom-pipeline-tab" [tabList]=tabList (OnSelectTab)="onTabClick($event)">
        </nep-tab>
    </div>
</div>
<div class="row mr-0 ml-0" *ngIf="tabName=='Pipeline Details'">
    <div class="col-lg-12 pr-0 pl-0">
        <div class="add-user-component">
            <div class="card card-main">
                <div class="card-header card-header-main p-0">
                    <div class="row mr-0 ml-0 pipeline-header">
                        <div class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                            <div class="float-left">
                                <div class="pipe-title TextTruncate" title="Pipelines">
                                    Pipelines
                                </div>
                            </div>
                            <div class="float-right">
                                <div class="d-inline-block search">
                                    <span class="fa fa-search fasearchicon p-1"></span>
                                    <input #gb pInputText type="text" (input)="searchLoadPCLazy()" class="TextTruncate search-text-company companyListSearchHeight" placeholder="Search pipeline" [(ngModel)]="globalFilter">
                                </div>
                                <div id="download-pipeline" class="d-inline-block" [hideIfUnauthorized]='{featureId:feature.Pipeline,action:"export"}'>
                                    <img id="download-pipelines" class="p-action-padding download-excel" title="Export Pipeline (Excel file)" (click)="exportPipelineList()" src="assets/dist/images/Cloud-download.svg" />
                                </div>
                                <div class="d-inline">
                                    <span class="col-divider">
                                    </span>
                                </div>
                                <div class="d-inline-block" [hideIfUnauthorized]='{featureId:feature.Pipeline,action:"add"}'>
                                    <div id="add-pipeline" class="add-icon p-add-padding">
                                        <a id="pipeline-list-add" [routerLink]="['/pipeline']" title="Add Pipeline">
                                            <img class="" title="Add Pipeline" src="assets/dist/images/plus.svg" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <kendo-grid  [data]="view | async"  [pageSize]="state.take" [skip]="state.skip" 
                    [sortable]="true" [sort]="sort" [pageable]="{
                            buttonCount: 10,
                            info: true,
                            type: 'numeric',
                            pageSizes: [50,100,150,300,500],
                            previousNext: true  }" (dataStateChange)="dataStateChange($event)"
                    class="custom-kendo-pc-list-grid k-grid-border-right-width k-grid-outline-none">
                    <ng-container>
                        <kendo-grid-column class="TextTruncate" field="name">
                            <ng-template kendoGridHeaderTemplate>
                                <div class="header-icon-wrapper wd-98">
                                    <span class="TextTruncate S-M">
                                        Pipeline
                                    </span>
                                </div>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-pipeline>
                                <a title="{{pipeline?.name}}" class="click-view" (click)="setHeaderName(pipeline)" href="javascript:void(0);" [routerLink]="['/pipeline-details', pipeline.encryptedPipelineId]" title="View Details" [hideIfUnauthorized]='{featureId:feature.Pipeline,action:"view"}'>{{pipeline?.name}}
                                </a>
                            </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column class="TextTruncate" field="strategy">
                            <ng-template kendoGridHeaderTemplate>
                                <span class="TextTruncate S-M">
                                    Strategy
                                </span>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-pipeline>
                                <span title="{{pipeline.strategyDetails.strategy}}" class="" *ngIf="pipeline.strategyDetails!=null">{{pipeline.strategyDetails.strategy}}</span>
                            </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column class="TextTruncate" field="sector">
                            <ng-template kendoGridHeaderTemplate>
                                <div class="header-icon-wrapper wd-98">
                                    <span class="TextTruncate S-M">
                                        Sector
                                    </span>
                                </div>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-pipeline>
                                <span title="{{pipeline.sectorList?.sector}}" class="" *ngIf="pipeline.sectorList!=null">{{pipeline.sectorList?.sector}}</span>
                            </ng-template>
                        </kendo-grid-column>  
                        <kendo-grid-column class="TextTruncate" field="status">
                            <ng-template kendoGridHeaderTemplate>
                                <div class="header-icon-wrapper wd-98">
                                    <span class="TextTruncate S-M">
                                        Status
                                    </span>
                                </div>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-pipeline>
                                <span title="{{pipeline.pipelineStatus.status}}" class="" *ngIf="pipeline.pipelineStatus!=null">{{pipeline.pipelineStatus.status}}</span>
                            </ng-template>
                        </kendo-grid-column>  
                        <ng-template kendoGridNoRecordsTemplate>
                            <app-empty-state class="finacials-beta-empty-state" [imageHeight]="'71vh'"
                                [isGraphImage]="false"></app-empty-state>
                        </ng-template>
                    </ng-container>
                </kendo-grid>
                </div>
            </div>
        </div>
    </div>
</div>
<div *ngIf="tabName=='Pipeline Dashboard'">
        <app-pipeline-dashboard></app-pipeline-dashboard>
</div>

<app-loader-component *ngIf="isLoader"></app-loader-component>