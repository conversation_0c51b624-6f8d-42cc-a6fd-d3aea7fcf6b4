import { <PERSON><PERSON><PERSON>, OnInit, On<PERSON><PERSON><PERSON>, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { FileHistoryService } from '../../services/file-history.service';
import { FileUploadProgressService } from '../../services/file-upload-progress.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-file-history-button',
  templateUrl: './file-history-button.component.html',
  styleUrls: ['./file-history-button.component.scss']
})
export class FileHistoryButtonComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('historyButton', { static: false }) historyButtonRef: ElementRef;
  
  newUploadsCount: number = 0;
  private subscriptions: Subscription[] = [];
  
  constructor(
    private fileHistoryService: FileHistoryService,
    private uploadProgressService: FileUploadProgressService
  ) {}

  ngOnInit(): void {
    // Subscribe to new upload count updates
    this.subscriptions.push(
      this.uploadProgressService.newUploadCount$.subscribe(count => {
        this.newUploadsCount = count;
      })
    );
  }

  ngAfterViewInit(): void {
    // Make sure the button reference is available for the service
    if (this.historyButtonRef) {
      console.log('History button reference is ready');
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
  toggleFileHistory(event: MouseEvent): void {
    event.stopPropagation();
    // Make sure we have the button reference before toggling the popup
    if (this.historyButtonRef) {
      this.fileHistoryService.toggleFileHistoryPopup(this.historyButtonRef);
    }
  }
}
