import { Component, OnInit, Output, Input, EventEmitter, ViewChild } from '@angular/core';
import { DatePipe } from '@angular/common';
import { EsgService } from "../../services/esg.services";
import {  
  FinancialValueUnitsEnum,
  MiscellaneousService,
  OrderTypesEnum,
  DecimalDigitEnum,
  PeriodTypeEnum,
  FxRates,
} from "../../services/miscellaneous.service";
import { Constants, decimalDigitOptions, GlobalConstants } from 'src/app/common/constants';
import {
  AUTO_STYLE,
  animate,
  state,
  style,
  transition,
  trigger
} from '@angular/animations';
import {ModuleList, PeriodTypeFilterOptions } from "src/app/common/constants";
import { Subject } from 'rxjs';
import { FormatSettings } from "@progress/kendo-angular-dateinputs";
import { CurrencyService } from 'src/app/services/currency.service';
import { SpotRateModel } from '../portfolioCompany/financials-beta/models/spot-rate.model';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
const DEFAULT_DURATION = 300;
@Component({
  selector: 'app-kpitablefilters',
  templateUrl: './kpitablefilters.component.html',
  styleUrls: ['./kpitablefilters.component.scss'],
  providers: [DatePipe],
  animations: [
    trigger('collapse', [
      state('false', style({ height: AUTO_STYLE, visibility: AUTO_STYLE })),
      state('true', style({ height: '0', visibility: 'hidden' })),
      transition('false => true', animate(DEFAULT_DURATION + 'ms ease-in')),
      transition('true => false', animate(DEFAULT_DURATION + 'ms ease-out'))
    ])
  ]
})
/**
 * Represents the KpitablefiltersComponent class.
 * This component is responsible for managing the filters used in the KPI table.
 * It provides functionality for selecting date ranges, decimal units, currency codes, and other filter options.
 * The selected filters are emitted through output events and used to trigger actions based on the current tab name.
 */
export class KpitablefiltersComponent implements OnInit {
  @ViewChild("myCalendar") datePicker;
  public range = { start: null, end: null };
  @Output() Kpifilter = new EventEmitter<any>();
  @Output() Valuesfilter = new EventEmitter<any>();
  @Output() decimalChange = new EventEmitter<number>();
  @Input()
  selectedDecimal: number;
  parentSubject: Subject<any>;
  periodTypes: any;
  collapsed = false;
  orderType: any;
  decimalType: any;
  today: Date;
  yearRange: any;
  minDate: Date | null = null;
  model: any = {};
  dateRange: any[];
  en: any;
  @Input() typeField: string;
  @Input() tabname: string;
  @Input() currencyCode: string;
  iscustom: boolean = false;
  isdate: boolean = false;
  yearOptions: any = [];
  filterTabNames = new Set(["Trading", "Operational", "Company"]);
  quarterOptions: any = [
    { value: "Q1", text: "Q1", number: 1 },
    { value: "Q2", text: "Q2", number: 2 },
    { value: "Q3", text: "Q3", number: 3 },
    { value: "Q4", text: "Q4", number: 4 },
  ];
  fxRatesList = [
    {
      id: FxRates.SystemAPI,
      type: FxRates[FxRates.SystemAPI],
    },
    {
      id: FxRates.BulkUpload,
      type: FxRates[FxRates.BulkUpload],
    },
  ];
  spotRate:number = null;
  spotRateErrorMessage: string = null;
  originalCurrencyList:any[] = [];
  currencyList: any[] = [];
  minDay: Date = new Date("2010-01-01");
  defaultDate: Date; // Set the default date to today
  public format: FormatSettings = {
    displayFormat: "MM/dd/yyyy",
    inputFormat: "MM/dd/yyyy",
  };
  filteredCurrencyList: any[] = [];
  isReportingCurrency: boolean = true;
  unitTypeList = [
    {
      typeId: FinancialValueUnitsEnum.Absolute,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Absolute],
    },
    {
      typeId: FinancialValueUnitsEnum.Thousands,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Thousands],
    },
    {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    },
    {
      typeId: FinancialValueUnitsEnum.Billions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Billions],
    },
  ];
  decimalDigitOptions:any = decimalDigitOptions;
  decimalDigit: any;
  investmentKpiValueUnit: any;
  isMatMenu:boolean=true;
  @Input() isDefaultMillion = false;
  @Input() defaultUnitType :number = FinancialValueUnitsEnum.Millions;
  DATE_RANGE = "Date Range";
  CUSTOM = "Custom";
  @Input() isFundKpi: boolean = false;
  constructor(private datePipe: DatePipe,
    private miscService: MiscellaneousService, private esgService: EsgService,
    private currencyService: CurrencyService,
    private portfolioCompanyService: PortfolioCompanyService) {
    let year = new Date();
    this.today = year;
    this.yearRange = "2000:" + year.getFullYear();
    this.onLoadPeriodTypes();
    this.orderType = [{ type: OrderTypesEnum.LatestOnLeft }, { type: OrderTypesEnum.LatestOnRight }];
    this.model.periodType = { type: PeriodTypeEnum.Last1Year };
    this.model.orderType = { type: OrderTypesEnum.LatestOnLeft };
    this.intilizedates();
    this.model.decimalType = 0;
    this.calenderbuttons();
    this.yearOptions = this.miscService.bindYearList();
    this.en = {
      firstDayOfWeek: 0,
      dayNames: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],

    };
    // Set the default date to one day before today
    this.defaultDate = new Date(
      new Date().getFullYear(),
      new Date().getMonth(),
      new Date().getDate() - 1
    );
    if (
      this.model.SpotRateDate == null ||
      this.model.SpotRateDate == undefined
    ) {
      this.model.SpotRateDate = this.defaultDate;
    }
  }
onLoadPeriodTypes=()=>{
  this.periodTypes = [
    { type: PeriodTypeEnum.Last3Month },
    { type: PeriodTypeEnum.Last6Month },
    { type: PeriodTypeEnum.YearToDate },
    { type: PeriodTypeEnum.Last1Year },
    { type: PeriodTypeEnum.DateRange },
    { type: "Custom" }
  ];
}
filterPeriodTypes=()=> {
  if (this.typeField == PeriodTypeFilterOptions.Monthly ||this.typeField == PeriodTypeFilterOptions.Quarterly || this.typeField == PeriodTypeFilterOptions.Annual) {
    const unwantedPeriodTypes = [PeriodTypeEnum.Last3Month, PeriodTypeEnum.Last6Month, PeriodTypeEnum.YearToDate];
    this.periodTypes = this.periodTypes.filter((item) => !unwantedPeriodTypes.includes(item.type));
  }
}

public disabledDates = (date: Date): boolean => {
  return date < this.minDay || date > new Date();
};

/**
 * Sets the selected decimal unit type.
 * 
 * @param decimal - The decimal object containing the unit type.
 */
onDecimalSelection(decimal: any) {
  this.selectedDecimal = decimal.unitType; 
}

/**
 * Applies the selected filters and triggers the necessary actions based on the current tab name.
 * If the tab name is ESG, it emits the selected decimal value and calls the changeDecimal method of the ESG service.
 */
emitDecimalValue() {
  if(this.tabname === ModuleList.ESG) {
  this.decimalChange.emit(this.selectedDecimal);
  this.esgService.changeDecimal(this.selectedDecimal);
  }
}

  onSubmit(event) {
    if( event.submitter.name == "Save" ){
    this.emitDecimalValue();
    let searchFilter = {
      sortOrder: null,
      periodType: this.model.periodType.type,
      startPeriod: this.model.fromDate == undefined ? "" : this.model.fromDate,
      endPeriod: this.model.toDate == undefined ? "" : this.model.toDate,
      FromQuarter: this.model.fromQuarter == undefined ? "" : this.model.fromQuarter.value,
      FromYear: this.model.fromYear == undefined ? "" : this.model.fromYear.value,
      ToQuarter: this.model.toQuarter == undefined ? "" : this.model.toQuarter.value,
      ToYear: this.model.toYear == undefined ? "" : this.model.toYear.value,
      UnitType:this.investmentKpiValueUnit,
      isSpotRate: this.model.isSpotRate,
      currencyCode: this.model.currencyCode?.currencyCode,
      spotRate: this.model.spotRate,
      spotRateDate: this.model.spotRateDate,
      currencyRateSource: this.model.fxRates?.type,
      isApply: true
    };
    if (this.model.periodType.type == "Date Range"){
      this.validateKPIPeriod(this.model);
    }
    if (this.periodErrorMessage == "")
    { 
      this.Kpifilter.emit(searchFilter);
      this.convertUnitType();
      this.isMatMenu=false;
      this.isMatMenu=true;
    }
  }

    if (event.submitter?.name === "Reset") {
      this.model.startPeriod = [];
      const periodType = this.tabname === ModuleList.ESG ? PeriodTypeEnum.Last3Years : PeriodTypeEnum.Last1Year;
      this.model.periodType = { type: periodType };
      this.model.UnitType = this.investmentKpiValueUnit;
      this.investmentKpiValueUnit = this.isDefaultMillion ? this.unitTypeList[2] : this.unitTypeList.find(x => x.typeId === this.defaultUnitType);
      this.minDate = null;
      this.decimalDigit = this.decimalDigitOptions[1];
      this.selectedDecimal = 2;
      this.emitDecimalValue();
      this.periodErrorMessage = "";
      this.spotRateErrorMessage = null;
      this.spotRate = null;
      this.model.spotRate = null;
      this.model.fxRates = {
        id: FxRates.SystemAPI,
        type: FxRates[FxRates.SystemAPI],
      };
      this.model.currecyUnit = {
        typeId: FinancialValueUnitsEnum.Thousands,
        unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Thousands],
      };
      this.model.currencyCode = null;
      this.model.isSpotRate = false;
        this.model.SpotRateDate = this.defaultDate;
      const searchFilter = this.createSearchFilter(this.model, this.investmentKpiValueUnit);
      this.Kpifilter.emit(searchFilter);
      this.convertUnitType();
    }
  }
  createSearchFilter(model, investmentKpiValueUnit) {
    const { fromDate, toDate, fromQuarter, fromYear, toQuarter, toYear } = model;
    return {
      sortOrder: null,
      periodType: model.periodType.type,
      startPeriod: fromDate ?? "",
      endPeriod: toDate ?? "",
      FromQuarter: fromQuarter?.value ?? "",
      FromYear: fromYear?.value ?? "",
      ToQuarter: toQuarter?.value ?? "",
      ToYear: toYear?.value ?? "",
      UnitType: investmentKpiValueUnit,
      isApply: false
    };
  }
  fromQuaterevent(event) {

    this.checkvalidation_Qauter_Year(this.model);
  }
  toQuaterevent(event) {
    this.checkvalidation_Qauter_Year(this.model);
  }
  fromYearevent(ev) {
    this.checkvalidation_Qauter_Year(this.model);
  }
  toYearevent(ev) {
    this.checkvalidation_Qauter_Year(this.model);
  }
  checkvalidation_Qauter_Year(model: any) {
    this.periodErrorMessage = "";
    let FromQuarter = this.model.fromQuarter == undefined ? "" : this.model.fromQuarter.number;
    let FromYear = this.model.fromYear == undefined ? "" : this.model.fromYear.value;
    let lToQuarter = this.model.toQuarter == undefined ? "" : this.model.toQuarter.number;
    let ToYear = this.model.toYear == undefined ? "" : this.model.toYear.value;
    if (FromYear != "" && ToYear != "") {
      if (FromYear > ToYear) {
        this.periodErrorMessage = "Invalid Period Range! FromYear cannot be after ToYear!"
      }
      if (FromYear == ToYear) {
        if (FromQuarter > lToQuarter)        
          this.periodErrorMessage = "Invalid period range! To quarter cannot be less than from quarter"
      }

    }
  }

  intilizedates() {
    let lastSixthMonthDate = new Date();
    lastSixthMonthDate.setDate(1);
    lastSixthMonthDate.setMonth(lastSixthMonthDate.getMonth() - 6);
  }
  toggle() {
    this.collapsed = !this.collapsed;
  }
  expand() {
    this.collapsed = false;
  }
  collapse() {
    this.collapsed = true;
  }
  calenderbuttons() {
    this.en = {
      firstDayOfWeek: 0,
      monthNamesShort: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
      today: 'Today',
      clear: 'Clear'
    };
  }
  ngOnInit(): void {
    this.periodErrorMessage="";
    this.investmentKpiValueUnit = this.isDefaultMillion ? this.unitTypeList[2] :
                                   this.unitTypeList.find(x=> x.typeId == this.defaultUnitType);  
    this.decimalDigit = this.decimalDigitOptions[1];                         
    this.setobjectperiodtype(this.tabname);
    this.getCurrencyLists();
    this.model.fxRates = {
      id: FxRates.SystemAPI,
      type: FxRates[FxRates.SystemAPI],
    };
  }

  getCurrencyLists() {
    this.currencyService.getAllCurrencies().subscribe((result) => {
      let resp = result;
      if (resp != undefined && resp.code == "OK") {
        this.currencyList = resp.body;
        this.originalCurrencyList = resp.body;
        this.currencyList = this.currencyList.filter(
          (x) => x?.currencyCode?.trim() != this.currencyCode?.trim() && x?.currencyCode === GlobalConstants.USDCurrencyCode
        );
        this.filteredCurrencyList = this.currencyList;
      }
    });
  }

  onCurrencyChange(e) {
    this.isReportingCurrency = true;
    if (this.currencyCode != e?.currencyCode)
      this.isReportingCurrency = false;
    if(this.model.isSpotRate)
      this.getSpotRate();
  }

  /**
   * Retrieves the spot rate based on the selected currency and reporting currency.
   *
   * This method constructs a SpotRateModel object using the current reporting currency code,
   * selected currency code, effective date, and conversion rate source. It then calls the
   * getSpotRate method from the PortfolioCompanyService to fetch the spot rate.
   *
   * @returns {void}
   */
  getSpotRate(): void {
    this.spotRateErrorMessage = null;
    if(!this.model.currencyCode || !this.model.fxRates)
    {
      this.spotRateErrorMessage = GlobalConstants.CurrencyRateError;
      return;
    }
    const spotRateModel: SpotRateModel = {
      FromCurrencyId: this.originalCurrencyList.find(x => x.currencyCode === this.currencyCode)?.currencyID,
      ToCurrencyId: this.model?.currencyCode?.currencyID,
      EffectiveDate:this.datePipe.transform(this.model.spotRateDate, 'yyyy-MM-dd'),
      ConversionRateSource: this.model?.fxRates?.type,
    };
    this.portfolioCompanyService.getSpotRate(spotRateModel).subscribe({
      next: (result: number) => {
        if (result==null) {
          this.spotRate= null;
          this.model.spotRate = null;
          this.spotRateErrorMessage = GlobalConstants.SpotRateError;
        }
        else
        {
          this.model.spotRate = result;
          this.spotRate= result;
        }
      },
      error: (error) => {
        console.error("Error retrieving spot rate:", error);
        this.spotRateErrorMessage = GlobalConstants.SpotRateRetrievalError;
      }
    });
  }

  /**
   * Custom filter logic for the ComboBox.
   */
  public handleFilter(value: string): void {
    this.filteredCurrencyList = this.currencyList.filter(
      (s) =>
        s.currencyCode.toLowerCase().includes(value.toLowerCase()) ||
        s.currency.toLowerCase().includes(value.toLowerCase())
    );
  }

  /**
   * Handles the change event of the spot rate checkbox.
   * Clears the spot rate error message.
   * Resets the spot rate date if the checkbox is unchecked.
   * Retrieves the spot rate if the checkbox is checked.
   */
  onSpotRateChange() {
    this.spotRateErrorMessage = null;
    if (!this.model.isSpotRate) this.model.spotRateDate = null;
    if(this.model.isSpotRate) 
      this.getSpotRate();
    else
    {
      this.model.currencyCode = null;
    }
  }

  /**
   * Formats the given date.
   * 
   * @param date - The date to be formatted.
   * @returns The formatted date as a string, or an empty string if the date is "NA", undefined, or null.
   */
  getFormattedDate(date: any) {
    return date == "NA" || date == undefined || date == null
      ? ""
      : new Date(date);
  }

  setobjectperiodtype(tabname: any) {
    this.periodErrorMessage="";
    if (tabname == "Company"||tabname == "Trading" ||tabname == "Operational" ) {
      this.periodTypes = this.periodTypes.filter((item) => item.type !== 'Custom');
      const exits = this.periodTypes.filter(S => S.type == "Date Range");
      if (exits.length == 0) {
        this.periodTypes.push({ type: PeriodTypeEnum.Last3Month });
        this.periodTypes.push({ type: PeriodTypeEnum.Last6Month });
        this.periodTypes.push({ type: PeriodTypeEnum.YearToDate });
        this.periodTypes.push({ type: PeriodTypeEnum.DateRange });
      }
    } else {
      const exits = this.periodTypes.filter(S => S.type == "Custom");
      if (exits.length == 0) {
        this.periodTypes.push({ type: "Custom" });
      }
      this.periodTypes = this.periodTypes.filter((item) => item.type !== 'Date Range');
      this.periodTypes = this.periodTypes.filter((item) => item.type !== "3M (Last 3 months)");
      this.periodTypes = this.periodTypes.filter((item) => item.type !== "6M (Last 6 months)");
      this.periodTypes = this.periodTypes.filter((item) => item.type !== "YTD (Year to Date)");
    }
   this.model={};
    this.model.periodType = { type: PeriodTypeEnum.Last1Year };
    
    if (tabname == ModuleList.ESG) {
      this.model.periodType = { type: PeriodTypeEnum.Last3Years };

      const unwantedPeriodTypes = [PeriodTypeEnum.Last1Year];
      this.periodTypes = this.periodTypes.filter((item) => !unwantedPeriodTypes.includes(item.type));    
      if (!this.periodTypes.some(item => item.type === PeriodTypeEnum.Last3Years)) {
        this.periodTypes.push({ type: PeriodTypeEnum.Last3Years });
      }
    }  }
  converttransformDate(date) {
    return this.datePipe.transform(date, 'MM/yyyy');
  }
  resetForm(form: any) {
    form.resetForm();
    this.minDate = null;
    this.periodErrorMessage = "";
    this.Kpifilter.emit(null);
    localStorage.setItem("searchFilter", null);
    this.investmentKpiValueUnit = this.isDefaultMillion ? this.unitTypeList[2] : this.unitTypeList.find(x=> x.typeId == this.defaultUnitType);
    this.convertUnitType();
    this.model.fxRates = {
      id: FxRates.SystemAPI,
      type: FxRates[FxRates.SystemAPI],
    };
    
  }
  onPeriodChange(event) {
    this.model.startPeriod = [];
    this.periodErrorMessage = "";
    this.iscustom = false;
    this.isdate = false;
    this.model.periodType = event;
    switch (event.type) {
      case this.DATE_RANGE:
        this.model.startPeriod = null;
        this.isdate = true;
        break;
      case this.CUSTOM:
        this.iscustom = true;
        break;
    }
  }
  fromchangeSelect(ev) {
    this.onDateSelect(null);
  }
  onDateSelect(event:any) {
    this.model.fromDate = null;
    this.model.toDate = null;
    if (this.model.startPeriod?.length > 0) {
      this.model.fromDate = this.model.startPeriod[0];
      this.model.toDate = this.model.startPeriod[1];
      if(this.model.toDate!=null)
        this.datePicker.overlayVisible = false;
    }
    if (this.model.periodType.type == "Date Range")
      this.validateKPIPeriod(this.model);
    else
      this.periodErrorMessage = "";
  }

  validateKPIPeriod(model: any) {
    this.periodErrorMessage = "";
    if ((this.model.startPeriod == null || this.model.startPeriod == undefined)) {
      this.periodErrorMessage = "Select Date..!";
    }
    model = this.model;
    if (
      model.startPeriod != undefined &&
      model.startPeriod.length > 1 &&
      model.startPeriod[0] != null &&
      model.startPeriod[1] != null
    ) {
      this.datePicker.overlayVisible = false;
      this.datePicker.datepickerClick = true;
      if (model.startPeriod[0] > model.startPeriod[1]) {
        this.periodErrorMessage = "Start date should be smaller than end date";
      } else {
        this.periodErrorMessage = "";
        this.model.fromDate = model.startPeriod[0];
        this.model.toDate = model.startPeriod[1];
      }
    }
  }

  onDateClear() {
    this.minDate = null;
    this.model.fromDate = null;
    this.model.toDate = null;
  }

  periodErrorMessage: string = "";

  tochangeSelect(ev) {
    this.model.toDate = ev;
  }
  convertUnitType() {
    this.Valuesfilter.emit(this.investmentKpiValueUnit);
  }
  ngOnChanges() {
    if (this.filterTabNames.has(this.tabname)) {
      this.filterPeriodTypes();
    }
  }
}


