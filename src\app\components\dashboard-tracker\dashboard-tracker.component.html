<app-loader-component *ngIf="isLoading"></app-loader-component>
<div class="kendo-container">
  <kendo-grid id="dashboardTrackerTable" class="dashboard-tracker-table" [ngClass]="passedClass" [data]="view | async"
    [scrollable]="'scrollable'" [sortable]="true" [style.min-width.px]="gridColumns.length * 200 + 480"
    [pageSize]="state.take" [skip]="state.skip" [pageable]="{
      buttonCount: 10,
      info: true,
      type: 'numeric',
      pageSizes: [100, 200, 300],
      previousNext: true
    }" (dataStateChange)="dataStateChange($event)">

    <ng-container *ngFor="let col of gridColumns; let i = index">

      <kendo-grid-column *ngIf="col.name === DashboardConfigurationConstants.SerialNo" [name]="col.name"
        [title]="col.name" [width]="200">
        <ng-template kendoGridHeaderTemplate>
          <input id="{{col.name}}-{{i}}" kendoCheckBox type="checkbox"
            class="k-checkbox k-checkbox-md k-rounded-md custom-border" [checked]="col.selected"
            (click)="onColumnSelectionClick(i)">
          <span class="Body-R text-truncate header-title ml-3">S.No.</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{ (state.skip || 0) + rowIndex + 1 }}
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column *ngIf="col.name === DashboardConfigurationConstants.FundName" [name]="col.name"
        [title]="col.name" [width]="200">
        <ng-template kendoGridHeaderTemplate>
          <span class="Body-R text-truncate header-title ml-3">{{ col.name }}</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem[DashboardConfigurationConstants.FundName] }}
        </ng-template>
      </kendo-grid-column>
      <!-- Special handling for Portfolio Company column (with logo and name) -->
      <kendo-grid-column *ngIf="col.name === DashboardConfigurationConstants.PortfolioCompanyName" [name]="col.name"
        [title]="col.name" [width]="300">
        <ng-template kendoGridHeaderTemplate>
          <span class="Body-R text-truncate header-title ml-3">{{ col.name }}</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <div class="d-flex align-items-center">
            <ng-container *ngIf="dataItem.CompanyLogo && dataItem.CompanyLogo.trim() !== ''">
              <img [src]="dataItem.CompanyLogo" alt="logo" class="company-logo mr-2 p-1" />
            </ng-container>
            <ng-container *ngIf="!dataItem.CompanyLogo || dataItem.CompanyLogo.trim() === ''">
              <span class="text-logo mr-2">{{ dataItem[DashboardConfigurationConstants.PortfolioCompanyName]?.slice(0,1)
                }}</span>
            </ng-container>
            <span>{{ dataItem[DashboardConfigurationConstants.PortfolioCompanyName] }}</span>
          </div>
        </ng-template>
      </kendo-grid-column>

      <!-- All other columns -->
      <kendo-grid-column *ngIf="col.name !==  DashboardConfigurationConstants.PortfolioCompanyName && 
      col.name !== DashboardConfigurationConstants.CompanyLogo && 
      col.name !== DashboardConfigurationConstants.FundName && 
      col.name !== DashboardConfigurationConstants.PCID && 
      col.name !== DashboardConfigurationConstants.FundID &&
      col.name !== DashboardConfigurationConstants.SerialNo" [name]="col.name" [title]="col.name" [width]="200">
        <ng-template kendoGridHeaderTemplate>
          <input id="{{col.name}}-{{i}}" kendoCheckBox type="checkbox"
            class="k-checkbox k-checkbox-md k-rounded-md custom-border" [checked]="col.selected"
            (click)="onColumnSelectionClick(i)">
          <span class="Body-R text-truncate header-title ml-3">{{ col.name }}</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <!-- Dashboard Configuration Tab - Special rendering -->
          <ng-container *ngIf="isDashboardConfigurationTab">

            <!-- Regular cell for columns with mapTo -->
            <ng-container *ngIf="!col.isDropDown && col.mapTo">
              {{ dataItem[col.name] || '' }}
            </ng-container>
            <!-- Dropdown for columns with isDropDown -->
            <ng-container *ngIf="col.isDropDown">
              <div class="dropdown-input">
                <kendo-dropdownlist
                  textField="displayText"
                  id="dropdown-input-{{ i }}"
                  class="k-custom-solid-dropdown k-dropdown-height-32"
                  [data]="col.dropDownValues"
                  valueField="value"
                  [groupField]="'typeText'"
                  [clearButton]="false"
                  [rounded]="'medium'"
                  [fillMode]="'solid'"
                  placeholder="Select Here"
                  [showStickyHeader]="false"
                  [valuePrimitive]="true"
                  [ngModel]="dataItem[col.name]"
                  (valueChange)="onDropdownValueChange($event, dataItem, col)"
                >
                  <ng-template kendoComboBoxGroupTemplate let-groupName>
                    <span class="dropdown-group-label">{{ groupName }}</span>
                  </ng-template>
                  <ng-template kendoComboBoxItemTemplate let-dataItem>
                    <ng-container *ngIf="dataItem.type === 1">
                      <span class="icon-text-option d-flex align-items-center">
                        <kendo-svg-icon *ngIf="dataItem.svgIcon" [icon]="dataItem.svgIcon" size="medium" class="mr-2"></kendo-svg-icon>
                        <span>{{ dataItem.displayText }}</span>
                      </span>
                    </ng-container>
                    <ng-container *ngIf="dataItem.type !== 1">
                      <span>{{ dataItem.displayText }}</span>
                    </ng-container>
                  </ng-template>
                  <ng-template kendoDropDownListValueTemplate let-selected>
                    <ng-container *ngIf="col.dropDownValues">
                      <kendo-svg-icon *ngIf="selected?.type == 1 && selected?.svgIcon" [icon]="selected?.svgIcon" size="medium" class="mr-2"></kendo-svg-icon>
                      <span *ngIf="selected?.type !== 1">{{ selected?.displayText }}</span>
                    </ng-container>
                  </ng-template>
                </kendo-dropdownlist>
              </div>
            </ng-container>



            <!-- Textbox for other columns -->
            <ng-container *ngIf="!col.isDropDown && !col.mapTo">
              <kendo-textbox fillMode="flat" [placeholder]="col.dataType === 3 ? defaultDateFormat : ''"
                [value]="dataItem[col.name] || ''" [showSuccessIcon]="false"
                [showErrorIcon]="hasValidationError(dataItem, col)" [valid]="!hasValidationError(dataItem, col)"
                [ngClass]="{'validation-error': hasValidationError(dataItem, col)}"
                (valueChange)="onTextboxValueChange($event, dataItem, col)">
                <ng-template kendoTextBoxSuffixTemplate>
                  <button kendoButton fillMode="clear" (click)="clearTextboxValue(dataItem, col)">
                    <img src="assets/dist/images/close-clear.svg" alt="Clear">
                  </button>
                </ng-template>
              </kendo-textbox>
            </ng-container>
          </ng-container>

          <!-- Regular view - existing behavior -->
          <ng-container *ngIf="!isDashboardConfigurationTab && col.isDropDown">
            <ng-container *ngIf="getDropDownValuesData(dataItem[col.name], col.dropDownValues) as result">
              <ng-container *ngIf="result.svgIcon; else justText">
                <kendo-svg-icon [icon]="result.svgIcon" size="medium" class="mr-2"></kendo-svg-icon>                
              </ng-container>
              <ng-template #justText>
                <span>{{ result.displayText || result }}</span>
              </ng-template>
            </ng-container>
          </ng-container>
          <ng-container *ngIf="!isDashboardConfigurationTab && !col.isDropDown">
            {{ dataItem[col.name] || '' }}
          </ng-container>
        </ng-template>
      </kendo-grid-column>
    </ng-container>
    <!-- default screen if grid data is empty -->
    <ng-template kendoGridNoRecordsTemplate>
      <div class="text-center py-5 mt-5" *ngIf="totalRecords === 0">
        <img src="assets/dist/images/Illustrations.svg" alt="No data" class="mb-3" />
        <p class="mb-0 Body-R content-secondary">No Data Found</p>
      </div>
    </ng-template>
  </kendo-grid>
  <!-- Floating Selection Popup -->
  <div *ngIf="showSelectionPopup" class="selection-popup">
    <div class="selection-content">
      <div class="close-selection-btn" (click)="handleDeleteSelectedClick()" id="close-selection-btn">
        <span><img src="assets/dist/images/delete-red.svg" alt="Close" /></span>
      </div>
      <div class="vertical-separator"></div>
      <div class="delete-selected-btn Body-R pr-4" [disabled]="isDeleteDisabled" (click)="closeSelectionPopup()"
        id="delete-selected-btn">
        <span><img src="assets/dist/images/cross-blueicon.svg" /></span>
        Clear All Data
      </div>

    </div>
  </div>
</div>

<!-- Delete Selected Columns Modal -->
<div *ngIf="showDeletePopup">
  <confirm-modal customwidth="500px" isCustomFooterClass="true" primaryButtonIsDanger="true"
    primaryButtonName="Yes, Delete" secondaryButtonName="No, keep it"
    (primaryButtonEvent)="deleteDashboardTrackerColumns()" modalTitle="{{ modalTitle }}"
    (secondaryButtonEvent)="cancelDelete()" isDeleteConfirmModal="true" isCloseEnable="true"
    (closeIconClick)="cancelDelete()">
    <div class="container px-2">
      <div class="d-flex">
        <div class="mr-13">
          <img src="assets/dist/images/exclamation-circle-delete.svg" alt="Exclamation Circle" />
        </div>
        <div>
          <div class="Heading2-M mb-1">{{deleteHeader}}</div>
          <div class="Caption-R content-secondary">{{deleteMessage}}</div>
        </div>
      </div>
    </div>
  </confirm-modal>
</div>