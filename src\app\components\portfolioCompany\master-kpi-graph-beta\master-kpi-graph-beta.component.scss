.portfolio-detail-component {
  .chart-bg {
      padding: 0px !important;
      margin: 0px !important;
  }
}
.custom-ui-label{
  width: 400px !important;
}

::ng-deep .k-treeview-leaf.k-selected{
  background-color: transparent;
  color: black;
}

.k-popup{
  overflow: visible !important;
}
.Threedots{
  cursor: pointer;
  position: absolute;
}

.parent-kpi{
  margin-left: 26.5rem !important;
}
.child-kpi{
  margin-left: 25rem !important;
}

.preference-popup{
  background-color: #FFFFFF;
}

.preference-lineitem {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  
  img {
      width: 17px;
      height: 17px;
      background: transparent;
  }
  
  span {
      line-height: 1;
  }
}

.line-item {
display: flex;
align-items: center;
gap: 8px;
padding: 4px 8px;

span {
    line-height: 1;
    vertical-align: middle;
}
}
.selected-preference{
width: 14px;
height: 14px;
flex-shrink: 0;
}

.lineItem-width{
width: 25rem;
}
.child-lineItem-width{
  width: 23.5rem;
  }

.ml-kpi{
  margin-left: 1.313rem;
}

.selected-kpi{
  margin-top: -1.2rem;
}
::ng-deep .k-input-solid:focus-within{
  padding-left:0.75rem
}

.custom-dropdown-search {
  .k-input-solid:focus-within {
    padding-left:0.75rem !important
  }
}
