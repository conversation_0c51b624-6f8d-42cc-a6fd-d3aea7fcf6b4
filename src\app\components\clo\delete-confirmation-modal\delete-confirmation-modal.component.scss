@import "../../../../variables.scss";

.delete-confirmation-modal {
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;

  .delete-confirmation-header { 
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.5rem;
  }

  .delete-warning {
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;    
    border-bottom: $default-cell-bottom-border !important;
  
    img {
      width: $icon-size;
      height: $icon-size;
      opacity: 0px;
    }
  
    .warning-text {
      padding-top: $padding-21;
      padding-bottom: $padding-25;
      padding-left: $padding-22;
    }
  }

  .delete-warning-msg {
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;    
    padding-bottom: $padding-21_5;
    padding-left: $padding-16;
    padding-right: $padding-16;
    padding-top: $padding-6;
  
    .note {
      padding-bottom: $padding-12;
    }
  
    .content {
      padding-left: $padding-12;
    }
  }

  .note,.content {
    color: #DE3139;        
  }

  .tableContent{
    height: 112px;
    padding-left: $padding-medium;
    .table-warnings{
      padding-top: 13px;
    }
    .table-warning-text{
      padding-top: 10px;
    }
  }
}

:host ::ng-deep .nep-card-body {
  padding: 0px $nep-body-line-height;
}