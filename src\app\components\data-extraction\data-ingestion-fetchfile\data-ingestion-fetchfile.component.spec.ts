import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { BehaviorSubject, of } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { DataIngestionFetchFileComponent } from './data-ingestion-fetchfile.component';
import { FileSharingService } from 'src/app/services/file-sharing.service';
import { DataIngestionService } from 'src/app/services/data-ingestion.service';
import { PortfolioLogoModel, SelectedFile } from '../data-ingestion/data-ingestion.model';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { ExtractionSharedService } from 'src/app/services/extraction-shared.service';
import { Router, ActivatedRoute } from '@angular/router';
import { AccountService } from 'src/app/services/account.service';
import { OidcAuthService } from 'src/app/services/oidc-auth.service';
import { ExtractionIngestionService } from 'src/app/services/extraction-ingestion.service';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';

describe('DataIngestionFetchFileComponent', () => {
  let component: DataIngestionFetchFileComponent;
  let fixture: ComponentFixture<DataIngestionFetchFileComponent>;
  let fileSharingServiceMock: jasmine.SpyObj<FileSharingService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let dataIngestionServiceMock: jasmine.SpyObj<DataIngestionService>;
  let portfolioCompanyServiceMock: jasmine.SpyObj<PortfolioCompanyService>;
  let extractionSharedServiceMock: jasmine.SpyObj<ExtractionSharedService>;
  let routerMock: jasmine.SpyObj<Router>;
  let oidcAuthServiceMock: jasmine.SpyObj<OidcAuthService>;
  let extractionIngestionServiceMock: jasmine.SpyObj<ExtractionIngestionService>;
  let repositoryConfigServiceMock: jasmine.SpyObj<RepositoryConfigService>;

  const mockFileData = {
    selectedFilesList: [],
    selectedFiles: [],
    ingestionFormData: {
      company: {
        dealId: 1,
        companyId: 1,
        fundId: 1,
        companyName: 'Test Company',
        fundName: 'Test Fund'
      },
      module: 'test',
      monthQuarter: 'Q1',
      period: 'Quarter',
      sourceType: 'PDF',
      year: 2023
    }
  };

  const mockDocumentTypes = [
    { documentName: 'Type 1', id: 1 },
    { documentName: 'Type 2', id: 2 }
  ];
const mockPortfolioCompany = {
  imagePath: null
}
  beforeEach(async () => {
    fileSharingServiceMock = jasmine.createSpyObj('FileSharingService', [''], {
      currentFiles: new BehaviorSubject(mockFileData)
    });
    
    toastrServiceMock = jasmine.createSpyObj('ToastrService', 
      ['success', 'error', 'warning']);
    
    dataIngestionServiceMock = jasmine.createSpyObj('DataIngestionService',
      ['getDataExtractionTypes', 'getKpiModulesPageConfigDetails']);
    dataIngestionServiceMock.getDataExtractionTypes.and.returnValue(of(mockDocumentTypes));
    dataIngestionServiceMock.getKpiModulesPageConfigDetails.and.returnValue(of([]));

    portfolioCompanyServiceMock = jasmine.createSpyObj('PortfolioCompanyService', 
      ['getPortfolioCompanyLogo']);
    portfolioCompanyServiceMock.getPortfolioCompanyLogo.and.returnValue(of(mockPortfolioCompany));
    
    // Add mock for ExtractionSharedService
    extractionSharedServiceMock = jasmine.createSpyObj('ExtractionSharedService',
      ['post', 'get', 'getWithApiKey', 'postWithApiKey']);
    extractionSharedServiceMock.post.and.returnValue(of([]));

    // Mock different responses based on the URL
    extractionSharedServiceMock.get.and.callFake((url: string) => {
      if (url.includes('process-details')) {
        return of({
          companyName: 'Test Company',
          encryptedPortfolioCompanyId: 'test-encrypted-id',
          documents: [],
          parentJobId: 'test-parent-job-id',
          extractionType: 'AsIsExtraction',
          state: 'FILE_DRAFT_BEFORE_EXTRACTION',
          statusId: 1,
          jobId: 'test-job-id',
          isClassifiers: false,
          processId: 'test-process-id',
          fundName: 'Test Fund',
          encryptedFundId: 'test-fund-id'
        });
      }
      return of([]);
    });

    extractionSharedServiceMock.getWithApiKey.and.returnValue(of([]));
    extractionSharedServiceMock.postWithApiKey.and.returnValue(of([]));
    
    // Add mock for Router
    routerMock = jasmine.createSpyObj('Router', ['navigate']);

    // Add mock for OidcAuthService
    oidcAuthServiceMock = jasmine.createSpyObj('OidcAuthService', ['getEnvironmentConfig']);
    oidcAuthServiceMock.getEnvironmentConfig.and.returnValue({ client_env: 'test' });

    // Add mock for ExtractionIngestionService
    extractionIngestionServiceMock = jasmine.createSpyObj('ExtractionIngestionService', ['specificKpiExtract']);
    extractionIngestionServiceMock.specificKpiExtract.and.returnValue(of({ isSuccess: true, id: 'test-id' }));

    // Add mock for RepositoryConfigService
    repositoryConfigServiceMock = jasmine.createSpyObj('RepositoryConfigService', ['getRepositoryStructureData']);
    repositoryConfigServiceMock.getRepositoryStructureData.and.returnValue(of({ data: [] }));

    await TestBed.configureTestingModule({
      declarations: [ 
        DataIngestionFetchFileComponent
      ],
      imports: [ 
        ReactiveFormsModule,
        ToastrModule.forRoot(),
        KendoModule,
        HttpClientTestingModule
      ],
      providers: [
        FormBuilder,
        { provide: FileSharingService, useValue: fileSharingServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: DataIngestionService, useValue: dataIngestionServiceMock },
        { provide: PortfolioCompanyService, useValue: portfolioCompanyServiceMock },
        { provide: ExtractionSharedService, useValue: extractionSharedServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: ActivatedRoute, useValue: { snapshot: { params: { id: 'test-process-id' } }, params: of({}), queryParams: of({}) } },
        { provide: AccountService, useValue: {} },
        { provide: OidcAuthService, useValue: oidcAuthServiceMock },
        { provide: ExtractionIngestionService, useValue: extractionIngestionServiceMock },
        { provide: RepositoryConfigService, useValue: repositoryConfigServiceMock },
        { provide: 'BASE_URL', useValue: 'http://localhost:5001/' }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DataIngestionFetchFileComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct company data', async () => {
    // Wait for async operations to complete
    await fixture.whenStable();
    fixture.detectChanges();

    expect(component.companyName).toBe('Test Company');
    expect(component.companyInitials).toBe('TC');
    // Note: reportingPeriod is not set in the current implementation
    // expect(component.reportingPeriod).toBe('Q1, 2023');
  });

  it('should load document types on init', () => {
    expect(dataIngestionServiceMock.getDataExtractionTypes).toHaveBeenCalled();
    expect(component.documentTypes).toEqual(mockDocumentTypes);
  });

  it('should handle file selection', () => {
    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    const mockEvent = {
      target: {
        files: [mockFile]
      }
    };

    component.onFileSelected(mockEvent);
    expect(component.selectedFilesList.length).toBeGreaterThan(0);
  });

  it('should validate max file count', () => {
    const mockFiles = Array(26).fill(new File([''], 'test.pdf', { type: 'application/pdf' }));
    const mockEvent = {
      target: {
        files: mockFiles
      }
    };

    component.onFileSelected(mockEvent);
    expect(toastrServiceMock.warning).toHaveBeenCalled();
  });

  it('should sort files correctly', () => {
    const mockFiles:SelectedFile[] = [
      { id: '1', name: 'B.pdf', status: 'invalid', errors: [], documentType: null, file: null, size: 0, type: '' },
      { id: '2', name: 'A.pdf', status: 'valid', errors: [], documentType: null, file: null, size: 0, type: '' }
    ];
    component.selectedFilesList = [...mockFiles];
    
    component.sortFilesByStatusAndName();
    
    expect(component.selectedFilesList[0].name).toBe('B.pdf');
    expect(component.selectedFilesList[0].status).toBe('invalid');
  });

  it('should handle document type change', () => {
    // Initialize the selection form
    component.initSelectionForm();

    // Mock the updateDocumentStructure method to avoid errors
    spyOn(component, 'updateDocumentStructure').and.stub();

    // Call the method with a document type
    component.onDocumentTypeChange({ id: '1', name: 'Type 1' }, 0);

    // Verify that the selection form is marked as touched
    expect(component.selectionForm.touched).toBe(true);

    // Verify that updateDocumentStructure was called
    expect(component.updateDocumentStructure).toHaveBeenCalledWith('1');
  });

  it('should delete file correctly', () => {
    const mockFile:SelectedFile = {
      id: '1', name: 'test.pdf', status: 'valid',
      errors: [], documentType: null, file: null, size: 0, type: ''
    };

    // Add a document to match the file
    const mockDocument = {
      id: '1',
      name: 'test.pdf',
      s3Path: 'test/path/test.pdf',
      url: 'test/url/test.pdf',
      documentTypeId: 1,
      documentType: 'Test Document',
      errors: [],
      status: 'valid',
      type: 'pdf',
      extension: 'pdf',
      jobId: 'test-job-id',
      processId: 'test-process-id',
      tenantId: 'test-tenant-id',
      statusId: 'test-status-id'
    };

    component.selectedFilesList = [mockFile];
    component.selectedFiles = [{ id: '1', name: 'test.pdf' }];
    component.documents = [mockDocument];
    component.updateFormArray();

    // Mock the deleteDocumentFile method to avoid actual API calls
    spyOn(component, 'deleteDocumentFile').and.returnValue(true);

    component.deleteFile(0, '1');

    expect(component.selectedFilesList.length).toBe(0);
    expect(component.selectedFiles.length).toBe(0);
    expect(component.deleteDocumentFile).toHaveBeenCalledWith('1', 'test/path/test.pdf');
  });

  it('should fetch portfolio company data when companyEncryptedID is available', () => {
    component.companyEncryptedID = 'test-id';
    component.getPortfolioCompanies();
    expect(portfolioCompanyServiceMock.getPortfolioCompanyLogo).toHaveBeenCalledWith({ Value: 'test-id' });
  });

  it('should not fetch portfolio company data when companyEncryptedID is null', () => {
    component.companyEncryptedID = null;
    component.getPortfolioCompanies();
    expect(portfolioCompanyServiceMock.getPortfolioCompanyLogo).not.toHaveBeenCalled();
  });
});
