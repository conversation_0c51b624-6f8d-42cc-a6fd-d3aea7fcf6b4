export class AddClo {
    cLO_Id: number;
    companyId: number;
    uniqueID: string;
    companyName: string;
    domicile: string;
    issuer: string;
    arranger: string;
    trustee: string;
    priced: string;
    closed: string;
    lastRefiDate: string;
    lastResetDate: string;
    callEndDate: string;
    originalEndOfReinvestmentDate: string;
    currentEndOfReinvestmentDate: string;
    currentMaturityDate: string;
    lastRefiResetArranger:string;
  
    constructor(init?: Partial<AddClo>) {
      Object.assign(this, init);
    }
  }