import { Component, OnInit } from '@angular/core';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { ActivatedRoute} from "@angular/router";


@Component({
  selector: 'app-email-configuration-page',
  templateUrl: './email-configuration-page.component.html',
  styleUrls: ['./email-configuration-page.component.scss']
})
export class EmailConfigurationPageComponent implements OnInit {
  emailConfigTabs: ITab[] = [
    { name: 'User Info by Company', active: true, hidden: false },
    { name: 'Email Groups', active: false, hidden: false }
  ];
  selectedCompanies: any[] = [];
  activeTab: string = 'User Info by Company';
  createEmailGroupText: string = "Create Email Group";

  constructor(private route: ActivatedRoute
) { }

 ngOnInit() {
    this.route.queryParams.subscribe(params => {
      if (params['activeTab']) {
        this.activeTab = params['activeTab'];
        // Find and update the active tab
        this.emailConfigTabs.forEach(tab => {
          tab.active = (tab.name === this.activeTab);
        });
      }
    });
  }

  onCompanySelected(companies: any[]): void {
    this.selectedCompanies = companies;
  }

  onTabClick(tab: ITab): void {
    this.activeTab = tab.name;
    this.emailConfigTabs.forEach(t => {
      t.active = (t.name === tab.name);
    });
  }
}