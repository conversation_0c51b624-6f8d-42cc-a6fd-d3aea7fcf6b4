@import "../../../variables";
.portfolio-company-list-section {
    .performance-section {
        .outer-section {
            background: $nep-white 0% 0% no-repeat padding-box;
            border: border-color-light-gray;
            border-radius: 4px 4px 4px 4px;
            opacity: 1;
            box-shadow: 0px 0px 12px $nep-shadow-color;
        }
        .nav-link {
            background-color: $nep-white !important;
            letter-spacing: 0px;
            color: $nep-text-grey;
            font-size: 0.9rem !important;
            padding-top: 9px;
            padding-bottom: 9px;
            &.active {
                background-color: $nep-white !important;
                color: $nep-primary !important;
               // @extend .Body-R;
            }
        }
        .tab-bg,.content-bg {
            background-color: $nep-white !important;
        }
    }
}

.filter-section{
    border: 1px solid $Neutral-Gray-05;
    border-radius: 8px;
    
    .filter-by{
        border: 1px solid $Neutral-Gray-10;
        border-radius: 4px;
    }

    .sort-by {
        position: relative;
        
        &::after {
            content: '';
            position: absolute;
            right: 0;
            top: 20%;
            height: 58%;
            width: 1px;
            background-color: $Primary-100;
        }
    }

    .pl-36{
        padding-left: 36px;
    }

    .pr-36{
        padding-right: 36px;
    }
}