import { AfterViewInit, Component, EventEmitter, Input, OnInit, ViewChild } from "@angular/core";
import { Table } from "primeng/table";
import { CapTableService } from "src/app/services/cap-table.service";
import { FinancialValueUnitsEnum, MiscellaneousService, ValueTypes, ErrorMessage } from "src/app/services/miscellaneous.service";
import { isNumeric } from "@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric";
import { EsgConstants, FinancialsValueTypes, GlobalConstants, ImpactKPIConstants, KpiInfo, KpiTypesConstants, NumberDecimalConst, PeriodType, PeriodTypeFilterOptions } from "src/app/common/constants";
import {
  feed,
  getUnitTypeList,
  setCommonFilterOptionsKeys,
} from "src/app/utils/kpi-utility";
import { isNil } from "src/app/utils/utils";
import { MatMenu, MatMenuTrigger } from "@angular/material/menu";
import { filter } from "rxjs";
import { SortDescriptor } from '@progress/kendo-data-query';
import { sortAscSmallIcon } from "@progress/kendo-svg-icons";
import { sortDescSmallIcon } from "@progress/kendo-svg-icons";
import { CapTablePageLoad } from "../models/capTable-pageLoad.model";
import { Audit, MappedDocuments, ModuleCompanyModel, TableHeader } from "../../file-uploads/kpi-cell-edit/kpiValueModel";
import { ToastrService } from "ngx-toastr";
import { DropDownFilterSettings } from '@progress/kendo-angular-dropdowns';
import { AuditService } from "src/app/services/audit.service";
import { OidcAuthService } from "src/app/services/oidc-auth.service";
import { KPIModulesEnum } from "src/app/services/permission.service";
import { extractDateComponents } from "../../file-uploads/kpi-cell-edit/cell-edit-utils";
@Component({
  selector: "app-cap-table",
  templateUrl: "./cap-table.component.html",
  styleUrls: ["./cap-table.component.scss"],
})
export class CapTableComponent implements OnInit, AfterViewInit {
  public virtual: any = {
    itemHeight: 32,
    pageSize: 20
  };
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: 'contains',
  };
  exportLoading: boolean = false;
  kpiInfo = KpiInfo;
  @ViewChild("menu") uiUxMenu!: MatMenu;
  @ViewChild("masterMenuTrigger") menuTrigger: MatMenuTrigger;
  NumberDecimalConst = NumberDecimalConst;
  tableReload = false;
  frozenCols: any = [{ field: "Kpi", header: "Kpi" }];
  @Input() modelList: any;
  @Input() config: any;
  filterOptions: any[] = [];
  isMonthly: boolean = true;
  isQuarterly: boolean = false;
  isAnnually: boolean = false;
  loading = false;
  isLoader: boolean = false;
  tabList: any[] = [];
  globalFilter: string = null;
  kpiValueUnit: string = null;
  @ViewChild("dt") dt: Table | undefined;
  tableResult = [];
  tableResultClone = [];
  errorNotation: boolean = false;
  periodType: any[] = [];
  selectedPeriodType: any;
  unitTypeList: any[] = [];
  capTableValueUnit: any;
  activeTab: any = null;
  tableColumns: any[] = [];
  tableFrozenColumns: any[] = [];
  isPageLoad: boolean = false;
  selectedPeriod: string = null;
  capTablePageLoad: CapTablePageLoad[] = [];
  icons = {
    sortAscSmallIcon: sortAscSmallIcon,
    sortDescSmallIcon: sortDescSmallIcon,
  };
  selectNxtType: string = "Monthly";
  sort: SortDescriptor[] = [];
  isUploadPopupVisible: boolean = false;
  uniqueModuleCompany: ModuleCompanyModel;
  dataRow: object;
  dataColumns: TableHeader;
  componentName = "CapTable";
  infoUpdate: boolean = false;
  @Input() capTablePermission: any = [];
  hasDownloadPermission: boolean = false;
  /**
   * Represents the CapTableComponent class.
   * This component is responsible for managing the cap table.
   */
  constructor(
    private identityService: OidcAuthService,
    private auditService: AuditService,
    private capTableService: CapTableService,
    private miscService: MiscellaneousService,
    public toasterService: ToastrService,
  ) {
    this.unitTypeList = getUnitTypeList();
    this.capTableValueUnit = this.unitTypeList[2];
  }
  /**
   * Initializes the component.
   */
  ngOnInit(): void {
    this.isLoader = true;
    let activeTabs = this.config?.activeTabs.filter(x => this.capTablePermission.some(y => y.moduleId == x.moduleId && y.canView));
    this.tabList = activeTabs;
    this.capTablePageLoad = activeTabs;
    this.capTablePageLoad.forEach((x) => (x.pageLoad = true));
    if (this.tabList?.length > 0) {
      this.tabList[0].isActive = true;
      this.activeTab = this.tabList[0];
    }
    this.setPeriodsOptions(this.tabList[0]);
    this.setPeriodType();
    this.capTableValueUnit = this.unitTypeList[2];
    this.kpiValueUnit = this.capTableValueUnit?.unitType;
    this.getCapTableValues();
    this.hasDownloadPermission = this.capTablePermission.some(x => x.moduleId == this.activeTab.moduleId && x.canExport);
  }
  /**
   * Sets the period type based on the selected options.
   */
  setPeriodType() {
    let periodCondition = this.isMonthly
      ? "isMonthly"
      : this.isQuarterly
      ? "isQuarterly"
      : "isAnnually";
    this.periodType = this.config.capTablePeriods.filter(
      (x) => x.moduleId == this.activeTab.moduleId && x[periodCondition]
    );
    this.selectedPeriodType = this.periodType[0];
    this.selectedPeriod = this.selectedPeriodType?.period;
  }
  /**
   * Changes the active tab and performs necessary actions.
   * @param tab - The tab object to be set as active.
   */
  changeTabType(tab: any) {
    this.hasDownloadPermission = this.capTablePermission.some(x => x.moduleId == tab.moduleId && x.canExport);
    this.isLoader = true;
    this?.tabList?.forEach(
      (row: { isActive: boolean }) => (row.isActive = false)
    );
    tab.isActive = true;
    this.activeTab = tab;
    this.changeTabSetPageLoad();
    this.capTableValueUnit = this.unitTypeList[2];
    this.kpiValueUnit = this.capTableValueUnit?.unitType;
    this.setPeriodsOptions(tab);
    this.setPeriodType();
    this.getCapTableValues();
    this.sort = [];
  }
  periodTypeFilterOptions: any[] = [
    { field: PeriodTypeFilterOptions.Monthly, key: false},
    { field: PeriodTypeFilterOptions.Quarterly, key: false },
    { field: PeriodTypeFilterOptions.Annual, key: false }
  ];
  /**
   * Sets the periods options based on the provided page configuration tab.
   * @param pageConfigTab - The page configuration tab object.
   */
  setPeriodsOptions(pageConfigTab: any) {
    let periodOptions = this.periodTypeFilterOptions;
    let activeTabData = this.config.capTableSections
      .find((x) => x.fieldId === pageConfigTab.fieldId)
      ?.sectionName?.split(",");
    this.filterOptions = periodOptions?.filter((item) =>
      activeTabData?.some((otherItem) => otherItem === item.field)
    );
    let periodType = null;
    if (this.filterOptions.length > 0) {
      for (const element of periodOptions) {
        element.key = false;
      }
      this.filterOptions[0].key = true;
      periodType = this.filterOptions[0];
    }
    this.onChangePeriodOption(periodType);
  }
  /**
   * Handles the change event of the period option.
   * @param opt - The selected period option.
   */
  onChangePeriod(opt: any) {
    this.isLoader = true;
    this.resetPageLoad();
    this.onChangePeriodOption(opt);
    this.setPeriodType();
    this.getCapTableValues();
  }
  /**
   * Handles the change event when selecting a period option.
   * @param {any} type - The selected period option.
   */
  onChangePeriodOption(type) {
    this.filterOptions.forEach((x) => (x.key = false));
    if (type?.field == "Monthly") {
      type.key = this.isMonthly = true;
      this.isQuarterly = false;
      this.isAnnually = false;
      this.selectNxtType = this.filterOptions.some(
        (x) => x.field == PeriodTypeFilterOptions.Quarterly
      )
        ? PeriodTypeFilterOptions.Quarterly
        : this.filterOptions.some(
            (x) => x.field == PeriodTypeFilterOptions.Annual
          )
        ? PeriodTypeFilterOptions.Annual
        : PeriodTypeFilterOptions.Reset;
    } else if (type?.field == "Quarterly") {
      this.isMonthly = false;
      type.key = this.isQuarterly = true;
      this.isAnnually = false;
      this.selectNxtType = this.filterOptions.some(
        (x) => x.field == PeriodTypeFilterOptions.Annual
      )
        ? PeriodTypeFilterOptions.Annual
        : PeriodTypeFilterOptions.Reset;
    } else {
      this.isMonthly = false;
      this.isQuarterly = false;
      this.selectNxtType = PeriodTypeFilterOptions.Reset;
      if (type != undefined) type.key = this.isAnnually = true;
    }
  }
  /**
   * Sets the filter options keys.
   * @param result - The result to set the filter options keys from.
   */
  SetFilterOptionsKeys(result: any) {
    this.filterOptions = setCommonFilterOptionsKeys(this.filterOptions, result);
  }
  /**
   * Handles the form submission event.
   * @param $event - The form submission event.
   */
  onSubmit($event) {
    this.isLoader = true;
    this.selectedPeriod = this.selectedPeriodType?.period;
    this.kpiValueUnit = this.capTableValueUnit?.unitType;
    if ($event.submitter.name === "Save") {
      this.menuTrigger.closeMenu();
    } else {
      this.selectedPeriodType = this.periodType[0];
      this.capTableValueUnit = this.unitTypeList[2];
    }
    this.getCapTableValues();
  }
  /**
   * Retrieves the cap table values for the portfolio company.
   *
   * @remarks
   * This method makes an API call to fetch the cap table values based on the provided parameters.
   *
   * @returns An Observable that emits the cap table values.
   */
  getCapTableValues() {
    this.capTableService
      .getCapTableValues({
        portfolioCompanyID: this.modelList?.portfolioCompanyID,
        isMonthly: this.isMonthly,
        isQuarterly: this.isQuarterly,
        isAnnually: this.isAnnually,
        isPageLoad: true,
        moduleID: this.activeTab.moduleId,
        companyId: this.modelList?.portfolioCompanyID,
        PeriodId: this.selectedPeriodType?.periodId,
      })
      .subscribe({
        next: (result) => {
          if (result != null) {
            this.errorNotation = false;
            this.loading = false;
            this.isLoader = false;
            this.tableReload = true;
            if (result?.rows.length == 0 && this.checkPageLoad()) {
              this.updateDefaultViewFilterOptionsAndPeriod();
            }
            result.headers.forEach((element: any) => {
              element.dir = null;
            });
            this.tableColumns = result?.headers || [];
            this.tableFrozenColumns = this.frozenCols;
            this.tableResult = result?.rows || [];
            this.tableResultClone = result?.rows || [];
            this.tableResult = this.commonConvertUnits(
              this.tableResult,
              this.capTableValueUnit,
              this.tableResultClone,
              this.tableColumns
            );
            this.isPageLoad = false;
            if (this.tableResult.length > 0) {
              this.resetPageLoad();
            }
            if (result != null) {
              setTimeout(() => {
                result.isMonthly = this.isMonthly;
                result.isQuarterly = this.isQuarterly;
                result.isAnnually = this.isAnnually;
                this.SetFilterOptionsKeys(result);
              }, 1000); 
            }
          } else {
            this.clearData();
          }
          this.isLoader = false;
        },
        error: (error) => {
          this.clearData();
          this.isLoader = false;
        },
      });
  }
  /**
   * Checks if the given value is a number.
   *
   * @param str - The value to check.
   * @returns `true` if the value is a number, `false` otherwise.
   */
  isNumberCheck(str: any) {
    return isNumeric(str);
  }
  /**
   * Clears the data in the component.
   */
  clearData() {
    this.loading = false;
    this.isLoader = false;
    this.tableColumns = [];
    this.tableResult = [];
    this.tableResultClone = [];
    this.isPageLoad = false;
  }
  /**
   * Converts the values in the provided `tableResultClone` array based on the specified `kpiValueUnit` type.
   * The converted values are then pushed into a new array and returned.
   *
   * @param tableResult - The original array to be cleared and replaced with the converted values.
   * @param kpiValueUnit - The unit type used for conversion.
   * @param tableResultClone - The array containing the values to be converted.
   * @param tableColumns - The columns used for conversion calculations.
   * @returns An array containing the converted values.
   */
  commonConvertUnits(
    tableResult: any[],
    kpiValueUnit: any,
    tableResultClone: any[],
    tableColumns: any[]
  ): any[] {
    tableResult = [];
    var local = this;
    tableResultClone.forEach(function (value: any) {
      let valueClone = JSON.parse(JSON.stringify(value));
      switch (Number(kpiValueUnit.typeId)) {
        case FinancialValueUnitsEnum.Absolute:
          break;
        case FinancialValueUnitsEnum.Thousands:
          valueClone = local.capConversionValue(valueClone, tableColumns, 1000);
          break;
        case FinancialValueUnitsEnum.Millions:
          valueClone = local.capConversionValue(
            valueClone,
            tableColumns,
            1000000
          );
          break;
        case FinancialValueUnitsEnum.Billions:
          valueClone = local.capConversionValue(
            valueClone,
            tableColumns,
            1000000000
          );
          break;
      }
      tableResult.push(valueClone);
    });
    return tableResult;
  }
  /**
   * Converts the cap table value based on the provided table columns and value.
   * @param valueClone - The clone of the original value.
   * @param tableColumns - The array of table columns.
   * @param value - The value to be converted.
   * @returns The converted value clone.
   */
  capConversionValue(valueClone: any, tableColumns: any[], value: any): any {
    tableColumns.forEach((col: any, index: any) => {
      const kpiInfo = valueClone["IsOverrule"]
        ? valueClone["KPI Info"]
        : col["kpiInfo"] || valueClone["KPI Info"];
      const invalidKpiInfos = [
        KpiInfo.Percentage,
        KpiInfo.Multiple,
        KpiInfo.Number,
        KpiInfo.Text,
        "",
      ];
      if (!invalidKpiInfos.includes(kpiInfo)) {
        this.conversionByKpiInfo(valueClone, col, value);
      }
    });
    return valueClone;
  }
  /**
   * Converts the value of a specific column in the `valueClone` object by dividing it with the provided `value`.
   * If the value is not equal to 0, it performs the conversion.
   *
   * @param valueClone - The object containing the value to be converted.
   * @param col - The column object representing the specific column.
   * @param value - The value to divide the column value by.
   */
  conversionByKpiInfo(valueClone: any, col: any, value: any) {
    if (valueClone[col.field] != 0) {
      valueClone[col.field] = !isNil(valueClone[col.field])
        ? !isNaN(parseFloat(valueClone[col.field]))
          ? valueClone[col.field] / value
          : valueClone[col.field]
        : valueClone[col.field];
    }
  }
  /**
   * Lifecycle hook that is called after Angular has fully initialized the component's view.
   * It is called only once after the first `ngAfterContentChecked`.
   * Use this hook to perform any initialization logic that relies on the component's view or child views.
   */
  ngAfterViewInit() {
    if (this.uiUxMenu != undefined) {
      (this.uiUxMenu as any).closed = this.uiUxMenu.closed;
      this.configureMenuClose(this.uiUxMenu.closed);
    }
  }
  /**
   * Configures the menu close behavior.
   *
   * @param old - The original close function of the MatMenu.
   * @returns An EventEmitter that emits an event when the menu should be closed.
   */
  configureMenuClose(old: MatMenu["close"]): MatMenu["close"] {
    const upd = new EventEmitter();
    feed(
      upd.pipe(
        filter((event) => {
          if (event === "click") {
            return false;
          }
          return true;
        })
      ),
      old
    );
    return upd;
  }
  handleChange($event) {
    this.errorNotation = $event;
  }
  /**
   * Exports the cap table values for a portfolio company.
   */
  exportCapTableValues() {
    if(!this.hasDownloadPermission){
      this.showNoAccessError();
      return;
    }
    this.exportLoading = true;
    this.capTableService
      .exportCapTableValues({
        CompanyId: this.modelList?.portfolioCompanyID,
        ModuleId: this.activeTab.moduleId,
        Unit: this.capTableValueUnit.typeId,
        Period: this.selectedPeriodType.period,
        PeriodId: this.selectedPeriodType.periodId,
      })
      .subscribe({
        next: (response) => {
          this.exportLoading = false;
          this.miscService.downloadExcelFile(response);
        },
        error: () => {
          this.exportLoading = false;
        },
      });
  }
  getValue(row: any, field: string) {
    return row[field];
  }
  /**
   * This method updates the default view filter options and period based on the selected next type.
   * If the selected next type is not "Reset", it iterates over the filter options and sets the key of the matching option to true and others to false.
   * Then, it finds the selected option from the filter options and calls the `defaultCallNextState` method with the selected option as an argument.
   */
  updateDefaultViewFilterOptionsAndPeriod() {
    if (this.selectNxtType != PeriodTypeFilterOptions.Reset) {
      this.filterOptions.forEach((option) => {
        if (option.field === this.selectNxtType) {
          option.key = true;
        } else {
          option.key = false;
        }
      });
      let selectedOption = this.filterOptions.find(
        (option) => option.field === this.selectNxtType
      );
      this.defaultCallNextState(selectedOption);
    }
  }
  /**
   * This method resets all filter options.
   * It iterates over the filter options and sets the key of each option to false.
   */
  resetFilterOptions() {
    this.filterOptions.forEach((x) => (x.key = false));
  }
  /**
   * This method resets the page load status for the active tab.
   * It iterates over the `capTablePageLoad` array and sets the `pageLoad` property of the object with a matching `moduleId` to false.
   */
  resetPageLoad() {
    this.capTablePageLoad.forEach((x) => {
      if (x.moduleId == this.activeTab.moduleId) {
        x.pageLoad = false;
      }
    });
  }
  /**
   * This method checks if the page for the active tab has been loaded.
   * It finds the module corresponding to the active tab in the `capTablePageLoad` array.
   * If the module is found, it returns the `pageLoad` property of the module; otherwise, it returns false.
   * @returns {boolean} - The page load status of the active tab's module.
   */
  checkPageLoad(): boolean {
    let selectedModule = this.capTablePageLoad.find(
      (x) => x.moduleId == this.activeTab.moduleId
    );
    return selectedModule ? selectedModule.pageLoad : false;
  }
  /**
   * This method is used to set the default state for the next operation.
   * It sets `isLoader` to true, indicating that a loading operation is in progress.
   * Then, it calls `onChangePeriodOption` with the provided option, `setPeriodType` to set the period type, and `getCapTableValues` to fetch the cap table values.
   * @param {any} opt - The option to be passed to the `onChangePeriodOption` method.
   */
  defaultCallNextState(opt: any) {
    this.isLoader = true;
    this.onChangePeriodOption(opt);
    this.setPeriodType();
    this.getCapTableValues();
  }
  /**
   * This method sets the page load status for the active tab to true.
   * It iterates over the `capTablePageLoad` array and sets the `pageLoad` property of the object with a matching `moduleId` to true.
   */
  changeTabSetPageLoad() {
    this.capTablePageLoad.forEach((x) => {
      if (x.moduleId == this.activeTab.moduleId) {
        x.pageLoad = true;
      }
    });
  }
  showNoAccessError() {
    this.toasterService.error(ErrorMessage.NoAccess, "", { positionClass: "toast-center-center" });
  }
  /**
   * Initializes the edit operation for a specific row and column in the cap table.
   * @param rowData - The data of the row being edited.
   * @param column - The column being edited.
   */
  onEditInit(rowData: any, column: any) {
    let hasEditPermission = this.capTablePermission.some(x => x.moduleId == this.activeTab.moduleId && x.canEdit);
    if(!hasEditPermission){
      this.showNoAccessError();
      return;
    }
    if (this.errorNotation || column?.field.toLowerCase() == ImpactKPIConstants.KPI.toLowerCase()) {
      return;
    }
    if (
      Number(this.capTableValueUnit.typeId) !=
        FinancialValueUnitsEnum.Absolute &&
      !rowData.IsHeader
    ) {
      this.infoUpdate = true;
    } else if (!rowData.IsHeader) {
      this.uniqueModuleCompany = {
        moduleId: this.activeTab.moduleId,
        companyId: this.modelList.portfolioCompanyID,
        valueType:'Actual',
        subPageId: 0,
        capTableColPeriod: this.selectedPeriod,
        capTablePeriodId: this.selectedPeriodType?.periodId,
      };
      this.dataRow = rowData;
      this.dataColumns = column;
      this.isUploadPopupVisible = true;
    }else{
      this.showErrorToast(GlobalConstants.CapTableCellEditError);
    }
  }
  /**
   * Cancels the button event and hides the upload popup.
   */
  cancelButtonEvent() {
    this.isUploadPopupVisible = false;
  }
  /**
   * Handles the submit button event.
   * @param results - The results from the submit action.
   */
  onSubmitButtonEvent(results: any) {
    if (results.code != null && results.code.trim().toLowerCase() == "ok") {
        this.showSuccessToast(results.message);
      this.isUploadPopupVisible = false;
    } else {
      this.showErrorToast(results.message);
      this.isUploadPopupVisible = false;
    }
    this.isLoader = true;
    this.getCapTableValues();
  }
  /**
   * Closes the information update.
   */
  CloseInfo() {
    this.infoUpdate = false;
  }
  /**
   * Displays an error toast message.
   * 
   * @param message - The error message to display.
   * @param title - The title of the error toast. (optional)
   * @param position - The position of the error toast. (optional)
   */
  showErrorToast(message: string, title: string = '', position: string = EsgConstants.ToastCenterCenter): void {
    this.toasterService.error(message, title, { positionClass: position });
  }
  /**
   * Displays a success toast message.
   * 
   * @param message - The message to be displayed in the toast.
   * @param title - The title of the toast (optional).
   * @param position - The position of the toast on the screen (optional, default is 'toast-center-center').
   */
  showSuccessToast(message: string, title: string = '', position: string = EsgConstants.ToastCenterCenter): void {
    this.toasterService.success(message, title, { positionClass: position });
  }
  /**
   * Handles the audit log functionality for a specific row in the cap table.
   * @param rowData - The data of the row.
   * @param field - The field data.
   */
  onAuditLog(rowData: any, field: any) {
    const ERROR_MESSAGE = GlobalConstants.AuditLogError;
    if (!this.errorNotation || rowData.IsHeader) {
      if (this.errorNotation) {
        this.showErrorToast(ERROR_MESSAGE);
      }
      return;
    }
    const dateComponents = extractDateComponents(field.kpiId > 0 ? this.selectedPeriodType?.period: field.header);
    let auditLogFilter = this.getAuditLogFilter(rowData, dateComponents,field);
    this.auditService
      .getPortfolioEditSupportingCommentsData(auditLogFilter)
      .subscribe({
        next: (data: MappedDocuments) => {
          if (data?.auditLogId > 0 && data?.auditLogCount > 0) {
            let attributeName = rowData.Kpi;
            let isOverrule = rowData.IsOverrule;
            this.redirectToAuditLogPage(
              field,
              attributeName,
              data,
              auditLogFilter,
              isOverrule
            );
          } else if (data?.auditLogCount == 0) {
            this.showErrorToast(GlobalConstants.AuditLogNAMessage);
          }
        },
        error: (error: any) => {
          this.showErrorToast(ERROR_MESSAGE);
        },
      });
  }
  /**
   * Redirects to the audit log page with the specified parameters.
   * @param field - The field object.
   * @param attributeName - The attribute name.
   * @param data - The mapped documents.
   * @param auditLogFilter - The audit log filter.
   * @param isOverrule - Indicates whether it is an overrule.
   */
  redirectToAuditLogPage(field: any, attributeName: any, data: MappedDocuments, auditLogFilter: Audit,isOverrule:boolean) {
    let params = {
      KPI: "",
      header: field.header,
      PortfolioCompanyID: this.modelList.portfolioCompanyID,
      AttributeName: attributeName,
      ModuleId:this.activeTab.moduleId,
      currency: this.modelList?.reportingCurrencyDetail?.currencyCode,
      AttributeId: data.valueId,
      isNewAudit: true,
      KpiId: auditLogFilter.kpiId,
      MappingId: auditLogFilter.mappingId,
      periodId:this.selectedPeriodType?.periodId,
      AsOfPeriod: this.selectedPeriodType?.period,
      columnKpiId: auditLogFilter.columnKpiId,
      columnKpi: field.header,
      columnKpiInfo: field.kpiId!=null && field.kpiId > 0 && !isOverrule ? field.kpiInfo : null
    };
    sessionStorage.setItem(GlobalConstants.CurrentModule, this.getModule(this.activeTab.moduleId));
    sessionStorage.setItem(GlobalConstants.CapTableAuditLocalStorage + '_' + this.activeTab.moduleId, JSON.stringify(params));
    let config = this.identityService.getEnvironmentConfig();
    if (config.redirect_uri != "") {
      let myAppUrl = config.redirect_uri.split("/in")[0] + ImpactKPIConstants.AuditLogRoute;
      window.open(myAppUrl, '_blank');
    }
  }
  /**
   * Retrieves the module name based on the provided module ID.
   * @param moduleId - The ID of the module.
   * @returns The name of the module.
   */
  getModule(moduleId: number): string {
    switch (moduleId) {
      case KPIModulesEnum.CapTable1:
        return KpiTypesConstants.CapTable_KPI1;
      case KPIModulesEnum.CapTable2:
        return KpiTypesConstants.CapTable_KPI2;
      case KPIModulesEnum.CapTable3:
        return KpiTypesConstants.CapTable_KPI3;
      case KPIModulesEnum.CapTable4:
        return KpiTypesConstants.CapTable_KPI4;
      case KPIModulesEnum.CapTable5:
        return KpiTypesConstants.CapTable_KPI5;
      case KPIModulesEnum.CapTable6:
        return KpiTypesConstants.CapTable_KPI6;
      case KPIModulesEnum.CapTable7:
        return KpiTypesConstants.CapTable_KPI7;
      case KPIModulesEnum.CapTable8:
        return KpiTypesConstants.CapTable_KPI8;
      case KPIModulesEnum.CapTable9:
        return KpiTypesConstants.CapTable_KPI9;
      case KPIModulesEnum.CapTable10:
        return KpiTypesConstants.CapTable_KPI10;
      case KPIModulesEnum.OtherCapTable1:
        return KpiTypesConstants.OtherCapTable_KPI1;
      case KPIModulesEnum.OtherCapTable2:
        return KpiTypesConstants.OtherCapTable_KPI2;
      case KPIModulesEnum.OtherCapTable3:
        return KpiTypesConstants.OtherCapTable_KPI3;
      case KPIModulesEnum.OtherCapTable4:
        return KpiTypesConstants.OtherCapTable_KPI4;
      case KPIModulesEnum.OtherCapTable5:
        return KpiTypesConstants.OtherCapTable_KPI5;
      case KPIModulesEnum.OtherCapTable6:
        return KpiTypesConstants.OtherCapTable_KPI6;
      case KPIModulesEnum.OtherCapTable7:
        return KpiTypesConstants.OtherCapTable_KPI7;
      case KPIModulesEnum.OtherCapTable8:
        return KpiTypesConstants.OtherCapTable_KPI8;
      case KPIModulesEnum.OtherCapTable9:
        return KpiTypesConstants.OtherCapTable_KPI9;
      case KPIModulesEnum.OtherCapTable10:
        return KpiTypesConstants.OtherCapTable_KPI10;
      default:
        return '';
    }
  }
  /**
   * Retrieves the audit log filter based on the provided parameters.
   * @param rowData - The data of the row.
   * @param dateComponents - The date components including year, month, and quarter.
   * @param field - The field information.
   * @returns The audit log filter object.
   */
  getAuditLogFilter(
    rowData: any,
    dateComponents: { year: any; month: number; quarter: any },field:any
  ) {
    return <Audit>{
      valueType: "",
      kpiId: rowData["KpiId"],
      mappingId: rowData["MappingId"],
      quarter: dateComponents.quarter,
      year: dateComponents.year,
      month: dateComponents.month,
      moduleId: this.activeTab.moduleId,
      companyId: this.modelList.portfolioCompanyID,
      periodId: this.selectedPeriodType?.periodId,
      columnKpiId: field.kpiId,
      valueTypeId: (() => {
        switch (true) {
          case field.header.toUpperCase().includes(FinancialsValueTypes.LTM):
            return ValueTypes.ActualLTM;
          case field.header.toUpperCase().includes(FinancialsValueTypes.YTD):
            return ValueTypes.ActualYTD;
          default:
            return ValueTypes.Actual;
        }
      })()
    };
  }
}
