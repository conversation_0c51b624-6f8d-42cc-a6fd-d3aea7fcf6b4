import { ComponentFixture, TestBed } from "@angular/core/testing";
import { NO_ERRORS_SCHEMA } from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { ConfirmModalComponent } from "./confirm-modal.component";
import { By } from "@angular/platform-browser";
import { SharedComponentModule } from "src/app/custom-modules/shared-component.module";

describe("ConfirmModalComponent", () => {
  let component: ConfirmModalComponent;
  let fixture: ComponentFixture<ConfirmModalComponent>;

  beforeEach(() => {
    const toastrServiceStub = () => ({
      overlayContainer: {},
      success: (toasterMessage, string, object) => ({}),
      error: (toasterMessage, string, object) => ({})
    });
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [ConfirmModalComponent],
      imports:[SharedComponentModule],
      providers: [{ provide: ToastrService, useFactory: toastrServiceStub }]
    });
    fixture = TestBed.createComponent(ConfirmModalComponent);
    component = fixture.componentInstance;
  });

  it("can load instance", () => {
    expect(component).toBeTruthy();
  });

  it(`customwidth has default value`, () => {
    expect(component.customwidth).toEqual(`456px`);
  });

  it(`disablePrimaryButton has default value`, () => {
    expect(component.disablePrimaryButton).toEqual(false);
  });

  it(`IsInfoPopup has default value`, () => {
    expect(component.IsInfoPopup).toEqual(false);
  });

  it(`customTop has default value`, () => {
    expect(component.customTop).toEqual(`35%`);
  });

  it(`isToasterSuccess has default value`, () => {
    expect(component.isToasterSuccess).toEqual(true);
  });

  it(`hasHeaderStyle has default value`, () => {
    expect(component.hasHeaderStyle).toEqual(false);
  });

  describe("ngOnChanges", () => {
    it("makes expected calls", () => {
      const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(
        ToastrService
      );
      spyOn(toastrServiceStub, "success").and.callThrough();
      spyOn(toastrServiceStub, "error").and.callThrough();
      component.ngOnChanges();
      expect(toastrServiceStub.success).toHaveBeenCalled();
      expect(toastrServiceStub.error).toHaveBeenCalled();
    });
  });

  it('should display the modal title', () => {
    component.modalTitle = 'Test Title';
    fixture.detectChanges();
    const titleElement = fixture.debugElement.query(By.css('.nep-modal-title')).nativeElement;
    expect(titleElement.textContent).toContain('Test Title');
  });

  it('should trigger onPrimaryEvent when primary button is clicked', () => {
    spyOn(component, 'onPrimaryEvent');
    const primaryButton = fixture.debugElement.query(By.css('[Name="confirm-modal-primary"]')).nativeElement;
    primaryButton.click();
    expect(component.onPrimaryEvent).toHaveBeenCalled();
  });

  it('should trigger onSecondaryEvent when secondary button is clicked', () => {
    spyOn(component, 'onSecondaryEvent');
    const secondaryButton = fixture.debugElement.query(By.css('[Name="confirm-modal-secondary"]')).nativeElement;
    secondaryButton.click();
    expect(component.onSecondaryEvent).toHaveBeenCalled();
  });

  it('should close the modal when close icon is clicked', () => {
    spyOn(component, 'onCloseIconClick');
    const closeIcon = fixture.debugElement.query(By.css('#confirm-modal-close')).nativeElement;
    closeIcon.click();
    expect(component.onCloseIconClick).toHaveBeenCalled();
  });
});
