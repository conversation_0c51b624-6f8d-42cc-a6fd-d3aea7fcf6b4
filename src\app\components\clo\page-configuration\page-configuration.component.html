<div class="page-config-container" id="page-configuration" >
    <div class="page-config-header ml-4 mr-4">
        <div class="header-info Caption-R">
            Easily customize your page by adding or hiding content as needed.
        </div>
    </div>

    <div class="config-content-wrapper mr-4 ml-4">
        <div class="content">   
            <div class="panel-bar-container">
                <div class="row bborder header-page panel-bar-heading">
                    <div class="col-12 col-md-12 col-lg-12 col-xl-12 col-sm-12 col-xs-12">
                        <div class="float-left col-9 pl-0" id="page-config-filter">
                            <div class="form-group">
                                <div class="form-item" [ngClass]="selectedPageItem.pageID == 2 ? 'col-4 pl-0 pr-0' : 'col-6'">
                                  <label for="page-dropdown" class="btn k-dropdown-height-36 dropdown-info">Select Pages</label>
                                  <kendo-combobox
                                    id="page-dropdown"
                                    [clearButton]="false"
                                    [kendoDropDownFilter]="filterSettings"
                                    [(ngModel)]="selectedPageItem"
                                    #module="ngModel"
                                    [fillMode]="'solid'"
                                    [filterable]="true"
                                    name="page"
                                    [virtual]="virtual"
                                    class="w-100 k-custom-solid-dropdown k-dropdown-height-36 mr-2"
                                    [size]="'medium'"
                                    [data]="pageDropdownOptions"
                                    [valuePrimitive]="false"
                                    textField="name"
                                    placeholder="Select Page"
                                    (valueChange)="onPageDdSelectionChange($event);"
                                    valueField="pageID"
                                    [popupSettings]="{ popupClass: 'custom-popup' }"
                                  ></kendo-combobox>
                                </div>
                              
                                <div class="form-item" [ngClass]="selectedPageItem.pageID == 2 ? 'col-4 pr-0' : 'col-6'">
                                  <label for="page-company-dropdown" class="btn k-dropdown-height-36 dropdown-info">Company</label>
                                  <kendo-combobox
                                    id="page-company-dropdown"
                                    [clearButton]="false"
                                    [kendoDropDownFilter]="filterSettings"
                                    [(ngModel)]="selectedCompanyItem"
                                    #module="ngModel"
                                    [fillMode]="'solid'"
                                    [filterable]="true"
                                    name="compnay"
                                    [virtual]="virtual"
                                    class="w-100 k-custom-solid-dropdown k-dropdown-height-36 mr-2"
                                    [size]="'medium'"
                                    [data]="compnayList"
                                    [valuePrimitive]="false"
                                    textField="companyName"
                                    placeholder="Select Company"
                                    (valueChange)="onCompanyDdSelectionChange($event);"
                                    valueField="id"
                                    [popupSettings]="{ popupClass: 'custom-popup' }"
                                  ></kendo-combobox>
                                </div>
                              
                                <div class="form-item" [ngClass]="selectedPageItem.pageID == 2 ? 'col-4 pr-0' : ''">
                                  <label for="page-clo-dropdown" *ngIf="selectedPageItem.pageID == 2" class="btn k-dropdown-height-36 dropdown-info">CLO</label>
                                  <kendo-combobox
                                    id="page-clo-dropdown"
                                    [clearButton]="false"
                                    *ngIf="selectedPageItem.pageID == 2"
                                    [kendoDropDownFilter]="filterSettings"
                                    [(ngModel)]="selectedCLOItem"
                                    #module="ngModel"
                                    [fillMode]="'solid'"
                                    [filterable]="true"
                                    name="clo"
                                    [virtual]="virtual"
                                    class="w-100 k-custom-solid-dropdown k-dropdown-height-36 mr-2"
                                    [size]="'medium'"
                                    [data]="cloList"
                                    [valuePrimitive]="false"
                                    textField="issuer"
                                    placeholder="Select CLO"
                                    (valueChange)="onCLODdSelectionChange($event);"
                                    valueField="clO_ID"
                                    [popupSettings]="{ popupClass: 'custom-popup' }"
                                  ></kendo-combobox>
                                </div>
                              </div>
                              
                        </div>
                        <div class="float-right">
                            <button id="page-config-reset" type="reset" title="Clear" class="width-120 nep-button nep-button-secondary reset-btn btn-padding" (click)="reset()"
                                [disabled]="isDisabledBtn">Reset</button>
                            <button id="page-config-save-changes" type="submit" title="Save" class="nep-button nep-button-primary width-135 save-changes btn-padding"
                            [disabled]="isDisabledBtn" (click)="loadPopup()">Save Changes
                            </button>
                        </div>
                    </div>
                  </div>
                  <app-panelbar-item [subPageList]="subPageList" [subPageListClone]="subPageListClone" (checkAnyDataChangeEmmiter)="checkAnyDataChange($event)"></app-panelbar-item>
                 
            </div>
        </div>
        <div *ngIf="isPopup">
            <modal customwidth="489px" [modalTitle]="modalTitle" primaryButtonName="Confirm" secondaryButtonName="Cancel"
                (primaryButtonEvent)="OnConfig($event)" (secondaryButtonEvent)="OnCancel($event)"
                [disablePrimaryButton]="disableRenameConfirm">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-xs-12">
                        This will implement changes to the system. Are you confirm?
                    </div>
                </div>
            </modal>
          </div>
    </div>
    
      <app-loader-component *ngIf="isLoader"></app-loader-component>
</div>


