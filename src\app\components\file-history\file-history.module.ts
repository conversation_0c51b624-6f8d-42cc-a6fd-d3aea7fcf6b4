import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { FileHistoryComponent } from './file-history.component';
import { FileHistoryButtonComponent } from '../file-history-button/file-history-button.component';
import { MaterialModule } from '../../custom-modules/material.module';

// Kendo UI Modules
import { ButtonsModule } from '@progress/kendo-angular-buttons';
import { IconsModule } from '@progress/kendo-angular-icons';
import { ProgressBarModule } from '@progress/kendo-angular-progressbar';
import { InputsModule } from '@progress/kendo-angular-inputs';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { LabelModule } from '@progress/kendo-angular-label';
import { LoaderModule } from '@progress/kendo-angular-indicators';
import { LayoutModule } from '@progress/kendo-angular-layout';
import { PopupModule } from '@progress/kendo-angular-popup';

@NgModule({
  declarations: [
    FileHistoryComponent,
    FileHistoryButtonComponent
  ],    imports: [
    CommonModule,
    ReactiveFormsModule,
    MaterialModule,
    
    // Kendo UI Modules
    ButtonsModule,
    IconsModule,
    ProgressBarModule,
    InputsModule,
    DateInputsModule,
    DropDownsModule,
    LabelModule,
    LoaderModule,
    LayoutModule,
    PopupModule
  ],
  exports: [
    FileHistoryComponent,
    FileHistoryButtonComponent
  ]
})
export class FileHistoryModule { }
