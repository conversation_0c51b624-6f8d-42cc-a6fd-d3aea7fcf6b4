import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { DatePipe } from '@angular/common';
import { MiscellaneousService, PeriodTypeEnum } from '../../services/miscellaneous.service';
import { FormsModule } from '@angular/forms';
import { KpitablefiltersComponent } from './kpitablefilters.component';
import {  PeriodTypeFilterOptions } from 'src/app/common/constants';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { EsgService } from "../../services/esg.services";
import { of } from 'rxjs';
import { CurrencyService } from 'src/app/services/currency.service';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';

describe('KpitablefiltersComponent', () => {
  let component: KpitablefiltersComponent;
  let fixture: ComponentFixture<KpitablefiltersComponent>;
  let mockEsgService: any;

  beforeEach(async () => {
    const currencyServiceStub = {
      getAllCurrencies: () => of({
      })
    };
    const portfolioCompanyServiceStub = {
      getSpotRate: (spotRateModel: any) => of(1.2)
    };
    mockEsgService = {
      changeDecimal: jasmine.createSpy('changeDecimal'),
    };
    const datePipeStub = () => ({ transform: (date, string) => ({}) });
    const miscellaneousServiceStub = () => ({ bindYearList: () => ({}) });
    await TestBed.configureTestingModule({
      imports: [FormsModule, HttpClientTestingModule],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [KpitablefiltersComponent],
      providers: [
        { provide: CurrencyService, useValue: currencyServiceStub },
        { provide: PortfolioCompanyService, useValue: portfolioCompanyServiceStub },
        { provide: DatePipe, useFactory: datePipeStub },
        { provide: MiscellaneousService, useFactory: miscellaneousServiceStub },
        { provide: EsgService, useValue: mockEsgService },
        { provide: 'BASE_URL', useValue: 'http://localhost:4200/' },
      ]
    });
    spyOn(KpitablefiltersComponent.prototype, 'intilizedates');
    spyOn(KpitablefiltersComponent.prototype, 'calenderbuttons');
    fixture = TestBed.createComponent(KpitablefiltersComponent);
    component = fixture.componentInstance;
    mockEsgService = TestBed.inject(EsgService);
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  it(`collapsed has default value`, () => {
    expect(component.collapsed).toEqual(false);
  });

  it(`iscustom has default value`, () => {
    expect(component.iscustom).toEqual(false);
  });

  it(`isdate has default value`, () => {
    expect(component.isdate).toEqual(false);
  });

  it(`yearOptions has default value`, () => {
    expect(component.yearOptions).toEqual(component.yearOptions);
  });

  it(`quarterOptions has default value`, () => {
    expect(component.quarterOptions).toEqual([
      { value: "Q1", text: "Q1", number: 1 },
      { value: "Q2", text: "Q2", number: 2 },
      { value: "Q3", text: "Q3", number: 3 },
      { value: "Q4", text: "Q4", number: 4 },
    ]);
  });

  it(`unitTypeList has default value`, () => {
    expect(component.unitTypeList).toEqual(component.unitTypeList);
  });

  it(`isMatMenu has default value`, () => {
    expect(component.isMatMenu).toEqual(true);
  });

  it(`isDefaultMillion has default value`, () => {
    expect(component.isDefaultMillion).toEqual(false);
  });

    it('makes expected calls', () => {
      expect(
        KpitablefiltersComponent.prototype.intilizedates
      ).toHaveBeenCalled();
      expect(
        KpitablefiltersComponent.prototype.calenderbuttons
      ).toHaveBeenCalled();
    });

    it('makes expected calls', () => {
      spyOn(component, 'setobjectperiodtype').and.callThrough();
      component.ngOnInit();
      expect(component.setobjectperiodtype).toHaveBeenCalled();
    });


    it('should filter out unwanted period types when typeField is Monthly', () => {
      component.typeField = PeriodTypeFilterOptions.Monthly;
      component.periodTypes = [
        { type: PeriodTypeEnum.Last3Month },
        { type: PeriodTypeEnum.Last6Month },
        { type: PeriodTypeEnum.YearToDate },
        { type: 'Other' }
      ];
      component.filterPeriodTypes();
      expect(component.periodTypes).toEqual([{ type: 'Other' }]);
    });
  
    it('should filter out unwanted period types when typeField is Quarterly', () => {
      component.typeField = PeriodTypeFilterOptions.Quarterly;
      component.periodTypes = [
        { type: PeriodTypeEnum.Last3Month },
        { type: PeriodTypeEnum.Last6Month },
        { type: PeriodTypeEnum.YearToDate },
        { type: 'Other' }
      ];
      component.filterPeriodTypes();
      expect(component.periodTypes).toEqual([{ type: 'Other' }]);
    });
  
    it('should filter out unwanted period types when typeField is Annual', () => {
      component.typeField = PeriodTypeFilterOptions.Annual;
      component.periodTypes = [
        { type: PeriodTypeEnum.Last3Month },
        { type: PeriodTypeEnum.Last6Month },
        { type: PeriodTypeEnum.YearToDate },
        { type: 'Other' }
      ];
      component.filterPeriodTypes();
      expect(component.periodTypes).toEqual([{ type: 'Other' }]);
    });
  
    it('should not filter out any period types when typeField is not Monthly, Quarterly, or Annual', () => {
      component.typeField = 'Other';
      component.periodTypes = [
        { type: PeriodTypeEnum.Last3Month },
        { type: PeriodTypeEnum.Last6Month },
        { type: PeriodTypeEnum.YearToDate },
        { type: 'Other' }
      ];
      component.filterPeriodTypes();
      expect(component.periodTypes).toEqual([
        { type: PeriodTypeEnum.Last3Month },
        { type: PeriodTypeEnum.Last6Month },
        { type: PeriodTypeEnum.YearToDate },
        { type: 'Other' }
      ]);
    });
  
    it('should not emit searchFilter or call convertUnitType when submitter name is not Save', () => {
      const event = { submitter: { name: 'Not Save' } };
      spyOn(component.Kpifilter, 'emit');
      spyOn(component, 'convertUnitType');
      component.onSubmit(event);
      expect(component.Kpifilter.emit).not.toHaveBeenCalled();
      expect(component.convertUnitType).not.toHaveBeenCalled();
    });
  
    it('should not emit searchFilter or call convertUnitType when periodErrorMessage is not empty', () => {
      const event = { submitter: { name: 'Save' } };
      component.periodErrorMessage = 'Error';
      spyOn(component.Kpifilter, 'emit');
      spyOn(component, 'convertUnitType');
      component.onSubmit(event);
      expect(component.Kpifilter.emit).not.toHaveBeenCalled();
      expect(component.convertUnitType).not.toHaveBeenCalled();
    });

  describe('Quarter and Year events', () => {
    beforeEach(() => {
      spyOn(component, 'checkvalidation_Qauter_Year');
      component.model = { someField: 'someValue' };
    });
  
    it('should call checkvalidation_Qauter_Year when fromQuaterevent is triggered', () => {
      component.fromQuaterevent({});
      expect(component.checkvalidation_Qauter_Year).toHaveBeenCalledWith(component.model);
    });
  
    it('should call checkvalidation_Qauter_Year when toQuaterevent is triggered', () => {
      component.toQuaterevent({});
      expect(component.checkvalidation_Qauter_Year).toHaveBeenCalledWith(component.model);
    });
  
    it('should call checkvalidation_Qauter_Year when fromYearevent is triggered', () => {
      component.fromYearevent({});
      expect(component.checkvalidation_Qauter_Year).toHaveBeenCalledWith(component.model);
    });
  
    it('should call checkvalidation_Qauter_Year when toYearevent is triggered', () => {
      component.toYearevent({});
      expect(component.checkvalidation_Qauter_Year).toHaveBeenCalledWith(component.model);
    });
  });
  
    it('should set periodErrorMessage when FromYear is greater than ToYear', () => {
      component.model = {
        fromQuarter: { number: 'Q1' },
        fromYear: { value: '2022' },
        toQuarter: { number: 'Q4' },
        toYear: { value: '2021' }
      };
      component.checkvalidation_Qauter_Year(component.model);
      expect(component.periodErrorMessage).toBe('Invalid Period Range! FromYear cannot be after ToYear!');
    });
  
    it('should set periodErrorMessage when FromYear is equal to ToYear and FromQuarter is greater than ToQuarter', () => {
      component.model = {
        fromQuarter: { number: 'Q4' },
        fromYear: { value: '2021' },
        toQuarter: { number: 'Q1' },
        toYear: { value: '2021' }
      };
      component.checkvalidation_Qauter_Year(component.model);
      expect(component.periodErrorMessage).toBe('Invalid period range! To quarter cannot be less than from quarter');
    });
  
    it('should not set periodErrorMessage when FromYear is less than ToYear', () => {
      component.model = {
        fromQuarter: { number: 'Q1' },
        fromYear: { value: '2021' },
        toQuarter: { number: 'Q4' },
        toYear: { value: '2022' }
      };
      component.checkvalidation_Qauter_Year(component.model);
      expect(component.periodErrorMessage).toBe('');
    });
  
    it('should not set periodErrorMessage when FromYear is equal to ToYear and FromQuarter is less than ToQuarter', () => {
      component.model = {
        fromQuarter: { number: 'Q1' },
        fromYear: { value: '2021' },
        toQuarter: { number: 'Q4' },
        toYear: { value: '2021' }
      };
      component.checkvalidation_Qauter_Year(component.model);
      expect(component.periodErrorMessage).toBe('');
    });
  
  
    it('should toggle collapsed state when toggle is called', () => {
      component.collapsed = false;
      component.toggle();
      expect(component.collapsed).toBe(true);
      component.toggle();
      expect(component.collapsed).toBe(false);
    });
  
    it('should set collapsed to false when expand is called', () => {
      component.collapsed = true;
      component.expand();
      expect(component.collapsed).toBe(false);
    });
  
    it('should set collapsed to true when collapse is called', () => {
      component.collapsed = false;
      component.collapse();
      expect(component.collapsed).toBe(true);
    });
  
  
    it('should set isdate to true when event value type is DATE_RANGE', () => {
      const event = { value: { type: component.DATE_RANGE } };
      component.onPeriodChange(event);
      expect(component.dateRange).toEqual(undefined);
      expect(component.periodErrorMessage).toBe('');
      expect(component.iscustom).toBe(false);
      expect(component.isdate).toBe(false);
    });
  
    it('should set iscustom to true when event value type is CUSTOM', () => {
      const event = { value: { type: component.CUSTOM } };
      component.onPeriodChange(event);
      expect(component.dateRange).toEqual(undefined);
      expect(component.periodErrorMessage).toBe('');
      expect(component.isdate).toBe(false);
    });
  
    it('should set neither isdate nor iscustom to true when event value type is neither DATE_RANGE nor CUSTOM', () => {
      const event = { value: { type: 'OTHER' } };
      component.onPeriodChange(event);
      expect(component.dateRange).toEqual(undefined);
      expect(component.periodErrorMessage).toBe('');
      expect(component.iscustom).toBe(false);
      expect(component.isdate).toBe(false);
    });
 
    it('should call onDateSelect with null when fromchangeSelect is called', () => {
      spyOn(component, 'onDateSelect');
      component.fromchangeSelect(null);
      expect(component.onDateSelect).toHaveBeenCalledWith(null);
    });

    it('should set periodErrorMessage to "Select Date..!" when fromDate or toDate is null', () => {
      let model = { fromDate: null, toDate: new Date() };
      component.validateKPIPeriod(model);
      expect(component.periodErrorMessage).toBe('Select Date..!');
  
      model = { fromDate: new Date(), toDate: null };
      component.validateKPIPeriod(model);
      expect(component.periodErrorMessage).toBe('Select Date..!');
    });
  
    it('should not set periodErrorMessage when fromDate and toDate are not null', () => {
      const model = { fromDate: new Date(), toDate: new Date() };
      component.validateKPIPeriod(model);
      expect(component.periodErrorMessage).toBe('Select Date..!');
    });
  
    it('should set minDate, fromDate, and toDate to null', () => {
      component.onDateClear();
      expect(component.minDate).toBeNull();
      expect(component.model.fromDate).toBeNull();
      expect(component.model.toDate).toBeNull();
    });

  
    it('should set toDate to the passed event value', () => {
      const event = new Date();
      component.tochangeSelect(event);
      expect(component.model.toDate).toBe(event);
    });

  
    it(' convertUnitType should emit investmentKpiValueUnit through Valuesfilter', () => {
      component.investmentKpiValueUnit = 'Million';
      spyOn(component.Valuesfilter, 'emit');
      component.convertUnitType();
      expect(component.Valuesfilter.emit).toHaveBeenCalledWith('Million');
    });
  
    it('should call filterPeriodTypes when filterTabNames has tabname', () => {
      spyOn(component, 'filterPeriodTypes');
      component.tabname = 'Tab1';
      component.filterTabNames = new Set(['Tab1', 'Tab2']);
      component.ngOnChanges();
      expect(component.filterPeriodTypes).toHaveBeenCalled();
    });
  
    it('should not call filterPeriodTypes when filterTabNames does not have tabname', () => {
      component.tabname = 'Tab3';
      component.filterTabNames = new Set(['Tab1', 'Tab2']);
      spyOn(component, 'filterPeriodTypes');
      component.ngOnChanges();
      expect(component.filterPeriodTypes).not.toHaveBeenCalled();
    });
  
    it('should set datePicker.overlayVisible to false if toDate is not null', () => {
      component.dateRange = [new Date(), new Date()];
      component.datePicker = { overlayVisible: true };
      component.onDateSelect(null);
      expect(component.datePicker.overlayVisible).toBeTrue();
    });

    it('should set selectedDecimal to the unitType of the input', () => {
      const decimal = { unitType: 1 };
      component.onDecimalSelection(decimal);
      expect(component.selectedDecimal).toEqual(1);
    });
  
});
