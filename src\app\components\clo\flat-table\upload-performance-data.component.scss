// Import necessary styles
@import '../../../../assets/dist/css/font.scss';
@import '../../../../variables';

// Define color variables
$color-black: #000000;
$color-white: #FFFFFF;
$color-light-blue: #EBF3FF;
$color-active-blue: #D9EDFF;
$color-border: #F2F2F2;
$color-border-active: #93B0ED;

// Define common styles
$padding-small: 0.5rem 1rem;
$padding-medium: 10px 1rem;
$border-radius-small: 4px;

// Vertical Divider
.vertical-divider {
  border-right: 1px solid #DEDFE0;
  height: 13.25rem;
}

// Word Wrap
.clo-word-wrap {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Upload Header
.upload-header {
  font-size: 14px;
}

// Template Download Button
.template-download {
  color: #4061C7;
}

// Zero State Height
.zero-state-height {
  height: 124px;
}

// Uploaded File Style
.uploaded-file-style {
  width: 150px;
}

// Default Font Style
.default-font-style {
  font-size: 12px;
}

// Invalid File Style
.invalid-file-style {
  background-color: #f5f9ff;
  outline: 1px solid #DE3139;
  border-radius: 4px;
}

// Valid File Style
.valid-file-style {
  background-color: #f5f9ff;
  border-radius: 4px;
}

// Upload Dialogue
.upload-dialogue .k-button-md.k-icon-button .k-button-icon {
  display: none !important;
}

// Upload Image Styles
.upload-image-disabled {
  opacity: 0.5;
}

.upload-image-enabled {
  opacity: 1;
}

// Browse Icon
.browse-icon {
  transition: filter 0.3s ease;
}

button:hover .browse-icon {
  filter: brightness(0) invert(1); /* Changes the color to white */
}