import { Injectable, ElementRef } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { FileUploadProgressService } from './file-upload-progress.service';

@Injectable({
  providedIn: 'root'
})
export class FileHistoryService {
  private _showPopup = new BehaviorSubject<boolean>(false);
  public showPopup$ = this._showPopup.asObservable();
  
  // Store the anchor element for positioning the popup
  private _anchorElement = new BehaviorSubject<ElementRef>(null);
  public anchorElement$ = this._anchorElement.asObservable();

  constructor(private http: HttpClient, private uploadProgressService: FileUploadProgressService) {}

  toggleFileHistoryPopup(anchorElement?: ElementRef): void {
    if (anchorElement) {
      this._anchorElement.next(anchorElement);
    }
    this._showPopup.next(!this._showPopup.value);
  }

  showFileHistoryPopup(anchorElement: ElementRef): void {
    this._anchorElement.next(anchorElement);
    this._showPopup.next(true);
  }

  hideFileHistoryPopup(): void {
    this._showPopup.next(false);
  }
}
