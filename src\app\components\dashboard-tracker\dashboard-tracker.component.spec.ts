import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { of, Observable, throwError } from 'rxjs';
import { DashboardTrackerComponent } from './dashboard-tracker.component';
import { DashboardTrackerService } from '../../services/dashboard-tracker.service';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { GridDataResult } from '@progress/kendo-angular-grid';
import { ToastrService } from 'ngx-toastr';
import { DashboardCellValueDto, SaveDashboardCellValuesDto } from './model/dashboard-tracker-config.model';

class MockDashboardTrackerService {
  response: any = {
    data: [{ id: 1, name: 'Test' }],
    columns: [{ field: 'id' }, { field: 'name' }],
    totalRecords: 1
  };

  getDashboardTableData() {
    return of(this.response);
  }

  saveDashboardCellValues(payload: SaveDashboardCellValuesDto) {
    return of({ success: true });
  }

  deleteDashboardTrackerConfigs(configs: any[]) {
    return of({ success: true });
  }
}

class MockToastrService {
  success(message: string, title?: string, options?: any) {}
  error(message: string, title?: string, options?: any) {}
}
describe('DashboardTrackerComponent', () => {
  let component: DashboardTrackerComponent;
  let fixture: ComponentFixture<DashboardTrackerComponent>;
  let dashboardTrackerService: MockDashboardTrackerService;
  let toastrService: MockToastrService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DashboardTrackerComponent],
      providers: [
        { provide: DashboardTrackerService, useClass: MockDashboardTrackerService },
        { provide: ToastrService, useClass: MockToastrService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(DashboardTrackerComponent);
    component = fixture.componentInstance;
    dashboardTrackerService = TestBed.inject(DashboardTrackerService) as any;
    toastrService = TestBed.inject(ToastrService) as any;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default property values', () => {
    expect(component.passedClass).toBe('');
    expect(component.isDashboardConfigurationTab).toBe(false);
    expect(component.isLoading).toBe(true);
    expect(component.moreColumn).toBe(false);
    expect(component.gridColumns).toEqual([]);
    expect(component.totalRecords).toBe(0);
    expect(component.state).toEqual({ skip: 0, take: 100 });
    expect(component.view).toBeUndefined();
  });

  it('should load data and columns on ngOnInit (positive scenario)', (done) => {
    fixture.detectChanges(); // triggers ngOnInit
    component.view.subscribe((result: GridDataResult) => {
      expect(result.data).toEqual([{ id: 1, name: 'Test' }]);
      expect(result.total).toBe(1);
      // gridColumns will have extra properties (e.g., hide), so check only field property
      expect(component.gridColumns[0].field).toBe('id');
      expect(component.gridColumns[1].field).toBe('name');
      expect(component.isLoading).toBe(false);
      done();
    });
  });

  it('should set totalRecords to 0 if not present in response', (done) => {
    dashboardTrackerService.response = {
      data: [{ id: 2 }],
      columns: [{ field: 'id' }]
    };
    fixture.detectChanges();
    component.view.subscribe((result: GridDataResult) => {
      expect(component.totalRecords).toBe(0);
      expect(result.total).toBe(0);
      done();
    });
  });

  it('should handle null response (negative scenario)', (done) => {
    spyOn(dashboardTrackerService, 'getDashboardTableData').and.returnValue(of(null));
    fixture.detectChanges();
    component.view.subscribe((result: GridDataResult) => {
      expect(result.data).toEqual([]);
      expect(result.total).toBe(0);
      expect(component.gridColumns).toEqual([]);
      expect(component.isLoading).toBe(false);
      done();
    });
  });

  it('should handle response with missing data or columns (edge case)', (done) => {
    dashboardTrackerService.response = { foo: 'bar' };
    fixture.detectChanges();
    component.view.subscribe((result: GridDataResult) => {
      expect(result.data).toEqual([]);
      expect(result.total).toBe(0);
      expect(component.gridColumns).toEqual([]);
      done();
    });
  });

  it('should handle empty data and columns (edge case)', (done) => {
    dashboardTrackerService.response = { data: [], columns: [], totalRecords: 0 };
    fixture.detectChanges();
    component.view.subscribe((result: GridDataResult) => {
      expect(result.data).toEqual([]);
      expect(result.total).toBe(0);
      expect(component.gridColumns).toEqual([]);
      done();
    });
  });

  it('should update state and reload data on dataStateChange', (done) => {
    fixture.detectChanges();
    const newState = { skip: 10, take: 10 };
    spyOn(component, 'loadDashboardTableData').and.callThrough();
    component.dataStateChange(newState);
    expect(component.state).toEqual(newState);
    expect(component.loadDashboardTableData).toHaveBeenCalledWith(newState);
    // Check observable emits after state change
    component.view.subscribe((result: GridDataResult) => {
      expect(result.data).toEqual([{ id: 1, name: 'Test' }]);
      done();
    });
  });

  it('should set isLoading true while loading and false after', (done) => {
    let isLoadingDuringCall = undefined;
    spyOn(dashboardTrackerService, 'getDashboardTableData').and.callFake(() => {
      isLoadingDuringCall = component.isLoading;
      return of({ data: [], columns: [] });
    });
    component.loadDashboardTableData({ skip: 0, take: 1 });
    expect(isLoadingDuringCall).toBe(true);
    setTimeout(() => {
      expect(component.isLoading).toBe(false);
      done();
    }, 0);
  });

  it('should call navigateToDashboardConfig without error', () => {
    expect(() => component.navigateToDashboardConfig()).not.toThrow();
  });

  it('should handle navigateToDashboardConfig method call', () => {
    // This method is currently empty but should not throw errors
    expect(() => component.navigateToDashboardConfig()).not.toThrow();
    
    // Verify the method exists and is callable
    expect(typeof component.navigateToDashboardConfig).toBe('function');
  });

  it('should handle rapid consecutive dataStateChange calls (edge case)', (done) => {
    fixture.detectChanges();
    const states = [
      { skip: 0, take: 5 },
      { skip: 5, take: 5 },
      { skip: 10, take: 5 }
    ];
    let callCount = 0;
    spyOn(component, 'loadDashboardTableData').and.callFake(() => {
      callCount++;
      component.view = of({ data: [], total: 0 });
    });
    states.forEach(s => component.dataStateChange(s));
    expect(callCount).toBe(states.length);
    done();
  });

  describe('onDropdownValueChange', () => {
    it('should call updateCellValue with correct parameters', () => {
      const mockDataItem = { PCID: 1, FundID: 2, testColumn: 'oldValue' };
      const mockColumn = { id: 3, name: 'testColumn', dataType: 1 };
      const newValue = 'newValue';

      spyOn(component as any, 'updateCellValue');

      component.onDropdownValueChange(newValue, mockDataItem, mockColumn);

      expect((component as any).updateCellValue).toHaveBeenCalledWith(mockDataItem, mockColumn, newValue);
    });
  });

  describe('onTextboxValueChange', () => {
    it('should handle valid input and call textboxChangeSubject', fakeAsync(() => {
      const mockDataItem = { PCID: 1, FundID: 2, testColumn: 'oldValue' };
      const mockColumn = { id: 3, name: 'testColumn', dataType: 1 };
      const newValue = 'validValue';

      spyOn(component as any, 'validateInput').and.returnValue({ isValid: true, errorMessage: '' });
      spyOn((component as any).textboxChangeSubject, 'next');

      component.onTextboxValueChange(newValue, mockDataItem, mockColumn);

      expect(mockDataItem[mockColumn.name]).toBe(newValue);
      expect((component as any).textboxChangeSubject.next).toHaveBeenCalledWith({
        value: newValue,
        dataItem: mockDataItem,
        column: mockColumn
      });
      expect(component.validationErrors.size).toBe(0);
    }));

    it('should handle invalid input and store validation error', () => {
      const mockDataItem = { PCID: 1, FundID: 2, testColumn: 'oldValue' };
      const mockColumn = { id: 3, name: 'testColumn', dataType: 2 };
      const invalidValue = 'invalidNumber';

      spyOn(component as any, 'validateInput').and.returnValue({
        isValid: false,
        errorMessage: 'Please enter a valid number'
      });

      component.onTextboxValueChange(invalidValue, mockDataItem, mockColumn);

      expect(mockDataItem[mockColumn.name]).toBe(invalidValue);
      expect(component.validationErrors.size).toBe(1);
      expect(component.validationErrors.get('1_3')).toBe('Please enter a valid number');
    });
  });

  describe('clearTextboxValue', () => {
    it('should clear validation error and call onTextboxValueChange with empty string', () => {
      const mockDataItem = { PCID: 1, FundID: 2, testColumn: 'value' };
      const mockColumn = { id: 3, name: 'testColumn', dataType: 1 };

      // Set up validation error first
      component.validationErrors.set('1_3', 'test error');

      spyOn(component, 'onTextboxValueChange');

      component.clearTextboxValue(mockDataItem, mockColumn);

      expect(component.validationErrors.has('1_3')).toBeFalse();
      expect(component.onTextboxValueChange).toHaveBeenCalledWith('', mockDataItem, mockColumn);
    });
  });

  describe('updateCellValue', () => {
    it('should add new cell value to collection', () => {
      const mockDataItem = { PCID: 1, FundID: 2, testColumn: 'oldValue' };
      const mockColumn = { id: 3, name: 'testColumn', timeSeriesID: 'ts1' };
      const newValue = 'newValue';

      spyOn(component.cellChangesUpdated, 'emit');

      (component as any).updateCellValue(mockDataItem, mockColumn, newValue);

      expect(component.cellValueChanges.length).toBe(1);
      expect(component.cellValueChanges[0]).toEqual({
        PortfolioCompanyId: 1,
        FundId: 2,
        ColumnId: 3,
        TimeSeriesID: 'ts1',
        CellValue: newValue
      });
      expect(mockDataItem[mockColumn.name]).toBe(newValue);
      expect(component.cellChangesUpdated.emit).toHaveBeenCalledWith(1);
    });

    it('should update existing cell value in collection', () => {
      const mockDataItem = { PCID: 1, FundID: 2, testColumn: 'oldValue' };
      const mockColumn = { id: 3, name: 'testColumn', timeSeriesID: 'ts1' };

      // Add initial value
      component.cellValueChanges.push({
        PortfolioCompanyId: 1,
        FundId: 2,
        ColumnId: 3,
        TimeSeriesID: 'ts1',
        CellValue: 'initialValue'
      });

      const newValue = 'updatedValue';
      spyOn(component.cellChangesUpdated, 'emit');

      (component as any).updateCellValue(mockDataItem, mockColumn, newValue);

      expect(component.cellValueChanges.length).toBe(1);
      expect(component.cellValueChanges[0].CellValue).toBe(newValue);
      expect(component.cellChangesUpdated.emit).toHaveBeenCalledWith(1);
    });

    it('should show error for missing required IDs', () => {
      const mockDataItem = { FundID: 2 }; // Missing PCID
      const mockColumn = { id: 3, name: 'testColumn' };
      const newValue = 'newValue';

      spyOn(toastrService, 'error');

      (component as any).updateCellValue(mockDataItem, mockColumn, newValue);

      expect(toastrService.error).toHaveBeenCalledWith(
        'Missing required IDs for cell value update.',
        '',
        { positionClass: 'toast-center-center' }
      );
      expect(component.cellValueChanges.length).toBe(0);
    });

    it('should not save when validation errors exist', () => {
      const mockDataItem = { PCID: 1, FundID: 2, testColumn: 'value' };
      const mockColumn = { id: 3, name: 'testColumn' };
      const newValue = 'newValue';

      // Set validation error
      component.validationErrors.set('1_3', 'validation error');

      (component as any).updateCellValue(mockDataItem, mockColumn, newValue);

      expect(component.cellValueChanges.length).toBe(0);
    });
  });

  describe('saveCellValues', () => {
    it('should save cell values successfully', () => {
      const mockCellValue: DashboardCellValueDto = {
        PortfolioCompanyId: 1,
        FundId: 2,
        ColumnId: 3,
        TimeSeriesID: 'ts1',
        CellValue: 'testValue'
      };

      component.cellValueChanges = [mockCellValue];
      spyOn(dashboardTrackerService, 'saveDashboardCellValues').and.returnValue(of({ success: true }));
      spyOn(toastrService, 'success');
      spyOn(component.cellChangesUpdated, 'emit');

      component.saveCellValues();

      expect(dashboardTrackerService.saveDashboardCellValues).toHaveBeenCalledWith({
        CellValues: [mockCellValue]
      });
      expect(toastrService.success).toHaveBeenCalledWith(
        'Modified values saved successfully.',
        '',
        { positionClass: 'toast-center-center' }
      );
      expect(component.cellValueChanges.length).toBe(0);
      expect(component.cellChangesUpdated.emit).toHaveBeenCalledWith(0);
    });

    it('should not save when no changes exist', () => {
      component.cellValueChanges = [];
      spyOn(dashboardTrackerService, 'saveDashboardCellValues');

      component.saveCellValues();

      expect(dashboardTrackerService.saveDashboardCellValues).not.toHaveBeenCalled();
    });

    it('should not save when validation errors exist', () => {
      component.cellValueChanges = [{ PortfolioCompanyId: 1, FundId: 2, ColumnId: 3, CellValue: 'test' }];
      component.validationErrors.set('test_key', 'validation error');

      spyOn(dashboardTrackerService, 'saveDashboardCellValues');
      spyOn(toastrService, 'error');

      component.saveCellValues();

      expect(dashboardTrackerService.saveDashboardCellValues).not.toHaveBeenCalled();
      expect(toastrService.error).toHaveBeenCalledWith(
        'Please fix validation errors before saving.',
        '',
        { positionClass: 'toast-center-center' }
      );
    });

    it('should handle save error', () => {
      component.cellValueChanges = [{ PortfolioCompanyId: 1, FundId: 2, ColumnId: 3, CellValue: 'test' }];
      spyOn(dashboardTrackerService, 'saveDashboardCellValues').and.returnValue(throwError('Save failed'));
      spyOn(toastrService, 'error');

      component.saveCellValues();

      expect(toastrService.error).toHaveBeenCalledWith(
        'Failed to save cell values.',
        '',
        { positionClass: 'toast-center-center' }
      );
    });
  });

  describe('getPendingChangesCount', () => {
    it('should return correct count of pending changes', () => {
      component.cellValueChanges = [
        { PortfolioCompanyId: 1, FundId: 2, ColumnId: 3, CellValue: 'test1' },
        { PortfolioCompanyId: 1, FundId: 2, ColumnId: 4, CellValue: 'test2' }
      ];

      expect(component.getPendingChangesCount()).toBe(2);
    });

    it('should return 0 when no pending changes', () => {
      component.cellValueChanges = [];
      expect(component.getPendingChangesCount()).toBe(0);
    });
  });

  describe('clearPendingChanges', () => {
    it('should clear all pending changes and validation errors', () => {
      component.cellValueChanges = [{ PortfolioCompanyId: 1, FundId: 2, ColumnId: 3, CellValue: 'test' }];
      component.validationErrors.set('test_key', 'error');
      spyOn(component.cellChangesUpdated, 'emit');

      component.clearPendingChanges();

      expect(component.cellValueChanges.length).toBe(0);
      expect(component.validationErrors.size).toBe(0);
      expect(component.cellChangesUpdated.emit).toHaveBeenCalledWith(0);
    });
  });

  describe('getCurrentChanges', () => {
    it('should return copy of current changes', () => {
      const mockChanges = [
        { PortfolioCompanyId: 1, FundId: 2, ColumnId: 3, CellValue: 'test1' },
        { PortfolioCompanyId: 1, FundId: 2, ColumnId: 4, CellValue: 'test2' }
      ];
      component.cellValueChanges = mockChanges;

      const result = component.getCurrentChanges();

      expect(result).toEqual(mockChanges);
      expect(result).not.toBe(mockChanges); // Should be a copy, not the same reference
    });
  });

  describe('getValidationKey', () => {
    it('should generate correct validation key', () => {
      const mockDataItem = { PCID: 123 };
      const mockColumn = { id: 456 };

      const result = (component as any).getValidationKey(mockDataItem, mockColumn);

      expect(result).toBe('123_456');
    });
  });

  describe('validateInput', () => {
    it('should return valid for empty string', () => {
      const result = (component as any).validateInput('', 2);
      expect(result.isValid).toBeTrue();
      expect(result.errorMessage).toBe('');
    });

    it('should return valid for whitespace only string', () => {
      const result = (component as any).validateInput('   ', 2);
      expect(result.isValid).toBeTrue();
      expect(result.errorMessage).toBe('');
    });

    it('should validate number datatype', () => {
      const validResult = (component as any).validateInput('123.45', 2);
      expect(validResult.isValid).toBeTrue();

      const invalidResult = (component as any).validateInput('abc', 2);
      expect(invalidResult.isValid).toBeFalse();
      expect(invalidResult.errorMessage).toBe('Please enter a valid number');
    });

    it('should validate date datatype', () => {
      const validResult = (component as any).validateInput('25/12/2023', 3);
      expect(validResult.isValid).toBeTrue();

      const invalidResult = (component as any).validateInput('invalid-date', 3);
      expect(invalidResult.isValid).toBeFalse();
      expect(invalidResult.errorMessage).toBe('Please enter date in DD/MM/YYYY format');
    });

    it('should return valid for unknown datatype', () => {
      const result = (component as any).validateInput('any value', 999);
      expect(result.isValid).toBeTrue();
      expect(result.errorMessage).toBe('');
    });
  });

  describe('validateNumber', () => {
    it('should validate positive numbers', () => {
      const result = (component as any).validateNumber('123');
      expect(result.isValid).toBeTrue();
      expect(result.errorMessage).toBe('');
    });

    it('should validate negative numbers', () => {
      const result = (component as any).validateNumber('-123');
      expect(result.isValid).toBeTrue();
      expect(result.errorMessage).toBe('');
    });

    it('should validate decimal numbers', () => {
      const result = (component as any).validateNumber('123.45');
      expect(result.isValid).toBeTrue();
      expect(result.errorMessage).toBe('');
    });

    it('should reject invalid number formats', () => {
      const testCases = ['abc', '12.34.56', '12a34', ''];

      testCases.forEach(testCase => {
        const result = (component as any).validateNumber(testCase);
        expect(result.isValid).toBeFalse();
        expect(result.errorMessage).toContain('valid number');
      });
    });

    it('should handle numbers with leading/trailing spaces', () => {
      const result = (component as any).validateNumber('  123.45  ');
      expect(result.isValid).toBeTrue();
    });
  });

  describe('validateDate', () => {
    it('should validate correct date format', () => {
      const result = (component as any).validateDate('25/12/2023');
      expect(result.isValid).toBeTrue();
      expect(result.errorMessage).toBe('');
    });

    it('should reject invalid date format', () => {
      const invalidFormats = ['2023-12-25', '25-12-2023', '25/12/23', 'Dec 25, 2023'];

      invalidFormats.forEach(format => {
        const result = (component as any).validateDate(format);
        expect(result.isValid).toBeFalse();
        expect(result.errorMessage).toBe('Please enter date in DD/MM/YYYY format');
      });
    });

    it('should reject invalid month', () => {
      const result = (component as any).validateDate('25/13/2023');
      expect(result.isValid).toBeFalse();
      expect(result.errorMessage).toBe('Invalid month (01-12)');
    });

    it('should reject invalid day', () => {
      const result = (component as any).validateDate('32/12/2023');
      expect(result.isValid).toBeFalse();
      expect(result.errorMessage).toBe('Invalid day (01-31)');
    });

    it('should reject invalid dates like February 30th', () => {
      const result = (component as any).validateDate('30/02/2023');
      expect(result.isValid).toBeFalse();
      expect(result.errorMessage).toBe('Invalid date');
    });

    it('should handle leap year correctly', () => {
      const validLeapYear = (component as any).validateDate('29/02/2024');
      expect(validLeapYear.isValid).toBeTrue();

      const invalidLeapYear = (component as any).validateDate('29/02/2023');
      expect(invalidLeapYear.isValid).toBeFalse();
    });
  });

  describe('hasValidationError', () => {
    it('should return true when validation error exists', () => {
      const mockDataItem = { PCID: 1 };
      const mockColumn = { id: 3 };
      component.validationErrors.set('1_3', 'test error');

      const result = component.hasValidationError(mockDataItem, mockColumn);
      expect(result).toBeTrue();
    });

    it('should return false when no validation error exists', () => {
      const mockDataItem = { PCID: 1 };
      const mockColumn = { id: 3 };

      const result = component.hasValidationError(mockDataItem, mockColumn);
      expect(result).toBeFalse();
    });
  });

  describe('getValidationError', () => {
    it('should return validation error message when exists', () => {
      const mockDataItem = { PCID: 1 };
      const mockColumn = { id: 3 };
      const errorMessage = 'test validation error';
      component.validationErrors.set('1_3', errorMessage);

      const result = component.getValidationError(mockDataItem, mockColumn);
      expect(result).toBe(errorMessage);
    });

    it('should return empty string when no validation error exists', () => {
      const mockDataItem = { PCID: 1 };
      const mockColumn = { id: 3 };

      const result = component.getValidationError(mockDataItem, mockColumn);
      expect(result).toBe('');
    });
  });

  describe('hasErrors', () => {
    it('should return true when validation errors exist', () => {
      component.validationErrors.set('test_key', 'test error');
      expect(component.hasErrors()).toBeTrue();
    });

    it('should return false when no validation errors exist', () => {
      component.validationErrors.clear();
      expect(component.hasErrors()).toBeFalse();
    });
  });

  describe('ngOnDestroy', () => {
    it('should complete destroy subject', () => {
      spyOn((component as any).destroy$, 'next');
      spyOn((component as any).destroy$, 'complete');

      component.ngOnDestroy();

      expect((component as any).destroy$.next).toHaveBeenCalled();
      expect((component as any).destroy$.complete).toHaveBeenCalled();
    });
  });

  describe('setupTextboxDebounce', () => {
    it('should setup debounced textbox changes', fakeAsync(() => {
      const mockData = {
        value: 'test',
        dataItem: { PCID: 1, FundID: 2 },
        column: { id: 3, name: 'testColumn' }
      };

      // Spy on the private method before component initialization
      spyOn(component as any, 'updateCellValue');

      // Re-initialize the component to set up the debounce subscription
      component.ngOnInit();

      // Trigger the subject
      (component as any).textboxChangeSubject.next(mockData);

      // Fast-forward time by 2 seconds (debounce time)
      tick(2000);

      expect((component as any).updateCellValue).toHaveBeenCalledWith(
        mockData.dataItem,
        mockData.column,
        mockData.value
      );
    }));
  });

  describe('setColumnVisibility', () => {
    it('should hide all except Fund/Portfolio when selectedNames is empty', () => {
      component.gridColumns = [
        { name: 'Fund Name' },
        { name: 'Portfolio Company Name' },
        { name: 'Other Column' }
      ];
      component.setColumnVisibility([]);
      expect(component.gridColumns[0].hide).toBe(false);
      expect(component.gridColumns[1].hide).toBe(false);
      expect(component.gridColumns[2].hide).toBe(true);
    });

    it('should show only selected columns', () => {
      component.gridColumns = [
        { name: 'Fund Name' },
        { name: 'Portfolio Company Name' },
        { name: 'Other Column' }
      ];
      component.setColumnVisibility(['Other Column']);
      expect(component.gridColumns[2].hide).toBe(false);
    });
  });

  describe('emitColumnVisibility', () => {
    it('should emit only visible columns', () => {
      spyOn(component.columnVisibilityChanged, 'emit');
      component.gridColumns = [
        { name: 'A', hide: false },
        { name: 'B', hide: true }
      ];
      component.emitColumnVisibility();
      expect(component.columnVisibilityChanged.emit).toHaveBeenCalledWith([{ name: 'A', hide: false }]);
    });
  });

  describe('Debounce textbox changes', () => {
    it('should debounce textbox changes', fakeAsync(() => {
      spyOn(component as any, 'updateCellValue');
      // ngOnInit must be called to set up debounce subscription
      component.ngOnInit();
      const mockDataItem = { PCID: 1, FundID: 2, testColumn: '' };
      const mockColumn = { id: 3, name: 'testColumn', dataType: 1 };
      component.onTextboxValueChange('value1', mockDataItem, mockColumn);
      component.onTextboxValueChange('value2', mockDataItem, mockColumn);
      tick(500); // match debounceTime in component (500ms)
      expect((component as any).updateCellValue).toHaveBeenCalledWith(mockDataItem, mockColumn, 'value2');
    }));
  });

  describe('getSelectedDropDownValue', () => {
    it('should return correct dropdown value', () => {
      const col = { dropDownValues: [{ value: 1 }, { value: 2 }] };
      expect(component.getSelectedDropDownValue(col, 2)).toEqual({ value: 2 });
    });
    it('should return null if not found', () => {
      const col = { dropDownValues: [{ value: 1 }] };
      expect(component.getSelectedDropDownValue(col, 3)).toBeNull();
    });
  });

  describe('getDropDownValuesData', () => {
    it('should return displayText for type !== 1', () => {
      const dropDownValues = [{ value: 1, type: 2, displayText: 'Text' }];
      expect(component.getDropDownValuesData(1, dropDownValues)).toBe('Text');
    });
    it('should return svgIcon and displayText for type === 1', () => {
      const dropDownValues = [{ value: 2, type: 1, displayText: 'Icon', svgIcon: 'icon' }];
      expect(component.getDropDownValuesData(2, dropDownValues)).toEqual({ svgIcon: 'icon', displayText: 'Icon' });
    });
    it('should handle grouped dropdown values', () => {
      const dropDownValues = [{ items: [{ value: 3, type: 2, displayText: 'Grouped' }] }];
      expect(component.getDropDownValuesData(3, dropDownValues)).toBe('Grouped');
    });
  });

  describe('ngOnDestroy', () => {
    it('should complete destroy subject', () => {
      const spy = spyOn((component as any).destroy$, 'complete');
      component.ngOnDestroy();
      expect(spy).toHaveBeenCalled();
    });
  });

  describe('Column Selection and Delete Functionality', () => {
    beforeEach(() => {
      component.gridColumns = [
        { id: 1, name: 'SerialNo', selected: false },
        { id: 2, name: 'FundName', selected: false },
        { id: 3, name: 'PortfolioCompany', selected: false }
      ];
    });

    describe('onColumnSelectionClick', () => {
      it('should toggle column selection state', () => {
        // Initially not selected
        expect(component.gridColumns[0].selected).toBeFalse();
        
        // Select column
        component.onColumnSelectionClick(0);
        expect(component.gridColumns[0].selected).toBeTrue();
        expect(component.selectedDocumentCount).toBe(1);
        
        // Deselect column
        component.onColumnSelectionClick(0);
        expect(component.gridColumns[0].selected).toBeFalse();
        expect(component.selectedDocumentCount).toBe(0);
      });

      it('should update selectedDocumentCount correctly', () => {
        // Select first column
        component.onColumnSelectionClick(0);
        expect(component.selectedDocumentCount).toBe(1);
        
        // Select second column
        component.onColumnSelectionClick(1);
        expect(component.selectedDocumentCount).toBe(2);
        
        // Deselect first column
        component.onColumnSelectionClick(0);
        expect(component.selectedDocumentCount).toBe(1);
      });

      it('should show selection popup when columns are selected', () => {
        expect(component.showSelectionPopup).toBeFalse();
        
        component.onColumnSelectionClick(0);
        expect(component.showSelectionPopup).toBeTrue();
        
        component.onColumnSelectionClick(0); // Deselect
        expect(component.showSelectionPopup).toBeFalse();
      });

      it('should enable delete button when columns are selected', () => {
        expect(component.isDeleteDisabled).toBeFalse();
        
        component.onColumnSelectionClick(0);
        expect(component.isDeleteDisabled).toBeFalse();
      });

      it('should handle multiple column selections', () => {
        // Select multiple columns
        component.onColumnSelectionClick(0);
        component.onColumnSelectionClick(1);
        component.onColumnSelectionClick(2);
        
        expect(component.selectedDocumentCount).toBe(3);
        expect(component.gridColumns[0].selected).toBeTrue();
        expect(component.gridColumns[1].selected).toBeTrue();
        expect(component.gridColumns[2].selected).toBeTrue();
        expect(component.showSelectionPopup).toBeTrue();
      });
    });

    describe('closeSelectionPopup', () => {
      it('should hide selection popup and clear selections', () => {
        // Setup: select some columns
        component.onColumnSelectionClick(0);
        component.onColumnSelectionClick(1);
        expect(component.showSelectionPopup).toBeTrue();
        expect(component.selectedDocumentCount).toBe(2);
        
        // Close popup
        component.closeSelectionPopup();
        
        expect(component.showSelectionPopup).toBeFalse();
        expect(component.selectedDocumentCount).toBe(0);
        expect(component.gridColumns[0].selected).toBeFalse();
        expect(component.gridColumns[1].selected).toBeFalse();
      });

      it('should reset all column selections to false', () => {
        // Setup: select all columns
        component.gridColumns.forEach((_, index) => component.onColumnSelectionClick(index));
        expect(component.selectedDocumentCount).toBe(3);
        
        // Close popup
        component.closeSelectionPopup();
        
        component.gridColumns.forEach(column => {
          expect(column.selected).toBeFalse();
        });
      });
    });

    describe('handleDeleteSelectedClick', () => {
      it('should show delete popup and hide selection popup', () => {
        // Setup: select a column
        component.onColumnSelectionClick(0);
        expect(component.showSelectionPopup).toBeTrue();
        expect(component.showDeletePopup).toBeFalse();
        
        // Handle delete click
        component.handleDeleteSelectedClick();
        
        expect(component.showDeletePopup).toBeTrue();
        expect(component.showSelectionPopup).toBeFalse();
      });

      it('should work when no columns are selected', () => {
        expect(component.showSelectionPopup).toBeFalse();
        expect(component.showDeletePopup).toBeFalse();
        
        component.handleDeleteSelectedClick();
        
        expect(component.showDeletePopup).toBeTrue();
        expect(component.showSelectionPopup).toBeFalse();
      });
    });

    describe('cancelDelete', () => {
      it('should hide delete popup and restore selection popup', () => {
        // Setup: select columns and show delete popup
        component.onColumnSelectionClick(0);
        component.onColumnSelectionClick(1);
        component.handleDeleteSelectedClick();
        expect(component.showDeletePopup).toBeTrue();
        expect(component.selectedDocumentCount).toBe(2);
        
        // Cancel delete
        component.cancelDelete();
        
        expect(component.showDeletePopup).toBeFalse();
        expect(component.showSelectionPopup).toBeTrue();
        expect(component.selectedDocumentCount).toBe(2);
        expect(component.gridColumns[0].selected).toBeTrue();
        expect(component.gridColumns[1].selected).toBeTrue();
      });

      it('should work when no columns are selected', () => {
        component.showDeletePopup = true;
        expect(component.showDeletePopup).toBeTrue();
        
        component.cancelDelete();
        
        expect(component.showDeletePopup).toBeFalse();
        expect(component.showSelectionPopup).toBeFalse();
      });
    });

    describe('deleteDashboardTrackerColumns', () => {
      beforeEach(() => {
        // Mock the service method
        spyOn(dashboardTrackerService, 'deleteDashboardTrackerConfigs').and.returnValue(
          of({ success: true })
        );
        spyOn(component, 'loadDashboardTableData');
        spyOn(toastrService, 'success');
        spyOn(toastrService, 'error');
        spyOn(console, 'error');
      });

      it('should delete selected columns successfully', fakeAsync(() => {
        // Setup: select columns
        component.onColumnSelectionClick(0);
        component.onColumnSelectionClick(1);
        component.handleDeleteSelectedClick();
        
        // Delete columns
        component.deleteDashboardTrackerColumns();
        tick();
        
        expect(component.showDeletePopup).toBeFalse();
        expect(dashboardTrackerService.deleteDashboardTrackerConfigs).toHaveBeenCalledWith([
          {
            ID: component.gridColumns[0].id,
            FieldType: component.gridColumns[0].fieldType,
            DataType: component.gridColumns[0].dataType,
            Name: component.gridColumns[0].name,
            IsTimeSeries: component.gridColumns[0].isTimeSeries,
            TimeSeriesID: component.gridColumns[0].timeSeriesID
          },
          {
            ID: component.gridColumns[1].id,
            FieldType: component.gridColumns[1].fieldType,
            DataType: component.gridColumns[1].dataType,
            Name: component.gridColumns[1].name,
            IsTimeSeries: component.gridColumns[1].isTimeSeries,
            TimeSeriesID: component.gridColumns[1].timeSeriesID
          }
        ]);
        expect(component.isLoading).toBeTrue();
        expect(toastrService.success).toHaveBeenCalledWith(
          'Columns deleted successfully.',
          '',
          { positionClass: 'toast-center-center' }
        );
        expect(component.loadDashboardTableData).toHaveBeenCalledWith(component.state);
      }));

      it('should handle delete failure', fakeAsync(() => {
        // Setup: select a column
        component.onColumnSelectionClick(0);
        component.handleDeleteSelectedClick();
        
        // Mock service failure
        dashboardTrackerService.deleteDashboardTrackerConfigs = jasmine.createSpy().and.returnValue(
          of({ success: false })
        );
        
        // Delete columns
        component.deleteDashboardTrackerColumns();
        tick();
        
        expect(component.showDeletePopup).toBeFalse();
        expect(toastrService.error).toHaveBeenCalledWith(
          'Failed to delete columns.',
          '',
          { positionClass: 'toast-center-center' }
        );
        expect(component.loadDashboardTableData).not.toHaveBeenCalled();
      }));

      it('should handle service error', fakeAsync(() => {
        // Setup: select a column
        component.onColumnSelectionClick(0);
        component.handleDeleteSelectedClick();
        
        // Mock service error
        dashboardTrackerService.deleteDashboardTrackerConfigs = jasmine.createSpy().and.returnValue(
          throwError(() => new Error('Service error'))
        );
        
        // Delete columns
        component.deleteDashboardTrackerColumns();
        tick();
        
        expect(component.showDeletePopup).toBeFalse();
        expect(toastrService.error).toHaveBeenCalledWith(
          'Failed to delete columns.',
          '',
          { positionClass: 'toast-center-center' }
        );
        expect(console.error).toHaveBeenCalledWith('Error deleting columns:', jasmine.any(Error));
        expect(component.isLoading).toBeFalse();
        expect(component.loadDashboardTableData).not.toHaveBeenCalled();
      }));

      it('should not delete when no columns are selected', () => {
        component.showDeletePopup = true;
        
        component.deleteDashboardTrackerColumns();
        
        expect(component.showDeletePopup).toBeFalse();
        expect(dashboardTrackerService.deleteDashboardTrackerConfigs).not.toHaveBeenCalled();
        expect(component.isLoading).toBeFalse();
      });

      it('should handle columns without optional properties', () => {
        // Setup: columns without optional properties
        component.gridColumns = [
          { id: 1, name: 'TestColumn', selected: false }
        ];
        component.onColumnSelectionClick(0);
        component.handleDeleteSelectedClick();
        
        // Delete columns
        component.deleteDashboardTrackerColumns();
        
        expect(dashboardTrackerService.deleteDashboardTrackerConfigs).toHaveBeenCalledWith([
          {
            ID: 1,
            FieldType: undefined,
            DataType: undefined,
            Name: 'TestColumn',
            IsTimeSeries: undefined,
            TimeSeriesID: undefined
          }
        ]);
      });
    });

    describe('Selection Popup State Management', () => {
      it('should show selection popup only when columns are selected', () => {
        expect(component.showSelectionPopup).toBeFalse();
        
        // Select a column
        component.onColumnSelectionClick(0);
        expect(component.showSelectionPopup).toBeTrue();
        
        // Deselect all columns
        component.closeSelectionPopup();
        expect(component.showSelectionPopup).toBeFalse();
      });

      it('should maintain selection state across popup operations', () => {
        // Select columns
        component.onColumnSelectionClick(0);
        component.onColumnSelectionClick(1);
        expect(component.selectedDocumentCount).toBe(2);
        
        // Show delete popup
        component.handleDeleteSelectedClick();
        expect(component.showDeletePopup).toBeTrue();
        expect(component.showSelectionPopup).toBeFalse();
        
        // Cancel delete should restore selection popup
        component.cancelDelete();
        expect(component.showDeletePopup).toBeFalse();
        expect(component.showSelectionPopup).toBeTrue();
        expect(component.selectedDocumentCount).toBe(2);
      });
    });

    describe('Edge Cases and Error Handling', () => {
      it('should handle invalid column index gracefully', () => {
        expect(() => component.onColumnSelectionClick(-1)).not.toThrow();
        expect(() => component.onColumnSelectionClick(999)).not.toThrow();
      });

      it('should handle empty gridColumns array', () => {
        component.gridColumns = [];
        
        expect(() => component.onColumnSelectionClick(0)).not.toThrow();
        expect(component.selectedDocumentCount).toBe(0);
        expect(component.showSelectionPopup).toBeFalse();
      });

      it('should handle columns with missing properties', () => {
        component.gridColumns = [
          { name: 'TestColumn', selected: false } // Missing id
        ];
        
        expect(() => component.onColumnSelectionClick(0)).not.toThrow();
        expect(component.gridColumns[0].selected).toBeTrue();
      });
    });
  });
});
