<div class="row client-reporting-section" id="client-reporting-report">
    <div class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
        <div class="row mr-0 ml-0 fixed-top-section">
            <div class="col-10 col-xs-10 col-sm-10 col-lg-10 col-xl-10 col-md-10 pr-0 pl-0">
                <div class="d-inline-block select-pr">
                    <div class="d-inline-block label-padding-css Caption-M label-color" >Company Name</div>
                    <div>
                        <kendo-combobox id="clientReportingCompanyName" [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="selectedCompany"
                            #financialYearEnd="ngModel" [fillMode]="'solid'" [filterable]="true" name="company" [virtual]="virtual"
                            class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="companyList"
                            [filterable]="true" textField="companyName" valueField="portfolioCompanyID" (valueChange)="companyChangeEvent()"
                            placeholder="Select Company">
                            <ng-template kendoComboBoxItemTemplate let-company>
                                <span title="{{company.companyName}}" class="TextTruncate">{{company.companyName}}</span>
                            </ng-template>                    
                        </kendo-combobox>

                    </div>
                </div>
                <div class="d-inline-block pl3 pr3 select-pr">
                    <div class="d-inline-block label-padding-css Caption-M label-color" >Period Type</div>
                    <div>
                    <kendo-combobox id="clientReportingPeriodType" [clearButton]="false" [(ngModel)]="selectedPeriodType" #periodType="ngModel" [fillMode]="'solid'"
                        name="period" [virtual]="virtual" class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32"
                        [size]="'medium'" [data]="periodTypeList" [filterable]="true" textField="label" placeholder="Select Period"
                        (valueChange)="changePeriod($event)" valueField="value">
                    </kendo-combobox>
                    </div>
                </div>
                <div class="d-inline-block pr3 select-pr client-p-calendar" class="{{sideNavBaseClass}}">
                    <div *ngIf="selectedPeriodType?.label=='Monthly'" class="d-inline-block label-padding-css">From Month</div>
                    <div *ngIf="selectedPeriodType?.label=='Monthly'">
                        <kendo-datepicker id="clientReportingFromMonth" bottomView="year" topView="decade" calendarType="classic" *ngIf="selectedPeriodType.label=='Monthly'"
                            class="k-picker-custom-solid k-datepicker-height-32 k-datepicker-width-240" [format]="format" [fillMode]="'solid'"
                            placeholder="Select Month" id="fromMonth" name="fromMonth" [(ngModel)]="selectedMonth"
                            [value]="getFormattedDate(selectedMonth)" (valueChange)="onSelectMonth($event)"></kendo-datepicker>
                    </div>
                    <div *ngIf="selectedPeriodType?.label=='Quarterly'" class="d-inline-block label-padding-css">Quarterly</div>
                    <div *ngIf="selectedPeriodType?.label=='Quarterly'">
                        <quarter-year-control id="clientReportingQuarter" *ngIf="selectedPeriodType.label=='Quarterly'" [ControlName]="'quarterYearValue'"
                            [QuarterYear]="selectedQuarter" (onCalendarYearPicked)="fromQuarterYear($event)">
                        </quarter-year-control>
                    </div>
                    <div *ngIf="selectedPeriodType?.label=='Yearly'" class="d-inline-block label-padding-css">Yearly</div>
                    <div *ngIf="selectedPeriodType?.label=='Yearly'">
                        <kendo-combobox id="clientReportingYear" *ngIf="selectedPeriodType.label=='Yearly'" [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                            [(ngModel)]="selectedYear" #yearly="ngModel" [fillMode]="'solid'" [filterable]="true" name="yearly"
                            [virtual]="virtual" class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                            [data]="yearOptions"  [filterable]="true" textField="text" placeholder="Select year"
                            (valueChange)="yearOptionsChange($event)" valueField="value">
                        </kendo-combobox>
                    </div>
                </div>
                <div class="d-inline-block select-pr client-p-calendar" class="{{sideNavBaseClass}}">
                    <div *ngIf="selectedPeriodType?.label=='Monthly'"  class="d-inline-block label-padding-css" >To Month</div>
                    <div *ngIf="selectedPeriodType?.label=='Monthly'" >
                    <kendo-datepicker id="clientReportingToMonth" bottomView="year" topView="decade" calendarType="classic" *ngIf="selectedPeriodType.label=='Monthly'"
                        class="k-picker-custom-solid k-datepicker-height-32 k-datepicker-width-240" [format]="format" [fillMode]="'solid'"
                        placeholder="Select Month" id="toMonth" name="toMonth" [(ngModel)]="selectedToMonth"
                        [value]="getFormattedDate(selectedToMonth)" (valueChange)="onSelectToMonth($event)"></kendo-datepicker>
                    </div>
                </div>
            </div>
            <div class="col-2 col-xs-2 col-sm-2 col-lg-2 col-xl-2 col-md-2 pr-0 pl-0">
                <div   class="d-inline-block label-padding-css">&nbsp;</div>
                <div>
                    <div class="float-right download-Selector" class="{{sideNavBaseClass}}">
                        <div class="fund-splitButton ">
                            <a class="loading-input-controls2">
                            </a>
                            <div class="download-investor-excel">
                                <span id="clientReportingFullReport" (click)="exportFullReport()">
                                    <img alt="" src="assets/dist/images/FileDownloadWhite.svg"
                                        class="showHandIcon pr-2 mt-0 TextTruncate" title="Performance Report">Reports
                                </span>
                                <span *ngIf="isFullExportLoading"> <i *ngIf="isFullExportLoading" aria-hidden="true"
                                        class="fa fa-spinner fa-pulse fa-1x fa-fw "></i></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <client-report-tabs *ngIf="adhocDataScroll.length>0 && selectedPeriodType?.label=='Monthly'" [defaultSelectedTab]="defaultSelectedTab" id="client-report-tabs"
            [selectedPeriodType]="selectedPeriodType" [adhocDataScroll]="adhocDataScroll" (onSelectedActiveTab)="onClickActiveTab($event)"></client-report-tabs>
        <div class="row portfolio-company-list-section">
            <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pr-0 pl-0">
                <div class="card-body portafolio-table client-card-margin">
                    <div class="row performance-section mr-0 ml-0">
                        <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 outer-section pl-0 pr-0">
                            <div class="panel panel-default "
                                [ngClass]="tabList.length > 0 ?'border-top-0 tab-bg':'bg-transparent'">
                                <div class="panel-heading pl-0 pr-0" [ngClass]="tabList.length==0 ?'pb-0':''">
                                    <div class="panel-title custom-tabs pc-tabs  custom-client-tab" *ngIf="tabList.length>0">
                                        <nav mat-tab-nav-bar class="pt-2" [ngClass]="tabList.length==0 ?'filter-bg':''">
                                            <a id="{{tab.name}}" mat-tab-link [disableRipple]="true" *ngFor="let tab of tabList;"
                                                (click)="selectTab(tab)"  [active]="tab.active" class="TextTruncate tab-name-max-width client-report-tab-pedno">
                                               <span  class="TextTruncate" title="{{tab.name}}"> {{tab.name}}</span></a>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                            <div class="content-bg">
                                <div class="row  mr-0 ml-0 filter-bg border-bottom">
                                    <div
                                        class="col-lg-12 col-md-12 col-sm-12 col-xl-12 col-12 col-xs-12 pl-0 pr-0 content-mr">
                                        <div class="float-right">
                                            <div class="d-inline-block search">
                                                <span class="fa fa-search fasearchicon p-1"></span>
                                                <input id="clientReportingSearch" #globalFilterId pInputText type="text" (keyup)="adhocSearch()"
                                                    class="search-text-company TextTruncate companyListSearchHeight"
                                                    [(ngModel)]="globalFilter" placeholder="Search">
                                            </div>
                                            <div class="d-inline-block">
                                                <i aria-hidden="true" *ngIf="isExportLoading"
                                                    class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                                <img id="clientReportingExportReport" class="showHandIcon" title="Export Client Report (Excel file)"
                                                    (click)="exportReport()" src="assets/dist/images/Cloud-download.svg"
                                                    alt="" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="datatable-container">
                                    <kendo-grid id="clientReportingTable" [data]="reportingList" [resizable]="true"
                                        class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cab-table-grid">
                                        <!-- Frozen columns -->
                                        <kendo-grid-column *ngFor="let col of frozenTableColumns" [locked]="reportingList.length>0?col.isFrozenColumn:false" [width]="customWidth"
                                            title="{{col.header}}" [ngClass]="{'k-grid-border-right-width': !col.isFrozenColumn}">
                                            <ng-template kendoGridHeaderTemplate>
                                                <span class="TextTruncate S-M">{{col.header}}</span>
                                            </ng-template>
                                            <ng-template kendoGridCellTemplate let-rowData>
                                                <span  [ngClass]="col.isFrozenColumn ? 'row-white-space' : ''" title="{{rowData[col.field] | safeHtml}}"
                                                    [innerHTML]="rowData[col.field]"></span>
                                            </ng-template>
                                        </kendo-grid-column>
                                        <!-- Non-frozen columns -->
                                        <kendo-grid-column *ngFor="let col of colHeaders" [width]="200" title="{{col.header}}">
                                            <ng-template kendoGridHeaderTemplate>
                                                <span class="TextTruncate S-M">{{col.header}}</span>
                                            </ng-template>
                                            <ng-template kendoGridCellTemplate let-rowData>
                                                <span class="TextTruncate" title="{{rowData[col.field] | safeHtml}}" [innerHtml]='rowData[col.field]'></span>
                                            </ng-template>
                                        </kendo-grid-column>
                                        <ng-template kendoGridNoRecordsTemplate>
                                            <app-empty-state class="finacials-beta-empty-state" [isGraphImage]="false"></app-empty-state>
                                        </ng-template>
                                    </kendo-grid>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <app-loader-component *ngIf="isLoader"></app-loader-component>
</div>
