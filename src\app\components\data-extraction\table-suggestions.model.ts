export interface TableBoundingBox {
    x1: number;
    y1: number;
    x2: number;
    y2: number;
}

export interface TableSuggestion {
    bbox: TableBoundingBox;
    page: number;
    file_id: string;
    score: number;
}

export interface PageDetails {
    page: number;
    score: number;
}

export interface FilePageDetails {
    file_id: string;
    pages: PageDetails[];
}

export interface TablePageDetails {
    label: string;
    files: FilePageDetails[];
}

export interface TableType {
    label: string;
    suggestions: TableSuggestion[];
}

export interface TableSuggestionResponse {
    isError: boolean;
    status: string;
    job_id: string;
    timestamp: string;
    message: string;
    files: Array<{
        file_name: string;
        s3_path: string;
        source: string;
        file_id: string;
    }>;
    tables: TableType[];
}