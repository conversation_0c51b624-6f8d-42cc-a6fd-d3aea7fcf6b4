import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { constants } from 'buffer';
import { ToastrService } from 'ngx-toastr';
import { CommonPCConstants, GlobalConstants, OperationalKPIConstants } from 'src/app/common/constants';
import { AppConfig } from 'src/app/common/models';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';

@Component({
  selector: 'app-static-info-modification-message',
  templateUrl: './static-info-modification-message.component.html',
  styleUrls: ['./static-info-modification-message.component.scss']
})
export class StaticInfoModificationComponent {
	@Input() staticInfoSaveWarningMessage : string = GlobalConstants.StaticInfoSaveWarningMessage;
  @Input() staticInfoEnableWorkflow : string = GlobalConstants.StaticInfoEnableWorkflow;
  isWorkflowEnable : boolean;
  isPageConfig:boolean = false;
  @Input() portfolioCompanySelected: boolean;
  isTestHost: boolean = false;
  constructor(
     private portfolioCompanyService: PortfolioCompanyService,private toastService: ToastrService,private router: Router) {
       if (window.location.host == CommonPCConstants.TestHost || window.location.host == CommonPCConstants.UATHost || window.location.host == CommonPCConstants.HimeraHost || window.location.host==CommonPCConstants.FeatureHost) {
            this.isTestHost = true;
            if(OperationalKPIConstants.PageConfigRoute=== this.router.url) {
              this.isPageConfig = true;
          }       
        }
  }
  ngOnInit() {
    this.portfolioCompanySelected = true;
      this.portfolioCompanyService.getWorkFlowPageConfiguration().subscribe((res) => {    
      this.isWorkflowEnable = res;     
      });
  }

  onCommentaryWorkflowToggleChange(isWorkFlowEnable:boolean) { 
    this.portfolioCompanyService.updateWorkFlowPageConfiguration(isWorkFlowEnable).subscribe((result) => { 
    if(result)   
    {
       let message = isWorkFlowEnable ? "enabled" : "disabled";
       this.toastService.success(
        ` Page Configuration Workflow ${message} successfully`,
        "",
        { positionClass: "toast-center-center" })
    } 
    else{
      this.toastService.error("Something went wrong", "", {
        positionClass: "toast-center-center",
      });
   
    }
    });
  }
}
