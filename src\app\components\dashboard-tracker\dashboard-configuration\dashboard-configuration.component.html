<div class="tab-container" id="dashboard-configuration-container">
<!-- Tab-Header -->
<div class="row dashboard-tab-bar m-0">
    <div class="col-12 px-2 py-2 d-flex align-items-center" id="home-container">
        <div>
            <nep-tab [tabList]="tabList" (OnSelectTab)="onTabClick($event)"></nep-tab>
        </div>
        <div class="ml-auto" *ngIf="selectedTab.name == DashboardConfigurationConstants.DashboardConfigurationTab">
            <app-kendo-button name="cancel" type="Secondary" (onClick)="onCancelClick()">Cancel</app-kendo-button>
            <app-kendo-button [passedClass]="'px-5 mx-3'" name="save" type="Primary" [disabled]="isSaveDisabled()" (onClick)="onSaveClick()">Save</app-kendo-button>
        </div>
        <div class="ml-auto"  *ngIf="selectedTab.name === DashboardConfigurationConstants.DeletedColumnTab">
            <app-kendo-button [passedClass]="'px-5 mx-3'" name="restore" type="Primary">Restore</app-kendo-button>
        </div>
    </div>
</div>
<!-- Tab-Body -->
<ng-container *ngIf="selectedTab.name == DashboardConfigurationConstants.DashboardConfigurationTab">
    <div class="dashboard-configuration-body">
        <div class="dashboard-table-section">
            <app-dashboard-tracker #dashboardTracker [passedClass]="'configuration-dashboard'" [isDashboardConfigurationTab]="isDashboardConfigurationTab" (cellChangesUpdated)="onCellChangesUpdated($event)"></app-dashboard-tracker>
        </div>        
        <div class="add-column-section">
            <div class="add-column-header">
                <app-kendo-button (click)="navigateToDashboardConfig()" name="dashboard-tracker-setting" type="Secondary"
                icon="plus" aria-label="Add dashboard configuration">
                </app-kendo-button>
            </div>
        </div>
    </div>
</ng-container>
<ng-container *ngIf="selectedTab.name == DashboardConfigurationConstants.ManageTrackerFieldsTab">
    <app-manage-tracker-fields></app-manage-tracker-fields>
</ng-container>
<ng-container *ngIf="selectedTab.name == DashboardConfigurationConstants.DeletedColumnTab">
   <app-manage-deleted-columns></app-manage-deleted-columns>
</ng-container>
<ng-container *ngIf="selectedTab.name == DashboardConfigurationConstants.StatusFilterTab">
    StatusFilterTab
</ng-container>
</div>

