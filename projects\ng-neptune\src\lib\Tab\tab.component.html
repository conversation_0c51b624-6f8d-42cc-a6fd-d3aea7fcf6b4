<div class="nep-tabs nep-tabs-line" id="tab-container">
    <div class="nep-tabs-header" id="tabs-header">
        <div class="nep-tabs-header-tabs" id="tabs-header-tabs">
            <div class="nep-tabs-inner" id="tabs-inner">
                <div class="nep-tabs-scroll" [ngClass]="nav==true?'nep-tabs-custom nep-tab-alignment2':'nep-tab-alignment'" id="tab-container">
                    <ng-container *ngFor="let tab of tabList">
                        <div 
                            *ngIf="!tab.hidden" 
                            id="{{tab.name}}" 
                            class="nep-tabs-tab pl-0 pr-0" 
                            title="{{tab.name}}" 
                            (click)="selectTab(tab)" 
                            [class.nep-tabs-active]="tab.active"
                            style="max-width: 100% !important;"
                        >
                            {{tab.name}}
                        </div>
                    </ng-container>
                    <div class="nep-tabs-tab" *ngIf="isfilter" style="float: right">
                        <img id="collapsed" *ngIf="collapsed&&tabName !='Trading Records'" (click)="expand()" [src]="'assets/dist/images/KPI_Filter.svg'" alt="KPI Filter's">
                        <img id="not-collapsed" *ngIf="!collapsed&&tabName !='Trading Records'" (click)="collapse()" [src]="'assets/dist/images/KPI_Filter.svg'" alt="KPI Filter's">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="nep-tabs-panel nep-tabs-show" style="background:#fafafa">
        <ng-content></ng-content>
    </div>
</div>