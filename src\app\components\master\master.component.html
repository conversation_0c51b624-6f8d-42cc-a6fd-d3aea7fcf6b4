<!-- <div class="wrapper">
    <mat-sidenav-container class="mat-side-bar" fullscreen [autosize]="true">
        <mat-sidenav id="mat-sidenav-id" #sidenav [style.width.em]="sidenavWidth" [style]="isClientUser ? 'background: linear-gradient(180deg, #021155, #39195e 90%)!important;' :'background: linear-gradient(180deg, #021155 40%, #00BFA5 136%);'" mode="side" class="example-sidenav mat-custom-sidenav" opened="true"  [disableClose]="true">
            <div class="logomain"><a href="#/"><span [ngClass]="!displayOpenOption ?'d-block':'d-none'"><img alt="brand-logo" calss="validate-class"
                src="assets/dist/images/acuity-logo-s-collapse.svg"
                theme="light" class="side-nav-logo" type="default"></span>
                <span [ngClass]="displayOpenOption ?'d-block':'d-none'"><img alt="brand-logo"
                    src="assets/dist/images/acuity-logo-s.svg" theme="light" class="side-nav-logo"
                    type="default"></span>
                </a>
            </div>
       <mat-nav-list class="mat-nav-custom">
          <ng-container *ngFor="let menu of featureList">
              <ng-container [ngTemplateOutlet]="menu.children.length ?  childMenu : parentMenu"
             [ngTemplateOutletContext]="{menu:menu}">
            </ng-container>
          </ng-container>
          <ng-template #parentMenu let-menu="menu">
            <mat-list-item tooltipPosition="right" [pTooltip]="displayOpenOption ? '' : menu.aliasName==null?menu.feature:menu.aliasName" id="{{menu.aliasName==null?menu.feature:menu.aliasName}}"
            tooltipStyleClass="bg-tooltip-color" [routerLink]="menu.path" class="mat-nav-custom-item" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact:
             true}" (click)="resetParntActive('reset')" title="{{displayOpenOption ? menu.aliasName==null?menu.feature:menu.aliasName : ''}}">
             <mat-icon class="mat-icon-custom" mat-list-icon [ngStyle]="{'color':'white'}"><img class="mat-icon-img mat-mast-c" src="assets/dist/images/{{featureImage[menu.feature=='Portfolio Company'?'PortfolioCompany':menu.feature]}}" alt="pin"
                    ></mat-icon>
                    <div fxFlex="10"></div>
                    <div class="sidenav-item" [ngStyle]="{'display': displayOpenOption ? 'block' : 'none' }">
                        <div class="side-bar-font">{{menu.aliasName==null?menu.feature:menu.aliasName}}</div>
                    </div>
            </mat-list-item>
          </ng-template>
          <ng-template #childMenu let-menu="menu">
                <mat-expansion-panel [expanded]="menu.isTabExpand" [class.mat-elevation-z0]="true" class="custom-expansion-panel" hideToggle = "{{!displayOpenOption}}">
                    <mat-expansion-panel-header tooltipPosition="right" [pTooltip]="displayOpenOption ? '' : menu.aliasName==null?menu.feature:menu.aliasName" id="{{menu.aliasName==null?menu.feature:menu.aliasName}}"
                    tooltipStyleClass="bg-tooltip-color" [ngStyle]="{'background-color': (menu.isActiveParent && !displayOpenOption) ? 'rgba(255, 255, 255, 0.2)' : '' }" (click)="displayOpenOption = true; resizeSideNav('incress'); activateSubMenu(menu)" title="{{displayOpenOption ? menu.aliasName==null?menu.feature:menu.aliasName : ''}}">
                        <mat-icon class="mat-icon-custom" mat-list-icon [ngStyle]="{'color':'white'}" [ngStyle]="{'display': displayOpenOption ? 'inline-block' : 'flex' }"><img class="mat-icon-img mat-mast-c" src="assets/dist/images/{{featureImage[menu.feature=='Portfolio Company'?'PortfolioCompany':menu.feature]}}" alt="pin"
                    ></mat-icon>
                        <div fxFlex="10"></div>
                        <div class="sidenav-item" [ngClass]="displayOpenOption ? 'vertical-alignment-center' : '' " [ngStyle]="{'display': displayOpenOption ? 'block' : 'none' }">
                        <div class="side-bar-font expanded-menu" >{{menu.aliasName==null?menu.feature:menu.aliasName}}</div>
                        </div>
                        <div class="triangle" [ngStyle]="{'display': displayOpenOption ? 'none' : 'block' }"></div>
                    </mat-expansion-panel-header>
                
                    <mat-nav-list class="custom-nav-child" [ngStyle]="{'display': displayOpenOption ? 'block' : 'none' }" [autosize]="true">
                        <mat-list-item id="{{submenu.aliasName==null?submenu.feature:submenu.aliasName}}" [routerLink]="submenu.path" tooltipPosition="right" [pTooltip]="displayOpenOption ? '' : submenu.aliasName==null?submenu.feature:submenu.aliasName"
                        tooltipStyleClass="bg-tooltip-color" *ngFor="let submenu of menu.children" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact:
                        true}" (click)="parentClickStatus(menu)" class="child-mat-list-item" title="{{displayOpenOption ? submenu.aliasName==null?submenu.feature:submenu.aliasName : ''}}">
                            <div fxFlex="10"></div>
                            <div class="mat-child-nav-item" [ngStyle]="{'display': displayOpenOption ? 'block' : 'none' }">
                                <div class="side-bar-font mat-child-nav-font">{{submenu.aliasName==null?submenu.feature:submenu.aliasName}}</div>
                            </div>
                        </mat-list-item>
                    </mat-nav-list>
                  </mat-expansion-panel>


            </ng-template>
       </mat-nav-list>
       <div class="mster-pf-b">
          <mat-divider></mat-divider>
          <div class="sidenav-arrow side-mstr-p" [style.width.em]="sidenavWidth" [ngStyle]="{ 'float': displayOpenOption ? 'right' : 'none', 'text-align': displayOpenOption ? 'right' : 'center'}">
             <i class="fa fa-arrow-circle-left fa-custom" aria-hidden="true" *ngIf="displayOpenOption" (click)="resizeSideNav('decrease')"></i>
             <i class="fa fa-arrow-circle-right fa-custom" aria-hidden="true" *ngIf="!displayOpenOption" (click)="resizeSideNav('incress')"></i>
          </div>
       </div>
       </mat-sidenav>
       <mat-sidenav-content  [style.margin-left.em]="sidenavWidth" class="example-container">
                <div class="content-page">
                    <app-header [userId]='userId' [userName]='userName'></app-header>
                    <div class="content" >
                        <div class="nep-panel">
                            <router-outlet name="master"></router-outlet>
                        </div>
                        <ngx-spinner bdColor="rgba(51,51,51,0.8)" size="medium" color="#fff" loadingText="Please wait..." type="ball-scale-multiple"> </ngx-spinner>
                    </div>
                </div>
        </mat-sidenav-content>
    </mat-sidenav-container>
 </div> -->
 <mat-toolbar class="nav-bar-top fixed-menu" color="primary">
    <div class="float-left">
        <div class="d-inline-block">
            <span class="logo"><img src="assets/dist/images/logo-foliosure.svg" alt="logo"/></span>
        </div>
        <span class="spacer"></span>
        <div class="nav-links d-inline-block" #navContainer>
            <ng-container *ngFor="let menu of featureUpdatedList">
                <button mat-button [matMenuTriggerFor]="menuTrigger" [routerLink]="menu.path"   routerLinkActive="is-active" class="mr-2">
                    {{menu.aliasName == null ? menu.feature : menu.aliasName}}
                    <img src="assets/dist/images/i-arrow-icon.svg" alt="arrow-icon" class="pl-1" *ngIf="menu.children.length" />
                </button>
                <mat-menu #menuTrigger="matMenu" class="custom-menu-dropdown">
                    <ng-container *ngFor="let submenu of menu.children">
                        <button mat-menu-item [routerLink]="submenu.path" routerLinkActive="is-active">
                            {{submenu.aliasName == null ? submenu.feature : submenu.aliasName}}
                        </button>
                    </ng-container>
                </mat-menu>
            </ng-container>
        </div>
    </div>
    <div class="float-right">
        <app-header [userId]='userId' [userName]='userName'></app-header>
    </div>
    <!-- Navigation Menu -->
</mat-toolbar>
<div class="row mr-0 ml-0" style="margin-top:60px">
    <div class="col-12 pl-0 pr-0">
        <app-breadcrumb name="breadcrumb"></app-breadcrumb>
    </div>
</div>
    <div class="master-content">
        <div class="master-body">
            <router-outlet name="master"></router-outlet>
            <div>
    <div>
   
<app-concurrency-session></app-concurrency-session>
  <app-loader-component *ngIf="isLoader"></app-loader-component>