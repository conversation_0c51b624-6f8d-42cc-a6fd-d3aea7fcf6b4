import { ComponentFixture, TestBed } from "@angular/core/testing";
import { ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { CompanyKpiGraphComponent } from "./company-kpi-graph.component";
import {
  DecimalDigitEnum,
  FinancialValueUnitsEnum,
  MiscellaneousService,
  OrderTypesEnum,
  PeriodTypeQuarterEnum,
} from "src/app/services/miscellaneous.service";
import { PortfolioCompanyService } from "src/app/services/portfolioCompany.service";
import { of } from "rxjs";
import { KpiInfo } from "src/app/common/constants";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { BrowserModule } from "@angular/platform-browser";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { DropDownsModule } from "@progress/kendo-angular-dropdowns";
import { GroupResult,groupBy } from '@progress/kendo-data-query';
const mockMiscService = () => ({});
let portfolioCompanyService: PortfolioCompanyService;
describe("CompanyKpiGraphComponent", () => {
  let component: CompanyKpiGraphComponent;
  let fixture: ComponentFixture<CompanyKpiGraphComponent>;
  let mockMiscService;
  let mockChangeDetectorRef;
  let mockRoute;
  beforeEach(async () => {
    mockRoute = {
      snapshot: {
        params: {
          id: '123'
        }
      }
    };
    mockChangeDetectorRef = jasmine.createSpyObj(['detectChanges']);
    mockMiscService = jasmine.createSpyObj(["GetCompanyOrInvestmentKPIList"]);
    await TestBed.configureTestingModule({
      declarations: [CompanyKpiGraphComponent],
      imports: [BrowserModule,BrowserAnimationsModule, FormsModule,ReactiveFormsModule,DropDownsModule ],
      providers: [
        { provide: MiscellaneousService, useValue: mockMiscService },
        {
          provide: PortfolioCompanyService,
          useValue: jasmine.createSpyObj("PortfolioCompanyService", [
            "getChartsKpiData",
          ]),
        },
        { provide: ActivatedRoute, useValue: mockRoute },
        { provide: ChangeDetectorRef, useValue: mockChangeDetectorRef }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CompanyKpiGraphComponent);
    component = fixture.componentInstance;
    component.companyKPIList = {
      KPIList: [
        { parentkpi: 'Sales', displayName: 'Total Sales' },
        { parentkpi: 'Marketing', displayName: 'Total Marketing' },
        { parentkpi: 'Sales', displayName: 'Sales Growth' }
      ]
    };

    component.modelList = {
      portfolioCompanyID: 1,
      moduleId: 2,
    };
    component.searchFilter = "example";
    component.typeField = "bar";
    portfolioCompanyService = TestBed.inject(PortfolioCompanyService);
    (portfolioCompanyService.getChartsKpiData as jasmine.Spy).and.returnValue(
      of({ data: [], yLineFields: [], yBarFields: [], xField: "", yShades: [] })
    );
    fixture.detectChanges();
  });
  it("should create", () => {
    expect(component).toBeTruthy();
  });

  it("should set the id property from the route parameter", () => {
    expect(component.id).toEqual("123");
  });

  it("should clear the chartData and other properties when clearAll is called", () => {
    component.chartData = [{ x: 1, y: 2 }];
    component.yLineFields = ["a", "b"];
    component.yBarFields = ["c", "d"];
    component.xField = ["x"];
    component.yShades = ["e", "f"];
    component.clearAll();
    expect(component.chartData).toEqual([]);
    expect(component.yLineFields).toEqual([]);
    expect(component.yBarFields).toEqual([]);
    expect(component.xField).toEqual(["x"]);
    expect(component.yShades).toEqual([]);
  });

  it("should update the width property when the window is resized", () => {
    const newWidth = 500;
    component.width = newWidth;
    component.onResized({ newRect: { width: newWidth } });
    expect(component.width).toEqual(newWidth);
  });
  it("should clear all chart data, yLineFields, yBarFields, and yShades", () => {
    component.chartData = [1, 2, 3];
    component.yLineFields = ["field1", "field2"];
    component.yBarFields = ["field3", "field4"];
    component.yShades = ["shade1", "shade2"];

    component.clearAll();

    expect(component.chartData).toEqual([]);
    expect(component.yLineFields).toEqual([]);
    expect(component.yBarFields).toEqual([]);
    expect(component.yShades).toEqual([]);
  });
  it("should set the symbol for the selected KPI in the setSymbol method", () => {
    let mockKpi1 = { kpiInfo: KpiInfo.Currency };
    let mockKpi2 = { kpiInfo: KpiInfo.Number };
    let mockKpi3 = { kpiInfo: KpiInfo.Percentage };
    let mockKpi4 = { kpiInfo: KpiInfo.Multiple };
    let mockModelList = { reportingCurrencyDetail: { currencyCode: "USD" } };
    component.modelList = mockModelList;
    component.setSymbol(mockKpi1);
    expect(component.companyKpiModuleCurrency).toEqual("USD");
    component.setSymbol(mockKpi2);
    expect(component.companyKpiModuleCurrency).toEqual(KpiInfo.Number);
    component.setSymbol(mockKpi3);
    expect(component.companyKpiModuleCurrency).toEqual(KpiInfo.Percentage);
    component.setSymbol(mockKpi4);
    expect(component.companyKpiModuleCurrency).toEqual(KpiInfo.Multiple);
  });
  it("should handle error when loading graph", () => {
    spyOn(component, "clearAll");

    component.LoadGraph();

    expect(component.clearAll).toHaveBeenCalled();
    expect(component.isLoaded).toBe(false);
  });
  it("should set modelCompanyKpi properties correctly on ngOnInit", () => {
    component.ngOnInit();

    expect(component.modelCompanyKpi.periodType).toEqual(
      PeriodTypeQuarterEnum.Last1Year
    );
    expect(component.modelCompanyKpi.orderType).toEqual({
      type: OrderTypesEnum.LatestOnRight,
    });
    expect(component.modelCompanyKpi.decimalPlaces).toEqual({
      type: DecimalDigitEnum.Zero,
      value: "1.0-0",
    });
  });

  it("should set companyKpiValueUnit properties correctly on ngOnInit", () => {
    component.ngOnInit();

    expect(component.companyKpiValueUnit).toEqual({
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    });
  });
  it("should get company KPIs", () => {
    const mockResponse = {
      body: [{ kpiid: 1 }, { kpiid: 2 }],
      code: "OK",
    };
    mockMiscService.GetCompanyOrInvestmentKPIList.and.returnValue(
      of(mockResponse)
    );

    spyOn(component, "setSymbol");
    spyOn(component, "LoadGraph");

    component.modelList = { portfolioCompanyID: 123 };
    component.getCompanyKPIs();

    expect(component.companyKPIList).toEqual(mockResponse.body);
    expect(component.selectedCompanyKPI).toEqual(mockResponse.body[0]);
    expect(component.setSymbol).toHaveBeenCalledWith(mockResponse.body[0]);
    expect(component.LoadGraph).toHaveBeenCalled();
  });
  it("should change company KPI", () => {
    spyOn(component, "setSymbol");
    spyOn(component, "LoadGraph");

    component.selectedCompanyKPI = { kpiid: 1 };
    var mockData = {category: null,displayName:"Auto_TD_CP Percentage",id:0,isDefault:false,isFavourite:false,
      isFavouriteOrder: null,isHeader: false,itemName:"Auto_TD_CP Percentage",items:[],kpiInfo:"%",kpiid:1215,
      order:0,parentId:null,parentkpi:null,segmentType:null};
    component.OnCompanyKPIChange(mockData);

    expect(component.setSymbol).toHaveBeenCalledWith(
      component.selectedCompanyKPI
    );
    expect(component.LoadGraph).toHaveBeenCalled();
  });

  it("should load graph", () => {
    const mockResponse = {
      data: [{}, {}],
      yLineFields: ["field1", "field2"],
      yBarFields: ["field3", "field4"],
      xField: "field5",
      yShades: ["shade1", "shade2"],
    };
    component.modelList = { portfolioCompanyID: 123 };
    component.searchFilter = "filter";
    component.companyKpiValueUnit = { typeId: 1 };
    component.typeField = "field";
    component.selectedCompanyKPI = { kpiid: 1 };
    component.LoadGraph();

    expect(mockResponse.data.length).toEqual(2);
    expect(mockResponse.yLineFields.length).toEqual(2);
    expect(mockResponse.yBarFields.length).toEqual(2);
  });
  it('should filter KPIList when value is provided', () => {
    spyOn(component, 'setKpiGroup');
component.companyKPIList = [
  { parentkpi: 'Sales', displayName: 'Total Sales' },
  { parentkpi: 'Sales', displayName: 'Sales Growth' },
  { parentkpi: 'Marketing', displayName: 'Total Marketing' }
];
    component.customFilter('Sales');
    expect(component.setKpiGroup).toHaveBeenCalledWith([
      { parentkpi: 'Sales', displayName: 'Total Sales' },
      { parentkpi: 'Sales', displayName: 'Sales Growth' }
    ]);
  });
  it('should group filteredData by parentkpi', () => {
    const mockFilteredData = [
      { parentkpi: 'Sales', displayName: 'Total Sales' },
      { parentkpi: 'Marketing', displayName: 'Total Marketing' },
      { parentkpi: 'Sales', displayName: 'Sales Growth' }
    ];

    const expectedGroupedData: GroupResult[] = groupBy(mockFilteredData, [
      { field: "parentkpi" },
    ]) as GroupResult[];

    component.setKpiGroup(mockFilteredData);

    expect(component.filteredData).toEqual(expectedGroupedData);
  });
});
