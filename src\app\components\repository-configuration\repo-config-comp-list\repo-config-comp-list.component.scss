@import "../../../../_variables";
@import "../../../../assets/dist/css/font.scss";

$margin-12: 12px;
$selected-fund-height: 200px;

.repo-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid $Neutral-Gray-10;
  overflow: hidden;
  height: 100%;
  border-radius: $space-4;

  .list-group {
    overflow: hidden;
  }

  .selection-container {
    background: $Neutral-Gray-02;

    .bottom-border {
      border-bottom: 1px solid $Neutral-Gray-10;
    }
  }

  .companies-list-container,
  .fund-list-container {
    overflow-y: auto;
    flex: 1;
    min-height: 0;

    .list-group-item {
      display: flex;
      align-items: center;
    }

    .repo-confis {
      padding-top: $margin-medium;
      padding-bottom: $margin-medium;
      color: $Neutral-Gray-90;

      .chkbx-border {
        border-color: $Neutral-Gray-80;
      }
    }

    .selected-portfolio {
      background-color: $Primary-40;
    }
  }

  .select-all-container {
    padding-top: $margin-medium;
    padding-bottom: $margin-medium;

    .chkbx-border {
      border-color: $Neutral-Gray-80;
    }
  }
}

.disabled {
  pointer-events: none;
  opacity: 76%;
}

.all-company {
  color: $Neutral-Gray-90;
  background-color: $Primary-35;
}

.cursor-pointer {
  cursor: pointer;
}

.pc-section-content {
  margin-left: $margin-12;
  margin-right: $padding-small;
}

.fund-section-content {
  margin-right: $margin-12;
  margin-left: $padding-small;
}

.border-gray {
  border: 1px solid $Neutral-Gray-10;
  border-radius: $Radius-4;
}

.selected-container {
  background: $Primary-78;
  color: $Neutral-Gray-00;
}

.deselected-container {
  background-color: $Neutral-Gray-00;
}

.ml-12 {
  margin-left: $margin-12;
}

.search-box-container {
  border: 1px solid $Neutral-Gray-10;
  border-radius: $Radius-4;
  margin: $margin-small $margin-12;
  background: $Neutral-Gray-00;
}

.selected-fund-header{
  color: $Neutral-Gray-90;
}

.list-divider{
  width: 100%;
  margin-top: $margin-12;
  border-bottom: 1px solid $Neutral-Gray-10;
}

.selected-fund-item{
  background-color: $Primary-40;
}

.selected-fund-static{
  max-height: auto;
  overflow-y: unset;
}

.selected-fund-scrollable{
  max-height: $selected-fund-height;
  overflow-y: auto;
}

.item-value{
  padding-top: $margin-medium;
  padding-bottom: $margin-medium;
  color: $Neutral-Gray-90;

  .chkbx-border {
    border-color: $Neutral-Gray-80;
  }
}

.truncate-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;  
  display: inline-block;
  vertical-align: middle;
}