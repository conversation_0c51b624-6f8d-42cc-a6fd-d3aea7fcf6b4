import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { PortfolioDetailsComponent } from './portfolio-details.component';
import { RouterTestingModule } from '@angular/router/testing';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { PcdocumentFoldersComponent } from './pcdocument-folders/pcdocument-folders.component';
import { PCDocumentListComponent } from './pcdocument-list/pcdocument-list.component';
import { PortfolioCompanyDetailComponent } from './portfolioCompany-detail.component';
import { ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgxSpinnerService } from 'ngx-spinner';
import { AccountService } from 'src/app/services/account.service';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { PermissionService } from 'src/app/services/permission.service';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { ReportService } from 'src/app/services/report.service';
import { ToastrService } from 'ngx-toastr';
import { CommonSubFeaturePermissionService } from "src/app/services/subPermission.service";
import { of } from 'rxjs';
import { PageConfigurationService } from 'src/app/services/page-configuration.service';

describe('PortfolioDetailsComponent', () => {
  let component: PortfolioDetailsComponent;
  let fixture: ComponentFixture<PortfolioDetailsComponent>;
  let subPermissionService: CommonSubFeaturePermissionService;
  let mockPageConfigurationService: jasmine.SpyObj<PageConfigurationService>;

  beforeEach(() => {
    const changeDetectorRefStub = () => ({});
    const activatedRouteStub = () => ({ snapshot: { params: {} } });
    const ngbModalStub = () => ({
      open: (savePortfolioProfitabilityComponent, modalOption) => ({})
    });
    const ngxSpinnerServiceStub = () => ({});
    const accountServiceStub = () => ({ redirectToUnauthorized: () => ({}) });
    const miscellaneousServiceStub = () => ({
      getSmallPagerLength: () => ({}),
      GetPriviousPageUrl: () => ({}),
      getMessageTimeSpan: () => ({}),
      getTitle: arg => ({}),
      showAlertMessages: (string, message) => ({}),
      downloadExcelFile: response => ({}),
      downloadPDFFile: results => ({}),
      redirectToLogin: error => ({})
    });
    const permissionServiceStub = () => ({
      checkUserPermission: (company, arg1, id) => ({})
    });
    const portfolioCompanyServiceStub = () => ({
      getPortfolioCompanyById: object => ({ subscribe: f => f({}) }),
      getfinaluploadfiles: path2 => ({ subscribe: f => f({}) }),
      getPortfolioCompanyInvestmentKPIValues: object => ({
        subscribe: of => of({})
      }),
      getPCCompanyKPIValues: object => ({ subscribe: f => f({}) }),
      getPortfolioCompanyImpactKPIValues: object => ({ subscribe: f => f({}) }),
      saveCustomCommentary: string => ({ subscribe: f => f({}) }),
      getPageConfigSubSectionField: string => ({ subscribe: f => f({}) }),
      getPortfolioCompanyCommentarySections: string => ({ subscribe: f => f({}) })
    });
    const reportServiceStub = () => ({
      setDataAvailabilityInReport: arg => ({})
    });
    const toastrServiceStub = () => ({
      success: (message, string, object) => ({})
    });
    const subPermissionServiceStub = () => ({
      getCommonSubFeatureAccessPermissions: (id, feature) => ({
        subscribe: (fn: any) => fn({
          next: (result: any) => result
        })
      })
    });
    const mockResult = {
      subPageList: [
        {
          name: 'Documents',
          id: 1,
          displayName: 'Document Tab',
          isActive: true,
        },
        {
          name: 'OtherTab',
          id: 2,
          displayName: 'Other Tab',
          isActive: false,
        },
      ],
      fieldValueList: [
        { subPageID: 1, fieldName: 'Field1', fieldValue: 'Value1' },
        { subPageID: 1, fieldName: 'Field2', fieldValue: 'Value2' },
        { subPageID: 2, fieldName: 'Field3', fieldValue: 'Value3' },
      ],
    };
    mockPageConfigurationService = jasmine.createSpyObj('PageConfigurationService', ['getPageConfigSettingById']);
// Mocking the service in tests
mockPageConfigurationService.getPageConfigSettingById.and.returnValue(of(mockResult));
    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule,
        SharedComponentModule
      ],
      declarations: [
        PortfolioDetailsComponent,
        PortfolioCompanyDetailComponent,
        PcdocumentFoldersComponent,
        PCDocumentListComponent
      ],
      providers: [
        { provide: ChangeDetectorRef, useFactory: changeDetectorRefStub },
        { provide: ActivatedRoute, useFactory: activatedRouteStub },
        { provide: NgbModal, useFactory: ngbModalStub },
        { provide: NgxSpinnerService, useFactory: ngxSpinnerServiceStub },
        { provide: AccountService, useFactory: accountServiceStub },
        { provide: MiscellaneousService, useFactory: miscellaneousServiceStub },
        { provide: PermissionService, useFactory: permissionServiceStub },
        {
          provide: PortfolioCompanyService,
          useFactory: PortfolioCompanyService
        },
        { provide: ReportService, useFactory: reportServiceStub },
        { provide: ToastrService, useFactory: toastrServiceStub },
        {
          provide: CommonSubFeaturePermissionService,
          useFactory: subPermissionServiceStub
        },
        { provide: PageConfigurationService, useValue: mockPageConfigurationService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA] // Added to handle custom elements like nep-tab
    });
    fixture = TestBed.createComponent(PortfolioDetailsComponent);
    component = fixture.componentInstance;
    subPermissionService = TestBed.inject(CommonSubFeaturePermissionService);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Permission Tests', () => {
    it('should check permission access correctly', () => {
      const mockPermissions = [
        { subFeature: 'Documents', CAN_VIEW: true, CAN_EDIT: false },
        { subFeature: 'Other', CAN_VIEW: false, CAN_EDIT: false }
      ];

      expect(component.checkPermissionAccess(mockPermissions, 'CAN_VIEW')).toBeTruthy();
      expect(component.checkPermissionAccess(mockPermissions, 'CAN_EDIT')).toBeFalsy();
    });

    it('should update tab list based on document permissions', () => {
      component.canViewDocuments = true;
      component.updateTabList();
      expect(component.tabList.length).toBe(2);
      expect(component.tabList[1].name).toBe('Document Tab');

      component.canViewDocuments = false;
      component.updateTabList();
      expect(component.tabList.length).toBe(1);
      expect(component.tabList[0].name).toBe('Portfolio Company Details');
    });

    it('should set document permissions correctly', () => {
      const mockResult = [{
        subFeature: 'Documents',
        CAN_VIEW: true,
        CAN_EDIT: true
      }];

      // Create the observable and spy
      const observable = of(mockResult);
      spyOn(subPermissionService, 'getCommonSubFeatureAccessPermissions').and.returnValue(observable);

      // Call getDocumentsPermissions
      component.getDocumentsPermissions();
      
      // Trigger subscription callback manually
      observable.subscribe({
        next: (result) => {
          component.canViewDocuments = component.checkPermissionAccess(
            result.filter(x => x.subFeature === 'Documents'),
            'CAN_VIEW'
          );
          component.canEditDocuments = component.checkPermissionAccess(
            result.filter(x => x.subFeature === 'Documents'),
            'CAN_EDIT'
          );
          component.updateTabList();

          // Now run the expectations
          expect(component.canViewDocuments).toBeTruthy();
          expect(component.canEditDocuments).toBeTruthy();
          expect(component.tabList.length).toBe(2);
        }
      });
    });

    it('should handle empty permission result', () => {
      spyOn(subPermissionService, 'getCommonSubFeatureAccessPermissions').and.returnValue(of([]));

      component.getDocumentsPermissions();

      expect(component.canViewDocuments).toBeFalsy();
      expect(component.canEditDocuments).toBeFalsy();
    });
    it('should call getPageConfigSetting when canViewDocuments is true', () => {
      spyOn(component, 'getPageConfigSetting');
      component.canViewDocuments = true;
      component.updateTabList();
      expect(component.getPageConfigSetting).toHaveBeenCalled();
    });
  
    it('should switch tabs correctly in switchTab', () => {
      const mockTab = { name: 'Documents', active: true, aliasname: 'Documents' };
      component.switchTab(mockTab);
      expect(component.tabName).toBe('Documents');
    });
  });
});
