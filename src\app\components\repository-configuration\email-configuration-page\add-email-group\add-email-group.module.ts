import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AddEmailGroupComponent } from './add-email-group.component';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { DialogModule } from '@progress/kendo-angular-dialog';
import { GridModule } from '@progress/kendo-angular-grid';
import { ButtonsModule } from '@progress/kendo-angular-buttons';
import { RepositorySharedModule } from '../../shared/repository-shared.module';

@NgModule({
    declarations: [
        AddEmailGroupComponent,
    ],
    imports: [
        CommonModule,
        SharedComponentModule,
        FormsModule,
        DialogModule,
        GridModule,
        ButtonsModule,
        RepositorySharedModule,
        RouterModule.forChild([
            {
                path: '',
                component: AddEmailGroupComponent
            }
        ])
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    exports: [AddEmailGroupComponent]
})

export class AddEmailGroupModule { }
