import { Component, EventEmitter, Output, OnInit } from '@angular/core';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';

@Component({
  selector: 'app-email-configuration-company-list',
  templateUrl: './email-configuration-company-list.component.html',
  styleUrls: ['./email-configuration-company-list.component.scss'] // Changed to .scss
})

export class EmailConfigurationCompanyListComponent implements OnInit {
  resetInProgress: boolean = false;
  @Output() selectedCompanies = new EventEmitter<any>();
  
  constructor(
    private repositoryConfigService: RepositoryConfigService
  ) {}
  
  searchTerm: string = '';
  inputValue: string = '';
  selectAll: boolean = false;
  companies: { name: string, companyId: number, selected: boolean }[] = [];
  searchedCompanies: { name: string, companyId: number, selected: boolean }[] = [];
  isLoader: boolean = false;
  
  ngOnInit() {
    this.getCompanies();
    this.repositoryConfigService.resetInProgress$.subscribe((state: boolean) => {
      this.resetInProgress = state;
    });
  } 
  
  getCompanies() {
    this.isLoader = true;
    this.repositoryConfigService.getPortfolioCompanies().subscribe(
      (data: any) => {
        this.companies = data.map((company: any) => ({
          name: company.companyName,
          companyId: company.portfolioCompanyID,
          selected: false
        }));
        this.isLoader = false; 
      },
      (error: any) => {
        console.error('Error fetching companies for email configuration:', error);
        this.isLoader = false; 
      }
    );
  }
  
  get filteredCompanies() {
    return this.companies.filter(company =>
      company.name.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

toggleCompanySelection(company: any): void {
  this.companies.forEach(c => c.selected = false); // Deselect all companies
  company.selected = true; // Select the clicked company
  this.selectedCompanies.emit([company]); // Emit the selected company
}
 
  onInputChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value.trim().toLowerCase();
    this.searchedCompanies = this.searchTerm
      ? this.companies.filter(company => company.name.toLowerCase().includes(this.searchTerm))
      : [];
  }
}