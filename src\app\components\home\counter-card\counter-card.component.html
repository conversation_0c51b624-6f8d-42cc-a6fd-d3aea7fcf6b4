<style>
  .style1 {
    text-transform: capitalize;
    letter-spacing: 0px;
    color: #55565A;
    opacity: 1;

  }

  .style2 {
    font-size: 1.25rem;
    text-align: left;
    letter-spacing: 0px;
    color: #212121;
    opacity: 1; 
  }

  .style3 {
    margin-top: 0px;
    font-size: 0.88rem;
    text-align: left;
    letter-spacing: 0.12px;
    color: #75787B;
    opacity: 1;
  }
</style>

  <div class="card-counter headerborder cardheadb-oh">
    <div class="style1 TextTruncate" title="{{Item}}">{{Item}}</div>
    <div class="style2 TextTruncate" title="{{Value}} {{Unit}}">{{Value}} {{Unit}}</div>
    <div class="style3 TextTruncate" title="{{Currency}}">{{Currency}}</div>
  </div>
