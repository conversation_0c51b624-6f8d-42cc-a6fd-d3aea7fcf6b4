/*! normalize.css v7.0.0 | MIT License | github.com/necolas/normalize.css */

@font-face{
    font-family: "Helvetica Neue LT W05_25 Ult Lt";
    src:url("./assets/Fonts/5664070/ec6281a0-c9c4-4477-a360-156acd53093f.woff2") format("woff2"),url("./assets/Fonts/5664070/11066b40-10f7-4123-ba58-d9cbf5e89ceb.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_26UltLtIt";
    src:url("./assets/Fonts/5664077/2707a251-2d32-4bb6-a3c4-87114ba2365f.woff2") format("woff2"),url("./assets/Fonts/5664077/40f50724-486b-4e7b-9366-237e06eabfc8.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_35 Thin";
    src:url("./assets/Fonts/5664081/7d63ccf8-e0ae-4dee-ad4d-bbc798aa5803.woff2") format("woff2"),url("./assets/Fonts/5664081/b2c1327f-ab3d-4230-93d7-eee8596e1498.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_36 Th It";
    src:url("./assets/Fonts/5664067/2a7e8f89-c0b2-4334-9c34-7a2078d2b959.woff2") format("woff2"),url("./assets/Fonts/5664067/32aad9d8-5fec-4b9d-ad53-4cf7a5b53698.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_45 Light";
    src:url("./assets/Fonts/5664085/f9c5199e-a996-4c08-9042-1eb845bb7495.woff2") format("woff2"),url("./assets/Fonts/5664085/2a34f1f8-d701-4949-b12d-133c1c2636eb.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_46 Lt It";
    src:url("./assets/Fonts/5664089/5e4f385b-17ff-4d27-a63a-9ee28546c9a8.woff2") format("woff2"),url("./assets/Fonts/5664089/116cde47-4a07-44a5-9fac-cbdcc1f14f79.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_55 Roman";
    src:url("./assets/Fonts/5664093/08b57253-2e0d-4c12-9c57-107f6c67bc49.woff2") format("woff2"),url("./assets/Fonts/5664093/08edde9d-c27b-4731-a27f-d6cd9b01cd06.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_56 Italic";
    src:url("./assets/Fonts/5664098/4bd56f95-e7ab-4a32-91fd-b8704cbd38bc.woff2") format("woff2"),url("./assets/Fonts/5664098/4fe1c328-1f21-434a-8f0d-5e0cf6c70dfb.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_65 Medium";
    src:url("./assets/Fonts/5664103/240c57a0-fdce-440d-9ce3-85e0cb56f470.woff2") format("woff2"),url("./assets/Fonts/5664103/7802e576-2ffa-4f22-a409-534355fbea79.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05 66 Md It";
    src:url("./assets/Fonts/5664107/de68be2a-5d0e-4b8d-b3eb-940f75503e2a.woff2") format("woff2"),url("./assets/Fonts/5664107/31029e78-79a0-4940-b82d-2e3c238e1355.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_75 Bold";
    src:url("./assets/Fonts/5664150/800da3b0-675f-465f-892d-d76cecbdd5b1.woff2") format("woff2"),url("./assets/Fonts/5664150/7b415a05-784a-4a4c-8c94-67e9288312f5.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_76 Bd It";
    src:url("./assets/Fonts/5664111/13ab58b4-b5ba-4c95-afde-ab2608fbbbd9.woff2") format("woff2"),url("./assets/Fonts/5664111/5018b5b5-c821-4653-bc74-d0b11d735f1a.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_85 Heavy";
    src:url("./assets/Fonts/5664115/7e42a406-9133-48c0-a705-4264ac520b43.woff2") format("woff2"),url("./assets/Fonts/5664115/837750e9-3227-455d-a04e-dc76764aefcf.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_86 Hv It";
    src:url("./assets/Fonts/5664119/0acba88f-0de4-4d43-81fd-920d7427f665.woff2") format("woff2"),url("./assets/Fonts/5664119/713c9c40-9cbd-4276-819e-d0efaf5d3923.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_95 Black";
    src:url("./assets/Fonts/5664121/fc4fb6ca-f981-4115-b882-c78e9f08be52.woff2") format("woff2"),url("./assets/Fonts/5664121/6ed03453-f512-45ba-84bf-fe4ea45d5e6a.woff") format("woff");
}
@font-face{
    font-family:"Helvetica Neue LT W05_96 Blk It";
    src:url("./assets/Fonts/5664128/995add04-59cc-41ea-abd2-4712eaddf2a8.woff2") format("woff2"),url("./assets/Fonts/5664128/7090e465-f6bf-4664-8b5a-d877a6915d87.woff") format("woff");
}

body {
    --btn-hover-darken: 10%;
    -webkit-text-size-adjust: none;
    -ms-text-size-adjust: none;
    -moz-text-size-adjust: none;
    text-size-adjust: none;
}

html {
    line-height: 1.15;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

article,
aside,
dialog,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section {
    display: block;
}

body {
    margin: 0;
    color: $nep-text-grey;
    color: var(--secondary-color, $nep-text-grey);
    font-family: $nep-font-family;
    font-size: 14px;
    line-height: 1.42857143;
}

article,
aside,
footer,
header,
nav,
section {
    display: block;
}

h1 {
    margin: 0.67em 0;
    font-size: 2em;
}

/*As per the spec provided by Vipin for h2*/

.headerheadding {
    cursor: default;
    font: normal normal normal 24px/24px Helvetica;
}

figcaption,
figure,
main {
    display: block;
}

figure {
    margin: 1em 40px;
}

hr {
    overflow: visible;
    height: 0;
    box-sizing: content-box;
}

pre {
    font-family: "Helvetica Neue LT W05_55 Roman",Arial, Verdana, Tahoma,sans-serif;
    font-size: 1em;
}

a {
    background-color: transparent;
    color: $nep-info;
    cursor: pointer;
    outline: none;
    text-decoration: none;
    -webkit-text-decoration-skip: objects;
    transition: color 0.3s;
}

a:active,
a:hover {
    color: $nep-info;
    outline: 0;
    text-decoration: none;
}

a[disabled] {
    color: $gray-200;
    color: var(--gray-600, $gray-200);
    cursor: not-allowed;
    pointer-events: none;
}

abbr[title] {
    border-bottom: none;
    text-decoration: underline;
    text-decoration: underline dotted;
}

b,
strong {
    font-weight: inherit;
    font-weight: bolder;
}

code,
kbd,
samp {
    font-family: monospace, monospace;
    font-size: 1em;
}

dfn {
    font-style: italic;
}

mark {
    background-color: #ff0;
    color: $nep-black;
}

small {
    font-size: 80%;
}

sub,
sup {
    position: relative;
    font-size: 75%;
    line-height: 0;
    vertical-align: baseline;
}

sub {
    bottom: -0.25em;
}

sup {
    top: -0.5em;
}

audio,
video {
    display: inline-block;
}

audio:not([controls]) {
    display: none;
    height: 0;
}

img {
    border-style: none;
}

svg:not(:root) {
    overflow: hidden;
}

button,
input,
optgroup,
select,
textarea {
    margin: 0;
    font-size: 100%;
    line-height: 1.15;
}

button,
input {
    overflow: visible;
}

button,
select {
    text-transform: none;
}

[type="reset"],
[type="submit"],
button,
html [type="button"] {
    -webkit-appearance: button;
}

[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner,
button::-moz-focus-inner {
    padding: 0;
    border-style: none;
}

[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring,
button:-moz-focusring {
    outline: 1px dotted ButtonText;
}

fieldset {
    padding: 0.35em 0.75em 0.625em;
}

legend {
    display: table;
    max-width: 100%;
    box-sizing: border-box;
    padding: 0;
    color: inherit;
    white-space: normal;
}

progress {
    display: inline-block;
    vertical-align: baseline;
}

textarea {
    overflow: auto;
}

[type="checkbox"],
[type="radio"] {
    box-sizing: border-box;
    padding: 0;
}

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
    height: auto;
}

[type="search"] {
    -webkit-appearance: textfield;
    outline-offset: -2px;
}

[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit;
}

details,
menu {
    display: block;
}

summary {
    display: list-item;
}

canvas {
    display: inline-block;
}

[hidden],
template {
    display: none;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    color: inherit;
    font-family: inherit;
    font-weight: 500;
    line-height: 1.1;
}

.h1 .small,
.h1 small,
.h2 .small,
.h2 small,
.h3 .small,
.h3 small,
.h4 .small,
.h4 small,
.h5 .small,
.h5 small,
.h6 .small,
.h6 small,
h1 .small,
h1 small,
h2 .small,
h2 small,
h3 .small,
h3 small,
h4 .small,
h4 small,
h5 .small,
h5 small,
h6 .small,
h6 small {
    color: #ababab;
    color: var(--gray-500, #ababab);
    font-weight: 400;
    line-height: 1;
}

.h1,
.h2,
.h3,
h1,
h2,
h3 {
    margin-top: 20px;
    margin-bottom: 10px;
}

.h1 .small,
.h1 small,
.h2 .small,
.h2 small,
.h3 .small,
.h3 small,
h1 .small,
h1 small,
h2 .small,
h2 small,
h3 .small,
h3 small {
    font-size: 65%;
}

.h4,
.h5,
.h6,
h4,
h5,
h6 {
    margin-top: 10px;
    margin-bottom: 10px;
}

.h4 .small,
.h4 small,
.h5 .small,
.h5 small,
.h6 .small,
.h6 small,
h4 .small,
h4 small,
h5 .small,
h5 small,
h6 .small,
h6 small {
    font-size: 75%;
}

p {
    margin: 0 0 10px;
}

.nep-expose-location-primary {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
    display: none;
    position: absolute;
}

.nep-expose-location-warning {
    color: #c68700;
    color: var(--warning-color, #c68700);
    display: none;
    position: absolute;
}

.nep-expose-location-danger {
    color: #ba0c2f;
    color: var(--danger-color, #ba0c2f);
    display: none;
    position: absolute;
}

.nep-expose-location-success {
    color: #388e3c;
    color: var(--success-color, #388e3c);
    display: none;
    position: absolute;
}

.nep-expose-location-secondary {
    color: #55565a;
    color: var(--secondary-color, #55565a);
    display: none;
    position: absolute;
}

.nep-expose-primary-background {
    background-color: #4061C7;
    background-color: var(--primary-color, #4061C7);
}

.nep-expose-primary-color {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-expose-primary-border {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
}

.nep-expose-warning-background {
    background-color: #c68700;
    background-color: var(--warning-color, #c68700);
}

.nep-expose-warning-color {
    color: #c68700;
    color: var(--warning-color, #c68700);
}

.nep-expose-warning-border {
    border-color: #c68700;
    border-color: var(--warning-color, #c68700);
}

.nep-expose-danger-background {
    background-color: #ba0c2f;
    background-color: var(--danger-color, #ba0c2f);
}

.nep-expose-danger-color {
    color: #ba0c2f;
    color: var(--danger-color, #ba0c2f);
}

.nep-expose-danger-border {
    border-color: #ba0c2f;
    border-color: var(--danger-color, #ba0c2f);
}

.nep-expose-success-background {
    background-color: #388e3c;
    background-color: var(--success-color, #388e3c);
}

.nep-expose-success-color {
    color: #388e3c;
    color: var(--success-color, #388e3c);
}

.nep-expose-success-border {
    border-color: #388e3c;
    border-color: var(--success-color, #388e3c);
}

.nep-expose-secondary-background {
    background-color: #55565a;
    background-color: var(--secondary-color, #55565a);
}

.nep-expose-secondary-color {
    color: #55565a;
    color: var(--secondary-color, #55565a);
}

.nep-expose-secondary-border {
    border-color: #55565a;
    border-color: var(--secondary-color, #55565a);
}

.nep-expose-table {
    color: $gray-200;
    color: var(--table-color, $gray-200);
}

.nep-expose-table-head {
    font-weight: 700;
    font-weight: var(--table-head-font-weight, bold);
    background: #fafafb;
    background: var(--table-head-bg, #fafafb);
    border-radius: 0 var(--table-border-radius-top, 0) 0 0;
    border-radius: var(--table-border-radius-top, 0) var(--table-border-radius-top, 0) 0 0;
    color: var(--table-head-color, #55565a);
    color: var(--table-head-color, var(--secondary-color, #55565a));
    border-color: #e0e0e0;
    border-color: var(--table-border-color, #e0e0e0);
}

.nep-expose-table-head-hover {
    background: #f5f5f5;
    background: var(--table-hover-bg, #f5f5f5);
}

.nep-expose-select-result-item {
    background: #fafafb;
    background: var(--gray-100, #fafafb);
    padding: var(--select-result-padding-vertical, 0) var(--select-result-padding-horizontal-16, 20px) var(--select-result-padding-vertical, 0) var(--select-result-padding-horizontal, 4px);
}

.nep-expose-select-option-hover {
    background: #f5f5f5;
    background: var(--select-option-hover-bg, #f5f5f5);
    color: #4061C7;
    color: var(--select-option-hover-color, #4061C7);
}

.nep-expose-datepicker-month-item {
    border-radius: 2px;
}

.nep-expose-slider-bar {
    background: #cac9c7;
    background: var(--slider-bar-color, #cac9c7);
}

.nep-expose-slider-indicator {
    background: #fff;
    background: var(--slider-indicator-bg, #fff);
    width: 14px;
    width: var(--slider-indicator-size, 14px);
}

.nep-expose-slider-value {
    height: 14px;
    height: var(--slider-value-bottom, 14px);
}

.nep-expose-menu-dark {
    background: #4061C7;
    background: var(--menu-dark-bg, #4061C7);
    color: hsla(0, 0%, 100%, 0.7);
    color: var(--menu-dark-color, hsla(0, 0%, 100%, 0.7));
}

.nep-expose-menu-dark-active {
    background: hsla(0, 0%, 100%, 0.2);
    background: var(--menu-dark-acitve-bg, hsla(0, 0%, 100%, 0.2));
}

.nep-expose-menu-active {
    background: #4061C7;
    background: var(--menu-item-active-bg, #4061C7);
    color: #fff;
    color: var(--menu-item-active-color, #fff);
    border-radius: 0;
    border-radius: var(--menu-active-border-radius, 0);
    padding: var(--menu-active-padding-vertical, 0) var(--menu-active-padding-horizontal, 0);
}

.nep-expose-form-inline {
    margin-right: 20px;
    margin-right: var(--form-item-margin-right, 20px);
}

.nep-expose-form-tip {
    color: #a9acbb;
    color: var(--form-tip-color, #a9acbb);
}

.nep-expose-gray-100 {
    color: #fafafb;
    color: var(--gray-100, #fafafb);
}

.nep-expose-gray-200 {
    color: #f0f0f1;
    color: var(--gray-200, #f0f0f1);
}

.nep-expose-gray-300 {
    color: #dedfe0;
    color: var(--gray-300, #dedfe0);
}

.nep-expose-gray-400 {
    color: #cac9c7;
    color: var(--gray-400, #cac9c7);
}

.nep-expose-gray-500 {
    color: #ababab;
    color: var(--gray-500, #ababab);
}

.nep-expose-gray-600 {
    color: $gray-200;
    color: var(--gray-600, $gray-200);
}

.nep-expose-gray-700 {
    color: #75787b;
    color: var(--gray-700, #75787b);
}

.nep-expose-gray-800 {
    color: #55565a;
    color: var(--gray-800, #55565a);
}

.nep-expose-gray-900 {
    color: #434655;
    color: var(--gray-900, #434655);
}

.nep-expose-button {
    margin-left: 12px;
    margin-left: var(--button-margin-left, 12px);
}

.nep-expose-input-focus {
    border-color: #4061C7;
    border-color: var(--input-border-focus-color, #4061C7);
}

.nep-expose-radio {
    width: 16px;
    width: var(--radio-width, 16px);
    border: 1px solid #fff;
    border: var(--radio-border-width, 1px) solid #fff;
}

.nep-expose-radio-inner {
    width: 10px;
    width: var(--radio-inner-width, 10px);
}

.nep-expose-pagination-hover {
    border-color: #e0e0e0;
    border-color: var(--pagination-hover-border, #e0e0e0);
    color: #4061C7;
    color: var(--pagination-hover-color, #4061C7);
    background-color: #fafafb;
    background-color: var(--pagination-hover-bg, #fafafb);
}

.nep-expose-modal-icon {
    width: 24px;
    width: var(--modal-icon-size, 24px);
}

.nep-expose-tag-close {
    color: hsla(0, 0%, 100%, 0.2);
    color: var(--tag-close-color, hsla(0, 0%, 100%, 0.2));
}

.nep-alert {
    position: relative;
    display: flex;
    padding: 8px 16px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border: var(--alert-border-width, 1px) solid transparent;
    transform-origin: 0 0;
    transition: transform 0.216s, opacity 0.216s ease-out;
    box-shadow: var(--alert-box-shadow, "none");
    font-size: 14px;
    font-size: var(--alert-font-size, 14px);
    border-radius: 0 0 8px 8px;
}

.nep-alert h2,
.nep-alert h3,
.nep-alert h4 {
    margin-top: 0.3em;
    color: inherit;
}

.nep-alert-with-close {
    padding-right: 36px;
}

.nep-alert-link {
    font-weight: 700;
}

.nep-alert-close {
    position: absolute;
    top: 8px;
    right: 12px;
    display: block;
}

.nep-alert-close svg {
    width: 10px;
    height: 10px;
}

.nep-alert-close svg path {
    fill: rgba(0, 0, 0, 0.3);
}

.nep-alert-close:hover svg path {
    fill: rgba(0, 0, 0, 0.8);
}

.nep-alert-dismissed {
    opacity: 0;
    transform: scaleY(0);
}

.nep-alert-icon {
    display: block;
    margin-top: 1px;
}

.nep-alert-content {
    flex: 1;
    word-break: break-all;
}

.nep-alert>p,
.nep-alert>ul {
    margin-bottom: 0;
}

.nep-alert>p+p {
    margin-top: 5px;
}

.nep-alert h2,
.nep-alert h3,
.nep-alert h4 {
    margin-top: 0;
}

.nep-alert-default {
    border-color: #f0f0f1;
    border-color: var(--gray-200, #f0f0f1);
    background-color: #fff;
    color: $nep-black;
}

.nep-alert-default hr {
    border-top-color: #e3e3e5;
    border-top-color: var(--gray-200-darken-5, #e3e3e5);
}

.nep-alert-default .alert-link {
    color: #080808;
}

.nep-alert-info {
    border-color: #c5f1f8;
    border-color: var(--alert-info-border-color, #c5f1f8);
    background-color: #e5f5fc;
    background-color: var(--alert-info-bg, #e5f5fc);
    color: #31708f;
    color: var(--alert-info-text-color, #31708f);
}

.nep-alert-info hr {
    border-top-color: #aeecf6;
    border-top-color: var(--alert-info-border-darken-5-color, #aeecf6);
}

.nep-alert-info .alert-link {
    color: #245269;
    color: var(--alert-info-text-darken-10-color, #245269);
}

.nep-alert-info .nep-alert-icon path {
    fill: #00acc1;
    fill: var(--info-color, #00acc1);
}

.nep-alert-warning {
    border-color: #ffd59a;
    border-color: var(--alert-warning-border-color, #ffd59a);
    background-color: #ffecb3;
    background-color: var(--alert-warning-bg, #ffecb3);
    color: #c68700;
    color: var(--alert-warning-text-color, #c68700);
}

.nep-alert-warning hr {
    border-top-color: #ffca80;
    border-top-color: var(--alert-warning-border-darken-5-color, #ffca80);
}

.nep-alert-warning .alert-link {
    color: #936400;
    color: var(--alert-warning-text-darken-10-color, #936400);
}

.nep-alert-warning .nep-alert-icon path {
    fill: #c68700;
    fill: var(--warning-color, #c68700);
}

.nep-alert-success {
    border-color: #bcdeb6;
    border-color: var(--alert-success-border-color, #bcdeb6);
    background-color: #c8e6c9;
    background-color: var(--alert-success-bg, #c8e6c9);
    color: #388e3c;
    color: var(--alert-success-text-color, #388e3c);
}

.nep-alert-success hr {
    border-top-color: #abd6a5;
    border-top-color: var(--alert-success-border-darken-5-color, #abd6a5);
}

.nep-alert-success .alert-link {
    color: #2a692d;
    color: var(--alert-success-text-darken-10-color, #2a692d);
}

.nep-alert-success .nep-alert-icon path {
    fill: #388e3c;
    fill: var(--success-color, #388e3c);
}

.nep-alert-danger,
.nep-alert-error {
    border-color: #ecb5be;
    border-color: var(--alert-danger-border-color, #ecb5be);
    background-color: #f1c9c9;
    background-color: var(--alert-danger-bg, #f1c9c9);
    color: #c62828;
    color: var(--alert-danger-text-color, #c62828);
}

.nep-alert-danger hr,
.nep-alert-error hr {
    border-top-color: #e6a1ac;
    border-top-color: var(--alert-danger-border-darken-5-color, #e6a1ac);
}

.nep-alert-danger .alert-link,
.nep-alert-error .alert-link {
    color: #9c1f1f;
    color: var(--alert-danger-text-darken-10-color, #9c1f1f);
}

.nep-alert-danger .nep-alert-icon path,
.nep-alert-error .nep-alert-icon path {
    fill: #ba0c2f;
    fill: var(--danger-color, #ba0c2f);
}

.nep-button {
    font-family: "Helvetica Neue LT W05_55 Roman",Arial, Verdana, Tahoma,sans-serif;
    display: inline-block;
    margin-bottom: 0;
    border: 1px solid transparent;
    background-image: none;
    cursor: pointer;
    font-weight: 14px;
    outline: none;
    text-align: center;
    touch-action: manipulation;
    vertical-align: middle;
    white-space: nowrap;
    padding: var(--button-padding-base-vertical, 4px) var(--button-padding-base-horizontal, 16px);
    border-radius: 3px;
    border-radius: var(--button-border-radius, 3px);
    font-size: 14px;
    font-size: var(--button-font-size-base, 14px);
    line-height: 1.42857143;
    height: 32px;
    user-select: none;
}

.nep-button-spin {
    display: inline-block;
    margin-right: 8px;
    margin-right: var(--button-spin-margin, 8px);
}

.nep-button:focus,
.nep-button[disabled]:hover {
    z-index: 10;
    outline: none;
    text-decoration: none;
}

.nep-button::-moz-focus-inner {
    border: 0;
}

.nep-button.active {
    background-image: none;
    outline: 0;
}

.nep-button+.nep-button {
    margin-left: 12px;
    margin-left: var(--button-margin-left, 12px);
}

.nep-button[disabled],
fieldset[disabled] .nep-button {
    position: relative;
    box-shadow: none;
    cursor: not-allowed;
    opacity: 0.65;
}

fieldset[disabled] a.nep-button {
    pointer-events: none;
}

.nep-button-default {
    border-color: transparent;
    background-color: #55565a;
    background-color: var(--secondary-color, #55565a);
    color: #fff;
    transition: background 0.15s ease-in-out;
}

.nep-button-default:active,
.nep-button-default:focus,
.nep-button-default:hover {
    border-color: #55565a;
    border-color: var(--secondary-color, #55565a);
    color: #fff;
}

.nep-button-default:active {
    animation: btn-focus-secondary 0.4s ease-out;
    background-color: #3c3d40;
    background-color: var(--secondary-color-dark-btn-hover, #3c3d40);
    background-image: none;
}

.nep-button-default[disabled]:focus,
.nep-button-default[disabled]:hover,
fieldset[disabled] .nep-button-default:focus,
fieldset[disabled] .nep-button-default:hover {
    border-color: transparent;
    background-color: #55565a;
    background-color: var(--secondary-color, #55565a);
}

.nep-button-default[disabled]:active,
fieldset[disabled] .nep-button-default:active {
    animation: none;
}

.nep-button-default.nep-button-outline {
    border-color: #fff;
    background: transparent;
    color: #fff;
}

.nep-button-default.nep-button-outline:active,
.nep-button-default.nep-button-outline:focus,
.nep-button-default.nep-button-outline:hover {
    background-color: #fff;
    color: #ababab;
    color: var(--gray-500, #ababab);
}

.nep-button-default.nep-button-outline:active {
    animation: btn-focus-secondary 0.4s ease-out;
}

.nep-button-default.nep-button-outline[disabled]:focus,
.nep-button-default.nep-button-outline[disabled]:hover,
fieldset[disabled] .nep-button-default.nep-button-outline:focus,
fieldset[disabled] .nep-button-default.nep-button-outline:hover {
    border-color: #fff;
    background: transparent;
    color: #fff;
}

.nep-button-default.nep-button-outline[disabled]:active,
fieldset[disabled] .nep-button-default.nep-button-outline:active {
    animation: none;
}

.nep-button-default[disabled]:hover {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-button-primary {
    border-color: transparent;
    background-color: #4061C7;
    background-color: var(--primary-color, #4061C7);
    color: #fff;
    transition: background 0.15s ease-in-out;
}

.nep-button-primary:focus,
.nep-button-primary:hover {
    background-color: #031b87;
    background-color: var(--primary-color-dark-btn-hover, #031b87);
    color: #fff;
}

.nep-button-primary:active {
    animation: btn-focus-primary 0.4s ease-out;
    background-color: #010723;
    background-color: var(--primary-color-dark-btn-active, #010723);
    color: #fff;
    background-image: none;
}

.nep-button-primary[disabled]:focus,
.nep-button-primary[disabled]:hover,
fieldset[disabled] .nep-button-primary:focus,
fieldset[disabled] .nep-button-primary:hover {
    border-color: transparent;
    background-color: #4061C7;
    background-color: var(--primary-color, #4061C7);
}

.nep-button-primary[disabled]:active,
fieldset[disabled] .nep-button-primary:active {
    animation: none;
}

.nep-button-primary.nep-button-outline {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
    background: transparent;
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-button-primary.nep-button-outline:active,
.nep-button-primary.nep-button-outline:focus,
.nep-button-primary.nep-button-outline:hover {
    background-color: #4061C7;
    background-color: var(--primary-color, #4061C7);
    color: #fff;
}

.nep-button-primary.nep-button-outline:active {
    animation: btn-focus-primary 0.4s ease-out;
}

.nep-button-primary.nep-button-outline[disabled]:focus,
.nep-button-primary.nep-button-outline[disabled]:hover,
fieldset[disabled] .nep-button-primary.nep-button-outline:focus,
fieldset[disabled] .nep-button-primary.nep-button-outline:hover {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
    background: transparent;
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-button-primary.nep-button-outline[disabled]:active,
fieldset[disabled] .nep-button-primary.nep-button-outline:active {
    animation: none;
}

.nep-button-secondary {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
    background-color: #fff;
    color: #4061C7;
    color: var(--primary-color, #4061C7);
    transition: background 0.15s ease-in-out;
}

@keyframes btn-focus-secondary {
    0% {
        box-shadow: 0 0 0 0 rgba(1, 12, 60, 0.6);
        box-shadow: 0 0 0 0 var(--primary-color-dark-5_fade-60, rgba(1, 12, 60, 0.6));
        box-shadow: 0 0 0 0 rgba(73, 73, 77, 0.6);
        box-shadow: 0 0 0 0 var(--secondary-color-dark-5_fade-60, rgba(73, 73, 77, 0.6));
    }
    50% {
        box-shadow: 0 0 0 0.4em rgba(1, 12, 60, 0);
        box-shadow: 0 0 0 0.4em var(--primary-color-dark-5_fade-0, rgba(1, 12, 60, 0));
        box-shadow: 0 0 0 0.4em rgba(73, 73, 77, 0);
        box-shadow: 0 0 0 0.4em var(--secondary-color-dark-5_fade-0, rgba(73, 73, 77, 0));
    }
    to {
        box-shadow: 0 0 0 0.8em rgba(1, 12, 60, 0);
        box-shadow: 0 0 0 0.8em var(--primary-color-dark-5_fade-0, rgba(1, 12, 60, 0));
        box-shadow: 0 0 0 0.8em rgba(73, 73, 77, 0);
        box-shadow: 0 0 0 0.8em var(--secondary-color-dark-5_fade-0, rgba(73, 73, 77, 0));
    }
}

.nep-button-secondary:active,
.nep-button-secondary:focus,
.nep-button-secondary:hover {
    border-color: #55565a;
    border-color: var(--secondary-color, #55565a);
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

app-change-confirm-growth .nep-button-secondary:focus{
    border-color: var(--primary-color, #4061C7) !important;
}

.nep-button-secondary:active {
    animation: btn-focus-secondary 0.4s ease-out;
    background-color: #fff;
    background-image: none;
}

.nep-button-secondary[disabled]:focus,
.nep-button-secondary[disabled]:hover,
fieldset[disabled] .nep-button-secondary:focus,
fieldset[disabled] .nep-button-secondary:hover {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
    background-color: #fff;
}

.nep-button-secondary[disabled]:active,
fieldset[disabled] .nep-button-secondary:active {
    animation: none;
}

.nep-button-secondary:hover {
    color: #031b87;
    color: var(--primary-color-dark-btn-hover, #031b87);
    background: #fff;
    border-color: #031b87;
    border-color: var(--primary-color-dark-btn-hover, #031b87);
}

.nep-button-secondary.nep-button-outline {
    border-color: #55565a;
    border-color: var(--secondary-color, #55565a);
    background: transparent;
    color: #55565a;
    color: var(--secondary-color, #55565a);
}

.nep-button-secondary.nep-button-outline:active,
.nep-button-secondary.nep-button-outline:focus,
.nep-button-secondary.nep-button-outline:hover {
    background-color: #55565a;
    background-color: var(--secondary-color, #55565a);
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-button-secondary.nep-button-outline:active {
    animation: btn-focus-secondary 0.4s ease-out;
}

.nep-button-secondary.nep-button-outline[disabled]:focus,
.nep-button-secondary.nep-button-outline[disabled]:hover,
fieldset[disabled] .nep-button-secondary.nep-button-outline:focus,
fieldset[disabled] .nep-button-secondary.nep-button-outline:hover {
    border-color: #55565a;
    border-color: var(--secondary-color, #55565a);
    background: transparent;
    color: #55565a;
    color: var(--secondary-color, #55565a);
}

.nep-button-secondary.nep-button-outline[disabled]:active,
fieldset[disabled] .nep-button-secondary.nep-button-outline:active {
    animation: none;
}

.nep-button-secondary.nep-button-outline:hover {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
    background: #fff;
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
}

.nep-button-secondary[disabled]:hover {
    color: #ababab;
    color: var(--gray-500, #ababab);
}

.nep-button-success {
    border-color: transparent;
    background-color: #388e3c;
    color: #fff;
    transition: background 0.15s ease-in-out;
}

@keyframes btn-focus-success {
    0% {
        box-shadow: 0 0 0 0 rgba(49, 124, 52, 0.6);
        box-shadow: 0 0 0 0 var(--success-color-dark-5_fade-60, rgba(49, 124, 52, 0.6));
    }
    50% {
        box-shadow: 0 0 0 0.4em rgba(49, 124, 52, 0);
        box-shadow: 0 0 0 0.4em var(--success-color-dark-5_fade-0, rgba(49, 124, 52, 0));
    }
    to {
        box-shadow: 0 0 0 0.8em rgba(49, 124, 52, 0);
        box-shadow: 0 0 0 0.8em var(--success-color-dark-5_fade-0, rgba(49, 124, 52, 0));
    }
}

.nep-button-success:focus,
.nep-button-success:hover {
    border-color: #388e3c;
    border-color: var(--success-color, #388e3c);
    background-color: #46b34b;
    color: #fff;
}

.nep-button-success:active {
    border-color: #388e3c;
    border-color: var(--success-color, #388e3c);
    animation: btn-focus-success 0.4s ease-out;
    background-color: #2a692d;
    color: #fff;
    background-image: none;
}

.nep-button-success[disabled]:focus,
.nep-button-success[disabled]:hover,
fieldset[disabled] .nep-button-success:focus,
fieldset[disabled] .nep-button-success:hover {
    border-color: transparent;
    background-color: #388e3c;
}

.nep-button-success[disabled]:active,
fieldset[disabled] .nep-button-success:active {
    animation: none;
}

.nep-button-success.nep-button-outline {
    border-color: #388e3c;
    border-color: var(--success-color, #388e3c);
    background: transparent;
    color: #388e3c;
    color: var(--success-color, #388e3c);
}

.nep-button-success.nep-button-outline:focus,
.nep-button-success.nep-button-outline:hover {
    background-color: #388e3c;
    color: #fff;
}

.nep-button-success.nep-button-outline:active {
    animation: btn-focus-success 0.4s ease-out;
    background-color: #388e3c;
    color: #fff;
}

.nep-button-success.nep-button-outline[disabled]:focus,
.nep-button-success.nep-button-outline[disabled]:hover,
fieldset[disabled] .nep-button-success.nep-button-outline:focus,
fieldset[disabled] .nep-button-success.nep-button-outline:hover {
    border-color: #388e3c;
    border-color: var(--success-color, #388e3c);
    background: transparent;
    color: #388e3c;
    color: var(--success-color, #388e3c);
}

.nep-button-success.nep-button-outline[disabled]:active,
fieldset[disabled] .nep-button-success.nep-button-outline:active {
    animation: none;
}

.nep-button-accent {
    border-color: transparent;
    background-color: #388e3c;
    color: #fff;
    transition: background 0.15s ease-in-out;
}

@keyframes btn-focus-accent {
    0% {
        box-shadow: 0 0 0 0 rgba(49, 124, 52, 0.6);
        box-shadow: 0 0 0 0 var(--success-color-dark-5_fade-60, rgba(49, 124, 52, 0.6));
    }
    50% {
        box-shadow: 0 0 0 0.4em rgba(49, 124, 52, 0);
        box-shadow: 0 0 0 0.4em var(--success-color-dark-5_fade-0, rgba(49, 124, 52, 0));
    }
    to {
        box-shadow: 0 0 0 0.8em rgba(49, 124, 52, 0);
        box-shadow: 0 0 0 0.8em var(--success-color-dark-5_fade-0, rgba(49, 124, 52, 0));
    }
}

.nep-button-accent:focus,
.nep-button-accent:hover {
    border-color: #388e3c;
    border-color: var(--success-color, #388e3c);
    background-color: #46b34b;
    color: #fff;
}

.nep-button-accent:active {
    border-color: #388e3c;
    border-color: var(--success-color, #388e3c);
    animation: btn-focus-accent 0.4s ease-out;
    background-color: #2a692d;
    color: #fff;
    background-image: none;
}

.nep-button-accent[disabled]:focus,
.nep-button-accent[disabled]:hover,
fieldset[disabled] .nep-button-accent:focus,
fieldset[disabled] .nep-button-accent:hover {
    border-color: transparent;
    background-color: #388e3c;
}

.nep-button-accent[disabled]:active,
fieldset[disabled] .nep-button-accent:active {
    animation: none;
}

.nep-button-accent.nep-button-outline {
    border-color: #388e3c;
    border-color: var(--success-color, #388e3c);
    background: transparent;
    color: #388e3c;
    color: var(--success-color, #388e3c);
}

.nep-button-accent.nep-button-outline:focus,
.nep-button-accent.nep-button-outline:hover {
    background-color: #388e3c;
    color: #fff;
}

.nep-button-accent.nep-button-outline:active {
    animation: btn-focus-accent 0.4s ease-out;
    background-color: #388e3c;
    color: #fff;
}

.nep-button-accent.nep-button-outline[disabled]:focus,
.nep-button-accent.nep-button-outline[disabled]:hover,
fieldset[disabled] .nep-button-accent.nep-button-outline:focus,
fieldset[disabled] .nep-button-accent.nep-button-outline:hover {
    border-color: #388e3c;
    border-color: var(--success-color, #388e3c);
    background: transparent;
    color: #388e3c;
    color: var(--success-color, #388e3c);
}

.nep-button-accent.nep-button-outline[disabled]:active,
fieldset[disabled] .nep-button-accent.nep-button-outline:active {
    animation: none;
}

.nep-button-info {
    border-color: transparent;
    background-color: #00acc1;
    background-color: var(--info-color, #00acc1);
    color: #fff;
    transition: background 0.15s ease-in-out;
}

@keyframes btn-focus-info {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 149, 168, 0.6);
        box-shadow: 0 0 0 0 var(--info-color-dark-5_fade-60, rgba(0, 149, 168, 0.6));
    }
    50% {
        box-shadow: 0 0 0 0.4em rgba(0, 149, 168, 0);
        box-shadow: 0 0 0 0.4em var(--info-color-dark-5_fade-0, rgba(0, 149, 168, 0));
    }
    to {
        box-shadow: 0 0 0 0.8em rgba(0, 149, 168, 0);
        box-shadow: 0 0 0 0.8em var(--info-color-dark-5_fade-0, rgba(0, 149, 168, 0));
    }
}

.nep-button-info:active,
.nep-button-info:focus,
.nep-button-info:hover {
    border-color: #00acc1;
    border-color: var(--info-color, #00acc1);
    background-color: #007f8e;
    background-color: var(--info-color-dark-btn-hover, #007f8e);
    color: #fff;
}

.nep-button-info:active {
    animation: btn-focus-info 0.4s ease-out;
    background-image: none;
}

.nep-button-info[disabled]:focus,
.nep-button-info[disabled]:hover,
fieldset[disabled] .nep-button-info:focus,
fieldset[disabled] .nep-button-info:hover {
    border-color: transparent;
    background-color: #00acc1;
    background-color: var(--info-color, #00acc1);
}

.nep-button-info[disabled]:active,
fieldset[disabled] .nep-button-info:active {
    animation: none;
}

.nep-button-info.nep-button-outline {
    border-color: #00acc1;
    border-color: var(--info-color, #00acc1);
    background: transparent;
    color: #00acc1;
    color: var(--info-color, #00acc1);
}

.nep-button-info.nep-button-outline:active,
.nep-button-info.nep-button-outline:focus,
.nep-button-info.nep-button-outline:hover {
    background-color: #00acc1;
    background-color: var(--info-color, #00acc1);
    color: #fff;
}

.nep-button-info.nep-button-outline:active {
    animation: btn-focus-info 0.4s ease-out;
}

.nep-button-info.nep-button-outline[disabled]:focus,
.nep-button-info.nep-button-outline[disabled]:hover,
fieldset[disabled] .nep-button-info.nep-button-outline:focus,
fieldset[disabled] .nep-button-info.nep-button-outline:hover {
    border-color: #00acc1;
    border-color: var(--info-color, #00acc1);
    background: transparent;
    color: #00acc1;
    color: var(--info-color, #00acc1);
}

.nep-button-info.nep-button-outline[disabled]:active,
fieldset[disabled] .nep-button-info.nep-button-outline:active {
    animation: none;
}

.nep-button-warning {
    border-color: transparent;
    background-color: #c68700;
    color: #fff;
    transition: background 0.15s ease-in-out;
}

@keyframes btn-focus-warning {
    0% {
        box-shadow: 0 0 0 0 rgba(173, 118, 0, 0.6);
        box-shadow: 0 0 0 0 var(--warning-color-dark-5_fade-60, rgba(173, 118, 0, 0.6));
    }
    50% {
        box-shadow: 0 0 0 0.4em rgba(173, 118, 0, 0);
        box-shadow: 0 0 0 0.4em var(--warning-color-dark-5_fade-0, rgba(173, 118, 0, 0));
    }
    to {
        box-shadow: 0 0 0 0.8em rgba(173, 118, 0, 0);
        box-shadow: 0 0 0 0.8em var(--warning-color-dark-5_fade-0, rgba(173, 118, 0, 0));
    }
}

.nep-button-warning:focus,
.nep-button-warning:hover {
    border-color: #c68700;
    border-color: var(--warning-color, #c68700);
    background-color: #f9aa00;
    color: #fff;
}

.nep-button-warning:active {
    border-color: #c68700;
    border-color: var(--warning-color, #c68700);
    animation: btn-focus-warning 0.4s ease-out;
    background-color: #936400;
    background-color: var(--warning-color-dark-btn-hover, #936400);
    color: #fff;
    background-image: none;
}

.nep-button-warning[disabled]:focus,
.nep-button-warning[disabled]:hover,
fieldset[disabled] .nep-button-warning:focus,
fieldset[disabled] .nep-button-warning:hover {
    border-color: transparent;
    background-color: #c68700;
}

.nep-button-warning[disabled]:active,
fieldset[disabled] .nep-button-warning:active {
    animation: none;
}

.nep-button-warning.nep-button-outline {
    border-color: #c68700;
    border-color: var(--warning-color, #c68700);
    background: transparent;
    color: #c68700;
    color: var(--warning-color, #c68700);
}

.nep-button-warning.nep-button-outline:focus,
.nep-button-warning.nep-button-outline:hover {
    background-color: #c68700;
    color: #fff;
}

.nep-button-warning.nep-button-outline:active {
    animation: btn-focus-warning 0.4s ease-out;
    background-color: #c68700;
    color: #fff;
}

.nep-button-warning.nep-button-outline[disabled]:focus,
.nep-button-warning.nep-button-outline[disabled]:hover,
fieldset[disabled] .nep-button-warning.nep-button-outline:focus,
fieldset[disabled] .nep-button-warning.nep-button-outline:hover {
    border-color: #c68700;
    border-color: var(--warning-color, #c68700);
    background: transparent;
    color: #c68700;
    color: var(--warning-color, #c68700);
}

.nep-button-warning.nep-button-outline[disabled]:active,
fieldset[disabled] .nep-button-warning.nep-button-outline:active {
    animation: none;
}

.nep-button-danger,
.nep-button-error {
    border-color: transparent;
    background-color: #c62828;
    color: #fff;
    transition: background 0.15s ease-in-out;
}

@keyframes btn-focus-danger {
    0% {
        box-shadow: 0 0 0 0 rgba(162, 10, 41, 0.6);
        box-shadow: 0 0 0 0 var(--danger-color-dark-5_fade-60, rgba(162, 10, 41, 0.6));
    }
    50% {
        box-shadow: 0 0 0 0.4em rgba(162, 10, 41, 0);
        box-shadow: 0 0 0 0.4em var(--danger-color-dark-5_fade-0, rgba(162, 10, 41, 0));
    }
    to {
        box-shadow: 0 0 0 0.8em rgba(162, 10, 41, 0);
        box-shadow: 0 0 0 0.8em var(--danger-color-dark-5_fade-0, rgba(162, 10, 41, 0));
    }
}

.nep-button-danger:focus,
.nep-button-danger:hover,
.nep-button-error:focus,
.nep-button-error:hover {
    border-color: #ba0c2f;
    border-color: var(--danger-color, #ba0c2f);
    background-color: #da4747;
    color: #fff;
}

.nep-button-danger:active,
.nep-button-error:active {
    border-color: #ba0c2f;
    border-color: var(--danger-color, #ba0c2f);
    animation: btn-focus-danger 0.4s ease-out;
    background-color: #8a0923;
    background-color: var(--danger-color-dark-btn-hover, #8a0923);
    color: #fff;
    background-image: none;
}

.nep-button-danger[disabled]:focus,
.nep-button-danger[disabled]:hover,
.nep-button-error[disabled]:focus,
.nep-button-error[disabled]:hover,
fieldset[disabled] .nep-button-danger:focus,
fieldset[disabled] .nep-button-danger:hover,
fieldset[disabled] .nep-button-error:focus,
fieldset[disabled] .nep-button-error:hover {
    border-color: transparent;
    background-color: #c62828;
}

.nep-button-danger[disabled]:active,
.nep-button-error[disabled]:active,
fieldset[disabled] .nep-button-danger:active,
fieldset[disabled] .nep-button-error:active {
    animation: none;
}

.nep-button-danger.nep-button-outline,
.nep-button-error.nep-button-outline {
    border-color: #ba0c2f;
    border-color: var(--danger-color, #ba0c2f);
    background: transparent;
    color: #ba0c2f;
    color: var(--danger-color, #ba0c2f);
}

.nep-button-danger.nep-button-outline:focus,
.nep-button-danger.nep-button-outline:hover,
.nep-button-error.nep-button-outline:focus,
.nep-button-error.nep-button-outline:hover {
    background-color: #c62828;
    color: #fff;
}

.nep-button-danger.nep-button-outline:active,
.nep-button-error.nep-button-outline:active {
    animation: btn-focus-danger 0.4s ease-out;
    background-color: #c62828;
    color: #fff;
}

.nep-button-danger.nep-button-outline[disabled]:focus,
.nep-button-danger.nep-button-outline[disabled]:hover,
.nep-button-error.nep-button-outline[disabled]:focus,
.nep-button-error.nep-button-outline[disabled]:hover,
fieldset[disabled] .nep-button-danger.nep-button-outline:focus,
fieldset[disabled] .nep-button-danger.nep-button-outline:hover,
fieldset[disabled] .nep-button-error.nep-button-outline:focus,
fieldset[disabled] .nep-button-error.nep-button-outline:hover {
    border-color: #ba0c2f;
    border-color: var(--danger-color, #ba0c2f);
    background: transparent;
    color: #ba0c2f;
    color: var(--danger-color, #ba0c2f);
}

.nep-button-danger.nep-button-outline[disabled]:active,
.nep-button-error.nep-button-outline[disabled]:active,
fieldset[disabled] .nep-button-danger.nep-button-outline:active,
fieldset[disabled] .nep-button-error.nep-button-outline:active {
    animation: none;
}

.nep-button-link {
    border-radius: 0;
    font-weight: 400;
    color: #00acc1;
}

.nep-button-link,
.nep-button-link:active,
.nep-button-link[disabled],
fieldset[disabled] .nep-button-link {
    background-color: transparent;
    box-shadow: none;
}

.nep-button-link,
.nep-button-link:active,
.nep-button-link:focus,
.nep-button-link[disabled]:hover {
    border-color: transparent;
}

.nep-button-link:focus,
.nep-button-link[disabled]:hover {
    background-color: transparent;
}

.nep-button-link:focus,
.nep-button-link:hover {
    text-decoration: underline;
    color: #00acc1;
}

.nep-button-link[disabled]:focus,
.nep-button-link[disabled][disabled]:hover,
fieldset[disabled] .nep-button-link:focus,
fieldset[disabled] .nep-button-link[disabled]:hover {
    text-decoration: none;
}

.nep-button-large {
    padding: var(--button-padding-large-vertical, 8px) var(--button-padding-large-horizontal, 20px);
    border-radius: 4px;
    font-size: 17px;
    font-size: var(--button-font-size-large, 17px);
    line-height: 1.42857143;
    height: 42px;
}

.nep-button-small {
    padding: var(--button-padding-small-vertical, 2px) var(--button-padding-small-horizontal, 12px);
    border-radius: 2px;
    font-size: 12px;
    font-size: var(--button-font-size-small, 12px);
    line-height: 1.5;
    height: 26px;
}

.nep-button-group .nep-button {
    position: relative;
    margin-left: 0;
    border-right-width: 0;
    border-left-width: 0;
}

.nep-button-group .nep-button:before {
    position: absolute;
    top: 50%;
    left: 0;
    height: 50%;
    border-left: 1px solid hsla(0, 0%, 100%, 0.4);
    content: " ";
    transform: translateY(-50%);
}

.nep-button-group .nep-button:first-child {
    margin-left: 0;
    border-left-width: 1px;
}

.nep-button-group .nep-button:first-child:before {
    display: none;
}

.nep-button-group .nep-button:last-child {
    border-right-width: 1px;
}

.nep-button-group .nep-button:not(:first-child):not(:last-child) {
    border-radius: 0;
}

.nep-button-group .nep-button:first-child:not(:last-child) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
}

.nep-button-group .nep-button:last-child:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
}

.nep-button-group.nep-button-outline .nep-button:before {
    border-left-color: inherit;
}

.nep-dropdown {
    position: relative;
    display: inline-block;
}

.nep-dropdown:focus {
    outline: none;
}

.nep-dropdown .nep-dropdown-button span.nep-dropdown-caret:after {
    vertical-align: middle;
}

.nep-dropdown-menu {
    position: absolute;
    z-index: 1000;
    min-width: 100%;
    padding: 0;
    background: #fff;
    background-clip: padding-box;
    border-radius: 4px;
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.02), 0 6px 12px rgba(0, 0, 0, 0.175);
}

.nep-dropdown-menu hr {
    height: 0;
    margin: 8px 0;
    border-width: 0;
    border-top: 1px solid #e0e0e0;
}

.nep-dropdown-menu .nep-dropdown-button {
    display: flex;
    align-items: center;
}

.nep-dropdown .nep-dropdown-button-content {
    display: inline-block;
    flex: 1;
}

.nep-dropdown-bottom-left>.nep-dropdown-menu,
.nep-dropdown-bottom-right>.nep-dropdown-menu {
    top: 100%;
    margin-top: 2px;
}

.nep-dropdown-bottom-left>.nep-dropdown-button .nep-dropdown-caret:after,
.nep-dropdown-bottom-right>.nep-dropdown-button .nep-dropdown-caret:after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 4px;
    content: "";
    border-top: 4px solid;
    border-right: 4px solid transparent;
    border-bottom: 0;
    border-left: 4px solid transparent;
    vertical-align: 3.4px;
}

.nep-dropdown-bottom-left>.nep-dropdown-menu,
.nep-dropdown-top-left>.nep-dropdown-menu {
    left: 0;
}

.nep-dropdown-bottom-right>.nep-dropdown-menu,
.nep-dropdown-top-right>.nep-dropdown-menu {
    right: 0;
}

.nep-dropdown-top-left>.nep-dropdown-menu,
.nep-dropdown-top-right>.nep-dropdown-menu {
    bottom: 100%;
    margin-bottom: 2px;
}

.nep-dropdown-top-left>.nep-dropdown-button .nep-dropdown-caret:after,
.nep-dropdown-top-right>.nep-dropdown-button .nep-dropdown-caret:after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 4px;
    content: "";
    border-top: 0;
    border-right: 4px solid transparent;
    border-bottom: 4px solid;
    border-left: 4px solid transparent;
    vertical-align: 3.4px;
}

.nep-dropdown-left-bottom>.nep-dropdown-menu,
.nep-dropdown-right-bottom>.nep-dropdown-menu {
    bottom: 0;
}

.nep-dropdown-left-bottom>.nep-dropdown-menu,
.nep-dropdown-left-top>.nep-dropdown-menu {
    right: 100%;
    margin-right: 2px;
}

.nep-dropdown-left-bottom>.nep-dropdown-button:before,
.nep-dropdown-left-top>.nep-dropdown-button:before {
    display: inline-block;
    width: 0;
    height: 0;
    margin-right: 4px;
    border-top: 4px solid transparent;
    border-right: 4px solid;
    border-bottom: 4px solid transparent;
    content: "";
}

.nep-dropdown-left-top>.nep-dropdown-menu,
.nep-dropdown-right-top>.nep-dropdown-menu {
    top: 0;
}

.nep-dropdown-right-bottom>.nep-dropdown-menu,
.nep-dropdown-right-top>.nep-dropdown-menu {
    left: 100%;
    margin-left: 2px;
}

.nep-dropdown-right-bottom>.nep-dropdown-button:after,
.nep-dropdown-right-top>.nep-dropdown-button:after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 4px;
    content: "";
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-left: 4px solid;
}

.nep-dropdown-item {
    display: block;
    padding: 8px 16px;
    color: #55565a;
    color: var(--gray-800, #55565a);
    line-height: 1.42857143;
    text-decoration: none;
}

a.nep-dropdown-item[disabled] {
    color: #cac9c7;
    color: var(--gray-400, #cac9c7);
    cursor: not-allowed;
}

.nep-dropdown-item:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.nep-dropdown-item:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

.nep-dropdown-item.nep-dropdown-active,
.nep-dropdown-item:hover {
    background-color: #f5f5f5;
    color: #49494d;
    color: var(--gray-800-darken-5, #49494d);
}

.nep-dropdown-item:focus {
    text-decoration: none;
}

.nep-dropdown-no-width {
    white-space: nowrap;
}

.nep-dropdown-split.nep-dropdown-button {
    padding-right: 11.2px;
    padding-right: var(--button-padding-base-horizontal-7, 11.2px);
    padding-left: 11.2px;
    padding-left: var(--button-padding-base-horizontal-7, 11.2px);
}

.nep-dropdown-split.nep-dropdown-button:after,
.nep-dropdown-split.nep-dropdown-button:before {
    margin-right: 0;
    margin-left: 0;
}

.nep-dropdown-split-dropdown {
    margin-left: -1px;
}

.nep-dropdown-split-dropdown .nep-dropdown-split-button {
    padding-right: 8px;
    padding-left: 8px;
    border-radius: 0 4px 4px 0 !important;
}

.nep-dropdown-split-dropdown .nep-dropdown-split-button .nep-dropdown-button-content {
    width: 0;
}

.nep-dropdown-split-dropdown .nep-dropdown-split-button .nep-dropdown-caret:after,
.nep-dropdown-split-dropdown .nep-dropdown-split-button:after {
    margin-left: 0;
}

.nep-hidable-fade-animation-240 {
    transition: height 0.24s ease-in-out, opacity 0.24s ease-in-out;
}

.nep-hidable-fade-animation-360 {
    transition: height 0.36s ease-in-out, opacity 0.36s ease-in-out;
}

.nep-hidable-fade-animation-480 {
    transition: height 0.48s ease-in-out, opacity 0.48s ease-in-out;
}

.nep-hidable-animation-240 {
    transition: height 0.24s ease-in-out, opacity 0.24s ease-in-out, transform 0.24s ease-in-out;
}

.nep-hidable-animation-360 {
    transition: height 0.36s ease-in-out, opacity 0.36s ease-in-out, transform 0.36s ease-in-out;
}

.nep-hidable-animation-480 {
    transition: height 0.48s ease-in-out, opacity 0.48s ease-in-out, transform 0.48s ease-in-out;
}

.nep-hidable-scale-y {
    transform: scaleY(0);
}

.nep-hidable-scale-y.nep-hidable-show {
    transform: scaleY(1);
}

.nep-hidable-collapse-fade,
.nep-hidable-fade {
    opacity: 0;
}

.nep-hidable-collapse-fade.nep-hidable-show,
.nep-hidable-fade.nep-hidable-show {
    opacity: 1;
}

.nep-list {
    display: block;
}

.nep-list-absolute-wrapper {
    position: absolute;
    top: 0;
    left: 0;
}

.nep-message {
    position: fixed;
    z-index: 1060;
    left: 50%;
    max-width: 50%;
}

@keyframes nep-message-left-in {
    0% {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes nep-message-right-in {
    0% {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

.nep-message-top {
    top: 20px;
    transform: translateX(-50%);
}

.nep-message-middle {
    top: 50%;
    transform: translate(-50%, -50%);
}

.nep-message-item {
    display: flex;
}

.nep-message-msg {
    display: inline-flex;
    margin: 0 auto 20px;
    border-color: #f0f0f1;
    border-color: var(--gray-200, #f0f0f1);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    box-shadow: var(--message-box-shadow, 0 6px 12px rgba(0, 0, 0, 0.175));
    color: $nep-black;
    color: var(--message-text-color, $nep-black);
    border-radius: 0 0 8px 8px;
}

.nep-message-top-bottom,
.nep-message-top-right {
    right: 20px;
    left: auto;
}

.nep-message-top-bottom .nep-message-msg,
.nep-message-top-right .nep-message-msg {
    min-width: 340px;
    padding: 20px;
    animation: nep-message-right-in 0.216s ease-out;
}

.nep-message-top-left,
.nep-message-top-right {
    top: 20px;
}

.nep-message-bottom-left,
.nep-message-bottom-right {
    top: auto;
    bottom: 0;
}

.nep-message-bottom-right,
.nep-message-top-right {
    right: 20px;
    left: auto;
}

.nep-message-bottom-right .nep-message-msg,
.nep-message-top-right .nep-message-msg {
    min-width: 340px;
    padding: 20px;
    animation: nep-message-right-in 0.216s ease-out;
}

.nep-message-bottom-left,
.nep-message-top-left {
    left: 20px;
}

.nep-message-bottom-left .nep-message-msg,
.nep-message-top-left .nep-message-msg {
    min-width: 340px;
    padding: 20px;
    animation: nep-message-left-in 0.216s ease-out;
}

.nep-table {
    border-radius: 0 var(--table-border-radius-top, 0) 0 0;
    border-radius: var(--table-border-radius-top, 0) var(--table-border-radius-top, 0) 0 0;
    position: relative;
    display: flex;
    overflow: auto;
    max-width: 100%;
    flex-direction: column;
    margin-bottom: 20px;
}

.nep-table table {
    min-width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    color: $gray-200;
    color: var(--table-color, $gray-200);
    table-layout: fixed;
}

.nep-table table>tbody>tr>td,
.nep-table table>tbody>tr>th,
.nep-table table>tfoot>tr>td,
.nep-table table>tfoot>tr>th,
.nep-table table>thead>tr>td,
.nep-table table>thead>tr>th {
    position: relative;
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    border-bottom: 1px solid var(--table-border-color, #e0e0e0);
    background-clip: padding-box;
    line-height: 1.42857143;
    word-break: break-all;
}

.nep-table table>thead>tr>th {
    border-bottom-width: 0;
    background-color: #fafafb;
    background-color: var(--table-head-bg, #fafafb);
    color: var(--table-head-color, #55565a);
    color: var(--table-head-color, var(--secondary-color, #55565a));
    vertical-align: middle;
    font-weight: 700;
    font-weight: var(--table-head-font-weight, bold);
}

.nep-table table>tbody+tbody,
.nep-table table>thead+tbody>tr:first-child>td {
    border-top: 2px solid #e0e0e0;
    border-top: 2px solid var(--table-border-color, #e0e0e0);
}

.nep-table table table {
    background-color: #fff;
}

.nep-table table>tbody>tr {
    position: relative;
}

table tr .nep-table-checkbox {
    width: 48px;
    padding-right: 0;
    padding-left: 0;
    text-align: center;
}

table tr .nep-table-checkbox label {
    margin-right: 0;
}

table tr .nep-table-checkbox i {
    vertical-align: top;
}

.nep-table caption {
    padding-top: 12px;
    padding-bottom: 12px;
    color: #ababab;
    color: var(--gray-500, #ababab);
    text-align: left;
}

.nep-table th {
    text-align: left;
}

.nep-table-vertical-top td {
    vertical-align: top;
}

.nep-table-vertical-middle td {
    vertical-align: middle;
}

.nep-table-sorter-asc,
.nep-table-sorter-desc {
    position: absolute;
    right: 0;
    width: 18px;
    color: transparent;
}

.nep-table-sorter-asc:active,
.nep-table-sorter-asc:focus,
.nep-table-sorter-desc:active,
.nep-table-sorter-desc:focus {
    color: transparent;
}

.nep-table-sorter-asc:after,
.nep-table-sorter-desc:after {
    position: absolute;
    right: 5px;
    border: solid transparent;
    content: " ";
}

.nep-table-sorter-asc {
    top: 0;
    bottom: 50%;
}

.nep-table-sorter-asc:after {
    bottom: 2px;
    border-width: 0 4px 6px;
    border-bottom-color: #ababab;
    border-bottom-color: var(--gray-500, #ababab);
}

.nep-table-sorter-asc:hover:after {
    border-bottom-color: #75787b;
    border-bottom-color: var(--gray-700, #75787b);
}

.nep-table-sorter-desc {
    top: 50%;
    bottom: 0;
}

.nep-table-sorter-desc:after {
    top: 2px;
    border-width: 6px 4px 0;
    border-top-color: #ababab;
    border-top-color: var(--gray-500, #ababab);
}

.nep-table-sorter-desc:hover:after {
    border-top-color: #75787b;
    border-top-color: var(--gray-700, #75787b);
}

.nep-table-sorter-active.nep-table-sorter-asc:after {
    border-bottom-color: #4061C7;
    border-bottom-color: var(--primary-color, #4061C7);
}

.nep-table-sorter-active.nep-table-sorter-desc:after {
    border-top-color: #4061C7;
    border-top-color: var(--primary-color, #4061C7);
}

th.nep-table-center {
    text-align: center;
}

table>thead>tr>th.nep-table-condensed {
    padding: 4px 12px;
}

.nep-table-hover table>tbody>tr:hover>td {
    background-color: #f5f5f5;
    background-color: var(--table-hover-bg, #f5f5f5);
}

.nep-table-normal td {
    background-color: #fff;
}

.nep-table-small table>tbody>tr>td,
.nep-table-small table>tbody>tr>th,
.nep-table-small table>tfoot>tr>td,
.nep-table-small table>tfoot>tr>th,
.nep-table-small table>thead>tr>td,
.nep-table-small table>thead>tr>th {
    padding: 4px 12px;
}

.nep-table-bordered {
    position: relative;
    border: 1px solid #e0e0e0;
    border: 1px solid var(--table-border-color, #e0e0e0);
}

.nep-table-bordered .nep-table-ignore-right-border,
.nep-table-bordered nep-table-ignore-bottom-border {
    border-right: none;
}

.nep-table-bordered .nep-table-ignore-right-border:after,
.nep-table-bordered nep-table-ignore-bottom-border:after {
    display: none;
}

.nep-table-bordered .nep-table-head table {
    border-bottom-width: 1px;
}

.nep-table-bordered table>tbody>tr>td,
.nep-table-bordered table>tbody>tr>th,
.nep-table-bordered table>tfoot>tr>td,
.nep-table-bordered table>tfoot>tr>th,
.nep-table-bordered table>thead>tr>td,
.nep-table-bordered table>thead>tr>th {
    border-right: 1px solid #e0e0e0;
    border-right: 1px solid var(--table-border-color, #e0e0e0);
    border-bottom-width: 1px;
}

.nep-table-bordered table>tbody>tr>td.nep-table-ignore-bottom-border {
    border-bottom: none;
}

.nep-table-bordered table>thead+tbody>tr:first-child>td {
    border-top-width: 1px;
}

.nep-table-bordered .nep-table-body table {
    border-top-width: 0;
}

.nep-table-bordered .nep-table-float-right .nep-table-float-left:after,
.nep-table-bordered .nep-table-float-right .nep-table-float-left:before {
    bottom: -1px;
}

.nep-table-bordered .nep-table-fixed-left,
.nep-table-bordered .nep-table-fixed-right {
    border-right: 0;
    z-index: 1;
}

.nep-table-bordered .nep-table-fixed-left:after,
.nep-table-bordered .nep-table-fixed-right:after {
    bottom: -1px;
    position: absolute;
    content: " ";
    background: #e0e0e0;
    background: var(--table-border-color, #e0e0e0);
    height: 100%;
    width: 1px;
    top: 0;
    right: 0;
}

table>tbody>tr.nep-table-even>td {
    background-color: #f5f5f5;
}

.nep-table-head {
    position: relative;
    overflow: hidden;
    flex: 0 0 auto;
    background: #fafafb;
    background: var(--table-head-bg, #fafafb);
}

.nep-table-head>table {
    border-bottom: 2px solid #e0e0e0;
    border-bottom: 2px solid var(--table-border-color, #e0e0e0);
}

.nep-table .nep-table-icon-tree-expand {
    margin-right: 8px;
    display: inline-block;
    width: 17px;
    height: 17px;
    line-height: 14px;
    text-align: center;
    background: #fff;
    border: 1px solid #e8e8e8;
    cursor: pointer;
    user-select: none;
}

.nep-table .nep-table-icon-tree-expand.nep-table-icon-tree-plus:after {
    content: "+";
}

.nep-table .nep-table-icon-tree-expand.nep-table-icon-tree-sub:after {
    content: "-";
}

.nep-table-icon-expand-plus,
.nep-table-icon-expand-sub {
    position: relative;
    display: block;
    width: 15px;
    height: 15px;
    margin: 2px 0;
    border: 1px solid $gray-200;
    border: 1px solid var(--gray-600, $gray-200);
    background: #fff;
    border-radius: 2px;
    cursor: pointer;
}

.nep-table-icon-expand-plus:after,
.nep-table-icon-expand-plus:before,
.nep-table-icon-expand-sub:after,
.nep-table-icon-expand-sub:before {
    position: absolute;
    top: 6px;
    left: 2px;
    width: 9px;
    height: 0;
    border-width: 0 0 1px;
    border-style: solid;
    border-color: inherit;
    content: " ";
}

.nep-table-icon-expand-plus:hover,
.nep-table-icon-expand-sub:hover {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
}

.nep-table-icon-expand-plus:hover:after,
.nep-table-icon-expand-plus:hover:before,
.nep-table-icon-expand-sub:hover:after,
.nep-table-icon-expand-sub:hover:before {
    background: #4061C7;
    background: var(--primary-color, #4061C7);
}

.nep-table-icon-expand-plus:after {
    top: 2px;
    left: 6px;
    width: 0;
    height: 9px;
    border-width: 0 1px 0 0;
}

.nep-table-scroll-y.nep-table-head {
    padding-right: 16px;
}

.nep-table-scroll-y.nep-table-head:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 16px;
    border-bottom: 2px solid #e0e0e0;
    border-bottom: 2px solid var(--table-border-color, #e0e0e0);
    background-color: #fafafb;
    background-color: var(--table-head-bg, #fafafb);
    content: " ";
}

.nep-table-body {
    min-width: 100%;
    flex: 1;
}

.nep-table-scroll-inner {
    min-width: 100%;
}

.nep-table-float-left .nep-table-fixed-left,
.nep-table-float-right .nep-table-fixed-right {
    position: relative;
    z-index: 100;
}

.nep-table-float-left .nep-table-fixed-last:after {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 100%;
    width: 6px;
    margin-left: 1px;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.15), transparent);
    content: " ";
}

.nep-table-float-right .nep-table-fixed-first:before {
    position: absolute;
    top: 0;
    right: 100%;
    bottom: 0;
    width: 6px;
    margin-right: 1px;
    background: linear-gradient(270deg, rgba(0, 0, 0, 0.15), transparent);
    content: " ";
}

.nep-table-fixed-right+.nep-table-fixed-right:before {
    width: 0;
}

.nep-table-loading {
    position: absolute;
    z-index: 11;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    overflow: hidden;
    background-color: hsla(0, 0%, 100%, 0.4);
}

.nep-table-empty {
    position: sticky;
    left: 0;
    display: flex;
    flex: 1;
    padding: 20px;
    color: #aaa;
    font-size: 16px;
    text-align: center;
}

.nep-table-empty span {
    display: block;
    margin: auto;
}

.nep-table-bordered .nep-table-empty {
    border-right: 1px solid #e0e0e0;
    border-right: 1px solid var(--table-border-color, #e0e0e0);
    border-bottom: 1px solid #e0e0e0;
    border-bottom: 1px solid var(--table-border-color, #e0e0e0);
}

.nep-table-fixed .nep-table-empty {
    border-right-width: 0;
}

td.nep-table-align-center,
th.nep-table-align-center {
    text-align: center;
}

td.nep-table-align-right,
th.nep-table-align-right {
    text-align: right;
}

td.nep-table-align-left,
th.nep-table-align-left {
    text-align: left;
}

.nep-table-resize-spanner {
    z-index: 12;
    position: absolute;
    right: -2px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #4061C7;
    background: var(--primary-color, #4061C7);
    opacity: 0;
    transition: opacity 0.3s;
    cursor: ew-resize;
}

.nep-table-resize-spanner:after,
.nep-table-resize-spanner:before {
    border-width: 3px;
    content: " ";
    position: absolute;
    width: 0;
    height: 0;
    margin: auto;
    bottom: 0;
    top: 0;
}

.nep-table-resize-spanner:after {
    border-style: dashed solid dashed dashed;
    border-color: transparent #4061C7 transparent transparent;
    border-right-color: var(--primary-color, #4061C7);
    border-top-color: transparent;
    right: 4px;
}

.nep-table-resize-spanner:before {
    border-style: dashed dashed dashed solid;
    border-color: transparent transparent transparent #4061C7;
    border-left-color: var(--primary-color, #4061C7);
    border-bottom-color: transparent;
    border-right-color: transparent;
    border-top-color: transparent;
    left: 4px;
}

.nep-table-resize thead tr th:last-child .nep-table-resize-spanner {
    display: none;
}

.nep-table-resize table.nep-table-resizing span.nep-table-resize-spanner {
    opacity: 0;
}

.nep-table-resize table th.nep-table-resizing-item .nep-table-resize-spanner,
.nep-table-resize th:hover .nep-table-resize-spanner {
    opacity: 1;
}

.nep-scroll,
.nep-scroll-inner {
    position: relative;
    overflow: hidden;
}

.nep-scroll-inner {
    z-index: 10;
    top: 0;
    left: 0;
}

.nep-scroll-iframe {
    position: absolute;
    z-index: 0;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
    background: transparent;
}

.nep-scroll-dragging {
    user-select: none;
}

.nep-scroll-bar {
    position: absolute;
    z-index: 90;
    border: 1px solid transparent;
    background: #fafafb;
}

.nep-scroll-bar.nep-scroll-dragging .nep-scroll-handle,
.nep-scroll-bar:hover .nep-scroll-handle {
    background: hsla(0, 0%, 49.8%, 0.9);
}

.nep-scroll-bar .nep-scroll-handle {
    position: absolute;
    z-index: 2;
    display: none;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    cursor: default;
}

.nep-scroll-bar .nep-scroll-handle:focus {
    background: hsla(0, 0%, 49.8%, 0.9);
}

.nep-scroll-bar.nep-scroll-y {
    top: 0;
    right: 0;
    bottom: 0;
    width: 16px;
    border-width: 0 1px;
}

.nep-scroll-bar.nep-scroll-y .nep-scroll-handle {
    left: 2px;
    width: 10px;
    min-height: 20px;
}

.nep-scroll-bar.nep-scroll-y.nep-scroll-padding-y:after {
    position: absolute;
    z-index: 1;
    top: 100%;
    left: -1px;
    width: 16px;
    height: 16px;
    background: #fafafb;
    content: " ";
}

.nep-scroll-bar.nep-scroll-x {
    right: 0;
    bottom: 0;
    left: 0;
    height: 16px;
    border-width: 1px 0;
}

.nep-scroll-bar.nep-scroll-x .nep-scroll-handle {
    top: 2px;
    min-width: 20px;
    height: 10px;
}

.nep-scroll-bar.nep-scroll-show .nep-scroll-handle {
    display: block;
}

.nep-scroll-show-x {
    padding-bottom: 16px;
}

.nep-scroll-show-x .nep-scroll-y {
    bottom: 16px;
}

.nep-scroll-show-y {
    padding-right: 16px;
}

.nep-scroll-show-x .nep-scroll-inner,
.nep-scroll-show-y .nep-scroll-x {
    right: 16px;
}

.nep-scroll-show-y .nep-scroll-inner {
    bottom: 16px;
}

.nep-pagination-section {
    display: inline-block;
    margin-right: 8px;
    vertical-align: middle;
}

.nep-pagination-section>span {
    color: $gray-200;
    color: var(--gray-600, $gray-200);
    line-height: 32px;
}

.nep-pagination-section:last-child {
    margin-right: 0;
}

.nep-pagination-center {
    text-align: center;
}

.nep-pagination-right {
    text-align: right;
}

.nep-pagination-small .nep-pagination-item {
    min-width: 26px;
    height: 26px;
    font-size: 12px;
    line-height: 26px;
}

.nep-pagination-large .nep-pagination-item {
    min-width: 42px;
    height: 42px;
    font-size: 17px;
    line-height: 42px;
}

.nep-pagination-item {
    position: relative;
    display: inline-block;
    min-width: 32px;
    height: 32px;
    padding: 0 2px;
    margin-right: 8px;
    border: 1px solid #e0e0e0;
    border: var(--pagination-border-width, 1px) solid #e0e0e0;
    background-color: #fff;
    border-radius: 4px;
    border-radius: var(--pagination-border-radius, 4px);
    color: $gray-200;
    color: var(--gray-600, $gray-200);
    line-height: 32px;
    text-align: center;
    text-decoration: none;
    user-select: none;
    vertical-align: top;
}

.nep-pagination-item:last-child {
    margin-right: 0;
}

.nep-pagination-item svg {
    width: 12px;
    height: 12px;
    fill: $gray-200;
    fill: var(--gray-600, $gray-200);
}

.nep-pagination-item[disabled] {
    color: #ababab;
    color: var(--gray-500, #ababab);
}

.nep-pagination-item[disabled] svg {
    fill: #ababab;
    fill: var(--gray-500, #ababab);
}

.nep-pagination-item:hover {
    border-color: #e0e0e0;
    border-color: var(--pagination-hover-border, #e0e0e0);
    background: #fafafb;
    background: var(--pagination-hover-bg, #fafafb);
    color: #4061C7;
    color: var(--pagination-hover-color, #4061C7);
}

.nep-pagination-item:hover svg {
    fill: #4061C7;
    fill: var(--pagination-hover-color, #4061C7);
}

.nep-pagination-item:active,
.nep-pagination-item:focus {
    text-decoration: none;
}

.nep-pagination-current[disabled] {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
    background: #4061C7;
    background: var(--primary-color, #4061C7);
    color: #fff;
}

.nep-pagination-more-left svg,
.nep-pagination-more-right svg {
    opacity: 0;
}

.nep-pagination-more-left:before,
.nep-pagination-more-right:before {
    position: absolute;
    top: 50%;
    left: 50%;
    content: "\2022\2022\2022";
    transform: translate(-50%, -50%);
}

.nep-pagination-more-left:hover svg,
.nep-pagination-more-right:hover svg {
    opacity: 1;
}

.nep-pagination-more-left:hover:before,
.nep-pagination-more-right:hover:before {
    display: none;
}

.nep-pagination-no-border,
.nep-pagination-no-border:hover {
    border-color: transparent;
    background: transparent;
}

div.nep-pagination-pagesize {
    display: inline-block;
    width: auto;
}

div.nep-pagination-pagesize .nep-select-result .nep-select-ellipsis {
    overflow: unset;
}

div.nep-pagination-pagesize .nep-select-options {
    width: auto;
}

.nep-breadcrumb {
    background-color: transparent;
    border-radius: 4px;
    font-size: 14px;
    list-style: none;
}

.nep-breadcrumb-separator {
    margin: 0 8px;
}

.nep-breadcrumb a,
.nep-breadcrumb b {
    display: inline-block;
}

.nep-breadcrumb a {
    color: #4061C7;
}

.nep-breadcrumb b {
    font-weight: 400;
}

.nep-breadcrumb a:hover {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-breadcrumb-down {
    display: inline-block;
    width: 12px;
    margin-left: 8px;
}

.nep-breadcrumb-down:after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 4px;
    content: "";
    border-top: 4px solid;
    border-right: 4px solid transparent;
    border-bottom: 0;
    border-left: 4px solid transparent;
    vertical-align: 3.4px;
    margin-left: 0;
}

.nep-breadcrumb-dropdown-item a {
    display: block;
    padding: 6px 20px;
    min-width: 100px;
}

.nep-breadcrumb-dropdown-item a:hover {
    background: rgba(0, 0, 0, 0.05);
}

.nep-icon {
    display: inline-block;
    font-size: inherit;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-style: normal;
    text-rendering: auto;
}

.nep-icon-small {
    font-size: 12px;
}

.nep-icon-large {
    font-size: 20px;
}

.nep-icon-primary {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-icon-success {
    color: #388e3c;
    color: var(--success-color, #388e3c);
}

.nep-icon-secondary {
    color: #55565a;
    color: var(--secondary-color, #55565a);
}

.nep-icon-info {
    color: #00acc1;
    color: var(--info-color, #00acc1);
}

.nep-icon-warning {
    color: #c68700;
    color: var(--warning-color, #c68700);
}

.nep-icon-danger {
    color: #ba0c2f;
    color: var(--danger-color, #ba0c2f);
}

.nep-menu {
    position: relative;
    z-index: 1000;
    height: 78%;
    background: #fff;
    line-height: 0;
    outline: none;
    overflow-x: scroll;
}

.nep-menu,
.nep-menu ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

.nep-menu-root {
    box-shadow: none;
}

.nep-menu-item a {
    display: block;
    color: #434655;
    color: var(--gray-900, #434655);
    white-space: nowrap;
    padding-left: 4px solid #4061C7;
}

.nep-menu-item a a {
    display: unset;
}

.nep-menu-item a:focus {
    text-decoration: none;
}

.nep-menu-item a:hover {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
    border-left: 4px solid #fff;
    padding-left: 16px;
}

.nep-menu-item.nep-menu-disabled a {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
}

.nep-menu-in-path>a {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-menu-title {
    position: relative;
    height: 36px;
    height: var(--menu-item-height, 36px);
    padding: 0 20px;
    line-height: 36px;
    line-height: var(--menu-item-height, 36px);
}

.nep-menu-has-children>.nep-menu-title {
    padding-right: 40px;
}

.nep-menu-has-children>.nep-menu-title:after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 4px;
    content: "";
    border-top: 4px solid;
    border-right: 4px solid transparent;
    border-bottom: 0;
    border-left: 4px solid transparent;
    vertical-align: 3.4px;
    position: absolute;
    top: 18px;
    top: var(--menu-item-height-half, 18px);
    left: 70%;
    transform: translateY(-65%) rotate(0deg);
}

.nep-menu-highlight>.nep-menu-title {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-menu-list {
    display: none;
}

.nep-menu-list .nep-menu-has-children.nep-menu-open>.nep-menu-title:after {
    transform: translateY(-65%) rotate(180deg);
}

.nep-menu-list .nep-menu-has-children.nep-menu-open>.nep-menu-list,
ul.nep-menu-root {
    display: block;
}

.nep-menu.nep-menu-inline {
    border-right: 1px solid #4061C7;
}

.nep-menu-inline.nep-menu-root {
    box-sizing: content-box;
}

.nep-menu-inline>.nep-menu-item {
    display: block;
}

.nep-menu-inline .nep-menu-active.nep-menu-no-children {
    padding: var(--menu-active-padding-vertical, 0) var(--menu-active-padding-horizontal, 0);
}

.nep-menu-inline .nep-menu-active.nep-menu-no-children>a {
    background: #4061C7;
    background: var(--menu-item-active-bg, #4061C7);
    color: #fff;
    color: var(--menu-item-active-color, #fff);
    border-radius: 0;
    border-radius: var(--menu-active-border-radius, 0);
    text-indent: 0;
    text-indent: var(--menu-active-padding-horizontal-negative, 0);
}

.nep-menu-inline .nep-menu-active.nep-menu-no-children>a:after {
    position: absolute;
    top: 0;
    right: 0;
    width: 3px;
    height: 100%;
    background: #4061C7;
    background: var(--primary-color, #4061C7);
    content: " ";
}

.nep-menu-inline .nep-menu-active.nep-menu-has-children>a {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-menu-horizontal {
    height: 40px;
}

.nep-menu-horizontal.nep-menu-root {
    border-bottom: 1px solid #4061C7;
    display: inline-block;
    white-space: nowrap;
    min-width: 100%;
}

.nep-menu-horizontal .nep-menu-wrapper {
    position: absolute;
    overflow: hidden;
    width: 100%;
}

.nep-menu-horizontal.nep-menu-has-open .nep-menu-wrapper {
    height: 100vh;
}

.nep-menu-horizontal:hover .nep-menu-bar {
    display: block;
}

.nep-menu-horizontal>.nep-scroll-bar {
    border: unset;
    background: transparent;
}

.nep-menu-horizontal>.nep-scroll-x {
    height: 4px;
    border: unset;
}

.nep-menu-horizontal>.nep-scroll-x>.nep-scroll-handle {
    top: 4px;
    height: 4px;
}

.nep-menu-horizontal>.nep-menu-item {
    position: relative;
    display: inline-block;
}

.nep-menu-horizontal>.nep-menu-item>.nep-menu-title {
    height: 38px;
    line-height: 38px;
}

.nep-menu-horizontal>.nep-menu-active>a,
.nep-menu-horizontal>.nep-menu-highlight>a {
    position: relative;
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-menu-horizontal>.nep-menu-active>a:before,
.nep-menu-horizontal>.nep-menu-highlight>a:before {
    position: absolute;
    right: 0;
    bottom: -1px;
    left: 0;
    height: 2px;
    background: #4061C7;
    background: var(--primary-color, #4061C7);
    content: " ";
}

.nep-menu-horizontal .nep-menu-active.nep-menu-no-children>a:after {
    width: 0;
}

.nep-menu-horizontal .nep-menu-root .nep-menu-list {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 100%;
    padding: 4px 0;
    margin-top: 4px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.nep-menu-bar {
    display: none;
}

.nep-menu-vertical.nep-menu-root {
    border-right: 1px solid #4061C7;
}

.nep-menu-vertical>.nep-menu-item {
    position: relative;
}

.nep-menu-vertical:hover .nep-menu-bar {
    display: block;
}

.nep-menu-vertical.nep-menu-scroll .nep-menu-wrapper {
    position: absolute;
    overflow: hidden;
    min-width: 100%;
    height: 100%;
}

.nep-menu-vertical.nep-menu-has-open .nep-menu-wrapper {
    width: 100vw;
}

.nep-menu-vertical .nep-menu-root .nep-menu-list {
    position: absolute;
    top: 0;
    left: 100%;
    width: auto;
    min-width: auto;
    padding: 4px 0;
    margin-left: 3px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.nep-menu-vertical .nep-menu-has-children.nep-menu-open>.nep-menu-title:after,
.nep-menu-vertical .nep-menu-has-children>.nep-menu-title:after {
    transform: translateY(-65%) rotate(270deg);
}

.nep-menu-vertical .nep-menu-root .nep-menu-open-up .nep-menu-list {
    top: auto;
    bottom: 0;
}

.nep-menu-vertical .nep-menu-active.nep-menu-no-children>a {
    background: #4061C7;
    background: var(--menu-item-active-bg, #4061C7);
    color: #fff;
    color: var(--menu-item-active-color, #fff);
}

.nep-menu-dark,
.nep-menu-dark .nep-menu-list {
    /* background-color: #4061C7;
  background-color: var(--menu-dark-bg, #4061C7); */
    /** TODO build neptune menu themes  -commented out by Sachithanandham as this is not required by Vipin Gupta*/
}

.nep-menu-dark,
.nep-menu-dark .nep-menu-item a,
.nep-menu-dark .nep-menu-list {
    color: hsla(0, 0%, 100%, 0.7);
    color: var(--menu-dark-color, hsla(0, 0%, 100%, 0.7));
}

.nep-menu-dark .nep-menu-in-path>a,
.nep-menu-dark .nep-menu-item a:hover {
    color: #fff;
}

.nep-menu-dark.nep-menu-horizontal,
.nep-menu-dark.nep-menu-vertical {
    border-color: #4061C7;
    border-color: var(--menu-dark-bg, #4061C7);
}

.nep-menu-dark.nep-menu-horizontal .nep-menu-list,
.nep-menu-dark.nep-menu-vertical .nep-menu-list {
    background-color: #4061C7;
    background-color: var(--menu-dark-bg, #4061C7);
    color: hsla(0, 0%, 100%, 0.7);
    color: var(--menu-dark-color, hsla(0, 0%, 100%, 0.7));
}

.nep-menu-dark.nep-menu-horizontal .nep-menu-active.nep-menu-no-children a,
.nep-menu-dark.nep-menu-inline .nep-menu-active.nep-menu-no-children a,
.nep-menu-dark.nep-menu-vertical .nep-menu-active.nep-menu-no-children a {
    background: hsla(0, 0%, 100%, 0.2);
    background: var(--menu-dark-acitve-bg, hsla(0, 0%, 100%, 0.2));
    color: #fff;
}

.nep-menu-dark.nep-menu-horizontal .nep-menu-active.nep-menu-no-children a:after,
.nep-menu-dark.nep-menu-horizontal .nep-menu-active.nep-menu-no-children aafter:before,
.nep-menu-dark.nep-menu-inline .nep-menu-active.nep-menu-no-children a:after,
.nep-menu-dark.nep-menu-inline .nep-menu-active.nep-menu-no-children aafter:before,
.nep-menu-dark.nep-menu-vertical .nep-menu-active.nep-menu-no-children a:after,
.nep-menu-dark.nep-menu-vertical .nep-menu-active.nep-menu-no-children aafter:before {
    width: 0;
    height: 0;
}

.nep-menu-dark.nep-menu-horizontal .nep-menu-active>a,
.nep-menu-dark.nep-menu-horizontal .nep-menu-highlight>a,
.nep-menu-dark.nep-menu-inline .nep-menu-active>a,
.nep-menu-dark.nep-menu-inline .nep-menu-highlight>a,
.nep-menu-dark .nep-menu-submenu-title:hover,
.nep-menu-dark .nep-menu-submenu-title:hover>a,
.nep-menu-dark.nep-menu-vertical .nep-menu-active>a,
.nep-menu-dark.nep-menu-vertical .nep-menu-highlight>a {
    color: #fff;
}

.nep-checkinput {
    position: relative;
    display: inline-block;
    margin-right: 24px;
    margin-right: var(--checkbox-margin-right, 24px);
    margin-bottom: 0;
    cursor: pointer;
    vertical-align: middle;
}

.nep-checkinput input[type="checkbox"],
.nep-checkinput input[type="radio"] {
    position: absolute;
    top: 0;
    left: 0;
    clip: rect(0, 0, 0, 0);
}

@keyframes nep-checkinput-focus {
    0% {
        box-shadow: 0 0 0 0 rgba(2, 17, 85, 0.5);
        box-shadow: 0 0 0 0 var(--primary-color-fade-50, rgba(2, 17, 85, 0.5));
    }
    50% {
        box-shadow: 0 0 0 4px rgba(2, 17, 85, 0);
        box-shadow: 0 0 0 4px var(--primary-color-fade-0, rgba(2, 17, 85, 0));
    }
    to {
        box-shadow: 0 0 0 8px rgba(2, 17, 85, 0);
        box-shadow: 0 0 0 8px var(--primary-color-fade-0, rgba(2, 17, 85, 0));
    }
}

.nep-checkinput-button.nep-checkinput-small .nep-checkinput {
    padding: var(--button-padding-small-vertical, 2px) var(--button-padding-small-horizontal, 12px);
    border-radius: 0;
    font-size: 12px;
    font-size: var(--button-font-size-small, 12px);
    line-height: 1.5;
    height: 26px;
}

.nep-checkinput-button.nep-checkinput-large .nep-checkinput {
    padding: var(--button-padding-large-vertical, 8px) var(--button-padding-large-horizontal, 20px);
    border-radius: 0;
    font-size: 17px;
    font-size: var(--button-font-size-large, 17px);
    line-height: 1.42857143;
    height: 42px;
}

.nep-checkinput-button .nep-checkinput {
    margin-right: 0;
    font-weight: 14px;
    outline: none;
    text-align: center;
    touch-action: manipulation;
    vertical-align: middle;
    white-space: nowrap;
    user-select: none;
    padding: var(--button-padding-base-vertical, 4px) var(--button-padding-base-horizontal, 16px);
    border-radius: 3px;
    border-radius: var(--button-border-radius, 3px);
    font-size: 14px;
    font-size: var(--button-font-size-base, 14px);
    line-height: 1.42857143;
    height: 32px;
    border: 1px solid #cac9c7;
    border-color: var(--gray-400, #cac9c7);
    background-color: #fff;
    color: #ababab;
    color: var(--gray-500, #ababab);
    transition: background 0.15s ease-in-out;
    border-radius: 0;
}

@keyframes btn-focus-default {
    0% {
        box-shadow: 0 0 0 0 rgba(222, 223, 224, 0.6);
        box-shadow: 0 0 0 0 var(--gray-300-fade-60, rgba(222, 223, 224, 0.6));
    }
    50% {
        box-shadow: 0 0 0 0.4em rgba(222, 223, 224, 0.6);
        box-shadow: 0 0 0 0.4em var(--gray-300-fade-60, rgba(222, 223, 224, 0.6));
    }
    to {
        box-shadow: 0 0 0 0.8em rgba(222, 223, 224, 0.6);
        box-shadow: 0 0 0 0.8em var(--gray-300-fade-60, rgba(222, 223, 224, 0.6));
    }
}

.nep-checkinput-button .nep-checkinput:active,
.nep-checkinput-button .nep-checkinput:focus,
.nep-checkinput-button .nep-checkinput:hover {
    border-color: #c4c6c7;
    border-color: var(--gray-300-darken-hover, #c4c6c7);
    color: #ababab;
    color: var(--gray-500, #ababab);
}

.nep-checkinput-button .nep-checkinput:active {
    animation: btn-focus-default 0.4s ease-out;
    background-color: #fff;
    background-image: none;
}

.nep-checkinput-button .nep-checkinput[disabled]:focus,
.nep-checkinput-button .nep-checkinput[disabled]:hover,
fieldset[disabled] .nep-checkinput-button .nep-checkinput:focus,
fieldset[disabled] .nep-checkinput-button .nep-checkinput:hover {
    border-color: #cac9c7;
    border-color: var(--gray-400, #cac9c7);
    background-color: #fff;
}

.nep-checkinput-button .nep-checkinput[disabled]:active,
fieldset[disabled] .nep-checkinput-button .nep-checkinput:active {
    animation: none;
}

.nep-checkinput-button .nep-checkinput>.nep-checkinput-indicator,
.nep-checkinput-button .nep-checkinput>input {
    display: none;
}

.nep-checkinput-button .nep-checkinput.nep-checkinput-checked {
    border-color: transparent;
    background-color: #4061C7;
    background-color: var(--primary-color, #4061C7);
    color: #fff;
    transition: background 0.15s ease-in-out;
}

.nep-checkinput-button .nep-checkinput.nep-checkinput-checked:focus,
.nep-checkinput-button .nep-checkinput.nep-checkinput-checked:hover {
    border-color: #55565a;
    border-color: var(--secondary-color, #55565a);
    background-color: #031b87;
    background-color: var(--primary-color-dark-btn-hover, #031b87);
    color: #fff;
}

.nep-checkinput-button .nep-checkinput.nep-checkinput-checked:active {
    border-color: #55565a;
    border-color: var(--secondary-color, #55565a);
    animation: btn-focus-primary 0.4s ease-out;
    background-color: #010723;
    background-color: var(--primary-color-dark-btn-active, #010723);
    color: #fff;
    background-image: none;
}

.nep-checkinput-button .nep-checkinput.nep-checkinput-checked[disabled]:focus,
.nep-checkinput-button .nep-checkinput.nep-checkinput-checked[disabled]:hover,
fieldset[disabled] .nep-checkinput-button .nep-checkinput.nep-checkinput-checked:focus,
fieldset[disabled] .nep-checkinput-button .nep-checkinput.nep-checkinput-checked:hover {
    border-color: transparent;
    background-color: #4061C7;
    background-color: var(--primary-color, #4061C7);
}

.nep-checkinput-button .nep-checkinput.nep-checkinput-checked[disabled]:active,
fieldset[disabled] .nep-checkinput-button .nep-checkinput.nep-checkinput-checked:active {
    animation: none;
}

.nep-checkinput-button .nep-checkinput.nep-checkinput-checked+.nep-checkinput {
    border-left-width: 0;
}

.nep-checkinput-button .nep-checkinput:not(:last-child) {
    border-right-width: 0;
}

.nep-checkinput-button .nep-checkinput:first-child {
    border-top-left-radius: 3px;
    border-top-left-radius: var(--button-border-radius, 3px);
    border-bottom-left-radius: 3px;
    border-bottom-left-radius: var(--button-border-radius, 3px);
}

.nep-checkinput-button .nep-checkinput:last-child {
    border-top-right-radius: 3px;
    border-top-right-radius: var(--button-border-radius, 3px);
    border-bottom-right-radius: 3px;
    border-bottom-right-radius: var(--button-border-radius, 3px);
}

.nep-checkinput-button.nep-checkinput-outline .nep-checkinput-checked {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
    background: transparent;
    color: #4061C7;
    color: var(--primary-color, #4061C7);
    border-right-width: 1px;
}

.nep-checkinput-button.nep-checkinput-outline .nep-checkinput-checked:active,
.nep-checkinput-button.nep-checkinput-outline .nep-checkinput-checked:focus,
.nep-checkinput-button.nep-checkinput-outline .nep-checkinput-checked:hover {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
    background: transparent;
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-checkinput-switch {
    user-select: none;
    text-align: center;
    margin: 8px 0;
    min-width: 44px;
    height: 16px;
    line-height: 16px;
    border-radius: 22px;
    padding-left: 14px;
    padding-right: 4px;
    background: #cac9c7;
    background: var(--gray-400, #cac9c7);
    position: relative;
    transition: all 0.36s cubic-bezier(0.78, 0.14, 0.15, 0.86);
}

.nep-checkinput-switch input {
    width: 0;
    height: 0;
}

.nep-checkinput-switch .nep-checkinput-indicator {
    display: none;
}

.nep-checkinput-switch-indicator {
    position: absolute;
    left: 0;
    transition: all 0.36s cubic-bezier(0.78, 0.14, 0.15, 0.86);
    width: 14px;
    height: 14px;
    top: 1px;
    background: #fff;
    display: inline-block;
    border-radius: 50%;
    box-shadow: 0 1px 4px #ababab;
    box-shadow: 0 1px 4px var(--gray-500, #ababab);
}

.nep-checkinput-switch-children {
    font-size: 12px;
    color: #55565a;
    padding: 0 4px;
}

.nep-checkinput-checked.nep-checkinput-switch {
    padding-right: 14px;
    padding-left: 4px;
    background: rgba(2, 17, 85, 0.5);
    background: var(--primary-color-fade-50, rgba(2, 17, 85, 0.5));
}

.nep-checkinput-checked.nep-checkinput-switch .nep-checkinput-switch-indicator {
    box-shadow: 0 1px 4px rgba(2, 17, 85, 0.5);
    box-shadow: 0 1px 4px var(--primary-color-fade-50, rgba(2, 17, 85, 0.5));
    left: 96%;
    margin-left: 0;
    transform: translateX(-100%);
    background: #4061C7;
    background: var(--primary-color, #4061C7);
}

.nep-checkinput-checked.nep-checkinput-small.nep-checkinput-switch {
    padding-left: 0;
    padding-right: 8px;
}

.nep-checkinput-checked.nep-checkinput-large.nep-checkinput-switch {
    padding-left: 0;
    padding-right: 20px;
}

.nep-checkinput-small.nep-checkinput-switch {
    min-width: 28px;
    height: 10px;
    line-height: 10px;
    border-radius: 14px;
    padding-left: 8px;
    padding-right: 0;
}

.nep-checkinput-small.nep-checkinput-switch .nep-checkinput-switch-indicator {
    width: 8px;
    height: 8px;
    top: 1px;
}

.nep-checkinput-large.nep-checkinput-switch {
    min-width: 60px;
    height: 22px;
    line-height: 22px;
    border-radius: 30px;
    padding-left: 20px;
    padding-right: 0;
}

.nep-checkinput-large.nep-checkinput-switch .nep-checkinput-switch-indicator {
    width: 20px;
    height: 20px;
    top: 1px;
}

.nep-checkinput-disabled.nep-checkinput-switch {
    opacity: 0.4;
}

.nep-checkinput-checked .nep-checkinput-switch-children {
    color: #fff;
}

.nep-checkinput-checked i.nep-checkinput-indicator,
.nep-checkinput-indeterminate i.nep-checkinput-indicator {
    animation: nep-checkinput-focus 0.6s ease-out;
}

i.nep-checkinput-indicator {
    position: relative;
    display: inline-block;
    overflow: hidden;
    width: 16px;
    height: 16px;
    box-sizing: content-box;
    border: 1px solid #cac9c7;
    border: 1px solid var(--input-border-color, #cac9c7);
    border-radius: 4px;
    vertical-align: middle;
}

i.nep-checkinput-indicator+span {
    padding: 0 4px;
    vertical-align: middle;
    left: 4%;
}

i.nep-checkinput-indicator:after {
    position: absolute;
    z-index: 10;
    display: block;
    content: " ";
}

i.nep-checkinput-checkbox.nep-checkinput-indicator:after {
    top: 20%;
    left: 2px;
    width: 80%;
    height: 40%;
    border: 2px solid transparent;
    border-width: 0 0 2px 2px;
}

i.nep-checkinput-radio.nep-checkinput-indicator {
    border-radius: 50%;
    display: inline-flex;
    width: 16px;
    width: var(--radio-width, 16px);
    height: 16px;
    height: var(--radio-width, 16px);
    border-width: 1px;
    border-width: var(--radio-border-width, 1px);
}

i.nep-checkinput-radio.nep-checkinput-indicator:after {
    margin: auto;
    position: static;
    width: 10px;
    width: var(--radio-inner-width, 10px);
    height: 10px;
    height: var(--radio-inner-width, 10px);
    border-radius: 50%;
}

.nep-checkinput .nep-checkinput-text {
    display: inline-block;
    width: auto;
}

.nep-checkinput-group {
    padding: 4px 0;
}

.nep-checkinput-block .nep-checkinput {
    display: block;
    margin-bottom: 8px;
}

.nep-checkinput:focus {
    outline: none;
}

.nep-checkinput:focus i.nep-checkinput-indicator {
    box-shadow: 0 0 0 var(--input-focus-width, 3px) rgba(2, 17, 85, 0.25);
    box-shadow: 0 0 0 var(--input-focus-width, 3px) var(--input-border-focus-color-fade-25, rgba(2, 17, 85, 0.25));
}

.nep-checkinput:focus i.nep-checkinput-indicator,
.nep-checkinput:hover i.nep-checkinput-indicator {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
}

.nep-checkinput-checked i.nep-checkinput-checkbox.nep-checkinput-indicator {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
    background: #4061C7;
    background: var(--primary-color, #4061C7);
}

.nep-checkinput-checked i.nep-checkinput-checkbox.nep-checkinput-indicator:after {
    border-color: #fff;
    transform: rotate(-45deg);
}

.nep-checkinput-checked i.nep-checkinput-radio.nep-checkinput-indicator {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
}

.nep-checkinput-checked i.nep-checkinput-radio.nep-checkinput-indicator:after,
.nep-checkinput-indeterminate i.nep-checkinput-indicator {
    background: #4061C7;
    background: var(--primary-color, #4061C7);
}

.nep-checkinput-indeterminate i.nep-checkinput-indicator {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
}

.nep-checkinput-indeterminate i.nep-checkinput-indicator:after {
    border-color: #fff;
    border-left-width: 0;
}

.nep-checkinput-disabled {
    cursor: not-allowed;
}

.nep-checkinput-disable:focus i.nep-checkinput-indicator,
.nep-checkinput-disabled.nep-checkinput-checked i.nep-checkinput-indicator,
.nep-checkinput-disabled:hover i.nep-checkinput-indicator,
.nep-checkinput-disabled i.nep-checkinput-indicator {
    border-color: #ababab;
    border-color: var(--gray-500, #ababab);
    box-shadow: none;
    background-color: #f0f0f1;
    background-color: var(--input-bg-disabled, #f0f0f1);
}

.nep-checkinput-disabled.nep-checkinput-checked i.nep-checkinput-radio:after {
    background-color: #ababab;
    background-color: var(--gray-500, #ababab);
}

.nep-checkinput-disabled.nep-checkinput-checked i.nep-checkinput-indicator:after,
.nep-checkinput-disabled.nep-checkinput-indeterminate i.nep-checkinput-indicator:after {
    border-color: #ababab;
    border-color: var(--gray-500, #ababab);
}

.nep-form {
    position: relative;
}

.nep-form-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    margin-bottom: var(--form-item-margin-bottom, 20px);
}

.nep-form-item .nep-form-label {
    display: inline-block;
    width: 140px;
    padding: 4px 8px;
    word-break: break-word;
}

.nep-form-item.nep-form-required .nep-form-label:before {
    margin-right: 4px;
    color: #ba0c2f;
    color: var(--danger-color, #ba0c2f);
    content: "*";
    font-family: "Helvetica Neue LT W05_55 Roman",Arial, Verdana, Tahoma,sans-serif;
}

.nep-form-item .nep-form-control {
    flex: 1;
    padding: 0 8px;
}

.nep-form-item .nep-form-control:first-child {
    padding-left: 0;
}

.nep-form .nep-form-error,
.nep-form .nep-form-tip {
    margin-top: 5px;
    margin-bottom: 8px;
    color: #a9acbb;
    color: var(--form-tip-color, #a9acbb);
    font-size: 12px;
}

.nep-form .nep-form-error {
    color: #ba0c2f;
    color: var(--danger-color, #ba0c2f);
}

.nep-form .nep-form-error+.nep-form-tip {
    display: none;
}

.nep-form-label-align-right .nep-form-label {
    text-align: right;
}

.nep-form-label-align-top {
    display: block;
    width: 100%;
}

.nep-form-label-align-top .nep-form-label {
    width: 100%;
    padding: 0;
    margin-bottom: 8px;
}

.nep-form-label-align-top .nep-form-control {
    padding: 0;
}

.nep-form-inline .nep-input {
    display: inline-flex;
    width: auto;
    vertical-align: top;
}

.nep-form-inline>.nep-input {
    margin-right: 20px;
    margin-right: var(--form-item-margin-right, 20px);
}

.nep-form-inline .nep-input-group {
    display: inline-flex;
}

.nep-form-inline .nep-form-item {
    display: inline-flex;
    margin-right: 20px;
    margin-right: var(--form-item-margin-right, 20px);
    vertical-align: top;
}

.nep-form-inline .nep-form-item .nep-form-label {
    width: auto;
    padding: 0;
    margin-top: 6px;
    margin-right: 16px;
}

.nep-form-inline .nep-form-item .nep-input {
    margin-right: 0;
}

.nep-form-inline .nep-form-item .nep-form-control {
    display: inline-block;
    padding: 0;
}

.nep-form-inline>* {
    margin-bottom: 12px;
}

.nep-form-disabled .nep-input,
.nep-form-disabled .nep-input:hover,
.nep-form-disabled .nep-input input {
    border-color: #cac9c7;
    border-color: var(--input-border-color, #cac9c7);
    background-color: #f0f0f1;
    background-color: var(--input-bg-disabled, #f0f0f1);
    cursor: not-allowed;
}

.nep-input {
    position: relative;
    display: flex;
    width: 100%;
    border: 1px solid #cac9c7;
    border: 1px solid var(--input-border-color, #cac9c7);
    background-color: #fff;
    background-image: none;
    color: #ababab;
    color: var(--gray-500, #ababab);
    height: 32px;
    padding: 0;
    border-radius: 4px;
    border-radius: var(--input-border-radius, 4px);
    font-size: 14px;
    line-height: 1.42857143;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.nep-input:focus {
    outline: none;
    border-color: #4061C7;
    border-color: var(--input-border-focus-color, #4061C7);
}

select.nep-input {
    height: 32px;
    line-height: 32px;
}

select[multiple].nep-input,
textarea.nep-input {
    height: auto;
}

.nep-input-bottom-border {
    border: none;
    border-bottom: 1px solid #cac9c7;
    border-bottom: 1px solid var(--input-border-color, #cac9c7);
    background-color: #fff;
    background-image: none;
    border-radius: 0;
}

.nep-input-bottom-border:focus {
    box-shadow: unset;
}

.nep-input-textarea {
    height: auto;
}

.nep-input-clear-wrapper {
    width: 16px;
}

.nep-input-clear-wrapper .nep-input-clear {
    border-radius: 6px;
    position: absolute;
    top: 50%;
    right: 8px;
    width: 12px;
    height: 12px;
    transform: translateY(-50%);
    cursor: pointer;
    border-radius: 8px;
}

.nep-input-clear-wrapper .nep-input-clear:after,
.nep-input-clear-wrapper .nep-input-clear:before {
    position: absolute;
    top: 7px;
    left: 1px;
    display: block;
    width: 12px;
    height: 1px;
    background: #717171;
    content: " ";
}

.nep-input-clear-wrapper .nep-input-clear:after {
    transform: rotate(45deg);
}

.nep-input-clear-wrapper .nep-input-clear:before {
    transform: rotate(315deg);
}

.nep-input-clear-wrapper .nep-input-clear:hover:after,
.nep-input-clear-wrapper .nep-input-clear:hover:before {
    background: #4061C7;
    background: var(--primary-color, #4061C7);
}

.nep-input-inline {
    display: inline-block;
    display: inline-flex;
}

.nep-input:hover {
    border-color: #4061C7;
    border-color: var(--input-border-focus-color, #4061C7);
}

.nep-input-number-down,
.nep-input-number-up {
    position: absolute;
    right: 0;
    width: 18px;
    padding: 0 4px;
    border-left: 1px solid #f0f0f1;
    border-left: 1px solid var(--gray-200, #f0f0f1);
    line-height: 1;
}

.nep-input-number-down svg path,
.nep-input-number-up svg path {
    fill: #ababab;
    fill: var(--gray-500, #ababab);
}

.nep-input-number-down:hover svg path,
.nep-input-number-up:hover svg path {
    fill: #4061C7;
    fill: var(--primary-color, #4061C7);
}

.nep-input-number-up {
    top: 0;
    bottom: 50%;
    border-bottom: 1px solid #f0f0f1;
    border-bottom: 1px solid var(--gray-200, #f0f0f1);
}

.nep-input-number-up svg {
    transform: rotate(-90deg);
}

.nep-input-number-down {
    top: 50%;
    bottom: 0;
}

.nep-input-number-down svg {
    transform: rotate(90deg);
}

.nep-input-group.nep-input-large,
.nep-input-group.nep-input-small {
    padding: 0;
}

.nep-input .nep-input {
    width: auto;
    flex: 1;
    border-width: 0;
    border-color: #cac9c7;
    border-color: var(--input-border-color, #cac9c7);
    background: transparent;
    border-radius: 0;
    box-shadow: none;
}

@keyframes nep-input-fade-center {
    0% {
        transform: scale(0.6) translateX(-50%);
    }
    to {
        transform: scale(1) translateX(-50%);
    }
}

@keyframes nep-input-fade {
    0% {
        transform: scale(0.6);
    }
    to {
        transform: scale(1);
    }
}

.nep-input-error,
.nep-input-tip {
    position: absolute;
    z-index: 1000;
    min-width: 200px;
    max-width: 400px;
    padding: 4px 16px;
    background: #fafafb;
    background: var(--gray-100, #fafafb);
    border-radius: 4px;
    border-radius: var(--input-border-radius, 4px);
    box-shadow: 0 0 0 1px #cac9c7, 0 2px 8px rgba(0, 0, 0, 0.15);
    box-shadow: 0 0 0 1px var(--input-border-color, #cac9c7), 0 2px 8px rgba(0, 0, 0, 0.15);
    color: #a9acbb;
    color: var(--gray-900-lighten-40, #a9acbb);
    font-size: 12px;
}

.nep-input-error:before,
.nep-input-tip:before {
    position: absolute;
    width: 6px;
    height: 6px;
    border: 1px solid #cac9c7;
    border: 1px solid var(--input-border-color, #cac9c7);
    border-width: 1px 0 0 1px;
    background: inherit;
    content: " ";
}

.nep-input-error.nep-input-bottom,
.nep-input-error.nep-input-bottom-left,
.nep-input-error.nep-input-bottom-right,
.nep-input-tip.nep-input-bottom,
.nep-input-tip.nep-input-bottom-left,
.nep-input-tip.nep-input-bottom-right {
    top: 100%;
    margin-top: 10px;
    transform-origin: 0 0;
}

.nep-input-error.nep-input-bottom-left:before,
.nep-input-error.nep-input-bottom-right:before,
.nep-input-error.nep-input-bottom:before,
.nep-input-tip.nep-input-bottom-left:before,
.nep-input-tip.nep-input-bottom-right:before,
.nep-input-tip.nep-input-bottom:before {
    bottom: 100%;
    transform: rotate(45deg) translateY(3px);
}

.nep-input-error.nep-input-top,
.nep-input-error.nep-input-top-left,
.nep-input-error.nep-input-top-right,
.nep-input-tip.nep-input-top,
.nep-input-tip.nep-input-top-left,
.nep-input-tip.nep-input-top-right {
    bottom: 100%;
    margin-bottom: 10px;
    transform-origin: 0 100%;
}

.nep-input-error.nep-input-top-left:before,
.nep-input-error.nep-input-top-right:before,
.nep-input-error.nep-input-top:before,
.nep-input-tip.nep-input-top-left:before,
.nep-input-tip.nep-input-top-right:before,
.nep-input-tip.nep-input-top:before {
    top: 100%;
    transform: rotate(225deg) translate(3px);
}

.nep-input-error.nep-input-bottom,
.nep-input-error.nep-input-top,
.nep-input-tip.nep-input-bottom,
.nep-input-tip.nep-input-top {
    left: 50%;
    animation: nep-input-fade-center 0.16s ease-in;
    transform: translateX(-50%);
}

.nep-input-error.nep-input-bottom:before,
.nep-input-error.nep-input-top:before,
.nep-input-tip.nep-input-bottom:before,
.nep-input-tip.nep-input-top:before {
    left: 50%;
}

.nep-input-error.nep-input-bottom-left,
.nep-input-error.nep-input-top-left,
.nep-input-tip.nep-input-bottom-left,
.nep-input-tip.nep-input-top-left {
    left: 0;
    animation: nep-input-fade 0.16s ease-in;
}

.nep-input-error.nep-input-bottom-right,
.nep-input-error.nep-input-top-right,
.nep-input-tip.nep-input-bottom-right,
.nep-input-tip.nep-input-top-right {
    right: 0;
    animation: nep-input-fade 0.16s ease-in;
}

.nep-input-error.nep-input-bottom-right:before,
.nep-input-error.nep-input-top-right:before,
.nep-input-tip.nep-input-bottom-right:before,
.nep-input-tip.nep-input-top-right:before {
    right: 13px;
}

.nep-input-error.nep-input-bottom-right,
.nep-input-tip.nep-input-bottom-right {
    transform-origin: 100% 0;
}

.nep-input-error.nep-input-top-right,
.nep-input-tip.nep-input-top-right {
    transform-origin: 100% 100%;
}

.nep-input-tip {
    visibility: hidden;
}

.nep-input-error {
    box-shadow: 0 0 0 1px #ecb5be, 0 2px 8px rgba(0, 0, 0, 0.15);
    color: #ba0c2f;
    color: var(--danger-color, #ba0c2f);
}

.nep-input-error:before {
    border-color: #ecb5be;
}

.nep-input-focus {
    border-color: #4061C7;
    border-color: var(--input-border-focus-color, #4061C7);
}

.nep-input-focus .nep-input-tip {
    visibility: visible;
}

.nep-input-invalid,
.nep-input-invalid.nep-input-focus,
.nep-input-invalid:hover {
    border-color: #ba0c2f;
    border-color: var(--danger-color, #ba0c2f);
}

.nep-input-no-border {
    border-width: 0;
}

.nep-input-no-border.nep-input-focus {
    box-shadow: none;
}

.nep-input-overflow-auto {
    overflow: auto;
}

.nep-input-overflow-hidden {
    overflow: hidden;
}

.nep-input-disabled,
.nep-input-disabled:hover,
.nep-input-disabled input {
    border-color: #cac9c7;
    border-color: var(--input-border-color, #cac9c7);
    background-color: #f0f0f1;
    background-color: var(--input-bg-disabled, #f0f0f1);
    cursor: not-allowed;
}

.nep-input input,
.nep-input textarea {
    display: block;
    width: 100%;
    padding: 4px 16px;
    border: 0;
    background: transparent;
    line-height: inherit;
    outline: none;
}

.nep-input-placeholder,
.nep-input ::placeholder {
    color: #dedfe0;
    color: var(--gray-300, #dedfe0);
}

.nep-input textarea {
    position: relative;
    z-index: 2;
    line-height: 1.4;
    resize: none;
}

.nep-input input.nep-input-number {
    padding-right: 12px;
}

textarea.nep-input-shadow {
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    visibility: hidden;
}

textarea.nep-input-auto-size {
    overflow: hidden;
}

.nep-input-small {
    height: 26px;
    padding: 0;
    border-radius: 2px;
    font-size: 12px;
    line-height: 1.5;
}

select.nep-input-small {
    height: 26px;
    line-height: 26px;
}

select[multiple].nep-input-small,
textarea.nep-input-small {
    height: auto;
}

.nep-input-small input {
    padding: 2px 12px;
}

.nep-input-large {
    height: 42px;
    padding: 0;
    border-radius: 6px;
    font-size: 17px;
    line-height: 1.3333333;
}

select.nep-input-large {
    height: 42px;
    line-height: 42px;
}

select[multiple].nep-input-large,
textarea.nep-input-large {
    height: auto;
}

.nep-input-large input {
    padding: 8px 20px;
}

.nep-input-group {
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    padding: 0;
    height: auto;
}

.nep-input-group .nep-input+.nep-input {
    border-left-width: 1px;
}

.nep-input-group button {
    border-width: 0;
}

.nep-input-group>i,
.nep-input-group>span {
    display: block;
    margin: auto 0;
    background: transparent;
}

.nep-input-group>i:first-child,
.nep-input-group>span:first-child {
    padding-left: 16px;
}

.nep-input-group>i:last-child,
.nep-input-group>span:last-child {
    padding-right: 16px;
}

.nep-input-group>b {
    display: flex;
    align-items: center;
    padding: 0 16px;
    margin: 0 0 0 -1px;
    border: 1px solid #cac9c7;
    border: 1px solid var(--input-border-color, #cac9c7);
    border-width: 0 1px;
    background: #f0f0f1;
    background: var(--gray-200, #f0f0f1);
    font-weight: 400;
}

.nep-input-group>b:first-child {
    margin-left: 0;
    border-left-width: 0;
    border-bottom-left-radius: 4px;
    border-bottom-left-radius: var(--input-border-radius, 4px);
    border-top-left-radius: 4px;
    border-top-left-radius: var(--input-border-radius, 4px);
}

.nep-input-group>b:last-child {
    border-right-width: 0;
    border-bottom-right-radius: 4px;
    border-bottom-right-radius: var(--input-border-radius, 4px);
    border-top-right-radius: 4px;
    border-top-right-radius: var(--input-border-radius, 4px);
}

.nep-input-group .nep-input-invalid.nep-input-focus {
    box-shadow: none;
}

.nep-select {
    position: relative;
}

.nep-select-inner {
    width: 100%;
    outline: none;
}

.nep-select-result {
    display: flex;
    overflow: auto;
    max-height: 80px;
    flex-flow: wrap;
    cursor: pointer;
    padding: 4px 8px 0 8px;
}

.nep-select-result span {
    display: inline-block;
}

.pr8 {
    padding-right: 8px !important;
}

.ptStyle {
    padding-top: 5px !important;
}

.nep-select-result span.nep-select-ellipsis {
    display: block;
    overflow: hidden;
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.nep-select-result .nep-thumbnail-small {
    width: 20px;
    height: 20px;
}

.nep-select-result .nep-select-ellipsis {
    margin-bottom: 4px;
}

.nep-select-result .nep-select-ellipsis:after {
    content: "\feff ";
}

.nep-select-result .nep-select-input {
    display: inline-flex;
    min-width: 12px;
    flex: 1;
    margin-bottom: 4px;
    outline: none;
    cursor: text;
    color: #55565a;
    color: var(--secondary-color, #55565a);
}

.nep-select-result .nep-select-input:after {
    content: "\feff ";
}

.nep-select-result .nep-select-input.nep-select-full {
    display: block;
}

.nep-select-result .nep-select-item {
    position: relative;
    display: inline-block;
    overflow: hidden;
    max-width: 80%;
    padding: var(--select-result-padding-vertical, 0) var(--select-result-padding-horizontal-16, 20px) var(--select-result-padding-vertical, 0) var(--select-result-padding-horizontal, 4px);
    margin-right: 4px;
    margin-right: var(--select-result-padding-horizontal, 4px);
    margin-bottom: 4px;
    background: #fafafb;
    background: var(--gray-100, #fafafb);
    border-radius: 3px;
    color: inherit;
    text-overflow: ellipsis;
}

.nep-select-result .nep-select-item:after {
    content: "\feff ";
}

.nep-select-result .nep-select-item-compressed {
    padding: 0 8px;
    font-size: 80%;
    opacity: 0.9;
    text-overflow: unset;
    transition: none;
}

.nep-select-result .nep-select-item-compressed>span {
    vertical-align: middle;
    letter-spacing: 2px;
}

.nep-select-result .nep-select-item-more {
    background: rgba(2, 17, 85, 0.6);
    background: var(--primary-color-fade-60, rgba(2, 17, 85, 0.6));
    color: #fff;
}

.nep-select-result .nep-select-item.nep-select-disabled {
    padding-right: 4px;
    padding-right: var(--select-result-padding-horizontal, 4px);
    cursor: not-allowed;
}

.nep-select-result .nep-select-item .nep-select-close {
    right: 4px;
    right: var(--select-result-padding-horizontal, 4px);
    display: block;
    background-color: transparent;
}

.nep-select-result .nep-select-item .nep-select-close:after,
.nep-select-result .nep-select-item .nep-select-close:before {
    background-color: #ababab;
    background-color: var(--gray-500, #ababab);
    width: 8px;
}

.nep-select-result .nep-select-item .nep-select-close:hover {
    background-color: transparent;
}

.nep-select-result .nep-select-item .nep-select-close:hover:after,
.nep-select-result .nep-select-item .nep-select-close:hover:before {
    background-color: $gray-200;
    background-color: var(--gray-600, $gray-200);
}

.nep-select-compressed {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
}

.nep-select-compressed .nep-select-ban {
    padding-right: 4px;
    padding-right: var(--select-result-padding-horizontal, 4px);
}

.nep-select-close-warpper {
    position: absolute;
    top: 50%;
    right: 7px;
    display: block;
    width: 18px;
    height: 18px;
    transform: translateY(-50%);
}

.nep-select-close-warpper .nep-select-indicator {
    right: 2px;
}

.nep-select-indicator {
    position: absolute;
    top: 50%;
    right: 7px;
    display: block;
    width: 12px;
    height: 12px;
    color: #ababab;
    color: var(--gray-500, #ababab);
    transform: translateY(-50%);
}

.nep-select-indicator.nep-select-close {
    display: none;
    border-radius: 8px;
}

.nep-select-indicator.nep-select-close:after,
.nep-select-indicator.nep-select-close:before {
    position: absolute;
    top: 7px;
    left: 1px;
    display: block;
    width: 12px;
    height: 1px;
    background: #717171;
    content: " ";
}

.nep-select-indicator.nep-select-close:after {
    transform: rotate(45deg);
}

.nep-select-indicator.nep-select-close:before {
    transform: rotate(315deg);
}

.nep-select-indicator.nep-select-close:hover:after,
.nep-select-indicator.nep-select-close:hover:before {
    background: #4061C7;
    background: var(--primary-color, #4061C7);
}

.nep-select-indicator.nep-select-caret:after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 4px;
    content: "";
    border-top: 4px solid;
    border-right: 4px solid transparent;
    border-bottom: 0;
    border-left: 4px solid transparent;
    vertical-align: 3.4px;
    position: absolute;
    top: 3.2px;
    left: 2px;
    margin: 0;
}

.nep-select-focus .nep-select-close,
.nep-select-result:hover .nep-select-close {
    display: block;
}

.nep-select-box-list,
.nep-select-options {
    position: absolute;
    z-index: 1000;
    left: 0;
    display: none;
    overflow: hidden;
    width: 100%;
    background: #fff;
    background-clip: padding-box;
    border-radius: 4px;
}

.nep-select-box-list {
    min-width: 100%;
}

.nep-select-tree {
    width: auto;
    min-width: 100%;
}

.nep-select-tree .nep-select-tree-wrapper {
    padding: 8px 8px 4px;
}

.nep-select-tree .nep-select-tree-wrapper .nep-tree-node:last-child>div {
    padding-bottom: 4px;
}

.nep-select-tree .nep-select-tree-wrapper .nep-tree-content {
    color: #ababab;
    color: var(--gray-500, #ababab);
    white-space: nowrap;
    cursor: default;
}

.nep-select-tree .nep-select-tree-wrapper .nep-tree-content .nep-select-tree-node {
    display: block;
    padding: 0 4px;
    border-radius: 2px;
}

.nep-select-tree .nep-select-tree-wrapper .nep-tree-content .nep-select-tree-node:hover {
    background-color: rgba(2, 17, 85, 0.1);
    background-color: var(--primary-color-fade-10, rgba(2, 17, 85, 0.1));
}

.nep-select-tree .nep-select-tree-wrapper .nep-tree-content .nep-select-tree-node.nep-select-selected {
    background-color: rgba(2, 17, 85, 0.6);
    background-color: var(--primary-color-fade-60, rgba(2, 17, 85, 0.6));
    color: #fff;
}

.nep-select-tree .nep-select-tree-wrapper .nep-tree-content .nep-select-tree-node.nep-select-disabled {
    padding-right: 4px;
    padding-right: var(--select-result-padding-horizontal, 4px);
    cursor: not-allowed;
    background: #f0f0f1;
    background: var(--input-bg-disabled, #f0f0f1);
}

.nep-select-box-list {
    display: flex;
    overflow: hidden;
    min-height: 200px;
    max-height: 300px;
    flex-direction: column;
}

.nep-select-box-list .nep-select-header {
    padding: 8px 16px;
    border-bottom: 1px solid #eee;
}

.nep-select-box-list .nep-select-box-options {
    overflow: auto;
    flex: 1;
    padding: 8px 0;
}

.nep-select-box-list .nep-select-box-options .nep-select-no-data {
    padding-top: 60px;
    color: #ababab;
    color: var(--gray-500, #ababab);
    text-align: center;
}

.nep-select-box-list .nep-select-option {
    display: inline-block;
    padding-right: 16px;
    margin-right: 0;
}

.nep-select-box-list .nep-select-filter-input {
    display: inline-flex;
    width: 180px;
    border-width: 1px;
    border-radius: 2px;
    float: right;
}

.nep-select-box-list .nep-select-filter-input svg {
    width: 22px;
    height: 22px;
    padding: 4px;
}

.nep-select-box-list .nep-select-filter-input svg path {
    fill: #9e9e9e;
}

.nep-select-drop-down .nep-select-box-list,
.nep-select-drop-down .nep-select-options {
    top: 100%;
    margin-top: 4px;
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.02), 0 6px 12px rgba(0, 0, 0, 0.175);
    transform-origin: 0 0;
}

.nep-select-drop-up .nep-select-box-list,
.nep-select-drop-up .nep-select-options {
    bottom: 100%;
    margin-bottom: 4px;
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1), 0 -2px 12px rgba(0, 0, 0, 0.175);
    transform-origin: 0 100%;
}

.nep-select-option {
    position: relative;
    display: block;
    overflow: hidden;
    padding: 8px 28px 8px 16px;
    color: #55565a;
    color: var(--gray-800, #55565a);
    font-size: 14px;
    line-height: 1.42857143;
    text-overflow: ellipsis;
    transition: none;
    white-space: nowrap;
}

.nep-select-option.nep-select-active {
    background-color: #f5f5f5;
    background-color: var(--select-item-active-bg, #f5f5f5);
    color: #49494d;
    color: var(--gray-800-darken-5, #49494d);
    text-decoration: none;
}

.nep-select-option.nep-select-active:after {
    position: absolute;
    top: 50%;
    right: 8px;
    width: 12px;
    height: 12px;
    background: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='#4061C7' d='M913.017 237.02c-25.311-25.312-66.349-25.312-91.66 0l-412.475 412.474-206.237-206.237c-25.312-25.312-66.35-25.312-91.661 0s-25.312 66.35 0 91.66l252.067 252.067c0.729 0.73 1.439 1.402 2.134 2.029 25.434 23.257 64.913 22.585 89.527-2.029l458.303-458.303c25.313-25.312 25.313-66.35 0.001-91.661z'%3E%3C/path%3E%3C/svg%3E");
    background: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='var(--primary-color, #4061C7)' d='M913.017 237.02c-25.311-25.312-66.349-25.312-91.66 0l-412.475 412.474-206.237-206.237c-25.312-25.312-66.35-25.312-91.661 0s-25.312 66.35 0 91.66l252.067 252.067c0.729 0.73 1.439 1.402 2.134 2.029 25.434 23.257 64.913 22.585 89.527-2.029l458.303-458.303c25.313-25.312 25.313-66.35 0.001-91.661z'%3E%3C/path%3E%3C/svg%3E");
    color: #4061C7;
    color: var(--primary-color, #4061C7);
    content: " ";
    transform: translateY(-50%);
}

.nep-select-option:hover {
    background-color: #f5f5f5;
    background-color: var(--select-option-hover-bg, #f5f5f5);
    color: #49494d;
    color: var(--gray-800-darken-5, #49494d);
}

.nep-select-option.nep-select-disabled,
.nep-select-option.nep-select-disabled.nep-select-active,
.nep-select-option.nep-select-disabled:hover {
    background: #f0f0f1;
    background: var(--input-bg-disabled, #f0f0f1);
    cursor: not-allowed;
}

.nep-select-group {
    font-size: 12px;
    color: #ababab;
    color: var(--gray-500, #ababab);
    cursor: default;
}

.nep-select-option+.nep-select-group:before {
    content: "";
    position: absolute;
    left: 16px;
    top: 0;
    right: 2px;
    border-top: 1px solid #e0e0e0;
}

.nep-select-control-mouse .nep-select-option:hover {
    background-color: #f5f5f5;
    background-color: var(--select-option-hover-bg, #f5f5f5);
    color: #49494d;
    color: var(--gray-800-darken-5, #49494d);
    text-decoration: none;
}

.nep-select-control-mouse .nep-select-group:hover {
    color: #ababab;
    color: var(--gray-500, #ababab);
}

.nep-select-control-mouse .nep-select-option.nep-select-disabled:hover {
    background: #f0f0f1;
    background: var(--input-bg-disabled, #f0f0f1);
}

.nep-select-control-keyboard .nep-select-option.nep-select-hover {
    background-color: #f5f5f5;
    background-color: var(--select-option-hover-bg, #f5f5f5);
    color: #49494d;
    color: var(--gray-800-darken-5, #49494d);
    text-decoration: none;
}

.nep-select-control-keyboard .nep-select-option.nep-select-group.nep-select-hover {
    color: #ababab;
    color: var(--gray-500, #ababab);
}

.nep-select-control-keyboard .nep-select-option {
    cursor: none;
}

span.nep-select-option {
    color: #ababab;
    color: var(--gray-500, #ababab);
}

.nep-select-small .nep-select-result {
    padding: 2px 24px 0 12px;
}

.nep-select-small .nep-select-result .nep-select-ellipsis,
.nep-select-small .nep-select-result .nep-select-input,
.nep-select-small .nep-select-result .nep-select-item {
    margin-bottom: 2px;
}

.nep-select-large .nep-select-result {
    padding: 8px 32px 0 20px;
}

.nep-select-large .nep-select-result .nep-select-ellipsis,
.nep-select-large .nep-select-result .nep-select-input,
.nep-select-large .nep-select-result .nep-select-item {
    margin-bottom: 8px;
}

.nep-select-multiple .nep-select-result {
    padding-left: 8px;
    padding-right: 38px;
}

.nep-select-multiple .nep-select-result .nep-select-close-warpper {
    right: 17px;
}

.nep-select-multiple .nep-select-compressed {
    padding-left: 8px;
    padding-right: 28px;
}

.nep-select-multiple .nep-select-compressed .nep-select-close-warpper {
    right: 7px;
}

.nep-select-large.nep-select-multiple .nep-select-result {
    padding-left: 10px;
}

.nep-select-small.nep-select-multiple .nep-select-result {
    padding-left: 6px;
}

.nep-select-disabled .nep-select-result {
    cursor: not-allowed;
}

.nep-select-disabled .nep-select-result .nep-select-item {
    padding-right: 4px;
    padding-right: var(--select-result-padding-horizontal, 4px);
    background: #f0f0f1;
    background: var(--input-bg-disabled, #f0f0f1);
}

.nep-select-disabled .nep-select-result .nep-select-item .nep-select-close {
    display: none;
}

.nep-select-root {
    position: absolute;
    top: 0;
    left: 0;
}

.nep-select-popover .nep-select-result {
    padding: 14px 14px 8px;
    max-height: 112px;
    max-width: 300px;
}

.nep-select-popover .nep-select-result .nep-select-item {
    max-width: 100%;
    color: $gray-200;
    color: var(--gray-600, $gray-200);
    font-size: 16px;
}

.nep-select-auto-adapt.nep-select-options,
.nep-select-auto-adapt .nep-select-options {
    width: auto;
}

.nep-treeSelect {
    position: relative;
}

.nep-treeSelect-inner {
    width: 100%;
    outline: none;
}

.nep-treeSelect-tree-wrapper {
    color: #ababab;
    color: var(--gray-500, #ababab);
    padding: 8px 8px 4px;
}

.nep-treeSelect-tree-wrapper .nep-tree-node:last-child>div {
    padding-bottom: 4px;
}

.nep-treeSelect-tree-wrapper .nep-tree-node .nep-tree-content {
    white-space: nowrap;
}

.nep-treeSelect-tree-wrapper .nep-treeSelect-single .nep-tree-content {
    cursor: default;
}

.nep-treeSelect-tree-wrapper .nep-treeSelect-single .nep-tree-content:hover {
    background: rgba(2, 17, 85, 0.1);
    background: var(--primary-color-fade-10, rgba(2, 17, 85, 0.1));
}

.nep-treeSelect-result {
    cursor: pointer;
    display: flex;
    overflow: auto;
    max-height: 80px;
    flex-flow: wrap;
    padding: 4px 28px 0 16px;
}

.nep-treeSelect-result span {
    display: inline-block;
}

.nep-treeSelect-result span.nep-treeSelect-ellipsis {
    display: block;
    overflow: hidden;
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.nep-treeSelect-result .nep-thumbnail-small {
    width: 20px;
    height: 20px;
}

.nep-treeSelect-result .nep-treeSelect-ellipsis {
    margin-bottom: 4px;
}

.nep-treeSelect-result .nep-treeSelect-ellipsis:after {
    content: "\feff ";
}

.nep-treeSelect-result .nep-treeSelect-input {
    display: inline-flex;
    min-width: 12px;
    flex: 1;
    margin-bottom: 4px;
    outline: none;
}

.nep-treeSelect-result .nep-treeSelect-input:after {
    content: "\feff ";
}

.nep-treeSelect-result .nep-treeSelect-input.nep-treeSelect-full {
    display: block;
}

.nep-treeSelect-result .nep-treeSelect-item {
    position: relative;
    display: inline-block;
    overflow: hidden;
    max-width: 80%;
    padding: var(--select-result-padding-vertical, 0) var(--select-result-padding-horizontal-16, 20px) var(--select-result-padding-vertical, 0) var(--select-result-padding-horizontal, 4px);
    margin-right: 4px;
    margin-right: var(--select-result-padding-horizontal, 4px);
    margin-bottom: 4px;
    background: #fafafb;
    background: var(--gray-100, #fafafb);
    border-radius: 3px;
    color: inherit;
    text-overflow: ellipsis;
}

.nep-treeSelect-result .nep-treeSelect-item-compressed {
    padding: 0 8px;
    font-size: 80%;
    opacity: 0.9;
    text-overflow: unset;
}

.nep-treeSelect-result .nep-treeSelect-item-compressed>span {
    vertical-align: middle;
    letter-spacing: 2px;
}

.nep-treeSelect-result .nep-treeSelect-item.nep-treeSelect-disabled {
    padding-right: 4px;
    padding-right: var(--select-result-padding-horizontal, 4px);
    cursor: not-allowed;
}

.nep-treeSelect-result .nep-treeSelect-item .nep-treeSelect-close {
    right: 4px;
    right: var(--select-result-padding-horizontal, 4px);
    display: block;
    background-color: transparent;
}

.nep-treeSelect-result .nep-treeSelect-item .nep-treeSelect-close:after,
.nep-treeSelect-result .nep-treeSelect-item .nep-treeSelect-close:before {
    background-color: #ababab;
    background-color: var(--gray-500, #ababab);
    width: 8px;
}

.nep-treeSelect-result .nep-treeSelect-item .nep-treeSelect-close:hover {
    background-color: transparent;
}

.nep-treeSelect-result .nep-treeSelect-item .nep-treeSelect-close:hover:after,
.nep-treeSelect-result .nep-treeSelect-item .nep-treeSelect-close:hover:before {
    background-color: $gray-200;
    background-color: var(--gray-600, $gray-200);
}

.nep-treeSelect-compressed {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
}

.nep-treeSelect-compressed .nep-treeSelect-item {
    padding-right: 4px;
    padding-right: var(--select-result-padding-horizontal, 4px);
}

.nep-treeSelect-indicator {
    position: absolute;
    top: 50%;
    right: 7px;
    display: block;
    width: 12px;
    height: 12px;
    color: #ababab;
    color: var(--gray-500, #ababab);
    transform: translateY(-50%);
}

.nep-treeSelect-indicator.nep-treeSelect-close {
    display: none;
    border-radius: 8px;
}

.nep-treeSelect-indicator.nep-treeSelect-close:after,
.nep-treeSelect-indicator.nep-treeSelect-close:before {
    position: absolute;
    top: 7px;
    left: 1px;
    display: block;
    width: 12px;
    height: 1px;
    background: #717171;
    content: " ";
}

.nep-treeSelect-indicator.nep-treeSelect-close:after {
    transform: rotate(45deg);
}

.nep-treeSelect-indicator.nep-treeSelect-close:before {
    transform: rotate(315deg);
}

.nep-treeSelect-indicator.nep-treeSelect-close:hover:after,
.nep-treeSelect-indicator.nep-treeSelect-close:hover:before {
    background: #4061C7;
    background: var(--primary-color, #4061C7);
}

.nep-treeSelect-indicator.nep-treeSelect-caret:after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 4px;
    content: "";
    border-top: 4px solid;
    border-right: 4px solid transparent;
    border-bottom: 0;
    border-left: 4px solid transparent;
    vertical-align: 3.4px;
    position: absolute;
    top: 3.2px;
    left: 2px;
    margin: 0;
}

.nep-treeSelect-focus .nep-treeSelect-close,
.nep-treeSelect-result:hover .nep-treeSelect-close {
    display: block;
}

.nep-treeSelect-box-list,
.nep-treeSelect-options {
    position: absolute;
    min-width: 100%;
    z-index: 1000;
    left: 0;
    display: none;
    overflow: hidden;
    background: #fff;
    background-clip: padding-box;
    border-radius: 4px;
}

.nep-treeSelect-box-list .nep-treeSelect-disabled,
.nep-treeSelect-box-list .nep-treeSelect-disabled.nep-treeSelect-active,
.nep-treeSelect-box-list .nep-treeSelect-disabled:hover,
.nep-treeSelect-options .nep-treeSelect-disabled,
.nep-treeSelect-options .nep-treeSelect-disabled.nep-treeSelect-active,
.nep-treeSelect-options .nep-treeSelect-disabled:hover {
    background: #f0f0f1;
    background: var(--input-bg-disabled, #f0f0f1);
    cursor: not-allowed;
}

.nep-treeSelect-options span.nep-treeSelect-content-wrapper {
    display: block;
    border-radius: 2px;
    padding: 0 4px;
}

.nep-treeSelect-options span.nep-treeSelect-selected {
    background-color: rgba(2, 17, 85, 0.6);
    background-color: var(--primary-color-fade-60, rgba(2, 17, 85, 0.6));
    color: #fff;
}

.nep-treeSelect-box-list {
    display: flex;
    overflow: hidden;
    min-height: 200px;
    max-height: 300px;
    flex-direction: column;
}

.nep-treeSelect-box-list .nep-treeSelect-header {
    padding: 8px 16px;
    border-bottom: 1px solid #eee;
}

.nep-treeSelect-box-list .nep-treeSelect-box-options {
    overflow: auto;
    flex: 1;
    padding: 8px 0;
}

.nep-treeSelect-box-list .nep-treeSelect-box-options .nep-treeSelect-no-data {
    padding-top: 60px;
    color: #ababab;
    color: var(--gray-500, #ababab);
    text-align: center;
}

.nep-treeSelect-box-list .nep-treeSelect-option {
    display: inline-block;
    padding-right: 16px;
    margin-right: 0;
}

.nep-treeSelect-box-list .nep-treeSelect-filter-input {
    display: inline-flex;
    width: 180px;
    border-width: 1px;
    border-radius: 2px;
    float: right;
}

.nep-treeSelect-box-list .nep-treeSelect-filter-input svg {
    width: 22px;
    height: 22px;
    padding: 4px;
}

.nep-treeSelect-box-list .nep-treeSelect-filter-input svg path {
    fill: #9e9e9e;
}

.nep-treeSelect-drop-down .nep-treeSelect-box-list,
.nep-treeSelect-drop-down .nep-treeSelect-options {
    top: 100%;
    margin-top: 4px;
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.02), 0 6px 12px rgba(0, 0, 0, 0.175);
    transform-origin: 0 0;
}

.nep-treeSelect-drop-up .nep-treeSelect-box-list,
.nep-treeSelect-drop-up .nep-treeSelect-options {
    bottom: 100%;
    margin-bottom: 4px;
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1), 0 -2px 12px rgba(0, 0, 0, 0.175);
    transform-origin: 0 100%;
}

.nep-treeSelect-option {
    position: relative;
    display: block;
    overflow: hidden;
    padding: 8px 28px 8px 16px;
    color: #55565a;
    color: var(--gray-800, #55565a);
    font-size: 14px;
    line-height: 1.42857143;
    text-overflow: ellipsis;
    transition: none;
    white-space: nowrap;
}

.nep-treeSelect-option.nep-treeSelect-active {
    background-color: #f5f5f5;
    background-color: var(--select-item-active-bg, #f5f5f5);
    color: #49494d;
    color: var(--gray-800-darken-5, #49494d);
    text-decoration: none;
}

.nep-treeSelect-option.nep-treeSelect-active:after {
    position: absolute;
    top: 50%;
    right: 8px;
    width: 12px;
    height: 12px;
    background: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='#4061C7' d='M913.017 237.02c-25.311-25.312-66.349-25.312-91.66 0l-412.475 412.474-206.237-206.237c-25.312-25.312-66.35-25.312-91.661 0s-25.312 66.35 0 91.66l252.067 252.067c0.729 0.73 1.439 1.402 2.134 2.029 25.434 23.257 64.913 22.585 89.527-2.029l458.303-458.303c25.313-25.312 25.313-66.35 0.001-91.661z'%3E%3C/path%3E%3C/svg%3E");
    background: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='var(--primary-color, #4061C7)' d='M913.017 237.02c-25.311-25.312-66.349-25.312-91.66 0l-412.475 412.474-206.237-206.237c-25.312-25.312-66.35-25.312-91.661 0s-25.312 66.35 0 91.66l252.067 252.067c0.729 0.73 1.439 1.402 2.134 2.029 25.434 23.257 64.913 22.585 89.527-2.029l458.303-458.303c25.313-25.312 25.313-66.35 0.001-91.661z'%3E%3C/path%3E%3C/svg%3E");
    color: #4061C7;
    color: var(--primary-color, #4061C7);
    content: " ";
    transform: translateY(-50%);
}

.nep-treeSelect-option:hover {
    color: #55565a;
    color: var(--gray-800, #55565a);
}

.nep-treeSelect-option.nep-treeSelect-disabled,
.nep-treeSelect-option.nep-treeSelect-disabled.nep-treeSelect-active,
.nep-treeSelect-option.nep-treeSelect-disabled:hover {
    background: #f0f0f1;
    background: var(--input-bg-disabled, #f0f0f1);
    cursor: not-allowed;
}

.nep-treeSelect-control-mouse .nep-treeSelect-option:hover {
    background-color: #f5f5f5;
    background-color: var(--select-option-hover-bg, #f5f5f5);
    color: #4061C7;
    color: var(--select-option-hover-color, #4061C7);
    text-decoration: none;
}

.nep-treeSelect-control-mouse .nep-treeSelect-option.nep-treeSelect-disabled:hover {
    background: #f0f0f1;
    background: var(--input-bg-disabled, #f0f0f1);
}

.nep-treeSelect-control-keyboard .nep-treeSelect-option.nep-treeSelect-hover {
    background-color: #f5f5f5;
    background-color: var(--select-option-hover-bg, #f5f5f5);
    color: #4061C7;
    color: var(--select-option-hover-color, #4061C7);
    text-decoration: none;
}

.nep-treeSelect-control-keyboard .nep-treeSelect-option {
    cursor: none;
}

span.nep-treeSelect-option {
    color: #ababab;
    color: var(--gray-500, #ababab);
}

.nep-treeSelect-small .nep-treeSelect-result {
    padding: 2px 24px 0 12px;
}

.nep-treeSelect-small .nep-treeSelect-result .nep-treeSelect-ellipsis,
.nep-treeSelect-small .nep-treeSelect-result .nep-treeSelect-input,
.nep-treeSelect-small .nep-treeSelect-result .nep-treeSelect-item {
    margin-bottom: 2px;
}

.nep-treeSelect-large .nep-treeSelect-result {
    padding: 8px 32px 0 20px;
}

.nep-treeSelect-large .nep-treeSelect-result .nep-treeSelect-ellipsis,
.nep-treeSelect-large .nep-treeSelect-result .nep-treeSelect-input,
.nep-treeSelect-large .nep-treeSelect-result .nep-treeSelect-item {
    margin-bottom: 8px;
}

.nep-treeSelect-multiple .nep-treeSelect-result {
    padding-left: 8px;
    padding-right: 38px;
}

.nep-treeSelect-multiple .nep-treeSelect-result a.nep-treeSelect-close.nep-treeSelect-indicator {
    right: 17px;
}

.nep-treeSelect-multiple .nep-treeSelect-compressed {
    padding-left: 8px;
    padding-right: 28px;
}

.nep-treeSelect-multiple .nep-treeSelect-compressed a.nep-treeSelect-close.nep-treeSelect-indicator {
    right: 7px;
}

.nep-treeSelect-large.nep-treeSelect-multiple .nep-treeSelect-result {
    padding-left: 10px;
}

.nep-treeSelect-small.nep-treeSelect-multiple .nep-treeSelect-result {
    padding-left: 6px;
}

.nep-treeSelect-disabled .nep-treeSelect-result {
    cursor: not-allowed;
}

.nep-treeSelect-disabled .nep-treeSelect-result .nep-treeSelect-item {
    padding-right: 4px;
    padding-right: var(--select-result-padding-horizontal, 4px);
    background: #f0f0f1;
    background: var(--input-bg-disabled, #f0f0f1);
}

.nep-treeSelect-disabled .nep-treeSelect-result .nep-treeSelect-item .nep-treeSelect-close {
    display: none;
}

.nep-modal {
    position: fixed;
    z-index: 1050;
    top: 0;
    left: 0;
    overflow: auto;
    width: 100%;
    height: 100%;
    text-align: center;
    opacity: 0;
    transition: opacity 0.2s linear;
}

.nep-modal-panel {
    text-align: left;
    margin: 0 auto;
    transform: translateY(-100px);
    transition: transform 0.2s linear;
}

.nep-modal-title {
    color: rgba(0, 0, 0, 0.85);
    color: var(--modal-title-color, rgba(0, 0, 0, 0.85));
    font-size: 24px;
    font-size: var(--modal-title-font-size, 24px);
    font-weight: 500;
}

.nep-modal-title-custom {
    color: rgba(0, 0, 0, 0.85);
    color: var(--modal-title-color, rgba(0, 0, 0, 0.85));
    font-size: 14px !important;
    font-family: 'Helvetica Neue LT W05_65 Medium',Arial, Verdana, Tahoma,sans-serif!important;
    font-weight: 500;
}

.nep-modal .nep-modal-body {
    padding: 40px 40px 20px 70px;
}

.nep-modal .nep-modal-body .nep-modal-title {
    margin-bottom: 8px;
}

.nep-modal .nep-modal-body .nep-modal-icon {
    position: absolute;
    top: 40px;
    left: 32px;
    width: 24px;
    width: var(--modal-icon-size, 24px);
    height: 24px;
    height: var(--modal-icon-size, 24px);
}

.nep-modal-show {
    opacity: 1;
}

.nep-modal-show .nep-modal-panel {
    transform: translate(0);
}

.nep-modal-right {
    top: 0;
    right: 0;
    bottom: 0;
    margin: 0;
    transform: translate(100%);
}

.nep-modal-left {
    top: 0;
    bottom: 0;
    left: 0;
    margin: 0;
    transform: translate(-100%);
}

.nep-modal-top {
    top: 0;
    right: 0;
    left: 0;
    margin: 0;
    transform: translateY(-100%);
}

.nep-modal-bottom {
    right: 0;
    bottom: 0;
    left: 0;
    margin: 0;
    transform: translateY(100%);
}

.nep-modal-bottom,
.nep-modal-top {
    max-width: 100%;
}

.nep-modal-success .nep-modal-icon path {
    fill: #388e3c;
    fill: var(--success-color, #388e3c);
}

.nep-modal-info .nep-modal-icon path {
    fill: #4061C7;
    fill: var(--primary-color, #4061C7);
}

.nep-modal-confirm .nep-modal-icon path,
.nep-modal-warning .nep-modal-icon path {
    fill: #c68700;
    fill: var(--warning-color, #c68700);
}

.nep-modal-error .nep-modal-icon path {
    fill: #c62828;
}

.nep-modal-confirm .nep-modal-footer,
.nep-modal-error .nep-modal-footer,
.nep-modal-info .nep-modal-footer,
.nep-modal-normal .nep-modal-footer,
.nep-modal-success .nep-modal-footer,
.nep-modal-warning .nep-modal-footer {
    border-top: 0;
}

.nep-modal-close {
    position: absolute;
    z-index: 100;
    top: 13px;
    right: 16px;
    display: block;
    width: 10px;
    height: 10px;
    padding: 0;
}

.nep-modal-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.nep-card {
    position: relative;
    display: flex;
    min-width: 0;
    flex-direction: column;
    border: var(--card-border-width, 1px) solid var(--card-border-color, #e0e0e0);
    background-clip: border-box;
    background-color: #fff;
    border-radius: 4px;
    word-wrap: break-word;
    color: var(--card-color, "inherit");
    font-size: var(--card-font-size, "inherit");
}

.nep-card-hover:hover,
.nep-card-shadow {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    box-shadow: var(--card-box-shadow, 0 2px 8px rgba(0, 0, 0, 0.1));
}

.nep-card-header {
    position: relative;
    z-index: 10;
    padding: 16px 24px;
    margin-bottom: -1px;
    border-bottom: 1px solid #e0e0e0;
    border-bottom: var(--card-divider-width, 1px) solid #e0e0e0;
    background: #EBF3FF !important;
    color: #1A1A1A !important;
    font-family: "Helvetica Neue LT W05_65 Medium", sans-serif;
    font-weight: 500;
    font-size: 1rem;
    line-height: 1.5rem;
}

.nep-card-header .nep-card-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 12px;
}

.nep-card-header .nep-card-indicator svg {
    transform: rotate(90deg);
    transition: transform 0.2s linear;
}

.nep-card-collapsible .nep-card-header {
    cursor: pointer;
}

.nep-card-collapsed {
    overflow: hidden;
}

.nep-card-collapsed .nep-card-indicator svg {
    transform: rotate(0deg);
}

.nep-card-accordion {
    margin-bottom: -1px;
}

.nep-card-accordion .nep-card-header {
    background: #fafafb;
    background: var(--gray-100, #fafafb);
}

.nep-card-body {
    flex: 1 1 auto;
    padding: 16px 20px;
    position: relative;
}

.nep-card-body:hover .nep-card-foldup {
    background: #dedfe0;
    background: var(--gray-300, #dedfe0);
}

.nep-card-body:hover .nep-card-foldup>span {
    margin-bottom: 1px;
    border-bottom-color: #fff;
}

.nep-card-body .nep-card-foldup {
    cursor: pointer;
    position: absolute;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    overflow: hidden;
    bottom: 0;
    left: 50%;
    text-align: center;
    transition: all 0.3s;
    transform: translate(-50%, 50%);
}

.nep-card-body .nep-card-foldup>span {
    transition: all 0.3s;
    display: inline-block;
    margin-bottom: 2px;
    border-top: 0;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #e0e0e0;
    border-left: 6px solid transparent;
}

.nep-card-footer {
    padding: 16px 24px;
    border-top: 1px solid #e0e0e0;
    border-top: var(--card-divider-width, 1px) solid #e0e0e0;
    background-color:#FFFFFF;
}

.nep-card-center {
    text-align: center;
}

.nep-card-right {
    text-align: right;
}

.nep-card>div:first-child {
    border-radius: 6px 6px 0 0;
}

.nep-card>div:last-child {
    border-radius: 0 0 6px 6px;
}

.nep-datepicker:focus {
    outline: none;
}

label.nep-datepicker {
    display: inline-block;
    width: 150px;
}

label.nep-datepicker-c-datetime {
    width: 240px;
}

label.nep-datepicker-c-time {
    width: 120px;
}

label.nep-datepicker-r-date,
label.nep-datepicker-r-month,
label.nep-datepicker-r-week {
    width: 300px;
}

label.nep-datepicker-r-datetime {
    width: 420px;
}

label.nep-datepicker-r-time {
    width: 250px;
}

.nep-datepicker-title {
    padding: 0 0 8px;
    text-align: center;
}

.nep-datepicker-inner {
    width: 100%;
    padding: 4px 28px 4px 16px;
    outline: none;
}

.nep-datepicker-size-small.nep-datepicker-inner {
    padding: 2px 24px 2px 12px;
}

.nep-datepicker-size-large.nep-datepicker-inner {
    padding: 8px 32px 8px 20px;
}

.nep-datepicker-result {
    position: relative;
    display: flex;
    cursor: pointer;
}

.nep-datepicker-result .nep-datepicker-icon {
    position: absolute;
    top: 50%;
    right: 0;
    width: 14px;
    height: 14px;
    margin: -9px -16px 0 0;
    font-size: 14px;
}

.nep-datepicker-result .nep-datepicker-icon path {
    fill: #ababab;
    fill: var(--gray-500, #ababab);
}

.nep-datepicker-result .nep-datepicker-close {
    display: none;
}

.nep-datepicker-result .nep-datepicker-txt {
    flex: 1;
    outline: none;
}

.nep-datepicker-result .nep-datepicker-txt:after {
    content: "\feff ";
}

.nep-datepicker-disabled .nep-datepicker-result {
    cursor: not-allowed;
}

.nep-datepicker-range .nep-datepicker-result .nep-datepicker-txt {
    text-align: center;
}

.nep-datepicker-txt[contenteditable="true"] {
    cursor: text;
}

.nep-datepicker-separate {
    margin: 0 8px;
}

.nep-datepicker-text-focus {
    background: #4061C7;
    background: var(--primary-color, #4061C7);
    color: #fff;
    border-radius: 50px;
}

.nep-datepicker:hover .nep-datepicker-result .nep-datepicker-indecator {
    display: none;
}

.nep-datepicker:hover .nep-datepicker-result .nep-datepicker-close {
    display: block;
}

.nep-datepicker-picker {
    position: absolute;
    z-index: 1000;
    padding: 12px;
    background: #fff;
    background-clip: padding-box;
    border-radius: 4px;
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.02), 0 6px 12px rgba(0, 0, 0, 0.175);
}

.nep-datepicker-picker span {
    color: $gray-200;
    color: var(--gray-600, $gray-200);
}

.nep-datepicker-picker .nep-datepicker-header {
    display: flex;
    padding: 4px 8px;
}

.nep-datepicker-picker .nep-datepicker-header span {
    cursor: pointer;
    line-height: 1;
}

.nep-datepicker-picker .nep-datepicker-header span:hover {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-datepicker-picker .nep-datepicker-header .nep-datepicker-icon {
    width: 18px;
    height: 16px;
    padding: 0 4px;
}

.nep-datepicker-picker .nep-datepicker-header .nep-datepicker-icon path {
    fill: $gray-200;
    fill: var(--gray-600, $gray-200);
}

.nep-datepicker-picker .nep-datepicker-header .nep-datepicker-icon:hover path {
    fill: #4061C7;
    fill: var(--primary-color, #4061C7);
}

.nep-datepicker-picker .nep-datepicker-header .nep-datepicker-icon.nep-datepicker-disabled {
    cursor: not-allowed;
}

.nep-datepicker-picker .nep-datepicker-header .nep-datepicker-icon.nep-datepicker-disabled path {
    fill: #cac9c7;
    fill: var(--gray-400, #cac9c7);
}

.nep-datepicker-picker .nep-datepicker-header .nep-datepicker-ym {
    flex: 1;
    text-align: center;
}

.nep-datepicker-picker .nep-datepicker-header .nep-datepicker-ym span {
    padding: 0 6px;
}

.nep-datepicker-picker .nep-datepicker-header .nep-datepicker-ym+.nep-datepicker-ym {
    text-align: left;
}

.nep-datepicker-picker span.nep-datepicker-today {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
    background: #fff;
    box-shadow: inset 0 0 0 1px #4061C7;
    box-shadow: 0 0 0 1px var(--primary-color, #4061C7) inset;
}

.nep-datepicker-picker .nep-datepicker-list span:hover {
    background: #fafafb;
    background: var(--gray-100, #fafafb);
    color: $nep-black;
}

.nep-datepicker-picker .nep-datepicker-list span:hover.nep-datepicker-today {
    background: #fff;
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-datepicker-picker span.nep-datepicker-active,
.nep-datepicker-picker span.nep-datepicker-active:hover,
.nep-datepicker-picker span.nep-datepicker-active:hover.nep-datepicker-today {
    background: #4061C7;
    background: var(--primary-color, #4061C7);
    color: #fff;
}

.nep-datepicker-left-bottom .nep-datepicker-picker {
    top: 100%;
    left: 0;
    margin-top: 4px;
}

.nep-datepicker-left-top .nep-datepicker-picker {
    left: 0;
}

.nep-datepicker-left-top .nep-datepicker-picker,
.nep-datepicker-right-top .nep-datepicker-picker {
    bottom: 100%;
    margin-bottom: 4px;
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.02), 0 -2px 12px rgba(0, 0, 0, 0.175);
}

.nep-datepicker-right-top .nep-datepicker-picker {
    right: 0;
}

.nep-datepicker-right-bottom .nep-datepicker-picker {
    top: 100%;
    right: 0;
    margin-top: 4px;
}

.nep-datepicker-day-picker,
.nep-datepicker-month-picker,
.nep-datepicker-year-picker {
    width: 252px;
}

.nep-datepicker-day-picker span,
.nep-datepicker-month-picker span,
.nep-datepicker-year-picker span {
    user-select: none;
}

.nep-datepicker-range-picker {
    display: flex;
}

.nep-datepicker-range-picker>div:nth-last-child(2) {
    margin-right: 12px;
}

.nep-datepicker-range-picker>div:last-child {
    position: relative;
}

.nep-datepicker-range-picker>div:last-child:before {
    content: " ";
    position: absolute;
    left: -6px;
    height: 100%;
    border-left: 1px solid #e0e0e0;
    border-left: 1px solid var(--table-border-color, #e0e0e0);
}

.nep-datepicker-day-picker {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.nep-datepicker-day-picker .nep-datepicker-week {
    margin-bottom: 4px;
    border-bottom: 1px solid #f2f2f2;
    text-align: center;
}

.nep-datepicker-day-picker .nep-datepicker-week span {
    display: inline-block;
    width: 28px;
    margin: 8px 4px;
}

.nep-datepicker-day-picker .nep-datepicker-list {
    position: relative;
    z-index: 10;
    background: #fff;
    text-align: center;
}

.nep-datepicker-day-picker .nep-datepicker-list div {
    display: inline-block;
    width: 36px;
    height: 32px;
    padding: 2px 0;
    margin-bottom: 4px;
}

.nep-datepicker-day-picker .nep-datepicker-list div.nep-datepicker-active,
.nep-datepicker-day-picker .nep-datepicker-list div.nep-datepicker-hover {
    background: #fafafb;
    background: var(--gray-100, #fafafb);
}

.nep-datepicker-day-picker .nep-datepicker-list div.nep-datepicker-hover-start {
    border-bottom-left-radius: 16px;
    border-top-left-radius: 16px;
}

.nep-datepicker-day-picker .nep-datepicker-list div.nep-datepicker-hover-end {
    border-bottom-right-radius: 16px;
    border-top-right-radius: 16px;
}

.nep-datepicker-day-picker .nep-datepicker-list div.nep-datepicker-active.nep-datepicker-hover-end span,
.nep-datepicker-day-picker .nep-datepicker-list div.nep-datepicker-active.nep-datepicker-hover-start span {
    background: #4061C7;
    background: var(--primary-color, #4061C7);
    color: #fff;
}

.nep-datepicker-day-picker .nep-datepicker-list span {
    display: block;
    width: 28px;
    height: 28px;
    margin: 0 auto;
    border-radius: 14px;
    cursor: pointer;
    font-size: 13px;
    line-height: 28px;
    text-align: center;
}

.nep-datepicker-day-picker .nep-datepicker-list .nep-datepicker-disabled {
    color: #cac9c7;
    color: var(--gray-400, #cac9c7);
    cursor: not-allowed;
}

.nep-datepicker-day-picker .nep-datepicker-list .nep-datepicker-disabled.nep-datepicker-today {
    box-shadow: inset 0 0 0 1px #cac9c7;
    box-shadow: 0 0 0 1px var(--gray-400, #cac9c7) inset;
}

.nep-datepicker-day-picker .nep-datepicker-list .nep-datepicker-disabled.nep-datepicker-today:hover,
.nep-datepicker-day-picker .nep-datepicker-list .nep-datepicker-other-month {
    color: #cac9c7;
    color: var(--gray-400, #cac9c7);
}

.nep-datepicker-day-picker .nep-datepicker-datetime {
    position: relative;
    padding-top: 12px;
    border-top: 1px solid #f2f2f2;
    text-align: center;
}

.nep-datepicker-day-picker .nep-datepicker-datetime .nep-datepicker-time-picker {
    position: absolute;
    z-index: 0;
    bottom: 100%;
    left: 50%;
    background: #fff;
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.02), 0 -2px 12px rgba(0, 0, 0, 0.175);
    transform: translate(-50%);
    visibility: hidden;
}

.nep-datepicker-day-picker .nep-datepicker-datetime:hover .nep-datepicker-time-picker {
    z-index: 100;
    visibility: visible;
}

.nep-datepicker-month-picker .nep-datepicker-list {
    padding-top: 12px;
    text-align: center;
}

.nep-datepicker-month-picker .nep-datepicker-list span {
    display: inline-block;
    width: 70px;
    padding: 4px 6px;
    margin: 6px 5px;
    border-radius: 2px;
    border-radius: var(--datepicker-rect-active-border-radius, 2px);
    cursor: pointer;
}

.nep-datepicker-month-picker .nep-datepicker-list .nep-datepicker-disabled {
    color: #cac9c7;
    color: var(--gray-400, #cac9c7);
    cursor: not-allowed;
}

.nep-datepicker-year-picker .nep-datepicker-list {
    padding-top: 12px;
    text-align: center;
}

.nep-datepicker-year-picker .nep-datepicker-list span {
    display: inline-block;
    width: 50px;
    padding: 4px 6px;
    margin: 4px 15px;
    border-radius: 2px;
    border-radius: var(--datepicker-rect-active-border-radius, 2px);
    cursor: pointer;
}

.nep-datepicker-time-picker {
    width: auto;
    font-size: 0;
    white-space: nowrap;
}

.nep-datepicker-time-picker .nep-datepicker-time-list {
    position: relative;
    display: inline-block;
    overflow: hidden;
    width: 65px;
    height: 150px;
    box-sizing: border-box;
    flex-direction: column;
    border-right: 1px solid #f0f0f1;
    border-right: 1px solid var(--gray-200, #f0f0f1);
    background: linear-gradient(180deg, var(--gray-100, #fafafb), #fafafb), 30px, transparent 30px, transparent 0 60px;
    background: linear-gradient( 180deg, var(--gray-100, #fafafb), var(--gray-100, #fafafb), 30px, transparent 30px, transparent) 0 60px;
    font-size: 14px;
    text-align: left;
}

.nep-datepicker-time-picker .nep-datepicker-time-list:last-child {
    border-right: 0;
}

.nep-datepicker-time-picker .nep-datepicker-time-list .nep-datepicker-pad {
    height: 60px;
}

.nep-datepicker-time-picker .nep-datepicker-time-list a,
.nep-datepicker-time-picker .nep-datepicker-time-list span {
    display: block;
    padding-left: 18px;
    color: #eee;
    line-height: 30px;
    height: 30px;
}

.nep-datepicker-time-picker .nep-datepicker-time-list .nep-datepicker-time-active {
    color: $nep-black;
    font-weight: 700;
}

.nep-datepicker-time-picker .nep-datepicker-time-list:hover {
    overflow: auto;
}

.nep-datepicker-absolute .nep-datepicker-absolute-left-top,
.nep-datepicker-absolute .nep-datepicker-absolute-right-top {
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.02), 0 -2px 12px rgba(0, 0, 0, 0.175);
}

.nep-datepicker-quick-select {
    min-width: 80px;
    border-right: 1px solid #dedfe0;
    border-right: 1px solid var(--gray-300, #dedfe0);
}

.nep-datepicker-quick-select-item {
    margin: 12px 0;
    cursor: pointer;
}

.nep-datepicker-quick-select-item:first-child {
    margin-top: 0;
}

.nep-datepicker-quick-select-item-active {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
    border-right: 2px solid #4061C7;
    border-right: 2px solid var(--primary-color, #4061C7);
}

.nep-rate {
    position: relative;
    white-space: nowrap;
}

@keyframes nep-rate-highlight {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(1.6);
    }
}

.nep-rate-background {
    position: relative;
    z-index: 0;
}

.nep-rate-background>span {
    color: #cac9c7;
    color: var(--gray-400, #cac9c7);
}

.nep-rate-front,
.nep-rate-static {
    position: absolute;
    z-index: 1;
}

.nep-rate-front>span,
.nep-rate-static>span {
    position: relative;
    color: #fadb14;
}

.nep-rate-background span,
.nep-rate-front span {
    cursor: pointer;
}

.nep-rate-background,
.nep-rate-front,
.nep-rate-static {
    top: 0;
    left: 0;
}

.nep-rate-background>span,
.nep-rate-front>span,
.nep-rate-static>span {
    display: inline-block;
    margin-right: 6px;
    vertical-align: middle;
}

.nep-rate-background>span:last-child,
.nep-rate-front>span:last-child,
.nep-rate-static>span:last-child {
    margin-right: 0;
}

.nep-rate-highlight {
    position: absolute;
    top: 0;
    left: 0;
    animation: nep-rate-highlight 0.3s ease-out;
    font-style: normal;
}

span.nep-rate-text {
    margin-left: 4px;
    color: $gray-200;
    color: var(--gray-600, $gray-200);
    font-size: 14px;
}

.nep-image {
    position: relative;
    display: inline-block;
    overflow: hidden;
}

.nep-image>* {
    position: absolute;
    top: 0;
    left: 0;
}

.nep-image-inner {
    right: 0;
    bottom: 0;
}

.nep-image img {
    max-width: 100%;
    max-height: 100%;
}

.nep-image-mask {
    display: flex;
    background: #fafafb;
    background: var(--gray-100, #fafafb);
    color: #ababab;
    color: var(--gray-500, #ababab);
}

.nep-image-mask div {
    margin: auto;
}

.nep-image-rounded {
    border-radius: 4px;
}

.nep-image-circle {
    border-radius: 50%;
}

.nep-image-thumbnail {
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.075);
    transition: all 0.2s ease-in-out;
}

.nep-image-thumbnail .nep-image-inner {
    top: 3px;
    right: 3px;
    bottom: 3px;
    left: 3px;
}

.nep-image-fill .nep-image-inner,
.nep-image-fit .nep-image-inner {
    background-position: 50% 50%;
    background-repeat: no-repeat;
}

.nep-image-fill .nep-image-inner {
    background-size: cover;
}

.nep-image-center .nep-image-inner {
    display: flex;
}

.nep-image-center .nep-image-inner img {
    margin: auto;
}

.nep-image-stretch .nep-image-inner img {
    width: 100%;
    height: 100%;
}

.nep-image-group .nep-image {
    margin-right: 8px;
}

.nep-image-pile.nep-image-group {
    position: relative;
}

.nep-image-pile.nep-image-group .nep-image {
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    display: none;
}

.nep-image-pile.nep-image-group .nep-image:first-child {
    position: relative;
    z-index: 10;
    display: inline-block;
}

.nep-image-pile.nep-image-group .nep-image:nth-child(2) {
    top: 8px;
    left: 8px;
    display: inline-block;
}

.nep-image-gallery {
    position: fixed;
    z-index: 1100;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.nep-image-gallery .nep-image-center,
.nep-image-gallery .nep-image-left,
.nep-image-gallery .nep-image-right {
    position: absolute;
    z-index: 10;
    top: 50%;
    background: #fff;
}

.nep-image-gallery .nep-image-center>div,
.nep-image-gallery .nep-image-left>div,
.nep-image-gallery .nep-image-right>div {
    box-sizing: content-box;
    border: 10px solid #fff;
}

.nep-image-gallery .nep-image-center {
    z-index: 20;
    min-width: 100px;
    min-height: 100px;
}

.nep-image-gallery .nep-image-left:hover,
.nep-image-gallery .nep-image-right:hover {
    cursor: pointer;
    opacity: 1;
}

@keyframes nep-image-kf-f2c {
    0% {
        left: 100%;
        margin-left: -80px;
        transform: translateY(-50%);
    }
    to {
        left: 50%;
        margin-left: 0;
        transform: translate(-50%, -50%);
    }
}

@keyframes nep-image-kf-c2b {
    0% {
        right: 50%;
        margin-right: 0;
        transform: translate(50%, -50%);
    }
    to {
        right: 100%;
        margin-right: -80px;
        transform: translateY(-50%);
    }
}

@keyframes nep-image-kf-c2f {
    0% {
        left: 50%;
        margin-left: 0;
        transform: translate(-50%, -50%);
    }
    to {
        left: 100%;
        margin-left: -80px;
        transform: translateY(-50%);
    }
}

@keyframes nep-image-kf-b2c {
    0% {
        right: 100%;
        margin-right: -80px;
        transform: translateY(-50%);
    }
    to {
        right: 50%;
        margin-right: 0;
        transform: translate(50%, -50%);
    }
}

.nep-image-gallery .nep-image-center.nep-image-init {
    left: 50%;
    transform: translate(-50%, -50%);
}

.nep-image-gallery .nep-image-left.nep-image-backward,
.nep-image-gallery .nep-image-left.nep-image-init {
    right: 100%;
    margin-right: -80px;
    opacity: 0.4;
    transform: translateY(-50%);
}

.nep-image-gallery .nep-image-right.nep-image-forward,
.nep-image-gallery .nep-image-right.nep-image-init {
    left: 100%;
    margin-left: -80px;
    opacity: 0.4;
    transform: translateY(-50%);
}

.nep-image-gallery .nep-image-center.nep-image-forward {
    animation: nep-image-kf-f2c 0.42s linear;
}

.nep-image-gallery .nep-image-left.nep-image-forward {
    animation: nep-image-kf-c2b 0.42s linear;
}

.nep-image-gallery .nep-image-center.nep-image-backward {
    animation: nep-image-kf-b2c 0.42s linear;
}

.nep-image-gallery .nep-image-right.nep-image-backward {
    animation: nep-image-kf-c2f 0.42s linear;
}

.nep-image-gallery .nep-image-overlay {
    position: absolute;
    z-index: 0;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
}

.nep-image-gallery .nep-image-close {
    position: absolute;
    top: -14px;
    right: -10px;
    width: 24px;
    height: 24px;
    border: 1px solid #fff;
    background: $nep-black;
    border-radius: 12px;
    box-shadow: 0 1px 2px 2px rgba(0, 0, 0, 0.3);
}

.nep-image-gallery .nep-image-close svg {
    position: absolute;
    top: 5px;
    left: 5px;
    width: 12px;
    height: 12px;
    fill: #fff;
}

.nep-image-gallery img {
    display: block;
}

.nep-image-magnify>img {
    background: #fff;
    position: relative;
    z-index: 2;
}

.nep-image-magnify-loading {
    z-index: 1;
    display: inline-block;
    margin: auto;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 30px;
    height: 30px;
    pointer-events: none;
}

.nep-image-left .nep-image-magnify-loading,
.nep-image-right .nep-image-magnify-loading {
    display: none;
}

.nep-tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    font-size: 12px;
    word-wrap: break-word;
}

@keyframes nep-tooltip-kf-opacity {
    0% {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes nep-tooltip-kf-top {
    0% {
        margin-top: 0;
    }
    to {
        margin-top: -8px;
    }
}

@keyframes nep-tooltip-kf-bottom {
    0% {
        margin-top: 0;
    }
    to {
        margin-top: 8px;
    }
}

@keyframes nep-tooltip-kf-left {
    0% {
        margin-left: 0;
    }
    to {
        margin-left: -8px;
    }
}

@keyframes nep-tooltip-kf-right {
    0% {
        margin-left: 0;
    }
    to {
        margin-left: 8px;
    }
}

.nep-tooltip-animation {
    transition: opacity 0.3 ease, transform 0.3s cubic-bezier(0.71, 1.7, 0.77, 1.24);
}

.nep-tooltip-arrow {
    position: absolute;
    z-index: 1;
    display: inline-block;
    width: 0;
    height: 0;
}

.nep-tooltip-inner {
    position: relative;
    z-index: 2;
    max-width: 200px;
    padding: var(--tooltip-padding-vertical, 3px) var(--tooltip-padding-horizontal, 8px);
    background-color: #75787b;
    background-color: var(--tooltip-bg, #75787b);
    border-radius: 4px;
    color: #fff;
    text-align: center;
}

.nep-tooltip-top {
    margin-top: -8px;
    transform: translate(-50%, -100%);
}

.nep-tooltip-top .nep-tooltip-arrow {
    top: 100%;
    left: 50%;
    border-top: 5px solid #75787b;
    border-top: 5px solid var(--tooltip-bg, #75787b);
    border-right: 5px solid transparent;
    border-left: 5px solid transparent;
    transform: translate(-50%);
}

.nep-tooltip-top.nep-tooltip-animation {
    animation: nep-tooltip-kf-opacity 0.3s ease, nep-tooltip-kf-top 0.3s cubic-bezier(0.71, 1.7, 0.77, 1.24);
}

.nep-tooltip-right {
    margin-left: 8px;
    transform: translateY(-50%);
}

.nep-tooltip-right .nep-tooltip-arrow {
    top: 50%;
    right: 100%;
    border-top: 5px solid transparent;
    border-right: 5px solid #75787b;
    border-right: 5px solid var(--tooltip-bg, #75787b);
    border-bottom: 5px solid transparent;
    transform: translateY(-50%);
}

.nep-tooltip-right.nep-tooltip-animation {
    animation: nep-tooltip-kf-opacity 0.3s ease, nep-tooltip-kf-right 0.3s cubic-bezier(0.71, 1.7, 0.77, 1.24);
}

.nep-tooltip-left {
    margin-left: -8px;
    transform: translate(-100%, -50%);
}

.nep-tooltip-left .nep-tooltip-arrow {
    top: 50%;
    left: 100%;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 5px solid #75787b;
    border-left: 5px solid var(--tooltip-bg, #75787b);
    transform: translateY(-50%);
}

.nep-tooltip-left.nep-tooltip-animation {
    animation: nep-tooltip-kf-opacity 0.3s ease, nep-tooltip-kf-left 0.3s cubic-bezier(0.71, 1.7, 0.77, 1.24);
}

.nep-tooltip-bottom {
    margin-top: 8px;
    transform: translateX(-50%);
}

.nep-tooltip-bottom .nep-tooltip-arrow {
    top: 0;
    left: 50%;
    border-right: 5px solid transparent;
    border-bottom: 5px solid #75787b;
    border-bottom: 5px solid var(--tooltip-bg, #75787b);
    border-left: 5px solid transparent;
    transform: translate(-50%, -100%);
}

.nep-tooltip-bottom.nep-tooltip-animation {
    animation: nep-tooltip-kf-opacity 0.3s ease, nep-tooltip-kf-bottom 0.3s cubic-bezier(0.71, 1.7, 0.77, 1.24);
}

.nep-popover {
    position: absolute;
    z-index: 1060;
    display: block;
    border: var(--popover-border-width, 1px) solid var(--popover-border-color, #dedfe0);
    background-clip: padding-box;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    box-shadow: var(--popover-box-shadow, 0 5px 10px rgba(0, 0, 0, 0.2));
    font-size: 12px;
    word-wrap: break-word;
}

@keyframes nep-popover-kf-opacity {
    0% {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.nep-popover-arrow {
    position: absolute;
    z-index: 1;
    display: inline-block;
    padding: 5.6px;
    border: var(--popover-border-width, 1px) solid var(--popover-border-color, #dedfe0);
    background-color: #fff;
}

.nep-popover-top,
.nep-popover-top-left,
.nep-popover-top-right {
    margin-top: -12px;
    transform: translate(-50%, -100%);
}

.nep-popover-top-left .nep-popover-arrow,
.nep-popover-top-right .nep-popover-arrow,
.nep-popover-top .nep-popover-arrow {
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 50%) rotate(135deg);
}

.nep-popover-top-left {
    transform: translateY(-100%);
}

.nep-popover-top-left .nep-popover-arrow {
    left: 16px;
}

.nep-popover-top-right {
    transform: translate(-100%, -100%);
}

.nep-popover-top-right .nep-popover-arrow {
    right: 8px;
    left: auto;
}

.nep-popover-left,
.nep-popover-left-bottom,
.nep-popover-left-top {
    margin-left: -12px;
    transform: translate(-100%, -50%);
}

.nep-popover-left-bottom .nep-popover-arrow,
.nep-popover-left-top .nep-popover-arrow,
.nep-popover-left .nep-popover-arrow {
    top: 50%;
    right: 0;
    transform: translate(50%, -50%) rotate(45deg);
}

.nep-popover-left-top {
    transform: translateX(-100%);
}

.nep-popover-left-top .nep-popover-arrow {
    top: 16px;
}

.nep-popover-left-bottom {
    transform: translate(-100%, -100%);
}

.nep-popover-left-bottom .nep-popover-arrow {
    top: auto;
    bottom: 8px;
}

.nep-popover-right,
.nep-popover-right-bottom,
.nep-popover-right-top {
    margin-left: 12px;
    transform: translateY(-50%);
}

.nep-popover-right-bottom .nep-popover-arrow,
.nep-popover-right-top .nep-popover-arrow,
.nep-popover-right .nep-popover-arrow {
    top: 50%;
    left: 0;
    transform: translate(-50%, -50%) rotate(225deg);
}

.nep-popover-right-top {
    transform: translateY(0);
}

.nep-popover-right-top .nep-popover-arrow {
    top: 16px;
}

.nep-popover-right-bottom {
    transform: translateY(-100%);
}

.nep-popover-right-bottom .nep-popover-arrow {
    top: auto;
    bottom: 8px;
}

.nep-popover-bottom,
.nep-popover-bottom-left,
.nep-popover-bottom-right {
    margin-top: 12px;
    transform: translateX(-50%);
}

.nep-popover-bottom-left .nep-popover-arrow,
.nep-popover-bottom-right .nep-popover-arrow,
.nep-popover-bottom .nep-popover-arrow {
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%) rotate(315deg);
}

.nep-popover-bottom-left {
    transform: translateX(0);
}

.nep-popover-bottom-left .nep-popover-arrow {
    left: 16px;
}

.nep-popover-bottom-right {
    transform: translateX(-100%);
}

.nep-popover-bottom-right .nep-popover-arrow {
    right: 8px;
    left: auto;
}

.nep-popover-bottom-right {
    transform: none;
}

.nep-popover-top-right {
    transform: translateY(-100%);
}

.nep-popover-content {
    position: relative;
    z-index: 2;
    background-color: #fff;
    border-radius: inherit;
    display: inline-block;
    width: 100%;
}

.nep-popover-info {
    border-color: #c5f1f8;
    border-color: var(--alert-info-border-color, #c5f1f8);
}

.nep-popover-info .nep-popover-arrow,
.nep-popover-info .nep-popover-content {
    background: #e5f5fc;
    background: var(--alert-info-bg, #e5f5fc);
}

.nep-popover-info .nep-popover-arrow {
    border-color: #c5f1f8;
    border-color: var(--alert-info-border-color, #c5f1f8);
}

.nep-popover-info .nep-popover-icon path {
    fill: #00acc1;
    fill: var(--info-color, #00acc1);
}

.nep-popover-warning {
    border-color: #ffd59a;
    border-color: var(--alert-warning-border-color, #ffd59a);
}

.nep-popover-warning .nep-popover-arrow,
.nep-popover-warning .nep-popover-content {
    background: #ffecb3;
    background: var(--alert-warning-bg, #ffecb3);
}

.nep-popover-warning .nep-popover-arrow {
    border-color: #ffd59a;
    border-color: var(--alert-warning-border-color, #ffd59a);
}

.nep-popover-warning .nep-popover-icon path {
    fill: #c68700;
    fill: var(--warning-color, #c68700);
}

.nep-popover-success {
    border-color: #bcdeb6;
    border-color: var(--alert-success-border-color, #bcdeb6);
}

.nep-popover-success .nep-popover-arrow,
.nep-popover-success .nep-popover-content {
    background: #c8e6c9;
    background: var(--alert-success-bg, #c8e6c9);
}

.nep-popover-success .nep-popover-arrow {
    border-color: #bcdeb6;
    border-color: var(--alert-success-border-color, #bcdeb6);
}

.nep-popover-success .nep-popover-icon path {
    fill: #388e3c;
    fill: var(--success-color, #388e3c);
}

.nep-popover-danger,
.nep-popover-error {
    border-color: #ecb5be;
    border-color: var(--alert-danger-border-color, #ecb5be);
}

.nep-popover-danger .nep-popover-content,
.nep-popover-error .nep-popover-content {
    background: #f1c9c9;
    background: var(--alert-danger-bg, #f1c9c9);
}

.nep-popover-danger .nep-popover-arrow,
.nep-popover-error .nep-popover-arrow {
    border-color: #ecb5be;
    border-color: var(--alert-danger-border-color, #ecb5be);
    background: #f1c9c9;
    background: var(--alert-danger-bg, #f1c9c9);
}

.nep-popover-danger .nep-popover-icon path,
.nep-popover-error .nep-popover-icon path {
    fill: #ba0c2f;
    fill: var(--danger-color, #ba0c2f);
}

.nep-popover-confirm {
    padding: 8px 12px;
}

.nep-popover-confirm .nep-popover-footer {
    text-align: right;
    margin-bottom: 4px;
}

.nep-popover-confirm .nep-popover-mention .nep-popover-alert {
    background: transparent;
    border: none;
    color: #55565a;
    color: var(--secondary-color, #55565a);
    margin-bottom: 8px;
    font-size: 14px;
}

.nep-tree,
.nep-tree-node {
    position: relative;
}

.nep-tree-node {
    padding-left: 20px;
    line-height: 1.42857143;
}

.nep-tree-node>div {
    padding-bottom: 4px;
}

.nep-tree-node:last-child>div {
    padding-bottom: 0;
}

.nep-tree-with-line .nep-tree-node:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 5px;
    width: 1px;
    background: linear-gradient(180deg, var(--gray-500, #ababab), #ababab) 1px, transparent 1px, transparent;
    background: linear-gradient( 180deg, var(--gray-500, #ababab), var(--gray-500, #ababab) 1px, transparent 0, transparent);
    background-size: 100% 2px;
    content: " ";
}

.nep-tree-with-line .nep-tree-node:after {
    position: absolute;
    top: 9px;
    left: 6px;
    width: 11px;
    height: 1px;
    background: linear-gradient(90deg, var(--gray-500, #ababab), #ababab) 1px, transparent 1px, transparent;
    background: linear-gradient( 90deg, var(--gray-500, #ababab), var(--gray-500, #ababab) 1px, transparent 0, transparent);
    background-size: 2px 100%;
    content: " ";
}

.nep-tree-with-line .nep-tree-node:last-child:before {
    top: -4px;
    bottom: auto;
    height: 14px;
}

.nep-tree-with-line .nep-tree-node:first-child:before {
    top: -4px;
}

.nep-tree-with-line.nep-tree>.nep-tree-node:first-child:before {
    top: 9px;
}

.nep-tree-with-line .nep-tree-icon-plus,
.nep-tree-with-line .nep-tree-icon-sub {
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
}

.nep-tree-with-line .nep-tree-icon-plus:hover span,
.nep-tree-with-line .nep-tree-icon-sub:hover span {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
}

.nep-tree-with-line .nep-tree-icon-plus:focus span,
.nep-tree-with-line .nep-tree-icon-sub:focus span {
    border-color: #4061C7;
    border-color: var(--input-border-focus-color, #4061C7);
    box-shadow: 0 0 0 var(--input-focus-width, 3px) rgba(2, 17, 85, 0.25);
    box-shadow: 0 0 0 var(--input-focus-width, 3px) var(--input-border-focus-color-fade-25, rgba(2, 17, 85, 0.25));
}

.nep-tree-with-line .nep-tree-icon-plus span,
.nep-tree-with-line .nep-tree-icon-sub span {
    position: relative;
    display: inline-block;
    width: 11px;
    height: 11px;
    border: 1px solid $gray-200;
    border: 1px solid var(--gray-600, $gray-200);
    background: #fff;
    border-radius: 2px;
}

.nep-tree-with-line .nep-tree-icon-plus span:before,
.nep-tree-with-line .nep-tree-icon-sub span:before {
    position: absolute;
    top: 4px;
    left: 1px;
    display: block;
    width: 7px;
    height: 0;
    border-bottom: 1px solid transparent;
    border-color: inherit;
    content: " ";
}

.nep-tree-with-line .nep-tree-icon-plus span:after {
    position: absolute;
    top: 1px;
    left: 4px;
    display: block;
    width: 0;
    height: 7px;
    border-right: 1px solid transparent;
    border-color: inherit;
    content: " ";
}

.nep-tree-no-line .nep-tree-icon-plus,
.nep-tree-no-line .nep-tree-icon-sub {
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
    width: 16px;
}

.nep-tree-no-line .nep-tree-icon-plus:hover span,
.nep-tree-no-line .nep-tree-icon-sub:hover span {
    border-left-color: #4061C7;
    border-left-color: var(--primary-color, #4061C7);
}

.nep-tree-no-line .nep-tree-icon-plus span,
.nep-tree-no-line .nep-tree-icon-sub span {
    position: absolute;
    top: 6px;
    left: 6px;
    display: inline-block;
    width: 0;
    height: 0;
    border-color: transparent transparent transparent $gray-200;
    border-left: 5px solid var(--gray-600, $gray-200);
    border-bottom: 4px solid transparent;
    border-right: 5px solid transparent;
    border-top: 4px solid transparent;
    transform-origin: 2px 4px;
    transition: transform 0.2s linear;
}

.nep-tree-no-line .nep-tree-icon-sub span {
    transform: rotate(90deg);
}

.nep-tree-icon-loading {
    position: absolute;
    z-index: 10;
    top: 3px;
    left: 0;
    width: 12px;
    height: 12px;
    background: #fff;
}

.nep-tree-content {
    z-index: 10;
    display: flex;
}

.nep-tree-content .nep-tree-text {
    flex: 1;
}

.nep-tree-checkbox {
    z-index: 10;
    margin: 2px 6px 0 -2px;
    font-size: 0;
}

.nep-tree-drag-place {
    position: relative;
    z-index: 0;
    width: 100%;
    height: 0;
}

.nep-tree-drag-place div {
    border: 1px dashed #e0e0e0;
    background: #fafafb;
    background: var(--gray-100, #fafafb);
}

.nep-slider {
    position: relative;
    padding: 20px 10px;
    user-select: none;
}

.nep-slider-background {
    height: 6px;
    height: var(--slider-bar-height, 6px);
    background: #4061C7;
    background: var(--primary-color, #4061C7);
    border-radius: 3px;
    border-radius: var(--slider-border-radius, 3px);
}

.nep-slider-inner {
    top: 20px;
    right: 10px;
    left: 10px;
    cursor: pointer;
}

.nep-slider-bar,
.nep-slider-inner {
    position: absolute;
    height: 6px;
    height: var(--slider-bar-height, 6px);
}

.nep-slider-bar {
    z-index: 2;
    top: 0;
    width: 100%;
}

.nep-slider-bar .nep-slider-bar-bg {
    height: 100%;
    background: #cac9c7;
    background: var(--slider-bar-color, #cac9c7);
    border-radius: 3px;
    border-radius: var(--slider-border-radius, 3px);
}

.nep-slider-bar .nep-slider-indicator {
    z-index: 100;
}

.nep-slider-bar .nep-slider-result {
    position: absolute;
    right: 0;
    bottom: 14px;
    bottom: var(--slider-value-bottom, 14px);
    display: none;
    padding: 0 4px;
    background: #fff;
    border-radius: 2px;
    font-size: 12px;
    transform: translate(50%);
}

.nep-slider-bar .nep-slider-result:after {
    position: absolute;
    top: 100%;
    left: 50%;
    width: 0;
    height: 0;
    border-color: #fff transparent transparent;
    border-style: solid;
    border-width: 3px 3px 0;
    content: " ";
    transform: translate(-50%);
}

.nep-slider-bar .nep-slider-result.nep-slider-show,
.nep-slider:hover .nep-slider-result {
    display: block;
}

.nep-slider-indicator {
    position: absolute;
    top: 50%;
    right: 0;
    width: 14px;
    width: var(--slider-indicator-size, 14px);
    height: 14px;
    height: var(--slider-indicator-size, 14px);
    border: 2px solid #4061C7;
    border: 2px solid var(--primary-color, #4061C7);
    background: #fff;
    background: var(--slider-indicator-bg, #fff);
    border-radius: 7px;
    border-radius: var(--slider-indicator-size-half, 7px);
    cursor: pointer;
    transform: translate(50%, -50%);
}

.nep-slider-disabled .nep-slider-background {
    background: #ababab;
    background: var(--gray-500, #ababab);
}

.nep-slider-disabled .nep-slider-inner {
    cursor: not-allowed;
}

.nep-slider-disabled .nep-slider-indicator {
    border: 2px solid #ababab;
    border: 2px solid var(--gray-500, #ababab);
    cursor: not-allowed;
}

.nep-slider-right {
    z-index: 10;
    right: 0;
}

.nep-slider-right .nep-slider-indicator {
    right: auto;
    left: 0;
    transform: translate(-50%, -50%);
}

.nep-slider-right .nep-slider-result {
    right: auto;
    left: 0;
    transform: translate(-50%);
}

.nep-slider-scale {
    display: flex;
    visibility: hidden;
}

.nep-slider-scale.nep-slider-show {
    visibility: visible;
}

.nep-slider-scale div {
    position: relative;
    display: block;
    flex: 1;
    padding-top: 12px;
}

.nep-slider-scale div:last-child {
    flex: 0;
}

.nep-slider-scale div:before {
    position: absolute;
    top: 5px;
    left: -1px;
    height: 6px;
    border-left: 1px solid #cac9c7;
    content: " ";
}

.nep-slider-scale div span {
    position: absolute;
    font-size: 12px;
    transform: translate(-50%) scale(0.9);
}

.nep-slider:hover .nep-slider-scale {
    visibility: visible;
}

.nep-slider-vertical {
    display: inline-flex;
    padding: 10px 70px 10px 20px;
}

.nep-slider-vertical .nep-slider-background {
    width: 6px;
    width: var(--slider-bar-height, 6px);
    height: 100%;
}

.nep-slider-vertical .nep-slider-inner {
    top: 10px;
    right: auto;
    bottom: 10px;
    left: 20px;
    display: inline-flex;
    width: 6px;
    width: var(--slider-bar-height, 6px);
    height: auto;
}

.nep-slider-vertical .nep-slider-result {
    right: auto;
    bottom: 0;
    left: 15px;
    padding: 1px 6px;
    background: #fff;
    transform: translateY(50%);
}

.nep-slider-vertical .nep-slider-result:after {
    top: 50%;
    right: 100%;
    left: auto;
    border-width: 3px 3px 3px 0;
    border-color: transparent #fff transparent transparent;
    transform: translateY(-50%);
}

.nep-slider-vertical .nep-slider-scale {
    display: inline-flex;
    flex: 1;
    flex-direction: column-reverse;
    margin-left: 15px;
}

.nep-slider-vertical .nep-slider-scale div {
    padding-top: 0;
}

.nep-slider-vertical .nep-slider-scale div:before {
    top: 0;
    left: -10px;
    width: 6px;
    height: 0;
    border-top: 1px solid #cac9c7;
    border-left: 0;
}

.nep-slider-vertical .nep-slider-scale div span {
    transform: translateY(-50%) scale(0.9);
}

.nep-slider-vertical .nep-slider-scale div:first-child {
    flex: 0;
}

.nep-slider-vertical .nep-slider-scale div:last-child {
    flex: 1;
}

.nep-slider-bottom {
    top: auto;
    bottom: 0;
}

.nep-slider-bottom .nep-slider-indicator {
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%);
}

.nep-slider-bottom .nep-slider-result {
    top: 0;
    bottom: auto;
    transform: translateY(-50%);
}

.nep-slider-top .nep-slider-indicator {
    top: auto;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 50%);
}

.nep-tabs,
.nep-tabs-header {
    position: relative;
}

.nep-tabs-header {
    z-index: 1;
    display: flex;
    margin-bottom: 0;
}

.nep-tabs-header-tabs {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.nep-tabs-header .nep-tabs-tab {
    position: relative;
    z-index: 1;
    display: inline-block;
    padding: 10px 15px;
    margin: 0 0 -1px;
    border: 1px solid transparent;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
    border-left-width: 1rem;
    border-right-width: 0.5rem;
}


.nep-tabs-header .nep-tabs-disabled {
    cursor: not-allowed;
}

.nep-tabs-header .nep-tabs-active {
    z-index: 100;
    cursor: default;
}

.nep-tabs-header .nep-tabs-hr {
    position: absolute;
    z-index: 10;
    bottom: 0;
    width: 100%;
    border-bottom: 1px solid transparent;
}

.nep-tabs-header .nep-tabs-extra {
    display: flex;
    align-items: center;
}

.nep-tabs .nep-tabs-indicator {
    display: inline-block;
    width: 40px;
    padding-top: 11px;
    cursor: pointer;
    text-align: center;
}

.nep-tabs .nep-tabs-indicator svg {
    width: 12px;
    height: 12px;
    transform: rotate(90deg);
    transition: transform 0.2s linear;
}

.nep-tabs .nep-tabs-indicator svg path {
    fill: #9e9e9e;
}

.nep-tabs .nep-tabs-indicator:hover svg path {
    fill: #4061C7;
    fill: var(--primary-color, #4061C7);
}

.nep-tabs .nep-tabs-indicator.nep-tabs-collapsed svg {
    transform: rotate(0);
}

.nep-tabs .nep-tabs-button-active {
    position: relative;
    z-index: 100;
    background: #4061C7;
    background: var(--primary-color, #4061C7);
    color: #fff;
}

.nep-tabs .nep-tabs-button-active:before {
    display: none;
}

.nep-tabs .nep-tabs-button-active:not(:last-child):after {
    position: absolute;
    top: 0;
    right: -1px;
    bottom: 0;
    width: 0;
    border-right: 1px solid #4061C7;
    border-right: 1px solid var(--primary-color, #4061C7);
    content: " ";
}

.nep-tabs-inner {
    overflow: hidden;
    flex: 1;
}

.nep-tabs-inner a.nep-tabs-link {
    color: #55565a;
    color: var(--secondary-color, #55565a);
}

.nep-tabs-inner a.nep-tabs-link.nep-tabs-active {
    color: #4061C7;
    color: var(--primary-color, #4061C7);
}

.nep-tabs-scroll {
    display: inline-block;
    transition: margin 0.2s linear;
    white-space: nowrap;
}

.nep-tabs-align-right .nep-tabs-inner {
    text-align: right;
}

.nep-tabs-vertical {
    display: flex;
    justify-content: flex-start;
}

.nep-tabs-vertical .nep-tabs-header {
    flex-direction: column;
}

.nep-tabs-vertical .nep-tabs-header .nep-tabs-inner {
    padding: 0 0 1px;
}

.nep-tabs-vertical .nep-tabs-header .nep-tabs-inner .nep-tabs-tab {
    display: block;
}

.nep-tabs-vertical .nep-tabs-header .nep-tabs-inner .nep-tabs-tab+.nep-tabs-tab {
    margin-top: 4px;
    margin-left: 0;
}

.nep-tabs-vertical .nep-tabs-header .nep-tabs-scroll-next,
.nep-tabs-vertical .nep-tabs-header .nep-tabs-scroll-prev {
    width: 100%;
    height: 30px;
}

.nep-tabs-vertical .nep-tabs-header .nep-tabs-scroll-next svg,
.nep-tabs-vertical .nep-tabs-header .nep-tabs-scroll-prev svg {
    transform: translate(-50%, -50%) rotate(90deg);
}

.nep-tabs-vertical .nep-tabs-header .nep-tabs-hr {
    width: 0;
    height: 100%;
    border-left: 1px solid transparent;
}

.nep-tabs-align-vertical-left .nep-tabs-header-tabs {
    flex-direction: column;
}

.nep-tabs-align-vertical-left .nep-tabs-header {
    margin-right: 0;
}

.nep-tabs-align-vertical-left .nep-tabs-tab {
    border-radius: 4px 0 0 4px;
}

.nep-tabs-align-vertical-left .nep-tabs-hr {
    right: 0;
}

.nep-tabs-align-vertical-right .nep-tabs-header-tabs {
    flex-direction: column;
}

.nep-tabs-align-vertical-right .nep-tabs-tab {
    border-radius: 0 4px 4px 0;
}

.nep-tabs-align-vertical-right .nep-tabs-hr {
    left: 0;
}

.nep-tabs-scroll-next,
.nep-tabs-scroll-prev {
    position: relative;
    width: 30px;
    background: #fff;
    cursor: pointer;
    text-align: center;
}

.nep-tabs-scroll-next svg,
.nep-tabs-scroll-prev svg {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    transform: translate(-50%, -50%);
}

.nep-tabs-scroll-next,
.nep-tabs-scroll-prev {
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.2);
}

.nep-tabs-panel {
    position: relative;
    z-index: 10;
    display: none;
}

.nep-tabs-panel.nep-tabs-show {
    display: block;
}

.nep-tabs-line .nep-tabs-active {
    color: #4061C7;
}

.nep-tabs-line .nep-tabs-active:after {
    position: absolute;
    width: 100%;
    content: " ";
    border-bottom: 2px solid #4061C7;
}

.nep-tabs-line:not(.nep-tabs-vertical) .nep-tabs-active:after {
    bottom: 0;
    left: 0;
}

.nep-tabs-line.nep-tabs-align-vertical-left .nep-tabs-active:after {
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
}

.nep-tabs-line.nep-tabs-align-vertical-right .nep-tabs-active:after {
    top: 0;
    left: 0;
    width: 2px;
    height: 100%;
}

.nep-tag {
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: var(--tag-padding-vertical, 4px) var(--tag-padding-horizontal, 12px);
    margin: 0 4px;
    outline: none;
    background: #f0f0f1;
    background: var(--gray-200, #f0f0f1);
    border-radius: 4px;
    border-radius: var(--tag-border-radius, 4px);
    color: #616161;
    color: var(--tag-color, #616161);
    cursor: pointer;
    font-size: 14px;
    line-height: 1.4;
    transition: all 0.3s;
    height: 32px;
}

.nep-tag .nep-tag-inline {
    display: inline-block;
    vertical-align: top;
}

.nep-tag-small {
    height: 24px;
    margin: 0 2px 2px;
}

.nep-tag.nep-tag-disabled {
    cursor: not-allowed;
}

.nep-tag-input {
    display: inline-block;
    width: 100px;
    height: 20px;
    margin: 0 0 0 4px;
    font-size: 14px;
    vertical-align: middle;
}

.nep-tag-input input {
    padding: 1px 4px;
}

.nep-tag-close-icon-wrapper {
    width: 12px;
    margin-left: 8px;
}

.nep-tag-close-icon-wrapper .nep-tag-close-icon {
    position: absolute;
    top: 50%;
    right: 8px;
    width: 16px;
    height: 16px;
    transform: translateY(-50%);
    cursor: pointer;
    border-radius: 8px;
}

.nep-tag-close-icon-wrapper .nep-tag-close-icon:after,
.nep-tag-close-icon-wrapper .nep-tag-close-icon:before {
    position: absolute;
    top: 7px;
    left: 1px;
    display: block;
    width: 12px;
    height: 1px;
    background: #717171;
    content: " ";
}

.nep-tag-close-icon-wrapper .nep-tag-close-icon:after {
    transform: rotate(45deg);
}

.nep-tag-close-icon-wrapper .nep-tag-close-icon:before {
    transform: rotate(315deg);
}

.nep-tag-close-icon-wrapper .nep-tag-close-icon:hover:after,
.nep-tag-close-icon-wrapper .nep-tag-close-icon:hover:before {
    background: #4061C7;
    background: var(--primary-color, #4061C7);
}

.nep-tag-close-icon-wrapper .nep-tag-close-icon svg {
    fill: hsla(0, 0%, 100%, 0.2);
    fill: var(--tag-close-color, hsla(0, 0%, 100%, 0.2));
    position: absolute;
    top: 50%;
    width: 8px;
    transform: translateY(-50%);
}

.nep-tag-close-icon-wrapper .nep-tag-close-icon-dark {
    position: absolute;
    top: 50%;
    right: 8px;
    width: 16px;
    height: 16px;
    transform: translateY(-50%);
    cursor: pointer;
    border-radius: 8px;
}

.nep-tag-close-icon-wrapper .nep-tag-close-icon-dark:after,
.nep-tag-close-icon-wrapper .nep-tag-close-icon-dark:before {
    position: absolute;
    top: 7px;
    left: 1px;
    display: block;
    width: 12px;
    height: 1px;
    background: hsla(0, 0%, 100%, 0.2);
    background: var(--tag-close-color, hsla(0, 0%, 100%, 0.2));
    content: " ";
}

.nep-tag-close-icon-wrapper .nep-tag-close-icon-dark:after {
    transform: rotate(45deg);
}

.nep-tag-close-icon-wrapper .nep-tag-close-icon-dark:before {
    transform: rotate(315deg);
}

.nep-tag-close-icon-wrapper .nep-tag-close-icon-dark:hover:after,
.nep-tag-close-icon-wrapper .nep-tag-close-icon-dark:hover:before {
    background: #fff;
}

.nep-tag .nep-tag-close-loading {
    display: inline-block;
    margin: 0 0 0 8px;
}

.nep-tag-rounded {
    border-radius: 16px;
    border-radius: var(--tag-border-radius, 16px);
    padding: var(--tag-padding-vertical, 4px) var(--tag-padding-horizontal, 12px);
}

.nep-tag-outlined {
    border: 1px solid #dedfe0;
    border-color: var(--tag-border-color, #dedfe0);
    background: #fff;
}

.nep-tag-thumbnail {
    padding-left: 8px;
}

.nep-tag-thumbnail .nep-thumbnail {
    margin-right: 8px;
}

.nep-tag-thumbnail .nep-tag-inline {
    display: inline-flex;
    align-items: center;
}

.nep-tag-dark {
    background: hsla(0, 0%, 100%, 0.2);
    background: var(--menu-dark-acitve-bg, hsla(0, 0%, 100%, 0.2));
    color: #fff;
}

.nep-tag-dark-outlined {
    background: transparent;
    border-color: hsla(0, 0%, 100%, 0.2);
    border-color: var(--menu-dark-acitve-bg, hsla(0, 0%, 100%, 0.2));
}

.nep-tag-success {
    background-color: #388e3c;
    background-color: var(--success-color, #388e3c);
}

.nep-tag-info,
.nep-tag-success {
    border-color: transparent;
    color: #fff;
}

.nep-tag-info {
    background-color: #4061C7;
    background-color: var(--primary-color, #4061C7);
}

.nep-tag-warning {
    background-color: #c68700;
    background-color: var(--warning-color, #c68700);
}

.nep-tag-danger,
.nep-tag-warning {
    border-color: transparent;
    color: #fff;
}

.nep-tag-danger {
    background-color: #ba0c2f;
    background-color: var(--danger-color, #ba0c2f);
}

.nep-progress-line {
    display: flex;
    align-items: center;
}

.nep-progress-line .nep-progress-background {
    position: relative;
    overflow: hidden;
    flex: 1;
    background: #f0f0f1;
    background: var(--gray-200, #f0f0f1);
}

.nep-progress-line .nep-progress-content {
    margin-left: 8px;
}

.nep-progress-line .nep-progress-front {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: #4061C7;
    background: var(--primary-color, #4061C7);
    transition: width 0.32s linear;
}

.nep-progress-circle {
    position: relative;
    display: inline-block;
}

.nep-progress-circle svg {
    transform: rotate(270deg);
}

.nep-progress-circle .nep-progress-background {
    stroke: #f0f0f1;
    stroke: var(--gray-200, #f0f0f1);
}

.nep-progress-circle .nep-progress-front {
    stroke: #4061C7;
    stroke: var(--primary-color, #4061C7);
    transition: stroke-dashoffset 0.32s ease 0s, stroke-dasharray 0.32s ease 0s, stroke 0.32s, stroke-width 0.06s ease 0.32s;
}

.nep-progress-circle .nep-progress-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.nep-progress-success .nep-progress-front {
    background: #388e3c;
    background: var(--success-color, #388e3c);
    stroke: #388e3c;
    stroke: var(--success-color, #388e3c);
}

.nep-progress-success .nep-progress-content {
    color: #388e3c;
    color: var(--success-color, #388e3c);
}

.nep-progress-info .nep-progress-front {
    background: #00acc1;
    background: var(--info-color, #00acc1);
    stroke: #00acc1;
    stroke: var(--info-color, #00acc1);
}

.nep-progress-warning .nep-progress-front {
    background: #c68700;
    background: var(--warning-color, #c68700);
    stroke: #c68700;
    stroke: var(--warning-color, #c68700);
}

.nep-progress-danger .nep-progress-front,
.nep-progress-error .nep-progress-front {
    background: #ba0c2f;
    background: var(--danger-color, #ba0c2f);
    stroke: #ba0c2f;
    stroke: var(--danger-color, #ba0c2f);
}

.nep-upload-handle {
    display: inline-block;
    margin-bottom: 8px;
    cursor: pointer;
}

.nep-upload-view-file,
.nep-upload-view-value {
    position: relative;
    padding: 4px 10px;
}

.nep-upload-view-file .nep-upload-text,
.nep-upload-view-value .nep-upload-text {
    margin-right: 20px;
    word-break: break-all;
}

.nep-upload-view-file .nep-upload-delete,
.nep-upload-view-file .nep-upload-recover,
.nep-upload-view-value .nep-upload-delete,
.nep-upload-view-value .nep-upload-recover {
    position: absolute;
    top: 2px;
    right: 10px;
    width: 12px;
    height: 12px;
    color: #ba0c2f;
    color: var(--danger-color, #ba0c2f);
    font-size: 16px;
    vertical-align: middle;
}

.nep-upload-view-file .nep-upload-delete svg,
.nep-upload-view-file .nep-upload-recover svg,
.nep-upload-view-value .nep-upload-delete svg,
.nep-upload-view-value .nep-upload-recover svg {
    width: 12px;
    height: 12px;
}

.nep-upload-view-file .nep-upload-delete:active,
.nep-upload-view-file .nep-upload-recover:active,
.nep-upload-view-value .nep-upload-delete:active,
.nep-upload-view-value .nep-upload-recover:active {
    text-decoration: none;
}

.nep-upload-view-file .nep-upload-delete svg,
.nep-upload-view-value .nep-upload-delete svg {
    width: 10px;
    height: 10px;
    fill: #ba0c2f;
    fill: var(--danger-color, #ba0c2f);
}

.nep-upload-view-file .nep-upload-recover svg,
.nep-upload-view-value .nep-upload-recover svg {
    fill: #4061C7;
    fill: var(--primary-color, #4061C7);
}

.nep-upload-view-file:hover,
.nep-upload-view-value:hover {
    background: #f5f5f5;
}

.nep-upload-view-file:hover .nep-upload-delete svg,
.nep-upload-view-value:hover .nep-upload-delete svg {
    fill: #ba0c2f;
    fill: var(--danger-color, #ba0c2f);
}

.nep-upload-view-file.nep-upload-to-be-delete,
.nep-upload-view-value.nep-upload-to-be-delete {
    color: #cac9c7;
    color: var(--gray-400, #cac9c7);
    text-decoration: line-through;
}

.nep-upload-view-file.nep-upload-error,
.nep-upload-view-value.nep-upload-error {
    color: #ba0c2f;
    color: var(--danger-color, #ba0c2f);
}

.nep-upload-view-file .nep-upload-progress,
.nep-upload-view-value .nep-upload-progress {
    margin-top: 2px;
}

.nep-upload-image-item {
    position: relative;
    display: inline-block;
    margin-right: 12px;
    border: 1px dashed #cac9c7;
    border: 1px dashed var(--gray-400, #cac9c7);
    background: #fff;
    border-radius: 2px;
    vertical-align: top;
}

.nep-upload-image-item .nep-upload-image-bg {
    position: absolute;
    z-index: 0;
    top: 4px;
    right: 4px;
    bottom: 4px;
    left: 4px;
    background-size: cover;
}

.nep-upload-image-item .nep-upload-progress-bg {
    position: absolute;
    z-index: 1;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.3);
    border-radius: inherit;
}

.nep-upload-image-item .nep-upload-progress {
    position: absolute;
    z-index: 10;
    right: 4px;
    bottom: 4px;
    left: 4px;
}

.nep-upload-image-item .nep-upload-delete,
.nep-upload-image-item .nep-upload-recover {
    position: absolute;
    z-index: 100;
    top: -7px;
    right: -7px;
    display: flex;
    width: 14px;
    height: 14px;
    background: #ba0c2f;
    background: var(--danger-color, #ba0c2f);
    border-radius: 8px;
}

.nep-upload-image-item .nep-upload-delete svg,
.nep-upload-image-item .nep-upload-recover svg {
    width: 6px;
    height: 6px;
    margin: auto;
    fill: #fff;
}

.nep-upload-image-item .nep-upload-recover {
    background: #4061C7;
    background: var(--primary-color, #4061C7);
}

.nep-upload-image-item .nep-upload-recover svg {
    width: 8px;
    height: 8px;
}

.nep-upload-image-item.nep-upload-error {
    border-color: #ba0c2f;
    border-color: var(--danger-color, #ba0c2f);
}

.nep-upload-image-item.nep-upload-error .nep-upload-message {
    position: absolute;
    z-index: 1000;
    top: 100%;
    left: 50%;
    min-width: 120px;
    max-width: 240px;
    padding: 2px 12px;
    margin-top: 8px;
    background: #fafafb;
    background: var(--gray-100, #fafafb);
    border-radius: 4px;
    border-radius: var(--input-border-radius, 4px);
    box-shadow: 0 0 0 1px #ecb5be, 0 2px 8px rgba(0, 0, 0, 0.15);
    color: #ba0c2f;
    color: var(--danger-color, #ba0c2f);
    font-size: 12px;
    transform: translateX(-50%);
    transform-origin: 0 0;
}

.nep-upload-image-item.nep-upload-error .nep-upload-message:before {
    position: absolute;
    bottom: 100%;
    left: 50%;
    width: 6px;
    height: 6px;
    border: 1px solid #ecb5be;
    border-width: 1px 0 0 1px;
    background: inherit;
    content: " ";
    transform: rotate(45deg) translateY(3px);
}

.nep-upload-image-item.nep-upload-error .nep-upload-progress-bg {
    display: none;
}

.nep-upload-dragger-area {
    position: relative;
    color: rgba(0, 0, 0, 0.8);
    padding: 16px;
    width: 100%;
    height: 100%;
    text-align: center;
    background: #fafafb;
    border-radius: 2px;
    cursor: pointer;
}

.nep-upload-dragger-area :after {
    content: "";
    display: block;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    position: absolute;
    border-radius: 2px;
    border: 1px dashed #cac9c7;
    border: 1px dashed var(--gray-400, #cac9c7);
    transition: border-color 0.3s;
}

.nep-upload-dragger-area.nep-upload-dragger-hover :after,
.nep-upload-dragger-area:hover :after {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
}

.nep-upload-dragger-area.nep-upload-disabled {
    cursor: not-allowed;
}

.nep-upload-dragger-area.nep-upload-disabled :after {
    border: 1px dashed #cac9c7;
    border: 1px dashed var(--gray-400, #cac9c7);
}

.nep-upload-image-plus {
    position: relative;
    display: inline-flex;
    vertical-align: top;
}

.nep-upload-image-plus.nep-upload-disabled {
    cursor: not-allowed;
}

.nep-upload-image-plus.nep-upload-disabled:focus,
.nep-upload-image-plus.nep-upload-disabled:hover {
    border-color: #cac9c7;
    border-color: var(--gray-400, #cac9c7);
}

.nep-upload-image-plus.nep-upload-disabled:focus .nep-upload-indicator:after,
.nep-upload-image-plus.nep-upload-disabled:focus .nep-upload-indicator:before,
.nep-upload-image-plus.nep-upload-disabled:hover .nep-upload-indicator:after,
.nep-upload-image-plus.nep-upload-disabled:hover .nep-upload-indicator:before {
    background: #9e9e9e;
}

.nep-upload-image-plus .nep-upload-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px;
}

.nep-upload-image-plus .nep-upload-indicator:after,
.nep-upload-image-plus .nep-upload-indicator:before {
    position: absolute;
    display: block;
    background: #9e9e9e;
    content: " ";
}

.nep-upload-image-plus .nep-upload-indicator:before {
    left: 14px;
    width: 2px;
    height: 30px;
}

.nep-upload-image-plus .nep-upload-indicator:after {
    top: 14px;
    width: 30px;
    height: 2px;
}

.nep-upload-image-plus:focus,
.nep-upload-image-plus:hover {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
}

.nep-upload-image-plus:focus .nep-upload-indicator:after,
.nep-upload-image-plus:focus .nep-upload-indicator:before,
.nep-upload-image-plus:hover .nep-upload-indicator:after,
.nep-upload-image-plus:hover .nep-upload-indicator:before {
    background: #4061C7;
    background: var(--primary-color, #4061C7);
}

.nep-upload-image-plus:focus {
    outline: none;
}

.nep-upload-image-result {
    border-style: solid;
}

.nep-upload-image-result .nep-upload-delete {
    visibility: hidden;
}

.nep-upload-image-result:hover .nep-upload-delete {
    visibility: visible;
}

.nep-upload-image-result.nep-upload-to-be-delete {
    border-style: dashed;
}

.nep-upload-image-result.nep-upload-to-be-delete .nep-upload-image-bg {
    filter: grayscale(100%);
    opacity: 0.6;
}

.nep-upload-bprogress {
    border-color: transparent;
    background-color: #4061C7;
    background-color: var(--primary-color, #4061C7);
    color: #fff;
    transition: background 0.15s ease-in-out;
    user-select: none;
    position: relative;
    padding: var(--button-padding-base-vertical, 4px) var(--button-padding-base-horizontal, 16px);
    border-radius: 3px;
    border-radius: var(--button-border-radius, 3px);
    font-size: 14px;
    line-height: 1.42857143;
    height: 32px;
}

@keyframes btn-focus-primary {
    0% {
        box-shadow: 0 0 0 0 rgba(1, 12, 60, 0.6);
        box-shadow: 0 0 0 0 var(--primary-color-dark-5_fade-60, rgba(1, 12, 60, 0.6));
        box-shadow: 0 0 0 0 rgba(73, 73, 77, 0.6);
        box-shadow: 0 0 0 0 var(--secondary-color-dark-5_fade-60, rgba(73, 73, 77, 0.6));
    }
    50% {
        box-shadow: 0 0 0 0.4em rgba(1, 12, 60, 0);
        box-shadow: 0 0 0 0.4em var(--primary-color-dark-5_fade-0, rgba(1, 12, 60, 0));
        box-shadow: 0 0 0 0.4em rgba(73, 73, 77, 0);
        box-shadow: 0 0 0 0.4em var(--secondary-color-dark-5_fade-0, rgba(73, 73, 77, 0));
    }
    to {
        box-shadow: 0 0 0 0.8em rgba(1, 12, 60, 0);
        box-shadow: 0 0 0 0.8em var(--primary-color-dark-5_fade-0, rgba(1, 12, 60, 0));
        box-shadow: 0 0 0 0.8em rgba(73, 73, 77, 0);
        box-shadow: 0 0 0 0.8em var(--secondary-color-dark-5_fade-0, rgba(73, 73, 77, 0));
    }
}

.nep-upload-bprogress:focus,
.nep-upload-bprogress:hover {
    border-color: #55565a;
    border-color: var(--secondary-color, #55565a);
    background-color: #031b87;
    background-color: var(--primary-color-dark-btn-hover, #031b87);
    color: #fff;
}

.nep-upload-bprogress:active {
    border-color: #55565a;
    border-color: var(--secondary-color, #55565a);
    animation: btn-focus-primary 0.4s ease-out;
    background-color: #010723;
    background-color: var(--primary-color-dark-btn-active, #010723);
    color: #fff;
    background-image: none;
}

.nep-upload-bprogress[disabled]:focus,
.nep-upload-bprogress[disabled]:hover,
fieldset[disabled] .nep-upload-bprogress:focus,
fieldset[disabled] .nep-upload-bprogress:hover {
    border-color: transparent;
    background-color: #4061C7;
    background-color: var(--primary-color, #4061C7);
}

.nep-upload-bprogress[disabled]:active,
fieldset[disabled] .nep-upload-bprogress:active {
    animation: none;
}

.nep-upload-bprogress .nep-upload-stream {
    z-index: 1;
    white-space: nowrap;
    overflow: hidden;
    color: #fff;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    transition: right 0.2s ease-in-out;
}

.nep-upload-bprogress .nep-upload-stream>span {
    display: inline-block;
    margin: 6px 12px;
}

.nep-upload-bprogress:focus {
    outline: none;
}

.nep-upload-bprogress.nep-upload-disabled {
    position: relative;
    box-shadow: none;
    cursor: not-allowed;
    opacity: 0.65;
}

.nep-upload-bprogress.nep-upload-disabled:focus,
.nep-upload-bprogress.nep-upload-disabled:hover {
    animation: none;
}

.nep-upload-bprogress.nep-upload-uploading {
    background: #fff;
    color: #ababab;
    color: var(--gray-500, #ababab);
    cursor: not-allowed;
    opacity: 0.85;
}

.nep-upload-bprogress.nep-upload-bprogress-success {
    background: #388e3c;
    background: var(--success-color, #388e3c);
    border-color: #388e3c;
}

.nep-upload-bprogress.nep-upload-bprogress-info {
    background: #00acc1;
    background: var(--info-color, #00acc1);
    border-color: #00acc1;
    border-color: var(--info-color, #00acc1);
}

.nep-upload-bprogress.nep-upload-bprogress-warning {
    background: #c68700;
    background: var(--warning-color, #c68700);
}

.nep-upload-bprogress.nep-upload-bprogress-danger,
.nep-upload-bprogress.nep-upload-bprogress-error {
    background: #ba0c2f;
    background: var(--danger-color, #ba0c2f);
    border-color: #c62828;
}

.nep-upload-bprogress-primary {
    background: #4061C7;
    background: var(--primary-color, #4061C7);
}

.nep-upload-border-primary,
.nep-upload-bprogress-primary {
    border-color: #4061C7;
    border-color: var(--primary-color, #4061C7);
}

.nep-upload-bprogress-success {
    background: #388e3c;
    background: var(--success-color, #388e3c);
    border-color: #388e3c;
}

.nep-upload-border-success {
    border-color: #388e3c;
}

.nep-upload-bprogress-info {
    background: #00acc1;
    background: var(--info-color, #00acc1);
}

.nep-upload-border-info,
.nep-upload-bprogress-info {
    border-color: #00acc1;
    border-color: var(--info-color, #00acc1);
}

.nep-upload-bprogress-warning {
    background: #c68700;
    background: var(--warning-color, #c68700);
    border-color: #c68700;
}

.nep-upload-border-warning {
    border-color: #c68700;
}

.nep-upload-bprogress-error {
    background: #ba0c2f;
    background: var(--danger-color, #ba0c2f);
    border-color: #c62828;
}

.nep-upload-border-error {
    border-color: #c62828;
}

.nep-upload-bprogress-danger {
    background: #ba0c2f;
    background: var(--danger-color, #ba0c2f);
    border-color: #fff;
}

.nep-upload-border-danger {
    border-color: #fff;
}

.nep-carousel {
    position: relative;
    overflow: hidden;
}

@keyframes nep-carousel-r2c {
    0% {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes nep-carousel-c2r {
    0% {
        transform: translateX(0);
    }
    to {
        transform: translateX(100%);
    }
}

@keyframes nep-carousel-l2c {
    0% {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes nep-carousel-c2l {
    0% {
        transform: translateX(0);
    }
    to {
        transform: translateX(-100%);
    }
}

@keyframes nep-carousel-t2c {
    0% {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes nep-carousel-c2t {
    0% {
        transform: translateY(0);
    }
    to {
        transform: translateY(-100%);
    }
}

@keyframes nep-carousel-b2c {
    0% {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes nep-carousel-c2b {
    0% {
        transform: translateY(0);
    }
    to {
        transform: translateY(100%);
    }
}

@keyframes nep-carousel-fade-in {
    0% {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes nep-carousel-fade-out {
    0% {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

.nep-carousel-item {
    position: absolute;
    top: 0;
    left: 0;
}

.nep-carousel-item,
.nep-carousel-item>* {
    width: 100%;
    height: 100%;
}

.nep-carousel-item-current {
    z-index: 10;
}

.nep-carousel-item-pre {
    z-index: 9;
}

.nep-carousel-slide.nep-carousel-forward .nep-carousel-item-current {
    animation: nep-carousel-r2c 0.4s ease-in-out;
}

.nep-carousel-slide.nep-carousel-forward .nep-carousel-item-pre {
    animation: nep-carousel-c2l 0.4s ease-in-out;
}

.nep-carousel-slide.nep-carousel-backward .nep-carousel-item-current {
    animation: nep-carousel-l2c 0.4s ease-in-out;
}

.nep-carousel-slide.nep-carousel-backward .nep-carousel-item-pre {
    animation: nep-carousel-c2r 0.4s ease-in-out;
}

.nep-carousel-slide-y.nep-carousel-forward .nep-carousel-item-current {
    animation: nep-carousel-b2c 0.4s ease-in-out;
}

.nep-carousel-slide-y.nep-carousel-forward .nep-carousel-item-pre {
    animation: nep-carousel-c2t 0.4s ease-in-out;
}

.nep-carousel-slide-y.nep-carousel-backward .nep-carousel-item-current {
    animation: nep-carousel-t2c 0.4s ease-in-out;
}

.nep-carousel-slide-y.nep-carousel-backward .nep-carousel-item-pre {
    animation: nep-carousel-c2b 0.4s ease-in-out;
}

.nep-carousel-fade .nep-carousel-item-current {
    animation: nep-carousel-fade-in 0.4s ease-in-out;
}

.nep-carousel-indicator {
    position: absolute;
    z-index: 100;
    bottom: 10px;
    bottom: var(--carousel-indicator-position, 10px);
}

.nep-carousel-indicator a {
    display: block;
    width: 10px;
    height: 10px;
    margin-right: 4px;
    border: 1px solid #fff;
    background-color: rgba(0, 0, 0, 0.25);
    border-radius: 50%;
    color: #fff;
    float: left;
}

.nep-carousel-indicator a:last-child {
    margin-right: 0;
}

.nep-carousel-indicator a:focus,
.nep-carousel-indicator a:hover {
    text-decoration: none;
}

.nep-carousel-indicator.nep-carousel-indicator-left {
    left: 10px;
    left: var(--carousel-indicator-position, 10px);
}

.nep-carousel-indicator.nep-carousel-indicator-center {
    left: 50%;
    transform: translateX(-50%);
}

.nep-carousel-indicator.nep-carousel-indicator-right {
    right: 10px;
    right: var(--carousel-indicator-position, 10px);
}

a.nep-carousel-indicator-active {
    background-color: hsla(0, 0%, 100%, 0.8);
    color: $nep-black;
}

.nep-carousel-indicator-number a {
    width: 14px;
    height: 14px;
    border-radius: 1px;
    font-size: 12px;
    line-height: 14px;
    text-align: center;
}

.nep-carousel-indicator-line a {
    width: 14px;
    height: 3px;
    border-width: 0;
    background-color: hsla(0, 0%, 100%, 0.3);
    border-radius: 0;
}

.nep-carousel-indicator-line a.nep-carousel-indicator-active {
    width: 18px;
    background-color: #fff;
}

.nep-carousel-slide-y .nep-carousel-indicator.nep-carousel-indicator-left,
.nep-carousel-slide-y .nep-carousel-indicator.nep-carousel-indicator-right {
    bottom: 50%;
    transform: translateY(50%);
}

.nep-carousel-slide-y .nep-carousel-indicator.nep-carousel-indicator-left a,
.nep-carousel-slide-y .nep-carousel-indicator.nep-carousel-indicator-right a {
    margin-right: 0;
    margin-bottom: 4px;
    clear: both;
}

.nep-carousel-slide-y .nep-carousel-indicator.nep-carousel-indicator-left a:last-child,
.nep-carousel-slide-y .nep-carousel-indicator.nep-carousel-indicator-right a:last-child {
    margin: 0;
}

.nep-carousel-slide-y .nep-carousel-indicator.nep-carousel-indicator-left.nep-carousel-indicator-line a,
.nep-carousel-slide-y .nep-carousel-indicator.nep-carousel-indicator-right.nep-carousel-indicator-line a {
    width: 3px;
    height: 14px;
}

.nep-carousel-slide-y .nep-carousel-indicator.nep-carousel-indicator-left.nep-carousel-indicator-line a.nep-carousel-indicator-active,
.nep-carousel-slide-y .nep-carousel-indicator.nep-carousel-indicator-right.nep-carousel-indicator-line a.nep-carousel-indicator-active {
    height: 18px;
}

.nep-cascader {
    position: relative;
    width: 100%;
}

.nep-cascader:focus {
    outline: none;
}

.nep-cascader-result {
    cursor: pointer;
    display: flex;
    max-height: 80px;
    overflow-y: auto;
    flex-flow: wrap;
    padding: 4px 28px 0 16px;
}

.nep-cascader-result .nep-input-placeholder {
    color: #dedfe0;
    color: var(--gray-300, #dedfe0);
}

.nep-cascader-result .nep-cascader-item,
.nep-cascader-result span {
    display: inline-block;
    overflow: hidden;
    max-width: 80%;
    padding: 0 4px;
    padding: 0 var(--select-result-padding-horizontal, 4px);
    margin-right: 4px;
    margin-right: var(--select-result-padding-horizontal, 4px);
    margin-bottom: 4px;
    border-radius: 3px;
    color: inherit;
    text-overflow: ellipsis;
}

.nep-cascader-result .nep-cascader-item-compressed,
.nep-cascader-result span-compressed {
    padding: 0 8px;
    font-size: 80%;
    opacity: 0.9;
}

.nep-cascader-result .nep-cascader-item-compressed>span,
.nep-cascader-result span-compressed>span {
    vertical-align: middle;
    letter-spacing: 2px;
    padding: 0;
    margin: 0;
    overflow: visible;
    max-width: none;
}

.nep-cascader-result .nep-cascader-item:after,
.nep-cascader-result span:after {
    content: "\feff ";
}

.nep-cascader-result .nep-thumbnail-small {
    width: 20px;
    height: 20px;
}

.nep-cascader-result .nep-cascader-item {
    background: #fafafb;
    background: var(--gray-100, #fafafb);
}

.nep-cascader-disabled .nep-cascader-result {
    cursor: not-allowed;
}

.nep-cascader-focus .nep-cascader-close,
.nep-cascader-result:hover .nep-cascader-close {
    display: block;
}

.nep-cascader-multiple .nep-cascader-close {
    right: 19px;
}

.nep-cascader-options {
    width: auto;
    align-items: stretch;
}

.nep-cascader-focus .nep-cascader-options {
    display: inline-flex;
}

.nep-cascader-no-data {
    width: 100%;
    position: relative;
    display: block;
    overflow: hidden;
    padding: 8px 28px 8px 16px;
    color: #ababab;
    color: var(--gray-500, #ababab);
    font-size: 14px;
    line-height: 1.42857143;
    text-overflow: ellipsis;
    transition: none;
    white-space: nowrap;
}

.nep-cascader-no-data:hover {
    color: #49494d;
    color: var(--gray-800-darken-5, #49494d);
}

.nep-cascader-list {
    display: inline-block;
    min-width: 120px;
    height: 100%;
    border-right: 1px solid #f0f0f1;
    border-right: 1px solid var(--gray-200, #f0f0f1);
    overflow-y: auto;
    vertical-align: top;
}

.nep-cascader-node {
    position: relative;
    padding: 8px 32px 8px 16px;
    color: #55565a;
    color: var(--gray-800, #55565a);
    white-space: nowrap;
}

.nep-cascader-node:hover {
    background-color: #f5f5f5;
}

.nep-cascader-node.nep-cascader-disabled {
    color: #cac9c7;
    color: var(--gray-400, #cac9c7);
    cursor: not-allowed;
}

.nep-cascader-node.nep-cascader-disabled:hover {
    background-color: transparent;
}

.nep-cascader-loading {
    position: absolute;
    top: 10px;
    right: 12px;
}

.nep-cascader-has-children:after,
.nep-cascader-may-be-children:after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 4px;
    content: "";
    border-top: 4px solid;
    border-right: 4px solid transparent;
    border-bottom: 0;
    border-left: 4px solid transparent;
    vertical-align: 3.4px;
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-65%) rotate(270deg);
}

.nep-cascader-may-be-children:after {
    color: #dedfe0;
    color: var(--gray-300, #dedfe0);
}

.nep-cascader-active {
    background-color: #f5f5f5;
    background-color: var(--select-item-active-bg, #f5f5f5);
    color: #49494d;
    color: var(--gray-800-darken-5, #49494d);
    font-weight: 700;
    text-decoration: none;
}

.nep-header {
    position: fixed;
    width: 100%;
    display: flex;
    height: 60px;
    align-items: center;
    border-bottom: 1px solid #e8f2ff;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.16);
    background: #fff;
}

@media (max-width: 979px) and (min-width: 0px) {
    .nep-header {
        right: 0;
        left: 0;
        z-index: 100;
    }
}

.nep-sidebar {
    height: 110%;
    transition: 0.2s;
    width: 60px;
    overflow: hidden;
    position: absolute;
}

// .nep-sidebar-acuity-background{
//     background: linear-gradient(180deg, #4061C7, #39195e 90%);
// }
// .nep-sidebar-client-background{
//     background: linear-gradient(180deg, #4061C7, #00BFA5 90%)!important;
// }

/* .nep-sidebar:hover {
  width: 210px;
} */


/** hover effect removed**/

.nep-sidebar .nep-sidebar-expandarrow {
    visibility: visible;
    float: right;
    padding: 8px 0 0;
    cursor: pointer;
}

.nep-sidebar .nep-sidebar-expand-pin {
    visibility: visible;
    float: right;
    padding: 8px 0 0;
    cursor: pointer;
}

.nep-sidebar .nep-menu {
    background: transparent;
    border: none;
}

.nep-sidebar .nep-logo {
    height: 72px;
    padding-left: 4px;
}

.nep-sidebar-expanded {
    width: 240px;
}

.nep-sidebar-expanded .nep-sidebar-expand-pin {
    visibility: visible;
    float: right;
    padding: 8px 0 0;
}

.nep-sidebar-expand-pin {
    visibility: hidden;
    width: 24px;
    height: 24px;
    margin: 1px 15px;
}

.nep-sidebar-toggle_bottom {
    position: fixed;
    z-index: 1001;
    bottom: 0;
    height: 40px;
    width: inherit;
    padding-left: 15px;
    margin: unset;
}

.nep-sidebar-expandarrow {
    width: 24px;
    height: 24px;
    padding: 12px;
}

.nep-thumbnail {
    height: 32px;
    width: 32px;
    align-items: center;
    display: flex;
    justify-content: center;
    user-select: none;
    background: #4061C7;
    background: var(--primary-color, #4061C7);
    color: #fff;
    border-radius: 4px;
    border-radius: var(--tag-border-radius, 4px);
    font-size: 12px;
    line-height: 1.4;
    transition: all 0.3s;
}

.nep-thumbnail-rounded {
    border-radius: 50%;
}

.nep-thumbnail-dark {
    background: hsla(0, 0%, 100%, 0.2);
    background: var(--tag-close-color, hsla(0, 0%, 100%, 0.2));
}

.nep-thumbnail-small {
    height: 24px;
    width: 24px;
}

.nep-login {
    height: 100vh;
    width: 100%;
    overflow: hidden;
    /* background: url(assets/images/Acuity_BEAT_Login_Background.png) 50% no-repeat; */
    background-size: cover;
}

.nep-login .customform {
    padding-top: 20px;
    text-align: center;
}

.nep-logo {
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    padding: 10px 0 10px 10px;
    height: 80px;
}

.nep-logo-dark {
    filter: invert(1) saturate(0) hue-rotate(0deg) brightness(6);
}

@font-size-h 4: ceil((@font-size-base * â­0.8571428571428571âŹ));
@font-size-h 5: ceil((@font-size-base * â­0.8571428571428571âŹ));
@font-size-h 6: ceil((@font-size-base * â­0.8571428571428571âŹ));
@font-face {
    font-display: block;
    font-family: nepicons;
    font-style: normal;
    font-weight: 400;
    /* src: url(Fonts/icomoon.eot);*/
}

[class*=" icon-"],
[class^="icon-"] {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    color: #75787b;
    color: var(--gray-700, #75787b);
    font-family: nepicons !important;
    font-style: normal;
    font-variant: normal;
    font-weight: 400;
    line-height: 1;
    text-transform: none;
    cursor: pointer;
}

[class*=" icon-"]:active,
[class*=" icon-"]:visited,
[class^="icon-"]:active,
[class^="icon-"]:visited {
    color: #4061C7;
}

.icon-Flag:before {
    content: "\e902";
}

.icon-Badge-Fail:before {
    content: "\e903";
}

.icon-Badge-Pass:before {
    content: "\e90b";
}

.icon-Warning-filled:before {
    content: "\e90e";
}

.icon-Warning-outline:before {
    content: "\e90f";
}

.icon-Bell-notification:before {
    content: "\e910";
}

.icon-Error:before {
    content: "\e911";
}

.icon-Information:before {
    content: "\e912";
}

.icon-Success:before {
    content: "\e913";
}

.icon-Portfolio:before {
    content: "\e900";
}

.icon-Requests:before {
    content: "\e901";
}

.icon-Configuration:before {
    content: "\10ffff";
}

.icon-Dashboard:before {
    content: "\e914";
}

.icon-Database:before {
    content: "\e904";
}

.icon-Effort-management:before {
    content: "\e905";
}

.icon-Options:before {
    content: "\e906";
}

.icon-Overview:before {
    content: "\e907";
}

.icon-Reports:before {
    content: "\e909";
}

.icon-Repository:before {
    content: "\e90a";
}

.icon-Settings:before {
    content: "\e908";
}

.icon-Template:before {
    content: "\e90c";
}

.icon-User-management:before {
    content: "\e90d";
}

.icon-Exit-full-screen:before {
    content: "\e915";
}

.icon-First-page:before {
    content: "\e921";
}

.icon-Full-screen:before {
    content: "\e922";
}

.icon-Last-page:before {
    content: "\e923";
}

.icon-Single-chevro-left:before {
    content: "\e924";
}

.icon-Single-chevron-right:before {
    content: "\e925";
}

.icon-Apps:before {
    content: "\e916";
}

.icon-Arrow-left:before {
    content: "\e917";
}

.icon-Arrow-right:before {
    content: "\e918";
}

.icon-Chevron-left:before {
    content: "\e919";
}

.icon-Chevron-right:before {
    content: "\e91a";
}

.icon-Expand-less:before {
    content: "\e91b";
}

.icon-Expand-more:before {
    content: "\e91c";
}

.icon-Menu-cascade-left:before {
    content: "\e91d";
}

.icon-Menu-cascade-right:before {
    content: "\e91e";
}

.icon-More_border:before {
    content: "\e91f";
}

.icon-More_Vertical:before {
    content: "\e920";
}

.icon-Pin:before {
    content: "\e926";
}

.icon-Stepper:before {
    content: "\e927";
}

.icon-Unpin:before {
    content: "\e928";
}

.icon-Image-File:before {
    content: "\e929";
}

.icon-Folder:before {
    content: "\e92a";
}

.icon-Zip-File:before {
    content: "\e92b";
}

.icon-Adobe-Acrobat:before {
    content: "\e92c";
}

.icon-Microsoft-excel:before {
    content: "\e92d";
}

.icon-Microsoft-powerpoint:before {
    content: "\e92e";
}

.icon-Microsoft-Word:before {
    content: "\e92f";
}

.icon-Attach-file:before {
    content: "\e930";
}

.icon-File:before {
    content: "\e931";
}

.icon-Insert-table:before {
    content: "\e932";
}

.icon-Redo:before {
    content: "\e933";
}

.icon-Undo:before {
    content: "\e934";
}

.icon-Line-chart:before {
    content: "\e935";
}

.icon-Multiline-chart:before {
    content: "\e936";
}

.icon-Align-justify:before {
    content: "\e937";
}

.icon-Align-left:before {
    content: "\e938";
}

.icon-Align-right:before {
    content: "\e939";
}

.icon-Format-bold:before {
    content: "\e93a";
}

.icon-Format-Italic:before {
    content: "\e93b";
}

.icon-Format-underline:before {
    content: "\e93c";
}

.icon-format_align_center-24px:before {
    content: "\e93d";
}

.icon-Indent-decrease:before {
    content: "\e93e";
}

.icon-Indent-increase:before {
    content: "\e93f";
}

.icon-Insert-chart:before {
    content: "\e940";
}

.icon-List-bulleted:before {
    content: "\e941";
}

.icon-List-numbered:before {
    content: "\e942";
}

.icon-Desktop:before {
    content: "\e943";
}

.icon-Calendar:before {
    content: "\e944";
}

.icon-Clock:before {
    content: "\e945";
}

.icon-Filter:before {
    content: "\e946";
}

.icon-Link:before {
    content: "\e947";
}

.icon-Preview:before {
    content: "\e948";
}

.icon-Sort:before {
    content: "\e949";
}

.icon-Email:before {
    content: "\e94a";
}

.icon-Message:before {
    content: "\e94b";
}

.icon-Send:before {
    content: "\e94c";
}

.icon-Star_Filled:before {
    content: "\e94d";
}

.icon-Star:before {
    content: "\e94e";
}

.icon-Add-as-new-line-item:before {
    content: "\e94f";
}

.icon-Map-preferences:before {
    content: "\e950";
}

.icon-Merge-line-with-previous-line:before {
    content: "\e951";
}

.icon-Settings1:before {
    content: "\e952";
}

.icon-Accept:before {
    content: "\e953";
}

.icon-Add-file:before {
    content: "\e954";
}

.icon-Add_Circle:before {
    content: "\e955";
}

.icon-Add:before {
    content: "\e956";
}

.icon-Clear_border:before {
    content: "\e957";
}

.icon-Clear:before {
    content: "\e958";
}

.icon-Cloud-download:before {
    content: "\e959";
}

.icon-Cloud-upload:before {
    content: "\e95a";
}

.icon-Delete:before {
    content: "\e95b";
}

.icon-Download-file:before {
    content: "\e95c";
}

.icon-Download:before {
    content: "\e95d";
}

.icon-Drag-indicator:before {
    content: "\e95e";
}

.icon-Edit:before {
    content: "\e95f";
}

.icon-Help:before {
    content: "\e960";
}

.icon-List-add .path1:before {
    content: "\e961";
    color: #75787b;
}

.icon-List-add .path2:before {
    content: "\e974";
    margin-left: -1em;
    color: #fff;
}

.icon-List-add .path3:before {
    content: "\e975";
    margin-left: -1em;
    color: #fff;
}

.icon-Power:before {
    content: "\e962";
}

.icon-Print:before {
    content: "\e963";
}

.icon-Push-to-database:before {
    content: "\e964";
}

.icon-Refesh:before {
    content: "\e965";
}

.icon-Reject:before {
    content: "\e966";
}

.icon-Save:before {
    content: "\e967";
}

.icon-Search:before {
    content: "\e968";
}

.icon-Share:before {
    content: "\e969";
}

.icon-Thumb-down:before {
    content: "\e96a";
}

.icon-Thumb-up:before {
    content: "\e96b";
}

.icon-Tick:before {
    content: "\e96c";
}

.icon-Tools:before {
    content: "\e96d";
}

.icon-Upload-file:before {
    content: "\e96e";
}

.icon-Upload:before {
    content: "\e96f";
}

.icon-Work:before {
    content: "\e970";
}

.icon-Zoom-in:before {
    content: "\e971";
}

.icon-Zoom-out:before {
    content: "\e972";
}

.icon-Wrench:before {
    content: "\e973";
}

@font-face{
    font-family: Helvetica Neue LT W05_25 Ult Lt;
    src:url(assets/Fonts/5664070/ec6281a0-c9c4-4477-a360-156acd53093f.woff2) format("woff2"),url(assets/Fonts/5664070/11066b40-10f7-4123-ba58-d9cbf5e89ceb.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_26UltLtIt;
    src:url(assets/Fonts/5664077/2707a251-2d32-4bb6-a3c4-87114ba2365f.woff2) format("woff2"),url(assets/Fonts/5664077/40f50724-486b-4e7b-9366-237e06eabfc8.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_35 Thin;
    src:url(assets/Fonts/5664081/7d63ccf8-e0ae-4dee-ad4d-bbc798aa5803.woff2) format("woff2"),url(assets/Fonts/5664081/b2c1327f-ab3d-4230-93d7-eee8596e1498.woff) format("woff");
}

@font-face{
    font-family: Helvetica Neue LT W05_36 Th It;
    src:url(assets/Fonts/5664067/2a7e8f89-c0b2-4334-9c34-7a2078d2b959.woff2) format("woff2"),url(assets/Fonts/5664067/32aad9d8-5fec-4b9d-ad53-4cf7a5b53698.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_45 Light;
    src:url(assets/Fonts/5664085/f9c5199e-a996-4c08-9042-1eb845bb7495.woff2) format("woff2"),url(assets/Fonts/5664085/2a34f1f8-d701-4949-b12d-133c1c2636eb.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_46 Lt It;
    src:url(assets/Fonts/5664089/5e4f385b-17ff-4d27-a63a-9ee28546c9a8.woff2) format("woff2"),url(assets/Fonts/5664089/116cde47-4a07-44a5-9fac-cbdcc1f14f79.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_55 Roman;
    src:url(assets/Fonts/5664093/08b57253-2e0d-4c12-9c57-107f6c67bc49.woff2) format("woff2"),url(assets/Fonts/5664093/08edde9d-c27b-4731-a27f-d6cd9b01cd06.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_56 Italic;
    src:url(assets/Fonts/5664098/4bd56f95-e7ab-4a32-91fd-b8704cbd38bc.woff2) format("woff2"),url(assets/Fonts/5664098/4fe1c328-1f21-434a-8f0d-5e0cf6c70dfb.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_65 Medium;
    src:url(assets/Fonts/5664103/240c57a0-fdce-440d-9ce3-85e0cb56f470.woff2) format("woff2"),url(assets/Fonts/5664103/7802e576-2ffa-4f22-a409-534355fbea79.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_66 Md It;
    src:url(assets/Fonts/5664107/de68be2a-5d0e-4b8d-b3eb-940f75503e2a.woff2) format("woff2"),url(assets/Fonts/5664107/31029e78-79a0-4940-b82d-2e3c238e1355.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_75 Bold;
    src:url(assets/Fonts/5664150/800da3b0-675f-465f-892d-d76cecbdd5b1.woff2) format("woff2"),url(assets/Fonts/5664150/7b415a05-784a-4a4c-8c94-67e9288312f5.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_76 Bd It;
    src:url(assets/Fonts/5664111/13ab58b4-b5ba-4c95-afde-ab2608fbbbd9.woff2) format("woff2"),url(assets/Fonts/5664111/5018b5b5-c821-4653-bc74-d0b11d735f1a.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_85 Heavy;
    src:url(assets/Fonts/5664115/7e42a406-9133-48c0-a705-4264ac520b43.woff2) format("woff2"),url(assets/Fonts/5664115/837750e9-3227-455d-a04e-dc76764aefcf.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_86 Hv It;
    src:url(assets/Fonts/5664119/0acba88f-0de4-4d43-81fd-920d7427f665.woff2) format("woff2"),url(assets/Fonts/5664119/713c9c40-9cbd-4276-819e-d0efaf5d3923.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_95 Black;
    src:url(assets/Fonts/5664121/fc4fb6ca-f981-4115-b882-c78e9f08be52.woff2) format("woff2"),url(assets/Fonts/5664121/6ed03453-f512-45ba-84bf-fe4ea45d5e6a.woff) format("woff");
}
@font-face{
    font-family: Helvetica Neue LT W05_96 Blk It;
    src:url(assets/Fonts/5664128/995add04-59cc-41ea-abd2-4712eaddf2a8.woff2) format("woff2"),url(assets/Fonts/5664128/7090e465-f6bf-4664-8b5a-d877a6915d87.woff) format("woff");
}
*,
body {
    font-family: "Helvetica Neue LT W05_55 Roman",Arial, Verdana, Tahoma,sans-serif;
    line-height: 1.42857143;
    letter-spacing: 0.0625rem;
    margin: 0;
    padding: 0;
}

// body::-webkit-scrollbar {
//     width: 0.425em;
// }
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    background-color: transparent;
}

body::-webkit-scrollbar-thumb {
    background-color: #757575;
    outline: 1px solid #708090;
    height: 10% !important;
    max-height: 20% !important;
}

::-webkit-scrollbar-thumb {
    display: unset;
    background-color:hsla(0,0%,50%,.5);
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    height: 10% !important;
    max-height: 20% !important;
}
// body::-webkit-scrollbar-track {
//     -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
// }
// ::-webkit-scrollbar-track {
//     border-radius: 6px;
//     background-color: transparent;
// }
::-webkit-scrollbar-track {
    background-color: transparent;
}

*,
:after,
:before {
    box-sizing: border-box;
}

.nep-spin-container {
    position: relative;
}

.nep-spin-container .nep-spin-loading {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 4;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.nep-spin-container .nep-spin-content {
    position: relative;
}

.nep-spin-container .nep-spin-content:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 3;
    width: 100%;
    height: 100%;
    background: #fff;
    opacity: 0;
    transition: all 0.3s;
    content: "";
}

.nep-spin-show .nep-spin-content:after {
    opacity: 0.5;
}

.nep-spin-default {
    position: relative;
    margin: auto;
}

@keyframes nep-spin-default-scale {
    0%,
    80%,
    to {
        transform: scale3d(0, 0, 1);
    }
    40% {
        transform: scaleX(1);
    }
}

@keyframes nep-spin-default-fade {
    0%,
    39%,
    to {
        opacity: 0.2;
    }
    40% {
        opacity: 1;
    }
}

.nep-spin-default-item {
    position: absolute;
    top: 0;
    left: 45%;
    width: 10%;
    height: 50%;
    transform-origin: 50% 100%;
}

.nep-spin-default-item div {
    height: 55%;
    margin: 0 auto;
    animation: nep-spin-default-fade 1s ease-in-out infinite both;
}

.nep-spin-default-scale div,
.nep-spin-default-scale svg {
    animation: nep-spin-default-scale 1s ease-in-out infinite both;
}

.nep-spin-default-fade div,
.nep-spin-default-fade svg {
    animation: nep-spin-default-fade 1s ease-in-out infinite both;
}

.nep-spin-default .nep-spin-default-item:nth-child(2) {
    transform: rotate(30deg);
}

.nep-spin-default .nep-spin-default-item:nth-child(2) div,
.nep-spin-default .nep-spin-default-item:nth-child(2) svg {
    animation-delay: -0.91666667s;
}

.nep-spin-default .nep-spin-default-item:nth-child(3) {
    transform: rotate(60deg);
}

.nep-spin-default .nep-spin-default-item:nth-child(3) div,
.nep-spin-default .nep-spin-default-item:nth-child(3) svg {
    animation-delay: -0.83333333s;
}

.nep-spin-default .nep-spin-default-item:nth-child(4) {
    transform: rotate(90deg);
}

.nep-spin-default .nep-spin-default-item:nth-child(4) div,
.nep-spin-default .nep-spin-default-item:nth-child(4) svg {
    animation-delay: -0.75s;
}

.nep-spin-default .nep-spin-default-item:nth-child(5) {
    transform: rotate(120deg);
}

.nep-spin-default .nep-spin-default-item:nth-child(5) div,
.nep-spin-default .nep-spin-default-item:nth-child(5) svg {
    animation-delay: -0.66666667s;
}

.nep-spin-default .nep-spin-default-item:nth-child(6) {
    transform: rotate(150deg);
}

.nep-spin-default .nep-spin-default-item:nth-child(6) div,
.nep-spin-default .nep-spin-default-item:nth-child(6) svg {
    animation-delay: -0.58333333s;
}

.nep-spin-default .nep-spin-default-item:nth-child(7) {
    transform: rotate(180deg);
}

.nep-spin-default .nep-spin-default-item:nth-child(7) div,
.nep-spin-default .nep-spin-default-item:nth-child(7) svg {
    animation-delay: -0.5s;
}

.nep-spin-default .nep-spin-default-item:nth-child(8) {
    transform: rotate(210deg);
}

.nep-spin-default .nep-spin-default-item:nth-child(8) div,
.nep-spin-default .nep-spin-default-item:nth-child(8) svg {
    animation-delay: -0.41666667s;
}

.nep-spin-default .nep-spin-default-item:nth-child(9) {
    transform: rotate(240deg);
}

.nep-spin-default .nep-spin-default-item:nth-child(9) div,
.nep-spin-default .nep-spin-default-item:nth-child(9) svg {
    animation-delay: -0.33333333s;
}

.nep-spin-default .nep-spin-default-item:nth-child(10) {
    transform: rotate(270deg);
}

.nep-spin-default .nep-spin-default-item:nth-child(10) div,
.nep-spin-default .nep-spin-default-item:nth-child(10) svg {
    animation-delay: -0.25s;
}

.nep-spin-default .nep-spin-default-item:nth-child(11) {
    transform: rotate(300deg);
}

.nep-spin-default .nep-spin-default-item:nth-child(11) div,
.nep-spin-default .nep-spin-default-item:nth-child(11) svg {
    animation-delay: -0.16666667s;
}

.nep-spin-default .nep-spin-default-item:nth-child(12) {
    transform: rotate(330deg);
}

.nep-spin-default .nep-spin-default-item:nth-child(12) div,
.nep-spin-default .nep-spin-default-item:nth-child(12) svg {
    animation-delay: -0.08333333s;
}

.nep-spin-ring {
    margin: auto;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.05);
    animation: nep-spin-ring-spin 1s linear infinite;
    border-radius: 50%;
}

@keyframes nep-spin-ring-spin {
    0% {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(359deg);
    }
}

.nep-spin-plane {
    margin: auto;
    animation: nep-spin-plane-spin 1s linear infinite;
}

@keyframes nep-spin-plane-spin {
    0% {
        transform: perspective(120px) rotateX(0deg) rotateY(0deg);
    }
    50% {
        transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
    }
    to {
        transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
    }
}

.nep-spin-pulse {
    margin: auto;
    animation: nep-spin-pulse-scale 1s ease-in-out infinite;
    border-radius: 100%;
}

@keyframes nep-spin-pulse-scale {
    0% {
        transform: scale3d(0, 0, 1);
    }
    to {
        opacity: 0;
        transform: scaleX(1);
    }
}

.nep-spin-wave {
    margin: auto;
    font-size: 10px;
    text-align: center;
    white-space: nowrap;
}

@keyframes nep-spin-wave-scale {
    0%,
    40%,
    to {
        transform: scaleY(0.4);
    }
    20% {
        transform: scaleX(1);
    }
}

.nep-spin-wave-item {
    display: inline-block;
    height: 100%;
    margin-right: 2px;
    animation: nep-spin-wave-scale 1.2s ease-in-out infinite;
}

.nep-spin-wave-item:last-child {
    margin-right: 0;
}

.nep-spin-wave .nep-spin-wave-item:nth-child(2) {
    animation-delay: -1.1s;
}

.nep-spin-wave .nep-spin-wave-item:nth-child(3) {
    animation-delay: -1s;
}

.nep-spin-wave .nep-spin-wave-item:nth-child(4) {
    animation-delay: -0.9s;
}

.nep-spin-wave .nep-spin-wave-item:nth-child(5) {
    animation-delay: -0.8s;
}

.nep-chasing-dots {
    position: relative;
    margin: auto;
    animation: nep-chasing-dots-rotate 2s linear infinite;
    text-align: center;
}

@keyframes nep-chasing-dots-rotate {
    to {
        transform: rotate(1turn);
    }
}

@keyframes nep-chasing-dots-bounce {
    0%,
    to {
        transform: scale3d(0, 0, 1);
    }
    50% {
        transform: scaleX(1);
    }
}

.nep-chasing-dots-item {
    position: absolute;
    top: 0;
    display: inline-block;
    width: 60%;
    height: 60%;
    animation: nep-chasing-dots-bounce 2s ease-in-out infinite;
    border-radius: 100%;
}

.nep-chasing-dots-item:last-child {
    top: auto;
    bottom: 0;
    animation-delay: -1s;
}

.nep-double-bounce {
    position: relative;
    margin: auto;
}

@keyframes nep-double-bounce-spin {
    0%,
    to {
        transform: scale3d(0, 0, 1);
    }
    50% {
        transform: scaleX(1);
    }
}

.nep-double-bounce-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    animation: nep-double-bounce-spin 2s ease-in-out infinite;
    border-radius: 50%;
    opacity: 0.6;
}

.nep-double-bounce-item:last-child {
    animation-delay: -1s;
}

.nep-cube-grid {
    margin: auto;
}

.nep-cube-grid-item {
    width: 33.33333%;
    height: 33.3333%;
    animation: nep-cube-grid-spin 1.3s ease-in-out infinite;
    float: left;
}

@keyframes nep-cube-grid-spin {
    0%,
    70%,
    to {
        transform: scaleX(1);
    }
    35% {
        transform: scale3D(0, 0, 1);
    }
}

.nep-cube-grid-item:first-child {
    animation-delay: 0.2s;
}

.nep-cube-grid-item:nth-child(2) {
    animation-delay: 0.3s;
}

.nep-cube-grid-item:nth-child(3) {
    animation-delay: 0.4s;
}

.nep-cube-grid-item:nth-child(4) {
    animation-delay: 0.1s;
}

.nep-cube-grid-item:nth-child(5) {
    animation-delay: 0.2s;
}

.nep-cube-grid-item:nth-child(6) {
    animation-delay: 0.3s;
}

.nep-cube-grid-item:nth-child(7) {
    animation-delay: 0s;
}

.nep-cube-grid-item:nth-child(8) {
    animation-delay: 0.1s;
}

.nep-cube-grid-item:nth-child(9) {
    animation-delay: 0.2s;
}

.nep-chasing-ring {
    position: relative;
    margin: auto;
}

@keyframes nep-chasing-ring-loading {
    0% {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(1turn);
    }
}

@keyframes nep-chasing-ring-loading1 {
    0% {
        transform: rotate(0deg);
    }
    44% {
        transform: rotate(225deg);
    }
    to {
        transform: rotate(1turn);
    }
}

@keyframes nep-chasing-ring-loading2 {
    0% {
        transform: rotate(75deg);
    }
    44% {
        transform: rotate(225deg);
    }
    to {
        transform: rotate(435deg);
    }
}

@keyframes nep-chasing-ring-loading3 {
    0% {
        transform: rotate(150deg);
    }
    44% {
        transform: rotate(225deg);
    }
    to {
        transform: rotate(510deg);
    }
}

@keyframes nep-chasing-ring-loading4 {
    0% {
        transform: rotate(225deg);
    }
    44% {
        transform: rotate(225deg);
    }
    to {
        transform: rotate(585deg);
    }
}

.nep-chasing-ring-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border: 1px solid transparent;
    border-radius: 100%;
    transform-origin: 50% 50%;
}

.nep-chasing-ring-item:first-child {
    animation: nep-chasing-ring-loading1 2s infinite;
}

.nep-chasing-ring-item:nth-child(2) {
    animation: nep-chasing-ring-loading2 2s infinite;
}

.nep-chasing-ring-item:nth-child(3) {
    animation: nep-chasing-ring-loading3 2s infinite;
}

.nep-chasing-ring-item:nth-child(4) {
    animation: nep-chasing-ring-loading4 2s infinite;
}

.nep-scale-circle {
    position: relative;
    margin: auto;
}

@keyframes nep-scale-circle-scale {
    0%,
    80%,
    to {
        transform: scale3d(0, 0, 1);
    }
    40% {
        transform: scaleX(1);
    }
}

@keyframes nep-scale-circle-fade {
    0%,
    39%,
    to {
        opacity: 0;
    }
    40% {
        opacity: 1;
    }
}

.nep-scale-circle-item {
    position: absolute;
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
}

.nep-scale-circle-item div {
    border-radius: 100%;
}

.nep-scale-circle-scale div,
.nep-scale-circle-scale svg {
    animation: nep-scale-circle-scale 1.2s ease-in-out infinite both;
}

.nep-scale-circle-fade div,
.nep-scale-circle-fade svg {
    animation: nep-scale-circle-fade 1.2s ease-in-out infinite both;
}

.nep-scale-circle .nep-scale-circle-item:nth-child(2) {
    transform: rotate(30deg);
}

.nep-scale-circle .nep-scale-circle-item:nth-child(2) div,
.nep-scale-circle .nep-scale-circle-item:nth-child(2) svg {
    animation-delay: -1.1s;
}

.nep-scale-circle .nep-scale-circle-item:nth-child(3) {
    transform: rotate(60deg);
}

.nep-scale-circle .nep-scale-circle-item:nth-child(3) div,
.nep-scale-circle .nep-scale-circle-item:nth-child(3) svg {
    animation-delay: -1s;
}

.nep-scale-circle .nep-scale-circle-item:nth-child(4) {
    transform: rotate(90deg);
}

.nep-scale-circle .nep-scale-circle-item:nth-child(4) div,
.nep-scale-circle .nep-scale-circle-item:nth-child(4) svg {
    animation-delay: -0.9s;
}

.nep-scale-circle .nep-scale-circle-item:nth-child(5) {
    transform: rotate(120deg);
}

.nep-scale-circle .nep-scale-circle-item:nth-child(5) div,
.nep-scale-circle .nep-scale-circle-item:nth-child(5) svg {
    animation-delay: -0.8s;
}

.nep-scale-circle .nep-scale-circle-item:nth-child(6) {
    transform: rotate(150deg);
}

.nep-scale-circle .nep-scale-circle-item:nth-child(6) div,
.nep-scale-circle .nep-scale-circle-item:nth-child(6) svg {
    animation-delay: -0.7s;
}

.nep-scale-circle .nep-scale-circle-item:nth-child(7) {
    transform: rotate(180deg);
}

.nep-scale-circle .nep-scale-circle-item:nth-child(7) div,
.nep-scale-circle .nep-scale-circle-item:nth-child(7) svg {
    animation-delay: -0.6s;
}

.nep-scale-circle .nep-scale-circle-item:nth-child(8) {
    transform: rotate(210deg);
}

.nep-scale-circle .nep-scale-circle-item:nth-child(8) div,
.nep-scale-circle .nep-scale-circle-item:nth-child(8) svg {
    animation-delay: -0.5s;
}

.nep-scale-circle .nep-scale-circle-item:nth-child(9) {
    transform: rotate(240deg);
}

.nep-scale-circle .nep-scale-circle-item:nth-child(9) div,
.nep-scale-circle .nep-scale-circle-item:nth-child(9) svg {
    animation-delay: -0.4s;
}

.nep-scale-circle .nep-scale-circle-item:nth-child(10) {
    transform: rotate(270deg);
}

.nep-scale-circle .nep-scale-circle-item:nth-child(10) div,
.nep-scale-circle .nep-scale-circle-item:nth-child(10) svg {
    animation-delay: -0.3s;
}

.nep-scale-circle .nep-scale-circle-item:nth-child(11) {
    transform: rotate(300deg);
}

.nep-scale-circle .nep-scale-circle-item:nth-child(11) div,
.nep-scale-circle .nep-scale-circle-item:nth-child(11) svg {
    animation-delay: -0.2s;
}

.nep-scale-circle .nep-scale-circle-item:nth-child(12) {
    transform: rotate(330deg);
}

.nep-scale-circle .nep-scale-circle-item:nth-child(12) div,
.nep-scale-circle .nep-scale-circle-item:nth-child(12) svg {
    animation-delay: -0.1s;
}

.nep-three-bounce {
    margin: auto;
    text-align: center;
}

@keyframes nep-three-bounce-bounce {
    0%,
    80%,
    to {
        transform: scale3d(0, 0, 1);
    }
    40% {
        transform: scaleX(1);
    }
}

.nep-three-bounce-item {
    display: inline-block;
    animation: nep-three-bounce-bounce 1.4s ease-in-out 0s infinite both;
}

.nep-three-bounce-item div {
    border-radius: 100%;
}

.nep-three-bounce-item:first-child {
    animation-delay: -0.32s;
}

.nep-three-bounce-item:nth-child(2) {
    animation-delay: -0.16s;
}

.nep-four-dots {
    position: relative;
    margin: auto;
    animation: nep-four-dots-rotate 1.6s linear infinite;
}

@keyframes nep-four-dots-rotate {
    0% {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(359deg);
    }
}

@keyframes nep-four-dots-fade {
    0% {
        opacity: 0.2;
    }
    50% {
        opacity: 0.9;
    }
    to {
        opacity: 0.2;
    }
}

.nep-four-dots-item {
    position: absolute;
    width: 32%;
    height: 32%;
    animation: nep-four-dots-fade 1.6s linear infinite;
    line-height: 0;
    opacity: 0.2;
}

.nep-four-dots-item div {
    border-radius: 100%;
}

.nep-four-dots-item:first-child {
    top: 10%;
    left: 10%;
}

.nep-four-dots-item:nth-child(2) {
    top: 10%;
    right: 10%;
    animation-delay: 0.4s;
}

.nep-four-dots-item:nth-child(3) {
    right: 10%;
    bottom: 10%;
    animation-delay: 0.8s;
}

.nep-four-dots-item:nth-child(4) {
    bottom: 10%;
    left: 10%;
    animation-delay: 1.2s;
}


/** Etc styles **/

.nep-label {
    color: var(--unnamed-color-55565a);
    text-align: left;
    font: normal normal normal 11px/12px Helvetica Neue;
    letter-spacing: 0px;
    color: #55565a;
    opacity: 1;
    padding-bottom: 6px;
    margin-left: 12px;
}

.nep-input {
    padding-left: 12px;
}

.nep-input ::placeholder {
    color: var(--unnamed-color-cac9c7);
    text-align: left;
    font: normal normal normal 14px/17px Helvetica Neue;
    letter-spacing: 0px;
    color: red;
    opacity: 1;
}

.nep-save-dialog {
    padding-left: 10px;
    padding-top: 37px;
    padding-bottom: 50px;
    padding-right: 30px;
}

.nep-save-dialog .nep-input {
    color: var(--unnamed-color-55565a);
    text-align: left;
    font: normal normal normal 14px/17px "Helvetica Neue LT W05_55 Roman",Arial, Verdana, Tahoma,sans-serif;
    /* letter-spacing: 0px; */
    color: #000000;
    opacity: 1;
}

.nep-error {
    color: var(--unnamed-color-c62828);
    text-align: left;
    font: normal normal normal 12px Helvetica;
    letter-spacing: 0px;
    color: #c62828;
    opacity: 1;
    line-height: 20px;
}

.nep-panel {
    margin: 20px;
}

.nep-dropdown-menu-item.nep-dropdown-active,
.nep-dropdown-menu-item:hover {
    display: block;
    background: #4061C7;
    background: var(--menu-item-active-bg, #4061C7);
    padding: 8px 16px;
    color: #55565a;
    color: var(--gray-800, #55565a);
    line-height: 1.42857143;
    text-decoration: none;
}