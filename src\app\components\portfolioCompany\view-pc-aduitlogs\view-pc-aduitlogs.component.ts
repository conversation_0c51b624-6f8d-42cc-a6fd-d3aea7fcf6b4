import { Component, Input, OnInit,  ViewChild } from "@angular/core";
import { AuditService } from "src/app/services/audit.service";
import { DataAuditModel, PcDataAuditModel } from "src/app/services/DataAuditModel";
import { MiscellaneousService } from "src/app/services/miscellaneous.service";
import * as moment from "moment";
import { ToastContainerDirective, ToastrService } from "ngx-toastr";
import { KPIModulesEnum } from "src/app/services/permission.service";
import { KpiConstants, DataAnalyticsConstants, GlobalConstants, InvestmentKpiConstants,KpiTypesConstants, CreditKpiConstants } from "src/app/common/constants";

@Component({
  selector: "app-view-pc-aduitlogs",
  templateUrl: "./view-pc-aduitlogs.component.html",
  styleUrls: ["./view-pc-aduitlogs.component.scss"],
})
export class ViewPCAduitlogsComponent implements OnInit {
  isNewAudit: boolean = false;
  constructor(
    private aduitservice: AuditService,
    private miscService: MiscellaneousService,
    private toastrService: ToastrService
  ) {}
  public KPI: string = "...";
  public docTableHeaders = [];
  public documents = [];
  dataAuditModel: DataAuditModel;
  datauditmodellog: DataAuditLogValueModel;
  ShowRestoreDialog: boolean = false;
  eventdata: any = {};
  data: any = {};
  isLoader: boolean = true;
  isMasterKpiData: boolean = false;
  pcAuditModel: PcDataAuditModel = null;
  numberWithCommas(x) {
    if (x === null || x === undefined) {
      return x;
    }
    const safeNumberRegex = /^[+-]?\d+(\.\d+)?$/;
    if (String(x).match(safeNumberRegex)) {
      x = parseFloat(x).toLocaleString("en-us", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    }
    return x;
  }
  @ViewChild(ToastContainerDirective, {})
  toastContainer: ToastContainerDirective;
  ngOnInit() {
    this.toastrService.overlayContainer = this.toastContainer;
    this.isLoader = true;
    if (history.state.data !== undefined) {
      this.data = history.state.data;
      this.isNewAudit = this.data.isNewAudit == undefined ? false : this.data.isNewAudit;
      if (history.state.data?.ModuleId == KPIModulesEnum.Investment) {
        this.data = JSON.parse(sessionStorage.getItem(InvestmentKpiConstants.InvestmentKpiAuditLocalStorage));
      }     
      this.setAuditModel();  
      sessionStorage.setItem("data", JSON.stringify(this.data));
    }
    else {
        let currentModule = sessionStorage.getItem(GlobalConstants.CurrentModule);
        this.GetJsonData(currentModule);
      this.isNewAudit = this.data.isNewAudit == undefined ? false : this.data.isNewAudit;
      this.setAuditModel();  
    }
    this.isMasterKpiData = this.data.ModuleId != undefined;
    if (this.data !== null) {
      this.KPI =
        this.data.KPI +
        " - " +
        this.data.AttributeName +
        " - " +
        this.data.header;
      this.dataAuditModel = {
        FieldName: this.data.AttributeName,
        AttributName: this.data.KPI,
        MonthAndYear: this.data.header,
        PortfolioCompanyID: this.data.PortfolioCompanyID,
        AttributeId: this.data.AttributeId,
        Comments: this.data.Comments,
        Attributid: this.data.AttributeId,
        ModuleId: this.data.ModuleId,
      };
      let that = this;
      this.docTableHeaders = [
        {
          field: "activityoccursin",
          header: "Activity occurs in",
          sort: false,
          valueCustomClass: "auditcell",
          headerWidthClass: "auditheader",
          valueWidthClass: "",
        },
        {
          field: "newvalue",
          header: "New Value",
          sort: false,
          valueCustomClass: "auditcell",
          headerWidthClass: "auditheader",
          valueWidthClass: "",
        },
        {
          field: "oldvalue",
          header: "Old Value",
          sort: false,
          valueCustomClass: "auditcell",
          headerWidthClass: "auditheader",
          valueWidthClass: "",
        },
        {
          field: "activityon",
          header: "Activity on",
          sort: false,
          valueCustomClass: "auditcell",
          headerWidthClass: "auditheader",
          valueWidthClass: "",
        },
        {
          field: "activityby",
          header: "Activity by",
          sort: false,
          valueCustomClass: "auditcell",
          headerWidthClass: "auditheader",
          valueWidthClass: "",
        },
        {
          field: "modofchange",
          header: "Mode of Change",
          sort: false,
          valueCustomClass: "auditcell ActionItem",
          headerWidthClass: "auditheader",
          valueWidthClass: "",
        },
      ];
      if (!this.isMasterKpiData) this.GetAuditlog(that);
      else {
        if (
          (this.data.ModuleId == KPIModulesEnum.TradingRecords ||
          this.data.ModuleId == KPIModulesEnum.CreditKPI)
        ) {
          if(!this.isNewAudit)
            this.GetMasterAuditlog(that);
        } 
        else if (
          (this.data.ModuleId == KPIModulesEnum.Company ||
          this.data.ModuleId == KPIModulesEnum.Operational)
        ){
          this.GetCompanyAndOperationalAuditlog();
        }
        else {
          this.GetFinancialAuditlog();
        }
      }
    }
  }

  /**
   * Retrieves JSON data based on the current module.
   * @param currentModule - The current module.
   */
  GetJsonData(currentModule: string) {
    switch (currentModule) {
      case KpiTypesConstants.INVESTMENT_KPI:
        this.data = JSON.parse(sessionStorage.getItem(InvestmentKpiConstants.InvestmentKpiAuditLocalStorage));
        break;
      case KpiTypesConstants.BALANCE_SHEET_KPI:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.BalanceSheetAuditLocalStorage));
        break;
      case KpiTypesConstants.PROFIT_LOSS_KPI:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.ProfitandLossAuditLocalStorage));
        break;
      case KpiTypesConstants.CASHFLOW_KPI:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CashFlowAuditLocalStorage));
        break;
      case KpiTypesConstants.CREDIT_KPI:
        this.data = JSON.parse(sessionStorage.getItem(CreditKpiConstants.CreditKpiAuditLocalStorage));
        break;
      case KpiTypesConstants.COMPANY_KPI:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CompanyKpiAuditLocalStorage));
        break;
      case KpiTypesConstants.OPERATIONAL_KPI:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.OperationalKpiAuditLocalStorage));
        break;
      case KpiTypesConstants.IMPACT_KPI:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.ImpactKpiAuditLocalStorage));
        break;
      case KpiTypesConstants.CapTable_KPI1:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.CapTable1));
        break;
      case KpiTypesConstants.CapTable_KPI2:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.CapTable2));
        break;
      case KpiTypesConstants.CapTable_KPI3:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.CapTable3));
        break;
      case KpiTypesConstants.CapTable_KPI4:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.CapTable4));
        break;
      case KpiTypesConstants.CapTable_KPI5:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.CapTable5));
        break;
      case KpiTypesConstants.CapTable_KPI6:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.CapTable6));
        break;
      case KpiTypesConstants.CapTable_KPI7:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.CapTable7));
        break;
      case KpiTypesConstants.CapTable_KPI8:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.CapTable8));
        break;
      case KpiTypesConstants.CapTable_KPI9:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.CapTable9));
        break;
      case KpiTypesConstants.CapTable_KPI10:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.CapTable10));
        break;
      case KpiTypesConstants.OtherCapTable_KPI1:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.OtherCapTable1));
        break;
      case KpiTypesConstants.OtherCapTable_KPI2:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.OtherCapTable2));
        break;
      case KpiTypesConstants.OtherCapTable_KPI3:  
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.OtherCapTable3));
        break;
      case KpiTypesConstants.OtherCapTable_KPI4:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.OtherCapTable4));
        break;
      case KpiTypesConstants.OtherCapTable_KPI5:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.OtherCapTable5));
        break;
      case KpiTypesConstants.OtherCapTable_KPI6:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.OtherCapTable6));
        break;
      case KpiTypesConstants.OtherCapTable_KPI7:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.OtherCapTable7));
        break;
      case KpiTypesConstants.OtherCapTable_KPI8:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.OtherCapTable8));
        break;
      case KpiTypesConstants.OtherCapTable_KPI9:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.OtherCapTable9));
        break;
      case KpiTypesConstants.OtherCapTable_KPI10:
        this.data = JSON.parse(sessionStorage.getItem(GlobalConstants.CapTableAuditLocalStorage + '_' + KPIModulesEnum.OtherCapTable10));
        break;
    }
  }

  /**
   * Sets the audit model based on the provided data.
   */
  setAuditModel() {
    this.pcAuditModel = {
      CompanyId: this.data.PortfolioCompanyID,
      AttributeId: this.data.AttributeId,
      ValueType: this.data.Comments,
      ModuleId: this.data.ModuleId,
      KpiId: this.data.KpiId,
      Period: this.data.header,
      PeriodId:this.data?.periodId,
      AsOfPeriod: this.data?.AsOfPeriod == undefined ? null : this.data?.AsOfPeriod,
      ColumnKpiId: this.data.columnKpiId == undefined ? null : this.data?.columnKpiId,
      ColumnKpi: this.data.columnKpi == undefined ? null : this.data?.columnKpi,
      ColumnKpiInfo:this.data.columnKpiInfo == undefined ? null : this.data?.columnKpiInfo,
      SectionId: this.data.SectionId == undefined ? null : this.data?.SectionId
    };
  }

  /**
   * Retrieves audit logs for the portfolio company.
   * @param that - The reference to the current instance of the component.
   */
  private GetAuditlog(that: this) {   
    this.aduitservice.getDataLog(this.dataAuditModel).subscribe((result) => {
      that.documents = result.body?.map(
        (s: {
          fieldName: any;
          newValue: string;
          oldValue: string;
          createdOn: string | number | Date;
          createdBy: any;
          description: any;
          attributeID: any;
          monthAndYear: any;
          newCurrencyType: any;
          oldCurrencyType: any;
          portfolioCompanyId: any;
          comments: any;
        }) => {
          return {
            activityoccursin: s.fieldName,
            newvalue:
              this.data.KPI == KpiConstants.INVESTMENT_KPI &&
              s.newCurrencyType == "Text" &&
              s.newValue != null
                ? s.newValue
                : s.newValue != null && s.newValue != ""
                ? this.numberWithCommas(s.newValue) == "NaN"
                  ? s.newValue
                  : this.numberWithCommas(s.newValue)
                : "NA",
            oldvalue:
              this.data.KPI == KpiConstants.INVESTMENT_KPI &&
              s.newCurrencyType == "Text" &&
              s.oldValue != null
                ? s.oldValue
                : s.oldValue != null && s.oldValue != ""
                ? this.numberWithCommas(s.oldValue) == "NaN"
                  ? s.oldValue
                  : this.numberWithCommas(s.oldValue)
                : "NA",
            activityon: moment(new Date(s.createdOn)).format(
              "D-MMM-YYYY,HH:mm"
            ),
            activityby: s.createdBy,
            modofchange: s.description != null ? s.description : "File upload",
            attributeID: s.attributeID,
            monthAndYear: s.monthAndYear,
            newCurrencyType: s.newCurrencyType,
            oldCurrencyType: s.oldCurrencyType,
            fieldName: s.fieldName,
            portfolioCompanyId: s.portfolioCompanyId,
            comments: s.comments,
          };
        }
      );
      this.isLoader = false;
    });
  }

  /**
   * Retrieves the master audit log for the portfolio company.
   * @param that - The reference to the current instance of the component.
   */
  private GetMasterAuditlog(that: this) {
    this.aduitservice
      .getMasterKpiAuditLog(this.dataAuditModel)
      .subscribe((result) => {
        that.documents = result.map((s) => {
          return {
            activityoccursin: s.fieldName,
            newvalue:
              s.moduleId == KPIModulesEnum.TradingRecords &&
              s.newCurrency == "Text" &&
              s.newValue != null
                ? s.newValue
                : s.newValue != null && s.newValue != ""
                ? this.numberWithCommas(s.newValue) == "NaN"
                  ? s.newValue
                  : this.numberWithCommas(s.newValue)
                : "NA",
            oldvalue:
              s.moduleId == KPIModulesEnum.TradingRecords &&
              s.newCurrency == "Text" &&
              s.oldValue != null
                ? s.oldValue
                : s.oldValue != null && s.oldValue != ""
                ? this.numberWithCommas(s.oldValue) == "NaN"
                  ? s.oldValue
                  : this.numberWithCommas(s.oldValue)
                : "NA",
            activityon: moment(new Date(s.createdOn)).format(
              "D-MMM-YYYY,HH:mm"
            ),
            activityby: s.createdBy,
            modofchange: s.auditType,
            attributeID: s.attributeId,
            monthAndYear: s.monthAndYear,
            newCurrency: s.newCurrency,
            oldCurrency: s.oldCurrency,
            fieldName: s.fieldName,
            portfolioCompanyId: s.portfolioCompanyId,
            comments: s.comments,
          };
        });
        this.isLoader = false;
      });
  }

  /**
   * Retrieves the company and operational audit log data.
   */
  GetCompanyAndOperationalAuditlog() {
    this.aduitservice.getPcAuditLogData(this.pcAuditModel).subscribe({
      next: (result) => {
        if (result?.length > 0) {
          this.documents = result.map((s) => {
            return {
              activityoccursin: s.fieldName,
              newvalue:
                s.newCurrency == "Text" && s.newValue != null
                  ? s.newValue
                  : s.newValue != null && s.newValue != ""
                  ? this.numberWithCommas(s.newValue) == "NaN"
                    ? s.newValue
                    : this.numberWithCommas(s.newValue)
                  : "NA",
              oldvalue:
                s.newCurrency == "Text" && s.oldValue != null
                  ? s.oldValue
                  : s.oldValue != null && s.oldValue != ""
                  ? this.numberWithCommas(s.oldValue) == "NaN"
                    ? s.oldValue
                    : this.numberWithCommas(s.oldValue)
                  : "NA",
              activityon: moment(new Date(s.createdOn)).format(
                "D-MMM-YYYY,HH:mm"
              ),
              activityby: s.createdBy,
              modofchange: s.auditType,
              attributeID: s.attributeId,
              monthAndYear: s.monthAndYear,
              newCurrency: s.newCurrency,
              oldCurrency: s.oldCurrency,
              fieldName: s.fieldName,
              portfolioCompanyId: s.portfolioCompanyId,
              comments: s.comments,
            };
          });
          this.isLoader = false;
        }
        this.isLoader = false;
      },
      error: (error) => {
        this.isLoader = false;
      },
    });
  }

  /**
   * Retrieves the financial model data for the portfolio company.
   * @returns An object containing the financial model data.
   */
  getFinancialModel() {
    return {
      PortfolioCompanyID: this.data.PortfolioCompanyID,
      AttributeId: this.data.AttributeId,
      ValueType: this.data.Comments,
      ModuleId: this.data.ModuleId,
      MappingId: this.data.MappingId,
    };
  }

  /**
   * Retrieves the financial audit log for a portfolio company.
   */
  GetFinancialAuditlog() {
    this.aduitservice
      .geFinancialKpiAuditLog(this.getFinancialModel())
      .subscribe({
        next: (result) => {
          if (result?.length > 0) {
            this.documents = result.map((s) => {
              return {
                activityoccursin: s.fieldName,
                newvalue:
                  s.newCurrency == "Text" && s.newValue != null
                    ? s.newValue
                    : s.newValue != null && s.newValue != ""
                    ? this.numberWithCommas(s.newValue) == "NaN"
                      ? s.newValue
                      : this.numberWithCommas(s.newValue)
                    : "NA",
                oldvalue:
                  s.newCurrency == "Text" && s.oldValue != null
                    ? s.oldValue
                    : s.oldValue != null && s.oldValue != ""
                    ? this.numberWithCommas(s.oldValue) == "NaN"
                      ? s.oldValue
                      : this.numberWithCommas(s.oldValue)
                    : "NA",
                activityon: moment(new Date(s.createdOn)).format(
                  "D-MMM-YYYY,HH:mm"
                ),
                activityby: s.createdBy,
                modofchange: s.auditType,
                attributeID: s.attributeId,
                monthAndYear: s.monthAndYear,
                newCurrency: s.newCurrency,
                oldCurrency: s.oldCurrency,
                fieldName: s.fieldName,
                portfolioCompanyId: s.portfolioCompanyId,
                comments: s.comments,
              };
            });
            this.isLoader = false;
          }
          this.isLoader = false;
        },
        error: (error) => {
          this.isLoader = false;
        },
      });
  }

  DownloadAudit() {
    this.aduitservice
      .ExportDataLog(this.dataAuditModel)
      .subscribe((response) => this.miscService.downloadExcelFile(response));
  }

  DownloadMasterAudit() {
    if (
      this.data.ModuleId == KPIModulesEnum.TradingRecords ||
      this.data.ModuleId == KPIModulesEnum.CreditKPI
    ) {
      this.aduitservice
        .exportMasterKpiAuditLog(this.dataAuditModel)
        .subscribe((response) => this.miscService.downloadExcelFile(response));
    } else {
      this.DownloadFinancialAuditAudit();
    }
  }
  DownloadFinancialAuditAudit() {
    this.aduitservice
      .exportFinancialKpiAuditLog(this.getFinancialModel())
      .subscribe((response) => this.miscService.downloadExcelFile(response));
  }
  restoreFinancialData() {
    let model = {
      AttributeId: this.datauditmodellog.AttributeID,
      KpiValue: this.datauditmodellog.OldValue,
      PortfolioCompanyId: this.datauditmodellog.portfolioCompanyId,
      ModuleId: this.data.ModuleId,
      MappingId: this.data.MappingId,
      ValueType: this.data.Comments,
    };
    this.aduitservice.revertFinancialKpiData(model).subscribe({
      next: (result) => {
        this.GetFinancialAuditlog();
        this.toastrService.success(GlobalConstants.RevertSuccess, "", {
          positionClass: "toast-center-center",
        });
      },
      error: (error) => {
        this.toastrService.error(GlobalConstants.SomethingWentWrong, "", {
          positionClass: "toast-center-center",
        });
      },
    });
  }
  RowItemClicked(event: any) {
    this.ShowRestoreDialog = true;
    this.eventdata = event;
  }
  CancelEvent() {
    this.ShowRestoreDialog = false;
  }
  RestoreEvent() {
    this.ShowRestoreDialog = false;
    this.datauditmodellog = {
      AttributeID: this.eventdata.attributeID,
      AttributeName: this.data.KPI,
      OldValue: this.eventdata.oldvalue,
      MonthAndYear: this.eventdata.monthAndYear,
      newCurrencyType: this.eventdata.newCurrencyType,
      oldCurrencyType: this.eventdata.oldCurrencyType,
      fieldName: this.eventdata.fieldName,
      portfolioCompanyId: this.eventdata.portfolioCompanyId,
      comments: this.eventdata.comments,
      description: GlobalConstants.Manual,
    };
    let that = this;
    if (!this.isMasterKpiData) {
      if (this.data?.KPI == DataAnalyticsConstants.Operational_KPIs) {
        this.aduitservice
          .RevertKpiData(this.datauditmodellog)
          .subscribe((result) => {
            this.GetAuditlog(that);
            this.toastrService.success(GlobalConstants.RevertSuccess, "", {
              positionClass: "toast-center-center",
            });
          });
      } else {
        this.aduitservice
          .UpdateKPIData(this.datauditmodellog)
          .subscribe((result) => {
            this.GetAuditlog(that);
            this.toastrService.success(GlobalConstants.RevertSuccess, "", {
              positionClass: "toast-center-center",
            });
          });
      }
    } else {
      if (
        this.data.ModuleId == KPIModulesEnum.TradingRecords ||
        this.data.ModuleId == KPIModulesEnum.CreditKPI
      ) {
        this.aduitservice
          .revertMasterKpiData({
            AttributeId: this.datauditmodellog.AttributeID,
            Value: this.datauditmodellog.OldValue,
            PortfolioCompanyID: this.datauditmodellog.portfolioCompanyId,
            ModuleId: this.data.ModuleId,
          })
          .subscribe({
            next: (result) => {
              this.GetMasterAuditlog(that);
              this.toastrService.success(GlobalConstants.RevertSuccess, "", {
                positionClass: "toast-center-center",
              });
            },
            error: (error) => {
              this.toastrService.error(GlobalConstants.SomethingWentWrong, "", {
                positionClass: "toast-center-center",
              });
            },
          });
      } else {
        this.restoreFinancialData();
      }
    }
  }
}
interface DataAuditLogValueModel {
  AttributeID: number;
  AttributeName: string;
  OldValue: string;
  MonthAndYear: string;
  newCurrencyType: string;
  oldCurrencyType: string;
  fieldName: string;
  portfolioCompanyId: number;
  comments: string;
  description: string;
}
