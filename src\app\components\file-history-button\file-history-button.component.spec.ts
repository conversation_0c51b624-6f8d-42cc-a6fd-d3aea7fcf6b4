import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of } from 'rxjs';
import { FileHistoryButtonComponent } from './file-history-button.component';
import { FileHistoryService } from '../../services/file-history.service';
import { FileUploadProgressService } from '../../services/file-upload-progress.service';

describe('FileHistoryButtonComponent', () => {
  let component: FileHistoryButtonComponent;
  let fixture: ComponentFixture<FileHistoryButtonComponent>;

  beforeEach(() => {
    const mockFileHistoryService = jasmine.createSpyObj('FileHistoryService', ['toggleFileHistoryPopup']);
    const mockFileUploadProgressService = jasmine.createSpyObj('FileUploadProgressService', ['newUploadCount$']);
    mockFileUploadProgressService.newUploadCount$ = of(0);

    TestBed.configureTestingModule({
      declarations: [FileHistoryButtonComponent],
      imports: [HttpClientTestingModule],
      providers: [
        { provide: FileHistoryService, useValue: mockFileHistoryService },
        { provide: FileUploadProgressService, useValue: mockFileUploadProgressService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    });
    fixture = TestBed.createComponent(FileHistoryButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
