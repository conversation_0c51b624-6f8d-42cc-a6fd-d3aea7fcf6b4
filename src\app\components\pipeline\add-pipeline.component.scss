.add-pipeline {
    .desc-section {
        background: #FFFFFF 0% 0% no-repeat padding-box;
        box-shadow: 0px 0px 12px #00000014;
        border: 1px solid #DEDFE0;
        border-radius: 4px;
        opacity: 1;
    }
    .description-text {
        border: none !important;
        color: #212121 !important;
    }
    .desc-header {
        padding: 16px !important;
    }
    .desc-section-pad {
        margin-top: 20px;
        padding: 16px !important;
    }
    .no-margin {
        margin: none !important;
    }
    label {
        margin-top: 0px !important;
        letter-spacing: 0px;
        color: #55565A;
        opacity: 1;
    }
    .form-group {
        margin-top: 0px !important;
    }
    .section-top {
        padding-top: 24px;
    }
    .default-txt {
        height: 35px !important;
    }
    label:after {
        padding-left: 2px;
        content: "*";
        color: red;
    }
}

.fixed-footer {
    position: fixed;
    bottom: 0;
    right: 0;
    background: #FAFAFB 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 6px #00000014;
    border: 1px solid #DEDFE0;
    opacity: 1;
    padding-top: 4px;
    padding-bottom: 2px;

    .btn-primary:disabled {
        color: #FFFFFF !important;
        background-color: #4061C7 !important;
        &:hover {
            cursor: not-allowed;
        }
    }
}