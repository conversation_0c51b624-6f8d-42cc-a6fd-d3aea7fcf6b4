import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EsgChartComponent } from './esg-chart.component';
import { EsgService } from "../../../services/esg.services";
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { of } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'; // Import CUSTOM_ELEMENTS_SCHEMA
import { KPIModulesEnum } from 'src/app/services/permission.service';
import { FinancialValueUnitsEnum } from 'src/app/services/miscellaneous.service';
import { KpiInfo, PeriodTypeFilterOptions } from 'src/app/common/constants';

describe('EsgChartComponent', () => {
  let component: EsgChartComponent;
  let fixture: ComponentFixture<EsgChartComponent>;
  let mockEsgService = jasmine.createSpyObj(['getQueryFilter', 'getEsgDataUpdatedFlag', 'getCompanyKpiValueByEsgModuleId', 'getCurrentDecimal','getEsgSelection']);
  let mockPortfolioCompanyService = jasmine.createSpyObj(['getChartsKpiData']);

  beforeEach(async () => {
    mockEsgService.getQueryFilter.and.returnValue(of({}));
    mockEsgService.getEsgDataUpdatedFlag.and.returnValue(of({}));
    mockEsgService.getEsgSelection.and.returnValue(of({}));
    mockEsgService.getCompanyKpiValueByEsgModuleId.and.returnValue(of({}));
    mockPortfolioCompanyService.getChartsKpiData.and.returnValue(of({}));
    mockEsgService.getCompanyKpiValueByEsgModuleId.and.returnValue(of({ typeId: 1 }));
    mockEsgService.getCurrentDecimal.and.returnValue(of({}));

    await TestBed.configureTestingModule({
      declarations: [ EsgChartComponent ],
      providers: [
        { provide: EsgService, useValue: mockEsgService },
        { provide: PortfolioCompanyService, useValue: mockPortfolioCompanyService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA] // Add this line
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(EsgChartComponent);
    component = fixture.componentInstance;
    component.selectedSubpageData = { subPageId: 1, esgKpiList: [] };
    component.companyKPIList = []; // Initialize companyKPIList as an empty array
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('should set ifNoDataAvailable to true when companyKPIList is empty', () => {
    component.companyKPIList = []; // Initialize companyKPIList as an empty array
    component.hiddingDivision();
    expect(component.ifNoDataAvailable).toBe(true);
  });
  it('should set ifNoDataAvailable to false when companyKPIList is not empty', () => {
    component.companyKPIList = ['test'];
    component.hiddingDivision();
    expect(component.ifNoDataAvailable).toBe(false);
  });
  it('should set ifNoDataAvailable to true when companyKPIList is empty', () => {
    component.hiddingDivision();
    expect(component.ifNoDataAvailable).toBe(true);
  });

  it('should set ifNoDataAvailable to false when companyKPIList is not empty', () => {
    component.companyKPIList = ['test'];
    component.hiddingDivision();
    expect(component.ifNoDataAvailable).toBe(false);
  });

  it('should handle changes to selectedSubpageData when subPageId is undefined', () => {
    component.selectedSubpageData = { subPageId: undefined, esgKpiList: [] };
    component.ngOnChanges({ selectedSubpageData: {} as any });
    expect(component.companyKPIList).toEqual([]);
    expect(component.KPIChartData).toEqual([]);
  });

 it('should emit valueChange event when valueChanged is called', () => {
    spyOn(component.valueChange, 'emit');
    component.valueChanged('test');
    expect(component.valueChange.emit).toHaveBeenCalledWith('test');
  });
  it('should create filter body with default values when searchFilter is not provided', () => {
    const result = component.applyFilterBody(null);
    expect(result.CompanyId).toEqual(component.selectedCompany?.portfolioCompanyID.toString());
    expect(result.portfolioCompanyID).toEqual(component.selectedCompany?.portfolioCompanyID.toString());
    expect(result.SubPageModuleId).toEqual(component.selectedSubpageData?.subPageId);
    expect(result.moduleId).toEqual(KPIModulesEnum.ESG);
    expect(result.kpiId).toEqual(component.selectedCompanyKPI?.kpiId);
    expect(result.unit).toEqual(FinancialValueUnitsEnum.Absolute);
    expect(result.isAnnually).toBeFalse();
    expect(result.isQuarterly).toBeTrue();
    expect(result.ChartType).toEqual(component.typeField);
    
  });
  it('should set isQuarterly to true and isAnnually to false when typeField is Quarterly', () => {
    component.typeField = PeriodTypeFilterOptions.Quarterly;
    const result = component.applyFilterBody(null);
    expect(result.isQuarterly).toBeTrue();
    expect(result.isAnnually).toBeFalse();
  });
  it('should set isQuarterly to false and isAnnually to true when typeField is Annual', () => {
    component.typeField = PeriodTypeFilterOptions.Annual;
    const result = component.applyFilterBody(null);
    expect(result.isQuarterly).toBeFalse();
    expect(result.isAnnually).toBeTrue();
  });
  it('should set moduleCurrency to currencyCode when kpiInfo is Currency', () => {
    component.companyDetails = { reportingCurrencyDetail: { currencyCode: 'USD' } };
    component.setSymbol({ kpiInfo: KpiInfo.Currency });
    expect(component.moduleCurrency).toEqual('USD');
  });

  it('should set moduleCurrency to empty string when kpiInfo is Currency and currencyCode is not defined', () => {
    component.companyDetails = { reportingCurrencyDetail: {} };
    component.setSymbol({ kpiInfo: KpiInfo.Currency });
    expect(component.moduleCurrency).toEqual('');
  });

  it('should set moduleCurrency to Number when kpiInfo is Number', () => {
    component.setSymbol({ kpiInfo: KpiInfo.Number });
    expect(component.moduleCurrency).toEqual(KpiInfo.Number);
  });

  it('should set moduleCurrency to Percentage when kpiInfo is Percentage', () => {
    component.setSymbol({ kpiInfo: KpiInfo.Percentage });
    expect(component.moduleCurrency).toEqual(KpiInfo.Percentage);
  });
  it('should set moduleCurrency to Multiple when kpiInfo is Multiple', () => {
    component.setSymbol({ kpiInfo: KpiInfo.Multiple });
    expect(component.moduleCurrency).toEqual(KpiInfo.Multiple);
  });
  
  it('should call QuaterlyAndAnnual with empty array and getCompanykpi when selectedKPIData is not found', () => {
    component.KpiTableData = [{ kpiId: 'testKpiId', kpiData: 'testKpiData' }];
    component.selectedCompanyKPI = { kpiId: 'otherKpiId' };
    spyOn(component, 'QuaterlyAndAnnual');
    spyOn(component, 'getCompanykpi');
    component.OnCompanyKPIChange();
    expect(component.QuaterlyAndAnnual).toHaveBeenCalledWith([]);
    expect(component.getCompanykpi).toHaveBeenCalled();
  });

  it('should not call QuaterlyAndAnnual and only call getCompanykpi when KpiTableData is empty', () => {
    component.KpiTableData = [];
    spyOn(component, 'QuaterlyAndAnnual');
    spyOn(component, 'getCompanykpi');
    component.OnCompanyKPIChange();
    expect(component.QuaterlyAndAnnual).not.toHaveBeenCalled();
    expect(component.getCompanykpi).toHaveBeenCalled();
  });
});