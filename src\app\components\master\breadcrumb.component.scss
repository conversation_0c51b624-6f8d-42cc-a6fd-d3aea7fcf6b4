@import 'src/assets/dist/css/font.scss';
@import '../../../_variables.scss';

.breadcrumb {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 0;
    list-style: none;
    background-color: transparent;
    padding: 0 0 0 0.5rem !important;
}
.breadcrumb-item{
    display: flex;
    justify-content: center;
    align-items: center;
}
.breadcrumb-item a {
    text-decoration: none;
    @extend .Body-R;
    color: $Neutral-Gray-60;

    &:hover {
        text-decoration: none;
        color: $Primary-78;
        @extend .S-M;
    }
}
.cursor-disable{
    cursor: not-allowed !important;
    pointer-events: none !important;
    &:hover {
        color: $Neutral-Gray-60;
        @extend .S-M;
    }
}

.breadcrumb-item.active {
    a{
        color: $Primary-78 !important;
        @extend .S-M;
    }
}

.breadcrumb-container {
    position: fixed;
    width: 100%;
    z-index: 900;
    padding: 0.75rem 1.25rem;
    background: $Neutral-Gray-02;
}

.breadcrumb-item::before {
    display: none !important;
    content: '' !important;
}