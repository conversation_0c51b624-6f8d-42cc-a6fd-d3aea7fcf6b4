﻿@import "../../../variables.scss";

.wrapper {
    height: 100%;
    overflow: hidden;
    width: 100%;
}

.content-page {
    height: 100vh;
    .content {
        height: calc(100% - 50px);
        overflow-y: auto;
    }
}

.menu-expanded {
    margin-left: 240px;
}

.menu-collapsed {
    margin-left: 60px;
}

.side-bar-font{
    font-size: 14px;
    border: none;
    color: white;
    // height: 16px;
    color: #FFFFFF;
}

.expanded-menu
{
  padding-left:  12px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

::ng-deep{
  .mat-custom-sidenav{
    ::-webkit-scrollbar-thumb {
      background-color: hsla(0,0%,90%,.5)!important;
      max-height: 20% !important;
      height: 10% !important;
    }
    ::-webkit-scrollbar-track {
      background-color: transparent !important;
    }
  }
}

.bgColorWhite{
  background-color: #FFFFFF !important;
}

.flex-container{
  display: flex;
  align-items: center;
}
.vertical-alignment-center{
  display: flex !important;
    align-items: center;
    justify-content: center;
}

.material-icons{
  font-size: 16px !important;
}
.header {
  .link {
      padding: 2px;
      .circle {
          position: absolute;
          right: 6px;
          top: 6px;
          background-color: white;
          border-radius: 80%;
          width: 8px;
          height: 8px;
      }
  }
}
.clo-page-header{
  display: flex;
  flex-direction:row;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 6px #00000029;
}
.clo-fs-nav-items{
  display: flex;
  flex-direction:row;
  align-items: center;
}
.clo-fs-user-info{
  display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}
.nav-icons-space{
  padding:12px;
}
.clo-fs-tab{
  background-color: #FFFFFF;
  padding:20px;
}
.clo-fs-logo{
  display:flex;
}
.clo-arrow-image{
  border-radius:20px;
  border:1px solid #E6E6E6;
  padding:6px;
}
.icon-bell{
  border:1px solid #E6E6E6;
}
.clo-fs-tab {
  /* Default styles */
  background-color: white;
  color: #666666;
  padding: 10px;
  cursor: pointer;
  height: 60px;
  display: flex;
  align-items: center;
}

.clo-fs-tab.highlight {
  color:#4061C7;
  border-bottom: 2px solid #4061C7;
}
.admin-select-menu{
  border:none;
}

.arrow-icon{
  padding-left: $space-8;
}



mat-toolbar {
  display: flex;
  justify-content: space-between;
 }
 .spacer {
  flex: 1;
 }
 .nav-links {
  display: flex;
 }
 .nav-links button {

 }
 .master-content{
  overflow:auto;
  margin-top: 48px !important;
  height:calc(100vh - 108px);
 }
 .master-body{
  margin:20px;
 }