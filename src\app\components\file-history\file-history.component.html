<kendo-popup 
  *ngIf="show"
  [anchor]="anchor" 
  [anchorAlign]="{ horizontal: 'right', vertical: 'bottom' }"
  [popupAlign]="{ horizontal: 'right', vertical: 'top' }"
  [animate]="false"
  [popupClass]="'file-history-popup'"
  (closeEvent)="closePopup()"
  (anchorViewportLeave)="closePopup()"
  [preventBodyScroll]="false">
  <div class="file-history-popup-container" (click)="$event.stopPropagation()">
    <div class="file-history-menu">
      <!-- Header -->
      <div class="file-history-header">
        <h2>File History</h2>
        <div class="header-actions">
          <a class="view-all-link" (click)="viewAll(); $event.stopPropagation()">
            View All
          </a>
          <button kendoButton class="close-btn" icon="close" look="flat" (click)="closePopup()"></button>
        </div>
      </div>
      
      <!-- Empty state -->
      <div *ngIf="fileGroups.length === 0" class="no-files">
        <div class="empty-state">
          <kendo-icon name="folder" size="large"></kendo-icon>
          <p>No file history available</p>
        </div>
      </div>
      
      <!-- File Groups -->
      <div class="file-groups-container" *ngIf="fileGroups.length > 0">
        <div *ngFor="let group of fileGroups" class="file-group">
          <!-- Organization Header -->
          <div class="group-header" [ngClass]="group.expanded ? 'custom-border-r' : 'custom-border-r-all'" (click)="toggleGroup(group)">
            <div class="group-title">{{ group.organization }}</div>
            <div class="group-info">
              <span class="file-count">{{ group.files }} files</span>
              <kendo-icon [name]="group.expanded ? 'chevron-up' : 'chevron-down'" size="medium"></kendo-icon>
            </div>
          </div>

          <!-- File Items -->
          <div class="group-content" *ngIf="group.expanded">
            <div class="file-item" *ngFor="let item of group.items">
              <div class="file-row">
                <!-- Status Icon -->
                <div class="file-icon" [ngClass]="item.status">
                  <kendo-icon *ngIf="item.status === 'success'" name="check-circle" size="medium" class="success-icon"></kendo-icon>
                  <img *ngIf="item.status === 'in-progress'" src="assets/dist/images/file-loader.svg" />
                  <!-- <kendo-loader *ngIf="item.status === 'in-progress'" [type]="'pulsing'" [themeColor]="'info'" [size]="'medium'"></kendo-loader> -->
                  <kendo-icon *ngIf="item.status === 'failed'" name="error" size="medium" class="error-icon"></kendo-icon>
                </div>
                
                <!-- File Details -->
                <div class="file-details">
                  <!-- File Name with Icon -->
                  <div class="file-name">
                    <ng-container [ngSwitch]="getFileIcon(item.fileName)">
                        <ng-container *ngSwitchCase="'pdf'">
                            <img src="assets/dist/images/async-upload-pdf.svg" />
                        </ng-container>
                        <ng-container *ngSwitchCase="'xlsx'">
                            <img src="assets/dist/images/async-upload-excel.svg" />
                        </ng-container>
                        <ng-container *ngSwitchCase="'docx'">
                            <img src="assets/dist/images/async-upload-excel.svg" />
                        </ng-container>
                    </ng-container>
                    
                    <span>{{ item.fileName }}</span>
                  </div>
                  
                  <!-- File Size and Date -->
                  <div class="file-meta" *ngIf="item.fileSize">
                    <span>{{ item.fileSize }} KB</span>
                    <span *ngIf="item.uploadDate" class="upload-date">{{ getTimeDifference(item.uploadDate) }}</span>
                  </div>
                  
                  <!-- Progress Bar for in-progress files -->
                  <div class="progress-container" *ngIf="item.status === 'in-progress' && item.progress !== undefined">
                    <kendo-progressbar
                      [value]="item.progress"
                      [max]="100"
                      [animation]="true"
                      class="file-progress">
                    </kendo-progressbar>
                    <span class="progress-text">{{ item.progress }}%</span>
                  </div>
                  
                  <!-- Message -->
                  <div class="file-message">{{ item.message }}</div>
                </div>
                
                <!-- Actions -->
                <div class="file-actions">
                  <button kendoButton *ngIf="item.status === 'failed'" 
                          look="flat" 
                          icon="refresh" 
                          (click)="retryFile(item); $event.stopPropagation()"
                          title="Retry">
                  </button>
                  <button kendoButton *ngIf="item.status === 'success'" 
                          look="flat" 
                          icon="eye" 
                          (click)="viewFile(item); $event.stopPropagation()"
                          title="View">
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</kendo-popup>
