@import "../../../../../variables";

.data-request-section {
    
    display: block;

    .label-text {
        color: #6C6C7A !important;
    }

    .static-card {
        border-radius: 4px 4px 4px 4px !important;
        opacity: 1;
        border: 1px solid #DEDFE0 !important;
        box-shadow: 0px 0px 12px #00000014;
        padding: 20px !important;
    }

    .static-card2 {
        border-radius: 4px 4px 4px 4px !important;
        opacity: 1;
        border: 1px solid #DEDFE0 !important;
        box-shadow: 0px 0px 12px #00000014;
        margin-top: 1.5rem;
        min-height: calc(100vh - 260px);
    }

    .nep-card {
        width: 51.25rem;
        left: 1.75rem;
        top: 1.25rem;
        right: 1.25rem;
        position: relative;
        display: inline-flex;
        border-radius: 8px !important;
        border-top: 1px solid #E6E6E6;
    }
    .nep-card-header {
        border-bottom: transparent !important;
    }

    .custom-close-icon {
        color: #666666;
        vertical-align: inherit !important;
    }

    .custom-info-icon {
        color: #666666;
        vertical-align: bottom !important;
    }

    .dr-search-width {
        width: 40% !important;
    }

    .cursor-filter {
        line-height: 1.5rem;
    }

    .support-body {
        .label-color {
            color: #6C6C7A;
        }

        .support-header {
            border-bottom: 1px solid $nep-dark-b-title;
            background: $nep-light-bg;
            padding: 0.625rem 1rem;
            color: $nep-dark-grey-sub-h;
        }

        .download-link {
            text-decoration: underline;
            color: $nep-dark-blue-link;

            &:hover {
                color: $nep-primary;
            }
        }

        .support-body-content {
            padding: 0.75rem 1rem 0px 0.75rem;
            max-height: calc(100vh - 11.5rem);
            overflow-y: auto;
            min-height: 15rem;
        }
    }
}
$font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif;
    $font-style: normal;
    $font-weight: 400;
.source-doc {
    padding: 0.25rem 0.5rem 0.25rem 0.75rem;
    border-radius: 1.25rem;
    background-color: $nep-light-bg;
    width: 100%;
    color: $nep-dark-doc;
}

.circle-doc {
    max-width: calc(100% - 3.75rem);

    img {
        vertical-align: text-top;
    }
}

.source-link {
    color: $nep-dark-doc;

    img {
        width: 1.063rem;
        height: 1.063rem;
    }
}

.circle-doc-all {
    max-width: 15rem;
    margin-right: 1rem;
    margin-bottom: 1rem;
}

.audit-body {
    padding-bottom: 2.5rem !important;
}

.text-comments {
    color: $nep-dark-black;
    padding: 0.5rem 0.25rem 0px 0.5rem;
}

.empty-comments {
    padding-top: 0.5rem;
}

.text-above-image {
    position: absolute;
    color: $nep-white-secondary;
    text-align: center;
    padding-top: 0.375rem;
    padding-left: 0.25rem;
    width: 1rem;
    height: 1.125rem;
}

.doc-support {
    width: 25rem !important;
}

.doc-time {
    width: 15rem !important;
}

.source-doc-t {
    color: $nep-dark-grey-sub-h;
}

.all-download-icon {
    vertical-align: text-top;
}

.text-align-r {
    text-align: right;
}

.text-align-l {
    text-align: left;
}

.evidence-header {
    vertical-align: sub;
}

.details-search-header {
    height: 54px;
    border-radius: var(--radius-xx-sm, 0rem);
    border-bottom: 1px solid #DEDFE0;
    background: #FAFAFB;
}
.request-details-search {
    height: 35px;
}
.search-box{
    padding: 8px 5px 5px 35px;
}
.add-req-button-box{
    padding: 11px;
    .add-req-button{
        padding-block: 5px;
        background: white;
    }
    .add-req-button:hover{
        color: #4061C7 !important;
        background: white !important;
        box-shadow: none !important;
    }
}
.fixed-details-footer{
    position: fixed;
    bottom: 25px;
    width: calc(100% - 7.68em);
}
.k-expander + .k-expander.k-expanded, .k-expander.k-expanded + .k-expander {
    margin-top: 0px !important;
}
.supporting-documents {
    border: 1px solid #E6E6E6;
    border-radius: 4px !important;
    height: 200px !important;
    text-align: center !important;
    margin: 16px 0 !important;

}

.supporting-documents-body {
    max-height: 160px !important;
    overflow-y: auto !important;
}

.supporting-documents-header {
    background: #F5F5F7;
    border-bottom: 1px solid #E6E6E6;
}

.supporting-documents-title {
    @extend .common-styles;
    padding: 8px 8px 8px 19px !important;
    color: #666666 !important;
    font-size: 12px;
    font-weight: 500;
    line-height: 20px;
    label{
        color: #4061C7 !important;
        text-decoration: underline;
        text-underline-position: under;
    }
}

.supporting-documents-text-area {
    border: 1px solid #DEDFE0 !important;
    border-radius: 4px !important;
}

.supporting-documents-single-header {
    border: 1px solid #DEDFE0 !important;
    border-radius: 4px !important;
    padding: 2px 8px 6px 16px !important
}


.centered {
    @extend .common-styles;
    text-align: center;
}
.multiple-file-drop {
    opacity: 0 !important;
}
.common-styles {
    font-family: $font-family !important;
    font-style: $font-style;
    font-weight: $font-weight;
}
.multiple-file-browse {
    @extend .common-styles;
    padding: 8px 0 16px !important;
    color: #7E7E8C !important;
    line-height: normal;
}
.cursor-pointer {
    cursor: pointer;
}
img {
    width: 17px !important;
    height: 17px !important;
    object-fit: cover;
}
.height-input-drop-file {
    padding-top: 54px !important;
    img {
        width: 55px !important;
        height: 47px !important;
        object-fit: cover;
    }
}

.chips-container {
    display: flex;
    flex-wrap: wrap;
    padding: 10px 10px 8px 10px !important
}

.chip {
    margin: 5px;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f1f1f1;
    border-radius: 25px;
}

.chip i {
    margin-left: 10px;
    cursor: pointer;
}
.multiple-chip-content {
    display: flex !important;
}

.chip-file-name {
    width: 160px !important;
}

.chip-text-align {
    text-align: left !important;
}
.multiple-file-upload {
    .mat-mdc-chip {
        position: relative;
        z-index: 0;
        max-width: 220px;
        padding: 2px 4px 2px 4px;
        margin: 8px !important;
    }
}
.close-icon-chips {
    padding-top: 2px;
}
.extention-div{
    max-height: calc(100vh - 426px);
    overflow: overlay;
}
.no-group-div-body{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
.no-group-div,
.no-group-div-body .justify-content-center{
    height: calc(100vh - 426px);
    max-height: calc(100vh - 426px);
}
.exp-head-op{
    right: 50px;
    position: absolute;
}
.add-reminder {
    color: #0f5ae6;
    text-decoration: underline;
    cursor: pointer;
}
.delete-reminder{
    position: absolute;
    bottom: 0;
    cursor: pointer;
    width: 25px !important;
    height: 25px !important;
}
.user-header{
    height: 35px;
}
.frequency-drpd,
.period-drpd,
.days-drpd{
    height: 35px !important;
}

.days-drpd .k-input-spinner.k-spin-button{
    display: none !important;
}
.is-active, .is-automatic{
    position: absolute;
    top: 5px;
}
.no-group-div .no-group-div-body .justify-content-center .text-center img {
    width: 12% !important;
    height: auto !important;
}

// For tablets
@media (min-width: 768px) and (max-width: 1024px) {
    .no-group-div .no-group-div-body .justify-content-center .text-center img {
        width: 250px !important;
        height: 161px !important;
    }
}

// For desktops
@media (min-width: 1025px) {
    .no-group-div .no-group-div-body .justify-content-center .text-center img {
        width: 250px !important;
        height: 161px !important;
    }
}
.no-group-div .no-group-div-body .justify-content-center .empty-state-text{
    color: #666666 !important;
}
.actions.exp-head-op img{
    width: 25px !important;
    height: 25px !important;
}
.cursor-pointer{
    cursor: pointer;
}