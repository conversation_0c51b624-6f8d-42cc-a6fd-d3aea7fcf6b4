import { Component, Input, OnInit,ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { DecimalDigitEnum, FinancialValueUnitsEnum, MiscellaneousService, OrderTypesEnum, PeriodTypeQuarterEnum } from 'src/app/services/miscellaneous.service';
import { KPIModulesEnum } from 'src/app/services/permission.service';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { CommonPCConstants, KpiInfo } from 'src/app/common/constants';
import { DropDownFilterSettings } from '@progress/kendo-angular-dropdowns';
import { GroupResult, groupBy,filterBy } from "@progress/kendo-data-query";
import { Observable, of } from 'rxjs';
@Component({
  selector: "app-master-kpi-graph-beta",
  templateUrl: "./master-kpi-graph-beta.component.html",
  styleUrls: ["./master-kpi-graph-beta.component.scss"],
})
export class MasterKpiGraphBetaComponent implements OnInit {
  public virtual: any = {
    itemHeight: 30,
    pageSize: 20
  };
  groupedData: GroupResult[] = [];
  id: any;
  width: number = 0;
  modelKpi: any = {};
  @Input() modelList: any;
  kpiItems: [];
  filteredKpiItems: [];
  expandedKpiLineItems: any = [];
  ddlModel: any = {
    KPIList: [],
  };
  @Input() isValueUpdated: boolean = false;
  moduleCurrency: string;
  yBarFields = [];
  yLineFields = [];
  chartData = [];
  xField = null;
  @Input() searchFilter: any;
  @Input() typeField: string;
  masterKpiValueUnit: any;
  searchFilterCopy: any = null;
  isLoaded: boolean = false;
  selectedKPI: any;
  yShades = [];
  isNoData: boolean = false;
  @Input() isYtd: boolean = false;
  @Input() isLtm: boolean = false;
  public filteredData: any[] = [...this.groupedData];
  public showPopup = false;
  public selectedItem: any;
  defaultOrFavKpiExists:boolean = false;
  constructor(
    private miscService: MiscellaneousService,
    private portfolioCompanyService: PortfolioCompanyService,
    private _avRoute: ActivatedRoute,
    private cdr: ChangeDetectorRef
  ) {
    if (this._avRoute.snapshot.params["id"]) {
      this.id = this._avRoute.snapshot.params["id"];
    }
  }

  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: "contains",
  };

  ngOnInit(): void {
    if(this.ddlModel.kpiList?.length > 0){
      this.setKpiGroup(this.ddlModel.KPIList);
    }
    this.modelKpi.periodType = PeriodTypeQuarterEnum.Last1Year;
    this.modelKpi.orderType = { type: OrderTypesEnum.LatestOnRight };
    this.modelKpi.decimalPlaces = {
      type: DecimalDigitEnum.Zero,
      value: "1.0-0",
    };
    this.masterKpiValueUnit = {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    };
  }

  /**
  * Handles the click event on the three dots menu icon
  * @param {MouseEvent} event - The mouse click event
  * @param {any} dataItem - The KPI item data associated with the clicked row
  */
  public handleDotsClick(event: MouseEvent, dataItem: any) {
    event.stopPropagation();
    this.showPopup = this.selectedItem !== dataItem || !this.showPopup;
    this.selectedItem = dataItem;
  }

  /**
 * Sets the selected KPI as default
 * @param {MouseEvent} event - The mouse click event
 * @param {any} item - The KPI item to be set as default
 */
  public onSetDefaultKpi(event: MouseEvent, item: any): void {
    if(this.selectedKPI.isDefault){
      this.selectedKPI.isDefault = false;
    }
    if(this.selectedKPI.kpiid == item.kpiid){
      this.selectedKPI.isDefault = true;
    }
    this.setShowPopUp();
    this.getUpdatedKPIs(item.kpiid,true,item.isFavourite,event);
  }

  /**
 * Removes the default status from selected KPI
 * @param {MouseEvent} event - The mouse click event
 * @param {any} item - The KPI item to remove default status
 */
  public onUnSetDefaultKpi(event: MouseEvent, item: any): void {
    if(item.kpiid == this.selectedKPI.kpiid){
      this.selectedKPI.isDefault = false;
    }
    this.setShowPopUp();
    this.getUpdatedKPIs(item.kpiid,false,item.isFavourite,event);

  }
  /**
 * Updates the popup state and marks KPI preference as updated
 * @private
 */
  private setShowPopUp() {
    this.showPopup = false;
  }

  /**
 * Sets the selected KPI as favorite
 * @param {MouseEvent} event - The mouse click event
 * @param {any} item - The KPI item to be marked as favorite
 */
  public onSetFavKpi(event: MouseEvent, item: any): void {
    if(this.selectedKPI.kpiid == item.kpiid){
      this.selectedKPI.isFavourite = true;
    }
    this.setShowPopUp();
    this.getUpdatedKPIs(item.kpiid,item.isDefault,true,event);
  }
  /**
 * Removes favorite status from selected KPI
 * @param {MouseEvent} event - The mouse click event
 * @param {any} item - The KPI item to remove favorite status
 */
  public onUnSetFavKpi(event: MouseEvent, item: any): void {
    if(item.kpiid == this.selectedKPI.kpiid){
      this.selectedKPI.isFavourite = false;
    }
    this.setShowPopUp();
    this.getUpdatedKPIs(item.kpiid,item.isDefault,false,event);
  }
  setSymbol(kpi: any) {
    let currCode = this.modelList?.reportingCurrencyDetail?.currencyCode;
    if (window.location.host.includes('himera') || window.location.host.includes('local') || window.location.host.includes('test') || window.location.host.includes('uat')) {
      currCode = this.modelList?.fundReportingCurrency?.currencyCode;
    }
    switch (kpi?.kpiInfo) {
      case KpiInfo.Currency:
        if (this.modelList.moduleId == KPIModulesEnum.Investment) {
          this.moduleCurrency =
            this.modelList?.fundReportingCurrency != null
              ? this.modelList?.fundReportingCurrency?.currencyCode
              : "NA";
        } else {
          this.moduleCurrency =
          currCode != null ? currCode : "NA";
        }
        break;
      case KpiInfo.Number:
        this.moduleCurrency = KpiInfo.Number;
        break;
      case KpiInfo.Percentage:
        this.moduleCurrency = KpiInfo.Percentage;
        break;
      case KpiInfo.Multiple:
        this.moduleCurrency = KpiInfo.Multiple;
        break;
      default:
        break;
    }
  }

  getKpiItemList(result: any,isPreferenceUpdated:boolean) {
    const expandedKpiLineItems = [];
    let expandedId = 0;
    this.kpiItems = [];
      let filteredItems = result.filter(item => item?.parentId === null);
      let filteredChieldItems = result.filter(item => item?.parentId !== null);
      filteredItems.forEach((element) => {
        element.items = filteredChieldItems.filter((item) => item.parentId === element.kpiid);
      });
      if (filteredItems.length > 0) {
        expandedKpiLineItems.push(expandedId++);
        this.kpiItems = filteredItems;
        this.filteredKpiItems = [...this.kpiItems];
        if(!isPreferenceUpdated && !this.selectedKPI){
          let defaultLineItem = result.find(x => x.isDefault);
          if(!defaultLineItem){
            defaultLineItem = result.find(x => x.isFavourite);
          }
          let defaultKpi = defaultLineItem ? defaultLineItem : filteredItems[0];
          this.selectedKPI = defaultKpi;
        }
      };
    this.expandedKpiLineItems = expandedKpiLineItems.join(",");
  }

  public fetchChildren(node: any): Observable<any[]> {
    // returns the items collection of the parent node as children
    return of(node.items);
  }

  public hasChildren(node: any): boolean {
    // checks if the parent node has children
    return node.items && node.items.length > 0;
  }

  onFilterChange(filterString: string) {
    if (filterString?.length === 0) {
        this.filteredKpiItems = [...this.kpiItems];
        this.cdr.detectChanges(); // Manually trigger change detection
        return this.filteredKpiItems;
    }

    const searchTerm = filterString?.toLowerCase();
    const filteredRes = (this.kpiItems as any[]).map(group => ({
        ...group,
        items: group?.items?.filter((item) =>
            item?.itemName?.toLowerCase().includes(searchTerm)
        )
    })).filter(group =>
        group?.items?.length > 0 ||
        group?.itemName?.toLowerCase().includes(searchTerm)
    );

    this.filteredKpiItems = filteredRes as [];
    this.cdr.detectChanges(); // Manually trigger change detection
    return this.filteredKpiItems;
}

  getKPIs() {
    let KPIQueryModel = {
      portfolioCompanyIds: this.modelList.portfolioCompanyID.toString(),
      moduleId: this.modelList.moduleId,
      kpiType: this.getKpiTypes(),
    };
    this.miscService.getKPIListByPCIdsKPIType(KPIQueryModel).subscribe({
      next: (result) => {
        let resp = result["body"];
        if (resp != null && result.code == "OK") {
          this.processKpisAndLoadGraph(resp,false);
        }
      },
      error: (_error) => {},
    });
  }

  truncateTabName(name: string, maxLength: number): any {
    name = name?.trim();
    if (name?.length <= maxLength) {
      return name;
    } else {
      const shortFilename = name.slice(0, maxLength); // Leave space for "...."
      return `${shortFilename}...`;
    }
  }

  /**
 * Handles the close event of the dropdown tree
 * @param {MouseEvent} event - The mouse event that triggered the dropdown close
 */
  onDropDownClose(event: MouseEvent) {
    this.showPopup = false;
  }

  OnKPIChange(event:any) {
    if(event?.kpiInfo != KpiInfo.Text && !event?.isHeader){
      this.selectedKPI = event;
      this.updateWidth(false);
      this.setSymbol(this.selectedKPI);
      this.loadGraph();
    }else{
      this.showNoDataGraph();
    }
  }

  private showNoDataGraph() {
    this.isNoData = true;
    this.chartData = [];
  }

  onResized(event: any) {
    this.width = event?.newRect?.width;
  }
  ngOnChanges(changes: any): void {
    if (
      this.searchFilter != this.searchFilterCopy ||
      this.typeField ||
      this.isValueUpdated
    ) {
      if (this.modelList.moduleId == KPIModulesEnum.Investment) {
        this.moduleCurrency =
          this.modelList?.fundReportingCurrency != null
            ? this.modelList?.fundReportingCurrency?.currencyCode
            : "NA";
      } else {
        this.moduleCurrency =
          this.modelList?.reportingCurrencyDetail != null
            ? this.modelList?.reportingCurrencyDetail?.currencyCode
            : "NA";
      }
      let loadGraphData = false;
      if (this.searchFilterCopy !== null) {
        loadGraphData = true;
      }
      this.searchFilterCopy = this.searchFilter;
      this.modelKpi.periodType = this.searchFilter?.periodType;
      this.masterKpiValueUnit = this.searchFilter?.UnitType;
      if (loadGraphData) this.getKPIs();
    }
  }
  createFilter() {
    const defaultFilter = {
      periodType: PeriodTypeQuarterEnum.Last1Year,
    };
    return {
      companyId: this.modelList.portfolioCompanyID.toString(),
      portfolioCompanyID: this.modelList.portfolioCompanyID.toString(),
      searchFilter: this.searchFilter || defaultFilter,
      moduleId: this.modelList.moduleId,
      unit:
        this.masterKpiValueUnit == undefined
          ? FinancialValueUnitsEnum.Millions
          : this.masterKpiValueUnit.typeId,
      chartType: this.typeField,
      kpiId: this.selectedKPI?.kpiid,
      isYtd: this.isYtd,
      isLtm: this.isLtm,
    };
  }

  /**
 * Updates KPI preferences and refreshes KPI list
 * @param {number} kpiId - The ID of the KPI to update
 * @param {boolean} isDefault - Flag indicating if KPI should be set as default
 * @param {boolean} isFavourite - Flag indicating if KPI should be set as favourite
 * @param {MouseEvent} event - Mouse event that triggered the update
 * @description
 * Sends update request to server with new KPI preferences.
 * On successful response, processes updated KPI list and reloads graph.
 */
  getUpdatedKPIs(kpiId: number, isDefault: boolean, isFavourite: boolean,event: MouseEvent) {
    let KPIQueryModel = {
      portfolioCompanyIds: this.modelList.portfolioCompanyID.toString(),
      moduleId: this.modelList.moduleId,
      kpiType: this.getKpiTypes(),
      CompanyId : this.modelList.portfolioCompanyID,
      KpiId:kpiId,
      IsDefault:isDefault,
      IsFavourite:isFavourite
    };
    this.miscService.UpdateKpiPreferenceAndGetKPIList(KPIQueryModel).subscribe({
      next: (result) => {
        let resp = result;
        if (resp != null) {
          this.processKpisAndLoadGraph(resp,true);
        }
      },
      error: (_error) => {},
    });
  }

  /**
 * Processes KPI response data and initializes graph
 * @param {any} resp - Response containing KPI list data
 * @private
 * @description
 * - Updates KPI list in dropdown model
 * - Generates KPI item list and groups
 * - Sets default KPI selection
 * - Initializes graph with selected KPI
 */
  private processKpisAndLoadGraph(resp: any, isPreferenceUpdated:boolean) {
    this.defaultOrFavKpiExists = resp.find(x => x.isDefault || x.isFavourite) != undefined;
    this.ddlModel.KPIList = resp;
    this.getKpiItemList(this.ddlModel.KPIList,isPreferenceUpdated);
    this.setKpiGroup(this.ddlModel.KPIList);
    if(!isPreferenceUpdated){
      if (!this.selectedKPI) {
        this.selectedKPI = resp[0];
      } else {
        this.selectedKPI = resp.find(x => x["kpiid"] == this.selectedKPI?.kpiid);
      }
    }
    if(this.selectedKPI?.kpiInfo != KpiInfo.Text && !this.selectedKPI?.isHeader){ 
      this.setSymbol(this.selectedKPI);
      this.loadGraph();
    } else{
      this.showNoDataGraph();
    }

  }

  loadGraph() {
    this.isNoData = false;
    this.clearAll();
    this.isLoaded = true;
    let filter = this.createFilter();
    this.portfolioCompanyService.getChartsKpiData(filter).subscribe({
      next: (result) => {
        if (result != undefined) {
          this.chartData = result?.data || [];
          this.yLineFields = result?.yLineFields || [];
          this.yBarFields = result?.yBarFields || [];
          this.xField = result?.xField;
          this.yShades = result?.yShades || [];
          if (this.yBarFields.length == 0) {
            this.chartData = [];
          }
          this.isNoData = this.chartData.length > 0 ? false : true;
        } else {
          this.isNoData = true;
          this.clearAll();
        }
        this.isLoaded = false;
      },
      error: (error) => {
        this.clearAll();
        this.isLoaded = false;
      },
    });
  }
  clearAll(): void {
    this.chartData = [];
    this.yLineFields = [];
    this.yBarFields = [];
    this.yShades = [];
  }
  getKpiTypes(): string {
    const kpiTypes = new Map([
      [KPIModulesEnum.TradingRecords, "MasterKpis"],
      [KPIModulesEnum.Operational, "Operational"],
      [KPIModulesEnum.Investment, "Investment"],
      [KPIModulesEnum.CreditKPI, "MasterKpis"],
      [KPIModulesEnum.Company, "Company"],
      [KPIModulesEnum.Impact, "Impact"],
    ]);
    return kpiTypes.get(Number(this.modelList.moduleId));
  }
  dynamicWidth: string = "240px";
  onFocus(): void {
    this.updateWidth(false);
  }
  onBlur(): void {
    this.updateWidth(true);
  }
  open(): void {
    this.updateWidth(false);
  }
  close(): void {
    this.updateWidth(true);
  }
  opened(): void {
    this.updateWidth(false);
  }
  closed(): void {
    this.updateWidth(true);
  }
  updateWidth(defaultWidth:boolean): void {
    if (defaultWidth) {
      this.dynamicWidth = "240px";
    } else {
      this.dynamicWidth = Math.round(this.width - 10) + "px";
    }
    this.cdr.detectChanges();
  }
  onDoubleClick(){
    this.updateWidth(false);
  }
  /**
   * Filters the KPI list based on the provided value and updates the KPI group.
   *
   * @param value - The string value to filter the KPI list. If the value is empty, the original KPI list is set.
   *
   * The filter configuration checks if the `parentkpi` or `displayName` fields contain the provided value, ignoring case.
   * The filtered data is then used to update the KPI group.
   */
  public customFilter(value: string): void {
    if (!value) {
      this.setKpiGroup(this.ddlModel.KPIList);
      return;
    }
    const filterConfig:any = {
      logic: 'or',
      filters: [
        { field: "parentkpi", operator: "contains", value: value, ignoreCase: true },
        { field: "displayName", operator: "contains", value: value, ignoreCase: true },
      ]
    };
    const filteredData = filterBy(this.ddlModel.KPIList, filterConfig);
    this.setKpiGroup(filteredData);
  }
  /**
   * Groups the provided KPI data by the "parentkpi" field and assigns the result to the `filteredData` property.
   *
   * @param filteredData - The data to be grouped, expected to be an array of objects.
   */
  setKpiGroup(filteredData:any){
    this.filteredData = groupBy(filteredData, [
      { field: "parentkpi" },
    ]) as GroupResult[];
  }
}