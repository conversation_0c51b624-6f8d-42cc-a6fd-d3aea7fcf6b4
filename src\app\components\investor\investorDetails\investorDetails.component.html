<div class="row tab-shadow">
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 col-xs-12 pr-0 pl-0">
        <nep-tab id="neptab" class="custom-pipeline-tab" [tabList]=tabList (OnSelectTab)="onTabClick($event)">
        </nep-tab>
    </div>
</div>
<div *ngIf="tabName=='Details'" class="row mr-0 ml-0 fund-detail-section" [ngClass]="loading?'d-none':'d-block'"
    (resized)="onResized($event)">
    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
        <div class="row mr-0 ml-0 header-section pb-2">
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                <div class="float-right">
                    <div class="fund-splitButton">
                        <a class="loading-input-controls2" *ngIf="exportLoading">
                            <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                        </a>
                        <div class="download-investor-excel"><img alt="" src="assets/dist/images/FileDownloadWhite.svg"
                                class="showHandIcon pr-1 mt-0 TextTruncate" title="Performance Report"> Performance Report </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mr-0 ml-0 card card-main static-card pb-4" *ngIf="canViewGeoLoc || canViewInvInfo || canEditInvInfo">
            <div class="col-12 col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                <div class="static-pc-section fund-detail section1-height chart-area row mr-0 ml-0 mb-0">
                    <div class="col-12  pr-0 pl-0">
                        <div class="row mr-0 ml-0 pb-1 static-bg">
                            <div class="col-12 pr-0 pl-0 chart-title pc-section-header">
                                <div class="float-right pr-2 fund-static-header">
                                    <a title="Edit" id="editredirect" (click)="editRedirect()" ><img
                                            alt="" src="assets/dist/images/EditIcon.svg"></a>
                                </div>
                            </div>
                        </div>
                        <div class="row mr-0 ml-0" *ngIf="canViewInvInfo">
                            <div class="col-sm-12 pr-3 pl-3 pt-3 pb-2">
                                <div class="line-wrapper">
                                    <span class="mr-2 TextTruncate" title="{{staticDetailsTitle.displayName}}"> {{staticDetailsTitle.displayName}}</span>
                                    <div class="line"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row mr-0 ml-0"*ngIf="canViewInvInfo">
                            <div class="pl-3 pr-3 Fund-section col-12">
                                <div class="row mr-0 ml-0">
                                    <ng-container *ngFor="let item of staticDataConfiguration">
                                        <div class="col-4 pr-0 pl-0 pt-2 mb-1 custom-investor-padding" *ngIf="item.name=='Website'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-4">
                                                    <div  class="TextTruncate"><span
                                                            class="hColor static-label TextTruncate" title="{{item.displayName}}">
                                                            {{item.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 pl-0 col-8 pl-3 custom-investor-value-padding">
                                                    <div class="TextTruncate">
                                                        <a title="{{item.value}}" class="linkStyle" class="click-view" id="clickitem"
                                                            href="{{item.value}}">{{item.value}}</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 pl-0 custom-investor-padding"
                                            *ngIf="item.name!='Website' && item.name!='BusinessDescription' && item.name != 'TotalCommitment'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-4">
                                                    <div title="{{item.displayName}}" class="TextTruncate"><span class="hColor TextTruncate static-label">
                                                            {{item.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 pl-0 col-8 pl-3 custom-investor-value-padding">
                                                    <div class="TextTruncate">
                                                        <span title="{{item.value}}"
                                                            class="vColor static-field-value TextTruncate">{{item.value}}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 pl-3 custom-investor-padding" *ngIf="item.name == 'TotalCommitment'">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-4">
                                                    <div title="{{item.displayName}}" class="TextTruncate"><span
                                                            class="hColor TextTruncate static-label">
                                                            {{item.displayName}}</span></div>
                                                </div>
                                                <div class="pr-0 pl-0 col-8 custom-investor-value-padding" *ngIf="item.name == 'TotalCommitment'">
                                                    <div title="{{(item.value |number : NumberDecimalConst.currencyDecimal)|| 'NA'}}"
                                                        class="TextTruncate">
                                                        <span title="{{item.value}}" class="vColor static-field-value TextTruncate">{{(item.value=='NA'?'NA':item.value |number :
                                                            NumberDecimalConst.currencyDecimal)|| "NA"}}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </ng-container>
                                </div>
                            </div>
                        </div>
                        <div class="row mr-0 ml-0" *ngIf="businessDesc != undefined && canViewInvInfo">
                            <div class="col-sm-12 pr-3 pl-3 pt-3 pb-2">
                                <div class="line-wrapper">
                                    <span class="mr-2 TextTruncate" title="{{businessDesc.displayName}}">{{businessDesc.displayName}}</span>
                                    <div class="line"></div>
                                </div>
                                <div class="chart-content ">
                                    <div class=" statit-desc pr-0 pl-0 pt-3 TextTruncate" title="{{businessDesc.value}}">{{businessDesc.value}}</div>
                                </div>
                            </div>
                        </div>

                        <div class="row mr-0 ml-0" *ngIf="canViewGeoLoc && investorGeographicalLocationsTitle">
                            <div class="col-sm-12 pr-3 pl-3 pt-3 pb-2">
                                <div class="line-wrapper">
                                    <span class="mr-2 TextTruncate" title="{{investorGeographicalLocationsTitle.displayName}}">{{investorGeographicalLocationsTitle.displayName}}</span>
                                    <div class="line"></div>
                                </div>
                            </div>
                            <div class="col-12 pr-0 pl-0 pt-2" *ngIf="geographicalLocationsDataConfiguration.length > 0">
                                <div class="card">
                                    <div class="card-body mb-0">
                                        <div class="table-responsive card-border">
                                            <table class='table mb-0 static-info-table custom-investor-table'>
                                                <thead>
                                                    <tr>
                                                        <ng-container
                                                            *ngFor="let geo of geographicalLocationsDataConfiguration">
                                                            <th scope="col" class="text-align-left" title="{{geo.displayName}}">{{geo.displayName}}
                                                            </th>
                                                        </ng-container>
                                                    </tr>
                                                </thead>
                                                <tbody *ngIf="investorDetails.investorLocationData.length > 0">
                                                    <tr *ngFor="let loc of investorDetails.investorLocationData">
                                                        <td class="text-align-left" *ngIf="hasRegion">
                                                            <span title="{{loc?.regionName || 'NA'}}">{{loc?.regionName || "NA"}}</span>
                                                        </td>
                                                        <td class="text-align-left" *ngIf="hasCountry">
                                                            <span title="{{loc?.countryName || 'NA'}}">{{loc?.countryName || "NA"}}</span>
                                                        </td>
                                                        <td class="text-align-left" *ngIf="hasState">
                                                            <span title="{{loc?.stateName || 'NA'}}">{{loc?.stateName || "NA"}}</span>
                                                        </td>
                                                        <td class="text-align-left" *ngIf="hasCity">
                                                            <span title="{{loc?.cityName || 'NA'}}"> {{loc?.cityName || "NA"}} </span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <app-empty-state [isGraphImage]="false"
                                                *ngIf="investorDetails?.investorLocationData?.length == 0">
                                            </app-empty-state>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mr-0 ml-0 investor-fund-table mt-3" *ngIf="canViewInvFunds">
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                <div class="float-left fund-header pb-2 TextTruncate" title="{{investorFundHeader}}">
                    {{investorFundHeader}} 
                </div>
            </div>
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 trackrecord-section mb-3">
                <div class="financial-page">
                    <div class="filter-bg">
                        <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                            <div class="align-items-start">
                                <div class="ui-widget-header ui-wid-bb border-bottom">
                                    <div class="row mr-0 ml-0">
                                        <div class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                                            <div class="float-left">
                                                <div class="allvalues-kpis TextTruncate" title="All Values in: Millions">All Values in: Millions</div>
                                            </div>
                                            <div class="float-right">
                                                <div class="d-inline-block search">
                                                    <span class="fa fa-search fasearchicon p-1"></span>
                                                    <input 
                                                        (input)="searchInvestorFundGrid($event.target.value)"
                                                        [(ngModel)]="globalFilter" type="text" pInputText
                                                        class="search-text-company companyListSearchHeight TextTruncate"
                                                        placeholder="Search">
                                                </div>
                                                <div class="d-inline-block">
                                                    <img class="p-action-padding download-excel" alt=""
                                                        title="Export  (Excel file)"
                                                        src="assets/dist/images/Cloud-download.svg" (click)="investorFundExport()" id="investorfundexport" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="datatable-container">
                                    <kendo-grid [kendoGridBinding]="investorFundData" scrollable="virtual" [rowHeight]="44" [resizable]="true"
                                        class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none  custom-kendo-cab-table-grid">
                                        <ng-container *ngFor="let columns of frozenInvestorTableColumns;">
                                            <kendo-grid-column *ngIf="columns.header=='FundId' || columns?.header=='FirmName'" [sticky]="true" [width]="300"
                                                title="{{columns.displayName}}" id="{{columns.subPageID}}">
                                                <ng-template kendoGridHeaderTemplate>
                                                    <span class="TextTruncate S-M">{{columns.field}}</span>
                                                </ng-template>
                                                <ng-template kendoGridCellTemplate let-investor>
                                                    <div [ngClass]="(columns?.header=='FirmName')?'frozen-headerWidth':''"
                                                        title="{{investor?.FirmName?.firmName}}" *ngIf="columns.header=='FirmName'">
                                                        <a *ngIf="investor?.FirmName?.firmName!=null && investor?.FirmName?.firmName!=''"
                                                            class="click-view TextTruncate" id="firmname" href="javascript:void(0);"
                                                            [routerLink]="['/firm-details',investor?.FirmName.encryptedFirmId]">{{investor?.FirmName?.firmName}}</a>
                                                        <span *ngIf="investor?.FirmName?.firmName==null || investor?.FirmName?.firmName==''">
                                                            NA
                                                        </span>
                                                    </div>
                                                    <div [ngClass]="(columns.header=='FundId')?'frozen-headerWidth':''"
                                                        title=" {{investor?.FundId?.fundName}}" *ngIf="columns.header=='FundId'">
                                                        <a *ngIf="investor?.FundId?.fundName!=null && investor?.FundId?.fundName!=''"
                                                            class="click-view TextTruncate" href="javascript:void(0);"
                                                            [routerLink]="['/fund-details',investor?.FundId.encryptedFundId]">{{investor?.FundId?.fundName}}</a>
                                                        <span *ngIf="investor?.FundId?.fundName==null || investor?.FundId?.fundName==''">
                                                            NA
                                                        </span>
                                                    </div>
                                                </ng-template>
                                            </kendo-grid-column>
                                        </ng-container>
                                        <ng-container *ngFor="let col of investorTableColumnsList;">
                                            <kendo-grid-column id="{{col.subPageID}}" *ngIf="col.header!='FundId' && col?.header!='FirmName'" [width]="200"
                                                title="{{col.name =='InvestorStake' ? investorStakeTitle: col.displayName}}">
                                                <ng-template kendoGridHeaderTemplate>
                                                    <span class="TextTruncate S-M">{{col.displayName}}</span>
                                                </ng-template>
                                                <ng-template kendoGridCellTemplate let-investor>
                                                    <div class="TextTruncate"
                                                        [ngClass]="(col.header!='FirmName' && col.header!='FundId' && col.name!=FundInvestorConstants.CommitmentDate) ? 'table-data-right' : 'table-data-left'"
                                                        [ngClass]="(col.header!='FirmName' && col.header!='FundId' && col.name!=FundInvestorConstants.CommitmentDate) ? 'table-data-right' : 'table-data-left'"
                                                        *ngIf="col.header!='FirmName' && col.header!='FundId'">
                                                        <container-element>
                                                            <container-element [ngSwitch]="col.name">
                                                                <container *ngSwitchCase="'Ownership'">
                                                                    <span [title]="investor[col.name]"
                                                                        *ngIf="investor[col.name] != undefined && [col.name] != '' && investor[col.name]!=null;else empty_Value"
                                                                        [innerHtml]="isNumberCheck(investor[col.name]) ? (investor[col.name] | number: NumberDecimalConst.percentDecimal | minusToBracketsWithPercentage: '%'): investor[col.name]">
                                                                    </span>
                                                                </container>
                                                                <container *ngSwitchCase="'InvestorStake'">
                                                                    <span [title]="investor[col.name]"
                                                                        *ngIf="investor[col.name] != undefined && [col.name] != '' && investor[col.name]!=null;else empty_Value"
                                                                        [innerHtml]="isNumberCheck(investor[col.name]) ? (investor[col.name] | number: NumberDecimalConst.percentDecimal | minusToBracketsWithPercentage: '%'): investor[col.name]">
                                                                    </span>
                                                                </container>
                                                                <container *ngSwitchCase="'CommitmentDate'">
                                                                    <span [title]="investor[col.name]"
                                                                        *ngIf="investor[col.name] != undefined && [col.name] != '' && investor[col.name]!=null;else empty_Value"
                                                                        [innerHtml]="investor[col.name]| date:'MM/dd/yyyy'"></span>
                                                                </container>
                                                                <container *ngSwitchCase="'Commitment'">
                                                                    <span [title]="investor[col.name]"
                                                                        *ngIf="investor[col.name] != undefined && [col.name] != '' && investor[col.name]!=null;else empty_Value"
                                                                        [innerHtml]="isNumberCheck(investor[col.name]) ? (investor[col.name] | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : investor[col.name]">
                                                                    </span>
                                                                </container>
                                                                <container *ngSwitchCase="'NetDrawn'">
                                                                    <span [title]="investor[col.name]"
                                                                        *ngIf="investor[col.name] != undefined && [col.name] != '' && investor[col.name]!=null;else empty_Value"
                                                                        [innerHtml]="isNumberCheck(investor[col.name]) ? (investor[col.name] | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : investor[col.name]">
                                                                    </span>
                                                                </container>
                                                                <container *ngSwitchCase="'Recallable'">
                                                                    <span [title]="investor[col.name]"
                                                                        *ngIf="investor[col.name] != undefined && [col.name] != '' && investor[col.name]!=null;else empty_Value"
                                                                        [innerHtml]="isNumberCheck(investor[col.name]) ? (investor[col.name] | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : investor[col.name]">
                                                                    </span>
                                                                </container>
                                                                <container *ngSwitchCase="'UndrawnCommitment'">
                                                                    <span [title]="investor[col.name]"
                                                                        *ngIf="investor[col.name] != undefined && [col.name] != '' && investor[col.name]!=null;else empty_Value"
                                                                        [innerHtml]="isNumberCheck(investor[col.name]) ? (investor[col.name] | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : investor[col.name]">
                                                                    </span>
                                                                </container>
                                                                <container *ngSwitchCase="'AstreaTransfer'">
                                                                    <span [title]="investor[col.name]"
                                                                        *ngIf="investor[col.name] != undefined && [col.name] != '' && investor[col.name]!=null;else empty_Value"
                                                                        [innerHtml]="isNumberCheck(investor[col.name]) ? (investor[col.name] | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : investor[col.name]">
                                                                    </span>
                                                                </container>
                                                                <container *ngSwitchCase="'CommitmentAfterAstreaTransfer'">
                                                                    <span [title]="investor[col.name]"
                                                                        *ngIf="investor[col.name] != undefined && [col.name] != '' && investor[col.name]!=null;else empty_Value"
                                                                        [innerHtml]="isNumberCheck(investor[col.name]) ? (investor[col.name] | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : investor[col.name]">
                                                                    </span>
                                                                </container>
                                                                <container *ngSwitchDefault></container>
                                                            </container-element>
                                                        </container-element>
                                                        <container-element>
                                                            <container-element [ngSwitch]="col.dataType">
                                                                <container *ngSwitchCase="mDataTypes.Percentage">
                                                                    <span [title]="investor[col.displayName]"
                                                                        *ngIf="investor[col.displayName] != undefined && [col.displayName] != '' && investor[col.displayName]!=null;else empty_Value"
                                                                        [innerHtml]="isNumberCheck(investor[col.displayName]) ? (investor[col.displayName] | number: NumberDecimalConst.percentDecimal | minusToBracketsWithPercentage: '%'): investor[col.displayName]"></span>
                                                                </container>
                                                                <container *ngSwitchCase="mDataTypes.CurrencyValue">
                                                                    <span [title]="investor[col.displayName]"
                                                                        *ngIf="investor[col.displayName] != undefined && [col.displayName] != '' && investor[col.displayName]!=null;else empty_Value"
                                                                        [innerHtml]="isNumberCheck(investor[col.displayName]) ? (investor[col.displayName] | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: ''): investor[col.displayName]"></span>
                                                                </container>
                                                                <container *ngSwitchCase="mDataTypes.Number">
                                                                    <span [title]="investor[col.displayName]"
                                                                        *ngIf="investor[col.displayName] != undefined && [col.displayName] != '' && investor[col.displayName]!=null;else empty_Value"
                                                                        [innerHtml]="isNumberCheck(investor[col.displayName]) ? (investor[col.displayName] | number:'1.0-0' | minusSignToBrackets) : investor[col.displayName]"></span>
                                                                </container>
                                                                <container *ngSwitchCase="mDataTypes.Multiple">
                                                                    <span [title]="investor[col.displayName]"
                                                                        *ngIf="investor[col.displayName] != undefined && [col.displayName] != '' && investor[col.displayName]!=null;else empty_Value"
                                                                        [innerHtml]="isNumberCheck(investor[col.displayName]) ? (investor[col.displayName] | number: NumberDecimalConst.multipleDecimal)+'x': investor[col.displayName]"></span>
                                                                </container>
                                                                <container *ngSwitchCase="mDataTypes.Date">
                                                                    <span [title]="investor[col.displayName]"
                                                                        *ngIf="investor[col.displayName] != undefined && [col.displayName] != '' && investor[col.displayName]!=null;else empty_Value"
                                                                        [innerHtml]="investor[col.displayName]| date:'MM/dd/yyyy'"></span>
                                                                </container>
                                                                <container *ngSwitchCase="mDataTypes.FreeText">
                                                                    <span [title]="investor[col.displayName]"
                                                                        *ngIf="investor[col.displayName] != undefined && [col.displayName] != '' && investor[col.displayName]!=null;else empty_Value"
                                                                        [innerHtml]="investor[col.displayName]"></span>
                                                                </container>
                                                                <container *ngSwitchDefault></container>
                                                            </container-element>
                                                        </container-element>
                                                        <ng-template #empty_Value class="detail-sec deal-link">
                                                            NA
                                                        </ng-template>
                                                    </div>
                                                </ng-template>
                                            </kendo-grid-column>
                                        </ng-container>
                                        <ng-template kendoGridNoRecordsTemplate>
                                            <app-empty-state class="finacials-beta-empty-state" [isGraphImage]="false"></app-empty-state>
                                        </ng-template>
                                    </kendo-grid>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mr-0 ml-0 investor-fund-table  mb-4" *ngIf="canViewInvTrackRec">
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0">
                <div class="float-left fund-header pb-2 TextTruncate" title="Investor Track Record">
                    Investor Track Record
                </div>
            </div>
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0 tc-box">
                <div class="row mr-0 ml-0">
                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 p-3">
                        <div class="sub-feature-section">
                            <div class="pb-2 TextTruncate" title="Fund Name">
                                Fund Name
                            </div>
                            <div>
                                <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="selectedFund" #fund="ngModel"
                                    [fillMode]="'solid'" [filterable]="true" name="fund" [virtual]="virtual"
                                    class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="fundList"
                                    [filterable]="true" [value]="selectedFund" [valuePrimitive]="false" textField="fundName" placeholder="Select Module"
                                    (valueChange)="getTRByFund()" id="getTRByFund" valueField="fundID">
                                    <ng-template kendoComboBoxItemTemplate let-fund>
                                        <span title="{{fund.fundName}}" class="TextTruncate" title="{{fund.fundName}}">
                                            {{fund.fundName}}
                                        </span>
                                    </ng-template>
                                </kendo-combobox>
                            </div>
                        </div>
                        <div class="sub-feature-section">
                            <div class="pb-2 TextTruncate" title="Quarter">
                                Quarter
                            </div>
                            <div>
                                <kendo-combobox [disabled]="selectedFund==null" [clearButton]="false"  [(ngModel)]="selectedQuarter" #quarter="ngModel"
                                [fillMode]="'solid'"  name="quarter" [virtual]="virtual"
                                class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="quarterOptions"
                                [filterable]="true" [value]="selectedQuarter" [valuePrimitive]="false" textField="text" placeholder="Select Quarter"
                                (valueChange)="getTRByQuarterAndYear()" id="getTRByQuarterAndYear" valueField="text">
                            </kendo-combobox>
                            </div>
                        </div>
                        <div class="sub-feature-section">
                            <div class="pb-2 TextTruncate" title="Year">
                                Year
                            </div>
                            <div>
                                    <kendo-combobox [disabled]="selectedQuarter==null" [filterable]="true" [kendoDropDownFilter]="filterSettings" [disabled]="selectedFund==null" [clearButton]="false"  [(ngModel)]="selectedYear" #year="ngModel"
                                    [fillMode]="'solid'"  name="year" [virtual]="virtual"
                                    class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="yearOptions"
                                    [filterable]="true" [value]="selectedYear" [valuePrimitive]="false" textField="text" placeholder="Select Year"
                                    (valueChange)="getTRByQuarterAndYear()" id="getTRByQuarterAndYear" valueField="value">
                                    </kendo-combobox>
                            </div>
                        </div>
                        <div class="sub-feature-section">
                            <div class="pb-2 TextTruncate" title="Values in">
                                Values in:
                            </div>
                            <div>
                                <kendo-dropdownlist  (valueChange)="valueChange($event)" id="valueChange"
                                class="access-subfeature k-outline-none dropdown-custom-width" textField="unitType" [disabled]="selectedYear==null"
                                valueField="typeId" (selectionChange)="convertTrackRecordValueUnits();convertToMillions();" [(ngModel)]="selectedValueUnitType" [data]="valueOptions"></kendo-dropdownlist>

                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0 pr-3 pl-3 pt-1 border-bottom">
                        <div class="float-left fund-header pb-2 TextTruncate" title="Fund Details">
                            Fund Details
                        </div>
                    </div>
                    <div [ngClass]="fundTrackRecordData?.length >0 ?'border-tr':''"
                        class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0">
                        <div class="datatable-container">
                            <kendo-grid  [kendoGridBinding]="fundTrackRecordData" scrollable="virtual" [rowHeight]="44" [resizable]="true"
                                class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none  custom-kendo-cab-table-grid">
                                <ng-container *ngFor="let columns of frozenInvestorTrackRecordTableColumns;">
                                    <kendo-grid-column title="{{columns.displayName}}" [sticky]="true" [width]="300">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="TextTruncate S-M">{{columns.field}}</span>
                                        </ng-template>
                                        <ng-template kendoGridCellTemplate let-fundTrackRecord>
                                            <div class="TextTruncate" title=" {{fundTrackRecord?.FundName!=null?fundTrackRecord?.FundName:'NA'}}">
                                                <span>
                                                    {{fundTrackRecord?.FundName!=null?fundTrackRecord?.FundName:'NA'}}
                                                </span>
                                            </div>
                                        </ng-template>
                                    </kendo-grid-column>
                                </ng-container>
                                <ng-container *ngFor="let col of fundTrackRecordColumns;">
                                    <kendo-grid-column *ngIf="col.name!='FundName'" title="{{col.displayName}}" [width]="200"
                                        id="{{col.subPageID}}">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span title="{{col.displayName}}" class="TextTruncate S-M">{{col.displayName}}</span>
                                        </ng-template>
                                        <ng-template kendoGridCellTemplate let-fundTrackRecord>
                                            <div class="TextTruncate" [ngClass]="(col.name!='FundName') ? 'table-data-right' : 'table-data-left'"
                                                *ngIf="col.name!='FundName'">
                                                {{
                                                col.name==FundTrackRecordStatic.TotalNumberOfInvestments?(fundTrackRecord[col.name]!=null?(fundTrackRecord[col.name]|number
                                                : NumberDecimalConst.noDecimal):'NA'):
                                                col.name==FundTrackRecordStatic.RealizedInvestments?(fundTrackRecord[col.name]
                                                !=null?(fundTrackRecord[col.name]|number :
                                                NumberDecimalConst.noDecimal):'NA'):
                                                col.name==FundTrackRecordStatic.UnRealizedInvestments?(fundTrackRecord[col.name]
                                                !=null?(fundTrackRecord[col.name]|number :
                                                NumberDecimalConst.noDecimal):'NA'):
                                                col.name==FundTrackRecordStatic.TotalInvestedCost?(fundTrackRecord[col.name]
                                                !=null?(fundTrackRecord[col.name]|number :
                                                NumberDecimalConst.currencyDecimal):'NA'):
                                                col.name==FundTrackRecordStatic.TotalRealizedValue?(fundTrackRecord[col.name]
                                                !=null?(fundTrackRecord[col.name]|number :
                                                NumberDecimalConst.currencyDecimal):'NA'):
                                                col.name==FundTrackRecordStatic.TotalUnRealizedValue?(fundTrackRecord[col.name]
                                                !=null?(fundTrackRecord[col.name]|number :
                                                NumberDecimalConst.currencyDecimal ):'NA'):
                                                col.name==FundTrackRecordStatic.TotalValue?(fundTrackRecord[col.name]
                                                !=null?(fundTrackRecord[col.name]|number :
                                                NumberDecimalConst.currencyDecimal):'NA'):
                                                col.name==FundTrackRecordStatic.Dpi?(fundTrackRecord[col.name]
                                                !=null?(fundTrackRecord[col.name]|number :
                                                NumberDecimalConst.multipleDecimal )+"x":'NA'):
                                                col.name==FundTrackRecordStatic.Rvpi?(fundTrackRecord[col.name]
                                                !=null?(fundTrackRecord[col.name]|number :
                                                NumberDecimalConst.multipleDecimal )+"x":'NA'):
                                                col.name==FundTrackRecordStatic.GrossMultiple?(fundTrackRecord[col.name]
                                                !=null?(fundTrackRecord[col.name]|number :
                                                NumberDecimalConst.multipleDecimal)+"x":'NA'):
                                                col.name==FundTrackRecordStatic.NetMultiple?(fundTrackRecord[col.name]
                                                !=null?(fundTrackRecord[col.name]|number :
                                                NumberDecimalConst.multipleDecimal)+"x":'NA'):
                                                col.name==FundTrackRecordStatic.GrossIRR?(fundTrackRecord[col.name]
                                                !=null?(fundTrackRecord[col.name]|number :
                                                NumberDecimalConst.percentDecimal)+"%":'NA'):
                                                col.name==FundTrackRecordStatic.NetIRR?(fundTrackRecord[col.name]
                                                !=null?(fundTrackRecord[col.name]|number :
                                                NumberDecimalConst.percentDecimal)+"%":'NA'):
                                                col.dataType != mDataTypes.Date?(
                                                col.dataType==mDataTypes.Multiple?(fundTrackRecord[col.displayName]!=null?(
                                                fundTrackRecord[col.displayName] | number :
                                                NumberDecimalConst.multipleDecimal)+"x":'NA'):
                                                col.dataType==mDataTypes.Percentage?(fundTrackRecord[col.displayName]!=null?(
                                                fundTrackRecord[col.displayName] | number :
                                                NumberDecimalConst.percentDecimal)+"%":'NA'):
                                                col.dataType==mDataTypes.CurrencyValue?(fundTrackRecord[col.displayName]!=null?(
                                                fundTrackRecord[col.displayName] | number :
                                                NumberDecimalConst.currencyDecimal):'NA'):
                                                col.dataType==mDataTypes.Number?(fundTrackRecord[col.displayName]!=null?(
                                                fundTrackRecord[col.displayName] | number :
                                                NumberDecimalConst.noDecimal):'NA'):fundTrackRecord[col.displayName] == null
                                                ?'NA':fundTrackRecord[col.displayName]
                                                ):
                                                fundTrackRecord[col.displayName]!=null?
                                                (fundTrackRecord[col.displayName]| date:'MM/dd/yyyy' ):
                                                'NA'
                                                }}
                                            </div>
                                        </ng-template>
                                    </kendo-grid-column>
                                </ng-container>
                                <ng-template kendoGridNoRecordsTemplate>
                                    <app-empty-state class="finacials-beta-empty-state"
                                        [isGraphImage]="false"></app-empty-state>
                                </ng-template>
                            </kendo-grid>
                        </div>
                    </div>
                </div>
                <div class="row mr-0 ml-0">
                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0">
                        <div class="pb-1 pl-3 pr-3 pb-2 company-details-padding fund-header TextTruncate" title="Company's Details">
                            Company's Details
                        </div>
                    </div>
                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 trackrecord-section">
                        <div class="company-investor-border">
                            <div class="panel panel-default border-0 pt-2 tab-bg">
                                <div class="panel-heading">
                                    <div class="panel-title custom-tabs">
                                        <ul class="nav nav-tabs ">
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link nav-custom active TextTruncate" id="home-tab"
                                                    data-bs-toggle="tab" type="button" role="tab" aria-controls="home"
                                                    aria-selected="true" title="Fund currency">
                                                    Fund currency
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="filter-bg">
                                <div class="align-items-start">
                                    <div class="ui-widget-header ui-wid-bb border-bottom">
                                        <div class="float-right plus-btn">
                                            <a title="Add">
                                                <img class="tc-add" title="Add" alt=""
                                                    src="assets/dist/images/plus.svg" /></a>
                                        </div>
                                        <div class="row mr-0 ml-0">
                                            <div
                                                class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                                                <div class="float-left">
                                                    <div class="allvalues-kpis TextTruncate" title="All Values in: {{company_table_fundCurrency}} ({{selectedValueUnitType?.unitType}})">All Values in: {{company_table_fundCurrency}} ({{selectedValueUnitType?.unitType}})</div>
                                                </div>
                                                <div class="float-right">
                                                    <div class="d-inline-block search">
                                                        <span class="fa fa-search fasearchicon p-1"></span>
                                                        <input  [(ngModel)]="CompanyGlobalFilter" (keyup)="searchCompanyDetailsGrid($event.target.value)" type="text" pInputText
                                                        class="search-text-company companyListSearchHeight TextTruncate"
                                                        placeholder="Search">
                                                    </div>
                                                    <div class="d-inline-block">
                                                        <img class="p-action-padding download-excel"
                                                            title="Export  (Excel file)"
                                                            src="assets/dist/images/Cloud-download.svg" alt="" id="companydetails" (click)="investorCompanyDetails()"  />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="datatable-container">
                                        <kendo-grid [kendoGridBinding]="company_results" scrollable="virtual" [rowHeight]="44" [resizable]="true"
                                        class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none  custom-kendo-cab-table-grid">
                                        <ng-container *ngFor="let col of frozenInvestorCompanyTableColumns">
                                            <kendo-grid-column *ngIf="col.name=='PortfolioCompanyName'" [sticky]="true" [width]="300"
                                            title="{{col.displayName}}" id="{{col.subPageID}}">
                                            <ng-template kendoGridHeaderTemplate>
                                                <span  title="{{col.displayName}}" class="TextTruncate S-M">{{col.displayName}}</span>
                                            </ng-template>
                                            <ng-template kendoGridCellTemplate let-portfolioCompanyFundHolding>
                                                <div class="TexTruncate" pFrozenColumn [ngClass]="(col.name=='PortfolioCompanyName')?'frozen-header-tr-Width':''"
                                                title=" {{portfolioCompanyFundHolding?.PortfolioCompanyName!=null?portfolioCompanyFundHolding?.PortfolioCompanyName:'NA'}}"
                                                *ngIf="col.name=='PortfolioCompanyName'">
                                                <span>
                                                    {{portfolioCompanyFundHolding?.PortfolioCompanyName!=null?portfolioCompanyFundHolding?.PortfolioCompanyName:'NA'}}
                                                </span>
                                            </div>
                                            </ng-template>
                                            </kendo-grid-column>
                                        </ng-container>
                                        <ng-container *ngFor="let col of company_cols">
                                            <kendo-grid-column *ngIf="col.name!='PortfolioCompanyName'"  [width]="200"
                                            title="{{col.displayName}}" id="{{col.subPageID}}">
                                            <ng-template kendoGridHeaderTemplate>
                                                <span  title="{{col.displayName}}" class="TextTruncate S-M">{{col.displayName}}</span>
                                            </ng-template>
                                            <ng-template kendoGridCellTemplate let-portfolioCompanyFundHolding>
                                                <div class="TextTruncate" *ngIf="col.name!='PortfolioCompanyName'"
                                                            [ngClass]="(col.name!='PortfolioCompanyName') ?  'table-data-right':'table-data-left'"
                                                            [ngClass]="(col.name=='Quarter') ?  'table-data-left':'table-data-right'">
                                                            {{
                                                            (col.name==DealTrackRecordInfo.Quarter)?(portfolioCompanyFundHolding[col.name]==null?"NA":portfolioCompanyFundHolding[col.name]+' '+portfolioCompanyFundHolding['Year']):
                                                            (col.name==DealTrackRecordInfo.Status)?(portfolioCompanyFundHolding[col.name]==null?"NA":portfolioCompanyFundHolding[col.name]):
                                                            (col.name==DealTrackRecordInfo.ValuationDate)?(portfolioCompanyFundHolding[col.name]==null?"NA":portfolioCompanyFundHolding[col.name]):
                                                            (col.name==DealTrackRecordInfo.InvestmentDate)?(portfolioCompanyFundHolding[col.name]==null?"NA":portfolioCompanyFundHolding[col.name]):
                                                            (col.name==DealTrackRecordInfo.InvestmentCost)?(portfolioCompanyFundHolding[col.name]==null?"NA":portfolioCompanyFundHolding[col.name]|
                                                            number : NumberDecimalConst.currencyDecimal):
                                                            (col.name==DealTrackRecordInfo.RealizedValue)?(portfolioCompanyFundHolding[col.name]==null?"NA":portfolioCompanyFundHolding[col.name]|
                                                            number : NumberDecimalConst.currencyDecimal):
                                                            (col.name==DealTrackRecordInfo.UnrealizedValue)?(portfolioCompanyFundHolding[col.name]==null?"NA":portfolioCompanyFundHolding[col.name]|
                                                            number : NumberDecimalConst.currencyDecimal):
                                                            (col.name==DealTrackRecordInfo.TotalValue)?(portfolioCompanyFundHolding[col.name]==null?"NA":portfolioCompanyFundHolding[col.name]|
                                                            number : NumberDecimalConst.currencyDecimal):
                                                            (col.name==DealTrackRecordInfo.Dpi)?(portfolioCompanyFundHolding[col.name]==null?"NA":(portfolioCompanyFundHolding[col.name]|
                                                            number : NumberDecimalConst.multipleDecimal)+"x"):
                                                            (col.name==DealTrackRecordInfo.Rvpi)?(portfolioCompanyFundHolding[col.name]==null?"NA":(portfolioCompanyFundHolding[col.name]|
                                                            number : NumberDecimalConst.multipleDecimal)+"x"):
                                                            (col.name==DealTrackRecordInfo.GrossMultiple)?((portfolioCompanyFundHolding[col.name]==null)?"NA":(portfolioCompanyFundHolding[col.name]|
                                                            number:NumberDecimalConst.multipleDecimal)+"x"):
                                                            (col.name==DealTrackRecordInfo.GrossIRR)?(portfolioCompanyFundHolding[col.name]==null?"NA":(portfolioCompanyFundHolding[col.name]|
                                                            number : NumberDecimalConst.percentDecimal)+"%"):
                                                            col.dataType != mDataTypes.Date?(
                                                                col.dataType==mDataTypes.Multiple?((portfolioCompanyFundHolding[col.displayName]!=null&&portfolioCompanyFundHolding[col.displayName]!='')?( portfolioCompanyFundHolding[col.displayName] | number:NumberDecimalConst.multipleDecimal)+"x":"NA"):
                                                                col.dataType==mDataTypes.Percentage?((portfolioCompanyFundHolding[col.displayName]!=null&&portfolioCompanyFundHolding[col.displayName]!='')?( portfolioCompanyFundHolding[col.displayName] | number:NumberDecimalConst.percentDecimal)+"%":"NA"):
                                                                col.dataType==mDataTypes.CurrencyValue?((portfolioCompanyFundHolding[col.displayName]!=null&&portfolioCompanyFundHolding[col.displayName]!='')?( portfolioCompanyFundHolding[col.displayName] | number : NumberDecimalConst.currencyDecimal):"NA"):
                                                                col.dataType==mDataTypes.Number?((portfolioCompanyFundHolding[col.displayName]!=null&&portfolioCompanyFundHolding[col.displayName]!='')?( portfolioCompanyFundHolding[col.displayName] | number : NumberDecimalConst.noDecimal):"NA"):
                                                                col.dataType==mDataTypes.FreeText?(portfolioCompanyFundHolding[col.displayName]!=null?( portfolioCompanyFundHolding[col.displayName]):"NA"):
                                                                portfolioCompanyFundHolding[col.displayName]
                                                            ):
                                                            portfolioCompanyFundHolding[col.displayName]!=null?
                                                            (portfolioCompanyFundHolding[col.displayName]| date:'MM/dd/yyyy' ):
                                                            "NA"
                                                            }}
                                                        </div>
                                            </ng-template>
                                            </kendo-grid-column>
                                        </ng-container>
                                        <ng-template kendoGridNoRecordsTemplate>
                                            <app-empty-state class="finacials-beta-empty-state"
                                                [isGraphImage]="false"></app-empty-state>
                                        </ng-template>
                                        </kendo-grid>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- start here -->
        <div class="row mr-0 ml-0 investor-fund-table  mb-4" *ngIf="canViewCompPerf">
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0">
                <div class="float-left fund-header pb-2 TextTruncate" title="{{companyPerformanceHeaderText}}">
                    {{companyPerformanceHeaderText}}
                </div>
            </div>
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0 tc-box">
                <div class="row mr-0 ml-0 ">
                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 p-3">
                        <div class="sub-feature-section">
                            <div class="pb-2 TextTruncate" title="Company Name">
                                Company Name
                            </div>
                            <div>
                            <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="selectedMasterCompany"
                                        #masterCompanyName="ngModel" [fillMode]="'solid'" [filterable]="true" name="masterCompanyName" [virtual]="virtual"
                                        class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                                        [data]="masterCompanyList" [filterable]="true" [value]="selectedMasterCompany" [valuePrimitive]="false"
                                        textField="masterCompanyName" id="companychange" placeholder="Select Company" (valueChange)="getCompanyPerformance()"
                                        valueField="masterCompanyName">
                                        <ng-template kendoComboBoxItemTemplate let-company>
                                            <span class="TextTruncate" title="{{company.masterCompanyName}}">
                                                {{company.masterCompanyName}}
                                            </span>
                                        </ng-template>
                                    </kendo-combobox>
                            </div>
                        </div>
                        <div class="sub-feature-section ">
                            <div class="pb-2 TextTruncate" title="Quarter & Year">
                                Quarter & Year
                            </div>
                            <div>
                                <quarter-year-control class="investor-quarter" [ControlName]="'pcYear'" [QuarterYear]="companyPerformanceYearQuarter"  (onCalendarYearPicked)="performanceFromQuarterYear($event)"></quarter-year-control>  
                            </div>
                        </div>
                    </div>                   
                    <div [ngClass]="fundTrackRecordData?.length >0 ?'border-tr':''" 
                        class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0">
                        <div class="ui-widget-header border-bottom-company">
                            <div class="row mr-0 ml-0">
                                <div
                                    class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 allvalues-kpis-company-performance border-bottom">
                                    <div class="float-left">
                                        <div class="allvalues-kpis TextTruncate" title="All Values in: {{dealCurrencyCode}} ({{performanceTableUnit?.unitType}})">All Values in: {{dealCurrencyCode}} ({{performanceTableUnit?.unitType}})</div>
                                    </div>
                                    <div class="float-right">
                                        <div class="d-inline-block search">
                                            <span class="fa fa-search fasearchicon p-1"></span>
                                            <input  [(ngModel)]="companyPerformanceSearchKeyWord" (keyup)="searchPerformanceGrid($event.target.value)" type="text" pInputText
                                            class="search-text-company companyListSearchHeight TextTruncate"
                                            placeholder="Search">
                                        </div>
                                        <div class="d-inline-block">
                                            <img class="p-action-padding download-excel"
                                                title="Export  (Excel file)"
                                                src="assets/dist/images/Cloud-download.svg" alt="" id="performanceexport" (click)="companyPerformanceExport()" />
                                        </div>
                                        <div class="d-inline">
                                            <span class="col-divider">
                                            </span>
                                        </div>
                                        <div class="d-inline-block pl-2 pref-icon"><img id="dropdownMenuButton" [matMenuTriggerFor]="menu"  src="assets/dist/images/ConfigurationWhite.svg" class="cursor-filter" alt="" #tRecordTrigger="matMenuTrigger" id="commonunit" (click)="isCommonUnitClick='Performance'"  /> </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="datatable-container">
                            <kendo-grid [kendoGridBinding]="compPerformanceData" scrollable="virtual" [rowHeight]="44" [resizable]="true"
                                class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none  custom-kendo-cab-table-grid fixed-calculation-table">
                                <ng-container *ngFor="let columns of frozenInvestorTrackRecordTableColumns;">
                                    <kendo-grid-column *ngIf="columns.header=='FundName'" [sticky]="true" [width]="300"
                                        title="{{columns.displayName}}" id="{{columns.subPageID}}">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span title="{{columns.field}}" class="TextTruncate S-M">{{columns.field}}</span>
                                        </ng-template>
                                        <ng-template kendoGridCellTemplate let-fundTrackRecord>
                                            <div class="TextTruncate"  
                                            title=" {{fundTrackRecord?.FundName!=null?fundTrackRecord?.FundName:'NA'}}"
                                            *ngIf="columns.header=='FundName'"
                                            [class.font-weight-bold]="fundTrackRecord['FundName']=='Total'"
                                            >
                                            <span title="{{fundTrackRecord?.FundName!=null?fundTrackRecord?.FundName:'NA'}}">
                                                {{fundTrackRecord?.FundName!=null?fundTrackRecord?.FundName:'NA'}}
                                            </span>
                                        </div>
                                        </ng-template>
                                    </kendo-grid-column>
                                </ng-container>
                                <ng-container *ngFor="let col of compPerformanceColumns;">
                                    <kendo-grid-column *ngIf="col.name!='FundName'"  [width]="200"
                                    title="{{col.displayName}}" id="{{col.subPageID}}">
                                    <ng-template kendoGridHeaderTemplate>
                                        <span title="{{col.displayName}}" class="TextTruncate S-M">{{col.displayName}}</span>
                                    </ng-template>
                                    <ng-template kendoGridCellTemplate let-fundTrackRecord>
                                        <div class="TextTruncate"  [class.font-weight-bold]="fundTrackRecord['FundName']=='Total'" [ngClass]="(col.header!='FundName') ? 'table-data-right' : 'table-data-left'"
                                                *ngIf="col.header!='FundName'">
                                                {{ 
                                                    (col.name==investorCompaniesPerformance.InvestorStake)?(fundTrackRecord['FundName']=='Total'?'-':(fundTrackRecord[col.name]==null?"NA":(fundTrackRecord[col.name]| number : NumberDecimalConst.currencyDecimal)+'%')):
                                                    (col.name==investorCompaniesPerformance.CurrentExitOwnershipPercent)?(fundTrackRecord['FundName']=='Total'?'-':(fundTrackRecord[col.name]==null?"NA":(fundTrackRecord[col.name]| number : NumberDecimalConst.currencyDecimal)+'%')):
                                                    (col.name==investorCompaniesPerformance.CommitmentAfterAstreaTransfer)?(fundTrackRecord[col.name]==null?"NA":fundTrackRecord[col.name] | number : NumberDecimalConst.currencyDecimal):
                                                    (col.name==investorCompaniesPerformance.InvestorStake)?(fundTrackRecord[col.name]==null?"NA":(fundTrackRecord[col.name]| number : NumberDecimalConst.currencyDecimal)+'%'):
                                                    (col.name==investorCompaniesPerformance.InvestmentDate)?(fundTrackRecord['FundName']=='Total'?'-':(fundTrackRecord[col.name]==null?"NA":fundTrackRecord[col.name])):
                                                    (col.name==investorCompaniesPerformance.Status)?(fundTrackRecord['FundName']=='Total'?'-':(fundTrackRecord[col.name]==null?"NA":fundTrackRecord[col.name])):
                                                    (col.name==investorCompaniesPerformance.Dpi||col.name==investorCompaniesPerformance.Rvpi||col.name==investorCompaniesPerformance.GrossMultiple)?(fundTrackRecord[col.name]==null?"NA":(fundTrackRecord[col.name] | number : NumberDecimalConst.currencyDecimal)+'x'):
                                                    (col.name==investorCompaniesPerformance.GrossIRR)?(fundTrackRecord['FundName']=='Total'?'-':(fundTrackRecord[col.name]==null?"NA":(fundTrackRecord[col.name] | number : NumberDecimalConst.currencyDecimal)+'%')):
                                                    (fundTrackRecord[col.name]==null?"NA":fundTrackRecord[col.name]| number : NumberDecimalConst.currencyDecimal)
                                                }}
                                            </div>
                                    </ng-template>
                                    </kendo-grid-column>
                                </ng-container>
                                <ng-template kendoGridNoRecordsTemplate>
                                    <app-empty-state class="finacials-beta-empty-state"
                                        [isGraphImage]="false"></app-empty-state>
                                </ng-template>
                            </kendo-grid>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- end here -->
        <div class="row mr-0 ml-0 investor-fund-table  mb-4" *ngIf="!isTaabo && canViewValData">
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0">
                <div class="float-left fund-header pb-2 TextTruncate" title="Valuation Data">
                    Valuation Data
                </div>
            </div>
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0 tc-box">
                <div class="row mr-0 ml-0 ">
                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 p-3">
                        <div class="sub-feature-section">
                            <div class="pb-2 TextTruncate" title="Fund Name">
                                Fund Name
                            </div>
                            <div>
                                <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="valuationFundId"
                                        #module="ngModel" [fillMode]="'solid'" [filterable]="true" name="module" [virtual]="virtual"
                                        class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                                        [data]="valuationSelection" [filterable]="true" [value]="valuationFundId" [valuePrimitive]="true"
                                        textField="fundName" placeholder="Select Fund" id="selectfund" (valueChange)="getValuationSelectionChange()"
                                        valueField="fundId">
                                        <ng-template kendoComboBoxItemTemplate let-fund>
                                            <span title="{{fund.fundName}}">
                                                {{fund.fundName}}
                                            </span>
                                        </ng-template>
                                    </kendo-combobox>
                            </div>
                        </div>
                        <div class="sub-feature-section">
                            <div class="pb-2 TextTruncate" title="Quarter & Year">
                                Quarter & Year
                            </div>
                            <div>
                                <quarter-year-control class="investor-quarter" [ControlName]="'valuationYear'" [QuarterYear]="valuationQuarter"  (onCalendarYearPicked)="fromQuarterYear($event)"></quarter-year-control>                              
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 trackrecord-section">
                        <div class="company-investor-border">
                            <div class="filter-bg">
                                <div class="align-items-start">
                                    <div class="ui-widget-header border-bottom-0" >
                                        <div class="row mr-0 ml-0">
                                            <div class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0 border-bottom">        
                                                <div class="float-left">
                                                    <div class="allvalues-kpis TextTruncate" title="All Values in: {{valuation_fundCurrency}} ({{valueUnit?.unitType}})">All Values in: {{valuation_fundCurrency}} ({{valueUnit?.unitType}})</div>
                                                </div>
                                                <div class="float-right">
                                                    <div class="d-inline-block search">
                                                        <span class="fa fa-search fasearchicon p-1"></span>
                                                        <input  [(ngModel)]="valuationGlobalFilter" (keyup)="searchValuationGrid($event.target.value)" type="text" pInputText
                                                        class="search-text-company companyListSearchHeight TextTruncate"
                                                        placeholder="Search">
                                                    </div>
                                                    <div class="d-inline-block">
                                                        <img class="p-action-padding download-excel"
                                                            title="Export  (Excel file)"
                                                            src="assets/dist/images/Cloud-download.svg" alt="" id="valuationexport" (click)="valuationExport()" />
                                                    </div>
                                                    <div class="d-inline">
                                                        <span class="col-divider">
                                                        </span>
                                                    </div>
                                                    <div class="d-inline-block pl-2 pref-icon"><img id="dropdownMenuButton" [matMenuTriggerFor]="menu"  src="assets/dist/images/ConfigurationWhite.svg" class="cursor-filter" alt="" #tRecordTrigger="matMenuTrigger" id="clickunit" (click)="isCommonUnitClick='Valuation'"  /> </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="datatable-container">
                                        <kendo-grid [kendoGridBinding]="valuationTable" scrollable="virtual" [rowHeight]="44" [resizable]="true"
                                        class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none  custom-kendo-cab-table-grid">
                                        <ng-container *ngFor="let col of frozenInvestorValuationTableColumns">
                                            <kendo-grid-column  *ngIf="col.name=='Quarter/Month'" [sticky]="true" [width]="200"
                                            title="{{col.displayName}}">
                                                <ng-template kendoGridHeaderTemplate>
                                                    <span title="{{col.displayName}}" class="TextTruncate S-M">{{col.displayName}}</span>
                                                </ng-template>
                                                <ng-template kendoGridCellTemplate let-valuation>
                                                    <div class="TextTruncate"   title="{{ valuation[col.name]==null?'NA':valuation[col.name]}}"
                                                    [ngClass]="(col.name=='Quarter/Month')?'frozen-header-tr-Width':''" *ngIf="col.name=='Quarter/Month'">
                                                        <span>
                                                        {{ valuation[col.name]==null?"NA":valuation[col.name]}}
                                                        </span>
                                                    </div> 
                                                </ng-template>
                                            </kendo-grid-column>
                                        </ng-container>
                                        <ng-container *ngFor="let col of valuationColumns">
                                            <kendo-grid-column  *ngIf="col.name!='Quarter/Month'"  [width]="200"
                                            title="{{col.displayName}}">
                                                <ng-template kendoGridHeaderTemplate>
                                                    <span title="{{col.displayName}}" class="TextTruncate S-M">{{col.displayName}}</span>
                                                </ng-template>
                                                <ng-template kendoGridCellTemplate let-valuation>
                                                    <div class="TextTruncate" [ngClass]="(col.name=='Quarter/Month')?'':'table-data-right'" *ngIf="col.name!='Quarter/Month'">
                                                        <span  [innerHtml]='(col.name==valuationDefaultValues.CapitalCall)?(valuation[col.name]==null?"NA":valuation[col.name]| number : NumberDecimalConst.currencyDecimal | minusSignToBrackets):
                                                        (col.name==valuationDefaultValues.CapitalDistribution)?(valuation[col.name]==null?"NA":valuation[col.name]| number : NumberDecimalConst.currencyDecimal | minusSignToBrackets):
                                                        (col.name==valuationDefaultValues.FeesAndExpenses)?(valuation[col.name]==null?"NA":valuation[col.name]| number : NumberDecimalConst.currencyDecimal | minusSignToBrackets):
                                                        (col.name==valuationDefaultValues.UnrealizedGainOrLoss)?(valuation[col.name]==null?"NA":valuation[col.name]| number : NumberDecimalConst.currencyDecimal | minusSignToBrackets):
                                                        (col.name==valuationDefaultValues.ClosingNAV)?(valuation[col.name]==null?"NA":valuation[col.name]| number : NumberDecimalConst.currencyDecimal | minusSignToBrackets):
                                                        (valuation[col.displayName]==null?"NA":valuation[col.displayName]| number : NumberDecimalConst.currencyDecimal | minusSignToBrackets)'></span> 
                                                    </div>
                                                </ng-template>
                                            </kendo-grid-column>
                                        </ng-container>
                                        <ng-template kendoGridNoRecordsTemplate>
                                            <app-empty-state class="finacials-beta-empty-state"
                                                [isGraphImage]="false"></app-empty-state>
                                        </ng-template>
                                        </kendo-grid>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<div *ngIf="tabName=='Dashboard'">
    <investor-dashboard></investor-dashboard>
</div>
<app-loader-component *ngIf="loading"></app-loader-component>
<mat-menu #menu="matMenu" [hasBackdrop]="true" class="fixed-menu">
    <div class="filter-first"  (click)="$event.stopPropagation()" id="stoppropagation" (keydown)="$event.stopPropagation()">
        <div class="row m-0 ">
            <div class="col-12 pb-1 pt-3 label-align TextTruncate" title="Values in" >
                Values in:
            </div>
            <div class="col-12 pl-3 pr-3" *ngIf="isCommonUnitClick=='Valuation'">
                <kendo-combobox [clearButton]="false" [(ngModel)]="valueUnit" #unit="ngModel" [fillMode]="'solid'" [filterable]="true"
                    name="Unit" class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                    [data]="unitTypeList" [filterable]="true" [valuePrimitive]="false" textField="unitType" placeholder="Select Unit"
                    (valueChange)="valuationTableConvertToMillions()" id="valuationTableConvertToMillions" valueField="typeId">
                </kendo-combobox>
            </div>
            <div class="col-12 pl-3 pr-3" *ngIf="isCommonUnitClick=='Performance'">
                <kendo-combobox [clearButton]="false" [(ngModel)]="performanceTableUnit" #unit="ngModel" [fillMode]="'solid'" [filterable]="true"
                name="Unit" class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                [data]="unitTypeList" [filterable]="true" [valuePrimitive]="false" id="convertCompPerfToMillions" textField="unitType" placeholder="Select Unit"
                (valueChange)="convertCompPerfToMillions()" valueField="typeId">
            </kendo-combobox>
            </div>
        </div>      
    </div>
</mat-menu>
