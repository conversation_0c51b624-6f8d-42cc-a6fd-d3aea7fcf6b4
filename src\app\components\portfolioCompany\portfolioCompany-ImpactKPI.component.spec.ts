import { ComponentFixture, TestBed } from "@angular/core/testing";
import { of } from "rxjs";
import { PortfolioCompanyImpactKPIComponent } from "./portfolioCompany-ImpactKPI.component";
import { PortfolioCompanyService } from "../../services/portfolioCompany.service";
import { CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
import { MatMenuModule } from "@angular/material/menu";
import { FormsModule } from "@angular/forms";
import {
  FinancialValueUnitsEnum,
  MiscellaneousService,
} from "src/app/services/miscellaneous.service";
import { ToastrService } from "ngx-toastr";
import { AuditService } from "src/app/services/audit.service";
import { InputSwitchModule } from "primeng/inputswitch";
import { OidcAuthService } from "src/app/services/oidc-auth.service";
import {
  MappedDocuments,
  Audit,
} from "../file-uploads/kpi-cell-edit/kpiValueModel";
import { PermissionService } from "src/app/services/permission.service";
import { AccountService } from "src/app/services/account.service";
import { KendoFilterDirective } from "src/app/directives/kendo-filter.directive";
import { SwitchModule } from "@progress/kendo-angular-inputs";
import { DatePipe } from "@angular/common";

describe("PortfolioCompanyImpactKPIComponent", () => {
  let component: PortfolioCompanyImpactKPIComponent;
  let fixture: ComponentFixture<PortfolioCompanyImpactKPIComponent>;
  let service: PortfolioCompanyService;
  let identityService: OidcAuthService;
  beforeEach(async () => {
    const toastrServiceStub = () => ({
      overlayContainer: {},
      success: (toasterMessage, string, object) => ({}),
      error: (toasterMessage, string, object) => ({}),
    });
    const auditServiceStub = () => ({
      auditLog: (auditLog) => ({}),
      getAuditLog: (auditLog) => ({}),
    });
    const miscServiceStub = () => ({
      createDateFromPeriod: (periodType, startPeriod, endPeriod) => ({}),
    });
    const oidcAuthServiceStub = () => ({ signinSilentCallback: () => ({}) });
    const accountServiceStub = () => ({
      logout: () => ({}),
      login: (model) => ({ subscribe: (f) => f({}) }),
    });
    const permissionServiceStub = () => ({ isCheckTaabo: () => ({}) });
    const portfolioCompanyServiceStub = () => ({
      getImpactKPIValues: (object) => ({
        subscribe: (f) => f({ data: "test" }),
      }),
      getfinancialsvalueTypes: (object) => ({ subscribe: (f) => f({}) }),
    });
    await TestBed.configureTestingModule({
      declarations: [PortfolioCompanyImpactKPIComponent, KendoFilterDirective],
      imports: [MatMenuModule, FormsModule, InputSwitchModule, SwitchModule],
      providers: [
        DatePipe,
        {
          provide: PortfolioCompanyService,
          useFactory: portfolioCompanyServiceStub,
        },
        { provide: ToastrService, useFactory: toastrServiceStub },
        { provide: AuditService, useFactory: auditServiceStub },
        { provide: MiscellaneousService, useFactory: miscServiceStub },
        { provide: PermissionService, useFactory: permissionServiceStub },
        { provide: AccountService, useFactory: accountServiceStub },
        { provide: OidcAuthService, useFactory: oidcAuthServiceStub },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();
  });
  beforeEach(() => {
    fixture = TestBed.createComponent(PortfolioCompanyImpactKPIComponent);
    identityService = TestBed.inject(OidcAuthService);
    identityService.getEnvironmentConfig = jasmine
      .createSpy()
      .and.callThrough();
    component = fixture.componentInstance;
    component.pageConfigData = [
      {
        kpiConfigurationData: [
          {
            sectionID: 13,
            fieldID: 15,
            aliasName: "Actual",
            subPageID: 2,
            options: null,
            chartValue: ["Monthly", "Quarterly", "Annual"],
          },
          {
            sectionID: 14,
            fieldID: 15,
            aliasName: "Budget",
            subPageID: 2,
            options: null,
            chartValue: ["Monthly", "Quarterly", "Annual"],
          },
          {
            sectionID: 15,
            fieldID: 15,
            aliasName: "Forecast",
            subPageID: 2,
            options: null,
            chartValue: ["Monthly", "Quarterly", "Annual"],
          },
          {
            sectionID: 16,
            fieldID: 15,
            aliasName: "IC",
            subPageID: 2,
            options: null,
            chartValue: ["Annual"],
          },
        ],
        hasChart: true,
        kpiType: "ImpactKPIs",
      },
    ];
    component.modelList = {
      portfolioCompanyID: 7361,
      moduleId: 6,
    };
    fixture.detectChanges();
  });

  it("should create", () => {
    expect(component).toBeTruthy();
  });
  it("should return true when input is a number", () => {
    const result = component.isNumberCheck("123");
    expect(result).toBe(true);
  });

  it("should return false when input is not a number", () => {
    const result = component.isNumberCheck("abc");
    expect(result).toBe(false);
  });
  it('should set correct flags and call methods when input field is "Monthly"', () => {
    spyOn(component, "setDefaultTypeTab");
    spyOn(component, "getPortfolioCompanyMasterKPIValues");

    component.onChangePeriodOption({ field: "Monthly" });

    expect(component.isMonthly).toBe(true);
    expect(component.isQuarterly).toBe(false);
    expect(component.isAnnually).toBe(false);
    expect(component.setDefaultTypeTab).toHaveBeenCalled();
    expect(component.getPortfolioCompanyMasterKPIValues).toHaveBeenCalledWith(
      null
    );
  });

  it('should set correct flags and call methods when input field is "Quarterly"', () => {
    spyOn(component, "setDefaultTypeTab");
    spyOn(component, "getPortfolioCompanyMasterKPIValues");

    component.onChangePeriodOption({ field: "Quarterly" });

    expect(component.isMonthly).toBe(false);
    expect(component.isQuarterly).toBe(true);
    expect(component.isAnnually).toBe(false);
    expect(component.setDefaultTypeTab).toHaveBeenCalled();
    expect(component.getPortfolioCompanyMasterKPIValues).toHaveBeenCalledWith(
      null
    );
  });

  it('should set correct flags and call methods when input field is not "Monthly" or "Quarterly"', () => {
    spyOn(component, "setDefaultTypeTab");
    spyOn(component, "getPortfolioCompanyMasterKPIValues");

    component.onChangePeriodOption({ field: "Annually" });

    expect(component.isMonthly).toBe(false);
    expect(component.isQuarterly).toBe(false);
    expect(component.isAnnually).toBe(true);
  });
  it("should set correct flags, set correct tab name, and call methods", () => {
    spyOn(component, "setPeriodsOptions");
    spyOn(component, "getPortfolioCompanyMasterKPIValues");

    const tab = { name: "Tab 1", active: false };
    component.tabValueTypeList = [
      { name: "Tab 1", active: false },
      { name: "Tab 2", active: true },
    ];

    component.selectValueTab(tab);

    expect(component.tabValueTypeList[0].active).toBe(false);
    expect(component.tabValueTypeList[1].active).toBe(false);
    expect(component.tabName).toBe("Tab 1");
    expect(component.setPeriodsOptions).toHaveBeenCalledWith(
      component.subSectionFields
    );
  });

  it("should reset all variables to their initial state", () => {
    component.loading = true;
    component.isLoader = true;
    component.tableColumns = ["column1", "column2"];
    component.tableResult = ["result1", "result2"];
    component.tableResultClone = ["clone1", "clone2"];
    component.isPageLoad = true;

    component.clearData();

    expect(component.loading).toBe(false);
    expect(component.isLoader).toBe(false);
    expect(component.tableColumns).toEqual([]);
    expect(component.tableResult).toEqual([]);
    expect(component.tableResultClone).toEqual([]);
    expect(component.isPageLoad).toBe(false);
  });
  it("should set masterKpiValueUnit and searchFilter correctly, and call methods when event.UnitType is undefined", () => {
    spyOn(component, "getPortfolioCompanyMasterKPIValues");
    const event = { UnitType: undefined };
    const expectedMasterKpiValueUnit = {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    };
    component.kpiTable_GlobalFilter(event);

    expect(component.masterKpiValueUnit).toEqual(expectedMasterKpiValueUnit);
    expect(component.searchFilter).toBe(event);
    expect(component.getPortfolioCompanyMasterKPIValues).toHaveBeenCalledWith(
      null
    );
  });

  it("should set masterKpiValueUnit and searchFilter correctly, and call methods when event.UnitType is defined", () => {
    spyOn(component, "getPortfolioCompanyMasterKPIValues");

    const event = { UnitType: "Unit Type" };

    component.kpiTable_GlobalFilter(event);

    expect(component.masterKpiValueUnit).toBe("Unit Type");
    expect(component.searchFilter).toBe(event);
    expect(component.getPortfolioCompanyMasterKPIValues).toHaveBeenCalledWith(
      null
    );
  });
  it("should call getImpactKPIValues with correct arguments", () => {
    const event = "event";
    const service: PortfolioCompanyService = TestBed.inject(
      PortfolioCompanyService
    );
    const miscService: MiscellaneousService =
      TestBed.inject(MiscellaneousService);
    spyOn(service, "getImpactKPIValues").and.returnValue(of({}));
    spyOn(miscService, "createDateFromPeriod").and.callFake((date) => date);

    component.modelImpactKpi = {
      periodType: { type: "Date Range" },
      startPeriod: new Date(2022, 0, 1),
      endPeriod: new Date(2022, 11, 31),
    };
    component.isYtd = false;
    component.isYtd = false;
    component.isYtdPageLoad = false;
    component.isLtmPageLoad = false;
    const expectedSearchFilter = {
      portfolioCompanyID: 7361,
      paginationFilter: event,
      searchFilter: {
        periodType: "Date Range",
        startPeriod: new Date(2022, 0, 1),
        endPeriod: new Date(2022, 11, 31),
      },
      valueType: "Actual",
      isMonthly: true,
      isQuarterly: false,
      isAnnually: false,
      isPageLoad: true,
      moduleID: 6,
      companyId: "7361",
      kpiConfigurationData: [
        {
          sectionID: 13,
          fieldID: 15,
          aliasName: "Actual",
          subPageID: 2,
          options: null,
          chartValue: ["Monthly", "Quarterly", "Annual"],
        },
        {
          sectionID: 14,
          fieldID: 15,
          aliasName: "Budget",
          subPageID: 2,
          options: null,
          chartValue: ["Monthly", "Quarterly", "Annual"],
        },
        {
          sectionID: 15,
          fieldID: 15,
          aliasName: "Forecast",
          subPageID: 2,
          options: null,
          chartValue: ["Monthly", "Quarterly", "Annual"],
        },
        {
          sectionID: 16,
          fieldID: 15,
          aliasName: "IC",
          subPageID: 2,
          options: null,
          chartValue: ["Annual"],
        },
      ],
      isYtdPageLoad: component.isYtdPageLoad,
      isLtmPageLoad: component.isLtmPageLoad,
      isYtd: component.isYtd,
      isLtm: component.isLtm,
    };

    component.getPortfolioCompanyMasterKPIValues(event);

    // Assert
    expect(component.isLoader).toBe(false);
  });

  it("should call getImpactKPIValues method of portfolioCompanyService", () => {
    const searchFilter = {};
    const event = {};
    const response = {
      headers: [],
      rows: [],
      companyKpiAuditLog: [],
      isMonthly: false,
      isQuarterly: false,
      isAnnually: false,
    };
    const service: PortfolioCompanyService = TestBed.inject(
      PortfolioCompanyService
    );
    spyOn(service, "getImpactKPIValues").and.returnValue(of(response));

    component.getImpactKPIValues(searchFilter, event);

    // Assert
    expect(service.getImpactKPIValues).toHaveBeenCalled();
  });

  it("should update ErrorNotation and isToggleChecked when handleChange is called", () => {
    const eventValue = true;
    component.handleChange(eventValue);
    expect(component.ErrorNotation).toBe(eventValue);
  });

  it("should call error method of toastrService when showErrorToast is called", () => {
    const message = "error message";
    const title = "error title";
    const position = "toast-center-center";
    const service: ToastrService = TestBed.inject(ToastrService);
    spyOn(service, "error");

    component.showErrorToast(message, title, position);

    // Assert
    expect(service.error).toHaveBeenCalledWith(message, title, {
      positionClass: position,
    });
  });

  it("should call getValueTypeTabList when ngOnInit is called", () => {
    spyOn(component, "getValueTypeTabList");

    component.ngOnInit();

    // Assert
    expect(component.getValueTypeTabList).toHaveBeenCalled();
  });
  it("should add LTM suffix when LTM exists", () => {
    component.pageConfigResponse = {
      kpiConfigurationData: [{ aliasName: "period LTM" }],
      hasChart: false,
      kpiType: "",
    };
    component.isLtm = true;
    expect(component.setLtmYtdPeriodType("period")).toEqual("period LTM");
  });

  it("should add YTD suffix when YTD exists", () => {
    component.pageConfigResponse = {
      kpiConfigurationData: [{ aliasName: "period YTD" }],
      hasChart: false,
      kpiType: "",
    };
    component.isYtd = true;
    expect(component.setLtmYtdPeriodType("period")).toEqual("period YTD");
  });

  it("should not add any suffix when neither LTM nor YTD exists", () => {
    component.pageConfigResponse = {
      kpiConfigurationData: [{ aliasName: "period" }],
      hasChart: false,
      kpiType: "",
    };
    component.isLtm = false;
    component.isYtd = false;
    expect(component.setLtmYtdPeriodType("period")).toEqual("period");
  });

  it("should reset period flags when neither LTM nor YTD should be added", () => {
    component.pageConfigResponse = {
      kpiConfigurationData: [{ aliasName: "period" }],
      hasChart: false,
      kpiType: "",
    };
    component.isLtm = true;
    component.isYtd = true;
    component.setLtmYtdPeriodType("period");
    expect(component.isLtm).toEqual(true);
    expect(component.isYtd).toEqual(true);
  });
  it("should process page load view when selectedPeriodTypeConfiguration is undefined", () => {
    component.pageConfigResponse = {
      kpiConfigurationData: [{ aliasName: "period" }],
      kpiType: "ImpactKPIs",
      hasChart: false,
    };
    spyOn(component, "getPeriodTypeWithSuffix").and.returnValue("period");
    const result = component.processPageLoadView(undefined, "period");
    expect(component.valueTypeString).toEqual("period");
    expect(result).toEqual({ aliasName: "period" });
  });

  it("should update period type flags when hasYtd is true, isYtd is false, hasLtm is false, and isLtm is false", () => {
    component.hasYtd = true;
    component.isYtd = false;
    component.hasLtm = false;
    component.isLtm = false;
    component.pageConfigResponse = {
      kpiConfigurationData: [{ aliasName: "period" }],
      kpiType: "ImpactKPIs",
      hasChart: false,
    };
    spyOn(component, "getPeriodTypeWithSuffix").and.returnValue("period");
    component.updatePeriodTypeFlags();
    expect(component.isYtd).toEqual(true);
    expect(component.isLtm).toEqual(false);
  });

  it("should find period type configuration", () => {
    component.pageConfigResponse = {
      kpiConfigurationData: [{ aliasName: "period" }],
      kpiType: "ImpactKPIs",
      hasChart: false,
    };
    spyOn(component, "getPeriodTypeWithSuffix").and.returnValue("period");
    const result = component.findPeriodTypeConfiguration("period");
    expect(result).toEqual({ aliasName: "period" });
  });

  it("should set page load when isLtm and isLtmPageLoad are true", () => {
    component.pageConfigResponse = {
      kpiConfigurationData: [{ aliasName: "period" }],
      kpiType: "ImpactKPIs",
      hasChart: false,
    };
    spyOn(component, "getPeriodTypeWithSuffix").and.returnValue("period");
    component.isLtm = true;
    component.isLtmPageLoad = true;
    const result = component.setPageLoad();
    expect(result).toEqual(true);
    expect(component.isLtmPageLoad).toEqual(false);
  });
  it("should redirect to audit log page (positive scenario)", () => {
    spyOn(window, "open");
    spyOn(sessionStorage, "setItem");

    const config = { redirect_uri: "http://localhost/in" };
    (identityService.getEnvironmentConfig as jasmine.Spy).and.returnValue(
      config
    );

    const mappedDocuments: MappedDocuments = {
      valueId: 1,
      documentId: 1,
      commentId: 1,
      comments: "test",
      supportingDocumentsId: "1",
      mappingId: 100,
      documentModels: null,
      auditLogId: 154,
      auditLogCount: 10,
    };
    const Audit: Audit = {
      valueTypeId: 1,
      mappingId: 100,
      quarter: "Q1",
      year: 2023,
      month: 0,
      kpiValueId: 1054,
      valueType: "Actual",
      kpiId: 1046,
      moduleId: 6,
      companyId: 10546,
      FieldName: "attributeName",
      periodId: 1,
      columnKpiId: 1,
    };

    component.redirectToAuditLogPage(
      {},
      "attributeName",
      mappedDocuments,
      Audit
    );

    expect(sessionStorage.setItem).toHaveBeenCalled();
    expect(window.open).toHaveBeenCalledWith(
      "http://localhost/audit-logs",
      "_blank"
    );
  });

  it("should not redirect to audit log page (negative scenario)", () => {
    spyOn(window, "open");
    spyOn(sessionStorage, "setItem");

    const config = { redirect_uri: "" };
    (identityService.getEnvironmentConfig as jasmine.Spy).and.returnValue(
      config
    );

    const mappedDocuments: MappedDocuments = {
      valueId: 1,
      documentId: 1,
      commentId: 1,
      comments: "test",
      supportingDocumentsId: "1",
      mappingId: 100,
      documentModels: null,
      auditLogId: 154,
      auditLogCount: 10,
    };
    const Audit: Audit = {
      valueTypeId: 1,
      mappingId: 100,
      quarter: "Q1",
      year: 2023,
      month: 0,
      kpiValueId: 1054,
      valueType: "Actual",
      kpiId: 1046,
      moduleId: 6,
      companyId: 10546,
      FieldName: "attributeName",
      periodId: 1,
      columnKpiId: 1,
    };

    component.redirectToAuditLogPage(
      {},
      "attributeName",
      mappedDocuments,
      Audit
    );

    expect(sessionStorage.setItem).toHaveBeenCalled();
    expect(window.open).not.toHaveBeenCalled();
  });
});
