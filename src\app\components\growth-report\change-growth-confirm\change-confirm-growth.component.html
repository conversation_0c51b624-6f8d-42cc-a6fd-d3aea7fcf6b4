<confirm-modal customwidth="489px" [isCloseEnable]="true" [modalTitle]="'Attention!'" class="repository-modal" primaryButtonName="Confirm" secondaryButtonName="Cancel"
        (primaryButtonEvent)="CloseModal('Yes')" (secondaryButtonEvent)="CloseModal('No')"
        (closeIconClick)="CloseModal('No')">
        <div class="body-color">
            <div class="col-lg-12 col-md-12 col-xs-12 col-md-12 col-xl-12 col-sm-12 p-0">
            You’re about to navigate out of the page without saving template details. Clicking on 'Confirm' will navigate you out of the page without saving changes.
        </div>
        </div>
</confirm-modal>