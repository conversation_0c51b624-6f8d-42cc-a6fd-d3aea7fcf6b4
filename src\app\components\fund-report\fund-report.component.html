<div class="lpTemplateConfigContainer">
    <div class="d-flex hfContainer bborder header-box_shadow">
        <div class="pr12 ">
            <kendo-combobox  [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="selectedItem"
                [fillMode]="'solid'" [filterable]="true" name="template" [virtual]="virtual"
                class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32 mr-3" [size]="'medium'"
                [data]="allTemplates" [filterable]="true" [value]="selectedItem" [valuePrimitive]="false" textField="name"
                placeholder="Select Template" (valueChange)="templateAction($event)" valueField="id">
                <ng-template let-item kendoComboBoxSelectedItemTemplate>
                    <div class="d-inline-block"><img src="assets/dist/images/{{item.icon}}" alt="" /></div>
                    <div class="d-inline-block custom-label TextTruncate">{{item.name}}</div>
                </ng-template>
                <ng-template kendoComboBoxItemTemplate let-object>
                    <div class="custom-ui-label TextTruncate"><img src="assets/dist/images/{{object.icon}}" alt="" /> <span
                            class="img-pad ">{{object.name}}</span></div>
                </ng-template>
            </kendo-combobox>
        </div>
        <div id="fund-report-active-inactive" class="mr-auto">
            <kendo-combobox [clearButton]="false"  [(ngModel)]="selectedAction" [disabled]="isAction"
                                    [fillMode]="'solid'" [filterable]="true" name="action" [virtual]="false"
                                    class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="actions"
                                    [filterable]="false"  [valuePrimitive]="false" textField="name" placeholder="Select Module"
                                    (valueChange)="ExecuteAction($event)" valueField="name">
                                </kendo-combobox>
        </div>
        <div id="fund-report-new-section" class="">
            <nep-button Type="Secondary" (click)="addSectionList();">
                <em class="fa fa-plus plus"></em> New Section
            </nep-button>
        </div>
    </div>
    <div class="bContainer" #target [ngClass]="isPageLoaded?'d-block':'d-none'"
        [ngStyle]="{'height':windowHeight+'px','overflow-y':'auto'}">
        <form [formGroup]="fundReportForm" (ngSubmit)="submit(f)" #f="ngForm">
            <div cdkDropList (cdkDropListDropped)="drop($event)" formArrayName="reportItems">
                <div class="row mr-0 ml-0 p-all-row"
                    *ngFor="let Cntrl of  fundReportForm?.controls?.reportItems?.controls ; let i = index" cdkDrag
                    #elem="cdkDrag">
                    <div [formGroupName]="i"
                        class=" col-12 col-md-12 col-sm-12 col-xs-12 col-lg-12 col-xl-12 pl-0 pr-0">
                        <div class="mr-0 ml-0 box drop-card-shadow">
                            <div class="d-inline-block box-border-right drag-icon">
                                <img cdkDragHandle src="assets/dist/images/6dots.svg" alt="">
                            </div>
                            <div class="d-inline-block p-all">
                                <app-customselect class="drag-dropdown" formControlName="sectionName"
                                    [selectList]="sectionList"
                                    [selectedValue]="Cntrl.value.sectionName!=null?Cntrl.value.sectionName:sectionList[0]"
                                    (OnSelect)="sectionOnChange(i,$event)">
                                </app-customselect>
                            </div>
                            <div class="d-inline-block p-all"
                                *ngIf="Cntrl.value.sectionName==null?false:Cntrl.value.sectionName.name|TrueOrFalse">
                                <kendo-combobox [clearButton]="false"  formControlName="period"
                                    [fillMode]="'solid'" [filterable]="true" name="period" [virtual]="virtual"
                                    class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="periodList"
                                    [filterable]="false"  [valuePrimitive]="true" textField="name" placeholder="Select Period"
                                     valueField="name">
                                </kendo-combobox>
                            </div>
                            <div class="d-inline-block p-all"
                                *ngIf="Cntrl.value.sectionName==null?false:Cntrl.value.sectionName.name==='Attribution Report Graph'">
                                <div formArrayName='nestedArray'>
                                    <button id="multipleFilter" [style]="{marginTop:'4px'}" class="multipleFilterButton"
                                        type="button" [ngbPopover]="popContent" placement="bottom top"container="body"
                                        #popOver="ngbPopover" [autoClose]="'outside'"
                                        [ngClass]="i==isHighlight ? 'activeFilterButton':''"
                                        (mousedown)="highlightclear(i)"
                                        (click)="onOpenFilters();highlightColorAttributionFilters(i,00);">
                                        <span  [ngClass]="((Cntrl.controls?.nestedArray?.controls[0].value|ReturnString)!='Select filters')?'multiple-filter-active-class':''" title={{Cntrl.controls?.nestedArray?.controls[0].value|ReturnString}} >{{Cntrl.controls?.nestedArray?.controls[0].value|ReturnString}}</span>
                                        <img class="multipleFilterIcon" src="assets/dist/images/advancedFilters.svg"
                                            alt="" />
                                    </button>

                                    <ng-template #popContent>
                                        <div class="attributionFilterDiv"
                                            *ngFor="let subgroup of Cntrl.controls?.nestedArray?.controls; let idx = index;"
                                            [formGroupName]="idx">
                                            <div #advancedFilters id="filtersContainer" class="fundfilter-wh">
                                                <div id="containerHeader"class="containerheader-h-p">
                                                    <span class="fundreport-fl-c" >
                                                        Apply filters
                                                    </span>
                                                    <span (click)="popOver.close();isFiltersOpen=false" class="fund-fr-cp">
                                                        <i class="pi pi-times"></i>
                                                    </span>
                                                </div>
                                                <div id="containerBody" class="containerBody-bb-bt">
                                                    <div class="fund-d">
                                                        <div class="fund-bb-bg">
                                                            <div [ngClass]="activeFilterCategory === filterCategory ? 'filterHeadingStyle activeCategory' : 'filterHeadingStyle inactiveCategory'"
                                                                [ngStyle]="{'margin-bottom':filterCategory == 'Evaluation Date' ? '20px':''}"
                                                                *ngFor="let filterCategory of filterCategories"
                                                                (click)="selectFilterCategory(filterCategory);">
                                                                {{filterCategory}}
                                                            </div>
                                                        </div>
                                                        <div class="fund-height">
                                                            <div *ngIf="activeFilterCategory === 'Evaluation Date'">
                                                                <div class="fundrep-w-mt-ml">
                                                                    <span class="contentStyle">Evaluation date</span>
                                                                        <kendo-datepicker calendarType="classic" [popupSettings]="popupSettings"
                                                                            class="k-picker-custom-flat k-datepicker-height-32 k-datepicker-width-240" [format]="format" [fillMode]="'flat'"
                                                                            formControlName="evaluationDate" placeholder="Select evaluation date" id="evaluationDate"
                                                                            name="evaluationDate"></kendo-datepicker>
                                                                </div>
                                                            </div>
                                                            <div class="fund-ml-mt">
                                                                <div
                                                                    *ngIf="activeFilterCategory === 'Attribution Type'">
                                                                    <div class="contentStyle content-margin-b">Attribution Type <span class="red">*</span>
                                                                    </div>
                                                                    <kendo-autocomplete appBlockCutCopyPaste #kendoAutoComplete
                                                                        [readonly]="true" name="typeAhead"
                                                                        [data]="attributionTypes"
                                                                        [readonly]="disabled || readonly"
                                                                        (filterChange)="search($event)"
                                                                        [valueField]="'label'"
                                                                        [suggest]="true"
                                                                        placeholder="Attribution Type"
                                                                        [inputStyle]="{paddingLeft: '8px'}"
                                                                        [disabled]="disabled"
                                                                        formControlName="attributionType"
                                                                        (focus)="kendoAutoComplete.toggle(true)">
                                                                    </kendo-autocomplete>
                                                                </div>
                                                                <div *ngIf="activeFilterCategory === 'Strategies'">
                                                                    <div class="contentStyle fund-constyle-mb">Strategies</div>
    
                                                                    <kendo-multiselect [data]="strategyList" #strategies [checkboxes]="true" [rounded]="'medium'" [fillMode]="'flat'"
                                                                        [virtual]="{ itemHeight: 36 }" [ngClass]="{'k-multiselect-search-150':selectedCopyToStrategiesList?.length>0}"
                                                                        [kendoDropDownFilter]="filterSettings" name="region" [virtual]="virtual" [clearButton]="false"
                                                                        class="k-multiselect-custom k-dropdown-width-300 k-multiselect-grey-chip k-multiselect-flat-custom"
                                                                        [placeholder]="'Select strategy'" [autoClose]="false" [valueField]="'strategyId'"
                                                                        textField="strategy"
                                                                        valueField="strategy"
                                                                        formControlName="strategies"
                                                                        [selectedItemsText]="'{0} items selected'">
                                                                    </kendo-multiselect>
                                                                </div>
                                                                <div *ngIf="activeFilterCategory === 'Funds'">
                                                                    <div class="contentStyle fundrpt-contentsty-mb">Funds</div>
                                                                    <kendo-multiselect [data]="fundList"
                                                                        [virtual]="{ itemHeight: 36 }"
                                                                        [maxSelectedItems]="1"
                                                                        [placeholder]="'Select funds'"
                                                                        textField="fundName"
                                                                        valueField="fundName"
                                                                        formControlName="funds"
                                                                        [selectedItemsText]="'{0} items selected'">
                                                                    </kendo-multiselect>
                                                                </div>
                                                                <div *ngIf="activeFilterCategory === 'Regions'">
                                                                    <div class="contentStyle contentsty-mb"
                                                                       >Regions</div>
                                                                    <kendo-multiselect #regions [checkboxes]="true" [rounded]="'medium'" [fillMode]="'flat'"
                                                                    [ngClass]="{'k-multiselect-search-150':selectedCopyToRegionList?.length>0}"
                                                                    [kendoDropDownFilter]="filterSettings" name="region" [virtual]="virtual" [clearButton]="false"
                                                                    class="k-multiselect-custom k-dropdown-width-300 k-multiselect-grey-chip k-multiselect-flat-custom"
                                                                    [tagMapper]="tagMapper" [data]="regionList" [(ngModel)]="selectedCopyToRegionList"
                                                                    [textField]="'region'" (close)="clearSearch(regions)" [valueField]="'regionId'"
                                                                    (valueChange)="onRegionChange($event)" [autoClose]="false" placeholder="Select regions"
                                                                    formControlName="regions">
                                                                    <ng-template kendoMultiSelectHeaderTemplate>
                                                                        <div class="inline-container">
                                                                            <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"
                                                                                kendoCheckBox [checked]="isCheckedCopyRegionAll" [indeterminate]="isRegionIndet"
                                                                                (click)="onRegionSelectAll()" />
                                                                            <kendo-label for="chk">Select All</kendo-label>
                                                                        </div>
                                                                    </ng-template>
                                                                    <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                                                        <span class="TextTruncate pl-1 Body-R" id="region-{{dataItem.regionId}}" [title]="dataItem.region">{{ dataItem.region }}</span>
                                                                    </ng-template>
                                                                </kendo-multiselect>
                                                                </div>

                                                                <div *ngIf="activeFilterCategory === 'Countries'">
                                                                    <div class="contentStyle contstyle-mb"
                                                                        >Countries</div>
                                                                    <kendo-multiselect [data]="countryList" [checkboxes]="true" [rounded]="'medium'" [fillMode]="'flat'"
                                                                        [virtual]="{ itemHeight: 36 }" [ngClass]="{'k-multiselect-search-150':selectedCopyToRegionList?.length>0}"
                                                                        [kendoDropDownFilter]="filterSettings" name="region" [virtual]="virtual" [clearButton]="false"
                                                                        class="k-multiselect-custom k-dropdown-width-300 k-multiselect-grey-chip k-multiselect-flat-custom"
                                                                        [maxSelectedItems]="1" [autoClose]="false"
                                                                        [placeholder]="'Select countries'"
                                                                        textField="country"
                                                                        valueField="country"
                                                                        formControlName="countries"
                                                                        [selectedItemsText]="'{0} items selected'">
                                                                    </kendo-multiselect>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="containerFooter">
                                                    <div class="container-fr-p">
                                                        <nep-button class="nep-butn-pl" Type="Secondary" [disabled]="subgroup.invalid"
                                                             (click)="clearFilters(i,idx)">
                                                            Reset
                                                        </nep-button>
                                                        <!-- <nep-button Type="Primary" [disabled]="subgroup.invalid"
                                                                                                                style="padding-left:12px"
                                                                                                                (click)="closeAttributionFiltersPopover()">
                                                                                                                Apply
                                                                                                            </nep-button> -->
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                            <div class="d-inline-block p-all"
                                *ngIf="Cntrl.value.sectionName==null?false:Cntrl.value.sectionName.name==='Attribution Report Graph'">
                                <div formArrayName='charts'>
                                    <button id="multipleFilter" [style]="{marginTop:'4px'}" class="multipleFilterButton"
                                        type="button" [ngbPopover]="popContent" placement="bottom top"
                                        #popOver="ngbPopover" [autoClose]="'outside'" container="body"
                                        [ngClass]="1+i==isChartHighlight ? 'activeFilterButton':''"
                                        (click)="onOpenCharts();highlightColorChartFilters(i,00)">
                                        <span [ngClass]="((fundReportForm.value['reportItems'][i]['charts']|ReturnChart)!='Select Charts')?
                                                                                'multiple-filter-active-class':''"
                                            title="{{fundReportForm.value['reportItems'][i]['charts']|ReturnChart}}">
                                            {{fundReportForm.value["reportItems"][i]["charts"]|ReturnChart}}</span>
                                        <img class="multipleFilterIcon" src="assets/dist/images/advancedFilters.svg"
                                            alt="" />
                                    </button>
                                    <ng-template #popContent>
                                        <div class="chart-Box">
                                            <div cdkDropList (cdkDropListDropped)="subdrop($event,i)">
                                                <div class="row ml-0 mr-0"
                                                    *ngFor="let subSubgroup of Cntrl.controls?.charts?.controls; let cidx = index;"
                                                    [formGroupName]="cidx" cdkDrag>
                                                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 pl-0 pr-0">
                                                        <div class="d-inline-block drag-icon">
                                                            <img class="chart-list" cdkDragHandle
                                                                src="assets/dist/images/6dots.svg" alt="">
                                                        </div>
                                                        <div class="d-inline-block">
                                                            <mat-checkbox class="chat-checkbox mat-custom-checkbox"
                                                                formControlName="isDisable">{{subSubgroup.value.label}}</mat-checkbox>
                                                        </div>
                                                        <!-- <div class="d-inline-block">
                                                            <span class="chart-list"> {{subSubgroup.value.label}}</span>
                                                        </div> -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                            <div class="d-inline-block  pull-right">
                                <div class="d-inline-block box-border-left pull-right">
                                    <em class="pi pi-times" (click)="delete(i)"></em>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div
        class="d-flex hfContainer lptemplate-footer flex-row-reverse paddingtop col-lg-12 col-md-12 col-sm-12 fixed-footer" [ngStyle]="{'width': sideNavWidth}">
        <div id="fund-report-save-as" class="">
            <nep-button Type="Primary" (click)="save()"
                [disabled]="!fundReportForm.valid||fundReportForm.value['reportItems'].length<1">
                Save as
            </nep-button>
        </div>
        <div id="fund-report-preview" class="pr12">
            <nep-button Type="Secondary" [disabled]=true>
                <img class="preview" src="assets/dist/images/Content.svg" alt="" /> Preview
            </nep-button>
        </div>
        <div id="fund-report-reset" class="pr12">
            <nep-button Type="Secondary" (click)="resetCntrl()"
                [disabled]="!fundReportForm.valid||fundReportForm.value['reportItems'].length<1">
                Reset
            </nep-button>
        </div>
        <div id="fund-report-cancel" class="pr12">
            <nep-button Type="Secondary" [disabled]=true>
                Cancel
            </nep-button>
        </div>
    </div>
    <div *ngIf="showpopup">
        <confirm-modal class="AddOrUpdateKpi" primaryButtonName="Confirm" (secondaryButtonEvent)="onClose()"
            [disablePrimaryButton]="disableConfirmSavebtn" (primaryButtonEvent)="confirmSave();" id="add_template"
            secondaryButtonName="Cancel" [modalTitle]="title">
            <div class="row mr-0 ml-0">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 pl-0 pr-0">
                    <div class="row mr-0 ml-0">
                        <div class="col-lg-12 col-md-5 col-xs-5 pl-0 pr-0 ">
                            <div class="">Template Name
                            </div>
                            <nep-input (mouseout)="onChange($event)" (onChange)="onChange($event)"
                                [ngClass]="{'custom-error-kpi-input':isexits}" [value]="fundTemplateName"
                                [placeholder]="placeholder"
                                class="kpis-custom-select custom-nep-input default-txtcolor lp-nep-input "></nep-input>
                            <div *ngIf="isexits" class="nep-error">Template name already exits</div>
                        </div>
                        <div class="col-lg-12 col-md-5 col-xs-5 pl-0 pr-0 pt-0 ">
                            <div class="d-inline-block padding-radio">
                                <label class="container">Update current template
                                    <input id="Update_current_template" type="radio" checked="checked" name="radio"  [(ngModel)]="selectionradio" value="Update" (mouseout)="removeRadioerror(selectionradio)" (click)="radiobtnClick('Update')" 
                                    [disabled]="default==selectedSection.name">
                                    <span class="checkmark"></span>
                                  </label>
                            </div>
                            <div class="padding-radio-save">
                                <label class="container">Save as new template
                                    <input id="Save_as_new_template" type="radio" name="radio" (click)="radiobtnClick('New');" (mouseout)="removeRadioerror(selectionradio)" name="radio" value="New" [(ngModel)]="selectionradio" >
                                    <span class="checkmark"></span>
                                  </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </confirm-modal>
    </div>

    <div *ngIf="isPopup">
        <modal customwidth="489px" [modalTitle]="modalTitle" primaryButtonName="Confirm" secondaryButtonName="Cancel"
            (primaryButtonEvent)="OnConfig($event)" (secondaryButtonEvent)="OnCancel($event)"
            [disablePrimaryButton]="disableRenameConfirm">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-xs-12" *ngIf="this.configObj.Status == 'Set as Active'">
                    This will set {{this.selectedSection.name}} as active. Are you sure?
                </div>
                <div class="col-lg-12 col-md-12 col-xs-12" *ngIf="this.configObj.Status == 'Set as Inactive'">
                    This will set {{this.selectedSection.name}} as Inactive and Default template as active. Are you
                    sure?
                </div>
                <div class="col-lg-12 col-md-5 col-xs-5 pl-3 pr-3 " *ngIf="this.configObj.Status == 'Rename template'">
                    <div class="">Template Name*
                    </div>
                    <nep-input (onInput)="ontemplateChange($event)" [ngClass]="{'custom-error-kpi-input':isexits}"
                        [value]="templateName" placeholder=""
                        class="kpis-custom-select custom-nep-input default-txtcolor">
                    </nep-input>
                    <div *ngIf="isexits" class="nep-error">Template name already exits</div>
                </div>
                <div class="col-lg-12 col-md-12 col-xs-12"
                    *ngIf="this.configObj.Status == 'Delete template' && selectedSection.name !== activeTemplateName">
                    This will delete {{this.selectedSection.name}} template. Are you sure?
                </div>
                <div class="col-lg-12 col-md-12 col-xs-12"
                    *ngIf="this.configObj.Status == 'Delete template'&& selectedSection.name === activeTemplateName">
                    This will delete {{this.selectedSection.name}} template and set Default template as active. Are you
                    sure?
                </div>
            </div>
        </modal>
    </div>
</div>
<app-loader-component *ngIf="isLoader"></app-loader-component>
