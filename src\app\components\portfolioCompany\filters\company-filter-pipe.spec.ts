import { CompanyGroupFilter } from './company-filter-pipe';

describe('CompanyGroupFilter', () => {
    let pipe: CompanyGroupFilter;

    beforeEach(() => {
        pipe = new CompanyGroupFilter();
    });

    it('should return the same items if no filter is provided', () => {
        const items = [{ groupName: 'Group1' }, { groupName: 'Group2' }];
        expect(pipe.transform(items, null)).toEqual(items);
        expect(pipe.transform(items, '')).toEqual(items);
    });

    it('should return the same items if items are not provided', () => {
        expect(pipe.transform(null, 'Group1')).toBeNull();
    });

    it('should filter items based on the group name', () => {
        const items = [{ groupName: 'Group1' }, { groupName: 'Group2' }, { groupName: 'Group3' }];
        expect(pipe.transform(items, 'Group1')).toEqual([{ groupName: 'Group1' }]);
    });

    it('should be case insensitive', () => {
        const items = [{ groupName: 'Group1' }, { groupName: 'Group2' }, { groupName: 'Group3' }];
        expect(pipe.transform(items, 'group1')).toEqual([{ groupName: 'Group1' }]);
    });
});