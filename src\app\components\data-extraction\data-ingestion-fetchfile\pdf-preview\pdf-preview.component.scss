@import "../../../../../variables";
@import "../../../../../assets/dist/css/font.scss";
.notification-sidebar
{
  width:60%;
  iframe{
    width: 100% !important;
    height: calc(100vh - 100px) !important;
    border: none !important;
}
  box-shadow:$shadow-short !important;
  label{
    margin-bottom: 0px !important;
    @extend .Caption-R;
    &:after {
      content: "*";
      color: $Negative-100;
      padding-left: 4px;
  }
  }
  .side-header {
    padding: 8px 0rem;
    margin:0 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .title-h{
    @extend .Body-B;
    color: #2B2B33;
    width: 80%;
  }
  
  .close-icon {
    a{
      cursor: pointer;
    }
    img{
      width: 1rem;
      height: 1rem;
    }
  }
  
  .close-btn img {
    width: 16px;
    height: 16px;
  }
  
  .light-divider {
    border: 0;
    border-top: 1px solid $Neutral-Gray-05;
    margin-bottom: 12px;
    margin-top: 6px;
  }
  .sidebar-container{
    width: 100%;
  }
}
