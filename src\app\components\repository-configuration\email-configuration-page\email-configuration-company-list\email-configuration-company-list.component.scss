@import "../../../../../_variables";
@import "../../../../../assets/dist/css/font.scss";

.email-company-container {
  width: 100%;
  height: calc(100vh - 200px);
  display: flex; 
  flex-direction: column;
  border: 1px solid $Neutral-Gray-10;
  overflow: hidden;
}
.data-ingestion-section{
    height: calc(100vh - 200px);
    border-radius: $space-4;
  }
.list-group {
  overflow: hidden;
  height: 100%;
}

.companies-list-container {
  overflow-y: auto;
  max-height: calc(100% - 48px);
}

.all-company {
  color: $Neutral-Gray-90;
  background-color: $Primary-35;
}

.repo-confis {
  height: 48px;
  padding: 10px 16px;
}

.selected-company {
  background-color: $Primary-40;
}

.chkbx-border {
  border-color: $Neutral-Gray-80;
}

.search-box-container {
  height: 48px;
  position: sticky;
  top: 0;
  z-index: 1;
}

.disabled {
  pointer-events: none;
  opacity: 76%; 
}