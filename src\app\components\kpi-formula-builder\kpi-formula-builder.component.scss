@import "../../../variables.scss";
.formula-builder-model {
    .formula-line-item {
        max-width: 90%;
    }
    .kpi-header {
        position: absolute;
    }
    .kpi-title {
        max-width: 90%;
    }
    .equals-op {
        position: absolute;
        max-width: 10%
    }
    td {
        cursor: pointer !important;
    }
    .active-kpi {
        background: $nep-row-active 0% 0% no-repeat padding-box !important;
    }
    text-align: center;
    .pr-nep-btn {
        padding-left: 12px;
    }
    .nep-card {
        width: 57rem;
        top: 6%;
        left: 2%;
        .nep-card-header {
            height: 52px;
            color: #1A1A1A !important;
            .close-icon {
                cursor: pointer;
                color: #1A1A1A !important;
                line-height: 1rem;
            }
        }

        .nep-card-body {
            padding: 20px;

            .formula-body {
                background: $nep-white 0% 0% no-repeat padding-box;
                box-shadow: 0px 0px 12px $nep-shadow-color;
                border: 1px solid $nep-divider;
                border-radius: 4px;
                -moz-border-radius: 4px;
                -webkit-border-radius: 4px;
                height: 100%;
                min-height: 460px;
                .formula-section {
                    padding: 20px 16px;
                    height: 100%;

                    .formula-column {
                        letter-spacing: 0px;
                        color: $nep-primary;
                        font-family: $nep-font-medium  !important;
                        font-style: italic;
                    }

                    .formula-builder-section {
                        background: $nep-white 0% 0% no-repeat padding-box;
                        height: calc(100% - 28px);

                        .custom-text-area {
                            height: 400px;
                            overflow-y: scroll;
                            width: 100%;
                            max-height: 100% !important;
                            padding: 16px !important;
                            border-radius: 4px;
                            border: 1px solid $nep-divider;
                            line-height: 2;
                            &:focus-visible {
                                outline: none !important;
                            }
                          
                        }
                    }
                }

                .border-right {
                    border-right: 1px solid $nep-divider;
                }

                .formula-kpi-section {
                    background: #FAFAFB 0% 0% no-repeat padding-box;

                    .kpi-type {
                        padding: 16px;

                        label {
                            display: block;
                            letter-spacing: 0px;
                            color: #55565A;
                        }
                         .formula-btn {
                            width: 24px;
                            height: 24px;
                            background: $nep-white 0% 0% no-repeat padding-box;
                            border: 1px solid $nep-icon-grey;
                            border-radius: 4px;
                            display: inline-block;
                            cursor: pointer;
                            letter-spacing: 0px;
                            color: $nep-icon-grey;
                            opacity: 1;

                            &:hover {
                                background-color: $nep-button-hover-color;
                            }
                        }
                    }
                       .companyListSearchHeight {
                        height: 42px !important;
                        width: 100% !important;
                        padding-top: 0px !important;
                        padding-bottom: 0px !important;
                        border-right: none;
                        border-left: none !important;
                        border-top: 1px solid #DEDFE0 !important;
                        border-bottom: 1px solid #DEDFE0 !important;
                    }
                }
            }
        }
    }
}
.custom-formula-ul-select
{
    position: absolute;
    top: 20px;
    z-index: 1500;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 6px #00000014;
    border-radius: 4px;
    border: 1px solid #dedfe0;
    ul{
        height: 220px;
        width: 240px;
        overflow-x: auto;
        margin-bottom: 0px !important;
        opacity: 1;
    }
    li{
        padding: 16px;
        width: 100%;
        text-align: left;
        letter-spacing: 0px;
        color: #4061C7;
        opacity: 1;
        &:hover{
            cursor: pointer;
            background: #F7F8FC 0% 0% no-repeat padding-box;
        }
    }
}
.error-select
{
    ul{
        border: 1px solid red !important;
        max-height: 200px !important;
        height: auto !important;
        li{
            color: red !important;
        }
    }
}
