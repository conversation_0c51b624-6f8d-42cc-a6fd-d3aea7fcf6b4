import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpServiceInterceptor } from 'src/app/interceptors/http-service-interceptor';
import { LpReportConfigComponent } from './lp-report-config.component';
import { LpReportConfigService } from 'src/app/services/lp-report-config.service';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from "@angular/platform-browser";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { MaterialModule } from 'src/app/custom-modules/material.module';
@NgModule({
  declarations: [
    LpReportConfigComponent
  ],
  imports: [
    FormsModule,ReactiveFormsModule,
    CommonModule,
    HttpClientModule,
    SharedComponentModule,
    KendoModule,
    MaterialModule,
    RouterModule.forChild([
      { path: '', component: LpReportConfigComponent }
    ])
  ],  
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [LpReportConfigService, {
    provide: HTTP_INTERCEPTORS,
    useClass: HttpServiceInterceptor,
    multi: true,
  }]
})
export class LpReportConfigModule { }
