﻿.all-bulk-upload {

    .upload-body {
        height: 438px !important;
    }

    .supporting-documents {
        border: 1px solid #E6E6E6;
        border-radius: 4px !important;
        height: 200px !important;
        text-align: center !important;
        margin: 16px 0 !important;

    }

    .supporting-documents-body {
        max-height: 160px !important;
        overflow-y: auto !important;
    }

    .supporting-documents-header {
        background: #F5F5F7;
        border-bottom: 1px solid #E6E6E6;
    }

    .supporting-documents-title {
        @extend .common-styles;
        padding: 8px 8px 8px 19px !important;
        color: #666666 !important;
        font-size: 12px;
        font-weight: 500;
        line-height: 20px;
        label{
            color: #4061C7 !important;
            text-decoration: underline;
            text-underline-position: under;
        }
    }

    .supporting-documents-text-area {
        border: 1px solid #DEDFE0 !important;
        border-radius: 4px !important;
    }

    .supporting-documents-single-header {
        border: 1px solid #DEDFE0 !important;
        border-radius: 4px !important;
        padding: 2px 8px 6px 16px !important
    }


    .centered {
        @extend .common-styles;
        text-align: center;
    }

    .single-file-drop {
        opacity: 0 !important;
        padding: 0.3rem 0.75rem !important;
    }

    .multiple-file-drop {
        opacity: 0 !important;
    }

    $font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif;
    $font-style: normal;
    $font-weight: 400;

    .common-styles {
        font-family: $font-family !important;
        font-style: $font-style;
        font-weight: $font-weight;
    }

    .single-upload-lable {
        @extend .common-styles;
        line-height: 20px;
        color: #B3B3B3 !important;
        display: flex !important;
        padding-top: 4px !important;
    }

    .single-file-browse {
        @extend .common-styles;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #4061C7 !important;
        line-height: 20px;
    }

    .multiple-file-browse {
        @extend .common-styles;
        padding: 8px 0 16px !important;
        color: #7E7E8C !important;
        line-height: normal;
    }

    .all-upload-popup-textarea {
        @extend .common-styles;
        padding-top: 8px !important;
        font-size: 12px;
        line-height: 20px;
        color: #000 !important;
    }
    .all-upload-popup-textarea::placeholder {
        color: #B3B3B3 !important;
    }

    .cursor-pointer {
        cursor: pointer;
    }
    img {
        width: 17px !important;
        height: 17px !important;
        object-fit: cover;
    }
    .height-input-drop-file {
        padding-top: 54px !important;
        img {
            width: 55px !important;
            height: 47px !important;
            object-fit: cover;
        }
    }

    .chips-container {
        display: flex;
        flex-wrap: wrap;
        padding: 10px 10px 8px 10px !important
    }

    .chip {
        margin: 5px;
        padding: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #f1f1f1;
        border-radius: 25px;
    }

    .chip i {
        margin-left: 10px;
        cursor: pointer;
    }

    .single-chip-content {
        display: flex !important;
        max-width: 89% !important;
    }

    .multiple-chip-content {
        display: flex !important;
    }

    .chip-file-name {
        width: 160px !important;
    }

    .chip-text-align {
        text-align: left !important;
    }

    .sinle-file-upload-style {
        display: flex;
        justify-content: space-between;
        height: 28px !important;
    }

    .multiple-file-upload {
        .mat-mdc-chip {
            position: relative;
            z-index: 0;
            max-width: 220px;
            padding: 2px 4px 2px 4px;
            margin: 8px !important;
        }
    }

    .single-file-upload {
        .mat-mdc-chip {
            position: relative;
            z-index: 0;
            max-width: 638px;
            padding: 2px 8px 2px 8px;
            margin-bottom: 0px !important;
            top: 2px !important;
        }
    }

    .popupcommon-labelpadding {
        @extend .common-styles;
        font-size: 12px;
        font-weight: 500;
        line-height: 20px;
        color: #666666 !important;
        padding-bottom: 4px !important;
    }

    .custom-close-icon {
        color: #000000 !important
    }


    .download-circle-btn-loader {
        color: #ffffff !important;
    }

    .close-icon-chips {
        padding-top: 2px;
    }
    .single-file-upload-padding{
        margin-top: 2px !important;
    }
}