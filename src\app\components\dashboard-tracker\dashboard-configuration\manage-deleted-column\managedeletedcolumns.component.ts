import { State } from '@progress/kendo-data-query';
import { GridDataResult } from '@progress/kendo-angular-grid';
import { Component, OnInit, Input, OnDestroy, Output, EventEmitter } from '@angular/core';
import { debounceTime, Observable, of, Subject, takeUntil } from 'rxjs';
import { DashboardTrackerService } from 'src/app/services/dashboard-tracker.service';
import { DashboardConfigurationConstants } from 'src/app/common/constants';

@Component({
  selector: 'app-manage-deleted-columns',
  templateUrl: './managedeletedcolumns.component.html',
  styleUrls: ['./managedeletedcolumns.component.scss']
})
export class ManageDeletedColumnsComponent implements OnInit, OnDestroy {
  deletedColumns: any[] = [];
  state: State = {
    skip: 0,
    take: 100
  };
  view: Observable<GridDataResult>;
  isLoading: boolean = true;
  gridColumns: any[] = [];
  totalRecords: number = 0;
   // Collection to store validation errors for textboxes
   validationErrors: Map<string, string> = new Map();
   
    // Subject for debouncing textbox changes
  private textboxChangeSubject = new Subject<{value: string, dataItem: any, column: any}>();
  private destroy$ = new Subject<void>();
  
  ngOnInit(): void {
    console.log('ManageDeletedColumnsComponent initialized');
    this.loadDashboardTableData(this.state);
    this.setupTextboxDebounce();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupTextboxDebounce(): void {
    this.textboxChangeSubject
      .pipe(
        debounceTime(500), 
        takeUntil(this.destroy$)
      )
      .subscribe(({value, dataItem, column}) => {
        this.updateCellValue(dataItem, column, value);
      });
  }
  // Expose constants to template
  DashboardConfigurationConstants = DashboardConfigurationConstants;

  constructor(private dashboardTrackerService: DashboardTrackerService) { }

  loadDashboardTableData(state: State): void {
    const filter = {
      First: state.skip,
      Rows: state.take
    };
    this.isLoading = true;
    
    this.dashboardTrackerService.getGetDeletedColumnsTableData(filter).subscribe({
      next: (response) => {
        if (response && response.data && response.columns) {
        const serialNoColumn = response.columns.find(col => col.name === this.DashboardConfigurationConstants.SerialNo);
        const otherColumns = response.columns.filter(col => col.name !== this.DashboardConfigurationConstants.SerialNo);
        
        if (serialNoColumn) {
          this.gridColumns = [serialNoColumn, ...otherColumns];
        } else {
          this.gridColumns = response.columns;
        }
         
          this.totalRecords = response.totalRecords || 0;
          this.view = of<GridDataResult>({
            data: response.data,
            total: this.totalRecords
          });
        } else {
          this.view = of<GridDataResult>({ data: [], total: 0 });
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.view = of<GridDataResult>({ data: [], total: 0 });
      }
    });
  }

   // Update or add cell value to the collection
   private updateCellValue(dataItem: any, column: any, newValue: any): void {
     dataItem[column.name] = newValue;
   }

  dataStateChange(event: any): void {
    this.state = event;
    this.loadDashboardTableData(this.state);
  }

 
   // Handle textbox value changes with debounce
   onTextboxValueChange(value: string, dataItem: any, column: any): void {
     // Send to debounced subject for processing
     this.textboxChangeSubject.next({value, dataItem, column});
   }

  // Handle clear button click for textbox
  clearTextboxValue(dataItem: any, column: any): void {
    const validationKey = this.getValidationKey(dataItem, column);
    // Clear validation error
    this.validationErrors.delete(validationKey);
    // Clear the value and process normally
    this.onTextboxValueChange('', dataItem, column);
  }
  // Handle column selection click
  onColumnSelectionClick(index: number): void {
    this.gridColumns[index].selected = !this.gridColumns[index].selected;
  }

  // Check if a specific textbox has validation error
  hasValidationError(dataItem: any, column: any): boolean {
    const validationKey = this.getValidationKey(dataItem, column);
    return this.validationErrors.has(validationKey);
  }

  // Get validation error message for a specific textbox
  getValidationError(dataItem: any, column: any): string {
    const validationKey = this.getValidationKey(dataItem, column);
    return this.validationErrors.get(validationKey) || '';
  }

  // Generate unique key for validation tracking
  private getValidationKey(dataItem: any, column: any): string {
    return "";
  }

}
