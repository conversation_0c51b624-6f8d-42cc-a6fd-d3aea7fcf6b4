<div class="row">
    <div class="col-lg-12">
        <div class="add-user-component">
            <div class="card card-main">
                <div class="card-header card-header-main p-0">
                    <div class="row mr-0 ml-0 fundlist-header">
                        <div class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                            <div class="float-left">
                                <div class="fundlist-title">
                            
                                </div>
                            </div>
                            <div class="float-right">
                                <div class="d-inline-block search">
                                    <span class="fa fa-search fasearchicon p-1"></span>
                                    <input #gb pInputText type="text" [appApplyFilter]="{ data: investorListClone, columns: cols,IsFreezeColumn:false,freezeColumns:'Kpi,text'}"
                                    (filtered)="investorList = $event" id="filterEvent"
                                        class="search-text-company TextTruncate companyListSearchHeight"
                                        placeholder="Search investors" [(ngModel)]="globalFilter">
                                </div>
                                <div class="d-inline-block">
                                    <img (click)="investorListExport()" alt="" class="p-action-padding download-excel" title="Export Fund (Excel file)"
                                        src="assets/dist/images/Cloud-download.svg" id="exportEvent" />
                                </div>
                                <div class="d-inline">
                                    <span class="col-divider">
                                    </span>
                                </div>
                                <div class="d-inline-block">
                                    <div class="add-icon p-add-padding">
                                        <a (click)="addRedirect()" tooltipPosition="top" title="Add investor" id="addEvent">
                                            <img alt="" class="" tooltipStyleClass="bg-tooltip-color" id="addinvestor" title="Add investor" src="assets/dist/images/plus.svg" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <kendo-grid [loading]="isLoading" [data]="canViewInv ? investorList : []" [sortable]="true" [sort]="sort" (dataStateChange)="dataStateChange($event)" id="dataStateChangeEvent" class="custom-kendo-list-grid k-grid-border-right-width k-grid-outline-none">
                        <kendo-grid-column field="investorName">
                            <ng-template kendoGridHeaderTemplate>
                                <div class="header-icon-wrapper wd-98">
                                    <span class="TextTruncate S-M">
                                        Investor Name
                                    </span>
                                </div>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-investor>
                                <a class="click-view TextTruncate" title="{{investor.investorName}}"
                                    title="View Details"
                                    [routerLink]="['/investor-details', investor.encryptedInvestorId]"
                                    (click)="redirectToInvestor(investor)" id="redirectToInvestorEvent"
                                    href="javascript:void(0);">{{investor.investorName}}</a>
                            </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="website">
                            <ng-template kendoGridHeaderTemplate>
                                <div class="header-icon-wrapper wd-98">
                                    <span class="TextTruncate S-M">
                                        Website
                                    </span>
                                </div>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-investor>
                                <a *ngIf="investor.website!=null && investor.website!=''" class="click-view TextTruncate" title="{{investor.investorName}}"
                                    title="View Details" target="_blank"
                                    href="{{investor.website}}">{{investor.website}}</a>
                                <span *ngIf="investor.website == '' || investor.website == null">
                                    NA
                                </span>
                            </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="totalCommitment">
                            <ng-template kendoGridHeaderTemplate>
                                <div class="header-icon-wrapper wd-98">
                                    <span class="TextTruncate S-M">
                                        Total Commitment (in M)
                                    </span>
                                </div>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-investor>
                                <span class="float-right" title="{{investor.totalCommitment!='NA'?( (investor.totalCommitment/ 1000000).toFixed(2) | number : NumberDecimalConst.currencyDecimal): investor.totalCommitment}}">
                                    {{investor.totalCommitment!="NA"?( (investor.totalCommitment/ 1000000).toFixed(2) | number : NumberDecimalConst.currencyDecimal): investor.totalCommitment}}
                                </span>
                            </ng-template>
                        </kendo-grid-column>
                        <ng-template kendoGridNoRecordsTemplate>
                            <app-empty-state class="finacials-beta-empty-state" [imageHeight]="'76vh'" [isGraphImage]="false"></app-empty-state>
                        </ng-template>
                        <ng-template kendoGridLoadingTemplate>
                            <app-loader-component></app-loader-component>
                        </ng-template>
                    </kendo-grid>
                </div>
            </div>
        </div>
    </div>
</div>
