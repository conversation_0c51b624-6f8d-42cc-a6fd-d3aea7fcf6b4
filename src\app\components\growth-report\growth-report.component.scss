// Variables
@import "../../../assets/dist/css/font";

$border-color: #E6E6E6;
$border-radius: 4px;
$margin: 5px;
$header-height: 42px;
$header-bg-color: #FAFAFA;
$font-color-dark: #2B2B33;
$font-color-light: #50505C;
$padding-horizontal: 4rem;
$panel-bg-color: #FAFAFC;
$panel-border-color: #E1E1E8;
$panel-header-bg-color: #FFFFFF;
$company-section-active-bg: #EBF3FF;
$company-section-active-color: #4061C7;
$company-section-color: #666666;
$color-required: red;

// Mixins
@mixin content-section-styles {
    border-radius: $border-radius;
    border: 1px solid $border-color;
    border-top: none;
}

@mixin content-header-styles {
    height: $header-height;
    background: $header-bg-color;
    border: 1px solid $border-color;
    border-radius: $border-radius $border-radius 0 0;
    border-left: none;
    border-right: none;
}

// Styles
.growth-report-section {

    .left-section,
    .right-section {
        .content-section {
            @include content-section-styles;
            width: calc(100% - #{$margin});

            .content-header {
                @include content-header-styles;

                .btn-section {
                    padding: 4px 12px;

                    .apply-btn {
                        padding-right: 12px !important;

                        .k-font-icon {
                            font-size: 18px;
                        }
                    }
                }
            }

            .button-section {
                padding-right: 0.75rem;
            }

            .content-body-l-section,
            .content-body-r-section,
            .content-body-r-no-section {
                overflow-y: auto;
            }

            .content-body-l-section {
                height: calc(100vh - 192px);
            }

            .content-body-r-section {
                height: calc(100vh - 224px);
                overflow-y: auto !important;
            }

            .content-body-r-no-section {
                height: calc(100vh - 142px);
            }

            .no-data-container {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
            }

            .no-content-section {
                .no-content-text {
                    color: $font-color-dark;
                    @extend .Body-M;
                }

                .no-content-sub,
                .template-text {
                    color: $font-color-light;
                }

                .break-word {
                    word-break: break-word;
                    width: 100%;
                    padding: 0 $padding-horizontal;
                }
            }
        }
    }

    .left-section .content-section {
        margin-right: $margin;
    }

    .right-section .content-section {
        margin-left: $margin;
    }

    .panel-section {
        .panel-h {
            height: 40px;
        }

        .panel-h-auto {
            height: auto;
        }

        .g-panel-header {
            background: $panel-bg-color;
            border: 1px solid $panel-border-color;
            border-top: none;
            border-left: none;

            .header-block {
                padding: 10px 14px;

                .S-M {
                    color: $company-section-color;
                }
            }

            img {
                margin-top: -4px;
            }

            .header-sub-block {
                border: 1px solid $panel-border-color;
                border-bottom: none;
                border-left: none;
                border-right: none;
                background: $panel-header-bg-color !important;

                .company-section-active {
                    background: $company-section-active-bg;
                    color: $company-section-active-color;
                    cursor: pointer;
                    @extend .Body-R;
                    padding: 10px 16px;
                }

                .company-section {
                    color: $company-section-color;
                    @extend .Body-R;
                    cursor: pointer;
                    padding: 10px 16px;
                }
            }

            .panel-h-margin {
                margin-bottom: -6px;
            }
        }
    }
}

label {
    color: $company-section-color !important;
    padding-bottom: 0.25rem;
}

.req-label {
    padding-bottom: 0.25rem;

    &:after {
        content: "*";
        color: $color-required;
        padding-left: 4px;
    }
}

.row-header-section {
    .kpi-header {
        width: calc(100% - 100px);
        padding: 10px 16px;
        color: $company-section-color;
    }

    .icon-header {
        padding-right: 0.75rem;
        cursor: pointer;
    }

    .row-header-content {
        background: #FAFAFC;
        border-bottom: 1px solid #E1E1E8;
    }
}

.exist-label {
    color: #DE3139;
}

.column-kpi-section {
    .column-row-section {
        box-shadow: 0px 0px 8px 0px #0000001F;
        border-radius: 4px;
        .close-section {
            width: 48px;
            border-left: 1px solid #E1E1E8;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0px 6px;
        }

        .content-kpi-section {
            padding: 0.75rem 1rem;
            height: auto;
        }
    }
}
.fixed-bottom-g{
     position: absolute;
  bottom: 0;
  width: 100%;
}
.row-h-section{
    height: calc(100% - 64px);
    overflow-y: auto; 
}
.card-section{
    padding-right: 11px;
    padding-left: 11px;
}
.disclaimer-text{
    color:$company-section-active-color;
}
.delete-msg{
    color:#4D4D4D;
}
@media (max-width: 1300px) {
    .content-kpi-section{
        width: calc(100% - 100px) !important;
    }
    .kpi-type-section{
        padding-bottom: 16px;
    }
}
.r-section{
    .card-section{
        border-bottom:1px solid #E1E1E8;
    }
}
.column-row-section{
   margin-bottom: 12px !important;
}