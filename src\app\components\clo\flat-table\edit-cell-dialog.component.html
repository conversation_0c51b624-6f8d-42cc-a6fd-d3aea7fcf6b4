<confirm-modal
  *ngIf="isCancelPopup"
  customwidth="700px"
  modalTitle="Update Cell Value"
  primaryButtonName="Save"
  secondaryButtonName="Cancel"
  [disablePrimaryButton]="disableSaveButton"
  (primaryButtonEvent)="onSave()"
  (secondaryButtonEvent)="OnCancel()"  
>
  <div class="row ml-0 mr-0 pt-0">
    <div
      class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0 mb-3"
      *ngIf="!oldValue.isNavTbl"
    >
      <div class="Heading2-B kpi-name">{{ oldValue.firstColumnValue }}</div>
    </div>

    <div
      class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0 mb-8"
    >
      <div class="row mr-0 ml-0">
        <div
          class="col-6 col-lg-6 col-xl-6 col-sm-6 col-lg-6 col-md-6 pr-3 pl-0"
        >
          <div class="Body-B label-color clo-word-wrap" [attr.title]="oldValue.columnName">
            {{ oldValue.columnName }}
          </div>

          <div class="row mr-0 ml-0 cell-color mt-label-4">
            <div
              class="col-6 col-lg-6 col-xl-6 col-sm-6 col-lg-6 col-md-6 pr-0 pl-0"
            >
              <div class="Body-R cell-padding">{{ oldValue.columnValue }}</div>
            </div>
            <div
              class="col-6 col-lg-6 col-xl-6 col-sm-6 col-lg-6 col-md-6 pr-0 pl-0 d-flex align-items-center justify-content-center"
            >
              <div class="current-value Body-R">Current Value</div>
            </div>
          </div>
        </div>

        <div
          class="col-6 col-lg-6 col-xl-6 col-sm-6 col-lg-6 col-md-6 pr-0 pl-3"
        >
          <div class="Caption-M ptb-1 label-color">New Value</div>
          <input
            type="text"
            class="input-border form-control Body-R input-text-color"
            name="newValue"
            placeholder="Enter here"
            [(ngModel)]="newValue"
            (ngModelChange)="onValueChange()"
          />
        </div>
      </div>
    </div>

    <div
      class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0 mt-11 border-rad"
    >
      <div class="row mr-0 ml-0 text-center">
        <div
          class="col-6 col-lg-6 col-xl-6 col-sm-6 col-lg-6 col-md-6 pr-0 pl-0 div-1 Caption-M black-color"
        >
          Supporting Documents
          <img
            class="m-10 vector-icon"
            src="assets/dist/images/k-information.svg"
            alt="Information Icon"
          />
        </div>
        <div
          class="col-6 col-lg-6 col-xl-6 col-sm-6 col-lg-6 col-md-6 pr-0 pl-0 div-2"
        >
          <div class="mtb-10 black-color Caption-M">Comments</div>
        </div>
      </div>

      <div class="row mr-0 ml-0 text-center doc-bg">
        <div
          class="col-6 col-lg-6 col-xl-6 col-sm-6 col-lg-6 col-md-6 pr-0 pl-0 Caption-M m-auto" *ngIf="!isUploading"
        >
          <button class="btn btn-bg Body-R left-icon" 
          kendoButton
          (click)="fileInput.click()"
          themeColor="primary"
          [look]="'outline'"                    
          type="button">
            <input
            #fileInput
            type="file"            
            class="d-none"
            (change)="onBrowsed($event)" />
            <img
              class="ptb-8"
              src="assets/dist/images/Left-Icon.svg"
              alt="Information Icon"
            />Attach Documents
          </button>
        </div>

        <div
        *ngIf="isUploading"
        class="col-6 col-lg-6 col-xl-6 col-sm-6 col-lg-6 col-md-6 pr-0 pl-0 m-auto"
        [ngClass]="{'uploading': isUploading, 'invalid-file': invalidFile}"
      >
      <div class="d-flex align-items-center justify-content-between mlt-45 bg-white">
        <div class="mb-0 flex-grow-1 d-flex align-items-center">
          <img
            src="assets/dist/images/doc-icon.svg"
            alt="Excel file"
            class="mr-2 pl-2"
          />
          <div class="d-flex flex-column mr-2 text-left w-file-name">
            <div              
              class="text-nowrap clo-word-wrap Caption-M"
              [attr.title]="uploadedFiles[0].name"
            >
              {{ uploadedFiles[0].name.split(".")[0] }}
            </div>
            <div class="Caption-R">{{ fileSize }} KB</div>
          </div>
        </div>
        <div *ngIf="isUploading" class="w-50 ms-3 plr-25">
          <kendo-progressbar
            [animation]="{ duration: 10000 }"                      
            class="progress-bar-style"
            [label]="false"
          ></kendo-progressbar>
        </div>
        <img
          *ngIf="!invalidFile"
          id="clo-remove-upload-file"
          alt="remove-file"
          class="pr-2 mt-3 mb-3"
          src="assets/dist/images/k-close.svg"          
          (click)="removeFile()"
          (keypress)="removeFile()"
        />
        <div *ngIf="invalidFile" class="d-flex">
        <img              
          id="clo-remove-upload-file"
          alt="remove-file"
          src="assets/dist/images/clo-warning.svg"
          class="mr-1"
          (click)="removeFile()"
          (keypress)="removeFile()"
        />
        <span class="text-danger">Invalid File</span>
        </div>
      </div>
      </div>

        <div
          class="col-6 col-lg-6 col-xl-6 col-sm-6 col-lg-6 col-md-6 pr-0 pl-0 Caption-M ta-padding"
        >
          <textarea
            placeholder="Write your Comments"
            autocomplete="off"
            class="form-control Body-R textarea-border input-text-color ta-in-padding"
            rows="4"
            cols="50"
            maxlength="500"
            name="Comments"
            [(ngModel)]="comments">
          </textarea>
        </div>
      </div>
    </div>
  </div>
</confirm-modal>
