<form name="form" (ngSubmit)="f.form.valid"  #f="ngForm" class="panel-bar-form">
    <div class="drag-boundary" cdkScrollable cdkDropListAutoScroll>
        <div cdkDropList #toWatch="cdkDropList"
        [cdkDropListData]="subPageList" (cdkDropListDropped)="dropForPage($event)" *ngIf="subPageList?.length>0">
        <div cdkDragBoundary=".drag-boundary" cdkDragLockAxis="y" cdkDrag #elem="cdkDrag" class="section-container page-section row mr-0 ml-0 mt-3" *ngFor="let page of subPageList; index as i">
          <div class="col-12 pr-0 pl-0">
            <div class="row page-section tab-level-content mr-0 ml-0 section-header">
                  <div class="col-10 pr-0 pl-0">
                      <div class="row mr-0 ml-0">
                          <div class="subpage-name-col col-4 p-0">
                              <div class="dot-img" cdkDragHandle>
                                  <img src="assets/dist/images/RFP Icon.svg" alt="">
                              </div>
                              <div class="displayName-topCard Body-M">
                                  {{page.name}}
                              </div>
                          </div>
                          <div class="col-5">
                              <input type="text" class="form-control field-text tab-text eachlabel-padding default-txt Body-R alias-name"
                                  name="{{page.aliasName}}{{i}}" 
                                  value="{{page.aliasName}}"
                                  #{{page.aliasName}}="ngModel" 
                                  [(ngModel)]="page.aliasName" 
                                  required
                                  (keyup)="checkAnyDataChange()" 
                                  (input)="checkAnyDataChange()" 
                                  autocomplete="off" 
                                  maxlength="100"
                                  placeholder="Click here to enter tab name"  />
                          </div>
                      </div>
                  </div>
                  
                  <div class="col-2 pr-0">
                      <div [ngClass]="page.name == 'Commentaries' ?'float-left p-0' : 'col-0'">
                          <nep-button Type="Secondary" 
                          id="page-config-add-field" 
                          type="button" title="Add Field" 
                          class="Body-R width-120"                     
                          *ngIf="page.name == 'Commentaries'">
                          <!-- (click)="addCustomField(page);" -->
                <img src="assets/dist/images/Page_config_add.svg" class="cursor-filter" alt="" class="plus Body-R" />
                Add Field</nep-button>
                        </div>
                      
                      <div [ngClass]="page.name == 'Commentaries' ?'float-right commentry-padding' : 'float-right'">
                          <span class="pr-2 d-none" *ngIf="page.name=='Static Information'">
                              <img src="assets/dist/images/ConfigurationWhite.svg" class="cursor-filter" alt="" />
                          </span>
                          <span class="pl-2" id="page-config-expand-card">
                              <span class="expand-doc cursor-filter" (click)="onTabToggle(page);"><span class="">
                                      <i [ngClass]="page.isTabExpanded?'pi pi-chevron-up':'pi pi-chevron-down'"
                                          aria-hidden="true"></i>
                                          
                                  </span>
                              </span>
                          </span>
                          <!--  -->
                          <div>
                             
                      </div>
      
                      </div>
                  </div>
      
              </div>
              <div class="section-content pt-3 padding-left-44 pagesection" *ngIf="page.isTabExpanded && page?.subTabList?.length">
                <div *ngIf="page?.subTabList.length > 0" cdkDropList #toWatch="cdkDropList"
                    [cdkDropListData]="page?.subTabList" (cdkDropListDropped)="drop($event)">
                    <div class="section-container page-section row mr-3 ml-0 mt-3" #sectioncontent 
                        *ngFor="let subPage of page?.subTabList;index as j"
                        cdkDrag #elem="cdkDrag" cdkDragLockAxis="y" cdkDragBoundary=".pagesection" [cdkDragDisabled]="subPage.length == 1" >
                        <div class="col-12 pr-0 pl-0">
                            <div class="row tab-level-content mr-0 ml-0 section-header">
                                <div class="col-10 pr-0 pl-0">
                                    <div class="row mr-0 ml-0">
                                        <div class="subpage-name-col col-4 pt-2 pr-0 pl-0 pb-0" [ngClass]="subPage.length != 1?'':'pl-isdropdown-false'">
                                            <div class="dot-img" cdkDragHandle>
                                                <img src="assets/dist/images/RFP Icon.svg" alt="">
                                            </div>
                                            <div class="displayName-topCard Body-M">
                                                {{subPage.name}}
                                            </div>
                                        </div>
                                        <div class="col-5 pt-0 pb-0">
                                            <input type="text" class="form-control field-text tab-text eachlabel-padding default-txt Body-R alias-name"
                                                name="{{subPage.aliasName}}{{i}}" value="{{subPage.aliasName}}"
                                                #{{subPage.aliasName}}="ngModel" [(ngModel)]="subPage.aliasName" required
                                                (keyup)="checkAnyDataChange()" (input)="checkAnyDataChange()" autocomplete="off" maxlength="100"
                                                placeholder="Click here to enter sub tab name"  />
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-2 pt-2 pr-0">
                                    <div [ngClass]="subPage.name == 'Commentaries' ?'float-left' : 'col-0'">
                                        <nep-button Type="Secondary" 
                                        id="page-config-add-field" 
                                        type="button" title="Add Field" 
                                        class="Body-R width-120"                     
                                        *ngIf="subPage.name == 'Commentaries'">
                                        <!-- (click)="addCustomField(page);" -->
                              <img src="assets/dist/images/Page_config_add.svg" class="cursor-filter" alt="" class="plus Body-R" />
                              Add Field</nep-button>
                                      </div>
                                    
                                    <div class="float-right">
                                        <span class="pr-2 d-none" *ngIf="page.name=='Static Information'">
                                            <img src="assets/dist/images/ConfigurationWhite.svg" class="cursor-filter" alt="" />
                                        </span>
                                        <span class="pl-2" id="page-config-expand-card">
                                            <span class="expand-doc cursor-filter" (click)="onTabToggle1(subPage,page);"><span class="">
                                                    <i [ngClass]="subPage.isTabExpanded?'pi pi-chevron-up':'pi pi-chevron-down'"
                                                        aria-hidden="true"></i>
                                                        
                                                </span>
                                            </span>
                                        </span>
                                        <div>
                                           
                                    </div>
                    
                                    </div>
                                </div>
                    
                            </div>
                        </div>
                        <div class="section-content pt-20 padding-left-36 w-100 subpagesection" *ngIf="subPage.isTabExpanded && subPage?.tableList?.length">
                            <div *ngIf="subPage?.tableList.length > 0" cdkDropList #toWatch="cdkDropList"
                                [cdkDropListData]="subPage?.tableList" (cdkDropListDropped)="dropForSubTable($event,page,subPage)">
                                <div class="row page-section no-border pt-3 m-0" #sectioncontent       
                                    *ngFor="let table of subPage?.tableList;index as k"
                                    cdkDrag #elem="cdkDrag" cdkDragLockAxis="y"  cdkDragBoundary=".subpagesection"
                                    [cdkDragDisabled]="getDisable(subPage.tableList)" >
                                     <div class="row page-section tab-level-content p-2 mr-0 ml-0 w-100 table-header" *ngIf="table.isShow">
                                        <div class="col-12 pr-0 pl-0">
                                            <div class="row mr-0 ml-0">
                                                <div class="subpage-name-col col-4 pt-2 pr-0 pl-0 pb-0" [ngClass]="subPage.tableList.length > 1?'':'pl-isdropdown-false'">
                                                    <div class="dot-img" cdkDragHandle>
                                                        <img src="assets/dist/images/RFP Icon.svg" alt="">
                                                    </div>
                                                    <div class="displayName-topCard Body-M">
                                                        {{table.name}}
                
                                                    </div>
                                                </div>
                                                <div class="col-5 pt-0 pb-0">
                                                    <input type="text" 
                                                    class="form-control field-text eachlabel-padding default-txt Body-M alias-name"
                                                    name="{{table.aliasName}}{{k}}" value="{{table.aliasName}}"
                                                    #{{subPage.name}}="ngModel" 
                                                    [(ngModel)]="table.aliasName" 
                                                    (keyup)="checkAnyDataChange()" 
                                                    autocomplete="off" 
                                                    maxlength="100"
                                                    placeholder="Click here to enter table name"  />
                                                </div>
                          
                                            </div>
                                        </div>
                                    </div>
                                    
                                </div>
                            </div>
                        </div>
                        </div>
                </div>
               
            </div>
            <div class="section-content pt-20 padding-left-120 w-100 pagetablesection"  *ngIf="page.isTabExpanded && page?.tableList?.length">
                <div *ngIf="page?.tableList.length > 0" cdkDropList #toWatch="cdkDropList"
                    [cdkDropListData]="page?.tableList" (cdkDropListDropped)="dropForTable($event,page)">
                    <div class="row table-level no-border m-0"  #sectioncontent       
                        *ngFor="let table of page?.tableList;index as k"
                        [ngStyle]="{'padding': table.isShow ? '16px 20px 0' : '0'}"
                        cdkDrag #elem="cdkDrag" cdkDragLockAxis="y" cdkDragBoundary=".pagetablesection" [cdkDragDisabled]="page?.tableList?.length == 1 || table.tableId==21 || table.tableId==22" >
                         <div class="row table-level tab-level-content p-2 mr-0 ml-0 w-100 table-header" *ngIf="table.isShow">
                            <div class="col-12 pr-0 pl-0">
                                <div class="row mr-0 ml-0">
                                    <div class="subpage-name-col col-4 pt-2 pr-0 pl-0 pb-0" [ngClass]="!(page?.tableList?.length == 1 || table.tableId==21 || table.tableId==22)?'':'pl-isdropdown-false'">
                                        <div class="dot-img" cdkDragHandle>
                                            <img src="assets/dist/images/RFP Icon.svg" alt="">
                                        </div>
                                        <div class="displayName-topCard Body-M">
                                            {{table.name}}
    
                                        </div>
                                    </div>
                                    <div class="col-5 pt-0 pb-0">
                                        <input type="text" 
                                        class="form-control field-text eachlabel-padding default-txt Body-M alias-name"
                                        name="{{table.aliasName}}{{k}}" 
                                        value="{{table.aliasName}}"
                                        #{{subPage.name}}="ngModel" 
                                        [(ngModel)]="table.aliasName" 
                                        (keyup)="checkAnyDataChange()" 
                                        autocomplete="off"
                                         maxlength="100"
                                        placeholder="Click here to enter table name"  />
                                    </div>
                                   
                                </div>
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>            
          </div>
      </div>
      </div>
    </div>
    
    <div class="nodata-container" *ngIf="subPageList.length==0">
      <img [src]="clonodataPath" alt="No Data" />
  </div> 
  </form>