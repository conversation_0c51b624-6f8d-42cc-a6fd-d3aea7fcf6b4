<div class="align-items-start" [style.width]="fullViewWidth">
    <div class="ui-widget-header ui-wed-bb border-bottom">
        <div class="row mr-0 ml-0">
            <div class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                <div class="float-right">
                    <div class="d-inline-block search">
                        <span class="fa fa-search fasearchicon p-1"></span>
                        <input #gbFundIngestion (input)="searchGrid($event.target.value)" [(ngModel)]="globalFilter"
                            type="text" pInputText class="search-text-company companyListSearchHeight TextTruncate"
                            placeholder="Search">
                    </div>
                    <div class="d-inline">
                        <span class="col-divider">
                        </span>
                    </div>
                    <!-- <div class="d-inline-block pl-2 pref-icon">
                        <img id="dropdownMenuButton" [matMenuTriggerFor]="menu"
                            src="assets/dist/images/ConfigurationWhite.svg" class="cursor-filter" alt=""
                            #ingestionTrigger="matMenuTrigger" />
                    </div> -->
                </div>
            </div>
        </div>
    </div>
    <div class="datatable-container">
        <kendo-grid [kendoGridBinding]="fundIngestionData" scrollable="virtual" [rowHeight]="44"
            [resizable]="true" [sortable]="true" [sort]="sort" (sortChange)="sortChange($event)"
            class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cab-table-grid kendo-fund-tr-grid">
            <kendo-grid-column [sticky]="true" [width]="300" title="Period"
                *ngFor="let col of frozenFundIngestionTableColumns;">
                <ng-template kendoGridHeaderTemplate>
                    <span class="TextTruncate S-M">Period</span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-fundIngestion>
                    <a id="fund-ingestion-details" class="click-view" (click)="openFundIngestionDetailForQuarter(fundIngestion);globalFilterDetails = '';"
                        title="View Details">{{ fundIngestion.quarterAndYear }}</a>
                </ng-template>
            </kendo-grid-column>
            <kendo-grid-column title="{{col.displayName}}" *ngFor="let col of fundIngestionColumns;" [width]="200">
                <ng-template kendoGridHeaderTemplate>
                    <span class="TextTruncate S-M" title="{{col.displayName}}">{{col.displayName}}</span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-fundIngestion>
                    <div class="TextTruncate" 
                        [ngClass]="{'table-data-left': col.dataType === Mdatatypes.String, 'table-data-right': col.dataType !== Mdatatypes.String}">
                        <ng-container *ngIf="col.name !== 'Year' && col.name !== 'Quarter' && col.name !== 'Month'">
                            {{
                                col.dataType != Mdatatypes.Date ? (
                                    col.dataType == Mdatatypes.Multiple ? (getCellValue(fundIngestion, col) != 'NA' ? 
                                        (getCellValue(fundIngestion, col) | number : NumberDecimalConst.multipleDecimal) + "x" : "NA") :
                                    col.dataType == Mdatatypes.Percentage ? (getCellValue(fundIngestion, col) != 'NA' ? 
                                        (getCellValue(fundIngestion, col) | number : NumberDecimalConst.percentDecimal) + "%" : "NA") :
                                    col.dataType == Mdatatypes.CurrencyValue ? (getCellValue(fundIngestion, col) != 'NA' ? 
                                        (getCellValue(fundIngestion, col) | number : NumberDecimalConst.currencyDecimal) : "NA") :
                                    col.dataType == Mdatatypes.Number ? (getCellValue(fundIngestion, col) != 'NA' ? 
                                        (getCellValue(fundIngestion, col) | number : NumberDecimalConst.noDecimal) : "NA") :
                                    getCellValue(fundIngestion, col)
                                ) :
                                (getCellValue(fundIngestion, col) != 'NA') ? (getCellValue(fundIngestion, col) | date:'MM/dd/yyyy') : "NA"
                            }}
                        </ng-container>
                    </div>
                </ng-template>
            </kendo-grid-column>
            <ng-template kendoGridNoRecordsTemplate>
                <app-empty-state class="finacials-beta-empty-state"
                    [isGraphImage]="false"></app-empty-state>
            </ng-template>
        </kendo-grid>
    </div>
</div>

<!-- Fund Ingestion Details Dialog -->
<div *ngIf="displayFundIngestionDetailsDialog" class="nep-modal nep-modal-show custom-modal" style="display: block; background: rgba(0, 0, 0, 0.25);">
    <div class="nep-modal-mask"></div>
    <div [style.width]="fullViewWidth" class="nep-card nep-card-shadow nep-modal-panel nep-modal-default" style="position: relative; display: inline-flex; top: 30%;width: 80rem;">
        <div class="nep-card-header nep-modal-title">
            <div class="float-left TextTruncate">{{headerText}}</div>
            <div id="fund-ingestion-hide-modal" class="float-right" (click)="hideModal()">
                <div class="close-icon"><i class="pi pi-times"></i></div>
            </div>
        </div>
        <div class="nep-card-body">    
            <div class="model-custom-padding">
                <div class="card">
                    <div class="row mr-0 ml-0 header-tr-bg">
                        <div class="col-12 pl-3 pr-0">
                            <!-- <div class="float-left pt-2 pb-2">
                                <span class="tr-popup-currency">All values in:
                                    {{model?.currencyDetail?.currencyCode}} ({{fundIngestionValueUnit?.unitType}})</span>
                            </div> -->
                            <div class="float-right search">
                                <span class="fa fa-search"></span> 
                                <input (input)="searchDetailsGrid($event.target.value)" type="text" pInputText placeholder="Search"
                                    class="form-control search-box search-text-company companyListSearchHeight TextTruncate search-tr-popup mt-0"
                                    [(ngModel)]="globalFilterDetails">
                            </div>
                        </div>
                    </div>
                    <div class="card-body mb-0">
                        <div class="align-items-start">
                            <div class="ui-widget-header ui-track-bb ui-wed-bb border-bottom">
                            </div>
                            <kendo-grid [kendoGridBinding]="fundIngestionDetailsData" [sortable]="true" scrollable="virtual"
                                [rowHeight]="44" [resizable]="true"
                                class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cab-table-grid kendo-fund-tr-grid">
                                <kendo-grid-column [sticky]="true" [width]="300" field="fieldName" title="Field">
                                    <ng-template kendoGridHeaderTemplate>
                                        <span class="TextTruncate S-M">Field</span>
                                    </ng-template>
                                    <ng-template kendoGridCellTemplate let-details>
                                        {{details['fieldName']}}
                                    </ng-template>
                                </kendo-grid-column>
                                <kendo-grid-column [sticky]="true" [width]="300" field="value" title="Value">
                                    <ng-template kendoGridHeaderTemplate>
                                        <span class="TextTruncate S-M">Value</span>
                                    </ng-template>
                                    <ng-template kendoGridCellTemplate let-details>
                                        <div class="TextTruncate table-data-right">
                                            {{details['value'] != "NA" ? 
                                                (details['dataType'] === Mdatatypes.CurrencyValue ? 
                                                    (details['value'] | number: NumberDecimalConst.currencyDecimal) : 
                                                details['dataType'] === Mdatatypes.Percentage ? 
                                                    (details['value'] | number: NumberDecimalConst.percentDecimal) + "%" : 
                                                details['dataType'] === Mdatatypes.Multiple ? 
                                                    (details['value'] | number: NumberDecimalConst.multipleDecimal) + "x" : 
                                                details['dataType'] === Mdatatypes.Date ? 
                                                    (details['value'] | date: 'MM/dd/yyyy') : 
                                                details['dataType'] === Mdatatypes.Number ? 
                                                    (details['value'] | number: NumberDecimalConst.noDecimal) : 
                                                details['value']) : 
                                            "NA"}}
                                        </div>
                                    </ng-template>
                                </kendo-grid-column>
                                <ng-template kendoGridNoRecordsTemplate>
                                    <app-empty-state class="finacials-beta-empty-state"
                                        [isGraphImage]="false"></app-empty-state>
                                </ng-template>
                            </kendo-grid>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Unit Type Configuration Menu -->
<mat-menu #menu="matMenu" [hasBackdrop]="true" class="fixed-menu">
    <div id="fund-ingestion-value-unit" class="filter-first" (click)="$event.stopPropagation()" (keydown)="$event.stopPropagation()">
        <div class="row m-0">
            <div class="col-12 pb-1 pt-3 label-align">
                Values in:
            </div>
            <div id="fund-ingestion-value-change" class="col-12 pl-3 pr-3">
                    <kendo-combobox id="fund-ingestion-unit-change" [clearButton]="false" [(ngModel)]="fundIngestionValueUnit" #unit="ngModel" [fillMode]="'solid'"
                        name="Unit" class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                        [data]="unitTypeList" [filterable]="true" [valuePrimitive]="false" textField="unitType" placeholder="Select Unit"
                        (valueChange)="convertFundIngestionValueUnits()" valueField="typeId">
                    </kendo-combobox>
            </div>
        </div>
    </div>
</mat-menu>