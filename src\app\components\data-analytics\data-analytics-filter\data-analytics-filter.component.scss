 .data-analytics-filter {
     .p-multiselect-trigger {
         padding-top: 6px;
     }

     .label-content-padding {
         padding-top: 16px !important;
     }

     .data-analytics-dropdown {
         width: 100% !important;

         .p-dropdown {
             width: 100% !important;
             border-bottom: 1px solid #b7b7b7 !important;
             padding-left: 8px;
         }

         .p-dropdown-panel.p-component.ng-star-inserted {
             max-width: 440px !important;
         }
     }

     .p-dropdown-panel.p-component {
         width: 100% !important;
         max-width: 100% !important;
     }

     .all-filter-button {
         background: #FAFAFB !important;
         border: 1px solid #DEDFE0 !important;
         box-shadow: 0 3px 6px #00000014;
         color: #000000;
         width: 100% !important;
     }

     .p-filter-button {
         width: 100% !important;

         button {
             background: #FAFAFB !important;
             border: none !important;
             color: #000000;
             float: right;
         }
     }

     .data-analytics-panel {
         .p-overlaypanel-content {
             background-color: #ffffff !important;
         }
     }

     .p-overlaypanel .p-overlaypanel-content {
         background-color: #ffffff !important;
     }

     .filter-panel-footer {
         background: #FAFAFB !important;
     }

     .draft-action {
         padding: 12px !important;
     }

     .p-multiselect.p-multiselect-chip .p-multiselect-token .p-multiselect-token-icon {
         color: #FFFFFF !important;
     }

     .kpi-prefence-filter .p-dropdown .p-dropdown-trigger {
         padding-left: 0.5rem !important;
         padding-right: 0px !important;
     }

     .p-dropdown-items {
         padding: 8px !important;
     }
 }

 .period-type-control {
     width: 100% !important;
 }

 .data-analytics-panel .p-overlaypanel-content {
     background-color: #FFFFFF !important;
     padding: 0px !important;
 }

 .data-analytics-panel .p-overlaypanel-close {
     display: none !important;
 }

 .data-analytics-panel:after,
 .data-analytics-panel:before {
     content: none !important;
 }

 .data-analytics-panel-with-expand {
     top: 148px !important;
     width: 100px !important;
     border-top: none !important;
 }

 .data-analytics-panel-without-expand {
     top: 100px !important;
     left: 242px !important;
     width: calc(100% - 252px) !important;
     border-top: none !important;
 }

 .filter-panel-footer {
     background: #FAFAFA !important;
     padding: 8px !important;
     padding-right: 16px !important;
     border-top: 1px solid #dfdfdf !important;
 }

 .filter-panel-header {
     background: #FAFAFA !important;
     padding: 16px !important;
     padding-bottom: 0px !important;
     border-bottom: 1px solid #dfdfdf !important;
 }

 .data-analytics-dropdown .p-dropdown {
     width: 100% !important;
 }

 .data-analytics-dropdown {
     width: 100% !important;
 }

 .filter-content-padding {
     padding-top: 16px !important;
 }

 .p-multiselect.p-multiselect-chip .p-multiselect-token {
     background: #021155 !important;
 }

 .p-calendar .p-datepicker {
     min-width: 100% !important;
     top: 33px !important;
     border-top: none !important;
     border-top-left-radius: 0px !important;
     border-top-right-radius: 0px !important;
 }

 .data-analytics-calendar {
     .p-button {
         border-left: none !important;
         border-radius: 0px !important;
     }

     .date-picker-input {
         border-bottom-right-radius: 0px !important;
         border-right: none !important;
         padding-left: 0px !important;
     }

     .p-button.p-button-icon-only {
         height: 0px !important;
         padding: 1rem !important;
         padding-right: 0.5rem !important;
     }
 }

 .filter-icons {
     right: 0px;
     float: right;
     cursor: pointer !important;
     z-index: 9999 !important;
 }

 .p-overlaypanel {
     margin-top: 2px !important;
 }

 .p-dropdown-panel.p-component.ng-star-inserted {
     max-width: 100% !important;
     border-top: none !important;
 }

 .mat-menu-dropdown {
     width: 100% !important;
     min-width: 260px !important;
 }
 .custom-tabs-header {
     box-shadow: 0px 3px 6px #00000014;
     opacity: 1;
 }

 .filter-margin-top {
     margin-top: -8px !important;
 }

 .data-filter-dropdown {
     .p-dropdown {
         width: 100% !important;
         border-bottom: 1px solid #DEDFE0;
         padding-top: 4px;
         font-size: 0.75rem;
         padding-left: 14px !important;
     }
 }

 .data-presetfilter-dropdown .p-dropdown .p-dropdown-label {
     padding-top: 5px !important;
     padding-left: 8px;
 }

 .custom-multiselect-texttruncate {
     .p-overlay {
         width: 100% !important;
     }
 }

 .investor-sections-dropdown .p-dropdown-label {
     padding-left: 0.6rem !important;
 }

 .static-padding-left-label {
     padding-left: 0.6rem !important;
 }

 .investor-sections-dropdown .p-dropdown .p-dropdown-trigger {
     padding: 6px !important;
 }

 .analytics-period-dropdown .p-button.p-button-icon-only {
     padding-bottom: 12px !important;
 }

.Caption-M{
    color: #666666;
}
.parent-child-graph{
    border: 1px solid #666666;
}
.parent-child-graph-container{
    padding-top: 1.7rem;
}
.parent-child-graph-title{
    padding-left: 0.4rem;
    position: relative;
    top: 0.2rem;
}
p.pc-warning-text {
    margin: 0rem 1.30rem 0.75rem;
    font-style: italic;
    font-weight: 400;
}
img.info-icon{
    margin: 5px 0px 0px 5px;
}
.disabled-chk {
    cursor: not-allowed;
    opacity: 0.5;
}
.k-treeview > ul > li > .k-treeview-top .k-checkbox-wrap,
.k-treeview > ul > li > .k-treeview-mid .k-checkbox-wrap,
.k-treeview > ul > li > .k-treeview-bot .k-checkbox-wrap {
    display: none !important;
}
.k-treeview > ul > li > .k-treeview-top .k-treeview-leaf,
.k-treeview > ul > li > .k-treeview-mid .k-treeview-leaf,
.k-treeview > ul > li > .k-treeview-bot .k-treeview-leaf {
    pointer-events: none;
}