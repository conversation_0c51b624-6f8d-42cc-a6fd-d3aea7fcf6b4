﻿<div class="row mr-0 ml-0 mb-4 custom-calendar-width" id="fund-resizeable">
    <div class="col-lg-12 col-md-12 col-sm-12 pr-0 pl-0 col-12 col-xl-12  fund-header pr-0 pl-0 pb-2 TextTruncate"
    *ngIf="subPageDetailList | arrayFilter: isFieldActive :fundPageSectionConstants.StaticInformation" title="{{subPageDetailList | arrayFilter: getStaticFieldDisplayName :fundPageSectionConstants.StaticInformation}}">
        {{subPageDetailList | arrayFilter: getStaticFieldDisplayName :fundPageSectionConstants.StaticInformation}}
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12 pr-0 pl-0 col-12 col-xl-12 pr-0 pl-0 fund-static">
        <div class="card-body">
            <form name="form" class="mt-0 pt-0 pb-0 pl-0 pr-0" (ngSubmit)="f.form.valid && save(f)" #f="ngForm"
                novalidate *ngIf="masterModel!=undefined">
                <div class="row mr-0 ml-0">
                    <div class="col-lg-12 col-md-12 col-sm-12  col-12 col-xl-12 investorcontainer pb-0">
                        <div class="row mr-0 ml-0">
                            <ng-container *ngFor="let staticField of fundStaticInfoFieldList">
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="staticField.name == fundStaticDetailConstants.FundName">
                                    <div class="form-group" [ngClass]="{ 'has-error': f.submitted && !fundName.valid }">
                                        <div class="row mr-0 ml-0">
                                            <div class="col-12 pr-0 pl-0">
                                                <label for="fundName"
                                                    class="Caption-M TextTruncate mandatory-label" title="{{staticField.displayName}}">{{staticField.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <input [placeholder]="'Enter '+ staticField.displayName"
                                                    autocomplete="off" type="text" class="form-control TextTruncate" name="fundName"
                                                    maxlength="200" [(ngModel)]="model.fundName" #fundName="ngModel"
                                                    validateRequired validateBusinessName required="true" />
                                                <div *ngIf="f.submitted && model.fundName ==undefined"
                                                    class="text-danger">
                                                    <p *ngIf="f.submitted && model.fundName ==undefined">
                                                        Please enter valid fund name</p>
                                                    <p *ngIf="fundName.errors.validateRequired">Fund name is required
                                                    </p>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="staticField.name == fundStaticDetailConstants.FirmName">
                                    <div class="form-group" [ngClass]="{ 'has-error': f.submitted && !firm.valid }">
                                        <div class="row mr-0 ml-0">
                                            <div class="col-12 pr-0 pl-0">
                                                <label for="firm" class="Caption-M TextTruncate" title="{{staticField.displayName}}">{{staticField.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                                                [(ngModel)]="model.firmDetail" #firm="ngModel" [fillMode]="'flat'"
                                                                [filterable]="true" name="firm" [virtual]="virtual"
                                                                class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32"
                                                                [size]="'medium'" [data]="masterModel.firmList" [filterable]="true" textField="firmName"
                                                                valueField="firmID" [placeholder]="'Select '+ staticField.displayName">
                                                            </kendo-combobox>
                                                <div *ngIf="(f.submitted || firm.dirty) && !firm.valid"
                                                    class="text-danger">Firm is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="staticField.name == fundStaticDetailConstants.AccountType">
                                    <div class="form-group"
                                        [ngClass]="{ 'has-error': f.submitted && !accountType.valid }">
                                        <div class="row mr-0 ml-0">
                                            <div class="col-12 pr-0 pl-0">
                                                <label class="Caption-M" for="accountType TextTruncate" title="{{staticField.displayName}}">{{staticField.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <kendo-combobox [clearButton]="false" [(ngModel)]="model.accountTypeDetail" #accountType="ngModel" [fillMode]="'flat'"
                                                    name="accountType" class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32"
                                                    [size]="'medium'" [data]="masterModel.accountTypeList" [filterable]="true" textField="accountType" valueField="accountTypeID"
                                                    [placeholder]="'Select '+ staticField.displayName">
                                                </kendo-combobox>
                                                <div *ngIf="(f.submitted || accountType.dirty) && !accountType.valid"
                                                    class="text-danger">Account Type is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="staticField.name == fundStaticDetailConstants.Sector">
                                    <div class="form-group" [ngClass]="{ 'has-error': f.submitted && !sector.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label class="Caption-M" for="sector TextTruncate" title="{{staticField.displayName}}">{{staticField.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <kendo-combobox [virtual]="virtual" [kendoDropDownFilter]="filterSettings" [filterable]="true" [clearButton]="false"
                                                    [(ngModel)]="model.sectorDetail" #sector="ngModel" [fillMode]="'flat'" name="sector"
                                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32" [size]="'medium'"
                                                    [data]="masterModel.sectorList" [filterable]="true" textField="sector" valueField="sectorID"
                                                    [placeholder]="'Select '+ staticField.displayName">
                                                </kendo-combobox>
                                                <div *ngIf="(f.submitted || sector.dirty) && !sector.valid"
                                                    class="text-danger">Sector is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="staticField.name == fundStaticDetailConstants.Strategy">
                                    <div class="form-group" [ngClass]="{ 'has-error': f.submitted && !strategy.valid }">
                                        <div class="row mr-0 ml-0">
                                            <div class="col-12 pr-0 pl-0">
                                                <label class="Caption-M" for="strategy TextTruncate" title="{{staticField.displayName}}">{{staticField.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                    <kendo-combobox [clearButton]="false" [(ngModel)]="model.strategyDetail" #strategy="ngModel" [fillMode]="'flat'"
                                                        name="strategy" class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32"
                                                        [size]="'medium'" [data]="masterModel.strategyList" [filterable]="true" textField="strategy" valueField="strategyID"
                                                        [placeholder]="'Select '+ staticField.displayName">
                                                    </kendo-combobox>
                                                <div *ngIf="(f.submitted || strategy.dirty) && !strategy.valid"
                                                    class="text-danger">{{staticField.displayName}} is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="staticField.name == fundStaticDetailConstants.Currency">
                                    <div class="form-group" [ngClass]="{ 'has-error': f.submitted && !currency.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label for="currency"
                                                    class="Caption-M TextTruncate mandatory-label" title="{{staticField.displayName}}">{{staticField.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <kendo-combobox [required]="true" [virtual]="virtual" [kendoDropDownFilter]="filterSettings" [filterable]="true"
                                                    [clearButton]="false" [(ngModel)]="model.currencyDetail" #currency="ngModel" [fillMode]="'flat'" name="currency"
                                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32" [size]="'medium'"
                                                    [data]="masterModel.currencyList" [filterable]="true" textField="currency" valueField="currencyID"
                                                    [placeholder]="'Select '+ staticField.displayName">
                                                </kendo-combobox>
                                                <div *ngIf="(f.submitted || currency.dirty) && !currency.valid"
                                                    class="text-danger">{{staticField.displayName}} is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="staticField.name == fundStaticDetailConstants.VintageYear">
                                    <div class="form-group"
                                        [ngClass]="{ 'has-error': f.submitted && !vintageYear.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label for="vintageYear" title="{{staticField.displayName}}" class="Caption-M TextTruncate">{{staticField.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">                                           
                                                <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="model.vintageYear"
                                                    #vintageYear="ngModel" [fillMode]="'flat'" [filterable]="true" name="vintageYear" [virtual]="virtual"
                                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32" [size]="'medium'"
                                                    [data]="yearOptions" [valuePrimitive]="true" [filterable]="true" textField="text" valueField="value"
                                                    [placeholder]="'Select '+ staticField.displayName">
                                                </kendo-combobox>
                                                <div *ngIf="(f.submitted || vintageYear.dirty) && !vintageYear.valid"
                                                    class="text-danger">{{staticField.displayName}} is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="staticField.name == fundStaticDetailConstants.FundSize">
                                    <div class="form-group" [ngClass]="{ 'has-error': f.submitted && !fundSize.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label  for="fundSize" class="TextTruncate Caption-M" title="{{staticField.displayName}}">{{staticField.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <input [placeholder]="'Enter '+ staticField.displayName"
                                                    autocomplete="off" numberOnly maxlength="15" type="text"
                                                    class="form-control TextTruncate" name="fundSize" [(ngModel)]="model.fundSize"
                                                    #fundSize="ngModel" validateRequired />
                                                <div *ngIf="(f.submitted || fundSize.dirty) && !fundSize.valid"
                                                    class="text-danger">{{staticField.displayName}} is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="staticField.name ==fundStaticDetailConstants.CustomField">
                                    <ng-container *ngFor="let customfield of customfieldValueList">
                                        <div class="form-group" [ngClass]="{ 'has-error': f.submitted}"
                                        *ngIf="customfield.fieldID == staticField.fieldID">
                                           
                                            <div class="Caption-M TextTruncate"
                                            [ngClass]="customfield.isMandatory ?  'required-field' : ''" for="Customfield"
                                            title="{{customfield.displayName}}">
                                            {{customfield.displayName}} </div>
                                        <input placeholder="Enter {{customfield.displayName}}"
                                            *ngIf="(customfield.dataTypeId==mDataTypes.Number)&&(customfield.dataTypeId!=mDataTypes.FreeText)"
                                            autocomplete="off" type="text"
                                            class="form-control eachlabel-padding default-txt TextTruncate"
                                            [(ngModel)]="customfield.value == 'NA' ? '' : customfield.value"
                                            
                                            id="{{customfield.displayName}}" name="{{customfield.displayName}}"
                                            (input)="numbersOnlyValidator($event)"
                                            (blur)="AddOrUpdateCustomFieldValue($event,customfield)" />
                                        <input placeholder="Enter {{customfield.displayName}}"
                                            *ngIf="(customfield.dataTypeId==mDataTypes.CurrencyValue||customfield.dataTypeId==mDataTypes.Percentage||staticField.dataTypeId==mDataTypes.Multiple)&&(staticField.dataTypeId!=mDataTypes.FreeText)"
                                            autocomplete="off" type="text"
                                            class="form-control eachlabel-padding default-txt TextTruncate"
                                            [(ngModel)]="customfield.value == 'NA' ? '' : customfield.value"
                                            id="{{customfield.displayName}}" name="{{customfield.displayName}}"
                                            (input)="decimalNumbersOnlyValidator($event)" appTwoDigitDecimaNumber
                                            (blur)="AddOrUpdateCustomFieldValue($event,customfield)" />
                                      
                                        <kendo-datepicker id="fund-datepicker" calendarType="classic" class="k-picker-custom-flat k-datepicker-height-35"  [format]="format" [fillMode]="'flat'"
                                            placeholder="Enter {{customfield.displayName}}"
                                            *ngIf="customfield.dataTypeId===mDataTypes.Date&&(customfield.dataTypeId!==mDataTypes.FreeText)"
                                            id="{{customfield.displayName}}" name="{{customfield.displayName}}"
                                            [(ngModel)]="customfield.value == 'NA' ? '' : customfield.value" [value]="getFormattedDate(customfield.value)"
                                            (valueChange)="onChangeDate($event,customfield)" #{{customfield.name}}></kendo-datepicker>
                      
                                        <input id="fund-datepicker-from" *ngIf="customfield.dataTypeId===mDataTypes.FreeText" type="text"
                                            class="form-control eachlabel-padding default-txt TextTruncate"
                                            name="Customfield" [value]="customfield.value == 'NA' ? '' : customfield.value"
                                            (blur)="AddOrUpdateCustomFieldValue($event,customfield)"
                                            [placeholder]="'Enter '+customfield.displayName" autocomplete="off"
                                            maxlength="100" />
                                        <input id="fund-datepicker-to" *ngIf="customfield.dataTypeId===0" type="text"
                                            class="form-control eachlabel-padding default-txt TextTruncate"
                                            name="Customfield" [value]="customfield.value == 'NA' ? '' : customfield.value"
                                            (blur)="AddOrUpdateCustomFieldValue($event,customfield)"
                                            [placeholder]="'Enter '+customfield.displayName" autocomplete="off"
                                            maxlength="100" />
                                        </div>                                        
                                    </ng-container>
                                    
                                </div>
                            </ng-container>
                        </div>
                    </div>
                </div>
                <div class="row mr-0 ml-0">
                    <div class="col-sm-12 pr-0 pl-0" *ngIf="fundStrategyDesc.isActive">
                        <div class="form-group">
                            <div class="row">
                                <div class="col-12">
                                    <div class="fund-header pb-2 pt-4 TextTruncate" title="{{fundStrategyDesc.displayName}}" for="strategyDescription">
                                        {{fundStrategyDesc.displayName}}</div>
                                </div>
                                <div class="col-12">
                                    <textarea [placeholder]="'Enter ' +fundStrategyDesc.displayName +' here..'"
                                        autocomplete="off" class="form-control text-desc" rows="4" cols="50"
                                        maxlength="500" name="strategyDescription"
                                        [(ngModel)]="model.strategyDescription"
                                        #strategyDescription="ngModel"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mr-0 ml-0 pt-4" *ngIf="subPageDetailList | arrayFilter: isFieldActive :fundPageSectionConstants.FundTerm">
                    <div class="col-lg-12 col-md-12 col-sm-12 pr-0 pl-0 col-12 col-xl-12  fund-header pr-0 pl-0 pb-2 TextTruncate" title="{{subPageDetailList | arrayFilter: getStaticFieldDisplayName
                        :fundPageSectionConstants.FundTerm}}">
                        {{subPageDetailList | arrayFilter: getStaticFieldDisplayName
                        :fundPageSectionConstants.FundTerm}}
                    </div>
                    <div
                        class="col-lg-12 col-md-12 col-sm-12  col-12 col-xl-12   investorcontainer pb-0">
                        <div class="row mr-0 ml-0">
                            <ng-container *ngFor="let field of fundTermFieldList">
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name == fundTermFieldConstants.TargetCommitment">
                                    <div class="form-group"
                                        [ngClass]="{ 'has-error': f.submitted && !targetCommitment.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label class="Caption-M" for="targetCommitment TextTruncate" title="{{field.displayName}}">{{field.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <input [placeholder]="'Enter ' + field.displayName" autocomplete="off"
                                                    numberOnly type="text" maxlength="15" class="form-control TextTruncate"
                                                    name="targetCommitment" [(ngModel)]="model.targetCommitment"
                                                    #targetCommitment="ngModel" validateRequired />
                                                <div *ngIf="(f.submitted || targetCommitment.dirty) && !targetCommitment.valid"
                                                    class="text-danger">Target commitment is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name == fundTermFieldConstants.MaximumCommitment">
                                    <div class="form-group"
                                        [ngClass]="{ 'has-error': f.submitted && !maximumCommitment.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label class="Caption-M" for="maximumCommitment TextTruncate" title="{{field.displayName}}">{{field.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <input [placeholder]="'Enter ' + field.displayName" autocomplete="off"
                                                    numberOnly maxlength="15" type="text" class="form-control TextTruncate"
                                                    name="maximumCommitment" [(ngModel)]="model.maximumCommitment"
                                                    #maximumCommitment="ngModel" validateRequired />
                                                <div *ngIf="(f.submitted || maximumCommitment.dirty) && !maximumCommitment.valid"
                                                    class="text-danger">Maximum commitment is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name == fundTermFieldConstants.GPCommitment">
                                    <div class="form-group"
                                        [ngClass]="{ 'has-error': f.submitted && !gPCommitment.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label class="Caption-M" for="gPCommitment TextTruncate" title="{{field.displayName}}">{{field.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <input [placeholder]="'Enter ' + field.displayName" autocomplete="off"
                                                    numberOnly maxlength="6" type="text" class="form-control TextTruncate"
                                                    name="gPCommitment" [(ngModel)]="model.gpCommitment"
                                                    #gPCommitment="ngModel" validateRequired />
                                                <div *ngIf="(f.submitted || gPCommitment.dirty) && !gPCommitment.valid"
                                                    class="text-danger">GP commitment percent is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name == fundTermFieldConstants.PreferredReturnPercent">

                                    <div class="form-group"
                                        [ngClass]="{ 'has-error': f.submitted && !preferredReturnPercent.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label class="Caption-M" for="preferredReturnPercent TextTruncate" title="{{field.displayName}}">{{field.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <input [placeholder]="'Enter ' + field.displayName" autocomplete="off"
                                                    numberOnly maxlength="6" type="text" class="form-control TextTruncate"
                                                    name="preferredReturnPercent"
                                                    [(ngModel)]="model.preferredReturnPercent"
                                                    #preferredReturnPercent="ngModel" validateRequired />
                                                <div *ngIf="(f.submitted || preferredReturnPercent.dirty) && !preferredReturnPercent.valid"
                                                    class="text-danger">Preferred return percent is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                               <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name == fundTermFieldConstants.CarriedInterestPercent">
                                    <div class="form-group"
                                        [ngClass]="{ 'has-error': f.submitted && !carriedInterestPercent.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label class="Caption-M" for="carriedInterestPercent TextTruncate" title="{{field.displayName}}">{{field.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <input [placeholder]="'Enter ' + field.displayName" autocomplete="off"
                                                    numberOnly maxlength="6" type="text" class="form-control TextTruncate"
                                                    name="carriedInterestPercent"
                                                    [(ngModel)]="model.carriedInterestPercent"
                                                    #carriedInterestPercent="ngModel" validateRequired />
                                                <div *ngIf="(f.submitted || carriedInterestPercent.dirty) && !carriedInterestPercent.valid"
                                                    class="text-danger">Carried interest percent is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name == fundTermFieldConstants.GPCatchupPercent">
                                    <div class="form-group"
                                        [ngClass]="{ 'has-error': f.submitted && !gPCatchupPercent.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label  for="gPCatchupPercent" class="TextTruncate Caption-M" title="{{field.displayName}}">{{field.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <input [placeholder]="'Enter ' + field.displayName" autocomplete="off"
                                                    numberOnly maxlength="6" type="text" class="form-control TextTruncate"
                                                    name="gPCatchupPercent" [(ngModel)]="model.gpCatchupPercent"
                                                    #gPCatchupPercent="ngModel" validateRequired />
                                                <div *ngIf="(f.submitted || gPCatchupPercent.dirty) && !gPCatchupPercent.valid"
                                                    class="text-danger">GP catchup percent is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                 
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name == fundTermFieldConstants.ManagementFee">
                                    <div class="form-group"
                                        [ngClass]="{ 'has-error': f.submitted && !managementFee.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label  for="managementFee" class="TextTruncate Caption-M" title="{{field.displayName}}">{{field.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <input [placeholder]="'Enter ' + field.displayName" autocomplete="off"
                                                    numberOnly maxlength="10" type="text" class="form-control TextTruncate"
                                                    name="managementFee" [(ngModel)]="model.managementFee"
                                                    #managementFee="ngModel" validateRequired />
                                                <div *ngIf="(f.submitted || managementFee.dirty) && !managementFee.valid"
                                                    class="text-danger">Management fee is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                               <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name == fundTermFieldConstants.ManagementFeeOffset">
                                    <div class="form-group"
                                        [ngClass]="{ 'has-error': f.submitted && !managementFeeOffset.valid }">
                                        <div class="row mr-0 ml-0">
                                            <div class="col-12 pr-0 pl-0">
                                                <label  for="managementFeeOffset" title="{{field.displayName}}" class="TextTruncate Caption-M">{{field.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <input [placeholder]="'Enter ' + field.displayName" autocomplete="off"
                                                    numberOnly maxlength="10" type="text" class="form-control TextTruncate"
                                                    name="managementFeeOffset" [(ngModel)]="model.managementFeeOffset"
                                                    #managementFeeOffset="ngModel" validateRequired />
                                                <div *ngIf="(f.submitted || managementFeeOffset.dirty) && !managementFeeOffset.valid"
                                                    class="text-danger">Management fee offset is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                  <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name == fundTermFieldConstants.FundTerm">

                                    <div class="form-group" [ngClass]="{ 'has-error': f.submitted && !fundTerm.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label  for="fundTerm" title="{{field.displayName}}" class="TextTruncate Caption-M">{{field.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <input [placeholder]="'Enter ' + field.displayName" autocomplete="off"
                                                    numberOnly maxlength="15" type="text" class="form-control TextTruncate"
                                                    name="fundTerm" [(ngModel)]="model.fundTerm" #fundTerm="ngModel"
                                                    validateRequired />
                                                <div *ngIf="(f.submitted || fundTerm.dirty) && !fundTerm.valid"
                                                    class="text-danger">Fund term is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name == fundTermFieldConstants.MaximumExtensionToFundTerm">
                                    <div class="form-group"
                                        [ngClass]="{ 'has-error': f.submitted && !maximumExtensionToFundTerm.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label for="maximumExtensionToFundTerm" title="{{field.displayName}}" class="TextTruncate Caption-M">{{field.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <input [placeholder]="'Enter ' + field.displayName" autocomplete="off"
                                                    numberOnly maxlength="15" type="text" class="form-control TextTruncate"
                                                    name="maximumExtensionToFundTerm"
                                                    [(ngModel)]="model.maximumExtensionToFundTerm"
                                                    #maximumExtensionToFundTerm="ngModel" validateRequired />
                                                <div *ngIf="(f.submitted || maximumExtensionToFundTerm.dirty) && !maximumExtensionToFundTerm.valid"
                                                    class="text-danger">Maximum extension to fund term is required
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name == fundTermFieldConstants.FundClosingDate">
                                    <div class="form-group"
                                        [ngClass]="{ 'has-error': f.submitted && !fundClosingDate.valid }">
                                        <div class="row mr-0 ml-0">
                                            <div class="col-12 pr-0 pl-0">
                                                <label for="fundClosingDate" title="{{field.displayName}}" class="TextTruncate Caption-M">{{field.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <div class="input-group">
                                                        <kendo-datepicker calendarType="classic" #fundClosingDate class="k-picker-custom-flat k-datepicker-height-32"
                                                            [format]="format" [fillMode]="'flat'" [placeholder]="'Select ' +field.displayName"  name="fundClosingDate"
                                                            [(ngModel)]="model.fundClosingDate" [value]="getFormattedDate(model.fundClosingDate)"></kendo-datepicker>
                                                </div>
                                                <div *ngIf="(f.submitted || fundClosingDate.dirty) && !f.control.controls.fundClosingDate.valid"
                                                    class="text-danger">Fund closing date is required</div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name == fundTermFieldConstants.OrgExpenses">
                                    <div class="form-group"
                                        [ngClass]="{ 'has-error': f.submitted && !orgExpenses.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label for="orgExpenses" title="{{field.displayName}}" class="TextTruncate Caption-M">{{field.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                <input [placeholder]="'Enter ' + field.displayName" autocomplete="off"
                                                    maxlength="15" numberOnly type="text" class="form-control TextTruncate"
                                                    name="orgExpenses" [(ngModel)]="model.orgExpenses"
                                                    #orgExpenses="ngModel" validateRequired />
                                                <div *ngIf="(f.submitted || orgExpenses.dirty) && !orgExpenses.valid"
                                                    class="text-danger">Org expenses is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name == fundTermFieldConstants.Clawback">
                                    <div class="form-group" [ngClass]="{ 'has-error': f.submitted && !clawback.valid }">
                                        <div class="row mr-0 ml-0">

                                            <div class="col-12 pr-0 pl-0">
                                                <label for="clawback" title="{{field.displayName}}" class="TextTruncate Caption-M">{{field.displayName}}</label>
                                            </div>
                                            <div class="col-12 pr-0 pl-0">
                                                    <kendo-combobox [clearButton]="false" [(ngModel)]="model.clawback" #clawback="ngModel" [fillMode]="'flat'"
                                                        [filterable]="true" name="clawback"
                                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32" [size]="'medium'"
                                                        [data]="clawbackOptions" [valuePrimitive]="true" [filterable]="false" textField="text" valueField="value"
                                                        [placeholder]="'Select '+ field.displayName">
                                                    </kendo-combobox>
                                                <div *ngIf="(f.submitted || clawback.dirty)&& !clawback.valid"
                                                    class="text-danger">Clawback is required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0"
                                    *ngIf="field.name ==fundStaticDetailConstants.CustomField">
                                    <ng-container *ngFor="let customfield of customfieldValueList">
                                        <div class="form-group" [ngClass]="{ 'has-error': f.submitted}"
                                        *ngIf="field.fieldID == customfield.fieldID">
                                            <div class="row mr-0 ml-0">
                                                <div class="col-12 pr-0 pl-0">
                                                    <label for="Customfield" title="{{field.displayName}}" class="TextTruncate Caption-M">{{customfield.displayName}}</label>
                                                </div>
                                                <div class="col-12 pr-0 pl-0">
                                                    <input id="add-fund-name" type="text" class="form-control TextTruncate"
                                                        name="Customfield" 
                                                        [value]="customfield.value == 'NA' ? '' : customfield.value"
                                                        (blur)="AddOrUpdateCustomFieldValue($event,customfield)"
                                                        [placeholder]="'Enter '+field.displayName" autocomplete="off"
                                                        maxlength="100" />
                                                </div>
                                            </div>
                                        </div>

                                    </ng-container>
                                    
                                  
                                    
                                </div>
                            <!-- -->

                            </ng-container>
                        </div>
                    </div>
                </div>
                <div class="row mr-0 ml-0 header-performance mb-4">
                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 trackrecord-section">
                        <div class="financial-page">
                            <div class="panel panel-default border-0 pt-2 tab-bg">
                                <div class="panel-heading">
                                    <div class="panel-title custom-tabs">
                                        <ul class="nav nav-tabs ">
                                            <li id="add-funds-select-menu" class="nav-item" role="presentation" *ngFor="let tab of tabList;"
                                                (click)="selectTab(tab);">
                                                <button class="nav-link nav-custom TextTruncate" [ngClass]="tab.isSelected == true ?'active':''"
                                                    id="home-tab" data-bs-toggle="tab" type="button" role="tab"
                                                    aria-controls="home" title="{{tab.displayName}}" aria-selected="true">
                                                    {{tab.displayName}}
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="content-bg">
                                <div *ngIf="tabName =='Investors'">
                                    <form  name="investForm" #investForm="ngForm" class="mb-0 border-bottom">
                                        <div class="row mr-0 ml-0 pt-3 pr-3 pl-3 pb-0">
                                            <ng-container *ngFor="let staticData of dynamicHeader">
                                                <div *ngIf="FundInvestorConstants.InvestorId==staticData.name"
                                                    class="col-sm-3 col-md-3 col-3 col-lg-3 col-xl-3 col-sm-3 pr-control-fund pl-0">
                                                    <div class="form-group">
                                                        <div class="row mr-0 ml-0">
                                                            <div class="col-12 pr-0 pl-0">
                                                                <label for="InvestorName"
                                                                    class="Caption-M TextTruncate" title="{{staticData.displayName}}">{{staticData.displayName}}</label>
                                                            </div>
                                                            <div class="col-12 pr-0 pl-0">
                                                                <kendo-combobox [virtual]="virtual" [required]="true" [kendoDropDownFilter]="filterSettings" [filterable]="true" [clearButton]="false"
                                                                [(ngModel)]="fundInvestor.investor" #investorName="ngModel" [fillMode]="'flat'" name="investorName"
                                                                class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32" [size]="'medium'"
                                                                [data]="investorNameList" [filterable]="true" textField="investorName" valueField="investorId"
                                                                [placeholder]="'Select '+ staticData.displayName">
                                                            </kendo-combobox>
                                                                <div *ngIf="(investorName.dirty) && !investorName.valid"
                                                                    class="text-danger">Investor Name is required</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div *ngIf="FundInvestorConstants.Commitment==staticData.name"
                                                    class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0">
                                                    <div class="form-group">
                                                        <div class="row mr-0 ml-0">

                                                            <div class="col-12 pr-0 pl-0">
                                                                <label for="fundSize" class="Caption-M TextTruncate" title="{{staticData.displayName}}">{{staticData.displayName}}</label>
                                                            </div>
                                                            <div class="col-12 pr-0 pl-0">
                                                                <input placeholder="Enter {{staticData.displayName}}"
                                                                    autocomplete="off" type="number"
                                                                    class="form-control TextTruncate" name="Commitment"
                                                                    [(ngModel)]="fundInvestor.Commitment"
                                                                    #commitment="ngModel" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div *ngIf="FundInvestorConstants.CommitmentDate==staticData.name"
                                                    class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0">
                                                    <div class="form-group">
                                                        <div class="row mr-0 ml-0">

                                                            <div class="col-12 pr-0 pl-0">
                                                                <label for="fundSize" title="{{staticData.displayName}}" class="TextTruncate Caption-M">{{staticData.displayName}}</label>
                                                            </div>
                                                            <div class="col-12 pr-0 pl-0">
                                                                <div class="input-group">
                                                                        <kendo-datepicker calendarType="classic" #fundClosingDate class="k-picker-custom-flat k-datepicker-height-32"
                                                                        [format]="format" [fillMode]="'flat'" [placeholder]="'Select ' +staticData.displayName"  id="commitmentDate" name="commitmentDate"
                                                                        [(ngModel)]="fundInvestor.CommitmentDate" (valueChange)="onChangeCommitmentDate(fundInvestor.CommitmentDate)" [value]="getFormattedDate(fundInvestor.CommitmentDate)"></kendo-datepicker>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div *ngIf="FundInvestorConstants.Ownership==staticData.name"
                                                    class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0">
                                                    <div class="form-group">
                                                        <div class="row mr-0 ml-0">

                                                            <div class="col-12 pr-0 pl-0">
                                                                <label for="fundSize" class="TextTruncate Caption-M" title="{{staticData.displayName}}">{{staticData.displayName}}</label>
                                                            </div>
                                                            <div class="col-12 pr-0 pl-0">
                                                                <input placeholder="Enter {{staticData.displayName}}" autocomplete="off" type="number" class="form-control TextTruncate"
                                                                    name="ownership" [(ngModel)]="fundInvestor.Ownership" #ownership="ngModel"/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div *ngIf="FundInvestorConstants.NetDrawn==staticData.name"
                                                    class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0">
                                                    <div class="form-group">
                                                        <div class="row mr-0 ml-0">

                                                            <div class="col-12 pr-0 pl-0">
                                                                <label for="fundSize" title="{{staticData.displayName}}" class="TextTruncate Caption-M">{{staticData.displayName}}</label>
                                                            </div>
                                                            <div class="col-12 pr-0 pl-0">
                                                                <input placeholder="Enter {{staticData.displayName}}"
                                                                    autocomplete="off" type="number"
                                                                    class="form-control TextTruncate" name="netDrawn"
                                                                    [(ngModel)]="fundInvestor.NetDrawn"
                                                                    #netDrawn="ngModel" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div *ngIf="FundInvestorConstants.Recallable==staticData.name"
                                                    class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0">
                                                    <div class="form-group">
                                                        <div class="row mr-0 ml-0">

                                                            <div class="col-12 pr-0 pl-0">
                                                                <label for="fundSize" title="{{staticData.displayName}}" class="TextTruncate Caption-M">{{staticData.displayName}}</label>
                                                            </div>
                                                            <div class="col-12 pr-0 pl-0">
                                                                <input placeholder="Enter {{staticData.displayName}}"
                                                                    autocomplete="off" type="number"
                                                                    class="form-control TextTruncate" name="Recallable"
                                                                    [(ngModel)]="fundInvestor.Recallable"
                                                                    #recallable="ngModel" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div *ngIf="FundInvestorConstants.UndrawnCommitment==staticData.name"
                                                    class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0">
                                                    <div class="form-group">
                                                        <div class="row mr-0 ml-0">

                                                            <div class="col-12 pr-0 pl-0">
                                                                <label for="fundSize" class="TextTruncate Caption-M" title="{{staticData.displayName}}">{{staticData.displayName}}</label>
                                                            </div>
                                                            <div class="col-12 pr-0 pl-0">
                                                                <input placeholder="Enter {{staticData.displayName}}"
                                                                    autocomplete="off" type="number"
                                                                    class="form-control TextTruncate" name="undrawnCommitment"
                                                                    [(ngModel)]="fundInvestor.UndrawnCommitment"
                                                                    #undrawnCommitment="ngModel" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div *ngIf="FundInvestorConstants.AstreaTransfer==staticData.name"
                                                    class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0">
                                                    <div class="form-group">
                                                        <div class="row mr-0 ml-0">

                                                            <div class="col-12 pr-0 pl-0">
                                                                <label for="fundSize" title="{{staticData.displayName}}" class="TextTruncate Caption-M">{{staticData.displayName}}</label>
                                                            </div>
                                                            <div class="col-12 pr-0 pl-0">
                                                                <input placeholder="Enter {{staticData.displayName}}"
                                                                    autocomplete="off" type="number"
                                                                    class="form-control TextTruncate" name="astreaTransfer"
                                                                    [(ngModel)]="fundInvestor.AstreaTransfer"
                                                                    #astreaTransfer="ngModel" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div *ngIf="FundInvestorConstants.CommitmentAfterAstreaTransfer==staticData.name"
                                                    class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0">
                                                    <div class="form-group">
                                                        <div class="row mr-0 ml-0">

                                                            <div class="col-12 pr-0 pl-0">
                                                                <label for="fundSize" class="TextTruncate Caption-M" title="{{staticData.displayName}}">{{staticData.displayName}}</label>
                                                            </div>
                                                            <div class="col-12 pr-0 pl-0">
                                                                <input placeholder="Enter {{staticData.displayName}}"
                                                                    autocomplete="off" type="number"
                                                                    class="form-control TextTruncate"
                                                                    name="commitmentAfterAstreaTransfer"
                                                                    [(ngModel)]="fundInvestor.CommitmentAfterAstreaTransfer"
                                                                    #commitmentAfterAstreaTransfer="ngModel" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div *ngIf="staticData.name==FundInvestorConstants.Customfield"
                                                    class="col-lg-3 col-md-3 col-sm-3 col-3 col-xl-3 pr-control-fund pl-0">
                                                    <div class="form-group">
                                                        <div class="row mr-0 ml-0">
                                                            <div
                                                                class="col-md-12 col-sm-12 col-12 col-lg-12 col-xl-12 pr-0 pl-0">
                                                                <label for="fundSize" title="{{staticData.displayName}}" class="TextTruncate Caption-M">{{staticData.displayName}}</label>
                                                            </div>
                                                            <div class="col-md-12 col-sm-12 col-12 col-lg-12 col-xl-12 pr-0 pl-0"
                                                                *ngIf="!investorEditMode">
                                                                <input id="add-fund-static-number" placeholder="Enter {{staticData.displayName}}"
                                                                    *ngIf="(staticData.dataType==mDataTypes.Number)&&(staticData.dataType!=mDataTypes.FreeText)"
                                                                    autocomplete="off" type="text" class="form-control TextTruncate"
                                                                    [(ngModel)]="staticData.value"
                                                                    id="{{staticData.displayName}}"
                                                                    name="{{staticData.displayName}}"
                                                                    (input)="numbersOnlyValidator($event)" />

                                                                <input id="add-fund-currency" placeholder="Enter {{staticData.displayName}}"
                                                                    *ngIf="(staticData.dataType==mDataTypes.CurrencyValue||staticData.dataType==mDataTypes.Percentage||staticData.dataType==mDataTypes.Multiple)&&(staticData.dataType!=mDataTypes.FreeText)"
                                                                    autocomplete="off" type="text" class="form-control TextTruncate"
                                                                    [(ngModel)]="staticData.value"
                                                                    id="{{staticData.displayName}}"
                                                                    name="{{staticData.displayName}}"
                                                                    appTwoDigitDecimaNumber
                                                                    (input)="decimalnumbersOnlyValidator($event)" />

                                                                <input id="add-fund-data-type-free-text" placeholder="Enter {{staticData.displayName}}"
                                                                    *ngIf="staticData.dataType===mDataTypes.FreeText"
                                                                    autocomplete="off" type="text" class="form-control TextTruncate"
                                                                    [(ngModel)]="staticData.value"
                                                                    id="{{staticData.displayName}}"
                                                                    name="{{staticData.displayName}}" />
                                                                <kendo-datepicker #{{staticData.displayName}} calendarType="classic"  class="k-picker-custom-flat k-datepicker-height-32"  *ngIf="staticData.dataType===mDataTypes.Date&&(staticData.dataType!==mDataTypes.FreeText)"
                                                                [format]="format" [fillMode]="'flat'" [placeholder]="'Enter ' +staticData.displayName"   id="{{staticData.displayName}}" name="{{staticData.displayName}}"
                                                                [(ngModel)]="staticData.value"  [value]="getFormattedDate(staticData.value)"></kendo-datepicker>
                                                            </div>
                                                            <div id="add-fund-data-type-number" class="col-md-12 col-sm-12 col-12 col-lg-12 col-xl-12 pr-0 pl-0"
                                                                *ngIf="investorEditMode">
                                                                <input placeholder="Enter {{staticData.displayName}}"
                                                                    *ngIf="(staticData.dataType==mDataTypes.Number)&&(staticData.dataType!=mDataTypes.FreeText)"
                                                                    autocomplete="off" type="text" class="form-control TextTruncate"
                                                                    [(ngModel)]="staticData.value"
                                                                    id="{{staticData.displayName}}"
                                                                    name="{{staticData.displayName}}"
                                                                    (input)="numbersOnlyValidator($event)" />

                                                                <input id="add-fund-data-type-currency-value" placeholder="Enter {{staticData.displayName}}"
                                                                    *ngIf="(staticData.dataType==mDataTypes.CurrencyValue||staticData.dataType==mDataTypes.Percentage||staticData.dataType==mDataTypes.Multiple)&&(staticData.dataType!=mDataTypes.FreeText)"
                                                                    autocomplete="off" type="text" class="form-control TextTruncate"
                                                                    [(ngModel)]="staticData.value"
                                                                    id="{{staticData.displayName}}"
                                                                    name="{{staticData.displayName}}"
                                                                    (input)="decimalnumbersOnlyValidator($event)"
                                                                    appTwoDigitDecimaNumber />

                                                                <input id="add-fund-value" placeholder="Enter {{staticData.displayName}}"
                                                                    *ngIf="staticData.dataType===mDataTypes.FreeText"
                                                                    autocomplete="off" type="text" class="form-control TextTruncate"
                                                                    [(ngModel)]="staticData.value"
                                                                    id="{{staticData.displayName}}"
                                                                    name="{{staticData.displayName}}" />
                                                                <kendo-datepicker id="add-fund-date"  #{{staticData.displayName}} calendarType="classic"  class="k-picker-custom-flat k-datepicker-height-32"  *ngIf="staticData.dataType===mDataTypes.Date&&(staticData.dataType!==mDataTypes.FreeText)"
                                                                [format]="format" [fillMode]="'flat'" [placeholder]="'Enter ' +staticData.displayName"   id="{{staticData.displayName}}" name="{{staticData.displayName}}"
                                                                [(ngModel)]="staticData.value"  [value]="getFormattedDate(staticData.value)"></kendo-datepicker>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </ng-container>
                                        </div>
                                        <div class="row mr-0 ml-0 pr-3 pt-0 pb-3 pr-3">
                                            <div
                                                class="col-sm-12 col-md-12 col-12 col-lg-12 col-xl-12 col-sm-12 pr-0 pl-0">
                                                <div id="add-funds-clear-investor" class="pull-right"> <a class="geography-clear pr-3"
                                                        (click)="clearInvestor(investForm)" title="Clear">Clear
                                                        all</a>
                                                    <a id="add-funds-add-investor" class="nep-button nep-button-secondary"
                                                        (click)="addInvestor(investForm)"
                                                        title="{{investorEditMode?'Update':'Add'}} Investor">{{investorEditMode?'Update':'Add'}}
                                                        Investor</a>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                    <div class="row mr-0 ml-0">
                                        <div class="col-sm-12 col-md-12 col-12 col-lg-12 col-xl-12 col-sm-12 pr-0 pl-0">
                                            <kendo-grid [kendoGridBinding]="model?.investorListData" scrollable="virtual" [rowHeight]="44" [resizable]="true"
                                                class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cab-table-grid">
                                                <kendo-grid-column title="{{col.displayName}}" id="{{col.subPageID}}" [width]="200"
                                                    *ngFor="let col of fundColumns;">
                                                    <ng-template kendoGridHeaderTemplate>
                                                        <span class="TextTruncate S-M">
                                                            {{col.displayName}}</span>
                                                    </ng-template>
                                                    <ng-template kendoGridCellTemplate let-investor>
                                                        <div class="TextTruncate" *ngIf="col.name==FundInvestorConstants.InvestorId">
                                                            <span title="{{ investor['investorName'] || investor['Investor
                                                                                                        Name']}}"
                                                                [ngClass]="!investor.isActive?'investor-active':''">
                                                                {{ investor['investorName'] || investor['Investor
                                                                Name']}}
                                                            </span>
                                                        </div>
                                                        <div class="TextTruncate" *ngIf="col.name!=FundInvestorConstants.InvestorId">
                                                            <span [ngClass]="!investor.isActive?'investor-active':''">
                                                                {{
                                                                col.name==FundInvestorConstants.CommitmentDate?((investor[col.displayName]
                                                                !=null ||
                                                                investor[col.name]!=null)?((investor[col.displayName]
                                                                ||investor[col.name])| date:'MM/dd/yyyy'):'NA'):
                                                                col.name==FundInvestorConstants.Commitment?((investor[col.displayName]
                                                                !=null ||
                                                                investor[col.name]!=null)?((investor[col.displayName]||investor[col.name])|number
                                                                : NumberDecimalConst.currencyDecimal):'NA'):
                                                                col.name==FundInvestorConstants.Ownership?((investor[col.displayName]
                                                                !=null ||
                                                                investor[col.name]!=null)?((investor[col.displayName]||investor[col.name])|number
                                                                : NumberDecimalConst.percentDecimal)+"%":'NA'):
                                                                col.name==FundInvestorConstants.NetDrawn?((investor[col.displayName]
                                                                !=null ||
                                                                investor[col.name]!=null)?((investor[col.displayName]||investor[col.name])|number
                                                                : NumberDecimalConst.currencyDecimal):'NA'):
                                                                col.name==FundInvestorConstants.Recallable?((investor[col.displayName]
                                                                !=null||
                                                                investor[col.name]!=null)?((investor[col.displayName]||investor[col.name])|number
                                                                : NumberDecimalConst.currencyDecimal ):'NA'):
                                                                col.name==FundInvestorConstants.UndrawnCommitment?((investor[col.displayName]
                                                                !=null||
                                                                investor[col.name]!=null)?((investor[col.displayName]||investor[col.name])|number
                                                                : NumberDecimalConst.currencyDecimal ):'NA'):
                                                                col.name==FundInvestorConstants.AstreaTransfer?((investor[col.displayName]
                                                                !=null||
                                                                investor[col.name]!=null)?((investor[col.displayName]||investor[col.name])|number
                                                                : NumberDecimalConst.currencyDecimal ):'NA'):
                                                                col.name==FundInvestorConstants.CommitmentAfterAstreaTransfer?((investor[col.displayName]
                                                                !=null||
                                                                investor[col.name]!=null)?((investor[col.displayName]||investor[col.name])|number
                                                                : NumberDecimalConst.currencyDecimal):'NA'):
                                                                col.dataType != mDataTypes.Date?(
                                                                col.dataType==mDataTypes.Multiple?(investor[col.displayName]!=null
                                                                && investor[col.displayName]!='' &&
                                                                investor[col.displayName]!='NA'?(
                                                                investor[col.displayName] | number :
                                                                NumberDecimalConst.multipleDecimal)+"x":'NA'):
                                                                col.dataType==mDataTypes.Percentage?(investor[col.displayName]!=null
                                                                && investor[col.displayName]!='' &&
                                                                investor[col.displayName]!='NA' ?(
                                                                investor[col.displayName] | number :
                                                                NumberDecimalConst.percentDecimal)+"%":'NA'):
                                                                col.dataType==mDataTypes.CurrencyValue?(investor[col.displayName]!=null
                                                                && investor[col.displayName]!='' &&
                                                                investor[col.displayName]!='NA' ?(
                                                                investor[col.displayName] | number :
                                                                NumberDecimalConst.currencyDecimal):'NA'):
                                                                col.dataType==mDataTypes.Number?(investor[col.displayName]!=null
                                                                && investor[col.displayName]!='' &&
                                                                investor[col.displayName]!='NA' ?(
                                                                investor[col.displayName] | number :
                                                                NumberDecimalConst.noDecimal):'NA'):(investor[col.displayName]==null
                                                                || investor[col.displayName]==""
                                                                )?'NA':investor[col.displayName]
                                                                ):
                                                                investor[col.displayName]!=null &&
                                                                investor[col.displayName]!='' &&
                                                                investor[col.displayName]!='NA' ?
                                                                (investor[col.displayName]| date:'MM/dd/yyyy' ):
                                                                'NA'
                                                                }}
                                                            </span>
                                                        </div>
                                                    </ng-template>
                                                </kendo-grid-column>
                                                <kendo-grid-column [width]="200" field="Action" title="Action">
                                                    <ng-template kendoGridHeaderTemplate>
                                                        <span class="TextTruncate S-M">
                                                            Action</span>
                                                    </ng-template>
                                                    <ng-template kendoGridCellTemplate let-investor>
                                                        <div id="add-funds-edit-investor" class="text-center"><a class="pr-3" (click)="editInvestor(investor)"><img alt=""
                                                                    src="assets/dist/images/EditIcon.svg"></a>
                                                            <a id="add-funds-delink-investor" (click)="setLinkDelinkInvestor(investor);linkText=investor.isActive?true:false">
                                                                <img tooltipPosition="top" tooltipStyleClass="bg-tooltip-fund-color" [pTooltip]="'De-link Investor'"
                                                                    *ngIf="investor.isActive" alt="" src="assets/dist/images/De-link.svg" />
                                                                <img tooltipPosition="top" tooltipStyleClass="bg-tooltip-fund-color" [pTooltip]="'Link Investor'"
                                                                    *ngIf="!investor.isActive" alt="" src="assets/dist/images/Link.svg" />
                                                            </a>
                                                        </div>
                                                    </ng-template>
                                                </kendo-grid-column>
                                                <ng-template kendoGridNoRecordsTemplate>
                                                    <app-empty-state class="finacials-beta-empty-state" [isGraphImage]="false"></app-empty-state>
                                                </ng-template>
                                            </kendo-grid>                                           
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="tabName =='Geographic Locations'">
                                    <div class="row ml-0 mr-0 pl-3 pr-3 pt-3 pb-3 location-form">                                       
                                        <ng-container *ngFor="let locationConfig of geographicLocationFieldList">
                                            <div class="col-sm-6 col-md-6  col-lg-3 pl-0 pr-3 firm-custom-padding"
                                                *ngIf="locationConfig.name == 'Region'">
                                                <div class="TextTruncate Caption-M" for="region" title="{{locationConfig.displayName}}">
                                                    {{locationConfig.displayName}}</div>
                                                <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                                    [(ngModel)]="model.geographyDetail.region" #region="ngModel" [fillMode]="'flat'" [filterable]="true"
                                                    name="region" [virtual]="virtual"
                                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                                    [size]="'medium'" [data]="masterModel.regionList" [filterable]="true" textField="region"
                                                    valueField="regionId" [placeholder]="'Select '+locationConfig.displayName"
                                                    (valueChange)="handleRegionChange()">
                                                </kendo-combobox>
                                            </div>
                                            <div class="col-sm-6 col-md-6  col-lg-3 pl-0 pr-3 firm-custom-padding"
                                                *ngIf="locationConfig.name == 'Country'">
                                                <div class="TextTruncate Caption-M" for="country" title="{{locationConfig.displayName}}">
                                                    {{locationConfig.displayName}}</div>
                                                <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                                    [(ngModel)]="model.geographyDetail.country" #country="ngModel" [fillMode]="'flat'"
                                                    [filterable]="true" name="country" [virtual]="virtual"
                                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                                    [size]="'medium'" [data]="masterModel.countryList" [filterable]="true" textField="country"
                                                    valueField="countryId" [placeholder]="'Select '+locationConfig.displayName"
                                                    (valueChange)="handleCountryChange()">
                                                </kendo-combobox>
                                            </div>
                                            <div class="col-sm-6 col-md-6  col-lg-3 pl-0 pr-3 firm-custom-padding"
                                                *ngIf="locationConfig.name == 'State'">
                                                <div class="TextTruncate Caption-M" for="state" title="{{locationConfig.displayName}}">
                                                    {{locationConfig.displayName}}</div>
                                                <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                                    [(ngModel)]="model.geographyDetail.state" #sate="ngModel" [fillMode]="'flat'" [filterable]="true"
                                                    name="state" [virtual]="virtual"
                                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                                    [size]="'medium'" [data]="masterModel.stateList" [filterable]="true" textField="state"
                                                    valueField="stateId" [placeholder]="'Select '+locationConfig.displayName"
                                                    (valueChange)="handleStateChange()">
                                                </kendo-combobox>
                                            </div>
                                            <div class="col-sm-6 col-md-6  col-lg-3 pl-0 pr-3 firm-custom-padding"
                                                *ngIf="locationConfig.name == 'City'">
                                                <div class="TextTruncate Caption-M" for="city" title="{{locationConfig.displayName}}">
                                                    {{locationConfig.displayName}}</div>
                                                <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                                    [(ngModel)]="model.geographyDetail.city" #city="ngModel" [fillMode]="'flat'" [filterable]="true"
                                                    name="city" [virtual]="virtual"
                                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                                    [size]="'medium'" [data]="masterModel.cityList" [filterable]="true" textField="city"
                                                    valueField="cityId" [placeholder]="'Select '+locationConfig.displayName">
                                                </kendo-combobox>
                                            </div>
                                        </ng-container>
                                    </div>
                                </div>
                                <div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mr-0 ml-0 fixed-footer" [ngStyle]="{'width': sideNavWidth}">
                    <div class="col-12 col-lg-12 col-md-12 col-sm-12  pr-0 pl-0">
                        <app-static-info-modification-message *ngIf="id !== undefined"></app-static-info-modification-message>
                        <div class="pull-right pt-2 pb-2 pr-3">
                            <div class="loading-input-controls-manual" *ngIf="loading"><i aria-hidden="true"
                                    class="fa fa-spinner fa-pulse fa-1x fa-fw"></i></div>
                            <input id="hiddenreloadButton" type="button" (click)=" formReset(f)" value="{{resetText}}"
                                title="{{resetText}}"
                                class="nep-button nep-button-secondary TextTruncate reset-update-portfolio-css" />
                            <button id="add-fund-create" type="submit"
                                class=" width-120 nep-button nep-button-primary width-135 reset-update-portfolio-css ml-2"
                                [disabled]="!f.form.valid" title="{{title}}">{{title}}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<div id="add-fund-cancel" *ngIf="isOpenconfirmPopUp">
    <modal customwidth="448px" modalTitle="{{linkText?'De-link Investor':'Link Investor'}}" primaryButtonName="Confirm"
        secondaryButtonName="Cancel" (primaryButtonEvent)="OnConfirmLinkAndUnlink($event)"
        (secondaryButtonEvent)="OnCancel($event)">
        <div class="row">
            <div class="col-lg-12 col-md-12 col-xs-12">
                This will {{linkText?'de-link':'link'}} the investor from this fund. Do you Confirm?
            </div>
        </div>
    </modal>
</div>