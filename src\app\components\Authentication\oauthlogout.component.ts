import { Component, OnInit,Inject } from '@angular/core';
import { OidcAuthService } from 'src/app/services/oidc-auth.service';
import { ActivatedRoute } from "@angular/router";
import { HttpClient } from "@angular/common/http";
import { throwError } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { AccountService } from 'src/app/services/account.service';
import { UserSessionStateModel } from 'src/app/common/models';
import { SessionIdleService } from 'src/app/services/session-idle.service';

@Component({
  selector: 'app-oauthlogout',
  template: ''
})
export class OauthLogoutComponent implements OnInit {

  myAppUrl: string = "";


  constructor(
    private identityloginService: OidcAuthService,
    private route: ActivatedRoute,private http: HttpClient,
    private accountService: AccountService,
    private sessionService:SessionIdleService,
    @Inject("BASE_URL") baseUrl: string
  ) {
    this.myAppUrl = baseUrl;}

  ngOnInit() {
   
    let action = this.route.snapshot.queryParams['action'];
    if(action == 'logout'){    
      this.loggingOut();
    }    
    else
      this.identityloginService.signoutRedirectCallback();
  }

  loggingOut =()=> {
    let emailId = localStorage.getItem("currentUserEmailId");
      let data : UserSessionStateModel = {            
        emailId: emailId,
        sessionState : null,
        expiredAt: null
      }
      this.accountService.addupdateSessionId(data)
      .subscribe({
        next: (data: any) => {  
         
        }, error: error => {
        
        }
     });
      this.deActivateToken().subscribe({
        next: (result => {
          this.identityloginService.logout();        
        }),
        error :
        (response) => {
          this.identityloginService.logout();
        }
      }
      );

  }
  errorHandler(error: Response) {
    return throwError(() => error);
  }

  deActivateToken = () => {
    return this.http.post(this.myAppUrl + "api/TokenManager/Cancel",null).pipe(
      map((response) => response),
      catchError(this.errorHandler
      ));
  }

}
