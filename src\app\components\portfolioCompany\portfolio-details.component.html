<div class="portfolio-details-container row ">
    <div class="col-12 pl-0 pr-0 tab-container">
        <nep-tab id="neptab" class="custom-pipeline-tab custom-ds-tab" [tabList]="tabList"
            (OnSelectTab)="switchTab($event)">
        </nep-tab>
    </div>

    <ng-container id="portfolio-company-detials" *ngIf="tabName === 'Portfolio Company Details'">
        <div class="col-12 pl-0 pr-0">
            <portfolio-company-detail [companyId]="id"></portfolio-company-detail>
        </div>
    </ng-container>

    <ng-container id="document-container" *ngIf="tabName === 'Documents'">
        <div class="col-md-3 pl-0 pr-0 folder-container">
            <app-pcdocument-folders [PortfolioCompanyId]="id" [documentsFieldPageConfig]="documentsFieldPageConfig" (folderSelected)="onFolderSelected($event)" [IsFund]="false"></app-pcdocument-folders>
        </div>

        <div class="col-md-9 pl-0 pr-0 doc-container">
            <app-pcdocument-list [PortfolioCompanyId]="id" [canEdit]="canEditDocuments" [documentsFieldPageConfig]="documentsFieldPageConfig" [IsFund]="false" #documentList></app-pcdocument-list>
        </div>
    </ng-container>
</div>