﻿import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnInit,
} from "@angular/core";
import { Router } from "@angular/router";
import { NgxSpinnerService } from "ngx-spinner";
import { LazyLoadEvent } from "primeng/api";
import { AccountService } from "../../services/account.service";
import { FundService } from "../../services/funds.service";
import { MiscellaneousService } from "../../services/miscellaneous.service";
import { FeaturesEnum } from "../../services/permission.service";
import { Observable, of } from "rxjs";
import { GridDataResult } from "@progress/kendo-angular-grid";
import { SortDescriptor, State } from "@progress/kendo-data-query";
import { KendoService } from "src/app/services/kendo.service";
import { CommonSubFeaturePermissionService } from "src/app/services/subPermission.service";
import { ErrorMessage } from "src/app/services/miscellaneous.service";
import { ToastrService } from "ngx-toastr";
@Component({
  selector: "fund-list",
  templateUrl: "./fund-list.component.html",
  styleUrls: ["./fund-list.component.scss"],
})
export class FundListComponent implements OnInit {
  feature: typeof FeaturesEnum = FeaturesEnum;
  public funds: any = [];
  closeResult: string;
  pagerLength: any;
  dataTable: any;
  blockedTable: boolean = false;
  totalRecords: number;
  totalPage: number;
  globalFilter: string = "";
  paginationFilterClone: any = {};
  isLoader: boolean = false;
  model: any = {};
  fundStaticConfigurationData = [];
  headers = [];
  public view: Observable<GridDataResult>;
  public state: State = {
    skip: 0,
    take: 100,
  };
  sort: SortDescriptor[] = [];
  canViewFund: boolean = false;
  canAddFund: boolean = false;
  canExportFund: boolean = false;
  constructor(
    private router: Router,
    private accountService: AccountService,
    private elementRef: ElementRef,
    private _fundService: FundService,
    protected changeDetectorRef: ChangeDetectorRef,
    private spinner: NgxSpinnerService,
    private miscService: MiscellaneousService,
    private kendoService: KendoService,
    private subPermissionService: CommonSubFeaturePermissionService,
    private toastrService: ToastrService,
  ) {
    this.pagerLength = this.miscService.getPagerLength();
    localStorage.setItem("headerName", "");
  }
  ngOnInit(): void {
    this.getSubFeatureAccessPermissions();
    this.getAllFundDetails(null);
  }
  exportFundList() {
    if(this.canExportFund){
      let event = JSON.parse(JSON.stringify(this.paginationFilterClone));
      event.globalFilter = this.globalFilter;
      event.filterWithoutPaging = true;
      this._fundService
        .exportFundList({ paginationFilter: event })
        .subscribe((response) => this.miscService.downloadExcelFile(response));
    }
    else{
      this.showNoAccessError();
    }
  }
  redirectToFund(fund: any) {
    localStorage.setItem("headerName", fund.value);
    this.router.navigate(["/fund-details", fund.encryptedId]);
  }

  getAllFundDetails(event: any) {
    this.isLoader = true;
    if (event == null) {
      event = {
        first: 0,
        rows: 100,
        globalFilter: null,
        sortField: null,
        sortOrder: 1,
      };
    }
    if (event.multiSortMeta == undefined) {
      event.multiSortMeta = [{ field: "ValueColumn1.Value", order: 1 }];
      event.sortField = "ValueColumn1.Value";
    }
    this._fundService.getFundsListData({ paginationFilter: event }).subscribe({
      next: (result) => {
        let resp = result != null ? result["body"] : null;
        if (resp != null && result.code == "OK") {
          this.headers = resp.headers;
          if (document.getElementById("HeaderNameID")) {
            this.miscService.getTitle(this.model?.fundName);
          }
          localStorage.setItem("headerName", this.model.fundName);
          this.fundStaticConfigurationData = resp.fundStaticConfiguartionData;
          this.totalRecords = resp.totalRecords;
          if (this.totalRecords > 100) {
            this.totalPage = Math.ceil(this.totalRecords / event.rows);
          } else {
            this.totalPage = 1;
          }
          this.isLoader = false;
        } else {
          this.totalRecords = 0;
        }
        this.blockedTable = false;
        this.isLoader = false;
        this.view = of<GridDataResult>({
          data: this.fundStaticConfigurationData,
          total: this.totalRecords,
        });
      },
      error: (error) => {
        this.isLoader = false;
      },
    });
  }
  searchLoadPCLazy() {
    let params: any = {
      first: 0,
      rows: 100,
      globalFilter: this.globalFilter != "" ? this.globalFilter : null,
      sortField: null,
      sortOrder: 1,
    };

    if (this.fundStaticConfigurationData.length != 0) {
      let result = this.kendoService.getHeaderValue(
        params,
        null,
        this.headers,
        this.sort
      );
      params = result.params;
      this.sort = result.parentSort;
    }
    this.getAllFundDetails(params);
  }
  dataStateChange($event) {
    this.state.skip = $event.skip;
    this.state.take = $event.take;
    let params: any = {
      first: $event.skip,
      rows: $event.take,
      globalFilter: this.globalFilter || null,
      sortField: null,
      sortOrder: 1,
    };
    let result = this.kendoService.getHeaderValue(
      params,
      $event,
      this.headers,
      this.sort
    );
    params = result.params;
    this.sort = result.parentSort;
    this.getAllFundDetails(params);
  }

  getSubFeatureAccessPermissions() {
    this.subPermissionService
      .getCommonFeatureAccessPermissions(FeaturesEnum.Fund)
      .subscribe({
        next: (result) => {
          if (result.length > 0) {
            this.canViewFund = result?.map((x) => x.canView).includes(true);
            this.canAddFund = result?.map((x) => x.canAdd).includes(true);
            this.canExportFund = result?.map((x) => x.canExport).includes(true);
          }
        },
        error: (_error) => {},
      });
  }

  addRedirect(){
    if (this.canAddFund) {
      this.router.navigate(['/create-fund']);
    }
    else {
      this.showNoAccessError();
    }
  }

  showNoAccessError() {
    this.toastrService.error(ErrorMessage.NoAccessFund, "", { positionClass: "toast-center-center" });
  }
}
