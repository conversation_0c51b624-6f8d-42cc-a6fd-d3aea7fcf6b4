import { Component, OnInit } from '@angular/core';
import { environment } from 'src/environments/environment';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { DocumentDetailDto } from '../extraction.model';
import { OidcAuthService } from 'src/app/services/oidc-auth.service';

@Component({
  selector: 'app-data-extraction',
  templateUrl: './data-extraction.component.html',
  styleUrls: ['./data-extraction.component.scss']
})
export class DataExtractionComponent implements OnInit {
  isLoading:boolean = true;
  panelUrl: string = environment.ingestion_url + '/#/panel';
  trustedPanelUrl: SafeResourceUrl;
  rowData: DocumentDetailDto | null = null;
  
  constructor(
    private oidcService: OidcAuthService, // Replace with actual OIDC service type
    private sanitizer: DomSanitizer,
    private router: Router
  ) {
    // We'll handle the router state data in ngOnInit
  }

  ngOnInit(): void {
    let config = this.oidcService.getEnvironmentConfig();
    if (history.state && history.state.rowData && !history.state.isSpecificKPI) {
      this.rowData = history.state.rowData as DocumentDetailDto;
      if (this.rowData.processId && this.rowData.documentId) {
        this.panelUrl = `${environment.ingestion_url}/#/panel?processId=${this.rowData.processId}&companyId=${this.rowData.companyId}&environment=${config.client_env}`;
        this.trustedPanelUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.panelUrl);
      }
    } else {
      this.rowData = history.state.rowData as DocumentDetailDto;
      this.panelUrl = `${environment.ingestion_url}/#/specific-kpi?processId=${this.rowData.processId}&fundId=${this.rowData.fundId}&environment=${config.client_env}`;
      this.trustedPanelUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.panelUrl);
    }

    console.log('Final Panel URL:', this.panelUrl);
  }
  onIframeLoaded() {
    this.isLoading = false;
  }
}
