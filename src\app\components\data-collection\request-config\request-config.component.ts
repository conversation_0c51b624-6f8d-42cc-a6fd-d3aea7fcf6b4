import { Component, OnInit } from '@angular/core';
import { SVGIcon, plusIcon, searchIcon } from "@progress/kendo-svg-icons";
import { Observable, filter, of } from "rxjs";
import { SortDescriptor, State } from "@progress/kendo-data-query";
import { GridDataResult } from "@progress/kendo-angular-grid";
import { DataRequestService } from 'src/app/services/data-request.service';
import { REQUEST_CONFIG_COLUMNS } from 'src/app/common/constants';
import { CompositeFilterDescriptor, filterBy } from '@progress/kendo-data-query';
import * as moment from 'moment';
@Component({
  selector: 'app-request-config',
  templateUrl: './request-config.component.html',
  styleUrls: ['./request-config.component.scss']
})
export class RequestConfigComponent implements OnInit {
  plusSVG: SVGIcon = plusIcon;
  searchSVG: SVGIcon = searchIcon;
  isCreateRequest: boolean = false;
  requestConfigColumns: any[] = REQUEST_CONFIG_COLUMNS;
  public view: Observable<GridDataResult>;
  public state: State = {
    skip: 0,
    take: 100,
  };
  sort: SortDescriptor[] = [];
  configRequest: any[] = [];
  configRequestClone: any[] = [];
  totalRecords: number = 100;
  globalFilter: string = "";
  isLoader: boolean;
  blockedTable: boolean = false;
  public data: any;
  totalPage: number;
  requestData = [];
  changeDetectorRef: any;
  public gridData: any[] = this.configRequest;
  gridFilter: CompositeFilterDescriptor;
  constructor(private dataRequestService: DataRequestService) { }

  getRequests() {
    this.dataRequestService.getDataRequest().subscribe(data => {
      this.configRequest = data.map(item => ({
        companies: item.companies,
        funds: item.funds,
        groups: item.groups,
        id: item.id,
        status: item.isActive ? 'Active' : 'Inactive',
        requestName: item.name,
        createdBy: item.createdBy,
        createdDate: moment(new Date(item.createdOn)).format(
          "D-MMM-YYYY,HH:mm"),
        type: item.isAutomatic ? 'Automatic' : 'Manual',
      }));
      this.configRequestClone= this.configRequest;
    });
  }
  getInitials(createdBy: any): string {
    if (typeof createdBy === 'string') {
      let parts = createdBy.split(' ');
      return (parts[0].charAt(0).toUpperCase() + (parts[1] ? parts[1].charAt(0).toUpperCase() : ''));
    }
    return '';
  }
  ngOnInit(): void {
    this.getRequests();
  }
  filterGrid(value: string) {
    if (value) {
      let requestConfigColumns = this.requestConfigColumns.map(column => {
        return {
          ...column,
          operator: 'contains', 
          value: value 
        };
      });
      this.gridFilter = {
        logic: 'or',
        filters:requestConfigColumns
      };
       this.configRequest = filterBy(this.configRequestClone, this.gridFilter);
    } else {
      this.configRequest = [...this.configRequestClone];
    }
  }
  
  }



