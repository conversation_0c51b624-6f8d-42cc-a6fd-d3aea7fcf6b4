@import '../../../variables';
@import '../../../../src/assets/dist/css/font';

$padding-small: 12px;
$padding-medium: 16px;
$border-radius: 4px;
$box-shadow: 0px 0px 8px 0px #0000001F;
$color-grey: #666666;
$color-dark: #1A1A1A;
$color-light-bg: #FAFAFA;
$color-border: #E6E6E6;
$color-drag-bg: #F5F9FF;
$color-required: red;

.lp-template-section {
    form {
        width: 100%;
    }

    .filter-section {
        padding: 0 $padding-medium $padding-medium 0;
        border-radius: $border-radius;
        box-shadow: $box-shadow;
    }

    label {
        color: $color-grey;
        padding-bottom: 0.25rem;
    }

    .req-label {
        padding-bottom: 0.25rem;
        &:after {
            content: "*";
            color: $color-required;
        }
    }

    .apply-btn {
        width: 98px;
    }

    .note-section {
        color: $color-dark;
    }

    .content-section {
        border-radius: $border-radius;
        box-shadow: $box-shadow;

        .no-data-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 60%;
        }

        .no-content-section {
            .no-content-text {
                color: #2B2B33;
                @extend .Body-M;
            }

            .no-content-sub,
            .template-text {
                color: #50505C;
            }
        }

        .header-section-detail {
            background: $color-light-bg;
            padding: 6px $padding-medium;
            border-bottom: 1px solid $color-border;
        }

        .section-list {
            padding: 20px $padding-small;
            height: calc(100vh - 390px);
            overflow-y: auto;

            .section {
                .section-detail {
                    box-shadow: $box-shadow;

                    .close-section {
                        width: 48px;
                        height: 100%;
                        cursor: pointer;
                        border-left: 1px solid $color-border;
                        padding: $padding-medium 6px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    .drag-content-section {
                        width: calc(100% - 100px);
                        height: auto;
                    }

                    .section-drag {
                        width: 48px;
                        height: 100%;
                        border-right: 1px solid $color-border;
                        padding: $padding-medium $padding-small;
                        background: $color-drag-bg;
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        .drag-bg {
                            a {
                                cursor: pointer;
                            }
                        }
                    }
                }
            }
        }
    }
}

.cdk-drag-preview {
    background: #FAFAFC;
    height: auto !important;
    box-shadow: none !important;

    .section-detail {
        box-shadow: $box-shadow;

        .close-section {
            width: 48px;
            height: 100%;
            cursor: pointer;
            border-left: 1px solid $color-border;
            padding: $padding-medium 6px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .drag-content-section {
            width: calc(100% - 100px);
            height: auto;
        }

        .section-drag {
            width: 48px;
            height: 100%;
            border-right: 1px solid $color-border;
            padding: $padding-medium $padding-small;
            background: $color-drag-bg;
            display: flex;
            justify-content: center;
            align-items: center;

            .drag-bg {
                a {
                    cursor: pointer;
                }
            }
        }
    }
}

.S-R {
    color: $color-dark;
}

.custom-info {
    color: $color-grey;
}

.save-as-label {
    color: #000000 !important;
}

.sdg-section {
    width: 100%;

    .no-data-container {
        padding: 2rem;
        border: dashed 2px #DEDFE0;
    }

    .sdg-image-container {
        border: dashed 2px #DEDFE0;
        border-radius: 4px;
    }

    .image-drag-drop-section {
        padding: 1rem 0rem 1rem 1rem;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .sdg-image-note {
        font-weight: 400;
        font-style: italic;
    }

    .image-drag-drop-header {
        height: 35px;
        background: #F2F2F2;
        padding: 6px 15px;
    }

    .image-box {
        height: 150px;
        width: 150px;
        border: dashed 2px #DEDFE0;
        cursor: move;
        display: inline-flex;
        background: transparent;
        border-radius: 4px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .image-drag-group {
        display: flex;
        width: 100%;
        overflow-y: auto;
    }

    .uploaded-image {
        height: 145px;
        max-width: 100%;
        max-height: 100%;
    }

    .remove-image-btn {
        position: absolute;
        top: -10px;
        right: -10px;
        background: #ffffff;
        color: white;
        border: none;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .image-box:active {
        box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
            0 8px 10px 1px rgba(0, 0, 0, 0.14),
            0 3px 14px 2px rgba(0, 0, 0, 0.12);
    }

    .image-box-preview {
        width: 145px;
        height: 145px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: dashed 2px #DEDFE0;
        border-radius: 4px;
        overflow: hidden;
    }
}
.icon-img-tooltip{
    vertical-align:text-bottom !important;
}
.k-single-select-custom{
    padding: 12px 16px;
}
.k-order-button{
    width: 240px;
    background: none;
    border: none;
    border-bottom: 1px solid #E6E6E6;
    border-radius: 0 !important;
    height: 32px;
    position: relative;
    padding: 0;
    text-align: left;
    color: #B3B3B3;
    .button-text {
        display: inline-block;
        text-align: left;
        padding: 0.38rem 0.75rem;
    }

    .button-icon {
        display: inline-block;
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        color: #424242;
    }
}
.drag-item-container {
    display: flex;
    align-items: center;
    padding: 16px 12px;
}

.drag-handle {
    display: inline-block;
    padding-right: 8px;
    cursor: pointer;
    
    img {
        width: 20px;
        height: 20px;
        vertical-align: middle;
    }
}

.item-content {
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
}
.data-order-list {
    min-height: 100px;
}
.clearfix::after {
    content: "";
    clear: both;
    display: table;
}
.currency-display-none{
    display: none !important;
}