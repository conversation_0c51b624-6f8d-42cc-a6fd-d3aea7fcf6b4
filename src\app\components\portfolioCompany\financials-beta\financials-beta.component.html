<div class="company-finacial company-kpis pb-3" *ngIf="tabList.length>0" id="financials">
  <div class="mt-3 financial-tab-header p-2">
      {{subHeaderName}}
  </div>
  <div class="financial-page financials-v1">
          <div class="panel panel-default border-0 pt-2 tab-bg">
              <div class="allvalues mt-2 float-right S-R"> <span>Default: {{reportingCurrencyCode}} ({{currencyModel?.defaultUnit?.unitType}})</span> <span *ngIf="currencyModel?.convertedCurrencyCode!=null && isApply"> | Converted: {{currencyModel?.convertedCurrencyCode}} ({{currencyModel.convertedUnit?.unitType}})</span> <span *ngIf="currencyModel?.spotRate!=null && currencyModel?.isSpotRate && isApply"> | Spot Rate: {{currencyModel.convertedCurrencyCode}} {{currencyModel?.spotRate}}</span></div>
              <div class="panel-heading border-bottom-0">
                  <div class="panel-title custom-tabs">
                      <ul class="nav nav-tabs ">
                        <a *ngFor="let tab of tabList;" (click)="selectTab(tab);ErrorNotation = false;" id="financials-{{tab.tabAliasName}}">
                            <li class="nav-item" role="presentation">
                                <button title="{{tab.tabAliasName}}" class="nav-link nav-custom settingMoreThan25Chars TextTruncate" [ngClass]="tab.active?'active':''" id="{{tab.tabAliasName}}" data-bs-toggle="tab" type="button" role="tab" aria-controls="home" aria-selected="true">
                                  {{tab.tabAliasName}}
                                </button>
                            </li>
                        </a>
                      </ul>
                  </div>
              </div>
          </div>
          <div class="filter-bg financials-ContainerPadding">
          <div class="border-top ">
            <div class="row mr-0 ml-0 filterContainerPadding">
                <div class=" col-lg-12 col-md-12 col-sm-12 pl-0 pr-0 height-100">
                    <div class="float-left height-100" id="valuetype-container">
                        <div class="pl-3 height-100" *ngIf="isChildtab" id="valuetype-childtab" >
                            <div class="nep-tabs nep-tabs-line height-100" id="valuetype-nep-tab" >
                                <div class="nep-tabs-header height-100" id="valuetype-header" >
                                    <div class="nep-tabs-header-tabs height-100" id="valuetype-tab" >
                                        <div class="nep-tabs-inner height-100" id="valuetype-inner-tab" >
                                            <div class="nep-tabs-scroll nep-tab-alignment-subtab financial-section height-100" id="valuetype-div" >
                                                <a id="porfolio-company-cap-table" title="{{tab.alias}}" class="custom-ds-tab height-100" *ngFor="let tab of tabValueTypeList" (click)="selectValueTab(tab);ErrorNotation = false;" >
                                                    <div id="{{tab.alias}}" class="nep-tabs-tab sub-tab-font-color height-100"   [class.nep-tabs-active]="tab.active" [ngStyle]="{'padding': '0px !important'}">
                                                        {{tab.alias}}
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pull-right headerfontsize">
                        <div class="d-inline  QMY_Container" *ngIf="hasMonthly || hasQuarterly || hasAnnually" id="financials-period-container">
                            <div class="d-inline" id="financials-period-div">
                                <a (click)="onChangePeriodOption('isMonthly');ErrorNotation = false;" *ngIf="hasMonthly" id="financials-monthly">
                                    <div id="financials-monthly-btn" class="d-inline QMY_Text MStyle QMYStyle"   [ngClass]="[isMonthly?'activeQMY':'',model.subTabName=='IC'?'btn-cursor-none':'']">
                                        Monthly
                                    </div>
                                </a>
                                <a (click)="onChangePeriodOption('isQuarterly');ErrorNotation = false;" *ngIf="hasQuarterly" id="financials-quarterly">
                                    <div id="financials-quarterly-btn" class="d-inline QMY_Text QMYStyle"   [ngClass]="[isQuarterly?'activeQMY':'',model.subTabName=='IC'?'btn-cursor-none':'']">
                                        Quarterly
                                    </div>
                                </a>
                                <a (click)="onChangePeriodOption('isAnnually');ErrorNotation = false;" *ngIf="hasAnnually" id="financials-annual">
                                    <div  id="financials-annual-btn" class="d-inline  QMY_Text QMYStyle YStyle" [ngClass]="isAnnually?'activeQMY':''">
                                        Annual
                                    </div>
                                </a>
                            </div>     
                        </div>
                        <div class="d-inline">
                            <div class="d-inline" id="financials-ytd-div">
                                <a (click)="onChangeValueTypeOption('YTD')" *ngIf="hasYtd" id="financials-ytd">
                                    <div class="d-inline value-type-switch-style" [ngClass]="model.subTabName=='IC' ? 'disable-value-type-style' : isYtd?'active-value-type-style':''">
                                        <span class="value-type-text-style" id="financials-ytd-btn">YTD</span>
                                    </div>
                                </a>
                            </div>
                            <div class="d-inline" id="financials-ltm-div">
                                <a (click)="onChangeValueTypeOption('LTM')" *ngIf="hasLtm" id="financials-ltm">
                                    <div class="d-inline value-type-switch-style" [ngClass]="model.subTabName=='IC' ? 'disable-value-type-style' : isLtm?'active-value-type-style':''">
                                        <span class="value-type-text-style" id="financials-ltm-btn">LTM</span>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="d-inline cloud_download download-container">
                            <div class="d-inline pr-1 headerfontsize-beta">
                            <div class="d-inline">Logs</div>
                            <div class="d-inline pr-2 pl-1" [title]="auditLogTitle">
                                <kendo-switch id="financials-log" size="small" [(ngModel)]="ErrorNotation" (valueChange)="handleChange($event)" [onLabel]="' '" [offLabel]="' '"></kendo-switch>
                            </div>
                        </div>
                        <div class="d-inline textsplit"></div>
                            <div class="d-inline pl-2 pr-2">
                                <a id="financials-download" *ngIf="!isDownload" (click)="export()"><img  src="assets/dist/images/Cloud-download.svg"  class="cursor-filter" alt=""></a>
                                <span class="pl-1" *ngIf="isDownload">
                                    <i aria-hidden="true"
                                        class="download-circle-loader fa fa-circle-o-notch fa-1x fa-fw"></i>
                                </span>
                            </div>
        
                            <div *ngIf="!isDownload && showDownloadIcon" class="d-inline textsplit"></div>
        
                            <div class="d-inline pl-2"><img  id="dropdownMenuButton" [matMenuTriggerFor]="menu" #filterMenuTrigger="matMenuTrigger" src="assets/dist/images/ConfigurationWhite.svg" class="cursor-filter" alt="">
                                <span [matMenuTriggerFor]="menu" #filterMenuTrigger="matMenuTrigger" class="spot-rate-circle Caption-R" title="Spot Rate applied" *ngIf="spotRate!=null && isSpotRateApply && isApply">S</span>
                            </div>
                        </div>
                    </div>
        
                </div>
            </div>
        </div>
        <div class="d-none" #OutsideClick>Out click</div>
        <mat-menu #menu="matMenu" [hasBackdrop]="true" class="fixed-menu">
            <form name="form" #form="ngForm" (ngSubmit)="form.form.valid && onSubmit()" (click)="$event.stopPropagation()">
                <div class="filter-first">
                    <div class="row m-0 ">
                        <div class="col-12 pb-1 pt-3 label-align Caption-M">
                            Period
                        </div>
                        <div class="col-12 pl-3 pr-3">
                            <kendo-combobox id="financials-period" [clearButton]="false" [(ngModel)]="model.periodType" #periodType="ngModel" [fillMode]="'solid'"
                                name="periodType" class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                                [data]="periodTypes" [valuePrimitive]="false" textField="type" placeholder="Select Date Range"
                                (valueChange)="onPeriodChange($event)" valueField="type">
                            </kendo-combobox>
                        </div>
        
                    </div>
                    <div class="row m-0" *ngIf="model.periodType?.type=='Date Range' ">
                        <div class="col-12 pb-1 pt-3 label-align Caption-M">
                            Date Range
                        </div>
                        <div class="col-12 pl-3 pr-3" id="financials-calendar">
                            <p-calendar appendTo="body" panelStyleClass="mat-menu-calendar" #myCalendar placeholder="Select Date Range " name="startPeriod" view="month" showButtonBar="true" [(ngModel)]="model.startPeriod" #startPeriod selectionMode="range" [yearNavigator]="true"
                                [readonlyInput]="true" hideOnDateTimeSelect="true" inputStyleClass="date-picker-input" dateFormat="mm/yy" [showIcon]="true" yearRange={{yearRange}} [maxDate]="today" class="calender-font table-header-calender-width" (onSelect)="validateKPIPeriod(form,model) ">
                            </p-calendar>
                        </div>
                    </div>
                    <div class="row m-0">
                        <div class="col-12 pb-1 pt-3 label-align Caption-M">
                            Fx rates
                        </div>
                        <div class="col-12 pl-3 pr-3">
                            <kendo-combobox id="financials-fxrates" [clearButton]="false" [(ngModel)]="model.fxRates" #fxRates="ngModel" [fillMode]="'solid'" name="fxRates"
                                class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="fxRatesList"
                                [valuePrimitive]="false" textField="type" placeholder="Select Fx" (valueChange)="this.model.isSpotRate ? getSpotRate() : null"
                                valueField="id">
                            </kendo-combobox>
                        </div>
                    </div>
                    <div class="row m-0">
                        <div class="col-12 pb-1 pt-3 label-align Caption-M">
                            Currency
                        </div>
                        <div class="col-12 pl-3 pr-3">
                            <kendo-combobox id="financials-currency" [clearButton]="false" [filterable]="true"  [(ngModel)]="model.currencyCode" #currencyCode="ngModel" [fillMode]="'solid'"
                                name="currencyCode" class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                                [data]="filteredCurrencyList" [valuePrimitive]="false" textField="currencyCode" placeholder="Select Currency"
                                (valueChange)="onCurrencyChange($event);" valueField="currencyID" (filterChange)="handleFilter($event)">
                            </kendo-combobox>
                        </div>
                    </div>
                    <div class="row m-0">
                        <div class="col-12 pb-1 pt-3 label-align Caption-M">
                            Currency Unit
                        </div>
                        <div class="col-12 pl-3 pr-3">
                            <kendo-combobox  id="financials-currency-unit" [clearButton]="false" [(ngModel)]="model.currecyUnit" #currecyUnit="ngModel" [fillMode]="'solid'"
                            name="currecyUnit" class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32"
                           [size]="'medium'" [data]="unitTypeList"  [valuePrimitive]="false" textField="unitType"
                           placeholder="Select Unit"  valueField="typeId">
                       </kendo-combobox>
                        </div>
                    </div>
                    <div class="row m-0">
                        <div class="col-12 pb-1 pt-3 label-align pr-0">
                            <div class="float-left Body-R spot-label">Spot Rate Conversion</div>
                            <div class="float-right"> <kendo-switch id="financials-spotrate" size="small" name="isSpotRate" (valueChange)="onSpotRateChange()"  #isSpotRate [(ngModel)]="model.isSpotRate"  [onLabel]="' '" [offLabel]="' '"></kendo-switch></div>
                        </div>
                    </div>
                    <div class="row m-0" *ngIf="model.isSpotRate">
                        <div class="col-12 pb-1 pt-3 label-align Caption-M">
                            Spot Rate Date
                        </div>
                        <div class="col-12 pl-3 pr-3">
                            <kendo-datepicker id="financials-spotrate-datepicker"  calendarType="classic" class="k-picker-custom-solid k-picker-custom-solid-pc k-datepicker-height-32 k-datepicker-width-260"  [format]="format" [fillMode]="'solid'"
                                        placeholder="Select Spot Rate Date"
                                        id="spotRateDate" name="spotRateDate" [disabledDates]="disabledDates" 
                                        [(ngModel)]="model.spotRateDate" (valueChange)="getSpotRate()" [value]="model.isSpotRate ? getFormattedDate(model.spotRateDate == null ? defaultDate : model.spotRateDate) : null"
                                          #spotRateDate></kendo-datepicker>
                        </div>
                        <div class="col-12 pb-1 pt-3 label-align Caption-M error-message" *ngIf="spotRateErrorMessage!=null">
                            {{spotRateErrorMessage}}
                        </div>
                    </div>
                </div>
                <div class="filter-footer pr-3 pb-3 mr-2">
                    <div class="d-inline ">
                        <button type="reset" (click)="$event.stopPropagation();$event.preventDefault();formReset(form);isSubmit == true ?isSubmit = false:isSubmit = true;spotRateErrorMessage = null; isApply = false" class="btn btn-reset" id="financials-reset-filter">Reset</button>
                    </div>
                    <div class="d-inline ">
                        <button type="submit" [disabled] ="model.periodType?.type=='Date Range' && (this.model.startPeriod == null || this.model.startPeriod == undefined) || spotRateErrorMessage!=null" class="btn btn-light btn-app pt-0 pb-0" id="financials-apply-filter">Apply</button>
                    </div>
                </div>
            </form>
        </mat-menu>
          <container-element [ngSwitch]="tabName">
            <app-profit-loss-beta [tabName]="tabName" [isDownload]="isDownload" [isErrorLog]="ErrorNotation" (onChangeValueType)="setValueType($event)" *ngSwitchCase="'Profit & Loss'" [model]="model" [valueType]="model.subTabName" [isFilter]="isSubmit" [periodType]="selectedPeriodType" [selectedCurrency]="selectedCurrencyCode"            
                (changeOptionType) = "changeOptionType($event)" (isDownloading) = "isDownloading($event)" [loadData]="loadData" [pageConfigData]="selectedPeriodTypeConfiguration" [isYtd] = "isYtd" [isLtm] = "isLtm" [valueTypeString]="valueTypeString"
                [profitLossPermission]="profitLossPermission" [sectionId]="sectionId"></app-profit-loss-beta>

          <app-balance-sheet-beta [tabName]="tabName" [isDownload]="isDownload" [isErrorLog]="ErrorNotation" (onChangeValueType)="setValueType($event)" *ngSwitchCase="'Balance Sheet'" [model]="model" [valueType]="model.subTabName" [isFilter]="isSubmit" [periodType]="selectedPeriodType" [selectedCurrency]="selectedCurrencyCode" 
          (changeOptionType) = "changeOptionType($event)" (isDownloading) = "isDownloading($event)" [loadData]="loadData" [pageConfigData]="selectedPeriodTypeConfiguration" [isYtd] = "isYtd" [isLtm] = "isLtm" [valueTypeString]="valueTypeString"
          [balanceSheetPermission]="balanceSheetPermission" [sectionId]="sectionId"></app-balance-sheet-beta>
          
          <app-cashflow-beta [tabName]="tabName" [isDownload]="isDownload" [isErrorLog]="ErrorNotation" (onChangeValueType)="setValueType($event)" *ngSwitchCase="'Cash Flow'" [model]="model" [valueType]="model.subTabName" [isFilter]="isSubmit" [periodType]="selectedPeriodType" [selectedCurrency]="selectedCurrencyCode" 
          (changeOptionType) = "changeOptionType($event)" (isDownloading) = "isDownloading($event)" [loadData]="loadData" [pageConfigData]="selectedPeriodTypeConfiguration" [isYtd] = "isYtd" [isLtm] = "isLtm" [valueTypeString]="valueTypeString"
          [cashFlowPermission]="cashFlowPermission" [sectionId]="sectionId"></app-cashflow-beta>
         </container-element>
      </div>
  </div>
</div>
<div class="row mr-0 ml-0" *ngIf="tabList.length>0">
    <div class="col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
        <div class="pt-0 mb-0">
            <span class="rcalcLegend"></span>
            <span class="pl-2 pr-3">Converted Value</span>
        </div>
    </div>
</div>
