import { Component, OnInit } from "@angular/core";
import { ActivatedRoute } from '@angular/router';
import { InvestorDashBoardStatic, NumberDecimalConst } from "src/app/common/constants";
import { InvestorService } from '../../../services/investor.service';
import { MiscellaneousService } from "../../../services/miscellaneous.service";
import { DropDownFilterSettings } from '@progress/kendo-angular-dropdowns';
@Component({
  selector: 'investor-dashboard',
  templateUrl: './investorDashboard.component.html',
  styleUrls: ['./investorDashboard.component.scss',"./../../home/<USER>"]
})
export class InvestorDashboardComponent implements OnInit {
  id: any;
  Unit="M";
  Currency="USD";
  dashboarddata: any={};
  sectorData: any[] = [];
  headerData: any[] = [];
  totalFunds: any = [];
  totalPortfolioCompanies: any = [];
  totalInvestedCapital: any;
  totalRealizedValue: any;
  totalUnrealizedValue: any;
  totalValue: any;
  width: number = 0;  
  NumberDecimalConst = NumberDecimalConst;
  dashBoardConstant=InvestorDashBoardStatic;
  isData:boolean=true;
  pageConfigField=[];
  pageConfigFieldClone = [];
  topCompanyData:any[] = [];
  TVPI: any = "0.0";
  DPI: any = "0.0";
  RVPI: any = "0.0";
  enableview: boolean;
  yearOptions: any[] = [];
  FromDate: any = (new Date()).getFullYear()-15;
  ToDate: any = (new Date()).getFullYear();
  isLoader:boolean = false;
  TVPIByVintageYear: any[] = [];
  dashboardContainer: string ="investor-dashboard-container-without-expand";
  model: any;
  public virtual: any = {
    itemHeight: 32,
    pageSize: 20,
  };
public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: "contains",
  };
  constructor(private _investorService: InvestorService, private _avRoute: ActivatedRoute, private miscService: MiscellaneousService,) {
    if (this._avRoute.snapshot.params["id"]) {
      this.id = this._avRoute.snapshot.params["id"];
    }
  }
  onResized(event: any) {
    this.width = event?.newRect?.width;
  }
  onResizedDashboard(event: any) {
    this.width = event?.newRect?.width;
    this.dashboardContainer =  "investor-dashboard-container-without-expand";
  }
  ngOnInit(): void {
    this.model = {
      FromDate: new Date(this.FromDate,0,2),
      ToDate: new Date()
    }
  this.getInvestorDashboardDetails(false,'','');
    this.yearOptions = this.miscService.bindYearList().map((element) => {
      return { ...element, value: parseInt(element.value) };
    });
  }
  getInvestorDashboardDetails(val:any, event:any, dateType:any) {
    this.isLoader = true;
    this.loadModelDefault(val,event,dateType);

    this._investorService.getInvestorDashBoard({FromDate: this.model.FromDate,ToDate: this.model.ToDate, encryptedInvestorId: this.id, paginationFilter: null }).subscribe({next:(result:any) => {
      if (result != null) {
        const { pageFieldValueModel = [], expandoObject = {}, topCompaniesTotalValue = [] } = result;
        this.pageConfigField = pageFieldValueModel;      
        if (pageFieldValueModel.length > 0) {
          this.totalFunds = pageFieldValueModel.filter(data => data.name == this.dashBoardConstant.TotalFunds);
          this.totalPortfolioCompanies = pageFieldValueModel.filter(data => data.name == this.dashBoardConstant.PortfolioCompanies);
          this.enableview = true;      
          const getValueByName = (name: string) => {
            const item = pageFieldValueModel.find(row => row.name == name);
            return item ? item.value : null;
          };
          this.TVPI = getValueByName("TVPI") || this.TVPI;
          this.DPI = getValueByName("DPI") || this.DPI;
          this.RVPI = getValueByName("RVPI") || this.RVPI;
        }
      
        this.dashboarddata = expandoObject;
        this.TVPIByVintageYear = expandoObject.TVPIbyVintageYearofInvestor || [];
        this.sectorData = expandoObject.sectorwise || [];
        this.topCompanyData = topCompaniesTotalValue.map(({ companyName, totalValue }) => ({ 'Company Name': companyName, 'Total Value': totalValue }));
        this.isData = false;
      }
    },
    error:(error) => {
      this.isData = false;
    }});
  }
  loadModelDefault(val: any, event: any, date: any) {
    if (val) {
      if (date == 'FromDate') {
        this.FromDate = event;
        this.model.FromDate = new Date(event,0,2);
        if (event > this.ToDate) {
          this.ToDate =event;
          this.model.ToDate = (new Date(event,11,31));
        }
      } else {
        this.ToDate =event;
        this.model.ToDate = (new Date(event,11,31));
        if (event < this.FromDate) {
          this.FromDate = event;
          this.model.FromDate = (new Date(event,0,2));
        }
      }
    } else {
      this.model.FromDate = new Date(this.FromDate,0,2);
      this.model.ToDate = new Date();
    }
  }
}