import { AfterViewInit, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { PdfModel, PdfSourceModel } from './pdf.model';
import { environment } from 'src/environments/environment';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-pdf-preview',
  templateUrl: './pdf-preview.component.html',
  styleUrls: ['./pdf-preview.component.scss']
})
export class PdfPreviewComponent implements OnInit,AfterViewInit {
  @ViewChild('iframeRef') iframeRef!: ElementRef;
  previewUrl: string = environment.ingestion_url + '/#/preview';
  trustedPreviewUrl: SafeResourceUrl;
  isLoading = true;
  @Input() modules: PdfModel[] = [];
  @Input() pdfSource: PdfSourceModel = null;
  @Output() close = new EventEmitter<void>();
  constructor(private sanitizer: DomSanitizer) {
  }
  ngOnInit(): void {
    this.trustedPreviewUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.previewUrl + "?processId=" + this.pdfSource.processId);
  }
  onClose() {
    this.close.emit();
  }
  onIframeLoaded() {
    this.isLoading = false;
  }
  ngAfterViewInit(): void {
    const iframeEl = this.iframeRef.nativeElement;
    iframeEl.onload = () => {
      setTimeout(() => {
        // Create a structured message object
        const messageData = {
          type: 'pdf-data',
          processId: this.pdfSource.processId,
          fileName: this.pdfSource.fileName,
          modules: this.pdfSource.pageList
        };
        
        try {
          const targetOrigin = window.location.origin; // This gives the protocol, hostname, and port
            // Make sure iframe content window is available
          if (iframeEl.contentWindow) {
            iframeEl.contentWindow.postMessage(messageData, targetOrigin);
            setTimeout(() => {
              iframeEl.contentWindow.postMessage(messageData, '*');
            }, 100);
          } else {
            console.error('iframe contentWindow is not available');
          }
        } catch (error) {
          console.error('Error posting message to iframe:', error);
        }
      }, 500); // Wait 500ms to ensure iframe content is fully initialized
    };
  }
}
