/* File History Popup Styles */
:host {
  display: contents;
}

/* Popup container */
.file-history-popup {
  border-radius: 8px;
  overflow: visible;
  z-index: 10000;
  *::-webkit-scrollbar {
  width: 5px;
  }

*::-webkit-scrollbar-track {
  background: #f1f1f1;
}
  // &::before {
  //   content: "";
  //   position: absolute;
  //   top: -8px;
  //   right: 12px;
  //   width: 16px;
  //   height: 16px;
  //   background-color: #ffffff;
  //   box-shadow: -1px -1px 3px rgba(0, 0, 0, 0.05);
  //   transform: rotate(45deg);
  // }
}

.file-history-popup-container {
  background-color: #fff;
  width: 400px;
  max-height: 750px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* File History Menu */
.file-history-menu {
  display: flex;
  flex-direction: column;
  max-height: 750px;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  z-index: 2;
}
.file-progress{
  .k-progress-status{
    display: none !important;
  }
}
/* Header */
.file-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #fff;

  h2 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e2842;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .view-all-link {
      font-size: 13px;
      color: #4061c7;
      cursor: pointer;
      text-decoration: none;
      font-weight: 500;
      
      &:hover {
        text-decoration: underline;
      }
    }

    .close-btn {
      color: #6c757d;
      padding: 4px;

      &:hover {
        color: #343a40;
      }
    }
  }
}

/* Filter Section */
.filter-section {
  padding: 16px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #e6e6e6;

  .filter-form {
    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      
      kendo-formfield {
        flex: 1;
      }

      &.filter-actions {
        justify-content: flex-end;
        margin-bottom: 0;
        
        button {
          margin-left: 8px;
        }
      }
    }
  }
}

/* Empty State */
.no-files {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  
  .empty-state {
    text-align: center;
    color: #666;
    
    kendo-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }
    
    p {
      margin: 0;
    }
  }
}

/* File Groups Container */
.file-groups-container {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  max-height: calc(100vh - 250px); /* Ensure it doesn't grow too large */
}

/* File Group */
.file-group {
  background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.0784313725);
    border: 1px solid #E6E6E6;
    border-radius: 8px;
    opacity: 1;
    margin: 0px 12px 12px 12px;
    overflow: hidden;

    .custom-border-r-all {
    border-radius: 8px;
    border-bottom: 0px !important;
  }
  .custom-border-r {
    border-radius: 8px 8px 0px 0px;
  }
  .group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    background-color: #f8f9fa;
    border-bottom: 1px solid #E6E6E6;
    &:hover {
      background-color: #f1f3f5;
    }
    
    .group-title {
      font-weight: 500;
      color: #1e2842;
      font-size: 14px;
    }
    
    .group-info {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #6c757d;
      font-size: 12px;
      
      .file-count {
        color: #6c757d;
      }

      kendo-icon {
        font-size: 12px;
      }
    }
  }
  
  .group-content {
    max-height: 300px;
    overflow-y: auto;
    padding: 0;
  }
}

/* File Item */
.file-item {
  border-bottom: 1px solid #f1f3f5;
  background-color: #ffffff;
  
  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f8f9fa;
  }
  
  .file-row {
    display: flex;
    padding: 12px 16px;
    align-items: flex-start;
    
    .file-icon {
      margin-right: 12px;
      margin-top: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &.success {
        color: #22c55e;
      }
      
      &.in-progress {
        color: #0d6efd;
      }
      
      &.failed {
        color: #dc3545;
      }

      kendo-icon {
        font-size: 16px;
      }
    }
    
    .file-details {
      flex: 1;
      min-width: 0;
      
      .file-name {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        color: #1e2842;
        margin-bottom: 4px;
        font-weight: 500;
        
        kendo-icon {
          font-size: 14px;
          color: #6c757d;
        }
        
        span {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      
      .file-meta {
        font-size: 11px;
        color: #6c757d;
        display: flex;
        align-items: center;
        gap: 16px;
        
        .upload-date {
          color: #adb5bd;
        }
      }
      
      .progress-container {
        margin: 8px 0;
        display: flex;
        align-items: center;
        gap: 12px;
        
        kendo-progressbar {
          flex: 1;
          height: 4px;
          
          ::ng-deep .k-progressbar-track {
            background-color: #e9ecef;
          }
          
          ::ng-deep .k-progressbar-fill {
            background-color: #0d6efd;
          }
        }
        
        .progress-text {
          font-size: 11px;
          color: #6c757d;
          white-space: nowrap;
        }
      }
      
      .file-message {
        font-size: 11px;
        color: #6c757d;
        margin-top: 4px;
      }
    }
    
    .file-actions {
      margin-left: 8px;
      display: flex;
      
      button {
        color: #6c757d;
        padding: 4px;
        
        &:hover {
          color: #343a40;
        }

        ::ng-deep .k-icon {
          font-size: 14px;
        }
      }
    }
  }
}

/* Filter Toggle Button */
.filter-toggle {
  padding: 10px 16px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: center;
  background-color: #ffffff;
  
  button {
    color: #4061c7;
    font-size: 13px;
    
    ::ng-deep .k-icon {
      font-size: 14px;
      margin-right: 4px;
    }
    
    &:hover {
      color: #324b96;
    }
  }
}

/* Filter Section */
.filter-section {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;

  .filter-form {
    .form-row {
      display: flex;
      gap: 12px;
      margin-bottom: 12px;
      
      kendo-formfield {
        flex: 1;
        
        ::ng-deep .k-label {
          font-size: 12px;
          color: #495057;
          margin-bottom: 4px;
        }
        
        ::ng-deep .k-dropdown,
        ::ng-deep .k-multiselect,
        ::ng-deep .k-datepicker {
          width: 100%;
          font-size: 13px;
        }
      }

      &.filter-actions {
        justify-content: flex-end;
        margin-bottom: 0;
        margin-top: 16px;
        
        button {
          font-size: 13px;
          padding: 6px 12px;
          
          &:first-child {
            background-color: #4061c7;
          }
        }
      }
    }
  }
}

/* Success, Error Icon Colors */
.success-icon {
  color: #22c55e;
}

.error-icon {
  color: #dc3545;
}

/* Empty State */
.no-files {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  
  .empty-state {
    text-align: center;
    color: #6c757d;
    
    kendo-icon {
      font-size: 40px;
      margin-bottom: 12px;
      color: #adb5bd;
    }
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

img[src="assets/dist/images/file-loader.svg"] {
  animation: spin 1s linear infinite;
}