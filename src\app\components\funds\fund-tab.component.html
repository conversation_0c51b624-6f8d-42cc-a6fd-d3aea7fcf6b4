<div class="fund-details-container row ">
    <div class="col-12 pl-0 pr-0 pt-1 pb-1 tab-container">
        <nep-tab id="neptab" class="custom-pipeline-tab custom-ds-tab" [tabList]="tabList"
            (OnSelectTab)="switchTab($event)">
        </nep-tab>
    </div>

    <ng-container id="portfolio-company-detials" *ngIf="tabName === 'Fund Details'">
        <div class="col-12 pt-2 pl-4 pr-4 show-border">
            <fund-details [fundId]="id"></fund-details>
        </div>
    </ng-container>

    <ng-container id="document-container" *ngIf="tabName === 'Documents'">
        <div class="col-md-3 pl-0 pr-0 folder-container">
            <app-pcdocument-folders [PortfolioCompanyId]="id" [documentsFieldPageConfig]="documentsFieldPageConfig" (folderSelected)="onFolderSelected($event)" [IsFund]="true"></app-pcdocument-folders>
        </div>

        <div class="col-md-9 pl-0 pr-0 doc-container">
            <app-pcdocument-list [PortfolioCompanyId]="id" [canEdit]="canEditDocuments" [documentsFieldPageConfig]="documentsFieldPageConfig" [IsFund]="true" #documentList></app-pcdocument-list>
        </div>
    </ng-container>
</div>