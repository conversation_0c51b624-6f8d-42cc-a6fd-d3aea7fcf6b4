import { ChangeDetectorR<PERSON>, Component, ElementRef, OnInit, QueryList, ViewChild, ViewChildren, ViewEncapsulation } from '@angular/core';
import { FormGroupDirective } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { PanelbarItemService } from '../shared/panelbar-item/panelbar-item.service';
import { DropDownFilterSettings } from '@progress/kendo-angular-dropdowns';
import { CloListService } from '../clo-list/clo-list.service';
import { CloService } from 'src/app/services/clo.service';
import { FeatureTableMapping } from 'src/app/common/constants';
interface PreviousValue {
  id: string;
  value: string;
}

@Component({
  selector: 'app-page-configuration',
  templateUrl: './page-configuration.component.html',
  styleUrls: ['./page-configuration.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class PageConfigurationComponent implements OnInit {
     pageList: any = [];
     pageListClone: any = [];
     subPageList: any = [];
     subPageListClone: any = [];
     selectedPageItem: any = {};
     selectedCompanyItem:any = {};
     selectedCLOItem:any = {};
     pageDropdownOptions: any = [];
     isAddFieldButtonClicked: boolean = false;
     @ViewChildren("sectioncontent") sectionContent: QueryList<ElementRef>;
     @ViewChild("f") f: FormGroupDirective;
     isLoader: boolean = false;
     lastSelectedPageItem: any = {};
     isPopup = false;
     modalTitle = "Confirmation";
     isDisabledBtn: boolean = true;
     temp: number;
     disablePageList = false;
     status: boolean = false;
     disabled: boolean;
     selectedPageType:number=0;
     selectedPageTypeName:string="";
     compnayList:any[];
     cloPageList: any = [];
     cloList: any = [];
     public virtual: any = {
      itemHeight: 40,
      pageSize: 20
    };
    public filterSettings: DropDownFilterSettings = {
        caseSensitive: false,
        operator: 'contains',
      };
      CLOModel: any=[];
      public TAB_NAMES = {
        CLO_Versus_CLO_Sector:14,
      };
     constructor(
       private panelbarItemService:PanelbarItemService,
       private readonly toastrService: ToastrService,
       private cdr: ChangeDetectorRef,
       private cloListService: CloListService,
       private readonly viewCloService: CloService,
     ) {
       
     }
     ngOnInit() {
      this.getInvestCompanies();
      this.getCLOPageDetails();
      this.panelbarItemService.getCompany().subscribe((data)=>{
        this.selectedCompanyItem=data;
      })
      this.panelbarItemService.getCLOPageDetails().subscribe((data)=>{
        this.pageDropdownOptions=data;
        this.selectedPageItem = this.pageDropdownOptions[0];
       this.selectedPageTypeName=this.pageDropdownOptions[0]?.name;
       if (Object.keys(this.lastSelectedPageItem).length != 0) {
         this.selectedPageItem = this.lastSelectedPageItem;
       }
       this.getConfiguration();
      })
     }
    
     OnCancel(e: any) {
       this.isPopup = false;
     }
     OnConfig(e: any) {
       this.save();
     }
     loadPopup() {
       this.isPopup = true;
       this.cdr.detectChanges();
     }
     ngAfterViewInit() {
       this.sectionContent.changes.subscribe(() => {
         this.sectionContent.forEach((row) => {
           let inputTextEle = row.nativeElement.querySelector("[type=text]");
           let labelEle = row.nativeElement.querySelector("label");
           inputTextEle.addEventListener("focus", () => {
             labelEle?.classList.toggle("active");
           });
           inputTextEle.addEventListener("blur", () => {
             labelEle?.classList.toggle("active");
           });
         });
         if (this.sectionContent && this.sectionContent.last) {
           this.isAddFieldButtonClicked &&
             this.sectionContent.last.nativeElement
               .querySelector("[type=text]")
               .focus();
           this.isAddFieldButtonClicked = false;
         }
       });
     }

     checkAnyDataChange(event:any) {
      this.isDisabledBtn = event.isDisabledBtn;
      this.subPageList=event.subPageList;
    }
     
     previousValues: PreviousValue[] = [];
     getConfiguration() {
      
    
      const companyId = this.selectedCompanyItem?.id || 0;
      const cloId = this.selectedCLOItem?.clO_ID || 0;
    
      if (this.selectedPageItem?.pageID != undefined && companyId!=0) {
        this.isLoader = true;
      this.isDisabledBtn = true;
        this.panelbarItemService.getTabList(this.selectedPageItem.pageID, companyId, cloId).subscribe({
          next: (data: any) => {
            if (!data) return;
    
            let result = this.transformSubPageDetailList([...data]);
            const parentIndex = result.findIndex(x => x.subTabList?.length > 0);
    
            if (parentIndex >= 0) {
              const subTabIndex = result[parentIndex].subTabList.findIndex(x => x.tabId == this.TAB_NAMES.CLO_Versus_CLO_Sector);
              if (subTabIndex >= 0) {
                const subTab = result[parentIndex].subTabList[subTabIndex];
                subTab.name = subTab.name.replace('Domicile', this.CLOModel?.domicile);
                if (subTab.id == 0) {
                  subTab.aliasName = subTab.name;
                }
              }
            }
    
            if(result){
              this.pageList = result;
            this.pageList?.forEach(page => {
              this.panelbarItemService.updateTableVisibility(page.tableList);
              page.subTabList?.forEach(tab => {
                this.panelbarItemService.updateTableVisibility(tab.tableList);
              });
            });
    
            this.pageListClone = JSON.parse(JSON.stringify(this.pageList));
            this.subPageList = this.pageList;
            this.subPageListClone = this.pageListClone;
            }
            else{
              console.error('list is undefined or null');
            }
            this.isLoader = false;
          },
          error: () => {
            this.isLoader = false;
          }
        });
      }
    }
  
     checkListPageToggle(list) {
       if (list != undefined && list.length >= 4) {
         this.disablePageList = true;
       } else {
         this.disablePageList = false;
       }
     }

     parseJsonResponse = (result: any[]) => {
       if (result == null || undefined) return;
       this.pageList = result;
       this.pageListClone = JSON.parse(JSON.stringify(this.pageList));
     };

     collectionHas(a, b) {
       for (let i = 0, len = a.length; i < len; i++) {
         if (a[i] == b) return true;
       }
       return false;
     }

     findParentBySelector(elm, selector) {
       let all = document.querySelectorAll(selector);
       let cur = elm.parentNode;
       while (cur && !this.collectionHas(all, cur)) {
         cur = cur.parentNode;
       }
       return cur;
     }

     transformSubPageDetailList(subPageDetailList: any[]): any[] {
      return subPageDetailList?.map((x) => {
        const transformedItem = Object.assign(x, {
          isTabExpanded: false,
          isCustomFieldSupported: false,
        });
  
        if (x?.subTabList && x?.subTabList.length > 0) {
          transformedItem.subTabList = this.transformSubPageDetailList(x.subTabList);
        }
        if (x?.tableList && x?.tableList.length > 0) {
          transformedItem.tableList = this.transformSubPageDetailList(x.tableList);
        }
  
        return transformedItem;
      });
    }

     previousVal: any;
     currentVal: any;
     
     save() {
      this.isPopup = false;
    
      const updateCloId = (list) => {
        list?.forEach(item => {
          item.cloId = this.selectedCLOItem.clO_ID;
          item.tableList?.forEach(table => table.cloId = this.selectedCLOItem.clO_ID);
        });
      };
    
      this.subPageList?.forEach(element => {
        element.companyId = element.companyId || this.selectedCompanyItem.id;
    
        if (this.selectedCLOItem) {
          element.cloId = this.selectedCLOItem.clO_ID;
          updateCloId(element.subTabList);
          updateCloId(element.tableList);
        }
      });
    
      this.updateNestedItem();
    
      this.panelbarItemService.saveTabDetails(this.subPageList).subscribe({
        next: (data) => {
          if (data.success) {
            this.getConfiguration();
            this.toastrService.success(data.message, "", { positionClass: "toast-center-center" });
          } else {
            this.toastrService.error(data.message, "", { positionClass: "toast-center-center" });
          }
        },
        error: (error) => {
          this.toastrService.error(error.message, "", { positionClass: "toast-center-center" });
        }
      });
    }
     reset = () => {
       this.getConfiguration();
       this.isDisabledBtn = true;
     };
     isDefaultItem(item: any): boolean {
      return item.id === null;
    }


    updateIds = (tableList: any[]) => {
      if (!tableList) return;
    
      const toBeChange = FeatureTableMapping.TOBE_CHANGE_DOMICILE_TABLES_NAME;
      const editChange = FeatureTableMapping.EDIT_DOMICILE_TABLES_NAME;
    
      Object.keys(toBeChange).forEach(key => {
        const editIndex = tableList.findIndex(x => x.tableId === editChange[key][0]);
        const changeIndex = tableList.findIndex(x => x.tableId === toBeChange[key][0]);
    
        if (editIndex >= 0 && changeIndex >= 0) {
          tableList[changeIndex].aliasName = tableList[editIndex].aliasName;
          tableList[changeIndex].sequenceNo = tableList[editIndex].sequenceNo;
        }
      });
    };


    updateNestedItem() {
      this.subPageList.forEach(page => {
        this.updateIds(page.tableList);
        page.subTabList.forEach(tab => {
          this.updateIds(tab.tableList);
        });
      });
    }

    
   onPageDdSelectionChange(event)  {
    this.subPageList=this.pageList;
     this.isLoader = true;
     if (event != undefined) {
       this.selectedPageItem = event;
     
     this.lastSelectedPageItem = event;
     this.selectedCompanyItem=null;
     this.subPageList=[];
        }
     this.isLoader = false;
   }
   onCompanyDdSelectionChange(event) {
    
    this.subPageList=this.pageList;
    if (event) {
      this.isLoader = true;
      this.previousVal = this.currentVal;
      this.currentVal = event;
    }
    this.subPageList=[];
    if (event != undefined) {
      this.selectedCompanyItem = event;
      this.panelbarItemService.setCompany(event);
      this.selectedCLOItem=null;
     
     if(this.selectedPageItem.pageID==2)
      this.getCLOList();
     else if(this.selectedPageItem.pageID!=2)
      this.getConfiguration();
    }
    
  };
  onCLODdSelectionChange(event) {
    this.subPageList=[];
    
    if (event) {
      this.isLoader = true;
      this.previousVal = this.currentVal;
      this.currentVal = event;
    }
    if (event != undefined) {
      this.selectedCLOItem = event;
      this.configureDomicile();
    }
    
  };
  getInvestCompanies(){
    this.isLoader = true;
    this.panelbarItemService.getInvestCompanyList().subscribe((data) => {
      this.isLoader = false;
        if(data==null){
          return;
        }
        this.compnayList=data;
        this.selectedCompanyItem = data[0];
        this.panelbarItemService.setCompany(this.selectedCompanyItem); 
        this.getConfiguration();
      
    });
  }
  getCLOPageDetails(){
    this.panelbarItemService.getCLOPageDetails().subscribe((data) => {
      if(data.length == 0){
        return;
      }
      this.cloPageList=data;
      
    
  });
  }
  getCLOList(){
    this.isLoader = true;
    this.cloListService.getClos(this.selectedCompanyItem.id).subscribe(
      (response) => {   
                 this.isLoader = false;    
        this.cloList = Array.isArray(response) ? response : []; 
      },
      error => {
        this.isLoader = false;
        console.error('Error fetching CLO', error);
      }
    );
  }

configureDomicile(){
  this.isLoader = true;
  this.viewCloService.getCloById(this.selectedCLOItem?.uniqueID).subscribe({
        next: (data) => { 
          this.isLoader = false;
          this.CLOModel=data;  
          this.getConfiguration();
        },
        error: (error) => {
          console.error('Error fetching investment company list', error);
        }
      });
}
}