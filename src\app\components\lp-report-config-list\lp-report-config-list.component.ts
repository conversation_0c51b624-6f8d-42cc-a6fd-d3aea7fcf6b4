import { Component, OnInit } from '@angular/core';
import { SVGIcon, searchIcon } from "@progress/kendo-svg-icons";
import { Observable, filter, of } from "rxjs";
import { SortDescriptor, State } from "@progress/kendo-data-query";
import { GridDataResult } from "@progress/kendo-angular-grid";
import { CompositeFilterDescriptor, filterBy } from '@progress/kendo-data-query';
import { LpReportConfigService } from 'src/app/services/lp-report-config.service';
import { Router } from '@angular/router';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { DialogAction } from "@progress/kendo-angular-dialog";
import { ToastrService } from "ngx-toastr";
@Component({
  selector: "app-lp-report-config-list",
  templateUrl: "./lp-report-config-list.component.html",
  styleUrls: ["./lp-report-config-list.component.scss"],
})
export class LpReportConfigListComponent implements OnInit {
  searchSVG: SVGIcon = searchIcon;
  isLoader:boolean = false;
  gridFilter: CompositeFilterDescriptor;
  public view: Observable<GridDataResult>;
  globalFilter: string = "";
  sort: SortDescriptor[] = [];
  templateList: any = [];
  templateListClone: any = [];
  tabName: string;
  tabList: ITab[] = [];
  deleteLpTemplate: boolean = false;
  deletePopupLayout: string = "end";
  templateToDelete: any = {};
  public deleteActions: DialogAction[] = [
      { text: "Cancel" },
      { text: "Confirm", themeColor: "primary" },
    ];
  constructor(
    private lpReportConfigService: LpReportConfigService,
    private router: Router,
    public toastrService: ToastrService,
  ) {}

  ngOnInit(): void {
    this.getTemplates();
    this.getTabList();
  }
  createTemplate() {}
  getTemplates() {
    this.isLoader = true;
    this.lpReportConfigService.getLpReportTemplates().subscribe({
      next: (data) => {
        this.templateList = data;
        this.templateListClone = data;
        this.isLoader = false;
      },
      error: (err) => {
        console.error("Error fetching templates:", err);
        this.templateList = [];
        this.templateListClone = [];
        this.isLoader = false;
      },
    });
  }

  canDeactivate() : boolean {
    return !this.lpReportConfigService.isSaveEnabled;
  }

  onTabClick(tab: ITab) {
    if (tab != null || tab != undefined) {
      this.tabName = tab.name;
    }
  }

  getTabList() {
      this.tabList = [
        {
          active: true,
          name: "Template List",
        },
        {
          active: false,
          name: "Cover Page Template",
        },
      ];
      this.tabName = 'Template List';
  }
  
  filterGrid(value: string) {
    if (value) {
      this.gridFilter = {
        logic: "or",
        filters: [
          { field: "templateName", operator: "contains", value: value },
        ],
      };
      this.templateList = filterBy(this.templateListClone, this.gridFilter);
    } else {
      this.templateList = [...this.templateListClone];
    }
  }
  redirectToTemplate(template:any) {
    if(template == null){
      localStorage.setItem("headerName","New Template");
      this.router.navigate(["/lp-report-template"]);
    }
    else{
      localStorage.setItem("headerName",template.templateName);
      this.router.navigate(["/lp-report-template", template.encryptedTemplateId]);
    }
  }

  /**
   * Opens the delete template confirmation dialog.
   * Sets the template to be deleted and displays the delete confirmation modal.
   *
   * @param template - The template object to be deleted. If null, the method does nothing.
   */
  openDeleteTemplate(template:any) {
    if(template != null){
      this.templateToDelete = template;
      this.deleteLpTemplate = true;
    }
  }

  /**
   * Closes the delete template dialog and resets the template to delete.
   *
   * @param status - A string representing the status of the delete operation.
   */
  closeDeleteTemplate(status: string): void {
    this.deleteLpTemplate = false;
    this.templateToDelete = {};
  }

  /**
   * Handles the deletion of a template based on the provided dialog action.
   * 
   * @param action - The dialog action object containing the user's choice (e.g., "Confirm" or "Cancel").
   * 
   * - If the action text is "Confirm" and a template is selected for deletion (`templateToDelete`),
   *   the method triggers the deletion process by calling the `deleteLpReportTemplates` service.
   *   Upon successful deletion, it resets the deletion state and refreshes the template list.
   * 
   * - If the action text is "Cancel", it closes the delete template dialog without performing any action.
   */
  deleteTemplate(action: DialogAction): void {
    if (action.text === "Confirm" && this.templateToDelete != null && this.deleteLpTemplate) {
      this.isLoader = true;
      this.lpReportConfigService.deleteLpReportTemplates(this.templateToDelete?.encryptedTemplateId).subscribe({
        next: (data) => {
          if (data) { 
            this.toastrService.success("Template deleted successfully", "", { positionClass: "toast-center-center" });
            this.deleteLpTemplate = false;
            this.templateToDelete = {};
            this.getTemplates();
          }
          else{
            this.toastrService.error("Template deletion failed", "", { positionClass: "toast-center-center" });
          }
          this.isLoader = false;
        },
        error: (err) => {
          this.isLoader = false;
        },
      });
    }
    if(action.text === "Cancel"){
      this.closeDeleteTemplate(action.text);
    }
  }
}
