import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { AngularResizeEventModule } from 'angular-resize-event';
import { QuillModule } from 'ngx-quill';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { CloListComponent } from './clo-list.component';
import { InvestCompanyService } from 'src/app/components/clo/investmentcompany/investmentcompany.service';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';

@NgModule({
  declarations: [
    CloListComponent // Declare CloListComponent here
    
  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MaterialModule,
    AngularResizeEventModule,
    QuillModule,
    SharedDirectiveModule,
    ReactiveFormsModule, // Import ReactiveFormsModule here
        SharedComponentModule,

    RouterModule.forChild([
        { path: '', component: CloListComponent}
    ]),
    KendoModule
  ],
  providers: [
    InvestCompanyService
  ],
  exports: [
    CloListComponent
  ]
})
export class CloListModule { }