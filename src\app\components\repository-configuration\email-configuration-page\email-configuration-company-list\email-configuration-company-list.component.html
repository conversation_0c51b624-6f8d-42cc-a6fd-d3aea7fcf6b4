<div class="email-company-container">
    <div class="list-group">
        <div class="search-box-container list-group-item d-flex align-items-center pl-3 pr-3 all-company">
            <input class="list-group-item all-company flex-grow-1 border-0 p-0 mb-0" type="text"
                   placeholder="Company List" [(ngModel)]="searchTerm"
                   (change)="onInputChange($event)" />
            <img src="assets/dist/images/FiSearch.svg" alt="Search" class="company-logo ml-2">
        </div>
        <div class="companies-list-container">
            <div *ngFor="let company of (searchTerm ? searchedCompanies : companies); let i = index" 
                 [ngClass]="{'list-group-item d-flex align-items-center pl-3 repo-confis Body-R': true, 'selected-company': company.selected}"
                 (click)="toggleCompanySelection(company)">
                <div class="TextTruncate" title="{{ company.name }}">{{ company.name }}</div>
            </div>
            <div *ngIf="(searchTerm ? searchedCompanies : companies).length === 0 && !isLoader" 
                 class="list-group-item d-flex justify-content-center align-items-center">
                No companies found
            </div>
        </div>
    </div>
    <app-loader-component *ngIf="isLoader"></app-loader-component>
</div>