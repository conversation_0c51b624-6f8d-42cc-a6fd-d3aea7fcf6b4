import { Component, EventEmitter, Input, ViewChild } from '@angular/core';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { ImpactKPIConstants, KpiTypes, NumberDecimalConst, PeriodType, GlobalConstants, InvestmentKpiConstants, KpiTypesConstants } from 'src/app/common/constants';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { commonConvertUnits, feed, setCommonDefaultTypeTab, setCommonFilterOptionsKeys } from 'src/app/utils/kpi-utility';
import { MatMenu, MatMenuTrigger } from "@angular/material/menu";
import { ActionsEnum, KPIModulesEnum, PermissionService, UserSubFeaturesEnum } from 'src/app/services/permission.service';
import { ToastContainerDirective, ToastrService } from "ngx-toastr";
import { Table } from "primeng/table";
import { isNumeric } from "@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric";
import { DecimalDigitEnum, ErrorMessage, FinancialValueUnitsEnum, MiscellaneousService, OrderTypesEnum, PeriodTypeQuarterEnum } from 'src/app/services/miscellaneous.service';
import { filter } from 'rxjs';
import { OidcAuthService } from "src/app/services/oidc-auth.service";
import { extractDateComponents } from "src/app/components/file-uploads/kpi-cell-edit/cell-edit-utils";
import { ModuleCompanyModel, TableHeader, Audit, MappedDocuments } from "src/app/components/file-uploads/kpi-cell-edit/kpiValueModel";
import { AuditService } from "src/app/services/audit.service";
import { DatePipe } from '@angular/common';
import { getConversionErrorMessage } from 'src/app/utils/utils';
@Component({
  selector: 'app-investment-kpi-beta',
  templateUrl: './investment-kpi-beta.component.html',
  styleUrls: ['./investment-kpi-beta.component.scss', '../master-kpi-beta/master-kpi-beta.component.scss']
})
export class InvestmentKpiBetaComponent {

  subFeature: typeof UserSubFeaturesEnum = UserSubFeaturesEnum;
  kpiModuleId = KPIModulesEnum.Investment;
  @ViewChild('dt') dt: Table | undefined;
  searchFilter: any = null;
  frozenCols: any = [{ field: "KPI", header: "KPI" }];
  model: any = {};
  tableReload = false;
  globalFilter: string;
  NumberDecimalConst = NumberDecimalConst;
  @ViewChild(ToastContainerDirective, {})
  toastContainer: ToastContainerDirective;
  isLoader: boolean = false;
  @ViewChild('menu') uiUxMenu!: MatMenu;
  @ViewChild('impactMenuTrigger') menuTrigger: MatMenuTrigger;
  @Input() pageConfigData = [{ kpiConfigurationData: [], hasChart: false, kpiType: "" }];
  @Input() investmentKPIPermissions: any = [];
  @Input() modelList: any;
  subSectionFields = [];
  pageConfigResponse = { kpiConfigurationData: [], hasChart: false, kpiType: "" };
  defaultType: string = ImpactKPIConstants.Monthly;
  tabValueTypeList: ITab[] = [];
  tabName: string = "";
  isMonthly: boolean = true;
  isQuarterly: boolean = false;
  isAnnually: boolean = false;
  loading = false;
  filterOptions: any[] = [];
  tableColumns = [];
  tableResult = [];
  tableResultClone = [];
  isPageLoad = true;
  modelInvestmentKpi: any = {};
  investmentKpiValueUnit: any;
  tableFrozenColumns = [];
  investmentKpiSearchFilter: any;
  kpiFilterCols: any = [];
  exportLoading: boolean = false;
  isValueUpdated: boolean = false;
  auditLogList: any = [];
  ErrorNotation: boolean = false;
  infoUpdate: boolean = false;
  id: any;
  financialKPIMultiSortMeta: any[] = [
    { field: "year", order: -1 },
    { field: "month", order: -1 },
  ];
  message: any;
  exportMasterKPILoading: boolean = false;
  kpiCurrencyFilterModel: any = {};
  isValueConverted: boolean = false;
  auditLogErrorForConvertedValue: string = GlobalConstants.AuditLogErrorForConvertedValue;
  editErrorForConvertedValue: string = GlobalConstants.EditgErrorForConvertedValue;
  editSpotRateConversionError: string = GlobalConstants.EditSpotRateConversionError;
  auditLogSpotRateConversionError: string = GlobalConstants.AuditLogSpotRateConversionError;
  auditLogTitle: string = GlobalConstants.AuditLogTitle;
  constructor(
    private portfolioCompanyService: PortfolioCompanyService,
    private miscService: MiscellaneousService,
    private toastrService: ToastrService,
    private identityService: OidcAuthService,
    private auditService: AuditService,
    private permissionService: PermissionService,
    private datePipe: DatePipe
  ) {

    this.modelInvestmentKpi.periodType = {
      type: PeriodTypeQuarterEnum.Last1Year,
    };
    this.investmentKpiValueUnit = {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    };
 
    this.modelInvestmentKpi.orderType = { type: OrderTypesEnum.LatestOnRight };
    this.modelInvestmentKpi.decimalPlaces = {
      type: DecimalDigitEnum.Zero,
      value: "1.0-0",
    };
  }
  ngAfterViewInit() {
    if (this.uiUxMenu != undefined) {
      (this.uiUxMenu as any).closed = this.uiUxMenu.closed
      this.configureMenuClose(this.uiUxMenu.closed);
    }
  }
  configureMenuClose(old: MatMenu['close']): MatMenu['close'] {
    const upd = new EventEmitter();
    feed(upd.pipe(
      filter(event => {
        if (event === 'click') {
          return false;
        }
        return true;
      }),
    ), old);
    return upd;
  }
  ngOnInit() {
    this.pageConfigResponse = this.pageConfigData.find(x => x.kpiType == KpiTypes.InvestmentBeta.type);
    this.subSectionFields = this.pageConfigResponse?.kpiConfigurationData;
    this.getValueTypeTabList();
  }
  isNumberCheck(str: any) {
    return isNumeric(str);
  }
  getValueTypeTabList() {
    this.portfolioCompanyService.getfinancialsvalueTypes().subscribe((x) => {
      let tabList = x.body?.financialTypesModelList;
      let pageConfigTabs = this.subSectionFields;
      tabList = tabList?.filter((item: any) => pageConfigTabs?.some((otherItem: any) => item.name === otherItem.aliasName && otherItem.chartValue.length > 0));
      if (tabList != undefined && tabList.length > 0) {
        this.tabValueTypeList = tabList;
        this.tabValueTypeList[0].active = true;
        this.tabName = this.tabValueTypeList[0].name;
        this.setPeriodsOptions(pageConfigTabs);
      }
    });
  }
  setPeriodsOptions(pageConfigTabs: any[]) {
    let periodOptions = PeriodType.filterOptions;
    let activeTabData = pageConfigTabs?.find(x => x.aliasName == this.tabName);
    this.filterOptions = periodOptions?.filter(item => activeTabData?.chartValue?.some(otherItem => otherItem === item.field));
    let periodType = this.filterOptions.find(x => x.key);
    if (periodType == undefined && this.filterOptions.length > 0) {
      for (const element of periodOptions) {
        element.key = false;
      }
      this.filterOptions[0].key = true;
      periodType = this.filterOptions[0];
    }
    this.onChangePeriodOption(periodType);
    this.getPortfolioCompanyMasterKPIValues(null);
  }
  onChangePeriodOption(type) {
    this.filterOptions.forEach((x) => (x.key = false));
    if (type?.field == ImpactKPIConstants.Monthly) {
      type.key = this.isMonthly = true;
      this.isQuarterly = false;
      this.isAnnually = false;
    } else if (type?.field == ImpactKPIConstants.Quarterly) {
      this.isMonthly = false;
      type.key = this.isQuarterly = true;
      this.isAnnually = false;
    } else {
      this.isMonthly = false;
      this.isQuarterly = false;
      if (type != undefined)
        type.key = this.isAnnually = true;
    }
    this.setDefaultTypeTab();
    this.getPortfolioCompanyMasterKPIValues(null);
  }
  setDefaultTypeTab = () => {
    this.defaultType = setCommonDefaultTypeTab(this.isMonthly, this.isQuarterly);
  }
  selectValueTab(tab: ITab) {
    this.tabValueTypeList.forEach((tab) => (tab.active = false));
    tab.active = true;
    this.tabName = tab.name;
    this.setPeriodsOptions(this.subSectionFields);
    this.getPortfolioCompanyMasterKPIValues(null);
  }
  SetFilterOptionsKeys(result: any) {
    this.filterOptions = setCommonFilterOptionsKeys(this.filterOptions, result);
    this.setDefaultTypeTab();
  }
  clearData() {
    this.loading = false;
    this.isLoader = false;
    this.tableColumns = [];
    this.tableResult = [];
    this.tableResultClone = [];
    this.isPageLoad = false;
  }
  kpiTable_GlobalFilter(event) {
    this.investmentKpiValueUnit = event?.UnitType == undefined ? {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    } : event?.UnitType;
    this.searchFilter = event;
    this.kpiCurrencyFilterModel = event;
    if(this.modelList?.reportingCurrencyDetail?.currencyCode != null && event?.currencyCode != null && this.modelList?.reportingCurrencyDetail?.currencyCode != event?.currencyCode){
      this.isValueConverted = true;
    }
    else{
      this.isValueConverted = false;
    }
    this.getPortfolioCompanyMasterKPIValues(null);
    this.menuTrigger.closeMenu();
  }
  getPortfolioCompanyMasterKPIValues(event: any) {
    this.isLoader = true;
    let searchFilter = this.searchFilter || {
      periodType: this.modelInvestmentKpi.periodType.type,
    };

    if (searchFilter.periodType == ImpactKPIConstants.DateRange) {
      const periodSource = searchFilter.startPeriod ? searchFilter : this.modelInvestmentKpi;
      searchFilter.startPeriod = this.miscService.createDateFromPeriod(periodSource.startPeriod);
      searchFilter.endPeriod = this.miscService.createDateFromPeriod(periodSource.endPeriod);
    }

    this.getInvestmentKPIValues(searchFilter, event);
  }

  getInvestmentKPIValues(searchFilter: any, event: any) {
    this.investmentKpiSearchFilter = searchFilter;
    this.portfolioCompanyService
      .getInvestmentKPIValues({
        portfolioCompanyID: this.modelList?.portfolioCompanyID,
        paginationFilter: event,
        searchFilter: searchFilter,
        valueType: this.tabValueTypeList.length == 0 ? "Actual" : this.tabName,
        isMonthly: this.isMonthly,
        isQuarterly: this.isQuarterly,
        isAnnually: this.isAnnually,
        isPageLoad: this.isPageLoad,
        moduleID: this.modelList?.moduleId,
        companyId: this.modelList?.portfolioCompanyID?.toString(),
        kpiConfigurationData: this.pageConfigResponse?.kpiConfigurationData,
        IsSpotRate : this.kpiCurrencyFilterModel.isSpotRate == null ? false : this.kpiCurrencyFilterModel.isSpotRate,
        SpotRateDate:this.kpiCurrencyFilterModel.isSpotRate ? this.datePipe.transform(this.kpiCurrencyFilterModel.spotRateDate, 'yyyy-MM-dd') : null,
        currencyCode: this.kpiCurrencyFilterModel?.currencyCode,
        reportingCurrencyCode: this.modelList?.fundReportingCurrency?.currencyCode,
        currencyRateSource: this.kpiCurrencyFilterModel?.currencyRateSource,
      })
      .subscribe({
        next: (result) => {
          if (result != null) {
            this.loading = false;
            this.isLoader = false;
            this.ErrorNotation = false;
            this.tableReload = true;
            this.tableColumns = result?.headers || [];
            this.tableFrozenColumns = this.frozenCols;
            this.tableResult = result?.rows || [];
            this.tableResultClone = result?.rows || [];
            this.auditLogList = result?.companyKpiAuditLog || [];
            this.tableResult = commonConvertUnits(this.tableResult, this.investmentKpiValueUnit, this.tableResultClone, this.tableColumns);
            this.kpiFilterCols = [...this.tableFrozenColumns, ...this.tableColumns];
            this.isPageLoad = false;
            if (result != null) {
              this.isMonthly = result?.isMonthly;
              this.isQuarterly = result?.isQuarterly;
              this.isAnnually = result?.isAnnually;
              this.SetFilterOptionsKeys(result);
            }
          } else {
            this.clearData();
          }
        },
        error: (error) => {
          this.clearData();
        }
      });
  }

  /**
   * Handles the audit log functionality for a specific row in the investment KPI beta component.
   * @param rowData - The data of the row.
   * @param field - The field of the row.
   */
  onAuditLog(rowData: any, field: any) {
    sessionStorage.removeItem(InvestmentKpiConstants.InvestmentKpiAuditLocalStorage);
    if (!this.ErrorNotation || rowData.IsHeader) {
      if (this.ErrorNotation) {
        this.showErrorToast(this.auditLogErrorForConvertedValue);
      }
      return;
    }
    if (this.isConvertedValue(rowData)) {
      const message = getConversionErrorMessage(
        this.kpiCurrencyFilterModel.isSpotRate,
        this.auditLogSpotRateConversionError,
        this.auditLogErrorForConvertedValue
      );
      this.showErrorToast(message);
      return;
    }
    const dateComponents = extractDateComponents(field.header);
    let auditLogFilter = this.getAuditLogFilter(rowData, dateComponents);
    this.auditService.getPortfolioEditSupportingCommentsData(auditLogFilter).subscribe({
      next: (data: MappedDocuments) => {
        if (data?.auditLogId > 0 && data?.auditLogCount > 0) {
          let attributeName = rowData.KPI;
          this.redirectToAuditLogPage(field, attributeName, data, auditLogFilter);
        } else if (data?.auditLogCount == 0) {
          this.showErrorToast(GlobalConstants.AuditLogNAMessage);
        }
      },
      error: (error: any) => {
        this.showErrorToast(this.auditLogErrorForConvertedValue);
     },
    });
  }

  /**
  * Redirects to the audit log page with the specified parameters.
  * 
  * @param field - The field object.
  * @param attributeName - The attribute name.
  * @param data - The mapped documents data.
  * @param auditLogFilter - The audit log filter.
  */
  redirectToAuditLogPage(field: any, attributeName: any, data: MappedDocuments, auditLogFilter: Audit) {
    let routeData = {
      KPI: "Investment KPI Beta",
      header: field.header,
      PortfolioCompanyID: this.modelList.portfolioCompanyID,
      AttributeName: attributeName,
      ModuleId: KPIModulesEnum.Investment,
      Comments: this.tabName,
      currency: this.modelList?.reportingCurrencyDetail?.currencyCode,
      AttributeId: data.valueId,
      isNewAudit: true,
      KpiId: auditLogFilter.kpiId,
    };

    // Store the data in local storage or session storage
    sessionStorage.setItem(GlobalConstants.CurrentModule, KpiTypesConstants.INVESTMENT_KPI);
    sessionStorage.setItem(InvestmentKpiConstants.InvestmentKpiAuditLocalStorage, JSON.stringify(routeData));
    let config = this.identityService.getEnvironmentConfig();
    if (config.redirect_uri != "") {
      let myAppUrl = config.redirect_uri.split("/in")[0] + InvestmentKpiConstants.InvestmentKpiAuditUrl;
      window.open(myAppUrl, '_blank');
    }
  }

  /**
   * Returns an Audit object representing the filter criteria for the audit log.
   * @param rowData - The data of the row.
   * @param dateComponents - The date components (year, month, quarter).
   * @returns An Audit object with the filter criteria.
   */
  getAuditLogFilter(rowData: any, dateComponents: { year: any; month: number; quarter: any; }) {
    return <Audit>{
      valueType: this.tabName,
      kpiId: rowData["KpiId"],
      mappingId: rowData["MappingId"],
      quarter: dateComponents.quarter,
      year: dateComponents.year,
      month: dateComponents.month,
      moduleId: KPIModulesEnum.Investment,
      companyId: this.modelList.portfolioCompanyID,
    };
  }

  /**
   * Handles the change event for the error notation checkbox.
   * @param e - The event.
   */
  handleChange(e) {
    this.ErrorNotation = e;
  }

  /**
   * Displays an error toast message.
   * 
   * @param message - The error message to display.
   * @param title - The title of the error toast. (optional)
   * @param position - The position of the error toast. (optional)
   */
  showErrorToast(message: string, title: string = '', position: string = ImpactKPIConstants.ToastCenterCenter): void {
    this.toastrService.error(message, title, { positionClass: position });
  }
  isUploadPopupVisible: boolean = false;
  uniqueModuleCompany: ModuleCompanyModel;
  dataRow: object;
  dataColumns: TableHeader;
  cancelButtonEvent() {
    this.isUploadPopupVisible = false;
  }
  /**
   * Handles the initialization of the edit process for a given row and column.
   * 
   * @param rowData - The data of the row that is being edited.
   * @param column - The column that is being edited.
   * 
   * @remarks
   * This method performs several checks before allowing the edit process to proceed:
   * - Verifies if the user has the necessary permissions to edit.
   * - Checks for any error notations that might prevent editing.
   * - Ensures the user has permission to edit the specific row data.
   * - Prevents editing if the value is a converted value and shows a warning toast.
   * - Updates information or shows an upload popup based on the row data and column.
   * - Displays an error toast if none of the conditions for editing are met.
   */
  onEditInit(rowData: any, column: any) {
    if (!this.canEdit()) {
      this.showErrorToast(ErrorMessage.NoAccess);
      return;
    }

    if (this.hasErrorNotation()) {
      return;
    }

    if (!this.hasPermissionToEdit(rowData)) {
      return;
    }

    if (this.isConvertedValue(rowData)) {
      const message = getConversionErrorMessage(
        this.kpiCurrencyFilterModel.isSpotRate,
        this.editSpotRateConversionError,
        this.editErrorForConvertedValue
      );
      this.showErrorToast(message);
      return;
    }

    if (this.shouldUpdateInfo(rowData)) {
      this.infoUpdate = true;
    } else if (this.shouldShowUploadPopup(rowData, column)) {
      this.showUploadPopup(rowData, column);
    } else {
      this.showErrorToast(this.editErrorForConvertedValue);
    }
  }

  /**
   * Determines if the user has edit permissions.
   *
   * This method checks the `investmentKPIPermissions` array to see if any of the permissions
   * include the ability to edit. It returns `true` if at least one permission allows editing,
   * otherwise it returns `false`.
   *
   * @returns {boolean} `true` if the user can edit, `false` otherwise.
   */
  private canEdit(): boolean {
    const canEdit = this.investmentKPIPermissions?.map(access => access.canEdit);
    return canEdit.includes(true);
  }

  /**
   * Checks if there is an error notation.
   *
   * @returns {boolean} - Returns `true` if there is an error notation, otherwise `false`.
   */
  private hasErrorNotation(): boolean {
    return this.ErrorNotation;
  }

  /**
   * Checks if the user has permission to edit the given row data.
   *
   * This method verifies if the user has the necessary permissions to edit
   * the Investment KPIs sub-feature and ensures that the row data is not
   * marked as a formula.
   *
   * @param rowData - The data of the row to check for edit permissions.
   * @returns `true` if the user has permission to edit and the row data is not a formula, otherwise `false`.
   */
  private hasPermissionToEdit(rowData: any): boolean {
    return this.permissionService.checkUserPermission(
      this.subFeature.InvestmentKPIs,
      ActionsEnum[ActionsEnum.canEdit],
      this.id
    ) && !rowData?.IsFormula;
  }

  /**
   * Checks if the value in the given row data has been converted.
   * 
   * @param rowData - The data row to check.
   * @returns `true` if the value is converted and the 'KPI Info' field is '$', otherwise `false`.
   */
  private isConvertedValue(rowData: any): boolean {
    return this.isValueConverted && rowData['KPI Info'] === '$';
  }

  /**
   * Determines whether the information should be updated based on the provided row data.
   *
   * @param rowData - The data of the row to be evaluated.
   * @returns `true` if the information should be updated; otherwise, `false`.
   *
   * The information is updated if:
   * - The `investmentKpiValueUnit.typeId` is not equal to `FinancialValueUnitsEnum.Absolute`.
   * - There is no error notation (`ErrorNotation` is falsy).
   * - The row is not a header (`rowData.IsHeader` is falsy).
   */
  private shouldUpdateInfo(rowData: any): boolean {
    return Number(this.investmentKpiValueUnit.typeId) != FinancialValueUnitsEnum.Absolute &&
      !this.ErrorNotation &&
      !rowData.IsHeader;
  }

  /**
   * Determines whether the upload popup should be shown based on the provided row data and column.
   *
   * @param rowData - The data for the current row.
   * @param column - The column information.
   * @returns `true` if the upload popup should be shown; otherwise, `false`.
   */
  private shouldShowUploadPopup(rowData: any, column: any): boolean {
    return !rowData.IsHeader && !this.checkCalcColumn(rowData, column);
  }

  /**
   * Displays the upload popup with the provided row data and column information.
   * 
   * @param rowData - The data for the selected row.
   * @param column - The column information for the selected row.
   * @private
   */
  private showUploadPopup(rowData: any, column: any): void {
    this.uniqueModuleCompany = {
      moduleId: this.kpiModuleId,
      companyId: this.modelList.portfolioCompanyID,
      valueType: this.tabName,
      subPageId: 0
    };
    this.dataRow = rowData;
    this.dataColumns = column;
    this.isUploadPopupVisible = true;
  }

  checkCalcColumn(rowData: any, column: any): boolean {
    let calColumn = `Calc ${column.field}`;
    return Boolean(rowData[calColumn]);
  }
  onSubmitButtonEvent(results: any) {  
    if (results.code != null && results.code.trim().toLowerCase() == "ok") {
      this.showSuccessToast(results.message);
      this.isUploadPopupVisible = false; 
      this.isValueUpdated = !this.isValueUpdated;
    } else {
      this.showErrorToast(results.message);
      this.isUploadPopupVisible = false;
    }
    this.getPortfolioCompanyMasterKPIValues(null);  
  }
  showSuccessToast(message: string, title: string = '', position: string = 'toast-center-center'): void {
    this.toastrService.success(message, title, { positionClass: position });
  }
  CloseInfo() {
    this.infoUpdate = false;
  }

  exportInvestmentKpiValues() {
    const canExport = this.investmentKPIPermissions?.map(access => access.canExport);
    if (canExport.includes(true)) {
      let event = {
        first: 0,
        rows: 1000,
        globalFilter: null,
        sortField: "FinancialKPI.KPI",
        multiSortMeta: this.financialKPIMultiSortMeta,
        sortOrder: -1,
      };
      let filter = {
        currency: this.modelList?.reportingCurrencyDetail?.currency,
        decimaPlace: this.modelInvestmentKpi?.decimalPlaces?.type,
        valueType: this.investmentKpiValueUnit?.typeId,
      };
      this.exportLoading = true;
      this.portfolioCompanyService
        .exportTradingRecordsList({
          companyId: this.modelList?.portfolioCompanyID?.toString(),
          portfolioCompanyID: this.modelList?.portfolioCompanyID?.toString(),
          paginationFilter: event,
          searchFilter: this.investmentKpiSearchFilter,
          kPIFilter: filter,
          moduleId: this.modelList?.moduleId,
          Unit: this.investmentKpiValueUnit.typeId,
          IsSpotRate : this.kpiCurrencyFilterModel.isSpotRate == null ? false : this.kpiCurrencyFilterModel.isSpotRate,
          SpotRateDate:this.kpiCurrencyFilterModel.isSpotRate ? this.datePipe.transform(this.kpiCurrencyFilterModel.spotRateDate, 'yyyy-MM-dd') : null,
          currencyCode: this.kpiCurrencyFilterModel?.currencyCode,
          SpotRate:this.kpiCurrencyFilterModel.isSpotRate ? this.kpiCurrencyFilterModel.spotRate : null,
          reportingCurrencyCode: this.modelList.fundReportingCurrency?.currencyCode,
          currencyRateSource: this.kpiCurrencyFilterModel?.currencyRateSource,
        })
        .subscribe(
          (response) => {
            this.exportLoading = false;
            this.miscService.downloadExcelFile(response);
          },
          (error) => {
            this.exportLoading = false;
            this.message = this.miscService.showAlertMessages(
              "error",
              ErrorMessage.SomethingWentWrong
            );
          }
        );
    }
    else{
      this.showErrorToast(ErrorMessage.NoAccess);
    }
  }
}

