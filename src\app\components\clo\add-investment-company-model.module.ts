import { AddInvestmentCompanyComponent } from './add-investment-company-form/add-investment-company.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { AngularResizeEventModule } from 'angular-resize-event';
import { QuillModule } from 'ngx-quill';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { InvestCompanyService } from './investmentcompany/investmentcompany.service';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';
import { InvestmentCompanyReviewFormComponent } from './investment-company-review-form/investment-company-review-form.component';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { AuditLogService } from './services/audit-log.service';


@NgModule({
  declarations: [
    AddInvestmentCompanyComponent,
    InvestmentCompanyReviewFormComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MaterialModule,
    PrimeNgModule,
    AngularResizeEventModule,
    QuillModule,
    SharedDirectiveModule,
    FormsModule,
    ReactiveFormsModule,
    SharedComponentModule,
    DateInputsModule,
    RouterModule.forChild([
      { path: '', component: AddInvestmentCompanyComponent }
  ]),
  KendoModule
],
providers: [
  InvestCompanyService,
  AuditLogService
]

})
export class AddInvestmentCompanyModule { }