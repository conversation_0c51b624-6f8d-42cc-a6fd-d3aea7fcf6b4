export interface DashboardTrackerConfigDto {
  ID?: number; // Nullable for insert
  FieldType: number;
  DataType: number;
  Name: string;
  FrequencyType?: number;
  StartPeriod?: string;
  EndPeriod?: string;
  MapTo?: number;
  MapToType?: number;
  IsPrefix?: boolean;
  TimeSeriesDateFormat?: string;
  IsTimeSeries?: boolean;
  TimeSeriesID?: string;
}

export interface DashboardCellValueDto {
  PortfolioCompanyId: number;
  FundId: number;
  ColumnId: number;
  TimeSeriesID?: string;
  CellValue?: string;
}

export interface SaveDashboardCellValuesDto {
  CellValues: DashboardCellValueDto[];
}

export enum MaptoType {
  StaticFields = 1,
  CustomFields = 2,
} 
