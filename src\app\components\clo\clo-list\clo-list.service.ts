import { Inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators'
import { CLOListServiceConstants } from 'src/app/common/constants';
@Injectable({
  providedIn: 'root'
})
export class CloListService {
  AppUrl: string = "";  

  constructor(private readonly http: HttpClient, @Inject("BASE_URL") baseUrl: string) {
    this.AppUrl = baseUrl;
  }

  getClos(id: number): Observable<any> {
    return this.http
    .get<any>(`${this.AppUrl}api/v1/CLO/clo-details/get/${id}`)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  saveClo(cloModel: any): Observable<any> {    
    return this.http
    .post(this.AppUrl + "api/v1/CLO/clo-details/add", cloModel)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  DeleteClo(id: string,tableName:string=null): Observable<any> {   
    let endPointName=null;
    if (tableName == null) {
      endPointName = `${CLOListServiceConstants.DeleteCLOURL}${id}`;
    } 
    else {
      endPointName = `${CLOListServiceConstants.DeleteCLOTableURL}${id}/${tableName}`;
    }

    const url = `${this.AppUrl}${endPointName}`;
      return this.http.delete<any>(url)
        .pipe(
          catchError(this.errorHandler)
        );
  }

  private errorHandler(error: any): Observable<never> {    
    return throwError(error);
  }
  getCloByIssuer(companyId: number, issuer: string): Observable<any> {
    return this.http
      .get<any>(`${this.AppUrl}api/v1/CLO/clo-search/${companyId}/issuer/${issuer}`)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }
}
