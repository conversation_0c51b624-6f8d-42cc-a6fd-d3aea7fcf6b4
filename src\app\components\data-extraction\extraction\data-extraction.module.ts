import { CommonModule } from "@angular/common";
import { HTTP_INTERCEPTORS, HttpClientModule } from "@angular/common/http";
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import { AngularResizeEventModule } from "angular-resize-event";
import { KendoModule } from "src/app/custom-modules/kendo.module";
import { SharedComponentModule } from "src/app/custom-modules/shared-component.module";
import { SharedDirectiveModule } from "src/app/directives/shared-directive.module";
import { DataExtractionComponent } from "./data-extraction.component";
@NgModule({
  imports: [
    CommonModule,
    HttpClientModule,
    SharedComponentModule,
    KendoModule,
    SharedDirectiveModule,
    ReactiveFormsModule,
    FormsModule,
    AngularResizeEventModule,
    RouterModule.forChild([
      { path: "", component: DataExtractionComponent },
    ]),
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [DataExtractionComponent]
})
export class DataExtractionModule {}
