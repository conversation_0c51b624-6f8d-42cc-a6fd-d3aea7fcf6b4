import { ComponentFixture, TestBed } from "@angular/core/testing";
import { NO_ERRORS_SCHEMA } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { InvestorService } from "../../services/investor.service";
import { FirmService } from "src/app/services/firm.service";
import { MiscellaneousService } from "src/app/services/miscellaneous.service";
import { FormsModule } from "@angular/forms";
import { AddinvestorComponent } from "./addinvestor.component";
import { of } from "rxjs";
import { CommonSubFeaturePermissionService } from "src/app/services/subPermission.service";

describe("AddinvestorComponent", () => {
  let component: AddinvestorComponent;
  let fixture: ComponentFixture<AddinvestorComponent>;
  let commonService: jasmine.SpyObj<CommonSubFeaturePermissionService>;
  const commonServiceSpy = jasmine.createSpyObj('CommonSubFeaturePermissionService', ['getCommonSubFeatureAccessPermissions','getCommonFeatureAccessPermissions']);
  beforeEach(() => {
    const activatedRouteStub = () => ({ snapshot: { params: {} } });
    const toastrServiceStub = () => ({
      success: (message, string, object) => ({}),
      error: (message, string, object) => ({})
    });
    const investorServiceStub = () => ({
      getInvestorAddEditConfiuration: object => ({ subscribe: f => f({}) }),
      getMasterGeoLocations: () => ({ subscribe: f => f({}) }),
      investorAddEdit: object => ({ subscribe: f => f({}) })
    });
    const firmServiceStub = () => ({});
    const miscellaneousServiceStub = () => ({  });
    TestBed.configureTestingModule({
      imports: [FormsModule],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [AddinvestorComponent],
      providers: [
        {
          provide: CommonSubFeaturePermissionService,
          useValue: commonServiceSpy,
        },
        { provide: ActivatedRoute, useFactory: activatedRouteStub },
        { provide: ToastrService, useFactory: toastrServiceStub },
        { provide: InvestorService, useFactory: investorServiceStub },
        { provide: FirmService, useFactory: firmServiceStub },
        { provide: MiscellaneousService, useFactory: miscellaneousServiceStub }
      ]
    });
    fixture = TestBed.createComponent(AddinvestorComponent);
    component = fixture.componentInstance;
    component.investorinfo = {
      Region: 'Region',
      Country: 'Country',
      State: 'State'
    } as any;
    component.dynamicGeoLocationData = [
      { name: 'Region', value: 'value1' },
      { name: 'Country', value: 'value2' },
      { name: 'State', value: 'value3' },
      { name: 'Other', value: 'value4' }
    ];
  
  });

  it("can load instance", () => {
    expect(component).toBeTruthy();
  });

  it(`title has default value`, () => {
    expect(component.title).toEqual(`Create`);
  });

  it(`resetText has default value`, () => {
    expect(component.resetText).toEqual(`Reset`);
  });

  it(`loading has default value`, () => {
    expect(component.loading).toEqual(false);
  });

  it(`investorinfo has default value`, () => {
    expect(component.investorinfo).toEqual({
      Region: 'Region',
      Country: 'Country',
      State: 'State'
    } as any);
  });

  it(`regionList has default value`, () => {
    expect(component.regionList).toEqual([]);
  });

  it(`countryCloneList has default value`, () => {
    expect(component.countryCloneList).toEqual([]);
  });

  it(`countryList has default value`, () => {
    expect(component.countryList).toEqual([]);
  });

  it(`stateCloneList has default value`, () => {
    expect(component.stateCloneList).toEqual([]);
  });

  it(`stateList has default value`, () => {
    expect(component.stateList).toEqual([]);
  });

  it(`cityList has default value`, () => {
    expect(component.cityList).toEqual([]);
  });

  it(`cityCloneList has default value`, () => {
    expect(component.cityCloneList).toEqual([]);
  });

  it(`investortypes has default value`, () => {
    expect(component.investortypes).toEqual([]);
  });

  it(`dynamicfielddata has default value`, () => {
    expect(component.dynamicfielddata).toEqual([]);
  });

  it(`dynamicBusinessData has default value`, () => {
    expect(component.dynamicBusinessData).toEqual([]);
  });

  it(`dynamicGeoLocationData has default value`, () => {
    expect(component.dynamicGeoLocationData).toEqual([   { name: 'Region', value: 'value1' },
    { name: 'Country', value: 'value2' },
    { name: 'State', value: 'value3' },
    { name: 'Other', value: 'value4' }]);
  });

  it(`tablegeographicLocationsModel has default value`, () => {
    expect(component.tablegeographicLocationsModel).toEqual([]);
  });

  it('#getFieldDisplayName should return the value of the item with the given name', () => {
    const items = [{ name: 'section1', value: 'value1' }, { name: 'section2', value: 'value2' }];
    expect(component.getFieldDisplayName(items, 'section1')).toBe('value1');
    expect(component.getFieldDisplayName(items, 'section2')).toBe('value2');
  });

  it('#getFieldDisplayName should return an empty string if no item with the given name is found', () => {
    const items = [{ name: 'section1', value: 'value1' }, { name: 'section2', value: 'value2' }];
    expect(component.getFieldDisplayName(items, 'section3')).toBe('');
  });


  it('#ngOnInit should call getConfigurationDetails, getMasterGeoLocations, and setDefaultValues', () => {
    spyOn(component, 'getConfigurationDetails');
    spyOn(component, 'getMasterGeoLocations');
    spyOn(component, 'setDefaultValues');
    spyOn(component, 'getFeatureAccessPermissions');
    component.ngOnInit();
    expect(component.getConfigurationDetails).toHaveBeenCalled();
    expect(component.getMasterGeoLocations).toHaveBeenCalled();
    expect(component.setDefaultValues).toHaveBeenCalled();
  });

  it('#setDefaultValues should set title to "Update" if id is not undefined', () => {
    component.id = '1';
    component.setDefaultValues();
    expect(component.title).toBe('Update');
  });

  it('#setDefaultValues should set model to an empty object if id is undefined', () => {
    component.id = undefined;
    component.setDefaultValues();
    expect(component.model).toEqual({});
  });

  it('#clearGeographicLocation should call resetForm on the provided form', () => {
    const form = jasmine.createSpyObj('Form', ['resetForm']);
    component.clearGeographicLocation(form);
    expect(form.resetForm).toHaveBeenCalled();
  });

  it('#removeLocation should remove the location with the provided investorId from tablegeographicLocationsModel', () => {
    component.tablegeographicLocationsModel = [
      { investorId: 1 },
      { investorId: 2 },
      { investorId: 3 }
    ];
    component.removeLocation(2);
    expect(component.tablegeographicLocationsModel).toEqual([
      { investorId: 1 },
      { investorId: 3 }
    ]);
  });

  it('#getConfigurationDetails should set properties correctly when result is not null', () => {
    const result = {
      investorMasterList: [{ investorTypeId: 1 }],
      geographicLocations: ['location1', 'location2'],
      pageConfigurations: [{ name: 'InvestorTypeId', value: '1' }],
      investorDetails: { investorLocationData: ['locationData1', 'locationData2'] }
    };
    const mockInvestorService = TestBed.inject(InvestorService);
    spyOn(component, 'addExistingGeographicLocation');
    spyOn(mockInvestorService, 'getInvestorAddEditConfiuration').and.returnValue(of(result));
    component.id = '1';

    component.getConfigurationDetails();

    expect(component.investortypes).toEqual(result.investorMasterList);
    expect(component.dynamicGeoLocationData).toEqual(result.geographicLocations);
    expect(component.dynamicfielddata).toEqual(result.pageConfigurations);
    expect(component.addExistingGeographicLocation).toHaveBeenCalledWith(result.investorDetails.investorLocationData);
  });

  it('#getConfigurationDetails should not call addExistingGeographicLocation when id is undefined', () => {
    const result = {
      investorMasterList: [{ investorTypeId: 1 }],
      geographicLocations: ['location1', 'location2'],
      pageConfigurations: [{ name: 'InvestorTypeId', value: '1' }],
      investorDetails: { investorLocationData: ['locationData1', 'locationData2'] }
    };
    const mockInvestorService = TestBed.inject(InvestorService);
    spyOn(mockInvestorService, 'getInvestorAddEditConfiuration').and.returnValue(of(result));
    spyOn(component, 'addExistingGeographicLocation');
    component.id = undefined;

    component.getConfigurationDetails();

    expect(component.addExistingGeographicLocation).not.toHaveBeenCalled();
  });

  it('#getMasterGeoLocations should set properties correctly', () => {
    const result = {
      region: ['region1', 'region2'],
      locationModel: ['location1', 'location2'],
      states: ['state1', 'state2'],
      cities: ['city1', 'city2']
    };
    const mockInvestorService = TestBed.inject(InvestorService);
    spyOn(mockInvestorService, 'getMasterGeoLocations').and.returnValue(of(result));

    component.getMasterGeoLocations();

    expect(component.regionList).toEqual(result.region);
    expect(component.countryList).toEqual(result.locationModel);
    expect(component.stateList).toEqual(result.states);
    expect(component.cityList).toEqual(result.cities);
    expect(component.stateCloneList).toEqual(result.states);
    expect(component.cityCloneList).toEqual(result.cities);
    expect(component.countryCloneList).toEqual(result.locationModel);
  });

  it('#onRegionChange should call clearLocationSelectedData and filter countryList', () => {
    component.countryCloneList = [
      { regionId: 1 },
      { regionId: 2 },
      { regionId: 3 }
    ];
    const value = { regionId: 2 };
    spyOn(component,'clearLocationSelectedData');
  
    component.onRegionChange(value);
  
    expect(component.clearLocationSelectedData).toHaveBeenCalledWith(component.investorinfo.Region);
    expect(component.countryList).toEqual([{ regionId: 2 }]);
  });

  it('#onCountryChange should call clearLocationSelectedData and filter countryList', () => {
    component.stateCloneList = [
      { countryId: 1 },
      { countryId: 2 },
      { countryId: 3 }
    ];
    const value = { countryId: 2 };
    spyOn(component,'clearLocationSelectedData');
  
    component.onCountryChange(value);
  
    expect(component.clearLocationSelectedData).toHaveBeenCalledWith(component.investorinfo.Country);
    expect(component.stateList).toEqual([{ countryId: 2 }]);
  });

  it('#onStateChange should call clearLocationSelectedData and filter countryList', () => {
    component.cityCloneList = [
      { stateId: 1 },
      { stateId: 2 },
      { stateId: 3 }
    ];
    const value = { stateId: 2 };
    spyOn(component,'clearLocationSelectedData');
  
    component.onStateChange(value);
  
    expect(component.clearLocationSelectedData).toHaveBeenCalledWith(component.investorinfo.State);
    expect(component.cityList).toEqual([{ stateId: 2 }]);
  });

  it('#numberOnly should return true for numeric characters', () => {
    const event = { which: 49 }; // ASCII for '1'
    expect(component.numberOnly(event)).toBeTrue();
  });

  it('#numberOnly should return true for decimal point', () => {
    const event = { which: 46 }; // ASCII for '.'
    expect(component.numberOnly(event)).toBeTrue();
  });

  it('#numberOnly should return false for non-numeric characters', () => {
    const event = { which: 65 }; // ASCII for 'A'
    expect(component.numberOnly(event)).toBeFalse();
  });

  it('#Reload should call getConfigurationDetails if id is defined', () => {
    const mockForm = jasmine.createSpyObj('form', ['resetForm']);
    spyOn(component, 'getConfigurationDetails');
    component.id = '1';

    component.Reload(mockForm);

    expect(component.getConfigurationDetails).toHaveBeenCalled();
    expect(mockForm.resetForm).not.toHaveBeenCalled();
  });

  it('#Reload should call resetForm if id is undefined', () => {
    const mockForm = jasmine.createSpyObj('form', ['resetForm']);
    component.id = undefined;
    spyOn(component,'getConfigurationDetails');
    component.Reload(mockForm);

    expect(mockForm.resetForm).toHaveBeenCalled();
    expect(component.getConfigurationDetails).not.toHaveBeenCalled();
  });

  it('#save should call AddOrUpdateInvestor', () => {
    const mockForm = jasmine.createSpyObj('form', ['resetForm']);
    spyOn(component, 'AddOrUpdateInvestor');
    component.save(mockForm);
    expect(component.AddOrUpdateInvestor).toHaveBeenCalledWith(mockForm);
  });

  it('#save should update dynamicfielddata when dynamicBusinessData is not empty', () => {
    const mockForm = jasmine.createSpyObj('form', ['resetForm']);
    component.dynamicfielddata = [{ name: 'BusinessDescription', value: 'oldValue' }];
    component.save(mockForm);
    expect(component.dynamicfielddata[0].value).toEqual('oldValue');
  });

  it('#clearLocationSelectedData should clear values not matching Region', () => {
    component.clearLocationSelectedData(component.investorinfo.Region);
    expect(component.dynamicGeoLocationData[1].value).toEqual('');
    expect(component.dynamicGeoLocationData[2].value).toEqual('');
    expect(component.dynamicGeoLocationData[3].value).toEqual('');
  });

  it('#clearLocationSelectedData should clear values not matching Region or Country', () => {
    component.clearLocationSelectedData(component.investorinfo.Country);
    expect(component.dynamicGeoLocationData[2].value).toEqual('');
    expect(component.dynamicGeoLocationData[3].value).toEqual('');
  });

  it('#clearLocationSelectedData should clear values not matching Region, Country, or State', () => {
    component.clearLocationSelectedData(component.investorinfo.State);
    expect(component.dynamicGeoLocationData[3].value).toEqual('');
  });
});
