import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GrowthReportComponent } from './growth-report.component';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpServiceInterceptor } from 'src/app/interceptors/http-service-interceptor';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { GrowthReportConfigService } from 'src/app/services/growth-report-config.service'
import { CanDeactivateGuard } from 'src/app/unsaved-changes/can-deactivate/can-deactivate.guard';
import { ChangeConfirmGrowthComponent } from './change-growth-confirm/change-confirm-growth.component';
import { CanDeactivateGuardGrowth } from 'src/app/unsaved-changes/can-deactivate/can-deactivate-growth.guard';

@NgModule({
  declarations: [
    GrowthReportComponent,
    ChangeConfirmGrowthComponent
  ],
  imports: [
    SharedDirectiveModule,
    SharedComponentModule,
    CommonModule,
    KendoModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule.forChild([
      { path: '', component: GrowthReportComponent, canDeactivate: [CanDeactivateGuardGrowth] }
    ])
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [ 
    GrowthReportConfigService,
    {
    provide: HTTP_INTERCEPTORS,
    useClass: HttpServiceInterceptor,
    multi: true,
  }, CanDeactivateGuardGrowth, ChangeConfirmGrowthComponent]
})
export class GrowthReportModule { }
