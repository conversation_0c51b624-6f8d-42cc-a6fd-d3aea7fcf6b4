﻿<div #divResetPassword>
</div>




<form name="form" class="" id="reset-form-id" (ngSubmit)="f.form.valid && resetPassword()" #f="ngForm" novalidate>
    <div *ngIf="eventName!='forgotPassword'">
        <div *ngIf="pageName!='changePassword'">
            <header>
                <div class="container">
                    <div class="row justify-content-between">
                        <div class="col-auto mr-auto">
                            <a class='navbar-brand' [routerLink]="['/home']">
                                <img class="img-fluid" src="assets/dist/images/logo-4.png" />

                            </a>
                        </div>

                    </div>
                </div>
            </header>
            <div class="padd-top"></div>
        </div>
    </div>
    <div class=" container">
        <div class="row">



            <div *ngIf="pageName=='changePassword'" class="card card-main col-md-12 p-0">

                <div class="card-header card-header-main">
                    <div class="row ">
                        <div class="col-auto mr-auto "> Change Password</div>
                    </div>
                </div>
            </div>
            <div *ngIf="pageName=='createPassword'" class="card card-main col-md-12">

                <div class="card-header card-header-main">
                    <div class="row ">
                        <div class="col-auto mr-auto ">Create Password</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class=" container">
        <div class="row">
            <div class="reset-password-section col-md-12">
                <div *ngIf="eventName!='forgotPassword'">
                    <div>

                        <div class="row">


                            <div class=" col-md-5 p-0">
                                <div class="card border-0">



                                    <div class="card-body">


                                        <div *ngIf='msgs!=""' class="text-danger error-msg-login">
                                            <div class="alert alert-danger">

                                                {{msgs}}
                                            </div>
                                        </div>


                                        <div *ngIf="eventName!='forgotPassword'">
                                            <div class="row form-group has-danger">
                                                <div class="col-sm-4 d-flex align-items-end p-0">
                                                    <label for="email ">User Id </label>
                                                </div>

                                                <div class="col-sm-8 p-0">
                                                    <label for="email" class="email-id">{{model.emailId}} </label>

                                                </div>
                                            </div>

                                            <div *ngIf="pageName=='changePassword'">
                                                <div class="row form-group" [ngClass]="{ 'has-error': f.submitted && !Password.valid }">

                                                    <div class="col-sm-4 d-flex align-items-end p-0">
                                                        <label for="pwd">Current Password</label>
                                                    </div>
                                                    <div class="col-sm-8 p-0">
                                                        <input type="Password" class="form-control" name="Password" [(ngModel)]="model.currentPassword" #Password="ngModel" placeholder="Current Password" validateRequired validatePassword parent="true" autocomplete="off" maxlength="50" />
                                                        <span class="text-danger align-middle">

                                                            <div *ngIf="Password.invalid && (Password.dirty|| f.submitted)"
                                                                class="text-danger">

                                                                <p *ngIf="Password.errors.validateRequired">Please enter
                                                                    current password</p>
                                                            </div>
                                                            <div *ngIf="Password.invalid && (Password.dirty|| f.submitted) && !Password.errors.validateRequired"
                                                                class="text-danger">
                                                                <p
                                                                    *ngIf="Password.errors.validatePassword && Password.errors.validatePassword.valid==false">
                                                                    Please enter correct password</p>
                                                            </div>
                                                        
                                                        </span>
                                                    </div>

                                                </div>
                                            </div>
                                            <div class="row form-group" [ngClass]="{ 'has-error': f.submitted && !NewPassword.valid }">
                                                <div class="col-sm-4 d-flex align-items-end p-0">
                                                    <label for="pwd">New Password</label>
                                                </div>
                                                <div class="col-sm-8 p-0">
                                                    <input type="Password" class="form-control" name="NewPassword" [(ngModel)]="model.newPassword" #NewPassword="ngModel" placeholder="New Password" validateRequired validatePassword comparePassword="ConfirmPassword" parent="true" autocomplete="off" maxlength="50"
                                                    />
                                                    <span class="text-danger align-middle">

                                                        <div *ngIf="NewPassword.invalid && (NewPassword.dirty|| f.submitted)"
                                                            class="text-danger">

                                                            <p *ngIf="NewPassword.errors.validateRequired">Please enter
                                                                new password</p>
                                                        </div>
                                                        <div *ngIf="NewPassword.invalid && (NewPassword.dirty|| f.submitted) && !NewPassword.errors.validateRequired"
                                                            class="text-danger">
                                                            <p
                                                                *ngIf="NewPassword.errors.validatePassword && NewPassword.errors.validatePassword.valid==false">
                                                                Please enter correct password</p>
                                                        </div>
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="row form-group" [ngClass]="{ 'has-error': f.submitted && !ConfirmPassword.valid }">
                                                <div class="col-sm-4 d-flex align-items-end p-0">
                                                    <label for="pwd">Confirm Password</label>
                                                </div>
                                                <div class="col-sm-8 p-0">
                                                    <input type="Password" class="form-control" name="ConfirmPassword" [(ngModel)]="model.confirmPassword" #ConfirmPassword="ngModel" placeholder="Confirm Password" validateRequired comparePassword="NewPassword" parent="false" autocomplete="off" maxlength="50"
                                                    />
                                                    <span class="text-danger align-middle">

                                                        <div *ngIf="ConfirmPassword.invalid && (ConfirmPassword.dirty|| f.submitted)"
                                                            class="text-danger">

                                                            <p *ngIf="ConfirmPassword.errors.validateRequired">Please
                                                                enter confirm password</p>
                                                        </div>
                                             
                                                        <div
                                                            *ngIf="ConfirmPassword.errors && ConfirmPassword.errors.compare && !ConfirmPassword.errors.validateRequired">

                                                            <p>New password and confirm password should be same</p>
                                                        </div>

                                                    </span>
                                                </div>
                                            </div>

                                        </div>
                                    </div>


                                    <div *ngIf="(pageName==undefined||pageName=='setForgotPassword')">
                                        <button [disabled]="loading" class="btn btn-login">Reset Password</button>
                                    </div>

                                    <div *ngIf="pageName=='changePassword'">
                                        <button [disabled]="loading" class="btn btn-login">Change Password</button>
                                    </div>

                                    <div *ngIf="pageName=='createPassword'">
                                        <button [disabled]="loading" class="btn btn-login">Create Password</button>
                                    </div>

                                    <img *ngIf="loading" class="loading-login" alt="" src="assets/dist/images/loading-small.gif" />
                                    <br />



                                </div>
                            </div>


                            <div class="col-md-4 ">
                                <div class="information p-3">
                                    <h5 class="m-0 border-0"><i aria-hidden="true" class="fa fa-info-circle p-2"></i>Password Requirement
                                    </h5>
                                    <ul>
                                        <li>Should be atleast 7 characters long</li>
                                        <li>Contain one number from [0 - 9]</li>
                                        <li>Contain one lowercase letter [a - z]</li>
                                        <li>Contain one uppercase letter [A - Z]</li>
                                        <li>Contain atleast one special character [#?!@$%&*+-]</li>

                                    </ul>
                                </div>
                            </div>



                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class=" container">
        <div class="row">
            <div class="forgot-password-section col-sm-12 card-body">
                <div *ngIf="eventName=='forgotPassword'">
                    <div class="col-sm-12">
                        <div class="row form-group has-danger" [ngClass]="{ 'has-error': f.submitted && !Email.valid }">
                            <div class="col-sm-4 d-flex align-items-end justify-content-sm-center">
                                <label for="email">Username</label>
                            </div>
                            <div class="col-sm-8">
                                <input type="text" name="email" class="form-control" id="email" placeholder="<EMAIL>" [(ngModel)]="model.emailId" #Email="ngModel" validateRequired autofocus autocomplete="off" maxlength="100" />

                                <span class="text-danger align-middle">
    
                                    <div *ngIf="Email.invalid && (Email.dirty|| f.submitted)" class="text-danger">

                                        <p *ngIf="Email.errors.validateRequired">Please enter username</p>
                                    </div>
                                </span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4 d-flex align-items-end justify-content-sm-center">

                            </div>
                            <div class="col-sm-8">
                                <button [disabled]="loading" class="btn btn-login mt-3">Reset Password</button>
                                <img *ngIf="loading" class="loading-login-reset" src="assets/dist/images/loading-small.gif" />
                            </div>

                        </div>
                    </div>

                </div>

            </div>
        </div>
    </div>
</form>