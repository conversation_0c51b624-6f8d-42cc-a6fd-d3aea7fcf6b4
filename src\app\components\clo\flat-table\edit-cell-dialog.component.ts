import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-edit-cell-dialog',
  templateUrl: './edit-cell-dialog.component.html',
  styleUrls: ['./edit-cell-dialog.component.scss']
})
export class EditCellDialogComponent implements OnInit {
  @Input() oldValue: any;
  newValue: any;
  isCancelPopup: boolean = true;
  openUploadDialog: boolean = false;
  uploadedFiles: File[] = [];
  isUploading: boolean = false;
  fileSize: number = 0;
  invalidFile: boolean = false;
  comments: string = '';
  disableSaveButton: boolean = true; 

  constructor(public modal: NgbActiveModal,private toastrService: ToastrService,) { }

  ngOnInit() {}

  OnCancel() {    
    this.modal.dismiss();    
  }

  onValueChange() {
    this.disableSaveButton = !this.newValue;
  }

  onSave() {    
    this.isCancelPopup = false;
    const result = {
      newValue: this.newValue,
      supportingDocument: this.uploadedFiles[0] || null,
      comments: this.comments
    };
    this.modal.close(result);
  }

  onBrowsed(event: any): void {
    const file = event.target.files[0];
    this.processFile(file);
  }

  processFile(file: File): void {
    this.isUploading = true;

    if (file) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      const fileSizeInKB = (file.size / 1024).toFixed(2);
      const fileSizeInMB = file.size / (1024 * 1024);

      if (fileSizeInMB > 20) {        
        this.invalidFile = true;
        this.isUploading = false;
      }
        this.uploadedFiles = [file];
        this.fileSize = parseFloat(fileSizeInKB);
        console.log('Selected file:', file);
    }
  }
  
  removeFile(){
    this.isUploading = false;
  }
}