import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { CloListService } from './clo-list.service';

describe('CloListService', () => {
  let service: CloListService;
  let httpMock: HttpTestingController;
  const baseUrl = 'http://localhost:4200/';

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        CloListService,
        { provide: 'BASE_URL', useValue: baseUrl }
      ]
    });

    service = TestBed.inject(CloListService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should fetch CLO details by ID', () => {
    const dummyClo = { id: 1, name: 'Test CLO' };

    service.getClos(1).subscribe(clo => {
      expect(clo).toEqual(dummyClo);
    });

    const req = httpMock.expectOne(`${baseUrl}api/v1/CLO/clo-details/get/1`);
    expect(req.request.method).toBe('GET');
    req.flush(dummyClo);
  });

  it('should save CLO details', () => {
    const cloModel = { name: 'New CLO' };
    const response = { success: true };

    service.saveClo(cloModel).subscribe(res => {
      expect(res).toEqual(response);
    });

    const req = httpMock.expectOne(`${baseUrl}api/v1/CLO/clo-details/add`);
    expect(req.request.method).toBe('POST');
    req.flush(response);
  });

  it('should fetch CLO by issuer', () => {
    const dummyClo = { id: 1, name: 'Test CLO' };

    service.getCloByIssuer(1, 'Test Issuer').subscribe(clo => {
      expect(clo).toEqual(dummyClo);
    });

    const req = httpMock.expectOne(`${baseUrl}api/v1/CLO/clo-search/1/issuer/Test Issuer`);
    expect(req.request.method).toBe('GET');
    req.flush(dummyClo);
  });

  it('should handle error', () => {
  const errorMessage = 'Error occurred';

  service.getClos(1).subscribe(
    () => fail('expected an error, not CLO details'),
    error => expect(error.error).toBe(errorMessage)
  );

  const req = httpMock.expectOne(`${baseUrl}api/v1/CLO/clo-details/get/1`);
  req.flush(errorMessage, { status: 500, statusText: 'Server Error' });
});
});