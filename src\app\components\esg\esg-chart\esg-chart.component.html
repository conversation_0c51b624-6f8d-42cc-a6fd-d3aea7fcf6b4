<div class="portfolio-detail-component"  (resized)="onResized($event)">
    <div class="chart-area section1-height chart-section-height">
        <div class="esgdrop-down-barchart" *ngIf="companyKPIList.length !== 0">
            <div class="custom-esg-p pl-4">
                <kendo-combobox title="{{selectedCompanyKPI?.kpiName}}" [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                    [(ngModel)]="selectedCompanyKPI" #module="ngModel" [fillMode]="'solid'" [filterable]="true" name="module"
                    [virtual]="virtual" class="k-dropdown-width-420 k-custom-solid-dropdown k-dropdown-height-32"
                    [size]="'medium'" [data]="companyKPIList" [filterable]="true" [value]="selectedCompanyKPI"
                    [valuePrimitive]="false" textField="kpiName" placeholder="Select Kpi" (valueChange)="OnCompanyKPIChange()"
                    valueField="kpiId">
                    <ng-template kendoComboBoxItemTemplate let-object>
                        <div tooltipPosition="top" tooltipStyleClass="bg-tooltip-report" [pTooltip]="object?.parentkpi"
                            class="custom-ui-label custom-delete-hover custom-zindex-parent TextTruncate">
                            <span title="{{object?.kpiName}}" class="img-pad TextTruncate">
                                {{object?.kpiName}}
                            </span>
                        </div>
                    </ng-template>
                </kendo-combobox>
            </div>
        </div>
        <div class="chart-bg">
            <div *ngIf="KPIChartData==null || KPIChartData.length==0 || ifNoDataAvailable">
                <div class="row mr-0 ml-0">
                    <div class="col-sm-12 pl-0 pr-0">
                        <div class="text-info">
                            <app-empty-state class="empty-chart pt-3" [isGraphImage]="true"></app-empty-state>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mr-0 ml-0 pt-3" *ngIf="KPIChartData!=null && KPIChartData?.length>0">
                <div class="col-sm-12 pl-0 pr-0">
                    <app-lineBar-chart  [isEsg]="true" [isDisplay]="width" [DecimalPlace]="decimal" [NoOfDecimals]="KPIModulesEnum.ESG" [data]="KPIChartData" [xField]="xField"
                        [yBarFields]="yBarFields" [isScrollBar]="true"
                        [yLineFields]="selectedCompanyKPI['kpiInfo']!= '%' && selectedCompanyKPI['kpiInfo']!= 'x'?yLineFields:[]"
                        [yLineFieldText]="selectedCompanyKPI['kpiInfo']!= '%' && selectedCompanyKPI['kpiInfo']!= 'x'?'% Change In Value':''"
                        [unit]="moduleCurrency" [barColors]="yShades">
                    </app-lineBar-chart>
                </div>
            </div>
        </div>
    </div>
</div>
<app-loader-component *ngIf="isLoaded"></app-loader-component>