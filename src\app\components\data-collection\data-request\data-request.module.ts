import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { DataRequestComponent } from "./data-request.component";
import { SharedComponentModule } from "src/app/custom-modules/shared-component.module";
import { KendoModule } from "src/app/custom-modules/kendo.module";
import { KendoService } from "src/app/services/kendo.service";
import { RouterModule } from "@angular/router";
import { HTTP_INTERCEPTORS } from "@angular/common/http";
import { HttpServiceInterceptor } from "src/app/interceptors/http-service-interceptor";
import { FormsModule } from "@angular/forms";
import { AngularResizeEventModule } from "angular-resize-event";
import { SharedDirectiveModule } from "src/app/directives/shared-directive.module";
import { DataRequestService } from "src/app/services/data-request.service";

@NgModule({
  declarations: [DataRequestComponent],
  imports: [
    CommonModule,
    FormsModule,
    SharedDirectiveModule,
    SharedComponentModule,
    AngularResizeEventModule,
    CommonModule,
    SharedComponentModule,
    KendoModule,
    RouterModule.forChild([{ path: "", component: DataRequestComponent }]),
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpServiceInterceptor,
      multi: true,
    },
    KendoService,
    DataRequestService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class DataRequestModule {}
