import { Component, OnInit ,ElementRef, AfterViewInit,ViewChild, QueryList, ViewChildren, HostListener  } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LpReportConfigService } from 'src/app/services/lp-report-config.service';
import { PortfolioCompanyService } from "src/app/services/portfolioCompany.service";
import { LpReportConfigModel, FundTemplateModel, FundCompanyModel,TemplateSectionModel,SectionStaticModel, LpReportKpiModel,TemplateKpiModel,TemplateConfigModel, UnitTypeModel, CurrencyModel, MappingLpReportKpiSectionModel, MappingLpReportSectionModel, FinancialMetricsModel, DataOrder } from './models/lp-report-config.model';
import { DropDownFilterSettings } from '@progress/kendo-angular-dropdowns';
import { GroupResult, groupBy } from "@progress/kendo-data-query";
import {CdkDragDrop, moveItemInArray, CdkDragEnter} from '@angular/cdk/drag-drop';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators, AbstractControl } from '@angular/forms';
import { SubPageModuleEnums } from 'src/app/common/page-config.enum';
import { FilterExpandSettings } from "@progress/kendo-angular-treeview";
import { MultiSelectTreeCheckableSettings } from "@progress/kendo-angular-dropdowns";
import { ToastContainerDirective, ToastrService } from 'ngx-toastr';
import { SplitButtonComponent } from '@progress/kendo-angular-buttons';
import { LpTemplateConstants,ACTUAL, ACTUAL_LTM, ACTUAL_YTD, LAST_1Q_TEXT  } from 'src/app/common/constants';
import { ChangeDetectorRef } from '@angular/core';
import { chevronDownIcon, infoCircleIcon, SVGIcon} from "@progress/kendo-svg-icons";
import { MultiSelectTreeComponent } from '@progress/kendo-angular-dropdowns';
import { of, Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
@Component({
  selector: "app-lp-report-config",
  templateUrl: "./lp-report-config.component.html",
  styleUrls: ["./lp-report-config.component.scss"],
})
export class LpReportConfigComponent implements OnInit {
  openConfirmApply:boolean = false;
  isSaveEnabled: boolean = false;
  initialFormValue: any;
  @ViewChild("splitButton") splitButton: SplitButtonComponent;
  @ViewChildren("multiKpiSelect") multiKpiSelect: MultiSelectTreeComponent[];
  saveAsTemplateName: string = "";
  openSaveAs:boolean = false;
  isApply:boolean = false;
  infoIcon = infoCircleIcon;
  openConfirm: boolean = false;
  isLoader: boolean = false;
  isSDGLoader: boolean = false;
  isTemplateExist: boolean = false;
  form: FormGroup;
  sectionDropList: any[] = [];
  selectedSection: any = null;
  isCheckedCopyFundAll: boolean = false;
  isCheckedCompanyAll: boolean = false;
  selectedCopyToFundList: any[] = [];
  selectedCompanyList: any[] = [];
  investmentProfessionalList: any[] = [];
  staticInformationList: any[] = [];
  geographicLocationList: any[] = [];
  commentaryList: any[] = [];
  templateId: number = 0;
  public virtualFund: any = {
    itemHeight: 32,
    pageSize: 1600,
  };
  public virtualCompany: any = {
    itemHeight: 32,
    pageSize: 20,
  };
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: "contains",
  };
  fundLoading: boolean = false;
  id: any;
  fundList: FundTemplateModel[] = [];
  companyList: FundCompanyModel[] = [];
  lpReportConfig: LpReportConfigModel | null = null;
  templateName: string = "";
  groupedCompanyList: any[] = [];
  kpiConfigModel: any[] = [];
  kpiConfigGroupModel: any[] = [];
  tradingKpiList: any[] = [];
  kpiLineItemList: any;
  kpiPeriodList: any;
  kpiItemList: any[] = [];
  isInitialLoad: boolean = false;
  isSaveAsTemplateExist:boolean = false;
  filteredFunds: any[] = [];
  @ViewChild(ToastContainerDirective, {})
  toastContainer: ToastContainerDirective;
  public filterExpandSettings: FilterExpandSettings = {
    expandMatches: true,
    maxAutoExpandResults: 200,
  };
  public checkableSettings: MultiSelectTreeCheckableSettings = {
    checkChildren: true,
    checkOnClick: true,
  };
  updateConfigModel: TemplateConfigModel = null;
  isEditing: boolean = false;
  removeValueTypeIdsNext = [ACTUAL, ACTUAL_LTM, ACTUAL_YTD];
  investment_Colum: number = LpTemplateConstants.INVESTMENT_COLUMN;
  companyListModel: any;
  periodComparisonList: { [key: number]: FinancialMetricsModel[] } = {};
  dataOrder:DataOrder[]=[];
   public chevronDown: SVGIcon = chevronDownIcon;
  constructor(
    private _portfolioCompanyService: PortfolioCompanyService,
    private router:Router,
    private _avRoute: ActivatedRoute,
    private lpReportConfigService: LpReportConfigService,
    private fb: FormBuilder,
    private toasterService: ToastrService,
    private cdr: ChangeDetectorRef
  ) {}
  /**
   * Lifecycle hook that is called after data-bound properties of a directive are initialized.
   * Initializes the form, retrieves the template configuration, and sets up the toaster service.
   * If an ID is present in the route parameters, it assigns it to the component's `id` property.
   *
   * @memberof LpReportConfigComponent
   */
  ngOnInit() {
    this.isInitialLoad = true;
    this.initializeForm();
    this.getTemplateConfig();
    if (this._avRoute.snapshot.params["id"]) {
      this.id = this._avRoute.snapshot.params["id"];
      this.isApply =  false;
      this.isEditing = true;
    }
    this.toasterService.overlayContainer = this.toastContainer;
    localStorage.setItem("headerName", this.templateName);
    this.form.valueChanges.subscribe(() => {
      this.checkEnableSaveButton();
    });
  }
  isButtonDisabled(): boolean {
    return this.isEditing || 
           this.selectedCompanyList.length === 0 || 
           this.templateName === '' || 
           this.templateName.length > 200 || 
           this.isTemplateExist || 
           this.selectedCopyToFundList.length === 0 || 
           this.isApply;
  }
  checkEnableSaveButton(): void {
    // Compare current form value with the initial form value
    const currentFormValue = this.form.getRawValue();
    this.isSaveEnabled = JSON.stringify(currentFormValue) !== JSON.stringify(this.initialFormValue);
  }
  /**
   * Toggles the state of the split button.
   *
   * @param $event - The event object that triggered the function.
   */
  openSplitButton($event: any) {
    this.splitButton.toggle(this.splitButton.openState ? false : true);
  }
  /**
   * Groups a list of objects by their `valueTypeId` property.
   *
   * @param list - The array of objects to be grouped.
   * @returns An object where the keys are `valueTypeId` values and the values are arrays of objects that share the same `valueTypeId`.
   */
  groupByValueTypeId(list) {
    return list.reduce((acc, obj) => {
      const key = obj.valueTypeId;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(obj);
      return acc;
    }, {});
  }
  /**
   * Handles the selection change event for a list of periods.
   *
   * @param periodList - The list of periods selected.
   * @param index - The index of the section control to update.
   *
   * This method removes duplicate periods based on `valueTypeId` and updates the
   * corresponding form control if duplicates are found. If duplicates are removed,
   * an error message is displayed using the toaster service.
   */
  onSelectionChange(periodList: any[], index: number): void {
    if (periodList.length === 0) return;

    const groupedList = this.groupByValueTypeId(periodList);
    const updatedPeriods: any[] = [];

    Object.keys(groupedList).forEach((key) => {
        const periods = groupedList[key];
        if (periods.length === 1) {
            updatedPeriods.push(periods[0]);
        } else {
          const filterPeriods = (prefix: string, suffix: string) => periods.filter(item => item.text.startsWith(prefix) && item.text.endsWith(suffix));

          const lastSelectedM = filterPeriods('Last', 'M');
          const lastSelectedQ = filterPeriods('Last', 'Q');
          const lastSelectedY = filterPeriods('Last', 'Yr');
          const nextSelectedM = filterPeriods('Next', 'M');
          const nextSelectedQ = filterPeriods('Next', 'Q');
          const nextSelectedY = filterPeriods('Next', 'Yr');

            const selections = [lastSelectedM, lastSelectedQ, lastSelectedY, nextSelectedM, nextSelectedQ, nextSelectedY];
            let hasMultipleSelections = false;

            selections.forEach((selection, idx) => {
                if (selection.length > 1) {
                    selections[idx] = selection.slice(0, 1);
                    hasMultipleSelections = true;
                }
            });

            if (hasMultipleSelections) {
                this.toasterService.error(LpTemplateConstants.PeriodSelectionWarning, "", {
                    positionClass: "toast-center-center",
                });
            }
            updatedPeriods.push(...selections.flat());
        }
    });
    if (updatedPeriods.length > 0) {
        this.sections.controls[index]?.get("periodList")?.setValue(updatedPeriods);
    }
  }
  /**
   * Fetches the template configuration by ID.
   *
   * This method sets the loader to true and calls the `getLpReportTemplateKpiConfigById`
   * method from the `lpReportConfigService` service. On a successful response, it updates
   * the component's state with the fetched configuration, including the template name,
   * template ID, selected company list, and selected fund list. It also triggers the
   * `getTemplateKpiConfig` method to fetch additional KPI configurations. The loader is
   * then set to false.
   *
   * In case of an error, it logs the error to the console and sets the loader to false.
   *
   * @returns {void}
   */
  getTemplateConfigById() {
    this.isLoader = true;
    this.lpReportConfigService
      .getLpReportTemplateKpiConfigById(this.id)
      .subscribe({
        next: (config: TemplateConfigModel) => {
          this.templateName = config.lpTemplateModel.templateName;
          localStorage.setItem("headerName", this.templateName);
          this.templateId = config.lpTemplateModel.templateId;
          this.selectedCopyToFundList = this.lpReportConfig?.fundList?.filter(
            (x) =>
              config.mappingLpReportTemplateModel?.fundId
                ?.split(", ")
                ?.includes(x.fundId.toString())
          );
          let fundIds = this.selectedCopyToFundList.map((x) => x.fundId);
          this.selectedCompanyList = this.lpReportConfig?.companyList.filter(
            (x) =>
              config.mappingLpReportTemplateModel?.companyId
                ?.split(",")
                ?.includes(x.companyId.toString()) && fundIds.includes(x.fundId)
          );
          this.setMappedFunds();
          this.setMappedCompany(this.selectedCompanyList);
          this.updateConfigModel = config;
          this.getTemplateKpiConfig();
          this.isLoader = false;
        },
        error: (err) => {
          console.error("Error fetching templates:", err);
          this.isLoader = false;
        },
      });
  }
  /**
   * Fetches the KPI configuration for the selected companies and modules.
   *
   * This method constructs a `TemplateKpiModel` using the selected company IDs and module IDs,
   * then calls the `getLpReportTemplateKpiConfig` service to retrieve the KPI configuration.
   * The retrieved KPI configuration is processed and stored in `kpiItemList` and `kpiLineItemList`.
   *
   * If the `updateConfigModel` flag is set, the `id` is defined, and `isInitialLoad` is true,
   * it calls the `loadSection` method.
   *
   * In case of an error during the service call, it logs the error, resets `lpReportConfig` and
   * `groupedCompanyList`, and sets `isLoader` to false.
   *
   * @returns {void}
   */
  getTemplateKpiConfig() {
    if (this.selectedCompanyList.length > 0) {
      this.isLoader = true;
      let kpiModel: TemplateKpiModel = {
        companyIds: this.selectedCompanyList
          .map((item) => item.companyId)
          .join(","),
        moduleIds: this.lpReportConfig?.templateSections
          .map((item) => item.moduleId)
          .join(","),
      };
      this.lpReportConfigService
        .getLpReportTemplateKpiConfig(kpiModel)
        .subscribe({
          next: (kpiConfig: LpReportKpiModel[]) => {
            this.kpiItemList = kpiConfig;
            let moduleIds = this.lpReportConfig.templateSections
              .filter((x) => x.moduleId > 0)
              ?.map((item) => item.moduleId)
              .filter((value, index, self) => self.indexOf(value) === index);
            this.kpiLineItemList = {};
            moduleIds.forEach((moduleId) => {
              let kpiList = this.transformKpiList(moduleId, kpiConfig);
              this.kpiLineItemList[moduleId] = kpiList;
            });
            if (
              this.updateConfigModel &&
              this.id != undefined &&
              this.isInitialLoad
            ) {
              this.loadSection();
            }
            this.isLoader = false;
          },
          error: (err) => {
            console.error("Error fetching templates:", err);
            this.lpReportConfig = null;
            this.groupedCompanyList = [];
            this.isLoader = false;
          },
        });
    }
  }
  /**
   * Retrieves a list of KPIs grouped by company name for a given module ID.
   *
   * @param moduleId - The ID of the module to filter the KPIs.
   * @param kpiConfig - The array of KPI configurations to filter and group.
   * @returns An array of grouped results by company name.
   */
  getKpiListByModuleId(
    moduleId: number,
    kpiConfig: LpReportKpiModel[]
  ): GroupResult[] {
    return groupBy(
      kpiConfig?.filter((item) => item.moduleId === moduleId),
      [{ field: "companyName" }]
    ) as GroupResult[];
  }
  /**
   * Transforms a list of KPI configurations by grouping them based on the company ID.
   * Each group will contain the company details and a list of KPIs associated with that company.
   *
   * @param moduleId - The ID of the module to filter the KPI configurations.
   * @param kpiConfig - An array of KPI configuration objects.
   * @returns An array of grouped KPI configurations, each containing company details and associated KPIs.
   */
  transformKpiList(moduleId: number, kpiConfig: any[]): any[] {
    let dynamicId = 0;
    let kpiList = kpiConfig?.filter((item) => item.moduleId === moduleId);
    if (kpiList?.length > 0) {
      const grouped = kpiList.reduce((acc, item) => {
        const { companyId, companyName, kpi, mappingId, moduleId } = item;
        if (!acc[companyId]) {
          acc[companyId] = {
            text: companyName,
            id: dynamicId++,
            companyId: companyId,
            items: [],
          };
        }
        acc[companyId].items.push({
          text: kpi,
          id: dynamicId++,
          mappingId: mappingId,
          moduleId: moduleId,
          companyId: companyId,
        });
        return acc;
      }, {} as { [key: number]: any });
      return Object.values(grouped);
    }
    return [];
  }
  /**
   * Transforms a list of period configurations based on the provided module ID.
   *
   * @param moduleId - The ID of the module to filter the period configurations.
   * @param periodConfig - An array of period configuration objects.
   * @returns An array of transformed period configuration objects grouped by valueTypeId.
   *
   * Each transformed object contains:
   * - `text`: The alias of the value type.
   * - `id`: A dynamically generated unique identifier.
   * - `valueTypeId`: The ID of the value type.
   * - `items`: An array of period types associated with the value type, each containing:
   *   - `valueTypeId`: The ID of the value type.
   *   - `text`: The period type.
   *   - `id`: A dynamically generated unique identifier.
   *   - `periodId`: The ID of the period type.
   */
  transformPeriodList(moduleId: number, periodConfig: any[]): any[] {
    let dynamicId = 0;
    let periodList = periodConfig?.filter((item) => item.moduleId === moduleId);
    if (periodList?.length > 0) {
      const grouped = periodList.reduce((acc, item) => {
        const { valueTypeAlias, valueTypeId, periodType } = item;
        if (!acc[valueTypeId]) {
          acc[valueTypeId] = {
            text: valueTypeAlias,
            id: dynamicId++,
            valueTypeId: valueTypeId,
            items: [],
          };
        }
        periodType?.forEach((periodType) => {
          if((moduleId >= 11 && moduleId <= 15 && periodType.id >= 1078 && periodType.id <= 1080) || 
          ((moduleId < 11 || moduleId > 15) && (periodType.id < 1078 || periodType.id > 1080))){
            acc[valueTypeId].items.push({
              valueTypeId: valueTypeId,
              text: periodType.periodType,
              id: dynamicId++,
              periodId: periodType.id,
            });
          }
          if (this.removeValueTypeIdsNext.includes(valueTypeId)) {
            acc[valueTypeId].items = acc[valueTypeId]?.items?.filter(item => !item.text.includes('Next'));
          }
        });
        return acc;
      }, {} as { [key: number]: any });
      const groupedArray = Object.values(grouped).sort((a:any, b:any) => a.text.localeCompare(b.text));
      return Object.values(groupedArray);
    }
    return [];
  }
  /**
   * Fetches the LP report template configuration and processes the data.
   *
   * This method sets the loader state to true and makes an HTTP request to fetch
   * the LP report template configuration. Upon successful retrieval, it processes
   * the configuration data by filtering section fields based on sub-page IDs and
   * grouping companies. It also transforms KPI period lists based on module IDs.
   * If an ID is defined and it's the initial load, it fetches the template configuration
   * by ID. Finally, it sets the loader state to false.
   *
   * In case of an error during the HTTP request, it logs the error, resets the
   * configuration and grouped company list, and sets the loader state to false.
   *
   * @returns {void}
   */
  getTemplateConfig() {
    this.isLoader = true;
    this.lpReportConfigService.getLpReportTemplateConfig().subscribe({
      next: (config: LpReportConfigModel) => {
        this.lpReportConfig = config;
        // Use the reusable function to set the lists
        this.geographicLocationList = this.filterSectionFieldsBySubPageId(
          config.sectionFields,
          SubPageModuleEnums.GeographicLocations
        );
        this.investmentProfessionalList = this.filterSectionFieldsBySubPageId(
          config.sectionFields,
          SubPageModuleEnums.InvestmentProfessionals
        );
        this.commentaryList = this.filterSectionFieldsBySubPageId(
          config.sectionFields,
          SubPageModuleEnums.Commentary
        );
        this.staticInformationList = this.filterSectionFieldsBySubPageId(
          config.sectionFields,
          SubPageModuleEnums.StaticInformation
        );
        this.dataOrder=this.lpReportConfig.dataOrder;
        this.groupedCompanyList = [];
        this.groupByCompany();
        this.groupByPeriodComparison();
        let moduleIds = config.templateSections
          .filter((x) => x.moduleId > 0)
          ?.map((item) => item.moduleId)
          .filter((value, index, self) => self.indexOf(value) === index);
        this.kpiPeriodList = {};
        if (moduleIds?.length > 0) {
          moduleIds.forEach((moduleId) => {
            let periodList = this.transformPeriodList(
              moduleId,
              config.kpiPeriodTypes
            );
            this.kpiPeriodList[moduleId] = periodList;
          });
        }
        if (this.id != undefined && this.isInitialLoad) {
          this.getTemplateConfigById();
        }
        this.isLoader = false;
      },
      error: (err) => {
        console.error("Error fetching templates:", err);
        this.lpReportConfig = null;
        this.groupedCompanyList = [];
        this.isLoader = false;
      },
    });
  }
  // Define a reusable function to filter section fields by subPageId
  /**
   * Filters the given section fields by the specified sub-page ID.
   *
   * @param sectionFields - An array of `SectionStaticModel` objects to be filtered.
   * @param subPageId - The sub-page ID to filter the section fields by.
   * @returns An array of `SectionStaticModel` objects that match the given sub-page ID.
   */
  private filterSectionFieldsBySubPageId(
    sectionFields: SectionStaticModel[],
    subPageId: number
  ): SectionStaticModel[] {
    return sectionFields?.filter(
      (field: SectionStaticModel) => field.subPageId === subPageId
    );
  }
  groupByCompany() {
    this.groupedCompanyList = groupBy(this.lpReportConfig.companyList, [
      { field: "fundName" },
    ]) as GroupResult[];
  }
  /**
   * Applies the configuration settings for the report.
   *
   * This method performs the following actions:
   * - Sets the initial load flag to false.
   * - Retrieves the template KPI configuration.
   * - Iterates through each section in the sections form array.
   * - If a section's moduleId is greater than 0, it resets the fieldList control to an empty array.
   */
  applyConfig() {
    if(!this.isTemplateExist)
    {
      this.isApply = true;
      this.isInitialLoad = false;
      this.isLoader = true;
      this.openConfirmApply = false;
      this.getTemplateKpiConfig();
      let index = 0;
      this.isLoader = true;
      this.sections?.value?.forEach((section) => {
        this.isLoader = true;
        if (section?.name?.moduleId > 0) {
          this.sections.controls[index]?.get("fieldList").setValue([]);
        }
        index++;
        this.isLoader = false;
      });
    }
    else{
      this.toasterService.error("Template already exists!", "", {
        positionClass: "toast-center-center",
      });
    }
  }
  /**
   * Maps the provided tags array. If the array contains fewer than 2 elements,
   * it returns the array as is. Otherwise, it returns an array containing the
   * original array as its single element.
   *
   * @param tags - An array of tags to be mapped.
   * @returns An array of tags or an array containing the original array.
   */
  public tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags];
  }
  /**
   * Clears the search filter in the given event.
   *
   * @param $event - The event object containing the filter to be cleared.
   */
  clearSearch($event: any) {
    $event.clearFilter();
    $event.filterChange.emit("");
  }
  /**
   * Updates the selection state of the fund list and sets the company selection state.
   *
   * This method performs the following actions:
   * 1. Calls `updateSelectionState` to update the selection state of the `selectedCopyToFundList`.
   * 2. Uses the length of `fundList` from `lpReportConfig` to determine the selection state.
   * 3. Sets the `isCheckedCopyFundAll` flag based on the updated selection state.
   * 4. Calls `setCompanySelected` to update the company selection state.
   */
  getFundSelected($event:any = null) {
    this.isApply = false;
    this.isEditing  = false;
    this.setMappedFunds(); 
    this.setCompanySelected($event);
    this.setMapCompanies();
    this.isFundIndet();
  }
  setMappedFunds() {
    this.updateSelectionState(
      this.selectedCopyToFundList,
      this.lpReportConfig?.fundList.length,
      "isCheckedCopyFundAll"
    );
  }
  setMapCompanies(){
    let fundIds = this.selectedCopyToFundList.map((x) => x.fundId);
    let fundSelectedIds = this.selectedCompanyList.filter((x) => fundIds.includes(x.fundId)).map((x) => x.fundId);
    fundSelectedIds = [...new Set(fundSelectedIds)];
    let selectedCompanies = this.lpReportConfig.companyList.filter((x) =>
      !fundSelectedIds.includes(x.fundId) && fundIds.includes(x.fundId)
    );
    let totalCompanyList = this.lpReportConfig.companyList.filter((x) =>
      fundIds.includes(x.fundId)
    );
    let selectedComp= this.selectedCompanyList.filter((x) => fundIds.includes(x.fundId));
    this.selectedCompanyList = selectedComp.concat(selectedCompanies);
    this.updateSelectionState(
      this.selectedCompanyList,
      totalCompanyList?.length,
      "isCheckedCompanyAll"
    );
  }
  /**
   * Updates the selected company list based on the selected funds.
   *
   * This method maps the `selectedCopyToFundList` to extract `fundId`s and then filters
   * the `companyList` from `lpReportConfig` to include only those companies whose `fundId`
   * is present in the mapped `fundIds`.
   */
  setCompanySelected(fundId:any = null) {
    let fundIds = this.selectedCopyToFundList.map((x) => x.fundId);
    let selectedCompanyList = this.lpReportConfig.companyList.filter((x) =>
      fundIds.includes(x.fundId)
    );
    this.groupedCompanyList = groupBy(selectedCompanyList, [
      { field: "fundName" },
    ]) as GroupResult[];
  }
  /**
   * Updates the selection state for the company list in the LP report configuration.
   * This method checks the selected company list against the total number of companies
   * and updates the `isCheckedCompanyAll` flag accordingly.
   */
  getCompanySelected() {
    this.isEditing = false;
    this.isApply = false;
    let fundIds = this.selectedCopyToFundList.map((x) => x.fundId);
    let totalCompanyList = this.lpReportConfig.companyList.filter((x) =>
      fundIds.includes(x.fundId)
    );
    this.setMappedCompany(totalCompanyList);
  }
  setMappedCompany(totalCompanyList: any) {
    this.setCompanySelected();
    this.updateSelectionState(
      this.selectedCompanyList,
      totalCompanyList?.length,
      "isCheckedCompanyAll"
    );
  }
  /**
   * Updates the selection state based on the length of the selected list and the total list length.
   *
   * @param selectedList - The list of selected items.
   * @param totalListLength - The total number of items in the list.
   * @param checkAllProperty - The property name to update the selection state.
   */
  updateSelectionState(
    selectedList: any[],
    totalListLength: number,
    checkAllProperty: string
  ) {
    if (selectedList?.length > 0) {
      this[checkAllProperty] = selectedList.length === totalListLength;
    } else {
      this[checkAllProperty] = false;
    }
  }
  /**
   * Handles the click event for the fund selection.
   * Toggles the `isCheckedCopyFundAll` state and updates the `selectedCopyToFundList` accordingly.
   * If `isCheckedCopyFundAll` is true, the entire `fundList` from `lpReportConfig` is copied to `selectedCopyToFundList`.
   * Otherwise, `selectedCopyToFundList` is cleared.
   * Finally, it calls `setCompanySelected` to update the company selection state.
   *
   * @param multiSelect - The event or data related to the multi-select action.
   */
  public onFundClick(event: Event, filteredFunds: any) {
    const checkbox = event.target as HTMLInputElement;
    const isChecked = checkbox.checked;
    this.isApply = false;
    this.isEditing = false;
    if (filteredFunds && filteredFunds.length > 0) {
      if (isChecked) {
        this.selectedCopyToFundList = [
          ...new Set([...this.selectedCopyToFundList, ...filteredFunds])
        ];
      } else {
        this.selectedCopyToFundList = this.selectedCopyToFundList.filter(
          fund => !filteredFunds.some(filteredFund => filteredFund.fundId === fund.fundId)
        );
      }
    } else {
      this.selectedCopyToFundList = isChecked ? this.lpReportConfig?.fundList.slice() : [];
    }
    this.setCompanySelected();
    this.setMapCompanies();
    this.isFundIndet();
  }
  /**
   * Handles the click event for the company selection.
   * Toggles the `isCheckedCompanyAll` flag and updates the `selectedCompanyList` accordingly.
   * If `isCheckedCompanyAll` is true, all companies from `lpReportConfig.companyList` are selected.
   * Otherwise, the `selectedCompanyList` is cleared.
   *
   * @param multiSelect - The multi-select element (not used in the current implementation).
   */
  public onCompanyClick() {
    this.isApply = false;
    this.isEditing = false;
    this.isCheckedCompanyAll = !this.isCheckedCompanyAll;
    let fundIds = this.selectedCopyToFundList.map((x) => x.fundId);
    this.selectedCompanyList = this.isCheckedCompanyAll
      ? this.lpReportConfig.companyList.filter((x) =>
        fundIds.includes(x.fundId)
      ).slice()
      : [];
  }
  /**
   * Determines if the fund selection is in an indeterminate state.
   *
   * This getter checks if the selected funds list is in an indeterminate state
   * by comparing the `selectedCopyToFundList` with the `fundList` from the
   * `lpReportConfig`.
   *
   * @returns {boolean} - Returns `true` if the fund selection is indeterminate, otherwise `false`.
   */
  isFundIndet() {
    return this.isIndeterminate(
      this.selectedCopyToFundList,
      this.lpReportConfig?.fundList
    );
  }
  /**
   * Determines if the company selection is in an indeterminate state.
   * This is typically used to indicate that only some, but not all, items in a list are selected.
   *
   * @returns {boolean} - True if the company selection is indeterminate, otherwise false.
   */
  public get isCompanyIndet() {
    let fundIds = this.selectedCopyToFundList?.map((x) => x.fundId);
    return this.isIndeterminate(
      this.selectedCompanyList,
      this.lpReportConfig.companyList.filter((x) =>
        fundIds.includes(x.fundId)
      )
    );
  }
  /**
   * Determines if the selection state is indeterminate.
   *
   * An indeterminate state occurs when the number of selected items is
   * neither zero nor equal to the total number of items.
   *
   * @param selectedList - The list of currently selected items.
   * @param totalList - The complete list of items.
   * @returns `true` if the selection state is indeterminate, otherwise `false`.
   */
  public isIndeterminate(selectedList: any[], totalList: any[]): boolean {
    return (
      selectedList.length !== 0 && selectedList.length !== totalList.length
    );
  }
  /**
   * Checks if the given data item is in the selected company list.
   *
   * @param dataItem - The data item to check.
   * @returns `true` if the data item is in the selected company list, `false` otherwise.
   */
  isSelected(dataItem: any): boolean {
    return this.selectedCompanyList.includes(dataItem);
  }
  /**
   * Handles the selection of an item in the list.
   * Adds the item to the selectedCompanyList if the event target is checked,
   * otherwise removes it from the selectedCompanyList.
   *
   * @param dataItem - The item being selected or deselected.
   * @param event - The event triggered by the selection action.
   * @returns void
   */
  onItemSelect(dataItem: any, event: any): void {
    if (event.target.checked) {
      if(this.selectedCompanyList.find(x=>x.companyId == dataItem.companyId) == undefined)
        this.selectedCompanyList.push(dataItem);
    } else {
      this.selectedCompanyList = this.selectedCompanyList.filter(
        (item) => item !== dataItem
      );
    }
  }
  /**
   * Filters the company list based on the provided query and groups the filtered results by fund name.
   *
   * @param query - The search string used to filter the company list.
   */
  public handleFilter(query: string): void {
    let fundIds = this.selectedCopyToFundList.map((x) => x.fundId);
    const predicate = (item) =>
      item.companyName.toLowerCase().indexOf(query.toLowerCase()) >= 0 && fundIds?.includes(item.fundId);
    this.groupedCompanyList = groupBy(
      this.lpReportConfig.companyList.filter(predicate),
      [{ field: "fundName" }]
    );
  }

  /**
   * Handles the change event for the fund filter input.
   * Filters the list of funds based on the input event and updates the filtered funds list.
   * If the input event is empty, clears the filtered funds list.
   * Also, triggers the `isFundIndet` method to update the fund indicator status.
   *
   * @param event - The input event containing the filter text.
   */
  onFundFilterChange(event: any) {
    if (event) {
      this.filteredFunds = this.lpReportConfig.fundList.filter(fund =>
        fund.fundName.toLowerCase().includes(event.toLowerCase())
      );
    } else {
      this.filteredFunds = [];
    }
    this.isFundIndet();
  }
  /**
   * Adds a section to the form and sets it as the selected section.
   * If the form contains a "sections" control, it creates the section.
   *
   * @param section - The section to be added, represented by a TemplateSectionModel.
   */
  addSection(section: TemplateSectionModel) {
    if(this.kpiItemList.length == 0)
    {
      this.getTemplateKpiConfig();
    }
    this.selectedSection = section;
    if(this.selectedSection.sectionName == LpTemplateConstants.SDGLOGO){
      const selectedCompanyList = this.selectedCompanyList.map(x => {
        return {
          companyId: x.companyId,
          companyName: x.companyName,
        }
      });
      this.lpReportConfig.companyModel = selectedCompanyList;
      this.companyListModel = selectedCompanyList;
    }
    if (this.form.get("sections")) this.createSection(section);
  }
  /**
   * Handles the drop event for drag-and-drop functionality.
   * Moves an item within the sections array from the previous index to the current index.
   * Updates the validity of the sections form control and triggers change detection.
   *
   * @param event - The drag-and-drop event containing the previous and current indices.
   */
  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(
      this.sections.controls,
      event.previousIndex,
      event.currentIndex
    );
    this.sections.updateValueAndValidity();
    this.cdr.detectChanges();
    this.show=false;
    this.selectedSectionIndex=-1;
  }
  /**
   * Loads sections from the configuration model and updates the component's sections array.
   *
   * This method retrieves the `mappingLpReportSectionModels` from the `updateConfigModel` and iterates over each section.
   * For each section, it finds the corresponding section model in `lpReportConfig.templateSections` using the `sectionId`.
   * If a matching section model is found, it creates fields for the section using `createFields` method and adds them to the `sections` array.
   *
   * @remarks
   * - The method assumes that `updateConfigModel` and `lpReportConfig` are already populated.   * - The `createFields` method is called with the section model, a boolean flag, and the `mappingSectionId`.ig   * ATIONS`, `INVESTMENT_PROFESSIONALS`: Returns a filtered list of `sectionFields` based on `kpiIds`.
   * - `COMMENTARY`: Returns a filtered list of `sectionFields` based on `commentaryIds`.
   * - Default: Calls `assignKpiItems` with the provided fields and mappingSectionId.
   */
  loadSection() {
    this.isLoader = true;
    let sections = this.updateConfigModel?.mappingLpReportSectionModels;
    if (sections) {
      sections.forEach((section) => {
        let sectionModel = this.lpReportConfig?.templateSections.find(
          (x) => x.sectionId == section.sectionId
        );
        if (sectionModel) {
          let sectionFields = this.createFields(
            sectionModel,
            true,
            section.mappingSectionId
          );
          this.sections.push(sectionFields);
        }
      });
    }
    this.isLoader = false;
    this.setDefaultSectionValues();
  }
  /**
   * Sets default values for sections in the form.
   * 
   * This method iterates over the sections in the form and checks if all items in a section's fieldStaticList
   * are selected. If so, it sets the isCheckAll property of that section to true.
   * 
   * The sections are checked against predefined lists based on their names:
   * - STATIC_INFORMATION: Uses `this.staticInformationList`
   * - GEOGRAPHIC_LOCATIONS: Uses `this.geographicLocationList`
   * - INVESTMENT_PROFESSIONALS: Uses `this.investmentProfessionalList`
   * - COMMENTARY: Uses `this.commentaryList`
   * 
   * @private
   */
  setDefaultSectionValues() {
    const checkAllIfNeeded = (section: any, list: any[]) => {
      const fieldListLength = section?.get("fieldStaticList")?.value.length;
      if (fieldListLength === list.length) {
        section.get("isCheckAll").setValue(true);
      }
    };
    this.sections.controls.forEach((section: any) => {
      const sectionName = section?.get("name")?.value?.sectionName;
      switch (sectionName) {
        case LpTemplateConstants.STATIC_INFORMATION:
          checkAllIfNeeded(section, this.staticInformationList);
          break;
        case LpTemplateConstants.GEOGRAPHIC_LOCATIONS:
          checkAllIfNeeded(section, this.geographicLocationList);
          break;
        case LpTemplateConstants.INVESTMENT_PROFESSIONALS:
          checkAllIfNeeded(section, this.investmentProfessionalList);
          break;
        case LpTemplateConstants.COMMENTARY:
          checkAllIfNeeded(section, this.commentaryList);
          break;
      }
    });
    this.initialFormValue = this.form.getRawValue();
    this.checkEnableSaveButton();
  }
  /**
   * Adds a new section to the sections array if it does not already exist.
   * If the section already exists, displays an error message using the toaster service.
   *
   * @param section - The section to be added, represented by a TemplateSectionModel object.
   */
  createSection(section: TemplateSectionModel) {
    if (!this.sectionExists(section)) {
      this.sections.push(this.createFields(section, false));
    } else {
      this.toasterService.error("This section has already been added to the template", "", {
        positionClass: "toast-center-center",
      });
    }
  }
  /**
   * Getter for the 'sections' form array.
   *
   * @returns {FormArray} The 'sections' form array from the form group.
   */
  get sections(): FormArray {
    return this.form.get("sections") as FormArray;
  }
  /**
   * Checks if a given section exists within the current sections.
   *
   * @param section - The section to check for existence.
   * @returns `true` if the section exists, otherwise `false`.
   *
   * The method first checks if there are any controls in the sections.
   * If there are no controls, it returns `false`.
   *
   * If the section's name matches one of the predefined constants
   * (COMPANY_NAME, COMPANY_LOGO, STATIC_INFORMATION, BUSINESS_DESCRIPTION,
   * GEOGRAPHIC_LOCATIONS, INVESTMENT_PROFESSIONALS, COMMENTARY),
   * it checks if any control's value name's pageConfigAliasName matches
   * the section's pageConfigAliasName.
   *
   * If a match is found, it returns `true`, otherwise `false`.
   */
  sectionExists(section: TemplateSectionModel): boolean {
    if (this.sections.controls.length === 0) return false;
    else if (
      section.sectionName === LpTemplateConstants.COMPANY_NAME ||
      section.sectionName === LpTemplateConstants.COMPANY_LOGO ||
      section.sectionName === LpTemplateConstants.STATIC_INFORMATION ||
      section.sectionName === LpTemplateConstants.BUSINESS_DESCRIPTION ||
      section.sectionName === LpTemplateConstants.GEOGRAPHIC_LOCATIONS ||
      section.sectionName === LpTemplateConstants.INVESTMENT_PROFESSIONALS||
      section.sectionId === LpTemplateConstants.INVESTMENT_COLUMN 
    )
      return this.sections.controls.some(
        (control) =>
          control?.value?.name?.pageConfigAliasName ==
          section.pageConfigAliasName
      );
    else return false;
  }
  /**
   * Creates a FormGroup based on the provided TemplateSectionModel and other parameters.
   *
   * @param {TemplateSectionModel} fields - The model containing the section details.
   * @param {boolean} [isUpdate=false] - Flag indicating whether the form is being updated.
   * @param {number} [mappingSectionId=0] - The ID used for mapping sections dynamically.
   * @returns {FormGroup} - The generated FormGroup based on the section type.
   *
   * The method handles different section types defined in LpTemplateConstants:
   * - COMPANY_NAME: Creates a form group with assigned values if updating, otherwise with default value 0.
   * - COMPANY_LOGO: Creates a form group with assigned values if updating, otherwise with default value 1.
   * - STATIC_INFORMATION, GEOGRAPHIC_LOCATIONS, INVESTMENT_PROFESSIONALS, COMMENTARY: Creates a static field group.
   * - BUSINESS_DESCRIPTION: Creates a form group with a null value.
   * - Default case: If the moduleId is greater than 0, creates a dynamic field group.
   */
  createFields(
    fields: TemplateSectionModel,
    isUpdate: boolean = false,
    mappingSectionId: number = 0
  ): FormGroup {
    if(isUpdate)
      fields.mappingSectionId = mappingSectionId;
    switch (fields.sectionName) {
      case LpTemplateConstants.COMPANY_NAME:
        return this.createFormGroup(
          fields,
          isUpdate ? this.assignValues(fields) : 0
        );
      case LpTemplateConstants.COMPANY_LOGO:
        return this.createFormGroup(
          fields,
          isUpdate ? this.assignValues(fields) : 1
        );
      case LpTemplateConstants.STATIC_INFORMATION:
      case LpTemplateConstants.GEOGRAPHIC_LOCATIONS:
      case LpTemplateConstants.INVESTMENT_PROFESSIONALS:
        return this.createStaticFieldGroup(fields, isUpdate);
      case LpTemplateConstants.COMMENTARY:
        return this.createStaticFieldGroupWithCommentary(fields, isUpdate,mappingSectionId);
      case LpTemplateConstants.BUSINESS_DESCRIPTION:
        return this.createFormGroup(fields, null);
      case LpTemplateConstants.SDGLOGO:
        return this.createSDGLogoFieldGroup(fields, isUpdate,mappingSectionId);
      default:
        if (fields.moduleId > 0) {
          return this.createDynamicFieldGroup(
            fields,
            isUpdate,
            mappingSectionId
          );
        }
    }
  }
  /**
 * Creates a FormGroup with static fields and commentary frequency.
 * 
 * @param fields - The template section model containing the fields.
 * @param isUpdate - A boolean indicating if the form is being updated.
 * @param mappingSectionId - The ID used for mapping sections.
 * @returns A FormGroup with the specified fields and commentary frequency.
 */
  createStaticFieldGroupWithCommentary(
    fields: TemplateSectionModel,
    isUpdate: boolean,
    mappingSectionId: number
  ): FormGroup {
    return this.createFormGroup(fields, null, {
      fieldStaticList: new FormControl(
        isUpdate ? this.assignValues(fields,mappingSectionId) : [],
        Validators.required
      ),
      commentaryFrequency: new FormControl(
        isUpdate ? this.getCommentaryFrequency(fields,mappingSectionId) : null,
        Validators.required
      ),
      isCheckAll: new FormControl(false),
    });
  }
  /**
   * Retrieves the commentary frequency based on the provided fields and mapping section ID.
   *
   * @param fields - The fields object containing section information.
   * @param mappingSectionId - The ID of the mapping section to match.
   * @returns The frequency model object if a matching frequency ID is found and greater than 0, otherwise undefined.
   */
  getCommentaryFrequency(fields: any, mappingSectionId: number): any {
    let frequencyId = this.updateConfigModel?.mappingLpReportCommentarySectionModel
      .find((x) => x.sectionId == fields.sectionId && x.mappingSectionId == mappingSectionId)
      ?.frequencyId;  
    if(frequencyId>0){
      return  this.lpReportConfig.frequencyModel.find((x) => x.id == frequencyId)
    }
  }
  /**
   * Creates a static field group FormGroup.
   *
   * @param fields - The template section model containing the fields.
   * @param isUpdate - A boolean indicating whether the form is being updated.
   * @returns A FormGroup with the specified fields and controls.
   */
  createStaticFieldGroup(
    fields: TemplateSectionModel,
    isUpdate: boolean
  ): FormGroup {
    return this.createFormGroup(fields, null, {
      fieldStaticList: new FormControl(
        isUpdate ? this.assignValues(fields) : [],
        Validators.required
      ),
      isCheckAll: new FormControl(false),
    });
  }
  /**
   * Creates a dynamic field group FormGroup based on the provided fields and update status.
   *
   * @param fields - The template section model containing the fields to be included in the form group.
   * @param isUpdate - A boolean indicating whether the form group is being created for an update operation.
   * @param mappingSectionId - The ID of the mapping section used to assign values, periods, currency model, and unit type model.
   * @returns A FormGroup instance containing the dynamically created fields.
   */
  createDynamicFieldGroup(
    fields: TemplateSectionModel,
    isUpdate: boolean,
    mappingSectionId: number
  ): FormGroup {
    return this.createFormGroup(fields, null, {
      fieldList: new FormControl(
        isUpdate ? this.assignValues(fields, mappingSectionId) : [],
        Validators.required
      ),
      periodList: new FormControl(
        isUpdate ? this.assignPeriods(fields, mappingSectionId) : this.addDefaultPeriod(fields.sectionId),
        Validators.required
      ),
      currencyModel: new FormControl(
        this.assignCurrencyModel(fields, mappingSectionId)
      ),
      unitTypeModel: new FormControl(
        this.assignUnitType(fields, mappingSectionId)
      ),
      periodComparison: new FormControl(
        isUpdate
        ? this.assignPeriodComparison(fields, mappingSectionId)
        : []
      ),
      dataOrder:new FormControl(
        isUpdate?this.assignDataOrder(fields,mappingSectionId):this.dataOrder
      ),
      isKpiCheckAll: new FormControl(false),
      isCheckedKpi: new FormControl(false),
    });
  }
  
  /**
   * Finds an item in the provided list based on the given mapping section ID and key.
   *
   * @param fields - The template section model containing the section ID.
   * @param mappingSectionId - The ID of the mapping section to search for.
   * @param key - The key to use for finding the item in the list.
   * @param list - The list of items to search through.
   * @param defaultKey - The default key to use if the mapping section ID is not greater than 0.
   * @param defaultValue - The default value to use if the mapping section ID is not greater than 0.
   * @returns The found item from the list or `undefined` if no item is found.
   */
  findInMapping(
    fields: TemplateSectionModel,
    mappingSectionId: number,
    key: string,
    list: any,
    defaultKey: string,
    defaultValue: any
  ): any | undefined {
    if (mappingSectionId > 0) {
      const id = this.updateConfigModel?.mappingLpReportSectionModels.find(
        (x) =>
          x.sectionId === fields.sectionId &&
          x.mappingSectionId === mappingSectionId
      )?.[key];
      return list.find((x) => x[key] === id);
    }
    return list.find((x) => x[defaultKey] === defaultValue);
  }
  /**
   * Assigns a currency model based on the provided template section fields and mapping section ID.
   *
   * @param fields - The template section model containing the fields to be mapped.
   * @param mappingSectionId - The ID of the mapping section. Defaults to 0.
   * @returns The corresponding CurrencyModel if found, otherwise undefined.
   */
  assignCurrencyModel(
    fields: TemplateSectionModel,
    mappingSectionId: number = 0
  ): CurrencyModel | undefined {
    return this.findInMapping(
      fields,
      mappingSectionId,
      "currencyId",
      this.lpReportConfig.currencies,
      "currencyCode",
      "Default Currency"
    );
  }
  /**
   * Assigns a unit type to the given template section model based on the provided mapping section ID.
   *
   * @param fields - The template section model to which the unit type will be assigned.
   * @param mappingSectionId - The ID of the mapping section to be used for finding the unit type. Defaults to 0.
   * @returns The assigned unit type model, or undefined if no matching unit type is found.
   */
  assignUnitType(
    fields: TemplateSectionModel,
    mappingSectionId: number = 0
  ): UnitTypeModel | undefined {
    return this.findInMapping(
      fields,
      mappingSectionId,
      "unitTypeId",
      this.lpReportConfig.unitTypes,
      "unitType",
      "Default Unit"
    );
  }
  /**
   * Assigns values to the specified fields based on the section name.
   *
   * @param {TemplateSectionModel} fields - The fields to assign values to.
   * @param {number} [mappingSectionId=0] - The mapping section ID, default is 0.
   * @returns {any} The assigned values based on the section name.
   *
   * @remarks
   * This method handles different section names defined in `LpTemplateConstants`:
   * - `COMPANY_NAME`: Returns the index of the alignment ID in `lpReportConfig.alignments` or 0 if not found.
   * - `COMPANY_LOGO`: Returns the index of the alignment ID in `lpReportConfig.alignments` or 1 if not found.
   * - `STATIC_INFORMATION`, `GEOGRAPHIC_LOCATIONS`, `INVESTMENT_PROFESSIONALS`: Returns a filtered list of `sectionFields` based on `kpiIds`.
   * - `COMMENTARY`: Returns a filtered list of `sectionFields` based on `commentaryIds`.
   * - Default: Calls `assignKpiItems` with the provided fields and mappingSectionId.
   */
  assignValues(
    fields: TemplateSectionModel,
    mappingSectionId: number = 0
  ): any {
    switch (fields.sectionName) {
      case LpTemplateConstants.COMPANY_NAME: {
        let alignmentId =
          this.updateConfigModel?.mappingLpReportSectionModels.find(
            (x) => x.sectionId == fields.sectionId
          )?.alignmentId;
        if (alignmentId) {
          return this.lpReportConfig.alignments.findIndex(
            (x) => x.alignmentId == alignmentId
          );
        }
        return 0;
      }
      case LpTemplateConstants.COMPANY_LOGO:
        let alignmentId =
          this.updateConfigModel?.mappingLpReportSectionModels.find(
            (x) => x.sectionId == fields.sectionId
          )?.alignmentId;
        if (alignmentId) {
          return this.lpReportConfig.alignments.findIndex(
            (x) => x.alignmentId == alignmentId
          );
        }
        return 1;
      case LpTemplateConstants.STATIC_INFORMATION: {
        let staticInfoList =
          this.updateConfigModel?.mappingLpReportKpiSectionModel
            .find((x) => x.sectionId == fields.sectionId)
            ?.kpiIds?.split(",")
            ?.map((kpiId) => parseInt(kpiId));
        if (staticInfoList) {
          return this.lpReportConfig.sectionFields.filter((x) =>
            staticInfoList.includes(x.fieldId)
          );
        }
        return [];
      }
      case LpTemplateConstants.GEOGRAPHIC_LOCATIONS: {
        let staticInfoList =
          this.updateConfigModel?.mappingLpReportKpiSectionModel
            .find((x) => x.sectionId == fields.sectionId)
            ?.kpiIds?.split(",")
            ?.map((kpiId) => parseInt(kpiId));
        if (staticInfoList) {
          return this.lpReportConfig.sectionFields.filter((x) =>
            staticInfoList.includes(x.fieldId)
          );
        }
        return [];
      }
      case LpTemplateConstants.INVESTMENT_PROFESSIONALS: {
        let staticInfoList =
          this.updateConfigModel?.mappingLpReportKpiSectionModel
            .find((x) => x.sectionId == fields.sectionId)
            ?.kpiIds?.split(",")
            ?.map((kpiId) => parseInt(kpiId));
        if (staticInfoList) {
          return this.lpReportConfig.sectionFields.filter((x) =>
            staticInfoList.includes(x.fieldId)
          );
        }
        return [];
      }
      case LpTemplateConstants.COMMENTARY: {
        let commentaryIds =
          this.updateConfigModel?.mappingLpReportCommentarySectionModel
            .find((x) => x.sectionId == fields.sectionId && x.mappingSectionId == mappingSectionId)
            ?.commentaryIds?.split(",")
            ?.map((commentaryId) => parseInt(commentaryId));           
        if (commentaryIds) {
          return this.lpReportConfig.sectionFields.filter((x) =>
            commentaryIds.includes(x.fieldId)
          );
        }
        return [];
      }
      default:
        return this.assignKpiItems(fields, mappingSectionId);
    }
  }
  /**
   * Assigns KPI items based on the provided template section model and mapping section ID.
   *
   * @param {TemplateSectionModel} fields - The template section model containing module and section IDs.
   * @param {number} [mappingSectionId=0] - The mapping section ID to filter KPI items. Defaults to 0.
   * @returns {any[]} An array of selected KPI items that match the criteria.
   */
  assignKpiItems(
    fields: TemplateSectionModel,
    mappingSectionId: number = 0
  ): any[] {
    if (fields.moduleId <= 0) {
      return [];
    }
    const selectedKpiItems =
      this.updateConfigModel?.mappingLpReportKpiSectionModel
        .filter(
          ({ sectionId, mappingSectionId: id }) =>
            sectionId === fields.sectionId && id === mappingSectionId
        )
        .flatMap(({ kpiIds, companyId }) => {
          const kpiIdArray =Array.from(new Set(kpiIds?.split(",").map(Number) || []));
          const companyModels: any[] = this.kpiLineItemList[fields.moduleId]?.filter(
              (model) => model.companyId === companyId
            ) || [];
          let flatKpi =  companyModels.flatMap((companyModel) =>
            companyModel.items.filter((item) =>
              kpiIdArray.includes(item.mappingId)
            )
          );
          if(companyModels?.length > 0 && kpiIdArray?.length == companyModels[0].items.length )
          {
            flatKpi.push(companyModels[0]); 
          }
          return flatKpi;
        });
    return selectedKpiItems || [];
  }
  /**
   * Assigns periods to the given fields based on the module ID and mapping section ID.
   *
   * @param fields - The template section model containing the module ID and section ID.
   * @param mappingSectionId - The ID of the mapping section (default is 0).
   * @returns An array of selected periods or an empty array if the module ID is less than or equal to 0.
   *
   * The function filters the `mappingLpReportPeriodSectionModel` based on the section ID and mapping section ID.
   * It then maps the period IDs to the corresponding period models and returns the filtered items.
   */
  assignPeriods(
    fields: TemplateSectionModel,
    mappingSectionId: number = 0
  ): any {
    if (fields.moduleId <= 0) {
      return [];
    }
    const selectedPeriods =
      this.updateConfigModel?.mappingLpReportPeriodSectionModel
        .filter(
          ({ sectionId, mappingSectionId: id }) =>
            sectionId === fields.sectionId && id === mappingSectionId
        )
        .flatMap(({ periodIds, valueTypeId }) => {
          const periodModel: any = this.kpiPeriodList[fields.moduleId]?.find(
            (model) => model.valueTypeId === valueTypeId
          );
          const periodIdArray = periodIds?.split(",").map(Number) || [];
          return (
            periodModel?.items.filter((item) =>
              periodIdArray.includes(item.periodId)
            ) || []
          );
        });
    return selectedPeriods || [];
  }
  /**
   * Creates a FormGroup with the specified fields, alignment index, and additional controls.
   *
   * @param fields - The fields to include in the form group.
   * @param alignmentIndex - The index of the alignment to use. If null, no alignment control is added.
   * @param additionalControls - Additional controls to include in the form group.
   * @returns A FormGroup instance with the specified configuration.
   */
  private createFormGroup(
    fields: any,
    alignmentIndex: number,
    additionalControls: any = {}
  ) {
    const formGroup = {
      name: fields,
      aliasName: new FormControl(
        fields.pageConfigAliasName,
        Validators.required
      ),
      ...additionalControls,
    };
    if (alignmentIndex !== null) {
      formGroup["alignment"] = new FormControl(
        this.lpReportConfig.alignments[alignmentIndex],
        Validators.required
      );
    }
    return this.fb.group(formGroup);
  }
  /**
   * Resets the form to its initial state and reinitializes it.
   * Also sets the `openConfirm` flag to false.
   *
   * @returns {void}
   */
  onReset(): void {
    if(this.id!=undefined){
      this.form.reset();
      this.initializeForm();
      this.form.valueChanges.subscribe(() => {
        this.checkEnableSaveButton();
      });
      this.loadSection();
    }
    else{
      this.form.reset();
      this.initializeForm();
    }
    this.openConfirm = false;
  }
  /**
   * Initializes the form with a FormGroup containing an empty FormArray for sections.
   * This method sets up the initial structure of the form.
   */
  initializeForm() {
    this.form = this.fb.group({
      sections: this.fb.array([]), // Initially empty FormArray for sections
    });
  }
  /**
   * Handles the form submission event.
   *
   * @param $event - The event object triggered by the form submission.
   *
   * If the submitter's inner text is "Reset", it opens a confirmation popup and exits the function.
   * If the form is valid, it constructs a model object with the template name, encrypted template ID,
   * selected KPI values, company IDs, and fund IDs. Depending on whether the template ID is defined,
   * it either updates the template configuration or adds a new template configuration.
   */
  onSubmit($event: any): void {
    if ($event?.submitter?.innerText == "Reset") {
      this.openConfirmPopUp();
      return;
    }
    if ($event?.submitter?.innerText == "Save As") {
      this.openSaveAsPopUp();
      return;
    }
    if (this.form.valid) {
      this.isLoader = true;
      let model: any = this.getModel();
      if (this.id != undefined) {
        this.updateTemplateConfig(model);
      } else {
        this.addTemplateConfig(model);
      }
    }
  }
  private getModel(isSaveAs:boolean = false): any {
    return {
      templateName: isSaveAs ? this.saveAsTemplateName : this.templateName,
      encryptedTemplateId: this.id,
      sections: this.getSelectedKpiValue(),
      companyIds: this.selectedCompanyList
        .map((item) => item.companyId)
        .join(","),
      fundIds: this.selectedCopyToFundList
        .map((item) => item.fundId)
        .join(", "),
    };
  }
  /**
   * Updates the template configuration with the provided model.
   *
   * @param model - The model containing the template configuration to be updated.
   *
   * This method sets the `templateId` of the model to the current `templateId` and
   * calls the `updateLpReportTemplateConfig` method of `lpReportConfigService` to
   * update the template configuration. On success, it displays a success message
   * using `toasterService` and sets `isLoader` to false. On error, it logs the error
   * to the console and sets `isLoader` to false.
   */
  updateTemplateConfig(model: any) {
    model.templateId = this.templateId;
    this.lpReportConfigService.updateLpReportTemplateConfig(model).subscribe({
      next: (result: boolean) => {
        if (result) {
          this.toasterService.success("Template updated successfully", "", {
            positionClass: "toast-center-center",
          });
        }
        this.router.navigate(["/lp-report-configuration"]);
        this.isLoader = false;
      },
      error: (err) => {
        console.log(err);
        this.toasterService.error("Something went wrong", "", {
          positionClass: "toast-center-center",
        });
        this.isLoader = false;
      },
    });
  }
  /**
   * Adds a new template configuration by saving it through the lpReportConfigService.
   * Displays a success message upon successful save and clears the form.
   * Handles errors by logging them and stops the loader in both success and error cases.
   *
   * @param model - The template configuration model to be saved.
   */
  addTemplateConfig(model: any,isSaveAs:boolean = false) {
    this.lpReportConfigService.saveLpReportTemplateConfig(model).subscribe({
      next: (result: boolean) => {
        this.toasterService.success("Template added successfully", "", {
          positionClass: "toast-center-center",
        });
        this.isLoader = false;
        if(!isSaveAs){
          this.router.navigate(["/lp-report-configuration"]);
        }
        else{
          this.saveAsTemplateName = "";
          this.openSaveAs = false;
        }
      },
      error: (err) => {
        this.toasterService.error("Something went wrong", "", {
          positionClass: "toast-center-center",
        });
        this.isLoader = false;
      },
    });
  }
  /**
   * Retrieves and filters the selected KPI values from the form sections.
   *
   * This method iterates over the sections of the form, and for each section
   * with a moduleId greater than 0, it filters the fieldList to include only
   * those fields that have defined items with a length greater than 0.
   *
   * @returns {Array} The filtered sections with updated field lists.
   */
  getSelectedKpiValue() {
    let sections = this.form.value?.sections;
    sections.forEach((section) => {
      if (section.name.moduleId > 0) {
        let fieldList = section.fieldList.filter(
          (x) => x.items != undefined && x.items.length > 0
        );
        let groupedByCompanyId = section.fieldList
          .filter((x) => x.items == undefined)
          .reduce((acc, curr) => {
            if (!acc[curr.companyId]) {
              acc[curr.companyId] = { companyId: curr.companyId, items: [] };
            }
            acc[curr.companyId].items.push(curr);
            return acc;
          }, {});
        fieldList.push(...Object.values(groupedByCompanyId));
        // Ensure distinct entries based on companyId
        let distinctFieldList = fieldList.reduce((acc, curr) => {
          if (!acc.some((item) => item.companyId === curr.companyId)) {
            acc.push(curr);
          }
          return acc; 
        }, []);
        section.fieldList = distinctFieldList;
      }
      if(section?.name?.sectionName == LpTemplateConstants.SDGLOGO){
        section.sdgImageIds = section.sdgLogoOrderList.map((x) => x.id).join(",");
      }
    });
    return sections;
  }
  /**
   * Removes a section from the sections FormArray at the specified index.
   *
   * @param index - The index of the section to be removed.
   */
  removeSection(index: number): void {
    this.sections.removeAt(index);
  }
  /**
   * Updates the selection state of the objects and manages the "Select All" control.
   *
   * This method performs the following actions:
   * 1. Calls `updateSelectionState` to update the selection state based on the selected list and total list.
   * 2. Retrieves the "Select All" control for the specified section.
   * 3. Sets the "Select All" control to `true` if all items are selected.
   * 4. Sets the "Select All" control to `false` if no items are selected.
   *
   * @param {any[]} selectedList - The list of selected items.
   * @param {number} totalList - The total number of items in the list.
   * @param {string} checkAllProperty - The property name for the "Select All" control.
   * @param {number} index - The index of the section in the form array.
   * @returns {void}
   */
  getSelectedObjects(
    selectedList: any[],
    totalList: number,
    checkAllProperty: string,
    index: number
  ) {
    this.updateSelectionState(selectedList, totalList, checkAllProperty);
    const isCheckAllControl =
      this.sections.controls[index]?.get(checkAllProperty);
    if (selectedList.length == totalList) {
      isCheckAllControl?.setValue(true);
    }
    if (selectedList.length == 0) {
      isCheckAllControl?.setValue(false);
    }
  }
  /**
   * Handles the click event for the "Select All" checkbox.
   * Toggles the selection state of all items in the list.
   *
   * @param checkAllProperty - The current state of the "Select All" checkbox.
   * @param selectedList - The list of currently selected items.
   * @param totalList - The complete list of items that can be selected.
   * @param index - The index of the section control to update.
   */
  onSelectAllCheckBoxClick(
    checkAllProperty: boolean,
    selectedList: any[],
    totalList: any[],
    index: number
  ) {
    checkAllProperty = !checkAllProperty;
    selectedList = checkAllProperty ? totalList.slice() : [];
    this.sections.controls[index]
      ?.get("fieldStaticList")
      ?.setValue(selectedList);
    this.sections.controls[index]
      ?.get("isCheckAll")
      ?.setValue(checkAllProperty);
  }
  /**
   * Handles the change event for the template name input.
   *
   * This method checks if the template name exists by calling the `isTemplateExist` method
   * from `lpReportConfigService`. If the template exists, it sets `isTemplateExist` to true
   * and displays an error message using `toasterService`. If the template does not exist,
   * it sets `isTemplateExist` to false. The loader state is managed by setting `isLoader`
   * to true at the beginning and false at the end of the process.
   *
   * @returns {void} This method does not return a value.
   */
  onTemplateNameChange() {
    this.isApply = true;
    this.isEditing = false;
    if (!this.templateName) return;
    this.isLoader = true;
    this.lpReportConfigService.isTemplateExist(this.templateName).subscribe({
      next: (result: boolean) => {
        if (result) {
          this.isTemplateExist = true;
          this.toasterService.error("Template already exists!", "", {
            positionClass: "toast-center-center",
          });
        } else {
          this.isApply = false;
          this.isTemplateExist = false;
        }
        this.isLoader = false;
        this.isApply = false;
      },
      error: (err) => {
        console.log(err);
        this.isApply = false;
        this.isLoader = false;
      },
    });
  }
  /**
   * Opens the confirmation pop-up by setting the `openConfirm` property to true.
   */
  openConfirmPopUp() {
    this.openConfirm = true;
  }
  /**
   * Clears the form and resets template-related properties after saving.
   *
   * This method performs the following actions:
   * 1. Calls the `onReset` method to reset the form.
   * 2. Clears the `templateName` property.
   * 3. Sets the `isTemplateExist` property to `false`.
   *
   * @returns {void}
   */
  clearAfterSave() {
    this.onReset();
    this.templateName = "";
    this.isTemplateExist = false;
  }
  openSaveAsPopUp(){
    this.openSaveAs = true;
  }
  saveAsTemplate(){
      if (!this.saveAsTemplateName) 
        return;
      this.isLoader = true;
      this.lpReportConfigService.isTemplateExist(this.saveAsTemplateName).subscribe({
        next: (result: boolean) => {
          if (result) {
            this.toasterService.error("Template already Exist!", "", {
              positionClass: "toast-center-center",
            });
          } else {
            if (this.form.valid) {
              let model: any = this.getModel(true);
              this.addTemplateConfig(model,true)
            }
          }
          this.isLoader = false;
        },
        error: (err) => {
          console.log(err);
          this.isLoader = false;
        },
      });
    }
    onClear(): void {
     this.selectedCompanyList = [];
     this.isCheckedCompanyAll = false;
  }

  /**
   * Handles the click event for the "Select All" checkbox for logos.
   * Toggles the selection of all logos in the list.
   *
   * @param checkAllProperty - The current state of the "Select All" checkbox.
   * @param selectedList - The list of currently selected logos.
   * @param totalList - The complete list of available logos.
   * @param index - The index of the section in the form controls.
   */
  onSelectAllLogoCheckBoxClick(
    checkAllProperty: boolean,
    selectedList: any[],
    totalList: any[],
    index: number
  ) {
    checkAllProperty = !checkAllProperty;
    selectedList = checkAllProperty ? totalList.slice() : [];
    this.sections.controls[index]
      ?.get("sdgLogoList")
      ?.setValue(selectedList);
    this.sections.controls[index]
      ?.get("isCheckAllLogo")
      ?.setValue(checkAllProperty);
  }

  /**
   * Handles the SDG (Sustainable Development Goals) images by filtering and sorting them based on KPI (Key Performance Indicator) IDs,
   * and updates the provided form group with the selected images and related information.
   *
   * @param sdgImages - An array of SDG images.
   * @param sdgKpi - An object containing KPI information, including KPI IDs.
   * @param sdgSection - A FormGroup representing the SDG section in the form.
   * @param sdgCompany - An object representing the selected company.
   */
  private handleSDGImages(sdgImages: any[], sdgKpi: any, sdgSection: FormGroup, sdgCompany: any) {
    const kpiIds = sdgKpi[0]?.kpiIds?.split(",").map(Number) || [];
    const selectedSdgImages = sdgImages.filter(image => kpiIds.includes(image.id)).sort((a, b) => kpiIds.indexOf(a.id) - kpiIds.indexOf(b.id));

    sdgSection.get('selectedCompany').setValue(sdgCompany);
    sdgSection.get('sdgLogoList').setValue(selectedSdgImages);
    sdgSection.get('sdgImageList').setValue(sdgImages);
    sdgSection.get('sdgLogoOrderList').setValue(selectedSdgImages);
    sdgSection.get('isCheckAllLogo').setValue(sdgImages.length === selectedSdgImages.length);
  }

  /**
   * Handles the error state for the SDG loader.
   * This method sets the `isSDGLoader` flag to `false` to indicate that the loader has encountered an error.
   */
  private handleLoaderError() {
    this.isSDGLoader = false;
  }

  /**
   * Handles the change event when a portfolio company is selected.
   * 
   * @param value - The selected company value containing company details.
   * @param section - The form section where the company change occurred.
   * 
   * This method performs the following actions:
   * 1. Sets the `isSDGLoader` flag to true to indicate loading.
   * 2. Extracts the company ID from the selected value.
   * 3. Retrieves the sections control and its value from the form.
   * 4. Filters the sections to find the SDG (Sustainable Development Goals) section.
   * 5. Checks if the selected company already exists in the SDG section.
   * 6. If the company is already selected more than once, displays an error message and exits.
   * 7. Fetches the SDG image list for the selected company.
   * 8. Updates the form section with the fetched SDG image list and resets related fields.
   * 9. Sets the `isSDGLoader` flag to false after the operation completes.
   * 
   * @returns void
   */
  onPortfolioCompanyChange(value: any, section): void {
    this.isSDGLoader = true;
    const companyId = value?.companyId ?? 0;
    const sectionsControl = this.form.get("sections") as FormArray;
    const sectionsList = sectionsControl.value as any[];
    const sdgSection = sectionsList?.filter(x => x.name?.sectionName === LpTemplateConstants.SDGLOGO);
    let existingCompany: any[] = [];
    sdgSection.forEach((sdg: any) => {
      if (sdg?.selectedCompany?.companyId == companyId) {
        existingCompany.push(sdg);
      }
    });
    if (existingCompany?.length > 1) {
      this.isSDGLoader = false;
      this.toasterService.error("Company already selected!", "", {
        positionClass: "toast-center-center",
      });
      return;
    }
    this.getSDGImageList(companyId).subscribe({
      next: (sdgImages) => {
        section.get('sdgImageList').setValue(sdgImages.length > 0 ? sdgImages : []);
        section.get('sdgLogoList').setValue([]);
        section.get('sdgLogoOrderList').setValue([]);
        section.get('isCheckAllLogo').setValue(false);
        this.isSDGLoader = false;
      },
      error: this.handleLoaderError.bind(this)
    });
  }

  /**
   * Creates an image object with the provided image data.
   *
   * @param {any} image - The image data to create the object from.
   * @returns {object} The created image object containing the following properties:
   * - file: A new File object with the image name and type.
   * - url: The URL of the image.
   * - isExisting: A boolean indicating if the image is existing.
   * - id: The ID of the image.
   * - name: The name of the image.
   */
  createImageObject(image: any) {
    return {
      file: new File([], image.name, { type: image.type }),
      url: image.value,
      isExisting: true,
      id: image.id,
      name: image.name
    };
  }

  /**
   * Updates the `sdgLogoOrderList` form control with the current value of the `sdgLogoList` form control.
   *
   * @param section - The form section containing the `sdgLogoList` and `sdgLogoOrderList` controls.
   */
  onChangeSDGImageList(section) {
    section?.get('sdgLogoOrderList')?.setValue(section.get('sdgLogoList')?.value);
  }

  /**
   * Handles the drop event for SDG images and updates the order of the images.
   * 
   * @param event - The drag and drop event containing the previous and current index of the dragged item.
   * @param sdgLogoOrderList - The list of SDG logos in their current order.
   * @param control - The form control that holds the value of the SDG logo order list.
   */
  dropSDGImages(event: CdkDragDrop<any[]>, sdgLogoOrderList: any[], control: AbstractControl) {
    if (event.previousIndex !== event.currentIndex) {
      moveItemInArray(sdgLogoOrderList, event.previousIndex, event.currentIndex);
      control.setValue(sdgLogoOrderList);
    }
  }

  /**
   * Handles the event when an item enters a new container in a drag-and-drop operation.
   *
   * @param event - The drag enter event containing information about the item being dragged and the container it entered.
   * @param data - The array of items being manipulated.
   * @param control - The form control that holds the value of the data array.
   */
  entered(event: CdkDragEnter, data: any[], control: AbstractControl) {
    moveItemInArray(data, event.item.data, event.container.data);
    control.setValue(data);
  }

  /**
   * Creates a FormGroup for the SDG Logo field group.
   *
   * @param fields - The template section model containing the fields for the form group.
   * @param isUpdate - A boolean indicating whether the form group is being created for an update operation.
   * @param mappingSectionId - The ID of the mapping section (default is 0).
   * @returns A FormGroup instance for the SDG Logo field group.
   *
   * This method initializes the form group with the necessary controls and validators.
   * If `isUpdate` is true, it fetches the SDG company and image list, and updates the form group accordingly.
   * It also handles the loading state and error handling for the SDG image list retrieval.
   */
  createSDGLogoFieldGroup(
    fields: TemplateSectionModel,
    isUpdate: boolean,
    mappingSectionId: number = 0
  ): FormGroup {
    this.isSDGLoader = true;
    let sdgCompany: any;
    let sdgImage: any;
    let selecteSdgImage: any;
    const sdgSection = this.createFormGroup(fields, null, {
      selectedCompany: new FormControl(sdgCompany, Validators.required),
      sdgLogoList: new FormControl(isUpdate ? selecteSdgImage : [], Validators.required),
      sdgImageList: new FormControl(isUpdate ? sdgImage : []),
      sdgLogoOrderList: new FormControl(isUpdate ? selecteSdgImage : []),
      isCheckAllLogo: new FormControl(false),
    });

    if (isUpdate) {
      sdgCompany = this.assignSDGCompany(fields, mappingSectionId);
      if (sdgCompany) {
        this.getSDGImageList(sdgCompany?.companyId ?? 0).subscribe({
          next: (sdgImages) => {
            sdgImage = sdgImages;
            const sdgKpi = this.updateConfigModel?.mappingLpReportKpiSectionModel
              .filter(({ sectionId, mappingSectionId: id }) => sectionId === fields.sectionId && id === mappingSectionId);
            if (sdgKpi?.[0]?.kpiIds) {
              this.handleSDGImages(sdgImage, sdgKpi, sdgSection, sdgCompany);
            }
            this.isSDGLoader = false;
            this.isSaveEnabled = false;
          },
          error: this.handleLoaderError.bind(this)
        });
      }
    } else {
      this.isSDGLoader = false;
    }

    return sdgSection;
  }

  /**
   * Assigns the selected company's SDG (Sustainable Development Goals) KPI (Key Performance Indicator) to the lpReportConfig.
   * 
   * @param fields - The template section model containing section details.
   * @param mappingSectionId - The ID of the mapping section to filter by. Defaults to 0.
   * @returns The company model object that matches the SDG KPI's company ID, or undefined if no match is found.
   */
  assignSDGCompany(fields: TemplateSectionModel, mappingSectionId: number = 0): any {
    const sdgKpi = this.updateConfigModel?.mappingLpReportKpiSectionModel
      .filter(({ sectionId, mappingSectionId: id }) => sectionId === fields.sectionId && id === mappingSectionId);
    if (sdgKpi?.length) {
      const selectedCompanyList = this.selectedCompanyList.map(x => ({
        companyId: x.companyId,
        companyName: x.companyName,
      }));
      this.lpReportConfig.companyModel = selectedCompanyList;
      this.companyListModel = selectedCompanyList;
      return this.lpReportConfig.companyModel.find(x => x.companyId == sdgKpi[0].companyId);
    }
  }

  /**
   * Fetches the list of SDG (Sustainable Development Goals) images for a given company.
   * 
   * @param companyId - The ID of the company for which to fetch the SDG images.
   * @returns An Observable that emits an array of image objects.
   * 
   * The method sets `isSDGLoader` to true while the request is in progress and sets it to false once the request completes, 
   * whether successfully or with an error. If the request is successful and there are SDG images, it maps each image to a 
   * new object using `createImageObject`. If there are no images or if an error occurs, it returns an empty array.
   */
  getSDGImageList(companyId: number): Observable<any[]> {
    this.isSDGLoader = true;
    return this._portfolioCompanyService.getSDGImages(companyId).pipe(
      map((data: any) => {
        this.isSDGLoader = false;
        return data?.sdgImages?.length > 0 ? data.sdgImages.map((image: any) => this.createImageObject(image)) : [];
      }),
      catchError(() => {
        this.isSDGLoader = false;
        return of([]);
      })
    );
  }

  /**
   * Removes an SDG (Sustainable Development Goals) image from the specified section.
   * 
   * @param id - The unique identifier of the SDG image to be removed.
   * @param section - The section object containing the SDG image lists.
   * 
   * This method updates the `sdgLogoList` and `sdgLogoOrderList` of the section by 
   * filtering out the image with the specified id and then setting the updated lists 
   * back to the section.
   */
  removeSDGImage(id: number, section): void {
    const sdgLogoList = section.get('sdgLogoList').value.filter(x => x.id !== id);
    const sdgLogoOrderList = section.get('sdgLogoOrderList').value.filter(x => x.id !== id);
    section.get('sdgLogoList').setValue(sdgLogoList);
    section.get('sdgLogoOrderList').setValue(sdgLogoOrderList);
  }
  /**
 * Adds a default period to the KPI period list for a specific section.
 * 
 * @param sectionId - The ID of the section to add the default period to.
 * @returns An array of items that match the "Last 1Q" text if the sectionId is 1059, otherwise an empty array.
 */
  addDefaultPeriod(sectionId: number) {
    if (sectionId !== LpTemplateConstants.INVESTMENT_COLUMN) {
      return [];
    }
    const periodModel = this.kpiPeriodList[LpTemplateConstants.INVESTMENT_KPI_ID]?.find(
      (model: any) => model.valueTypeId === ACTUAL
    );
    if (!periodModel || !periodModel.items) {
      return [];
    }
    const last1QItem = periodModel.items.filter(
      (item: any) => item.text === LAST_1Q_TEXT
    );
    return last1QItem || [];
  }
  handleFilterComboBox(value, textField: string, formControlName: string) {
    if (formControlName === 'selectedCompany') {
      const filteredDate = this.companyListModel?.filter(
        (s) => s[textField].toLowerCase().indexOf(value.toLowerCase()) !== -1
      );
      this.lpReportConfig.companyModel = filteredDate;
    }
  }
  /**
 * Groups financial metrics by module ID from template sections
 * @returns void - Updates periodComparisonList property
 */
  groupByPeriodComparison() {
    // Get unique moduleIds
    const uniqueModuleIds = Array.from(new Set(
     this.lpReportConfig.templateSections
       .filter(section => section.moduleId > 0)
       .map(section => section.moduleId)
   ));
   // Create grouped results
   uniqueModuleIds.forEach(moduleId => {
     const filteredMetrics = this.lpReportConfig.financialMetricsModel
       .filter(metric => metric.moduleId === moduleId)||[];  
     this.periodComparisonList[moduleId] = filteredMetrics;
   });   
 }
 /**
 * Assigns period comparison metrics based on template section and mapping configuration
 * @param fields Template section model containing moduleId and sectionId
 * @param mappingSectionId Optional mapping section identifier
 * @returns Array of matching financial metrics or empty array
 */
 assignPeriodComparison(
   fields: TemplateSectionModel,
   mappingSectionId: number = 0
 ): any | undefined {
   if (fields.moduleId <= 0) {
     return [];
   }
  return this.getMetricsForSection(fields,mappingSectionId) || [];
 }
 /**
 * Filters and returns KPI sections that match the specified section and mapping IDs
 * @param fields - Object containing sectionId
 * @param mappingSectionId - ID of the mapping section to match
 * @returns Array of matching KPI sections
 */
 getMatchingKpiSections(fields: TemplateSectionModel, mappingSectionId: number): MappingLpReportKpiSectionModel[] {
  return this.updateConfigModel?.mappingLpReportKpiSectionModel
    .filter(section => 
      section.sectionId === fields.sectionId && 
      section.mappingSectionId === mappingSectionId
    ) || [];
}
/**
 * Filters financial metrics based on module ID and metric IDs
 * @param fields - Object containing moduleId
 * @param uniqueCompareIds - Array of unique metric IDs to match
 * @returns Array of matching financial metrics
 */
getSelectedMetrics(fields: TemplateSectionModel, uniqueCompareIds: number[]): FinancialMetricsModel[] {
  return this.lpReportConfig.financialMetricsModel.filter(metric =>
    metric.moduleId === fields.moduleId && 
    uniqueCompareIds.includes(metric.lpReportMetricId)
  ) || [];
}
/**
 * Main method that combines all steps to get metrics for a section
 * @param fields - Object containing sectionId and moduleId
 * @param mappingSectionId - ID of the mapping section
 * @returns Array of financial metrics matching the criteria
 */
getMetricsForSection(fields: TemplateSectionModel, mappingSectionId: number): FinancialMetricsModel[] {
  const matchingKpiSections = this.getMatchingKpiSections(fields, mappingSectionId);
  const uniqueCompareIds = this.getUniqueIds(matchingKpiSections, 'periodCompareIds');
  return this.getSelectedMetrics(fields, uniqueCompareIds);
}
/**
 * Assigns and orders data based on matching KPI sections for a given template section
 * 
 * @param fields - Template section model containing KPI section configurations
 * @param mappingSectionId - ID of the mapping section to match against
 * @returns Array of DataOrder objects - filtered and sorted based on matching KPI sections.
 *          Returns full dataOrder array if no matches found.
 */
assignDataOrder(fields: TemplateSectionModel, mappingSectionId: number): DataOrder[] {
  const matchingKpiSections = this.getMatchingKpiSections(fields, mappingSectionId);
  const uniqueIds =this.getUniqueIds(matchingKpiSections, 'dataOrder'); 
  const filteredDataOrder = this.lpReportConfig.dataOrder.filter(
    item => uniqueIds.includes(item.id)
  ).sort((a, b) => {
    const indexA = uniqueIds.indexOf(a.id);
    const indexB = uniqueIds.indexOf(b.id);
    return indexA - indexB;
  });
return filteredDataOrder.length > 0 
    ? filteredDataOrder 
    : this.lpReportConfig.dataOrder;
}
/**
 * Extracts and deduplicates numeric IDs from a specific field across multiple KPI sections
 * 
 * @typeParam T - Key of MappingLpReportKpiSectionModel that contains comma-separated IDs
 * @param matchingKpiSections - Array of KPI section models to process
 * @param fieldName - Name of the field containing comma-separated IDs
 * @returns Array of unique numeric IDs extracted from the specified field
 **/ 
getUniqueIds<T extends keyof MappingLpReportKpiSectionModel>(
  matchingKpiSections: MappingLpReportKpiSectionModel[], 
  fieldName: T
): number[] {
  const allIds = matchingKpiSections.reduce((ids, section) => {
    const sectionIds = section[fieldName]?.toString().split(',')
      .map(Number)
      .filter(Boolean) || [];
    return [...ids, ...sectionIds];
  }, [] as number[]);
  
  return Array.from(new Set(allIds));
}
/**
 * Handles drag and drop events for reordering data items within a section
 * 
 * @param event - The drag and drop event containing previous and current indices
 * @param sectionIndex - Index of the section in the form array where the drop occurred
 * 
 * @remarks
 * - Updates item sequences after reordering
 * - Marks the form control as dirty for change tracking
 * 
 * @example
 * <div cdkDropList (cdkDropListDropped)="onDataOrderDrop($event, i)">
 */
onDataOrderDrop(event: CdkDragDrop<DataOrder[]>, sectionIndex: number) {
  const section = this.sections.controls[sectionIndex];
  const dataOrder = [...section.get('dataOrder').value];
  
  moveItemInArray(dataOrder, event.previousIndex, event.currentIndex);
  
  dataOrder.forEach((item, index) => {
    item.sequence = index + 1;
  });

  section.get('dataOrder').setValue(dataOrder);
  section.get('dataOrder').markAsDirty();
}
public show = false;
public selectedSectionIndex: number = -1;
/**
 * Toggles the visibility state of a section
 * 
 * @param event - Mouse or keyboard event that triggered the toggle
 * @param index - Index of the section to toggle
 * 
 * @remarks
 * - If clicking same section: toggles visibility
 * - If clicking different section: shows new section and updates selected index
 * - Prevents event bubbling and default behavior
 * 
 * @example
 * <div (click)="toggle($event, sectionIndex)">
 */
public toggle(event: any, index: number): void {
    event.preventDefault();
    event.stopPropagation();
    if (this.selectedSectionIndex === index) {
        this.show = !this.show;
    } else {
        this.show = true;
        this.selectedSectionIndex = index;
    }
}

public getAnchorElement(index: number): ElementRef {
    return document.querySelector(`#anchor-${index}`) as any;
}
/**
 * Retrieves an anchor element from the DOM by its index
 * 
 * @param index - The numeric index used to construct the anchor element's ID
 * @returns ElementRef - Reference to the DOM element with id 'anchor-{index}'
 * 
 * @remarks
 * - Uses direct DOM querying which should be avoided in Angular
 * - Consider using ViewChild/ViewChildren decorators instead
 **/
@HostListener('document:click', ['$event'])
public documentClick(event: any): void {
    if (!event.target.closest('.k-data-order-popup') && 
        !event.target.closest(`#anchor-${this.selectedSectionIndex}`)) {
        this.show = false;
    }
}
/**
 * Handles escape key press events at document level
 * 
 * @decorator @HostListener('document:keydown.escape')
 * Binds to document's escape keydown event
 * 
 * @remarks
 * - Closes/hides the currently visible section
 * - Triggered when user presses ESC key anywhere in document
 */
@HostListener('document:keydown.escape')
public onEscape(): void {
    this.show = false;
}

}
