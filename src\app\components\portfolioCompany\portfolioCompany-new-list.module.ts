import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { MatMenuModule } from '@angular/material/menu';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { PortfolioCompanyNewListComponent } from './portfolioCompany-new-list.component';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { PublishedNewComponent } from './published/published.new.component';
import { PipesModule } from 'src/app/shared/pipes/pipes.module';
import { PortfolioCompanyDraftModule } from './draft/portfolio-company-draft-list/portfolio-company-draft-list.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatMenuModule,
    KendoModule,
    PrimeNgModule,
    SharedDirectiveModule,
    SharedComponentModule,
    DropdownModule,
    CalendarModule,
    PortfolioCompanyDraftModule,
    PipesModule,
    RouterModule.forChild([
      { path: '', component: PortfolioCompanyNewListComponent }
    ])
  ],
  schemas:[CUSTOM_ELEMENTS_SCHEMA],

  declarations: [PortfolioCompanyNewListComponent, PublishedNewComponent],
})
export class PortfolioCompanyNewListModule { }
