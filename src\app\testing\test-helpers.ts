import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { OidcAuthService } from '../services/oidc-auth.service';
import { AccountService } from '../services/account.service';
import { of } from 'rxjs';

/**
 * Common test providers for HTTP-related tests
 * Use this to avoid repeating the same mock setup across test files
 */
export const HTTP_TEST_PROVIDERS = [
  {
    provide: OidcAuthService,
    useValue: {
      getToken: jasmine.createSpy('getToken').and.returnValue('mock-token'),
      addUpdateUserSessionState: jasmine.createSpy('addUpdateUserSessionState'),
      getUserInfo: jasmine.createSpy('getUserInfo').and.returnValue(of({})),
      isAuthenticated: jasmine.createSpy('isAuthenticated').and.returnValue(true),
      login: jasmine.createSpy('login'),
      logout: jasmine.createSpy('logout'),
      refreshToken: jasmine.createSpy('refreshToken').and.returnValue(of('new-token')),
      deActivateToken: jasmine.createSpy('deActivateToken').and.returnValue(of({})),
      getEnvironmentConfig: jasmine.createSpy('getEnvironmentConfig').and.returnValue({}),
      signinRedirectCallback: jasmine.createSpy('signinRedirectCallback').and.returnValue(of(true)),
      signinSilentCallback: jasmine.createSpy('signinSilentCallback').and.returnValue(of(true))
    }
  },
  {
    provide: AccountService,
    useValue: {
      getToken: jasmine.createSpy('getToken').and.returnValue('mock-token'),
      redirectToUnauthorized: jasmine.createSpy('redirectToUnauthorized'),
      addupdateSessionId: jasmine.createSpy('addupdateSessionId').and.returnValue(of({})),
      getCurrentUser: jasmine.createSpy('getCurrentUser').and.returnValue(of({})),
      setCurrentUser: jasmine.createSpy('setCurrentUser'),
      clearCurrentUser: jasmine.createSpy('clearCurrentUser'),
      isLoggedIn: jasmine.createSpy('isLoggedIn').and.returnValue(true),
      getCurrentUserDeatils: jasmine.createSpy('getCurrentUserDeatils').and.returnValue(of({})),
      errorHandler: jasmine.createSpy('errorHandler').and.returnValue(of({}))
    }
  },
  {
    provide: HTTP_INTERCEPTORS,
    useValue: {
      intercept: (req: any, next: any) => next.handle(req)
    },
    multi: true
  }
];

/**
 * Mock service factory for common services
 */
export class MockServiceFactory {
  static createPortfolioCompanyService() {
    return {
      getPortfolioCompany: jasmine.createSpy('getPortfolioCompany').and.returnValue(of([])),
      checkIsKPITypeMarkedForExtraction: jasmine.createSpy('checkIsKPITypeMarkedForExtraction').and.returnValue(of({ isMarked: false })),
      getKPIMappingList: jasmine.createSpy('getKPIMappingList').and.returnValue(of([])),
      createDuplicateKPI: jasmine.createSpy('createDuplicateKPI').and.returnValue(of({})),
      getPortfolioMappedLpReportTemplates: jasmine.createSpy('getPortfolioMappedLpReportTemplates').and.returnValue(of([]))
    };
  }

  static createFundService() {
    return {
      getFundById: jasmine.createSpy('getFundById').and.returnValue(of({})),
      getFunds: jasmine.createSpy('getFunds').and.returnValue(of([])),
      createFund: jasmine.createSpy('createFund').and.returnValue(of({})),
      updateFund: jasmine.createSpy('updateFund').and.returnValue(of({})),
      deleteFund: jasmine.createSpy('deleteFund').and.returnValue(of({}))
    };
  }

  static createDealService() {
    return {
      getDealsList: jasmine.createSpy('getDealsList').and.returnValue(of([])),
      getDealById: jasmine.createSpy('getDealById').and.returnValue(of({})),
      createDeal: jasmine.createSpy('createDeal').and.returnValue(of({})),
      updateDeal: jasmine.createSpy('updateDeal').and.returnValue(of({})),
      deleteDeal: jasmine.createSpy('deleteDeal').and.returnValue(of({}))
    };
  }

  static createMiscellaneousService() {
    return {
      getCompanies: jasmine.createSpy('getCompanies').and.returnValue(of([])),
      getTemplates: jasmine.createSpy('getTemplates').and.returnValue(of([])),
      getConfigurations: jasmine.createSpy('getConfigurations').and.returnValue(of([])),
      saveConfiguration: jasmine.createSpy('saveConfiguration').and.returnValue(of({})),
      deleteConfiguration: jasmine.createSpy('deleteConfiguration').and.returnValue(of({}))
    };
  }

  static createAppSettingService() {
    return {
      getSettings: jasmine.createSpy('getSettings').and.returnValue(of({})),
      updateSettings: jasmine.createSpy('updateSettings').and.returnValue(of({})),
      getFeatureData: jasmine.createSpy('getFeatureData').and.returnValue(of([])),
      isPermissionAvailable: jasmine.createSpy('isPermissionAvailable').and.returnValue(true)
    };
  }
}

/**
 * Common test data factory
 */
export class TestDataFactory {
  static createMockUser() {
    return {
      id: 1,
      name: 'Test User',
      email: '<EMAIL>',
      role: 'admin'
    };
  }

  static createMockCompany() {
    return {
      id: 1,
      name: 'Test Company',
      encryptedPortfolioCompanyId: 'test123'
    };
  }

  static createMockFund() {
    return {
      id: 1,
      name: 'Test Fund',
      description: 'Test Fund Description'
    };
  }

  static createMockKPI() {
    return {
      id: 1,
      name: 'Test KPI',
      type: 'financial',
      value: 100
    };
  }
}
