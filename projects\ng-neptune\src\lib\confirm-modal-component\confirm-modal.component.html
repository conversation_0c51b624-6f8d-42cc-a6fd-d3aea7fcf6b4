<div class="nep-modal nep-modal-show confirm-model-popup" style="display: block; background: rgba(0, 0, 0, 0.25);" id="confirm-modal">
  <div class="nep-modal-mask"></div>
  <div [style.width]="customwidth"[style.top]="customTop"  class="nep-card nep-card-shadow nep-modal-panel nep-modal-default"
    style="position: relative; display: inline-flex;border-radius: 8px;">
    <div class="nep-card-header M-M" [ngClass]="hasHeaderStyle? 'nep-modal-title-custom':'nep-modal-title'" >
     <div class="float-left  M-M TextTruncate pt-1 pb-1">{{modalTitle}}</div>  
     <div id="confirm-modal-close" (click)="onCloseIconClick()" class="float-right" *ngIf="isCloseEnable"> <img class="close-img" src='assets/dist/images/fa-close.svg' alt="" /></div>
    </div>                                                         
    <div class="nep-card-body">
      <ng-content></ng-content>
    </div>
    <div class="nep-card-footer nep-card-right nep-modal-footer" [ngClass]="isCustomFooterClass? 'custom-background-footer':''">
      <div *ngIf="!IsInfoPopup">
        <div class="float-left" *ngIf="IsDeleteEnable">
          <nep-button Type="Secondary" (click)="onDeleteEvent()" Name="confirm-modal-secondary">
            Delete
          </nep-button>
        </div>
        <nep-button Type="Secondary" (click)="onSecondaryEvent()" Name="confirm-modal-secondary" [ngClass]="isDeleteConfirmModal ? 'delete-modal-button-secondary' : ''">
          {{secondaryButtonName}}
        </nep-button>
        <nep-button [Type]="btnType" [disabled]="disablePrimaryButton" style="padding-left:12px" (click)="onPrimaryEvent()" Name="confirm-modal-primary">
          {{primaryButtonName}}
        </nep-button>
      </div>
      <div *ngIf="IsInfoPopup">
        <nep-button Type="Primary" style="padding-left:12px" (click)="onPrimaryEvent()" Name="confirm-modal-primary-btn">
          {{primaryButtonName}}
        </nep-button>
      </div>
    </div>
  </div>
</div>
