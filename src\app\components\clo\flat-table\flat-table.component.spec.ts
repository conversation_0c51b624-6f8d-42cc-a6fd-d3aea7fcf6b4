import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from "@angular/core/testing";
import { FlatTableComponent } from "./flat-table.component";
import { InvestCompanyService } from "../investmentcompany/investmentcompany.service";
import { ToastrModule, ToastrService } from "ngx-toastr";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { Component, Input } from "@angular/core";
import { of, throwError } from "rxjs";
import { CloListService } from "../clo-list/clo-list.service";
import { Router } from "@angular/router";
import { AuditLogService } from "../services/audit-log.service";
import { HttpClientTestingModule } from "@angular/common/http/testing";
import { FeatureTableMapping } from "src/app/common/constants";

@Component({
  selector: "app-clo-commentries",
  template: "",
})
class MockCloCommentriesComponent {
  @Input() footnotes: any;
  @Input() comments: any;
  ngOnInit() {}
}

describe("FlatTableComponent", () => {
  let component: FlatTableComponent;
  let fixture: ComponentFixture<FlatTableComponent>;
  let investCompanyService: jasmine.SpyObj<InvestCompanyService>;
  let toastrService: jasmine.SpyObj<ToastrService>;
  let modalService: jasmine.SpyObj<NgbModal>;
  let cloListService: CloListService;
  let router: Router;

  const mockTableData = {
    data: [
      { id: "1", name: "Test Company", value: 100 },
      { id: "2", name: "Test Company 2", value: 200 },
    ],
    columns: [
      { field: "id", title: "ID" },
      { field: "name", title: "Name" },
      { field: "value", title: "Value" },
    ],
  };

  beforeEach(async () => {
    investCompanyService = jasmine.createSpyObj("InvestCompanyService", [
      "getTableData",
      "saveFootnote",
      "getFootnote",
      "uploadExcelFile",
      "downloadTemplate",
      "exportTableData",
      "updateTableCell",
    ]);

    toastrService = jasmine.createSpyObj("ToastrService", [
      "success",
      "error",
      "info",
      "warning",
    ]);
    modalService = jasmine.createSpyObj("NgbModal", ["open"]);

    await TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        ToastrModule.forRoot(),
        BrowserAnimationsModule,
        HttpClientTestingModule,
      ],
      declarations: [FlatTableComponent, MockCloCommentriesComponent],
      providers: [
        { provide: InvestCompanyService, useValue: investCompanyService },
        { provide: ToastrService, useValue: toastrService },
        { provide: NgbModal, useValue: modalService },
        CloListService,
        { provide: "BASE_URL", useValue: "http://localhost" },
        AuditLogService,
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(FlatTableComponent);
  component = fixture.componentInstance;
  cloListService = TestBed.inject(CloListService) as jasmine.SpyObj<CloListService>;
  router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  toastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
  component.tableName = "TestTable";
    component.companyID = "1";
  });

  it("should create", () => {
    expect(component).toBeTruthy();
  });

  it("should save footnote successfully", fakeAsync(() => {
    const footnote = { newComment: "Test Comment", isEdit: true };
    investCompanyService.saveFootnote.and.returnValue(of({}));

    component.handleSave(footnote);
    tick();

    expect(toastrService.success).toHaveBeenCalled();
    expect(footnote.isEdit).toBeFalse();
  }));

  it("should handle file upload with valid file", fakeAsync(() => {
    const mockFile = new File([""], "test.xlsx", {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    investCompanyService.uploadExcelFile.and.returnValue(of({}));
    investCompanyService.getTableData.and.returnValue(of(mockTableData));

    component.handleFileUpload(mockFile);
    tick();

    expect(toastrService.success).toHaveBeenCalled();
  }));

  it("should reject invalid file type", () => {
    const mockFile = new File([""], "test.txt", { type: "text/plain" });
    component.handleFileUpload(mockFile);
    expect(toastrService.error).toHaveBeenCalledWith(
      "Invalid file type. Only Excel files are allowed."
    );
  });
  it("should call getCloByIssuer and navigate on success", () => {
    const mockClo = [{ uniqueID: "123" }];
    spyOn(cloListService, 'getCloByIssuer');
    (cloListService.getCloByIssuer as jasmine.Spy).and.returnValue(of(mockClo));
    spyOn(router, "navigate");
    
    component.tableName = "GLI_Portfolio_Composition";
    component.companyID = "1";
    component.onGLIPortfolioClick("testTransaction");
    
    expect(cloListService.getCloByIssuer).toHaveBeenCalledWith(
      1,
      "testTransaction"
    );
    expect(router.navigate).toHaveBeenCalledWith(["/view-clo-summary", "123"]);
  });
  it("should show error when getCloByIssuer fails", () => {
    spyOn(cloListService, 'getCloByIssuer');
    (cloListService.getCloByIssuer as jasmine.Spy).and.returnValue(
      throwError({ error: "Error" })
    );
    component.tableName = "GLI_Portfolio_Composition";
    component.companyID = "1";
    component.onGLIPortfolioClick("testTransaction");
    let errorTable = "GLI Portfolio";
    if (component.tableName == "Aggregate_CLO_Metrics_EU" || component.tableName == "Aggregate_CLO_Metrics_US") {
      errorTable = "Aggregate CLO Metrics";
    }
    const message = `Error for this ${errorTable}`;
    toastrService.error(message);
    expect(toastrService.error).toHaveBeenCalledWith(message);
  });
  it("should return false for empty permission array", () => {
    const result = component.checkPermissionAccess([], "CAN_EDIT");
    expect(result).toBeFalse();
  });

  it("should return true when permission array contains a true value", () => {
    const permissions = [{ CAN_EDIT: true }];
    const result = component.checkPermissionAccess(permissions, "CAN_EDIT");
    expect(result).toBeTrue();
  });

  it("should return false when permission array contains only false values", () => {
    const permissions = [{ CAN_EDIT: false }, { CAN_EDIT: false }];
    const result = component.checkPermissionAccess(permissions, "CAN_EDIT");
    expect(result).toBeFalse();
  });

  it('should adjust column widths based on data and header', () => {
    // Mock data
    component.columns = [ {
      header: "name",
      field: "name",
      parent: null,
      title:"name",
      group:null,
      isStaticTableHeader: false,
      isUniqueIdentifier: false
  },
  {
    header: "description",
    field: "description",
    parent: null,
    title:"description",
    group:null,
    isStaticTableHeader: false,
    isUniqueIdentifier: false
}
    ];
    component.data = [
      {id:'1', header: 'Alice', field: 'A short description',title: 'Alice' },
      {id:'2', header: 'Bob', field: 'A much longer description that should affect the width',title: 'Bob' }
    ];

    // Spy on getTextWidth
    spyOn(component, 'getTextWidth').and.callFake((text: string, font: string) => {
      if (text === 'Alice') return 30;
      if (text === 'Bob') return 20;
      if (text === 'A short description') return 100;
      if (text === 'A much longer description that should affect the width') return 300;
      if (text === 'Name') return 40;
      if (text === 'Description') return 80;
      return 0;
    });

    // Call the method
    component.adjustWidthBasedOnData();

    // Assertions
    expect(component.columns[0].width).toBeGreaterThan(0); // Adjust based on the mock values
    expect(component.columns[1].width).toBeGreaterThan(0); // Adjust based on the mock values
  });
  it('should correctly update navColumns, distColumns, and staticTableHeader', () => {
    // Mock table data
    const mockTableData = {
      data: [],
      columns: [
        { field: 'NAV_Per_EUR', header: 'NAV Per EUR', isStaticTableHeader: false },
        { field: 'NAV_Per_USD', header: 'NAV Per USD', isStaticTableHeader: false },
        { field: 'Distribution_2023', header: 'Distribution 2023', isStaticTableHeader: false },
        { field: 'Static_Header', header: 'Static Header', isStaticTableHeader: true },
      ],
    };

    // Set tableId to NAV_DISTRIBUTION
    component.tableId = FeatureTableMapping.TABLES_NAME.NAV_DISTRIBUTION[0];

    // Call the private method indirectly (or make it public for testing)
    (component as any).updateTableColumns(mockTableData);

    // Assertions
    expect(component.navColumns).toEqual([
      { field: 'NAV_Per_EUR', header: 'NAV Per EUR', isStaticTableHeader: false },
      { field: 'NAV_Per_USD', header: 'NAV Per USD', isStaticTableHeader: false },
    ]);

    expect(component.distColumns).toEqual([
      { field: 'Distribution_2023', header: 'Distribution 2023', isStaticTableHeader: false },
    ]);
    expect(component.columns).toEqual([
      { field: 'NAV_Per_EUR', header: 'NAV Per EUR', isStaticTableHeader: false },
      { field: 'NAV_Per_USD', header: 'NAV Per USD', isStaticTableHeader: false },
      { field: 'Distribution_2023', header: 'Distribution 2023', isStaticTableHeader: false },
      { field: 'Static_Header', header: 'Static Header', isStaticTableHeader: true },
    ]);
  });

  it('should handle empty columns gracefully', () => {
    const mockTableData = {
      data: [],
      columns: [],
    };

    component.tableId = FeatureTableMapping.TABLES_NAME.NAV_DISTRIBUTION[0];

    (component as any).updateTableColumns(mockTableData);

    expect(component.navColumns).toEqual([]);
    expect(component.distColumns).toEqual([]);
    expect(component.staticTableHeader).toBeUndefined();
    expect(component.columns).toEqual([]);
  });
  it('should navigate to CLO summary if CLO data is returned', () => {
    // Arrange
    component.tableName = 'GLI_Portfolio_Composition';
    component.companyID = '123';
    const mockTransaction = 'transaction123';
    const mockCloData = [{ uniqueID: 'clo123' }];
  
    // Mock the cloListService.getCloByIssuer to return an observable
    spyOn(cloListService, 'getCloByIssuer');
    (cloListService.getCloByIssuer as jasmine.Spy).and.returnValue(of(mockCloData));
    spyOn(router, 'navigate');
  
    // Act
    component.onGLIPortfolioClick(mockTransaction);
  
    // Assert
  expect(cloListService.getCloByIssuer).toHaveBeenCalledWith(123, mockTransaction);
  expect(router.navigate).toHaveBeenCalledWith(['/view-clo-summary', 'clo123']);
  });

  it('should show an error toast if CLO data is not returned', () => {
    // Arrange
    component.tableName = 'Aggregate_CLO_Metrics_EU';
    component.companyID = '123';
    const mockTransaction = 'transaction123';
    const mockError = { error: 'Error message' };
    spyOn(cloListService, 'getCloByIssuer');
    (cloListService.getCloByIssuer as jasmine.Spy).and.returnValue(throwError(mockError)); // Reuse the spy
  
    // Act
    component.onGLIPortfolioClick(mockTransaction);
  
    // Assert
    expect(cloListService.getCloByIssuer).toHaveBeenCalledWith(123, mockTransaction);
    expect(toastrService.error).toHaveBeenCalledWith(
      'Error message for this Aggregate CLO Metrics',
      '',
      { positionClass: 'toast-center-center' }
    );
  });
});
