import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { UploadPerformanceData } from './upload-performance-data.component';
import { DialogsModule } from '@progress/kendo-angular-dialog';
import { ButtonsModule } from '@progress/kendo-angular-buttons';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { QuillModule } from 'ngx-quill';
import { AngularResizeEventModule } from 'angular-resize-event';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';

@NgModule({
  declarations: [
    UploadPerformanceData,
  ],
  imports: [
CommonModule,
    MaterialModule,
    PrimeNgModule,
    AngularResizeEventModule,
    QuillModule,
    SharedDirectiveModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule.forChild([
      { path: '', component: UploadPerformanceData }
    ]),
    DialogsModule, // Import Kendo UI Dialogs module
    ButtonsModule,  // Import Kendo UI Buttons module
    KendoModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA] // Optionally add this to suppress unknown element errors
})
export class UploadPerformanceDataModule { }