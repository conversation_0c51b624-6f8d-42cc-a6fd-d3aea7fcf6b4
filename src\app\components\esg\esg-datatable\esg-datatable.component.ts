import { Component, Input, EventEmitter, OnInit, Output, OnChanges, SimpleChanges, ChangeDetectorRef, ViewChild, QueryList, ViewChildren, ElementRef, AfterViewInit, AfterViewChecked } from '@angular/core';
import { isNumeric } from '@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric';
import { PeriodTypeFilterOptions, NumberDecimalConst, ModuleList, FileUploadStatus, KpiInfo, EsgConstants, GlobalConstants } from "src/app/common/constants";
import { EsgService } from "../../../services/esg.services";
import { MatMenu, MatMenuTrigger } from '@angular/material/menu';
import { ErrorMessage, FinancialValueUnitsEnum, MiscellaneousService, DecimalDigitEnum } from 'src/app/services/miscellaneous.service';
import { ExportEsgKpiType } from '../enum/export-esg-enum';
import { KpitablefiltersComponent } from "../../kpitablefilters/kpitablefilters.component";
import { isUndefined } from 'mathjs';
import { Table } from 'primeng/table';
import { ToastrService } from 'ngx-toastr';
import { ModuleCompanyModel, TableHeader, Audit, MappedDocuments  } from 'src/app/components/file-uploads/kpi-cell-edit/kpiValueModel';
import { Router } from "@angular/router";
import { debounceTime, Subscription } from "rxjs";
import { KPIModulesEnum } from 'src/app/services/permission.service';
import { OidcAuthService } from "src/app/services/oidc-auth.service";
import { extractDateComponents } from "src/app/components/file-uploads/kpi-cell-edit/cell-edit-utils";
import { AuditService } from "src/app/services/audit.service";

@Component({
  selector: 'app-esg-datatable',
  templateUrl: './esg-datatable.component.html',
  styleUrls: ['./esg-datatable.component.scss']
})
export class EsgDatatableComponent implements OnInit, OnChanges, AfterViewInit {
  isTruncatedAny: boolean = false;
  @ViewChildren('cellText') textElements: QueryList<ElementRef>;
  @ViewChildren("textareaEle") textareaEle: QueryList<ElementRef>;
  textAreaSubscription: Subscription;
  textElementSubScription: Subscription;
  @Output() refreshEsgData: EventEmitter<boolean> = new EventEmitter(false);
  @Output() showFootNote: EventEmitter<boolean> = new EventEmitter(false);
  @Input() selectedSubpageData: any;
  @Input() selectedCompany: any;
  @Input() typeField: string;
  NumberDecimalConst = NumberDecimalConst;
  @Input() companyDetails: any;
  @Input() decimal: string;
  dataColumns: TableHeader;
  newRowData: object;
  unitOfCurrency = FinancialValueUnitsEnum[FinancialValueUnitsEnum.Absolute];
  currencyCode: string;
  ifNoDataAvailable: boolean = true;
  Columns: any = [];
  esgKpiStaticList: any = [];
  frozenCols: any = [
    { field: "kpiName", header: "KPI" }
  ];
  esgPeriodData: any = [];
  quarterlyColumns: any = [];
  annuallyColumns: any = [];
  subscriptions: any = [];
  esgKPICols: any = [];
  isLoading: boolean = false;
  showKpiStaticInfoModal: boolean = false;
  selectedStaticItem: any = {};
  @ViewChild('menu') uiuxMenu!: MatMenu;
  @ViewChild('esgMenuTrigger') menuTrigger: MatMenuTrigger;
  @ViewChild('exportMenu') uiuxexportMenu!: MatMenu;
  @ViewChild('iExportMenuTrigger') exportMenuTrigger: MatMenuTrigger;
  esgKpiValueUnit: any;
  searchFilter: any = null;
  isLoader: boolean;
  esgKPIMultiSortMeta: any[] = [
    { field: "year", order: -1 },
    { field: "month", order: -1 },
  ];
  modelESGKpi: any = {};
  @Input() model;
  tableResult = [];
  tableResultClone = [];
  globalFilter: string = "";
  tableColumns = [];
  tableFrozenColumns = [];
  esgKPIFilterCols = [];
  esgSubFieldList: any = [];
  kpiInfo = KpiInfo;
  infoUpdatePopUpVisible: boolean = false;
  confirmUpdatePopUp: boolean = false;
  isEsgDataUpdated: boolean = false;
  defaultFinancialValueUnit: any = FinancialValueUnitsEnum.Absolute;
  @ViewChildren(KpitablefiltersComponent) preferenFilterForm: QueryList<KpitablefiltersComponent>;
  updateModel: any = {};
  selectedExportOption = 0;
  decimalPlace: NumberDecimalConst = NumberDecimalConst.doubleDecimal;
  isExportLoading: boolean = false;
  @ViewChild('dt') dt: Table | undefined;
  selectedEsgKpiItem: any = {};
  disablePrimaryButton: boolean = true;
  ESG_TAB_NAME = ModuleList.ESG;
  isSingleClick: boolean = false;
  isToggleChecked:boolean=false;
  kpiModuleId = KPIModulesEnum.ESG;
  uniqueModuleCompany: ModuleCompanyModel;
  constructor(private toastrService: ToastrService, private _esgService: EsgService, protected changeDetectorRef: ChangeDetectorRef, private miscService: MiscellaneousService,
    private router: Router,private identityService: OidcAuthService,
    private auditService: AuditService) {
    this.esgKpiValueUnit = {
      typeId: FinancialValueUnitsEnum.Absolute,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Absolute],
    };
  }

  ngOnInit() {
    this.subscriptions.push(this._esgService.currentDecimalPlace.subscribe(decimalPlace => this.decimalPlace = decimalPlace));
    this.decimal = DecimalDigitEnum.Two;
    this.onDecimalChange(this.decimal);
    this.currencyCode = this.companyDetails?.reportingCurrencyDetail?.currencyCode ?? "";
    this.changeDetectorRef.detectChanges();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes["typeField"]) {
      //delay api call   
      setTimeout(() => {
        this.getCompanyKpiValueByEsgModuleId(this.searchFilter);
      }, 500);

    }
    if (changes["companyDetails"]) {
      this.currencyCode = this.companyDetails?.reportingCurrencyDetail?.currencyCode ?? "";
    }
    if (changes["selectedSubpageData"]) {
      this.preferenFilterForm?.first.onSubmit({
        submitter: { name: "Reset" }
      });
      if (!(this.selectedSubpageData?.subPageId)) {
        this.Columns = [];
        this.esgPeriodData = [];
        this.ifNoDataAvailable = true;

        return;
      }
      this.DecimalfunctionToDownloadExcel();
    }

  }
  ngAfterViewInit() {
    this.textAreaSubscription = this.textareaEle.changes.subscribe(() => {
      this.setTextAreaFocus();
    });
    this.textElementSubScription = this.textElements.changes.subscribe(() => {
     this.checkWidth();
    });
  }

  ngOnDestroy() {
    this.subscriptions.forEach(s => s.unsubscribe());
    this.textAreaSubscription.unsubscribe();
    this.textElementSubScription.unsubscribe();
  }

  /**
   * Sets focus to the textarea element that matches the update model's column name.
   */
  setTextAreaFocus() {
    this.textareaEle.forEach((item, index) => {
      let column = item.nativeElement.column;
      if (column == this.updateModel.colName) {
        item.nativeElement.focus();
      };

    });
  }
  /**
       * Handles the click event of the audit log button.
       * Navigates to the audit logs page with the necessary data.
       * @param rowData - The data of the clicked row.
       * @param field - The field of the clicked row.
       */
  onAuditLog(rowData: any, column: any) {
    sessionStorage.removeItem(EsgConstants.EsgAuditLocalStorage);
    const ERROR_MESSAGE = GlobalConstants.AuditLogError;
    if (!this.isToggleChecked || rowData.isHeader) {
      this.isSingleClick = true;
      setTimeout(() => {
          this.isSingleClick && this.setTextareaditable(rowData, column);
      }, 250);
      if (this.isToggleChecked) {
        this.showErrorToast(ERROR_MESSAGE);
      }
      return;
    }
    const dateComponents = extractDateComponents(column.header);
    let auditLogFilter = this.getAuditLogFilter(rowData, dateComponents, column);
    this.auditService.getPortfolioEditSupportingCommentsData(auditLogFilter).subscribe({
      next: (data: MappedDocuments) => {
        if(data?.valueId > 0 && data?.auditLogCount > 0){
          this.redirectToAuditLogPage(rowData, column, data, auditLogFilter);
        } else if(data?.auditLogCount == 0){
          this.showErrorToast(GlobalConstants.AuditLogNAMessage);
        }
      },
      error: (error: any) => {
        this.showErrorToast(ERROR_MESSAGE);
      },
    });
  }

  /**
   * Redirects to the audit log page with the specified parameters.
   * 
   * @param field - The field object.
   * @param attributeName - The attribute name.
   * @param data - The mapped documents data.
   * @param auditLogFilter - The audit log filter.
   */
  redirectToAuditLogPage(rowData: any, column: any, mappedData: MappedDocuments, auditLogFilter: Audit) {
    let data = {
      KpiName: rowData?.kpiName,
      Period: column?.header,
      PortfolioCompanyID: this.selectedCompany?.portfolioCompanyID,
      SubPageName: this.selectedSubpageData?.alias,
      SubPageId: this.selectedSubpageData?.subPageId,
      KpiId: auditLogFilter?.kpiId,
      KpiInfo: rowData?.kpiInfo,
      EsgKpiRecordId: mappedData?.valueId
    };
    
    // Store the data in local storage or session storage
    sessionStorage.setItem(EsgConstants.EsgAuditLocalStorage, JSON.stringify(data));
    let config = this.identityService.getEnvironmentConfig();
    if (config.redirect_uri != "") {
      let myAppUrl = config.redirect_uri.split("/in")[0] + EsgConstants.EsgAuditUrl;
      window.open(myAppUrl, '_blank');
    }
  }

  /**
   * Returns an Audit object representing the filter criteria for the audit log.
   * @param rowData - The data of the row.
   * @param dateComponents - The date components (year, month, quarter).
   * @returns An Audit object with the filter criteria.
   */
  getAuditLogFilter(rowData: any, dateComponents: { year: any; month: number; quarter: any; }, column: any) {
    return <Audit>{
      valueType: 'Actual',
      kpiId: rowData["kpiId"],
      mappingId: this.getEsgKpiRecordId(rowData, column),
      quarter: dateComponents.quarter,
      year: dateComponents.year,
      month: dateComponents.month,
      moduleId: KPIModulesEnum.ESG,
      companyId: this.selectedCompany?.portfolioCompanyID,
    };
  }

  /**
   * Sets the textarea editable state for the specified row and column.
   * @param rowData - The data of the row.
   * @param column - The column being edited.
   */
  setTextareaditable(rowData: any, column: any) {
      this.updateModel.colName = column.field;
      this.updateModel.header = column.header;
      this.updateModel.unit = rowData.kpiInfo;
      this.updateModel.rowName = rowData.kpiName;
      this.updateModel.kpiId = rowData.kpiId;
      this.updateModel.previousVal = rowData[column.field];
      let objIndex = this.esgPeriodData.findIndex((obj => obj.kpiId == rowData.kpiId));
      this.onTextareaEditComplete(rowData);
      rowData.kpiInfo == KpiInfo.Text && this.Columns.forEach(col => {
        this.esgPeriodData[objIndex][`${col.field} editable`] = true;
      });
  }

  /**
   * Gets the ESG KPI record id based on the given row data and field.
   * @param rowData - The row data.
   * @param field - The field.
   * @returns The ESG KPI record id.
   */

  getEsgKpiRecordId(rowData: any, field: any) {
    let selectedRecord = rowData?.kpiData?.find(x => x.year == field?.year && x.quarter === field?.quarter);
    if(!selectedRecord && field?.quarter.split(" ").length == 1){
      selectedRecord = rowData?.kpiData?.find(x => x.year == field?.year && x.quarter === '');
    }
    return selectedRecord?.esgKpiRecordId;
  }

  /**
   * Sets the value of the `decimal` property based on the current decimal value from the `_esgService`.
   * If the current decimal value is null or undefined, the `decimal` property is set to `DecimalDigitEnum.Two`.
   * Otherwise, the `decimal` property is set to the current decimal value.
   */

  DecimalfunctionToDownloadExcel() {
    this.subscriptions.push(this._esgService.currentDecimal.subscribe(decimal => {
      this.decimal = decimal;
    }));
  }
  /**
   * Handles the change event of the decimal input.
   * @param decimal - The new decimal value.
   */
  onDecimalChange(decimal: any) {
    switch (true) {
      case (decimal > DecimalDigitEnum.Two):
        this._esgService.changeDecimalPlace(`1.${decimal}-${decimal}`);
        break;
      case (decimal === DecimalDigitEnum.One):
        this._esgService.changeDecimalPlace('1.1-1');
        break;
      default:
        this._esgService.changeDecimalPlace('1.2-2');
    }
  }

  /**
  * Downloads the current KPI data as an Excel file.
  */
  downloadCurrentKpiDataExcel() {
    let requestBody = this.applyFilterBody(this.searchFilter);
    requestBody.exportType = ExportEsgKpiType.CurrentKpi;
    this._esgService.exportEsgKpiData(requestBody).subscribe({
      next: (response) => {
        this.miscService.downloadExcelFile(response);
      },
      error() {
        this.toastrService.error(FileUploadStatus.FileNotdownloadedMessage, "", { positionClass: "toast-center-center" });
      }
    });
  }

  /**
  * Downloads all the KPI data as an Excel file.
  */
  downloadAllKpiDataExcel() {
    let requestBody = this.applyFilterBody(this.searchFilter);
    requestBody.exportType = ExportEsgKpiType.AllKpi;
    this._esgService.exportEsgKpiData(requestBody).subscribe({
      next: (response) => {
        this.toastrService.success(FileUploadStatus.EmailNotificationToaster, "", { positionClass: "toast-center-center", enableHtml: true });
      },
      error() {
        this.toastrService.error(FileUploadStatus.FileNotdownloadedMessage, "", { positionClass: "toast-center-center" });
      }
    });
  }

  /**
   * Retrieves the company KPI value by ESG module ID.
   * 
   * @param searchfilter - Optional search filter.
   */
  getCompanyKpiValueByEsgModuleId(searchfilter?: any) {
    
    let requestBody = this.applyFilterBody(searchfilter);
    this.isLoading = true;
    this._esgService.getCompanyKpiValueByEsgModuleId(requestBody).subscribe({
      next: (_res => {
        this.isLoading = false;
        let dat = (_res['esgKpiList'] || [])?.map(item => ({ ...item, isExpanded: false }));

        this.esgSubFieldList = JSON.stringify(_res['esgSubFieldList']) || [];
        this.annuallyColumns = [];
        this.quarterlyColumns = [];
        this.Columns = [];
        this.formatKpiDataTable([...dat], _res.headers);
      }),
      error: (error) => {
        this.isLoading = false;
        this.annuallyColumns = [];
        this.quarterlyColumns = [];
        this.Columns = [];
        this.formatKpiDataTable([], []);
      }
    });
  }

  /**
   * Formats the KPI data table.
   * 
   * @param data - The data array.
   * @param headers - The headers array.
   */
  formatKpiDataTable(data: any[], headers: any) {
    this.esgPeriodData = [...data];
    if (this.esgPeriodData?.length > 0 && this.esgPeriodData.some(x => x.kpiData?.length > 0)) {
      this.ifNoDataAvailable = false;
      this.showFootNote.emit(true);
    } else {
      this.ifNoDataAvailable = true;
      this.showFootNote.emit(false);
    }
    (this.esgPeriodData || []).forEach(x => {
      x?.kpiData.forEach((dat) => {
        const headerField = !(dat.quarter) ? `${dat.year}` : `${dat.quarter} ${dat.year}`;
        x[headerField] = dat.kpiValue;
      });
    });
    this.tableResultClone = [...this.esgPeriodData];
    headers.forEach(element => {
      const headerArr = element.header.split(" ");
      switch (headerArr.length) {
        case 1:
          this.Columns.push({
            field: element.field,
            header: element.header,
            year: headerArr[0],
            quarter: headerArr[0]
          });
          break;
        case 2:
          this.Columns.push({
            field: element.field,
            header: element.header,
            year: headerArr[1],
            quarter: headerArr[0]
          });
          break;
        default:
          break;
      }
    });
    if (this.Columns.length > 0) {
      this.ifNoDataAvailable = false;
    } else {
      this.ifNoDataAvailable = true;
    }
    this.sortTableColumns();
    this.searchfilterFunction();
    this.checkWidth();
  }

  /**
   * Removes the first element from the `Columns` array and updates the `masterKPICols` array
   * by combining the `frozenCols` array with the modified `Columns` array.
   */
  searchfilterFunction() {
    this.esgKPICols = [...this.frozenCols, ...this.Columns];
  }

  /**
   * Sorts the table columns.
   */
  sortTableColumns = () => {
    if ((this.quarterlyColumns || []).length > 0) {
      this.quarterlyColumns = this.quarterlyColumns
        .sort((firstItem, secondItem) => {
          if (firstItem.quarter < secondItem.quarter) return -1;
          if (firstItem.quarter > secondItem.quarter) return 1;
        })
        .sort((firstItem, secondItem) => {
          if (firstItem.year < secondItem.year) return -1;
          if (firstItem.year > secondItem.year) return 1;
        });
    }
    if ((this.annuallyColumns || []).length > 0) {
      this.annuallyColumns = this.annuallyColumns.sort((firstItem, secondItem) => {
        if (firstItem.year < secondItem.year) return -1;
        if (firstItem.year > secondItem.year) return 1;
      });
    }
  }



  /**
   * Updates the columns of the datatable based on the value of x.
   * If x is true, the quarterlyColumns will be used.
   * If x is false, the annuallyColumns will be used.
   * Sets the ifNoDataAvailable flag based on the length of the Columns array.
   * @param x - A boolean value indicating whether to use quarterlyColumns or annuallyColumns.
   */
  viewQuarterlyData(x: boolean) {
    if (x) {
      this.Columns = [...this.quarterlyColumns];
    }
    else {
      this.Columns = [...this.annuallyColumns];
    }

    if (this.Columns.length > 0) {
      this.ifNoDataAvailable = false;
    } else {
      this.ifNoDataAvailable = true;
    }
  }

  /**
   * Checks if a given value is a static column header.
   * @param val - The value to check.
   * @returns True if the value is a static column header, false otherwise.
   */
  isStaticCloumnHeader(val: any) {
    const staticFields = this.frozenCols.map(x => x.field);
    return staticFields.indexOf(val) == -1;
  }

  /**
   * Checks if the given value is a number.
   * 
   * @param str - The value to be checked.
   * @returns `true` if the value is a number, `false` otherwise.
   */
  isNumberCheck(str: any) {
    return isNumeric(str);
  }

  /**
   * Applies the filter to the body of the request.
   * @param searchFilter - The search filter to apply.
   * @returns The filter body object.
   */
  applyFilterBody(searchFilter: any) {
    if (this.decimal == null || this.decimal == undefined) {
      this.decimal = DecimalDigitEnum.Two;
    }
    let filterbody = {
      CompanyId: this.selectedCompany?.portfolioCompanyID.toString(),
      portfolioCompanyID: this.selectedCompany?.portfolioCompanyID.toString(),
      SubPageModuleId: this.selectedSubpageData?.subPageId,
      searchFilter: searchFilter,
      isAnnually: false,
      exportType: ExportEsgKpiType.None,
      isQuarterly: true,
      DecimalPlace: parseInt(this.decimal),
      unit: FinancialValueUnitsEnum.Absolute,
    };
    if (!searchFilter) {
      let sortOrder = [
        { field: "year", order: 1 },
        { field: "quarter", order: 1 },
      ];

      let _searchFilter = {
        sortOrder: sortOrder,
        periodType: "3 YR (Last 3 years)",
      };
      filterbody.searchFilter = _searchFilter;
    }
    else {

      filterbody.unit = !(this.esgKpiValueUnit) ? FinancialValueUnitsEnum.Absolute : this.esgKpiValueUnit.typeId;
    }
    switch (this.typeField) {
      case PeriodTypeFilterOptions.Quarterly: {
        filterbody.isQuarterly = true;
        filterbody.isAnnually = false
        break;
      }
      case PeriodTypeFilterOptions.Annual: {
        filterbody.isQuarterly = false;
        filterbody.isAnnually = true;
        break;
      }
    }
    return filterbody;
  }

  /**
   * Filters the KPI table globally based on the provided event.
   * @param event - The event object containing the filter criteria.
   */
  kpiTable_GlobalFilter(event) {
    this.esgKpiValueUnit = event?.UnitType == undefined ? {
      typeId: FinancialValueUnitsEnum.Absolute,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Absolute],
    } : event?.UnitType;
    this.searchFilter = event;
    this._esgService.setQueryFilter(this.searchFilter);
    this.getCompanyKpiValueByEsgModuleId(this.searchFilter);
    this.menuTrigger.closeMenu();
  }

  /**
   * Handles the initialization of table data editing.
   * @param rowData - The data of the row being edited.
   * @param column - The column being edited.
   */
  onTableDataEditInit(rowData: any, column: any) {
    this.isSingleClick = false;
    if (this.isToggleChecked) {
      return;
    }
    if ((isUndefined(this.esgKpiValueUnit) || Number(this.esgKpiValueUnit?.typeId) != FinancialValueUnitsEnum.Absolute) && rowData.kpiInfo != 'Text'){
      this.infoUpdatePopUpVisible = true;
    }
    else {
      if(!rowData.isHeader) {
        this.uniqueModuleCompany = {
          moduleId: this.kpiModuleId,
          companyId: this.selectedCompany?.portfolioCompanyID ?? 0,
          valueType: 'Esg',
          subPageId: this.selectedSubpageData?.subPageId ?? 0
        };
        let newRowData = { ...rowData };
        newRowData["KpiInfo"] = rowData.kpiInfo;
        newRowData["Kpi"] = rowData.kpiName;
        newRowData["IsEsg"] = true;
        newRowData["LineItemId"] = rowData.kpiId;
        newRowData["MappingId"] = this.getEsgKpiRecordId(rowData, column);
        this.dataColumns = column;
        this.newRowData = newRowData;
        this.confirmUpdatePopUp = true;
      }
      else{
        this.toastrService.error(GlobalConstants.CellEditError, "", { positionClass: EsgConstants.ToastCenterCenter });
      }
    }
  }

  /**
   * Cancels the update pop-up and hides it.
   */
  cancelButtonEvent() {
    this.confirmUpdatePopUp = false;
  }

  /**
   * Handles the event when the submit button is clicked.
   * @param results - The results of the submit action.
   */
  onSubmitButtonEvent(results: any) {
    if (results.code != null && results.code.trim().toLowerCase() == "ok") {
      this.showSuccessToast(results.message);
      this._esgService.setEsgDataUpdatedFlag(true);
      this.getCompanyKpiValueByEsgModuleId(this.searchFilter);
    } else {
      this.showSuccessToast(results.message);
      this._esgService.setEsgDataUpdatedFlag(false);
    }
    this.confirmUpdatePopUp = false;
  }

  /**
   * Closes the info pop-up.
   */
  closeInfoPopUp() {
    this.infoUpdatePopUpVisible = false;
  }

  /**
   * Validates the maximum length of the input value based on its type.
   * If the value is not an integer, it should have a maximum length of 21 characters.
   * If the value is an integer, it should have a maximum length of 16 characters.
   * 
   * @param event - The event object containing the input value.
   * @returns A boolean indicating whether the input value is valid or not.
   */
  validateMaxLength(event: any) {
    if (!Number.isInteger(Number(event.target.value))) {
      if (event.target.value.length == 21) return false;
    } else {
      if (event.target.value.length == 16) return false;
    }
    return true;
  }

  /**
   * Handles the completion of editing a column in the ESG datatable.
   * @param index - The index of the column being edited.
   * @param col - The column being edited.
   * @param rowData - The data of the row being edited.
   * @param event - The event object containing the details of the edit completion.
   */
  onColumnEditComplete(index: any, col: any, rowData: any, event) {
    let prevVal = this.updateModel.previousVal;
    rowData[col.field] = event.target.value !== "" ? event.target.value : null;
    let currVal = rowData[col.field];
    if (!this.confirmUpdatePopUp && currVal != prevVal) {
      this.updateModel.updatedVal = rowData[col.field] === "" ? undefined : rowData[col.field];
      this.confirmUpdatePopUp = true;
    }
    else
      this.onEsgDataUpdateCancel("");
  }

  /**
   * Handles the completion of editing a textarea in the ESG datatable.
   * @param rowData - The data of the row being edited.
   */
  onTextareaEditComplete(rowData: any) {
    this.esgPeriodData.filter((obj => obj.kpiId !== rowData.kpiId))
      .forEach(row => {
        this.Columns.forEach(col => {
          row[`${col.field} editable`] = false;
        });
      });
  }

  /**
   * Handles the cancellation of an ESG data update.
   * Reverts the updated value to the previous value and clears the cell edit state.
   * 
   * @param event - The event object associated with the cancellation.
   */
  onEsgDataUpdateCancel(event: any) {
    let objIndex = this.esgPeriodData.findIndex((obj => obj.kpiId == this.updateModel.kpiId));
    this.esgPeriodData[objIndex][this.updateModel.colName] = this.updateModel.previousVal;
    this.clearCellEdit();
  }

  /**
   * Clears the cell edit state and updates the data.
   */
  clearCellEdit() {
    let objIndex = this.esgPeriodData.findIndex((obj => obj.kpiId == this.updateModel.kpiId));
    this.Columns.forEach(col => {
      this.esgPeriodData[objIndex][`${col.field} editable`] = false;
    })

    this.confirmUpdatePopUp = false;
    this.updateModel = {};
  }

  /**
   * Validates the input value to ensure it is a valid number.
   * If the input value is not a valid number, it will be converted to a float with 2 decimal places.
   * 
   * @param event - The event object representing the input event.
   */
  validateNumber(event: any) {
    if (event.which != 15) {
      let ex: RegExp = new RegExp(/^-*\d*(?:[.,]\d{1,10})?$/);
      if (!ex.test(event.target.value)) {
        if (!Number.isInteger(Number(event.target.value))) {
          event.target.value = parseFloat(event.target.value).toFixed(2);
        }
      }
    }
  }

  /**
   * Handles the event when a column is being edited.
   * @param event - The event object containing information about the edit event.
   */
  onColumnEdit(event: any) {
    event.target.blur();
  }

    /**
   * Opens the ESG static info pop-up model.
   * 
   * @param item - The item containing the KpiId, SubPageModuleId, and CompanyId.
   */
  openEsgStaticInfoPopUpModel = (item: any) => {
    let requestBody = {
      KpiId: item.kpiId,
      SubPageModuleId: item.subPageId,
      CompanyId: this.companyDetails.portfolioCompanyID
    };

    this.selectedEsgKpiItem = item;
    this.esgKpiStaticList = JSON.parse(this.esgSubFieldList);
    this.isLoading = true;
    this._esgService.getEsgKpiStaticInfoDetailsById(requestBody).subscribe({
      next: (result => {
        this.esgKpiStaticList = (this.esgKpiStaticList || []).map(item => {
          let obj = result.find(x => x.fieldId == item.fieldId);
          if (obj) {
            item.value = obj.value;
          }
          item.portfolioCompanyID = this.companyDetails.portfolioCompanyID;
          item.kpiId = requestBody.KpiId;
          item.esgModuleId = requestBody.SubPageModuleId;
          return item;
        });
        this.selectedStaticItem = this.esgKpiStaticList[0] || {};
        this.selectedStaticItem.index = 0;
        this.isLoading = false;
        this.showKpiStaticInfoModal = true;
        this.disablePrimaryButton = true;
      }),
      error: (err => {

      })
    });
  }

  /**
   * Handles the update of ESG static information.
   * @param item - The item to be updated.
   */
  onEsgStaticInfoUpdate = (item: any) => {
    let data = this.esgKpiStaticList.filter(x => x.value != null && x.value != undefined);
    this._esgService.AddOrUpdateEsgKpiStaticValues(data).subscribe({
      next: (result => {
        this.successToaster();
        this.showKpiStaticInfoModal = false;
      }),
      error: (err => {
        this.isLoading = false;
        this.toastrService.error(ErrorMessage.SomethingWentWrong, "", { positionClass: "toast-center-center" });
      })
    })
  }

  /**
   * Handles the cancellation of the ESG static info update.
   * Resets the selectedStaticItem and hides the KPI static info modal.
   * 
   * @param item - The item being cancelled.
   */
  onEsgStaticInfoUpdateCancel = (item: any) => {
    this.selectedStaticItem = {};
    this.showKpiStaticInfoModal = false;
  }

  /**
   * Handles the click event when a static item is clicked.
   * 
   * @param item - The selected static item.
   * @param idx - The index of the selected static item.
   */
  onStaticItemClick = (item: any, idx: any) => {
    this.selectedStaticItem = item;
    this.selectedStaticItem.index = idx;
  }

  /**
   * Handles the change event for the static info description.
   * Enables the primary button.
   */
  onStaticInfoDescChange = () => {
    this.disablePrimaryButton = false;
  }

  /**
   * Displays a success toaster message indicating that the entry has been updated successfully.
   */
  successToaster() {
    this.toastrService.success("Entry updated successfully", "", { positionClass: "toast-center-center" });
  }
  /**
     * Displays an error toast message.
     * 
     * @param message - The error message to display.
     * @param title - The title of the error toast. (optional)
     * @param position - The position of the error toast. (optional)
     */
    showErrorToast(message: string, title: string = '', position: string = 'toast-center-center'): void {
      this.toastrService.error(message, title, { positionClass: position });
    }
    /**
     * Displays a success toast message.
     * 
     * @param message - The message to be displayed in the toast.
     * @param title - The title of the toast (optional).
     * @param position - The position of the toast on the screen (optional, default is 'toast-center-center').
     */
    showSuccessToast(message: string, title: string = '', position: string = 'toast-center-center'): void {
      this.toastrService.success(message, title, { positionClass: position });
    }
    expandText(rowData:any)
    {
      rowData.isExpanded = !rowData.isExpanded;
    }
  /**
   * Checks the width of each text element and updates the isTruncated property accordingly.
   */
  checkWidth(): void {
    this.isTruncatedAny = false;
    this.textElements.forEach((elementRef, index) => {
      const columnWidth = elementRef.nativeElement.offsetWidth;
      const charCount = this.calculateCharCount(columnWidth);
      let kpiValue = this.esgPeriodData[index].kpiName?.length;
      this.esgPeriodData[index].isTruncated = kpiValue > charCount;
      if (this.esgPeriodData[index].isTruncated) {
        this.isTruncatedAny = true;
      }
    });
  }
  /**
 * Calculates the number of characters that can fit within a given width.
 * 
 * @param width - The width of the container in pixels.
 * @returns The number of characters that can fit within the given width.
 */
  calculateCharCount(width: number): number {
    const averageCharWidth = 8; // Average width of a character in pixels (this value may vary based on font)
    return Math.floor(width / averageCharWidth);
  }
}