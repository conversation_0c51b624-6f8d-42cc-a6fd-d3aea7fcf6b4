import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormGroup, FormControl } from '@angular/forms';
import { ManagedAccountReviewFormComponent } from './managed-account-review-form.component';
import { ManagedAccountService } from '../managed-account.service';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('ManagedAccountReviewFormComponent', () => {
  let component: ManagedAccountReviewFormComponent;
  let fixture: ComponentFixture<ManagedAccountReviewFormComponent>;
  let managedAccountService: jasmine.SpyObj<ManagedAccountService>;

  beforeEach(() => {
    const managedAccountServiceSpy = jasmine.createSpyObj('ManagedAccountService', ['emitGoToStep']);

    TestBed.configureTestingModule({
      declarations: [ManagedAccountReviewFormComponent],
      providers: [{ provide: ManagedAccountService, useValue: managedAccountServiceSpy }]
    });

    fixture = TestBed.createComponent(ManagedAccountReviewFormComponent);
    component = fixture.componentInstance;
    managedAccountService = TestBed.inject(ManagedAccountService) as jasmine.SpyObj<ManagedAccountService>;
    component.ManagedAccountModel = { managedAccountName: 'Test Account' };
    fixture.detectChanges();
  });

  it('should create', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should initialize formattedManagedAccountModel on ngOnInit', () => {
    component.ManagedAccountModel = { managedAccountName: 'Test Account' };
    component.ngOnInit();
    expect(component.formattedManagedAccountModel).toEqual({ managedAccountName: 'Test Account' });
  });

  it('should call emitGoToStep with correct parameter', () => {
    const step = 2;
    component.triggerGoToStep(step);
    expect(managedAccountService.emitGoToStep).toHaveBeenCalledWith(step);
  });

});
