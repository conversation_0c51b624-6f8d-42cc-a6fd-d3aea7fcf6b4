import { Component, EventEmitter, Input, Output } from "@angular/core";
import { AddClo } from "../add-clo-list.model";

@Component({
  selector: "app-clo-review-form",
  templateUrl: "./clo-review-form.component.html",
  styleUrls: ["./clo-review-form.component.scss"],
})
export class CloReviewFormComponent {
  @Input() CLOModel: AddClo;
  @Input() companyName: string;
  @Output() isCLOForm = new EventEmitter<boolean>();
  @Output() cloData = new EventEmitter<AddClo>();
  company: string;
  cloModel: AddClo;
  component: { domicile: string; issuer: string; arranger: string; trustee: string; priced: string; closed: string; lastRefiDate: string; lastResetDate: string; callEndDate: string; originalEndOfReinvestmentDate: string; currentEndOfReinvestmentDate: string; currentMaturityDate: string; };

  constructor() {}

  ngOnInit(): void {
    this.cloModel = { ...this.CLOModel };
    this.assignDefaultValues();
    this.company = this.companyName;
  }

  onEdit(value: boolean): void {
    this.isCLOForm.emit(value);
    this.cloData.emit(this.cloModel);
  }

  private assignDefaultValues(): void {
    for (const key in this.cloModel) {
      if (this.cloModel[key] === null || this.cloModel[key] === '') {
        this.cloModel[key] = "N/A";
      }
    }
  }
}
