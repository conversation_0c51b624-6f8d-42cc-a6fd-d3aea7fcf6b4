.scrolling-wrapper{
	overflow-x: auto;
}

.h-scroll {
  height: 90vh; /* %-height of the viewport */
  position: fixed;
  overflow-x: scroll;
}
.container {
    
    
    border: none !important; position: relative; margin: 0 auto;
    margin-top: 23px;
  }
  .container input {
    opacity: 0;
    position: absolute;
    z-index: 2;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  .imgcross{
    float: right;
    margin-left: 181px;
    position: absolute;
    top: 9px;
    cursor: pointer;
    color: red;
    font-size: 21px;
    margin-top: -61px;
  }
  .card-block {
     min-height: 90px; 
   
}
.imgsize{
  width: 75% !important;
  margin-top: 10px;
  height: 55px !important;
}
.crossicon{
  margin-right: -20px;
    cursor: pointer;
    margin-top: -20px;
    float: right;
}
.box {
  
  width: 240px;
  height: 150px;
  /* margin-top: 10px; */
  margin: 6px;
  border: 1px solid #9e9e9e;
}

.box > img {
  width: 100%;
  height: 100%;
}
.imgfit{
  width: 150px;
  height: 100px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: 50% 50%;
}

.fill { object-fit: fill; }
.contain { object-fit: contain; border-color: #DEDFE0 !important; }
.cover { object-fit: cover; }
.none { object-fit: none; }
.scale-down { object-fit: scale-down; }

  

 .scrollingbar{
	overflow-x: scroll;
}
 

.card-block{
	height: 250px;
	background-color: #fff;
	border: none;
	background-position: center;
	background-size: cover;
	/* transition: all 0.2s ease-in-out !important; */
	border-radius: 24px;
  object-fit: contain;
}
.imgcontainer {
  /* margin: 10px; */
  margin: 8px;
  width: 125px;
  height: 125px;
  line-height: 115px;
  text-align: center;

}

.imgcontainer img {
  position: relative;
vertical-align: middle;
top: 50%;
-webkit-transform: translateY(-50%);
min-height: 100%;
min-width: 100%;
object-fit:cover;
}
.resize_fit_center {
  max-width:100%;
  max-height:100%;
  vertical-align: middle;
}