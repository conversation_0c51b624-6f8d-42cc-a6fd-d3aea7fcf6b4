.file-history-btn {
  position: relative;
  background: transparent;
  cursor: pointer;
  transition: transform 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
  
  &:hover {
    transform: scale(1.1);
    background-color: rgba(0, 0, 0, 0.04);
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  mat-icon {
    color: inherit;
    font-size: 24px;
    height: 24px;
    width: 24px;
    line-height: 24px;
  }

  .notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #ff4757;
    color: white;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    min-width: 16px;
    height: 16px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;
    z-index: 2;
  }
}

:host {
  display: inline-block;
  margin: 0 8px;
}