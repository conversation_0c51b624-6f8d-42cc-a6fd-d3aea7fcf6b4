<div class="email-reminder-container">
  <!-- Header Section with Portfolio Company and Document Type (Read-only) -->
  <div class="header-selection-container shadow-sm">
    <div class="row">
      <div class="col-md-6 portfolio-company-section">
        <div class="selection-group">
          <label class="Body-R mb-2 custom-headertext">Portfolio Company</label>
          <div class="chips-container d-flex flex-wrap" #portfolioContainer>
            <!-- Visible chips -->
            <span *ngFor="let company of selectedPortfolioCompanies; let i = index"
              class="selection-chip Body-R badge badge-light mr-2 mb-2">
              {{company.name}}
            </span>
          </div>
        </div>
      </div>
      <div class="col-md-6 document-type-section">
        <div class="selection-group">
          <label class="custom-headertext Body-R mb-2">Document Type</label>
          <div class="chips-container d-flex flex-wrap" #documentContainer>
            <!-- Visible chips -->
            <span *ngFor="let docType of selectedDocumentTypes;"
              class="selection-chip Body-R badge badge-light mr-2 mb-2">
              {{docType.name}}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="form-section shadow-sm">
    <form [formGroup]="reminderForm" (ngSubmit)="onSubmit()">

      <div class="form-core-section">
        <div class="email-box-section card">
          <div class="card-header bg-light text-center">
            <span class="Body-M custom-headertext">Automated Email Request</span>
          </div>
          <div class="card-body"> <!-- To Field -->
            <div class="form-group mb-3">
              <label for="to" class="form-label Body-R">To <span class="text-danger">*</span></label>
              <div class="email-input-container">
                <div class="input-with-chips" [class.is-invalid-container]="isFieldInvalid('to')">
                  <div class="chips-container-inline">
                    <span *ngFor="let email of toEmailArray" class="selection-chiptocc Body-R">
                      {{ email }}
                      <img [src]="'assets/dist/images/cross-blackicon.svg'" alt="remove" class="remove-icon"
                        (click)="removeToEmail(email)">
                    </span>
                  </div>
                  <input type="email" class="form-control reminder-input Body-R" id="to" formControlName="to"
                    (keydown.enter)="addToEmail($event)">
                </div>
                <div *ngIf="isFieldInvalid('to')" class="invalid-feedback d-block">
                  {{getFieldError('to')}}
                </div>
              </div>
            </div>

            <!-- CC Field -->
            <div class="form-group mb-3">
              <label for="cc" class="form-label Body-R">Cc</label>
              <div class="email-input-container">
                <div class="input-with-chips">
                  <div class="chips-container-inline">
                    <span *ngFor="let recipient of ccRecipientsArray" class="selection-chiptocc Body-R">
                      {{ recipient.email || recipient.name }}
                      <img [src]="'assets/dist/images/cross-blackicon.svg'" alt="remove" class="remove-icon"
                        (click)="removeCcRecipient(recipient)">
                    </span>
                  </div>
                  <input type="email" class="form-control Body-R reminder-input" id="cc" formControlName="cc"
                    (keydown.enter)="addCcRecipient($event)">
                </div>
                <div *ngIf="isFieldInvalid('cc')" class="invalid-feedback">
                  {{getFieldError('cc')}}
                </div>
              </div>
            </div>

            <!-- Subject Field -->
            <div class="form-group mb-3">
              <label for="subject" class="form-label Body-R">Subject <span class="text-danger">*</span></label>
              <input type="text" class="form-control Body-R reminder-input" id="subject" formControlName="subject"
                placeholder="Enter Subject" [class.is-invalid]="isFieldInvalid('subject')">
              <div *ngIf="isFieldInvalid('subject')" class="invalid-feedback Body-R">
                {{getFieldError('subject')}}
              </div>
            </div>

            <!-- Message Field with Quill Editor -->
            <div class="form-group mb-3">
              <label for="message" class="Txt-label Body-R">Message <span class="text-danger">*</span></label>
              <div class="quill-editor-container email-quill-container w-100 border rounded"> <app-custom-quill-editor
                  [formControl]="reminderForm.get('message')"
                  (ngModelChange)="onMessageChange($event)" class="custom-quill-editor email-editor Body-R">
                </app-custom-quill-editor>
              </div>
              <div *ngIf="isFieldInvalid('message')" class="invalid-feedback d-block Body-R">
                {{getFieldError('message')}}
              </div>
            </div>
          </div>
        </div> <!-- Follow-up Reminders section -->
        <div class="email-remainder-section card">
          <div class="card-header bg-light text-center">
            <span class="Body-M custom-headertext">Reminders</span>
          </div>
          <div class="card-body">
            <!-- Reminder Frequency -->
            <div class="form-group mb-3">
              <label class="custom-headertext Caption-R mb-2">Reminder Frequency <span class="text-danger">*</span></label>
              <div class="reminder-frequency-group">
                <kendo-formfield>
                  <ul class="k-list-horizontal d-flex">
                    <li class="k-radio-item mr-3">
                      <input type="radio" value="monthly" kendoRadioButton formControlName="reminderFrequency"
                        (change)="onReminderFrequencyChange()" class="custom-radio-btn" />
                      <label class="k-radio-label Body-R ml-2">Monthly</label>
                    </li>
                    <li class="k-radio-item mr-3">
                      <input type="radio" value="quarterly" kendoRadioButton formControlName="reminderFrequency"
                        (change)="onReminderFrequencyChange()" class="custom-radio-btn" />
                      <label class="k-radio-label Body-R ml-2">Quarterly</label>
                    </li>
                    <li class="k-radio-item">
                      <input type="radio" value="annual" kendoRadioButton formControlName="reminderFrequency"
                        (change)="onReminderFrequencyChange()" class="custom-radio-btn" />
                      <label class="k-radio-label Body-R ml-2">Annual</label>
                    </li>
                  </ul>
                </kendo-formfield>
              </div>
              <div *ngIf="isFieldInvalid('reminderFrequency')" class="invalid-feedback d-block">
                {{getFieldError('reminderFrequency')}}
              </div>
            </div>

            <!-- Total Number of Reminders -->
            <div class="form-group mb-3">
              <div class="d-flex align-items-center mb-2">
                <label class="form-label custom-headertext Caption-R mb-0 mr-3">Total No. of Reminder <span
                    class="text-danger">*</span></label>
                <div class="reminder-number-input">
                  <kendo-numerictextbox formControlName="totalReminders" [min]="1" [max]="5" [value]="3"
                    [spinners]="true" [decimals]="0" [autoCorrect]="true" [format]="'n0'" class="compact-numeric"
                    (valueChange)="onTotalRemindersChange()">
                  </kendo-numerictextbox>
                </div>
              </div>
              <div *ngIf="isFieldInvalid('totalReminders')" class="invalid-feedback d-block">
                {{getFieldError('totalReminders')}}
              </div>
            </div>

            <!-- First Reminder Date -->
            <div class="form-group mb-3">
              <label class="form-label Caption-R mb-2">1st Reminder <span class="text-danger">*</span></label>
              <kendo-datepicker formControlName="firstReminder" [format]="'dd MMM yyyy'" [min]="minDate" class="w-100"
                placeholder="Select date">
              </kendo-datepicker>
              <div *ngIf="isFieldInvalid('firstReminder')" class="invalid-feedback d-block">
                {{getFieldError('firstReminder')}}
              </div>
            </div>

            <!-- Dynamic Reminder Days -->
            <ng-container *ngFor="let reminder of reminderDays; let i = index">
              <div class="form-group mb-3" *ngIf="shouldShowReminder(i + 2)">
                <label class="form-label Body-R mb-2">{{getReminderLabel(i + 2)}} </label> <kendo-combobox
                  [data]="getReminderDayOptions(i + 2)" [formControlName]="'reminder' + (i + 2)" [textField]="'text'"
                  [valueField]="'value'" [valuePrimitive]="true" [defaultItem]="defaultItem"
                  (valueChange)="onReminderValueChange(i + 2)" class="w-100">
                </kendo-combobox>
                <div *ngIf="isFieldInvalid('reminder' + (i + 2))" class="invalid-feedback d-block">
                  {{getFieldError('reminder' + (i + 2))}}
                </div>
              </div>
            </ng-container>
          </div>
        </div>
      </div>
      <div class="action-button-section">
        <div class="d-flex justify-content-start">
          <button kendoButton themeColor="primary" fillMode="outline" class="Body-R btn-custom-width"
            type="button">Reset</button>

        </div>

        <div class="header-actions"> <button kendoButton themeColor="primary" fillMode="outline"
            class="Body-R btn-custom-width" type="button" (click)="onCancel()">Cancel</button>
          <button kendoButton themeColor="primary" fillMode="solid" type="submit"
            class="Body-R btn-custom-width btn-create">Save</button>
        </div>
      </div>
    </form>
  </div>
</div>