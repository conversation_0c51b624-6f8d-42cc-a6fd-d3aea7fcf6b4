@import "../../../../../variables.scss";
@import "../../../../../assets/dist/css/font.scss";

// Common pixel values
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-10: 10px;
$spacing-14: 14px;
$spacing-15: 15px;

$border-radius-sm: 4px;
$border-radius-lg: 20px;

$icon-size-sm: 28px;
$icon-size-md: 32px;

$height-sm: 28px;
$height-md: 32px;
$height-container: calc(100vh - 200px);
$height-min-header: 60px;
$height-no-data: 300px;

.email-user-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: $height-container;
  border: 1px solid $nep-light-border;
  background-color: $white-color;
  border-radius: $border-radius-sm;

  .no-content-image {
    width: 180px;
    height: auto;
    margin-bottom: $spacing-lg;
  }
}

.email-user-wrapper {
  height: $height-container;
  background-color: $white-color;
  display: flex;
  flex-direction: column;
  border: 1px solid $nep-light-border;
  padding: $spacing-xl $spacing-xl $spacing-15 $spacing-xl;
}

.user-info-title-container {
  background-color: $light-blue-color;
  border-color: $Neutral-Gray-10;
  border-style: solid;
  border-width: 1px 1px 0 1px;
  border-radius: $spacing-xs $spacing-xs 0 0;

  .user-info-text {
    padding: $spacing-14 0px;
  }
}

.user-info-grid {
  height: calc(100% - 48px) !important;
  width: 100% !important;
}

/* Side Overlay Styles */
.notification-sidebar {
  padding: 1rem 1.5rem;
  box-shadow: $shadow-short !important;

  .side-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid $Neutral-Gray-30;
  }

  .title-h {
    @extend .Body-M;
    color: $Neutral-Gray-70;
  }

  .form-group {
    margin-bottom: 1.25rem;

    label {
      margin-bottom: 0.5rem;
      display: block;
    }
  }

  .custom-bottom {
    padding-bottom: 1rem;
    padding-right: 1.25rem;
  }
}

.k-multiselect-custom {
  width: 100% !important;

  .k-chip {
    border-color: transparent;
    color: $content-paragraph;
  }
}

// For selected document type in the dropdown
.doc-type-item {
  display: flex;
  align-items: center;
  width: 100%;

  .TextTruncate {
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.form-control {
  height: $height-md;
  border: 1px solid $Neutral-Gray-10;
  border-radius: $border-radius-sm;
}

.input-container {
  position: relative;
  width: 100%;
}

.custom-input {
  width: 100%;
  height: $height-md;
  border: 1px solid $Neutral-Gray-10;
  border-radius: $border-radius-sm;
}

.is-invalid {
  border-color: $Negative-100;
}

.doc-type-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;

  .doc-type-chip {
    color: $content-paragraph;
    padding: $spacing-xs $spacing-10;
    display: inline-flex;
    align-items: center;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    .doc-name {
      background-color: $Primary-40;
      border-radius: $border-radius-lg;
      padding: $spacing-xs $spacing-10;
      display: inline-flex;
      align-items: center;
      max-width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .more-indicator {
      margin-left: $spacing-xs;
      background-color: $Primary-40;
      position: relative;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: $border-radius-lg;
      padding: $spacing-xs $spacing-10;
      display: inline-flex;
      align-items: center;
      cursor: pointer;
      max-width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}


.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  padding: 20px;

  .no-data-image {
    width: 140px;
    height: auto;
    margin-bottom: $spacing-lg;
  }

  .no-data-text {
    color: #666;
    font-size: 14px;
    font-weight: 500;
    margin: 0;
  }
}

.doc-tooltip-item {
  border-bottom: 1px solid $Neutral-Gray-10;
  padding: $spacing-10 $padding-large;
}

::ng-deep .k-popover-body {
  padding: 0px !important;
}

.action-icon {
  height: 1.5rem;
  width: 1.5rem;
  border-radius: 0.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.action-icon:hover {
  background-color: $delete-on-hover-color;
}

.action-icon:active {
  background-color: $delete-on-click-color;
}

// delete confirmation dialog
.warning-text {
    color: $Negative-100 !important;
}