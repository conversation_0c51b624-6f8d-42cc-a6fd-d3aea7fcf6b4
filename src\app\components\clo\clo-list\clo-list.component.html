<div class="row ml-4 mr-4  clo-container" *ngIf="cloCompanyList?.length > 0 ; else noCloCompanies">
    <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0">
    <div class="card-table row mr-0 ml-0">
      <div class="c-table-header col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 Heading2-R TextTruncate">
        <img class="image-custom" src='assets/dist/images/Frame.svg' alt="Company Icon">
        Investment Company & CLO
      </div>
      <div class="clo-table-body col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 Heading2-R pr-0 pl-0">
        <div class="row mr-0 ml-0 clo-table-content">
          <ng-container *ngFor="let clo of cloCompanyList; let i = index" >
            <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0 company-item"
            [ngClass]="{'company-active': clo.isExpanded, 'company-inactive': !clo.isExpanded, 'previous-active': cloCompanyList[i + 1]?.isExpanded}">
              <div class="custom-footnote col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 clo-item TextTruncate"                   
                [ngClass]="clo.isExpanded ? 'clo-active':'clo-in-active'"
                (click)="expandPanel(clo)">
                <div title="{{clo.name}}" class="float-left TextTruncate Body-R title">{{clo.name}}</div>
                <div class="float-right">
                  <a> <img
                      src="assets/dist/images/{{clo.isExpanded ? 'arrow-down.svg' :'chevron-down-i.svg'}}"
                      alt="Sort left" /> </a>
                </div>
              </div>
              <ng-container *ngIf="clo.isExpanded">
                <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 child-add-item TextTruncate">
                  <div class="float-left add-clo-item Heading2-M">CLO List</div>
                  <div class="float-right add-clo-btn pr-0">
                    <button id="add-clo" kendoButton class="kendo-custom-button" themeColor="primary" (click)="addCLO(clo)">
                      <img src='assets/dist/images/addicon.svg' class="custom-padding" alt="add Icon" >Add CLO</button>
                  </div>
                </div>
                <ng-container *ngIf="clo.items?.length == 0">
                  <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 no-content-section pt-3">
                    <div class="text-center">
                      <img src="assets/dist/images/Illustrations.svg" alt="No Content" class="no-content-image">
                    </div>
                    <div class="text-center no-content-text pt-3 pb-2 Body-M">
                      Please Click on the CTA to Add CLO
                    </div>
                  </div>
                </ng-container>
                <ng-container *ngIf="clo.items?.length > 0">
                  <div title="{{item.issuer}}" 
                    class="Body-R child-item TextTruncate clo-content"
                    *ngFor="let item of clo.items">
                    <span class="Body-R clo-name" (keypress)="redirectToCloViewPage(item.uniqueID)" (click)="redirectToCloViewPage(item.uniqueID)">{{item.issuer}}</span>
                    <div class="float-right delete-icon" id="delete-clo" (click)="showDelete(item);">
                      <img  class="deleteicon float-right"  
                          src="assets/dist/images/delete-company.svg" alt="Delete Icon" />
                    </div>
                  </div>
                </ng-container>
              </ng-container>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>
<ng-template #noCloCompanies>
  <div class="row ml-4 mr-4 no-companylistdata">
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 no-content-section pt-3">
      <div class="text-center">
        <img src="assets/dist/images/Illustrations.svg" alt="No Content" class="no-content-image">
      </div>
      <div class="text-center no-content-text pt-3 pb-2 Body-M">
        Please Add Investment Company Before you Add CLO
      </div>
    </div>
  </div>
</ng-template>
<div *ngIf="confirmDelete" id="confirm-modal">
  <app-delete-confirmation-modal [deletedCloName]="deletedCloName" [deleteNoteType]="CLO" (PrimaryButtonEventHandler)="deleteCLO()" [modalTitle]="modalTitle" (SecondaryButtonEventHandler)="cancelDelete()"></app-delete-confirmation-modal>
</div>

<app-loader-component *ngIf="isLoading"></app-loader-component>