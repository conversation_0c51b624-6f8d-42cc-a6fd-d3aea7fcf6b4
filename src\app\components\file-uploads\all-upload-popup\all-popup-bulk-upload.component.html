<div class="row ml-0 mr-0 all-bulk-upload">
    <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pl-0 pr-0">
        <div class="nep-card-header">
            All Uploads
            <a id="close-icon" class="float-right close-icon cursor-filter cursor-pointer" (click)="onClose()">
                <i class="pi pi-times custom-close-icon"></i>
            </a>
        </div>
        <div class="nep-card-body upload-body">
            <div class="row mr-0 ml-0 main-row">
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pl-0 pr-0">
                    <div class="row mr-0 ml-0">
                        <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12 pl-0 pr-0">
                            <div class="row mr-0 ml-0 supporting-documents-single-header">
                                <div class="col-md-12 col-lg-12 col-12 col-xl-12 col-sm-12 pr-0 pl-0">
                                    <div class="dropzone all-popup-bulk-upload" fileDragDrop>
                                        <div class="text-wrapper">
                                            <div class="centered sinle-file-upload-style">
                                                <label *ngIf="singleUploadFiles.length==0"
                                                    class="single-upload-lable  mb-0" title="Browse file
                                                    template(s) to upload" for="file">Browse file
                                                        template(s) to upload</label>
                                                <mat-chip-list *ngIf="singleUploadFiles.length>0"
                                                    class="chip-text-align single-file-upload">
                                                    <mat-chip *ngFor="let file of singleUploadFiles; let i = index"
                                                        removable (removed)="removeSingleDocumentFile()">
                                                        <div class="multiple-chip-content TextTruncate">
                                                            <div class="float-left pr-1">
                                                                <img [src]="'assets/dist/images/FaRegFileExcel.svg'" alt="xlsxfile">
                                                            </div>
                                                            <div title="{{ file.name }}" class="float-right single-file-upload-padding TextTruncate">{{ file.name }}</div>
                                                            <img id="remove-single-doc" [src]="'assets/dist/images/FiX.svg'" class="close-icon-chips ml-1 pt-2" (click)="removeSingleDocumentFile()"
                                                            alt="cancel" />
                                                        </div>
                                                    </mat-chip>
                                                </mat-chip-list>
                                                <input class="single-file-drop cursor-pointer d-none" #singleFileDropRef accept=".xlsx,.xls" type="file" name="file"
                                                    id="singleFileDropRef" (change)="onSingleFileChange($event.target.files)">
                                                <label class="single-file-browse cursor-pointer mt-1 mb-0" for="singleFileDropRef">Browse</label>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div
                            class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12 pr-0 pl-0 supporting-documents">
                            <div class="row mr-0 ml-0 supporting-documents-header">
                                <div class="col-md-12 col-lg-12 col-12 col-xl-12 col-sm-12 pr-0 pl-0  user-header">
                                    <div class="float-left supporting-documents-title TextTruncate">Supporting
                                        Document(s)</div>
                                    <div class="float-right supporting-documents-title TextTruncate">
                                        <label for="fileDropRef" class="mb-0"><img class="uploadFile" [src]="'assets/dist/images/MdOutlineAttachFile.svg'"
                                                alt="" /></label>
                                        <label for="fileDropRef" class="cursor-pointer mb-0" title="Attach">Attach</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row mr-0 ml-0 supporting-documents-body">
                                <div
                                    class="col-md-12 col-lg-12 col-12 col-xl-12 col-sm-12 pr-0 pl-0 repository-custom-header user-header">
                                    <div class="left-container modal-default-height left-c-image-height all-popup-bulk-upload" fileDragDrop>
                                        <div class="top-left-container">
                                            <input type="file" name="file" #fileDropRef id="fileDropRef" multiple
                                                class="multiple-file-drop"
                                                (change)="onFileChange($event.target.files)" hidden />
                                            <div class="centered" *ngIf="files.length==0">
                                                <div class="flex-container height-input-drop-file">
                                                    <div class="centered">
                                                        <label class="mb-0"><img class="uploadFile"
                                                                [src]="getIcons('uploadFile')" alt="" /></label>
                                                    </div>
                                                    <div class="centered">
                                                        <label class="multiple-file-browse mb-0">Upload
                                                            supporting documents here</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="top-left-container" *ngIf="files.length>0">
                                                <div class="chips-container multiple-supporting-documents">
                                                    <mat-chip-list class="chip-text-align multiple-file-upload">
                                                        <mat-chip *ngFor="let file of files; let i = index" removable>
                                                            <div class="multiple-chip-content TextTruncate d-flex align-items-center justify-content-between">
                                                                <div class="d-flex align-items-left ">
                                                                    <ng-container [ngSwitch]="file.extension">
                                                                        <img *ngSwitchCase="FileExtension.XLSX" [src]="'assets/dist/images/FaRegFileExcel.svg'" alt="xlsxfile">
                                                                        <img *ngSwitchCase="FileExtension.PDF" [src]="'assets/dist/images/Left Icon.svg'" alt="pdffile">
                                                                        <img *ngSwitchCase="FileExtension.ZIP" [src]="'assets/dist/images/FaRegFileArchive.svg'" alt="zipfile">
                                                                        <img *ngSwitchCase="FileExtension.PNG" [src]="'assets/dist/images/FaRegFileImage.svg'" alt="imagefile">
                                                                        <img *ngSwitchCase="FileExtension.JPG" [src]="'assets/dist/images/FaRegFileImage.svg'" alt="imagefile">
                                                                        <img *ngSwitchCase="FileExtension.TXT" [src]="'assets/dist/images/FaRegFileWord.svg'" alt="docfile">
                                                                        <img *ngSwitchCase="FileExtension.DOCX" [src]="'assets/dist/images/FaRegFileWord.svg'" alt="docfile">
                                                                        <img *ngSwitchCase="FileExtension.PPTX" [src]="'assets/dist/images/FaRegFilePowerpoint.svg'" alt="pptfile">
                                                                        <img *ngSwitchCase="FileExtension.PPT" [src]="'assets/dist/images/FaRegFilePowerpoint.svg'" alt="ppt file">
                                                                        <img *ngSwitchDefault [src]="'assets/dist/images/FaRegFileArchive.svg'" alt="defaultfile">
                                                                    </ng-container>
                                                                </div>
                                                                <div class="chip-file-name TextTruncate mx-auto align-items-center pl-1" title="{{ file.name }}">{{ file.name }}</div>
                                                                <img id="remove-support-doc" [src]="'assets/dist/images/FiX.svg'" class="close-icon-chips ml-1" (click)="removeSupportDocumentFile(i)"
                                                                    alt="cancel" />
                                                            </div>
                                                        </mat-chip>
                                                    </mat-chip-list>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12  pl-0 pr-0 pb-1">
                            <div class="popupcommon-labelpadding">Comments</div>
                            <div class="custom-nep-input kpi-modal-textarea">
                                <label class="nep-input nep-input-textarea supporting-documents-text-area">
                                    <textarea spellcheck="false" data-ms-editor="true"
                                        placeholder="Enter comments here..." class="all-upload-popup-textarea pl-0"
                                        autocomplete="off" rows="4" cols="50" name="comment" [(ngModel)]="comments"
                                        #comment="ngModel"></textarea>

                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="nep-card-footer nep-card-right nep-modal-footer add-or-update-modal-footer">
                <nep-button id="cancel-button" (click)="onClose()"  class="cursor-pointer" Type="Secondary">
                    Cancel
                </nep-button>

                <nep-button id="confirm-button" Type="Primary" (click)="onSubmit()" [disabled]="singleUploadFiles.length === 0 " class="kpinep-butn-pl cursor-pointer">
                    Confirm
                    <span class="pl-1 upload-loader pt-2" *ngIf="isLoader">
                        <i aria-hidden="true" class="download-circle-btn-loader fa fa-circle-o-notch fa-1x fa-fw"></i>
                    </span>
                </nep-button>
        </div>
    </div>
</div>