﻿<div class="d-flex justify-content-end mb-3" [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"add"}'>
    <div class="mr-auto Heading1-B">Portfolio Companies</div>
    <button [routerLink]="['/add-portfolio-company']" class="btn btn-primary d-flex align-items-center justify-content-center" id="id-add-portfolio" class="kendo-custom-button Body-R apply-btn"
      kendoButton themeColor="primary">
        <div class="d-flex align-items-center justify-content-center">
            <img src="assets/dist/images/add-portfolio.svg" alt="Add Icon" class="mr-1">
            <span class="text-center"> {{addPortfolioText}}</span>
        </div>
    </button>
</div>

<div class="row mr-0 ml-0 filter-section mt-4 mb-4">
    <div class="sort-by col-6 col-sm-6 col-md-6 col-xl-6 col-lg-6 pt-4 pb-4 pr-36 pl-3">        
        <div class="row mr-0 ml-0">
            <div class="col-4 col-sm-4 col-md-4 col-xl-4 col-lg-4 pl-0 pr-0">
                <div class="d-flex align-items-center justify-content-between filter-by mr-3">
                    <div class="pt-2 pb-2 pl-2">
                        <img src="assets/dist/images/patch-check-fill.svg" alt="Check Icon">
                    </div>            
                    <div class="Heading2-M pr-2">Sign-Off Received</div>
                </div>    
            </div>
            <div class="col-4 col-sm-4 col-md-4 col-xl-4 col-lg-4 filter-by pl-0 pr-0">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="pt-2 pb-2 pl-2">
                        <img src="assets/dist/images/turn-right-fill.svg" alt="Right Icon">
                    </div>            
                    <div class="Heading2-M pr-3">Sent For Sign-Off</div>
                </div>    
            </div>
            <div class="col-4 col-sm-4 col-md-4 col-xl-4 col-lg-4 pl-0 pr-0">
                <div class="d-flex align-items-center justify-content-between filter-by ml-3">
                    <div class="pt-2 pb-2 pl-2">
                        <img src="assets/dist/images/exclamation-fill.svg" alt="Exclamation Icon">
                    </div>            
                    <div class="Heading2-M pr-3">Not Received</div>
                </div>
            </div>
        </div>                                           
    </div>
    <div class="col-6 col-sm-6 col-md-6 col-xl-6 col-lg-6 pt-2 pl-36 pr-0">
        <div class="row mr-0 ml-0">
            <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0 pb-2">
                <div class="Heading2-R">Filter By</div>
            </div>
            <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0">
                <div class="row mr-0 ml-0 pl-0 pr-0">
                    <div class="col-4 col-sm-4 col-md-4 col-xl-4 col-lg-4 pl-0 pr-0">
                        <kendo-multiselect class="k-multiselect-custom k-dropdown-width-200" 
                        id="filter" [rounded]="'medium'" [fillMode]="'solid'" [clearButton]="false"
                        textField="Associates" valueField="id" [filterable]="true" [autoClose]="false"
                        placeholder="Filter By Associate">                       
                        </kendo-multiselect>
                    </div>
                    <div class="col-4 col-sm-4 col-md-4 col-xl-4 col-lg-4 pl-0 pr-0">
                        <kendo-multiselect class="k-multiselect-custom k-dropdown-width-200" 
                        id="filter" [rounded]="'medium'" [fillMode]="'solid'" [clearButton]="false"
                        textField="Funds" valueField="id" [filterable]="true" [autoClose]="false"
                        placeholder="Filter By Fund">                       
                        </kendo-multiselect>
                    </div>
                    <div class="col-4 col-sm-4 col-md-4 col-xl-4 col-lg-4 pl-0 pr-0">
                        <kendo-multiselect class="k-multiselect-custom k-dropdown-width-200" 
                        id="filter" [rounded]="'medium'" [fillMode]="'solid'" [clearButton]="false"
                        textField="Dates" valueField="id" [filterable]="true" [autoClose]="false"
                        placeholder="Filter by Date">                       
                        </kendo-multiselect>
                    </div>   
                </div>              
            </div>
        </div>        
    </div>
</div>

<div class="row portfolio-company-list-section">
    <div class="col-lg-12">
        <div class="card-body portafolio-table">
            <div class="row performance-section mr-0 ml-0">
                <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 outer-section pl-0 pr-0">                                       
                    <div class="content-bg">
                        <div *ngIf="tabName=='Published'">                            
                            <pc-new-published (onDraftEmitter)="changesModelevent($event)"></pc-new-published>
                        </div>
                        <div *ngIf="isWorkflowEnable && tabName=='Active Drafts'" class="text-center">                           
                            <app-portfolio-company-draft-list></app-portfolio-company-draft-list>
                        </div>
                    </div>                
                </div>
            </div>
        </div>
    </div>
</div>