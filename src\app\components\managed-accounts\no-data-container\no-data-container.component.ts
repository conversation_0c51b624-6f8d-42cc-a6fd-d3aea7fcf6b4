import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-no-data-container',
  templateUrl: './no-data-container.component.html',
  styleUrls: ['./no-data-container.component.scss']
})
export class NoDataContainerComponent {
  constructor(private router: Router) {}

  onAddManagedAccount(): void {
    this.router.navigate(['/add-managed-account']);
  }
}
