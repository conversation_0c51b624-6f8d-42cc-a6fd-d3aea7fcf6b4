import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { FundTabComponent } from './fund-tab.component';
import { ActivatedRoute } from '@angular/router';
import { CommonSubFeaturePermissionService } from 'src/app/services/subPermission.service';
import { PageConfigurationService } from 'src/app/services/page-configuration.service';
import { ToastrService } from 'ngx-toastr';
import { of } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('FundTabComponent', () => {
  let component: FundTabComponent;
  let fixture: ComponentFixture<FundTabComponent>;
  let subPermissionService: CommonSubFeaturePermissionService;
  let mockPageConfigurationService: jasmine.SpyObj<PageConfigurationService>;

  beforeEach(async () => {
    const activatedRouteStub = () => ({ snapshot: { params: { id: 1 } } });
    const subPermissionServiceStub = () => ({
      getCommonSubFeatureAccessPermissions: (id: any, feature: any) => of([])
    });
    mockPageConfigurationService = jasmine.createSpyObj('PageConfigurationService', ['getPageConfigSettingById']);
    const toastrServiceStub = () => ({ error: (msg: string, title: string) => {} });

    await TestBed.configureTestingModule({
      declarations: [ FundTabComponent ],
      providers: [
        { provide: ActivatedRoute, useValue: { snapshot: { params: { id: 1 } } } },
        { provide: CommonSubFeaturePermissionService, useValue: { getCommonSubFeatureAccessPermissions: () => of([]) } },
        { provide: PageConfigurationService, useValue: jasmine.createSpyObj('PageConfigurationService', ['getPageConfigSettingById']) },
        { provide: ToastrService, useValue: { error: () => {} } }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FundTabComponent);
    component = fixture.componentInstance;
    subPermissionService = TestBed.inject(CommonSubFeaturePermissionService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Permission Tests', () => {
    it('should check permission access correctly', () => {
      const mockPermissions = [
        { subFeature: 'Documents', CAN_VIEW: true, CAN_EDIT: false },
        { subFeature: 'Other', CAN_VIEW: false, CAN_EDIT: false }
      ];
      expect(component.checkPermissionAccess(mockPermissions, 'CAN_VIEW')).toBeTruthy();
      expect(component.checkPermissionAccess(mockPermissions, 'CAN_EDIT')).toBeFalsy();
    });

    it('should update tab list based on document permissions', () => {
      spyOn(component, 'getPageConfigSetting');
      component.canViewDocuments = true;
      component.updateTabList();
      expect(component.getPageConfigSetting).toHaveBeenCalled();
      component.canViewDocuments = false;
      component.updateTabList();
      expect(component.tabList.length).toBe(1);
      expect(component.tabList[0].name).toBe('Fund Details');
    });

    it('should set document permissions correctly', fakeAsync(() => {
      const mockResult = [{
        subFeature: 'Documents',
        canView: true,
        canEdit: true
      }];
      spyOn(subPermissionService, 'getCommonSubFeatureAccessPermissions').and.returnValue(of(mockResult));
      spyOn(component, 'updateTabList');
      component.getDocumentsPermissions();
      tick(); // Wait for observable to emit
      expect(component.canViewDocuments).toBeTrue();
      expect(component.canEditDocuments).toBeTrue();
      expect(component.updateTabList).toHaveBeenCalled();
    }));

    it('should handle empty permission result', () => {
      spyOn(subPermissionService, 'getCommonSubFeatureAccessPermissions').and.returnValue(of([]));
      component.getDocumentsPermissions();
      expect(component.canViewDocuments).toBeFalse();
      expect(component.canEditDocuments).toBeFalse();
    });
  });

  it('should call getPageConfigSetting and update tabList when canViewDocuments is true', fakeAsync(() => {
    const mockResult = {
      subPageList: [
        { name: 'Documents', id: 1, displayName: 'Document Tab', isActive: true },
        { name: 'OtherTab', id: 2, displayName: 'Other Tab', isActive: false }
      ],
      fieldValueList: [
        { subPageID: 1, fieldName: 'Field1', fieldValue: 'Value1' },
        { subPageID: 1, fieldName: 'Field2', fieldValue: 'Value2' },
        { subPageID: 2, fieldName: 'Field3', fieldValue: 'Value3' }
      ]
    };
    // Use the correct mockPageConfigurationService instance
    component['pageConfigurationService'] = mockPageConfigurationService;
    mockPageConfigurationService.getPageConfigSettingById.and.returnValue(of(mockResult));
    component.canViewDocuments = true;
    component.getPageConfigSetting();
    tick();
    expect(component.tabList.length).toBe(2);
    expect(component.tabList[1].name).toBe('Document Tab');
    expect(component.documentsFieldPageConfig.length).toBe(2);
  }));

  it('should switch tabs correctly in switchTab', () => {
    const mockTab = { name: 'Documents', active: true, aliasname: 'Documents' };
    component.switchTab(mockTab as any);
    expect(component.tabName).toBe('Documents');
  });
});
