import { Component, OnInit } from "@angular/core";
import { EsgService } from 'src/app/services/esg.services';
import { EsgAuditModel, EsgAuditDocumentModel, EsgDocAuditModel, EsgAuditLogModel, EsgAuditInfoModel } from "src/app/services/EsgAuditModel";
import * as moment from "moment";
import { FileExtension, KpiInfo } from "src/app/common/enums";
import { NumberDecimalConst, EsgConstants } from "src/app/common/constants";
import { MiscellaneousService } from "src/app/services/miscellaneous.service";
import { AuditService } from "src/app/services/audit.service";
import { ToastrService } from "ngx-toastr";
import { isNumeric } from '@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric';
import { DocAuditModel } from "src/app/services/DataAuditModel";

@Component({
  selector: "app-view-esg-aduitlogs.component",
  templateUrl: "./view-esg-aduitlogs.component.html",
  styleUrls: ["./view-esg-aduitlogs.component.scss"],
})
export class ViewEsgAduitlogsComponent implements OnInit {
  
  NumberDecimalConst = NumberDecimalConst;
  routeData: any = {};
  isLoader: boolean = false;
  isOpenPopup: boolean = false;
  auditLogData: EsgAuditLogModel[] = [];
  esgAuditModel: EsgAuditModel;
  fileExtension = FileExtension;
  kpiInfo = KpiInfo;
  commentText: string = "";
  documentData: any = [];
  sideNavWidth: string = "";
  leftWidth: string = "";
  isDocument: boolean = false;
  isComments: boolean = true;
  isDocLoading: boolean = false;
  isDocSupportLoading: boolean = false;
  auditInfo: EsgAuditInfoModel;
  constructor(private esgService: EsgService,
    private miscService: MiscellaneousService,
    private auditService: AuditService,
    public toastrService: ToastrService) {}

  /**
   * Initializes the component and retrieves the necessary data for displaying the ESG audit logs.
   */
  ngOnInit() {
    this.routeData = JSON.parse(sessionStorage.getItem(EsgConstants.EsgAuditLocalStorage));
    this.esgAuditModel = {
      CompanyId: this.routeData?.PortfolioCompanyID,
      EsgModuleId: this.routeData?.SubPageId,
      EsgKpiRecordId: this.routeData?.EsgKpiRecordId,
      KpiId: this.routeData?.KpiId
    };
    this.GetAuditlog();
  }

  ngOnDestroy() {
    localStorage.removeItem(EsgConstants.EsgAuditLocalStorage);
  }
  /**
   * Retrieves the audit log data for the ESG KPI.
   */
  GetAuditlog() {
    this.esgService.getEsgKpiAuditLog(this.esgAuditModel).subscribe({
      next: (result) => {
        this.auditInfo = result.auditInfo;
        this.auditLogData = result?.auditLogs.map(
          (s: EsgAuditLogModel) => {
            return {
              newValue: s.newValue,
              oldValue: s.oldValue,
              createdOn: moment(new Date(s.createdOn)).format(
                "DD-MMM-YYYY hh:mm:ss A"
              ),
              uploadedBy: s.uploadedBy,
              auditType: s.auditType != null ? s.auditType : "File upload",
              portfolioCompanyId: s.portfolioCompanyId,
              commentId: s.commentId,
              auditId: s.auditId,
              esgModuleId: s.esgModuleId,
              esgKpiRecordId: s.esgKpiRecordId,
              documentId: s.documentId,
              fileName: s.fileName,
              supportingDocumentsId: s.supportingDocumentsId,
              extension: s.extension,
            };
          }
        );
        this.isLoader = false;
      },
      error: (error: any) => {
        this.toastrService.error(error,"",{positionClass: 'toast-center-center'});
        this.isLoader = false;
      },
      
    });
  }

  /**
   * Opens a document based on the provided document IDs.
   * @param documentIds - The IDs of the documents to be opened.
   */
  openDoc(documentIds: string) {
    this.documentData = [];
    this.getSideNavWidth();
    this.isOpenPopup = true;
    this.isDocument = true;
    this.isComments = false;
    if (documentIds != null) {
      this.getSupportingDocuments(documentIds);
    }
  }

   /**
   * Opens the comments pop-up window.
   * @param commentId The ID of the comment to be displayed in the pop-up window.
   */
   openCommentsPopUp(commentId: number) {
    this.commentText = "";
    this.getSideNavWidth();
    this.isOpenPopup = true;
    this.isDocument = false;
    this.isComments = true;
    if (commentId != null && commentId > 0) {
      this.getComment(commentId);
    }
  }

  /**
   * Retrieves the supporting documents for the given document IDs.
   * @param supportingDocIds - A comma-separated string of document IDs.
   */
  getSupportingDocuments(supportingDocIds: string) {
    this.isLoader = true;
    let documentIds = supportingDocIds.split(",").map(Number);
    this.auditService.getSupportingDocs(documentIds).subscribe({
      next: (data: EsgAuditDocumentModel[]) => {
        this.documentData = data;
        this.isLoader = false;
      },
      error: (error: any) => {
        this.toastrService.error(error,"",{positionClass: 'toast-center-center'});
        this.isLoader = false;
      },
    });
  }
  /**
   * Retrieves a comment by its ID.
   * @param commentId The ID of the comment to retrieve.
   */
  getComment(commentId: number) {
    this.isLoader = true;
    this.auditService.getComment(commentId).subscribe({
      next: (data: any) => {
        this.commentText = data.comments;
        this.isLoader = false;
      },
      error: (error: any) => {
        this.toastrService.error(error,"",{positionClass: 'toast-center-center'});
        this.isLoader = false;
      },
    });
  }

  /**
   * Retrieves the width of the side navigation bar and calculates the width of the main content area.
   * @returns The calculated width of the main content area.
   */
    getSideNavWidth() {
      this.sideNavWidth = "50vw";
    }

  /**
   * Checks if the given value is a number.
   * 
   * @param str - The value to check.
   * @returns `true` if the value is a number, `false` otherwise.
   */
  isNumberCheck(str: any) {
    return isNumeric(str);
  }

  /**
   * Formats a number by adding commas as thousand separators and rounding to 2 decimal places.
   * 
   * @param x - The number to format.
   * @returns The formatted number as a string.
   */
  numberWithCommas(x) {
    if (x === null || x === undefined) {
      return x;
    }
    const safeNumberRegex = /^[+-]?\d+(\.\d+)?$/;
    if (String(x).match(safeNumberRegex)) {
      x = parseFloat(x).toLocaleString("en-us", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    }
    return x;
  }

  /**
   * Downloads a file with the given fileId and fileName.
   * @param {string} fileId - The ID of the file to download.
   * @param {string} fileName - The name of the file to download.
   */
  downloadFile(fileId: string, fileName: string) {
    this.isDocSupportLoading = true;
    let doc: DocAuditModel = {
      DocumentId: fileId,
      DocumentName: fileName,
      Period: '',
      KpiId: 0,
      ValueType: '',
      SubPageId: 0,
      IsSourceFile: false,
      SectionId: 0
    };
    this.auditService.exportDocFile(doc).subscribe({
      next: (response: any) => {
        this.isDocSupportLoading = false;
        this.miscService.downloadAllFormatFile(response, fileName);
      },
      error: (error: any) => {
        this.toastrService.error(error,"",{positionClass: 'toast-center-center'});
        this.isDocSupportLoading = false;
      },
    });
  }

  /**
   * Downloads a zip file containing audit logs.
   */
  downloadZip() {
    this.isDocLoading = true;
    const currentDate = new Date();
    const formattedDate = currentDate
      .toLocaleTimeString()
      .replace(/:/g, "_")
      .replace(/\./g, "_");
    this.isDocLoading = true;
    this.auditService.exportZipFile(this.documentData).subscribe({
      next: (response: any) => {
        this.isDocLoading = false;
        this.miscService.downloadAllFormatFile(
          response,
          `AuditLogDocuments_${"ESG"}_${formattedDate}.zip`
        );
      },
      error: (error: any) => {
        this.isDocLoading = false;
      },
    });
  }
 
  /**
   * Downloads the source file with the given fileId and fileName.
   * @param fileId - The ID of the document to download.
   * @param fileName - The name of the file to be downloaded.
   */
  downloadSourceFile(fileId: string, fileName: string) {
    this.isDocSupportLoading = true;
    let doc: EsgDocAuditModel = {
      DocumentId: fileId,
      DocumentName: fileName,
      Period:this.routeData?.Period,
      KpiId: this.routeData?.KpiId,
      SubPageId:this.routeData?.SubPageId
     };
    this.auditService.exportSourceDocFile(doc).subscribe({
      next: (response: any) => {
        this.isDocSupportLoading = false;
        this.miscService.downloadAllFormatFile(response, fileName);
      },
      error: (error: any) => {
        this.toastrService.error(error,"",{positionClass: 'toast-center-center'});
        this.isDocSupportLoading = false;
      },
    });
  }
}
