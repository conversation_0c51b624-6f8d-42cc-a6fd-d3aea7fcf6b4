import { Component, OnInit } from '@angular/core';
import { DashboardConfigurationConstants } from 'src/app/common/constants';
import { DashboardTrackerService } from 'src/app/services/dashboard-tracker.service';
import { DashboardTrackerConfigDto, MaptoType } from '../../model/dashboard-tracker-config.model';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { ListItemModel } from "@progress/kendo-angular-buttons";
import { checkIcon, SVGIcon } from '@progress/kendo-svg-icons';
import { GroupResult, groupBy } from "@progress/kendo-data-query";

@Component({
  selector: 'app-manage-tracker-fields',
  templateUrl: './manage-tracker-fields.component.html',
  styleUrls: ['./manage-tracker-fields.component.scss']
})
export class ManageTrackerFieldsComponent {
  public fieldTypesOptions: { text: string, value: number }[] = DashboardConfigurationConstants.fieldTypesOptions;
  public dataTypesOptions: { text: string, value: number }[] = DashboardConfigurationConstants.dataTypesOptions;
  public trackingFrequencyOptions: { text: string, value: number }[] = DashboardConfigurationConstants.trackingFrequencyOptions;
  public prefixSuffixOptions: { text: string, value: number }[] = DashboardConfigurationConstants.prefixSuffixOptions;
  public dropdownOptions: { text: string, value: number }[] = DashboardConfigurationConstants.dropdownOptions;
  public maptoFields: { text: string, value: number, type : MaptoType }[] = DashboardConfigurationConstants.maptoFields;

  selectedFieldType: { text: string, value: number } | null = null;
  selectedDataType: { text: string, value: number } | null = null;
  selectedTrackingFrequency: { text: string, value: number } = DashboardConfigurationConstants.trackingFrequencyOptions[0];
  selectedPrefixSuffix: { text: string, value: number } = DashboardConfigurationConstants.prefixSuffixOptions[0];
  selectedDropdownOption: { text: string, value: number } = DashboardConfigurationConstants.dropdownOptions[0];
  selectedTSDateFormat: { text: string, value: string } = DashboardConfigurationConstants.monthLabels[1];
  selectedMaptoField: { text: string, value: number, type: MaptoType } | null = null;
  selectedStartPeriod: string = "";
  selectedEndPeriod: string = "";
  selectedStartPeriodValue: any = null;
  selectedEndPeriodValue: any = null;
  selectedMapTo: number | null = null;
  columnName: string | null = null;
  maxPeriod: number = 5;
  namePatternTooltip: string = 'The name pattern consists of a period and a column name. You can configure the position of the period by selecting a prefix or suffix.';
  public svgShare: SVGIcon = checkIcon;
  public shareData: ListItemModel[] = DashboardConfigurationConstants.IconList;
  public selectedIcon: ListItemModel[] = [];  
  groupedMaptoData : any[] = [];
  
  onShareSelect(event: ListItemModel[]): void {
    this.selectedIcon = event;        
  }  

  public fromPeriodData: any[] = [];
  public toPeriodData: any[] = [];

  dropdownValues: string[] = [];
  dropdownInput: string = '';

  // Property to trigger refresh of manage-tracker-records component
  isNewColumnAdded: boolean = false;

  ngOnInit(): void {
    this.fromPeriodData = this.generatePeriodData(this.maxPeriod);
    // Initialize with static fields first
    this.groupedMaptoData = groupBy(this.maptoFields, [{ field: 'type' }]);
    this.loadCustomFields();
  }

  /**
   * Load custom fields from API and add them to maptoFields array
   */
  private loadCustomFields(): void {
    this.dashboardTrackerService.getAvailableCustomFieldsForMapTo().subscribe({
      next: (response) => {
        if (response && Array.isArray(response)) {
          // Add custom fields to the existing maptoFields array
          const customFields = response.map(field => ({
            text: field.displayText,
            value: field.value,
            type: MaptoType.CustomFields
          }));

          // Combine static fields with custom fields
          this.maptoFields = [...DashboardConfigurationConstants.maptoFields, ...customFields];
          this.groupedMaptoData = groupBy(this.maptoFields, [{ field: 'type' }]);
        }
      },
      error: (error) => {
        console.error('Error loading custom fields:', error);
        // Keep only static fields if API call fails
        this.maptoFields = [...DashboardConfigurationConstants.maptoFields];
        this.groupedMaptoData = groupBy(this.maptoFields, [{ field: 'type' }]);
      }
    });
  }

  /**
   * Get the display text for MaptoType enum values
   */
  getMaptoTypeText(type: MaptoType): string {
    switch (type) {
      case MaptoType.StaticFields:
        return 'Static Fields';
      case MaptoType.CustomFields:
        return 'Custom Fields';
      default:
        return 'Unknown';
    }
  }

  constructor(
    private readonly dashboardTrackerService: DashboardTrackerService,
    private readonly toastrService: ToastrService,private readonly router: Router,
  ) {
  }

  addDropdownValue(event: Event) {
    event.preventDefault();
    const value = this.dropdownInput.trim();
    if (value && !this.dropdownValues.includes(value)) {
      this.dropdownValues.push(value);
      this.dropdownInput = '';
    }
  }

  removeDropdownValue(index: number) {
    this.dropdownValues.splice(index, 1);
  }

  public onChangeFrequency(item: any): void {
    this.fromPeriodData = this.generatePeriodData(this.maxPeriod, item.value);
    // Reset tracking period values
    this.selectedStartPeriod = "";
    this.selectedEndPeriod = "";
    this.selectedStartPeriodValue = null;
    this.selectedEndPeriodValue = null;
    this.toPeriodData = []; // Reset to period data

    // Reset name pattern dropdown to default based on new frequency
    this.selectedTSDateFormat = this.getDefaultDateFormat(item.value);
  }

  isCreateEnabled(): boolean {
    return this.selectedFieldType !== null;
  }

  monthYearOptions(): { text: string, value: string }[] {
    switch (this.selectedTrackingFrequency['value']) {
      case 1: return DashboardConfigurationConstants.monthLabels;
      case 2: return DashboardConfigurationConstants.quarterLabels;
      case 3: return DashboardConfigurationConstants.yearLabels;
    }
  }

  private getDefaultDateFormat(frequencyValue: number): { text: string, value: string } {
    switch (frequencyValue) {
      case 1: return DashboardConfigurationConstants.monthLabels[0]; // Default to first month option
      case 2: return DashboardConfigurationConstants.quarterLabels[0]; // Default to first quarter option
      case 3: return DashboardConfigurationConstants.yearLabels[0]; // Default to first year option
      default: return DashboardConfigurationConstants.monthLabels[0];
    }
  }

  private shouldSkipYear(year: number, skipToYear?: number): boolean {
    return skipToYear !== undefined && year < skipToYear;
  }

  private getStartPeriodIndex(year: number, skipToYear?: number, skipToPeriod?: number): number {
    if (skipToYear !== undefined && skipToPeriod !== undefined && year === skipToYear) {
      return skipToPeriod - 1;
    }
    return 0;
  }

  private buildPeriods(valueToMap: string[], startPeriodIndex: number, parentId: number, year: number): any[] {
    return valueToMap.slice(startPeriodIndex).map((el, idx) => ({
      text: el,
      id: startPeriodIndex + idx + 1,
      parentId: parentId,
      parentYear: year.toString(),
    }));
  }

  generatePeriodData(yearsCount: number = this.maxPeriod, frequency: number = 1, skipToYear?: number, skipToPeriod?: number): any[] {
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const quarters = ["Q1", "Q2", "Q3", "Q4"];
    const valueToMap = frequency === 1 ? months : quarters;
    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: yearsCount }, (_, i) => currentYear + i);

    return years
      .map((year, i) => {
        if (frequency === 3 && this.shouldSkipYear(year, skipToYear)) {
          return null;
        }
        let periods: any[] | undefined = undefined;
        if (frequency !== 3) {
          if (this.shouldSkipYear(year, skipToYear)) {
            return null;
          }
          const startPeriodIndex = this.getStartPeriodIndex(year, skipToYear, skipToPeriod);
          periods = this.buildPeriods(valueToMap, startPeriodIndex, i + 1, year);

          if (periods.length === 0) {
            return null;
          }
        }
        return {
          text: year.toString(),
          id: i + 1,
          ...(periods && { periods }),
        };
      })
      .filter(year => year !== null);
  }

  saveTrackerField(): void {
    if (this.selectedFieldType?.value === null || this.selectedDataType?.value === null) {
      return;
    }

    // Additional validation for Time Series fields
    if (this.selectedFieldType?.value === 2) { // Time Series
      if (!this.selectedTrackingFrequency?.value ||
        this.selectedStartPeriod === '' ||
        this.selectedEndPeriod === '' || !this.columnName || this.columnName === '') {
        this.toastrService.error('Please fill all required fields for Time Series', '', {
          positionClass: 'toast-center-center'
        });
        // You might want to show a toast or alert here
        return;
      }
    }
    const config: DashboardTrackerConfigDto = {
      FieldType: this.selectedFieldType?.value,
      DataType: this.selectedMaptoField?.value ? DashboardConfigurationConstants.dataTypesOptions[0].value : this.selectedDataType?.value,
      Name: this.columnName || 'Custom Column',
      FrequencyType: this.selectedTrackingFrequency?.value || null,
      StartPeriod: this.selectedFieldType?.value === 2 ? this.selectedStartPeriod : null,
      EndPeriod: this.selectedFieldType?.value === 2 ? this.selectedEndPeriod : null,
      IsPrefix: this.selectedFieldType?.value === 2 ? this.selectedPrefixSuffix?.value === 1 : null,
      TimeSeriesDateFormat: this.selectedFieldType?.value === 2 ? this.selectedTSDateFormat?.value : null,
      MapTo: this.selectedMaptoField?.value || null,
      MapToType: this.selectedMaptoField?.type || null,
    };

    this.dashboardTrackerService.saveDashboardTrackerConfig(config).subscribe({
      next: (response) => {

        let dropdownValueWithType: any[] = [];
        if (this.selectedFieldType?.value === this.fieldTypesOptions[1].value) {
          if (this.selectedIcon && this.selectedIcon.length > 0) {
            const iconTexts = this.selectedIcon.map(icon => icon.text);
            dropdownValueWithType.push({
              dropdownValues: iconTexts,
              dropdownType: this.dropdownOptions[1].value // Assuming 1 is the type for icon dropdown values
            });
          }
        }

        if (this.dropdownValues && this.dropdownValues.length > 0) {            
            dropdownValueWithType.push({
              dropdownValues: this.dropdownValues,
              dropdownType: this.dropdownOptions[0].value // Assuming 0 is the type for text dropdown values
            });
        }

        if (this.selectedDataType?.value == 4) {
          const dto = {
            trackerFieldId: response.body.id,
            dropdownValueWithType: dropdownValueWithType
          };

          this.dashboardTrackerService.saveTrackerDropdownValues(dto).subscribe({
            next: (response) => {
              this.toastrService.success('Configuration saved successfully', '', {
                positionClass: 'toast-center-center'
              });
              this.resetForm();
              this.triggerRecordsRefresh();
            },
            error: (error) => {
              this.toastrService.error('Error saving configuration', '', {
                positionClass: 'toast-center-center'
              });
            }
          });
          return;
        }
        else {
          this.toastrService.success('Configuration saved successfully', '', {
            positionClass: 'toast-center-center'
          });
          this.resetForm();
          this.triggerRecordsRefresh();
        }
      },
      error: (error) => {
        this.toastrService.error('Error saving configuration', '', {
          positionClass: 'toast-center-center'
        });
        // Handle error - show error message to user
      }
    });
  }
  
  valueChangeStart(event: any) : void {
    if(event) {
      this.selectedStartPeriodValue = event;
      if(this.selectedTrackingFrequency?.value === 1 && event.parentId) {
        // Monthly Time series
        let year = this.fromPeriodData.find(x=>x.id === event.parentId);
        let month = year.periods.find(x=>x.id === event.id);
        this.selectedStartPeriod = month.text+ " "+ year.text;

        // Set toPeriodData with periods >= selected from period
        this.toPeriodData = this.generatePeriodData(this.maxPeriod, this.selectedTrackingFrequency.value, parseInt(year.text), event.id);

        // Reset end period selection when start period changes
        this.selectedEndPeriodValue = null;
        this.selectedEndPeriod = "";
      } else if(this.selectedTrackingFrequency?.value === 2 && event.parentId) {
       // Quarterly Time series
        let year = this.fromPeriodData.find(x=>x.id === event.parentId);
        let month = year.periods.find(x=>x.id === event.id);
        this.selectedStartPeriod = month.text+ " "+ year.text;

        // Set toPeriodData with periods >= selected from period
        this.toPeriodData = this.generatePeriodData(this.maxPeriod, this.selectedTrackingFrequency.value, parseInt(year.text), event.id);

        // Reset end period selection when start period changes
        this.selectedEndPeriodValue = null;
        this.selectedEndPeriod = "";
      } else if(this.selectedTrackingFrequency?.value === 3) {
        // Annual Time series
        this.selectedStartPeriod = event.text;

        // Set toPeriodData with periods >= selected from period
        this.toPeriodData = this.generatePeriodData(this.maxPeriod, this.selectedTrackingFrequency.value, parseInt(event.text));

        // Reset end period selection when start period changes
        this.selectedEndPeriodValue = null;
        this.selectedEndPeriod = "";
      } else {
        // condition is for selecting parent node, is not valid
        this.selectedStartPeriodValue = null;
        this.selectedStartPeriod = "";
        this.selectedEndPeriodValue = null;
        this.selectedEndPeriod = "";
        this.toPeriodData = [];
      }
    } else {
      this.selectedStartPeriodValue = null;
      this.selectedStartPeriod = "";
      this.selectedEndPeriodValue = null;
      this.selectedEndPeriod = "";
      this.toPeriodData = [];
    }
  }
   valueChangeEnd(event: any) : void {
    if(event) {
      this.selectedEndPeriodValue = event;
      if(this.selectedTrackingFrequency?.value === 1 && event.parentId) {
        // Monthly Time series
        let year = this.toPeriodData.find(x=>x.id === event.parentId);
        let month = year.periods.find(x=>x.id === event.id);
        this.selectedEndPeriod = month.text + " "+ year.text;
      } else if(this.selectedTrackingFrequency?.value === 2 && event.parentId) {
       // Quarterly Time series
        let year = this.toPeriodData.find(x=>x.id === event.parentId);
        let month = year.periods.find(x=>x.id === event.id);
        this.selectedEndPeriod = month.text + " "+ year.text;
      } else if(this.selectedTrackingFrequency?.value === 3) {
        // Annual Time series
         this.selectedEndPeriod = event.text;
      } else {
         // condition is for selecting parent node, is not valid
        this.selectedEndPeriodValue = null;
        this.selectedEndPeriod = "";
      }
    } else {
      this.selectedEndPeriodValue = null;
      this.selectedEndPeriod = "";
    }
  }

  enableDataTypeDropdown(): boolean {
    return this.selectedMaptoField == null || this.selectedMaptoField === undefined;
  }

  resetForm(): void {
    this.selectedFieldType = null;
    this.selectedDataType = null;
    this.selectedTrackingFrequency = DashboardConfigurationConstants.trackingFrequencyOptions[0];
    this.columnName = null;
    this.selectedStartPeriod = "";
    this.selectedEndPeriod = "";
    this.selectedStartPeriodValue = null;
    this.selectedEndPeriodValue = null;
    this.selectedMapTo = null;
    this.selectedTSDateFormat = DashboardConfigurationConstants.monthLabels[0];
    this.selectedPrefixSuffix = DashboardConfigurationConstants.prefixSuffixOptions[0];
    this.dropdownValues = [];
    this.dropdownInput = '';
    this.toPeriodData = [];
    this.selectedMaptoField = null;

  }

  cancelClick() :void {
    this.router.navigate(["/home"], { queryParams: { tab: 'dashboard-tracker' } });
  }

  /**
   * Triggers refresh of the manage-tracker-records component
   */
  private triggerRecordsRefresh(): void {
    // Toggle the flag to trigger ngOnChanges in manage-tracker-records component
    this.isNewColumnAdded = !this.isNewColumnAdded;
  }
}
