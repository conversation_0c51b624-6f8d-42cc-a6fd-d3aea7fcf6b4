import { AfterViewInit, Component, EventEmitter, HostListener, OnInit, ViewChild } from '@angular/core';
import { MatMenu, MatMenuTrigger } from '@angular/material/menu';
import { FilterExpandSettings } from "@progress/kendo-angular-treeview";
import { MultiSelectTreeCheckableSettings } from "@progress/kendo-angular-dropdowns";
import { DropDownFilterSettings } from '@progress/kendo-angular-dropdowns';
import { GroupResult, groupBy } from "@progress/kendo-data-query";
import {
  SVGIcon,
  chevronDownIcon
} from "@progress/kendo-svg-icons";
import { Observable, Subject, Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { ReportDownloadService } from 'src/app/services/report-download.service';
import { ToastContainerDirective, ToastrService } from 'ngx-toastr';
import { GrowthReportConfigService } from 'src/app/services/growth-report-config.service';
import { GrowthReportSectionModel } from './models/growth-report-model';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ComponentCanDeactivate } from 'src/app/unsaved-changes/can-deactivate/component-can-deactivate';
@Component({
  selector: "app-growth-report",
  templateUrl: "./growth-report.component.html",
  styleUrls: ["./growth-report.component.scss"],
})
export class GrowthReportComponent  extends ComponentCanDeactivate implements AfterViewInit, OnInit {
  initialFormValue: any;
  initialCompanyList: any[];
  isSaveEnabled: boolean = false;
  isFundChange: boolean = false;
  isRowHeaderAdded: boolean = false;
  isManual: boolean = false;
  isSet: boolean = false;
  isCancel: boolean = false;
  isDirty = false;
  growthId: number = 0;
  growthConfigList: any = null;
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: "contains",
  };
  kpiLineItemList: any;
  form: FormGroup;
  selectedCompanySection: FormGroup;
  selectedCompany: any = null;
  kpiModules: GrowthReportSectionModel[] = [];
  isEditHeader: boolean = false;
  editHeaderModel: any = null;
  rowHeaderList: any[] = [];
  isExistHeader: boolean = false;
  rowHeader: string = null;
  openHeaderPopUp: boolean = false;
  deleteHeaderPopUp: boolean = false;
  tabName: string = "Row Headers";
  tabList: any[] = [];
  searchText: string = "";
  originalPanelList: any[] = [];
  panelList: any[] = [];
  isNoData: boolean = true;
  companyList: any[] = [];
  selectedFundList: any[] = [];
  selectedCompanyList: any[] = [];
  groupedCompanyList: any[] = [];
  isCheckedCopyFundAll: boolean = false;
  isCheckedCompanyAll: boolean = false;
  isLoader: boolean = false;
  fundList: any[] = [];
  public virtual: any = {
    itemHeight: 32,
    pageSize: 100,
  };
  public filterExpandSettings: FilterExpandSettings = {
    expandMatches: true,
    maxAutoExpandResults: 200,
  };
  public checkableSettings: MultiSelectTreeCheckableSettings = {
    checkChildren: true,
    checkOnClick: false,
  };
  @ViewChild("menu") uiUxMenu!: MatMenu;
  @ViewChild("tRecordTrigger") menuTrigger: MatMenuTrigger;
  public svgCart: SVGIcon = chevronDownIcon;
  @ViewChild(ToastContainerDirective, {})
  toastContainer: ToastContainerDirective;
  constructor(
    private fb: FormBuilder,
    private reportDownloadService: ReportDownloadService,
    private toasterService: ToastrService,
    private growthReportService: GrowthReportConfigService
  ) {
    super();
  }

  canDeactivate() : boolean {
    return !this.isDirty && !this.isFundChange && !this.isRowHeaderAdded;
  }
  /**
   * Initializes the form with default values.
   * It creates a form group with an empty companySections form array
   * and sets the selectedCompanySection to the companySections form array.
   */
  initializeForm() {
    this.form = this.fb.group({
      companySections: this.fb.array([]),
    });
    this.selectedCompanySection = this.form?.get(
      "companySections"
    ) as FormGroup;
 }
  /**
   * Retrieves the companySections form array from the form.
   * This method provides a convenient way to access the companySections form array.
   *
   * @returns The companySections form array.
   */
  get companySections(): FormArray {
    return this.form?.get("companySections") as FormArray;
  }
  /**
   * Adds a new company section to the form.
   * It sets the selected KPI type and company section based on the companyId.
   * If the selected company section is undefined, it creates a new company section form group,
   * initializes it with company information, row headers, and column headers, and adds it to the companySections form array.
   * It then sets the selected company section to the newly added section.
   *
   * @param company - The company object containing companyId and fundId.
   */
  addCompanySection(company: any): void {
    this.setSelectedKpiType(company.companyId);
    this.setSelectedCompanySection(company.companyId);
    if (this.selectedCompanySection == undefined) {
      const companySection = this.fb.group({
        companyInfo: this.fb.group({
          companyId: [company.companyId, Validators.required],
          fundId: [company.fundId, Validators.required],
        }),
        rowHeaders: this.fb.array([]),
        columnHeaders: this.fb.group({
          kpiSections: this.fb.array([]),
        }),
      });
      this.companySections.push(companySection);
      this.rowHeaderList.forEach((header) => {
        this.addRowHeader(this.companySections.length - 1, header);
      });
      this.setSelectedCompanySection(company.companyId);
    }
  }
  /**
   * Adds a new KPI section to the column headers of the selected company section.
   * It retrieves the columnHeaders form group from the selected company section,
   * finds the kpiSections form array, and adds a new KPI section to the kpiSections form array.
   */
  addColumnKpi() {
    const columnHeaders = this.selectedCompanySection.get(
      "columnHeaders"
    ) as FormArray;
    const kpiSections = columnHeaders.get("kpiSections") as FormArray;
    kpiSections.push(this.createKpiSection());
    this.isDirty = true;
  }
  /**
   * Sets the selected company section based on the specified companyId.
   * It retrieves the companySections form array from the form, finds the section
   * with the matching companyId, and sets it as the selectedCompanySection.
   *
   * @param companyId - The ID of the company for which to set the selected company section.
   */
  setSelectedCompanySection(companyId: string): void {
    const companySections = this.form.get("companySections") as FormArray;
    this.selectedCompanySection = companySections.controls.find(
      (section: FormGroup) =>
        section.get("companyInfo.companyId").value === companyId
    ) as FormGroup;
  }
  /**
   * Sets the selected KPI type for the specified company.
   * It retrieves the company section based on the companyId, iterates through the row headers and KPI sections,
   * collects unique KPI type IDs, and fetches the line item list for these KPI type IDs.
   *
   * @param companyId - The ID of the company for which to set the selected KPI type.
   */
  getSelectedCompanyIndex() {
    return this.companySections?.controls?.findIndex(
      (section: FormGroup) =>
        section.get("companyInfo.companyId").value ===
        this.selectedCompany.companyId
    );
  }
  /**
   * Sets the selected KPI type for the specified company.
   * It retrieves the company section based on the companyId, iterates through the row headers and KPI sections,
   * collects unique KPI type IDs, and fetches the line item list for these KPI type IDs.
   *
   * @param companyId - The ID of the company for which to set the selected KPI type.
   */
  setSelectedKpiType(companyId: string): void {
    const companySections = this.form.get("companySections") as FormArray;
    let section = companySections.controls.find(
      (section: FormGroup) =>
        section.get("companyInfo.companyId").value === companyId
    ) as FormGroup;
    if (section != undefined) {
      let headers = section.get("rowHeaders") as FormArray;
      let kpiTypeIds = [];
      headers.controls.forEach((rowHeader: FormGroup) => {
        let kpiSections = rowHeader.get("kpiSections") as FormArray;
        kpiSections.controls.forEach((kpiSection: FormGroup) => {
          let kpiTypeId = kpiSection.get("kpiTypeId").value;
          if (
            kpiTypeId != 0 &&
            kpiTypeId != "" &&
            !kpiTypeIds.includes(kpiTypeId)
          ) {
            kpiTypeIds.push(kpiTypeId);
          }
        });
      });
      this.GetLineLineItemList(kpiTypeIds);
    }
  }
  /**
   * Fetches the line item list for the specified KPI type IDs.
   * It removes duplicate KPI type IDs, iterates through the unique KPI type IDs,
   * and calls the getKpiList method for each KPI type ID that is not already in the kpiLineItemList.
   *
   * @param kpiTypeIds - An array of KPI type IDs for which to fetch the line item list.
   */
  private GetLineLineItemList(kpiTypeIds: any[]) {
    kpiTypeIds = Array.from(new Set(kpiTypeIds));
    kpiTypeIds?.forEach((kpiTypeId) => {
      if (this.kpiLineItemList[kpiTypeId] == undefined) {
        this.getKpiList(kpiTypeId);
      }
    });
  }
  /**
   * Sets the selected column KPI type for the specified company.
   * It retrieves the company section based on the companyId, iterates through the KPI sections,
   * collects unique KPI type IDs, and fetches the line item list for these KPI type IDs.
   *
   * @param companyId - The ID of the company for which to set the selected column KPI type.
   */
  setSelectedColumKpiType(companyId: string): void {
    const companySections = this.form.get("companySections") as FormArray;
    let section = companySections.controls.find(
      (section: FormGroup) =>
        section.get("companyInfo.companyId").value === companyId
    ) as FormGroup;
    if (section != undefined) {
      let columnHeader = section.get("columnHeaders") as FormGroup;
      let kpiTypeIds = [];
      let kpiSections = columnHeader?.get("kpiSections") as FormArray;
      kpiSections?.controls?.forEach((kpiSection: FormGroup) => {
        let kpiTypeId = kpiSection.get("kpiTypeId").value;
        if (
          kpiTypeId != 0 &&
          kpiTypeId != "" &&
          !kpiTypeIds.includes(kpiTypeId)
        ) {
          kpiTypeIds.push(kpiTypeId);
        }
      });
      this.GetLineLineItemList(kpiTypeIds);
    }
  }
  /**
   * Adds a new row header to the specified company section.
   * It creates a form group for the row header with controls for headerName, headerId, and kpiSections,
   * and adds the form group to the rowHeaders form array of the specified company section.
   * If a companyId is provided, it retrieves the KPI sections for the specified header and company.
   *
   * @param companyIndex - The index of the company section to which the row header will be added.
   * @param headerGroup - The header information containing headerName and headerId.
   * @param companyId - The ID of the company for which to retrieve the KPI sections (default is 0).
   */
  addRowHeader(
    companyIndex: number,
    headerGroup: any,
    companyId: number = 0
  ): void {
    const rowHeaders = this.companySections?.at(companyIndex)?.get("rowHeaders") as FormArray;
    const rowHeader = this.fb.group({
      headerName: [headerGroup.header || ""],
      headerId: [headerGroup.headerId || 0],
      kpiSections: this.fb.array(
        companyId > 0
          ? this.getKpiSectionsByHeaderId(headerGroup.headerId, companyId)
          : []
      ),
    });
    rowHeaders?.push(rowHeader);
    //this.getKpiSectionsByHeaderId(headerGroup.headerId, companyId);
  }
  /**
   * Retrieves the KPI sections for a specified header and company, and returns them as an array of form groups.
   * It filters the rowHeaders in the growthConfigList based on the headerId and companyId,
   * and creates a form group for each KPI section with controls for kpiTypeId, lineItemId, and isCheckAll.
   *
   * @param headerId - The ID of the header for which to retrieve the KPI sections.
   * @param companyId - The ID of the company for which to retrieve the KPI sections (default is 0).
   * @returns An array of FormGroup representing the KPI sections.
   */
  getKpiSectionsByHeaderId(headerId: number, companyId: number = 0) {
    let formArray = [];
    let kpiSections = this.growthConfigList?.rowHeaders?.filter(
      (header: any) =>
        header.headerId == headerId && header.companyId == companyId
    );
    kpiSections?.forEach((kpiSection: any) => {
      formArray.push(
        this.fb.group({
          kpiTypeId: [kpiSection.moduleId, Validators.required],
          lineItemId: [
            kpiSection.kpiIds
              .split(",")
              .map((id) => parseInt(id.trim(), 10))
              .filter((id) => !isNaN(id)),
            Validators.required,
          ],
          isCheckAll: new FormControl(
            this.setCheckAll(kpiSection.moduleId, kpiSection)
          ),
        })
      );
    });
    return formArray == null || undefined ? [] : formArray;
  }
  /**
   * Retrieves the KPI sections for a specified company and returns them as an array of form groups.
   * It filters the columnHeaders in the growthConfigList based on the companyId,
   * and creates a form group for each KPI section with controls for kpiTypeId, lineItemId, and isCheckAll.
   *
   * @param companyId - The ID of the company for which to retrieve the KPI sections (default is 0).
   * @returns An array of FormGroup representing the KPI sections.
   */
  getKpiSections(companyId: number = 0) {
    let formArray = [];
    let kpiSections = this.growthConfigList?.columnHeaders?.filter(
      (column: any) => column.companyId == companyId
    );
    kpiSections?.forEach((kpiSection: any) => {
      formArray.push(
        this.fb.group({
          kpiTypeId: [kpiSection.moduleId, Validators.required],
          lineItemId: [
            kpiSection.kpiIds
              .split(",")
              .map((id) => parseInt(id.trim(), 10))
              .filter((id) => !isNaN(id)),
            Validators.required,
          ],
          isCheckAll: new FormControl(
            this.setCheckAll(kpiSection.moduleId, kpiSection)
          ),
        })
      );
    });
    return formArray == null || undefined ? [] : formArray;
  }
  setCheckAll(moduleId: number, kpiSection: any) {
    let selectedItems = kpiSection?.kpiIds
      ?.split(",")
      .map((id) => parseInt(id.trim(), 10))
      .filter((id) => !isNaN(id));
    let kpiList =
      this.kpiLineItemList != undefined ? this.kpiLineItemList[moduleId] : [];
    if (
      kpiList != undefined &&
      kpiList?.length > 0 &&
      selectedItems?.length > 0
    ) {
      if (kpiList.length == selectedItems.length) {
        return true;
      }
    }
    return false;
  }
  /**
   * Creates a new KPI section form group.
   * The form group contains controls for kpiTypeId, lineItemId, and isCheckAll,
   * with default values and required validators.
   *
   * @returns A FormGroup representing the KPI section.
   */
  createKpiSection(): FormGroup {
    return this.fb.group({
      kpiTypeId: [0, Validators.required],
      lineItemId: [[], Validators.required],
      isCheckAll: new FormControl(false),
    });
  }
  /**
   * Removes a KPI section from the column headers.
   * It retrieves the columnHeaders form group, finds the kpiSections form array,
   * and removes the KPI section at the specified index from the kpiSections form array.
   *
   * @param index - The index of the KPI section to be removed.
   */
  addColumnHeader(companyIndex: number): void {
    const columnHeaders = this.companySections
      .at(companyIndex)
      .get("columnHeaders") as FormArray;
    const columnHeader = this.fb.group({
      kpiSections: this.fb.array([]),
    });
    columnHeaders.push(columnHeader);
  }
  /**
   * Removes a KPI section from the column headers.
   * It retrieves the columnHeaders form group, finds the kpiSections form array,
   * and removes the KPI section at the specified index from the kpiSections form array.
   *
   * @param index - The index of the KPI section to be removed.
   */
  removeKpiSection(index: number) {
    const columnHeader = this.selectedCompanySection.get(
      "columnHeaders"
    ) as FormGroup;
    const sections = columnHeader.get("kpiSections") as FormArray;
    sections.clearValidators();
    sections.removeAt(index);
    this.form.updateValueAndValidity();
  }
  /**
   * Removes a KPI section from the specified row header.
   * It retrieves the rowHeaders form array, finds the specified row header by index,
   * and removes the KPI section at the specified index from the kpiSections form array.
   *
   * @param headerIndex - The index of the row header from which the KPI section will be removed.
   * @param index - The index of the KPI section to be removed.
   */
  removeRowKpiSection(headerIndex: number, index: number) {
    const rowHeaders = this.selectedCompanySection.get(
      "rowHeaders"
    ) as FormArray;
    const rowHeader = rowHeaders.at(headerIndex) as FormGroup;
    const sections = rowHeader.get("kpiSections") as FormArray;
    sections.clearValidators();
    sections.removeAt(index);
    this.form.updateValueAndValidity();
  }
  /**
   * Lifecycle hook that is called after data-bound properties of a directive are initialized.
   * This is a good place to put initialization logic.
   *
   * In this implementation, it calls the `getFundList` method to retrieve and initialize the list of funds.
   */
  ngOnInit(): void {
    this.toasterService.overlayContainer = this.toastContainer;
    this.getTemplateConfig();
  }
  initializeValues() {
    this.getFundList(true);
    this.getKpiModules();
    this.getRowHeaderList();
    this.initializeForm();
    this.form.valueChanges.subscribe(() => {
      this.checkEnableSaveButton();
    });
  }
  checkEnableSaveButton(): void {
    // Compare current form value with the initial form value
    const currentFormValue = this.form.getRawValue();
    this.isSaveEnabled =
      JSON.stringify(currentFormValue) !==
      JSON.stringify(this.initialFormValue);
  }
  /**
   * Handles the event when a fund is selected.
   *
   * @param $event - The event object associated with the selection. Defaults to `null` if not provided.
   *
   * This method performs the following actions:
   * 1. Calls `getCompanyList` to retrieve the list of companies.
   * 2. Updates the selection state by calling `updateSelectionState` with the current selected fund list,
   *    the total number of funds, and a string identifier "isCheckedCopyFundAll".
   */
  getFundSelected($event: any = null) {
    this.getCompanyList();
    this.updateSelectionState(
      this.selectedFundList,
      this.fundList.length,
      "isCheckedCopyFundAll"
    );
  }
  /**
   * Sets the selected company list by iterating over the grouped company list.
   * Each company and its items are assigned a unique ID and added to the selected company list.
   *
   * @returns {Array} The list of selected companies and their items with unique IDs.
   */
  setSelectedCompany() {
    let companyList = this.groupedCompanyList;
    let id = 0;
    let selectedCompanyList = [];
    companyList.forEach((company) => {
      selectedCompanyList.push(company);
      company.items.forEach((item) => {
        item.id = id++;
        selectedCompanyList.push(item);
      });
    });
    return selectedCompanyList;
  }

  /**
   * Updates the selection state of a list by setting a property to indicate whether all items are selected.
   *
   * @param selectedList - The list of currently selected items.
   * @param totalListLength - The total number of items in the list.
   * @param checkAllProperty - The property name to update, indicating if all items are selected.
   */
  updateSelectionState(
    selectedList: any[],
    totalListLength: number,
    checkAllProperty: string
  ) {
    if (selectedList?.length > 0) {
      this[checkAllProperty] = selectedList.length === totalListLength;
    } else {
      this[checkAllProperty] = false;
    }
  }
  setDefaultPanel()
  {
    this.expandPanel(this.panelList[0]);
    this.setCompanyState(this.panelList[0].items[0]);
  }
  /**
   * Clears the selected and grouped company lists.
   *
   * This method resets the `selectedCompanyList` and `groupedCompanyList` arrays to empty arrays.
   * It is typically used to clear any selections or groupings made by the user.
   */
  onClear(): void {
    this.selectedCompanyList = [];
    this.groupedCompanyList = [];
  }
  /**
   * Toggles the expansion state of a given panel.
   *
   * @param panel - The panel object whose expansion state is to be toggled.
   */
  expandPanel(panel: any) {
    panel.isExpanded = !panel.isExpanded;
  }
  /**
   * Sets the active state of a company and deactivates all items.
   *
   * @param company - The company object whose active state is to be toggled.
   * @param items - An array of items to be deactivated.
   */
  setCompanyState(company: any) {
    this.kpiLineItemList = {};
    this.panelList.forEach((panel) => {
      panel.items?.forEach((item) => {
        item.isActive = false;
      });
    });
    company.isActive = !company.isActive;
    this.selectedCompany = company;
    this.tabList?.forEach((tab) => (tab.active = false));
    this.tabList[0].active = true;
    this.onTabClick("Row Headers");
  }

  onDiscard(){
    this.isSet = false;
    this.isCancel = false;
  }
  /**
   * Maps the provided tags array. If the array contains fewer than 2 elements,
   * it returns the array as is. Otherwise, it returns an array containing the
   * original array as its single element.
   *
   * @param tags - An array of tags to be mapped.
   * @returns An array of tags or an array containing the original array.
   */
  public tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags];
  }
  /**
   * Clears the search filter in the growth report component.
   *
   * @param $event - The event object containing the filter methods.
   */
  clearSearch($event: any) {
    $event.clearFilter();
    $event.filterChange.emit("");
  }
  /**
   * Handles the submission of the growth report form.
   *
   * This method performs the following actions:
   * - Clears the current panel list.
   * - Closes the menu trigger.
   * - Sets the loader state to true.
   * - Resets the no data flag.
   * - Creates a deep copy of the selected company list.
   * - Extracts unique combinations of fund names and fund IDs.
   * - Groups companies by their fund names and fund IDs.
   * - Initializes the `isExpanded` property for each fund.
   * - Initializes the `isActive` property for each item within a fund.
   * - Updates the panel list with the grouped fund data.
   * - Sets the loader state to false.
   */
  onSubmit(isManual: boolean = false,isFirstLoad: boolean = false) {
    this.isManual = isManual;
    if(this.isDirty){
      this.menuTrigger.closeMenu();
      this.isSet = true;
    }
    else{
      this.onSubmitAction(false,isFirstLoad);
    }
  }

  onSubmitAction(isManual: boolean = false,isFirstLoad: boolean = false){
    isManual = isManual? isManual : this.isManual;
    this.searchText = "";
    this.panelList = [];
    this.menuTrigger?.closeMenu();
    this.isLoader = true;
    this.isNoData = false;
    let companiesList = JSON.parse(JSON.stringify(this.selectedCompanyList));

    const uniqueKeys = [
      ...new Set(
        companiesList
          ?.filter((x) => x.items == undefined)
          ?.map((company) => `${company.fundName}^${company.fundId}`)
      ),
    ];

    let fundList = uniqueKeys.map((key) => {
      const [fundName, fundId] = (key as string).split("^");
      return {
        fundName,
        fundId,
        items: companiesList.filter(
          (company) =>
            company.fundId === parseInt(fundId) &&
            company.items == undefined &&
            company.companyId != undefined
        ),
      };
    });
    fundList?.forEach((company) => {
      company["isExpanded"] = false;
      if (company?.items != undefined && company?.items?.length > 0) {
        company.items.sort((a, b) => a.text.localeCompare(b.text)).forEach((item) => {
          item["isActive"] = false;
        });
      }
    });
    this.panelList = fundList;
    this.panelList.sort((a, b) => a.fundName.localeCompare(b.fundName));
    if(isFirstLoad){
    this.originalPanelList = JSON.parse(JSON.stringify(fundList));
    }
    this.isLoader = false;
    this.getTabList();
    if(isManual && this.growthId > 0) {
      this.toasterService.success("Changes have been successfully made to the template", "", {
        positionClass: "toast-center-center",
      });
    }
    if(this.isSet){
      this.setDefaultForChange(this.selectedCompany?.companyId);
    }  
    this.onDiscard(); 
    this.panelList[0].isExpanded = true;
    this.setCompanyState(this.panelList[0].items[0]);
    this.onTabClick("Row Headers");
    this.isDirty = false;
    this.checkIsDirty(isFirstLoad); 
  }

  /**
   * 
   * @param isFirstLoad 
   * This method performs following actions:
   * -checks if the funds seected are changed.
   * -check if companies in funds are changed.
   * -Only does above checks if its not First Load.
   * If above all conditions are tru then we mark isDirty as true.
   */
  checkIsDirty(isFirstLoad: boolean = false){
    if(!isFirstLoad){
      if(this.originalPanelList?.length != this.panelList?.length){
        this.isDirty = true;
        this.isFundChange = true;
      }
      else{
        const map2 = new Map(this.panelList.map(fund => [fund.fundId, fund.items.length]));
        var isChange = this.originalPanelList.some(fund => map2.get(fund.fundId) !== fund.items.length) ||
            this.panelList.some(fund => !map2.has(fund.fundId));          
            this.isDirty = isChange;
            this.isFundChange = isChange;
      }
      this.isSaveEnabled = this.isDirty;      
    }

  }
  /**
   * Clears various fields and resets the state of the component.
   *
   * This method performs the following actions:
   * - Empties the selected fund list.
   * - Empties the selected company list.
   * - Unchecks the "Copy Fund All" checkbox.
   * - Unchecks the "Company All" checkbox.
   * - Clears the panel list.
   * - Clears the grouped company list.
   * - Closes the menu trigger.
   * - Sets the no data flag to true.
   */
  clearFields() {
    if(this.isDirty){
      this.menuTrigger.closeMenu();
      this.isCancel = true;
    }
    else{
      this.clearFieldsOnCancel();
    }
    
  }

  clearFieldsOnCancel(){
    this.selectedFundList = [];
    this.selectedCompanyList = [];
    this.isCheckedCopyFundAll = false;
    this.isCheckedCompanyAll = false;
    this.panelList = [];
    this.groupedCompanyList = [];
    this.menuTrigger.closeMenu();
    this.isNoData = true;
    this.isDirty = true;
    if(this.isCancel){
      this.setDefaultForChange(this.selectedCompany?.companyId);      
      this.onDiscard();
      this.isDirty = false;
    } 
    this.isSaveEnabled = true;
  }

  onConfirmClick(event : any){
    if(this.isSet){
      this.onSubmitAction(this.isManual)
      this.toasterService.info("Headers have been reset for this company", "", {
        positionClass: "toast-center-center",
      });
    }
    if(this.isCancel){
      this.clearFieldsOnCancel();
    }
  }
  /**
   * Fetches the list of funds from the report download service.
   *
   * This method sets the `isLoader` flag to `true` before making the request.
   * On receiving a response, it sets the `isLoader` flag to `false`.
   * If the response contains a non-null and non-empty list, it assigns the list to `fundList`.
   * In case of an error, it sets the `isLoader` flag to `false`.
   *
   * @returns {void}
   */
  getFundList(isFirstLoad: boolean = false) {
    this.isLoader = true;
    this.reportDownloadService.getFundList().subscribe({
      next: (result) => {
        this.isLoader = false;
        if (result != null && result.length > 0) {
          this.fundList = result;
          this.setFundSelected(isFirstLoad);
        }
      },
      error: (error) => {
        this.isLoader = false;
      },
    });
  }
  /**
   * Determines if the selected fund list is in an indeterminate state.
   *
   * This getter checks if the `selectedFundList` is in an indeterminate state
   * relative to the `fundList` by using the `isIndeterminate` method.
   *
   * @returns {boolean} True if the selected fund list is indeterminate, otherwise false.
   */
  public get isFundIndet() {
    return this.isIndeterminate(this.selectedFundList, this.fundList);
  }
  /**
   * Handles the click event for the fund selection.
   * Toggles the selection state of all funds and updates the selected fund list accordingly.
   * If all funds are selected, the selected fund list is set to the full fund list.
   * Otherwise, the selected fund list is cleared.
   * Also triggers the retrieval of the company list.
   */
  public onFundClick() {
    this.selectedFundList = [];
    this.isCheckedCopyFundAll = !this.isCheckedCopyFundAll;
    this.selectedFundList = this.isCheckedCopyFundAll ? this.fundList : [];
    this.getCompanyList();
  }
  /**
   * Determines if the selection state is indeterminate.
   *
   * An indeterminate state is when the number of selected items is
   * neither zero nor equal to the total number of items.
   *
   * @param selectedList - The list of currently selected items.
   * @param totalList - The total list of items.
   * @returns `true` if the selection state is indeterminate, otherwise `false`.
   */
  public isIndeterminate(selectedList: any[], totalList: any[]): boolean {
    return (
      selectedList?.length !== 0 && selectedList?.length !== totalList?.length
    );
  }
  /**
   * Groups the company list by the 'fundName' field and assigns the result to the 'groupedCompanyList' property.
   *
   * @remarks
   * This method uses the `groupBy` function to group the items in `companyList` based on the specified field.
   *
   * @returns {void}
   */
  groupByCompany() {
    this.groupedCompanyList = groupBy(this.companyList, [
      { field: "fundName" },
    ]) as GroupResult[];
  }
  /**
   * Fetches the list of companies associated with the selected funds and groups them by fund ID.
   *
   * This method performs the following steps:
   * 1. Constructs a filter object containing the selected fund IDs.
   * 2. Calls the `GetCompanies` method of `reportDownloadService` with the filter.
   * 3. Subscribes to the result of the `GetCompanies` call.
   * 4. On success:
   *    - Sets `isLoader` to false.
   *    - Checks if the result is not null and has items.
   *    - Groups the companies by `fundID`.
   *    - Transforms the grouped data into a hierarchical structure with unique IDs.
   *    - Assigns the transformed data to `groupedCompanyList`.
   * 5. On error:
   *    - Sets `isLoader` to false.
   *
   * @returns {void}
   */
  getCompanyList(isFirstLoad: boolean = false) {
    let filter = {
      fundIds: this.selectedFundList.map((item) => item.fundID).join(","),
    };
    this.reportDownloadService.GetCompanies(filter).subscribe({
      next: (result) => {
        this.isLoader = false;
        if (result != null && result.length > 0) {
          this.companyList = result;
          this.initialCompanyList = this.companyList;
          this.transformCompanyList(result);
          if (this.growthId > 0 && isFirstLoad) {
            this.setDefaultValues();
            this.onSubmit(false,isFirstLoad);
            this.initialFormValue = this.form.getRawValue();
            this.checkEnableSaveButton();
          }
        }
      },
      error: (error) => {
        this.isLoader = false;
      },
    });
  }
  /**
   * Transforms a list of companies into a grouped structure by fund ID.
   * Each fund will have a unique ID and contain a list of companies associated with it.
   *
   * @param result - The list of companies to be transformed. Each company object should contain `fundID`, `fundName`, `companyName`, and `companyId` properties.
   *
   * The transformed data will be stored in `this.groupedCompanyList` and `this.setCompanyList()` will be called to update the company list.
   */
  private transformCompanyList(result: any) {
    const groupedByFundId = result.reduce((acc, company) => {
      if (!acc[company.fundID]) {
        acc[company.fundID] = [];
      }
      acc[company.fundID].push(company);
      return acc;
    }, {} as { [key: number]: any[] });

    // Initialize a counter for unique IDs
    let idCounter = 1;
    const transformedData: any[] = Object.entries(groupedByFundId).map(
      ([fundID, companies]: [string, any[]]) => {
        const fundName = companies[0].fundName;
        const parentId = idCounter++;
        const items = companies.map((company) => ({
          text: company.companyName,
          companyId: company.companyId,
          fundId: company.fundID,
          fundName: company.fundName,
          id: idCounter++,
        }));
        return {
          text: fundName,
          fundId: parseInt(fundID),
          id: parentId,
          items: items,
        };
      }
    );
    this.groupedCompanyList = transformedData;
    this.setCompanyList();
  }

  /**
   * Populates the `selectedCompanyList` with a flattened list of companies and their items.
   *
   * This method iterates over the `groupedCompanyList`, adding each company and its items
   * to a temporary `companyList` array. The `selectedCompanyList` is then set to this
   * flattened list.
   *
   * @private
   */
  private setCompanyList() {
    let companyList = [];
    var companyIds = this.growthConfigList?.growthReportConfigModel?.companyIds?.split(",");
    this.groupedCompanyList.forEach((company) => {
      companyList.push(company);
      company.items.forEach((item) => {
        if(companyIds?.some(x => x == item?.companyId)){
          companyList?.push(item);
        }        
      });
    });
    this.selectedCompanyList = companyList;
  }

  /**
   * Lifecycle hook that is called after a component's view has been fully initialized.
   * This method checks if the `uiUxMenu` is defined and, if so, configures its `closed` property
   * using the `configureMenuClose` method.
   *
   * @memberof GrowthReportComponent
   */
  ngAfterViewInit() {
    if (this.uiUxMenu != undefined) {
      let menuClosed = (this.uiUxMenu as any).closed;
      (this.uiUxMenu as any).closed = menuClosed = this.configureMenuClose(
        this.uiUxMenu.closed
      );
    }
  }
  /**
   * Configures the closing behavior of a MatMenu to ignore "click" events.
   *
   * @param old - The original EventEmitter for the MatMenu's closed events.
   * @returns A new EventEmitter that filters out "click" events before emitting.
   */
  configureMenuClose(old: MatMenu["closed"]): MatMenu["closed"] {
    const upd = new EventEmitter();
    feed(
      upd.pipe(
        filter((event) => {
          if (event === "click") {
            return false;
          }
          return true;
        })
      ),
      old
    );
    return upd;
  }
  /**
   * Filters the grid based on the provided event.
   *
   * @param $event - The event containing the filter criteria.
   */
  filterGrid($event: any): void {
    this.isLoader = true;
    if ($event) {
      const searchTerm = $event.toLowerCase();
      this.panelList = this.originalPanelList
        .map((panel) => {
          const fundNameMatches = panel.fundName
            .toLowerCase()
            .includes(searchTerm);
          const matchedItems = panel.items?.filter((item) =>
            item?.text?.toLowerCase()?.includes(searchTerm)
          );
          const itemsMatch = matchedItems.length > 0;

          return {
            ...panel,
            items: itemsMatch ? matchedItems : panel.items,
            isMatched: fundNameMatches || itemsMatch,
          };
        })
        .filter((panel) => panel.isMatched);
    } else {
      this.panelList = this.originalPanelList;
    }
    this.isLoader = false;
  }
  /**
   * Handles the tab click event and updates the tabName.
   * If the selected tab is 'Column Headers', it sets the selected column KPI type for the selected company.
   * Otherwise, it adds a company section for the selected company.
   *
   * @param tabName - The name of the clicked tab.
   */
  onTabClick(tabName: string) {
    this.tabName = tabName;
    if (this.tabName == "Column KPI") {
      this.setSelectedColumKpiType(this.selectedCompany.companyId);
    } else {
      this.addCompanySection(this.selectedCompany);
    }
  }
  /**
   * Initializes the tab list for the component.
   *
   * This method sets up the tabList property with two tabs:
   * 1. "Row Headers" - set as active by default
   * 2. "Column Headers" - initially set as inactive
   *
   * @remarks
   * This method is likely called during component initialization to set up
   * the navigation structure for different sections of the growth report.
   * The active state of these tabs can be used to control which content is displayed.
   */
  getTabList() {
    this.tabList = [
      {
        active: true,
        name: "Row Headers",
      },
      {
        active: false,
        name: "Column KPI",
      },
    ];
  }
  saveItem() {
    this.isExist();
  }
  /**
   * Resets the header-related properties to their initial states.
   *
   * This method performs the following actions:
   * 1. Sets isEditHeader to false, ending any ongoing edit operation.
   * 2. Clears the editHeaderModel by setting it to null.
   * 3. Closes the header popup by setting openHeaderPopUp to false.
   * 4. Clears the rowHeader string.
   * 5. Closes the delete header popup by setting deleteHeaderPopUp to false.
   *
   * @remarks
   * This method is typically used to reset the component's state after
   * completing a header operation (such as adding, editing, or deleting),
   * or when cancelling such an operation.
   */
  saveHeader() {
    this.isLoader = true;
    let headerModel = {
      header: this.rowHeader,
      headerId: this.editHeader ? this.editHeaderModel?.headerId : 0,
    };
    this.growthReportService.addOrUpdateHeader(headerModel).subscribe({
      next: (result) => {
        this.isLoader = false;
        if (result > 0) {
          let message = this.isEditHeader
            ? "Row Header has been successfully updated"
            : "Row Header has been successfully added";
          this.toasterService.success(message, "", {
            positionClass: "toast-center-center",
          });
          this.addHeaderRow(this.rowHeader, result, this.isEditHeader);
          this.clearHeader();
          this.isDirty = false;
        } else {
          this.toasterService.error("something went wrong", "", {
            positionClass: "toast-center-center",
          });
        }
      },
      error: (error) => {
        this.isLoader = false;
      },
    });
  }
  /**
   * Adds or edits a header row in the rowHeaderList.
   * If isEdit is true, it updates the existing header row with the specified headerId.
   * Otherwise, it adds a new header row to the rowHeaderList.
   *
   * @param headerName - The name of the header.
   * @param headerId - The ID of the header.
   * @param isEdit - A boolean indicating whether to edit an existing header (default is false).
   */
  addHeaderRow(headerName: string, headerId: number, isEdit: boolean = false) {
    let rowHeader = {
      headerId: headerId,
      header: headerName,
    };
    if (isEdit) {
      let index = this.rowHeaderList.findIndex(
        (item) => item.headerId == headerId
      );
      this.rowHeaderList[index] = rowHeader;
      this.updateOrAddHeaderInSection(headerId, headerName);
    } else {
      this.rowHeaderList.push(rowHeader);
      this.updateOrAddHeaderInSection(headerId, headerName);
    }
  }
  /**
   * Resets the header-related properties to their initial states.
   *
   * This method performs the following actions:
   * 1. Sets isEditHeader to false, ending any ongoing edit operation.
   * 2. Clears the editHeaderModel by setting it to null.
   * 3. Closes the header popup by setting openHeaderPopUp to false.
   * 4. Clears the rowHeader string.
   * 5. Closes the delete header popup by setting deleteHeaderPopUp to false.
   *
   * @remarks
   * This method is typically used to reset the component's state after
   * completing a header operation (such as adding, editing, or deleting),
   * or when cancelling such an operation.
   */
  clearHeader() {
    this.isEditHeader = false;
    this.editHeaderModel = null;
    this.openHeaderPopUp = false;
    this.rowHeader = "";
    this.deleteHeaderPopUp = false;
  }
  /**
   * Checks if the current row header already exists and handles the result.
   *
   * This method performs the following actions:
   * 1. Sets the isLoader flag to true to indicate processing.
   * 2. Calls the growthReportService to check if the header exists.
   * 3. Based on the result:
   *    - If the header exists, sets isExistHeader to true.
   *    - If the header doesn't exist, sets isExistHeader to false and calls saveHeader().
   * 4. Sets isLoader back to false when the operation completes.
   *
   * @remarks
   * This method is likely used as part of a validation process when adding or editing headers.
   * It prevents duplicate headers and automatically saves new headers.
   *
   * @throws Will not throw an error directly, but may log errors from the service call.
   */
  isExist() {
    //this.openHeaderPopUp = false;
    this.isLoader = true;
    let headerModel = {
      header: this.rowHeader,
    };
    this.growthReportService.isHeaderExist(headerModel).subscribe({
      next: (result) => {
        this.isLoader = false;
        if (result) {
          this.isExistHeader = true;
        } else {
          this.isExistHeader = false;
          this.saveHeader();
        }
      },
      error: (error) => {
        this.isLoader = false;
      },
    });
  }

  /**
   * Handles the header focus event.
   *
   * This method sets the isExistHeader flag to false when the header receives focus.
   */
  headerFocus() {
    this.isExistHeader = false;
  }
  /**
   * Fetches the list of row headers from the growth report service.
   *
   * This method performs the following actions:
   * 1. Sets the loader state to true to indicate that data is being fetched.
   * 2. Calls the getHeaders() method of the growthReportService.
   * 3. Upon successful response:
   *    - Sets the loader state to false.
   *    - If the result is not null and contains items, updates rowHeaderList with the result.
   *    - If the result is null or empty, sets rowHeaderList to an empty array.
   * 4. In case of an error:
   *    - Sets the loader state to false.
   *    - Sets rowHeaderList to an empty array.
   *
   * @remarks This method does not return anything. It updates the component's state directly.
   * @throws Will not throw an error, but will set rowHeaderList to an empty array if an error occurs during the API call.
   */
  getRowHeaderList() {
    this.isLoader = true;
    this.growthReportService.getHeaders().subscribe({
      next: (result) => {
        this.isLoader = false;
        if (result != null && result.length > 0) {
          this.rowHeaderList = result;
        } else {
          this.rowHeaderList = [];
        }
      },
      error: (error) => {
        this.isLoader = false;
        this.rowHeaderList = [];
      },
    });
  }

  /**
   * Prepares the component for editing a header by setting up the necessary properties and opening the edit popup.
   *
   * @param item - The header item to be edited. This object should contain at least a 'header' property.
   * @remarks This method sets up the state for editing but does not perform the actual edit operation.
   * It prepares the component by:
   * 1. Setting the rowHeader to the item's header value
   * 2. Storing the entire item in editHeaderModel for further processing
   * 3. Setting isEditHeader flag to true to indicate edit mode
   * 4. Opening the header popup for user interaction
   */
  editHeader(item: any) {
    this.rowHeader = item.headerName;
    this.editHeaderModel = item;
    this.isEditHeader = true;
    this.openHeaderPopUp = true;
  }
  /**
   * Prepares for header deletion by setting up the edit model and showing the delete confirmation popup.
   *
   * @param item - The header item to be deleted. This object should contain the necessary information about the header.
   */
  deleteHeader(item: any) {
    this.editHeaderModel = item;
    this.deleteHeaderPopUp = true;
  }
  /**
   * Deletes a row header and updates the UI accordingly.
   *
   * This method calls the growth report service to delete a header,
   * displays a success or error message using a toaster service,
   * and refreshes the row header list if the deletion is successful.
   *
   * @throws {Error} If the deletion process fails or returns an unexpected result.
   */
  deleteHeaderConfirm() {
    this.isLoader = true;
    this.growthReportService
      .deleteHeader(this.editHeaderModel.headerId)
      .subscribe({
        next: (result) => {
          this.isLoader = false;
          if (result) {
            this.toasterService.success(
              "Row Header has been successfully deleted",
              "",
              {
                positionClass: "toast-center-center",
              }
            );
            this.removeHeaderFromSection(this.editHeaderModel.headerId);
            this.clearHeader();
          } else {
            this.toasterService.success("something went wrong", "", {
              positionClass: "toast-center-center",
            });
          }
        },
        error: (error) => {
          this.isLoader = false;
          this.toasterService.success("something went wrong", "", {
            positionClass: "toast-center-center",
          });
        },
      });
  }
  /**
   * Removes a header from the company sections and the rowHeaderList based on the headerId.
   * It iterates through each company section, finds the header by headerId, and removes it.
   * It also removes the header from the rowHeaderList.
   *
   * @param headerId - The ID of the header to be removed.
   */
  removeHeaderFromSection(headerId: number) {
    this.companySections.controls?.forEach((companySection: any) => {
      const rowHeaders = companySection?.get("rowHeaders") as FormArray;
      let index = rowHeaders?.controls?.findIndex(
        (header: FormGroup) => header.get("headerId").value == headerId
      );
      rowHeaders.removeAt(index);
    });
    let index = this.rowHeaderList.findIndex(
      (header: any) => header.headerId == headerId
    );
    if (index !== -1) {
      this.rowHeaderList.splice(index, 1);
    }
  }
  updateOrAddHeaderInSection(headerId: number, headerName: string) {
    this.companySections.controls?.forEach((companySection: any) => {
      const rowHeaders = companySection?.get("rowHeaders") as FormArray;
      let index = rowHeaders?.controls?.findIndex(
        (header: FormGroup) => header?.get("headerId")?.value == headerId
      );
      if (index != -1)
        rowHeaders.controls[index]?.get("headerName")?.setValue(headerName);
      else {
        const rowHeader = this.fb.group({
          headerName: [headerName || ""],
          headerId: [headerId || 0],
          kpiSections: this.fb.array([]),
        });
        rowHeaders.push(rowHeader);
      }
    });
  }
  /**
   * Handles the keyboard event for the 'Escape' key.
   * When the 'Escape' key is pressed, it closes the header pop-up and delete header pop-up,
   * and clears the header.
   *
   * @param event - The keyboard event.
   */
  @HostListener("document:keydown.escape", ["$event"])
  handleKeyboardEvent(event: KeyboardEvent) {
    this.openHeaderPopUp = false;
    this.deleteHeaderPopUp = false;
    this.clearHeader();
  }
  /**
   * Handles the keyboard event for the 'Enter' key.
   * When the 'Enter' key is pressed, it closes the header pop-up and delete header pop-up,
   * and clears the header.
   *
   * @param event - The keyboard event.
   */
  @HostListener("document:keydown.enter", ["$event"])
  handleKeyboardEnterEvent(event: KeyboardEvent) {}
  /**
   * Retrieves the KPI modules and updates the kpiModules list.
   * It sets the loader to true, makes a service call to get the KPI modules,
   * and updates the kpiModules list based on the result. If an error occurs,
   * it sets the loader to false.
   */
  getKpiModules() {
    this.isLoader = true;
    this.growthReportService.getKpiModules().subscribe({
      next: (result) => {
        this.isLoader = false;
        this.kpiModules = result;
      },
      error: (error) => {
        this.isLoader = false;
      },
    });
  }
  /**
   * Adds a new KPI section to the specified row header.
   * It retrieves the rowHeaders form array, finds the specified row header by index,
   * and adds a new KPI section to the kpiSections form array.
   *
   * @param header - The header information (not used in this method).
   * @param index - The index of the row header to which the KPI section will be added.
   */
  getKpiList(selectedModuleId: number, section: any = null) {
    this.isLoader = true;
    this.isDirty = true;
    this.growthReportService
      .getKpiListByModuleId({
        companyId: this.selectedCompany.companyId,
        moduleId: selectedModuleId,
        kpiIds: '',
      })
      .subscribe({
        next: (result) => {
          this.isLoader = false;
          if (result?.length > 0) {
            this.kpiLineItemList[selectedModuleId] = result;
          } else {
            this.kpiLineItemList[selectedModuleId] = [];
          }
          if (section != null) {
            section.get("lineItemId").setValue([]);
          }
        },
        error: (error) => {
          this.isLoader = false;
          this.kpiLineItemList[selectedModuleId] = [];
        },
      });
  }
  /**
   * Adds a new KPI section to the specified row header.
   * It retrieves the rowHeaders form array, finds the specified row header by index,
   * and adds a new KPI section to the kpiSections form array.
   *
   * @param header - The header information (not used in this method).
   * @param index - The index of the row header to which the KPI section will be added.
   */
  addKpiSection(header: any, index: number) {
    const rowHeaders = this.selectedCompanySection.get(
      "rowHeaders"
    ) as FormArray;
    const rowHeader = rowHeaders.at(index) as FormGroup;
    const kpiSections = rowHeader.get("kpiSections") as FormArray;
    kpiSections.setValidators(Validators.required);
    kpiSections.push(this.createKpiSection());
    this.isDirty = true;
    this.isRowHeaderAdded = true;
  }
  /**
   * Handles the click event for the "Select All" checkbox.
   * It toggles the selection state of all items in the list.
   * If the checkbox is checked, all items are selected.
   * If the checkbox is unchecked, all items are deselected.
   *
   * @param checkAllProperty - The current state of the "Select All" checkbox.
   * @param selectedList - The list of currently selected items.
   * @param totalList - The total list of items.
   * @param index - The index of the current section.
   * @param section - The section containing the controls to update.
   */
  onSelectAllCheckBoxClick(
    checkAllProperty: boolean,
    selectedList: any[],
    totalList: any[],
    index: number,
    section: any
  ) {
    checkAllProperty = !checkAllProperty;
    selectedList = checkAllProperty
      ? totalList.map((item) => item.mappingId).slice()
      : [];
    section?.get("lineItemId")?.setValue(selectedList);
    section?.get("isCheckAll")?.setValue(checkAllProperty);
  }
  /**
   * Handles the form submission event.
   * It checks the submitter's inner text to determine the action to take.
   * If the submitter's inner text is "Add Header" or "Add KPI", it returns without doing anything.
   * If the submitter's inner text is "Reset", it resets the form.
   * If the form is valid, it calls the saveConfig method to save the configuration.
   *
   * @param $event - The form submission event.
   */
  onSubmitForm($event) {
    if (
      $event?.submitter?.innerText == "Add Header" ||
      $event?.submitter?.innerText == "Add KPI"
    ) {
      return;
    }
    if ($event?.submitter?.innerText == "Reset") {
      this.resetForm();
      return;
    }
    if (this.form.valid) {
      this.saveConfig();
    }
  }
  resetForm() {   
    if (this.isDirty) {
      this.menuTrigger.closeMenu();
      this.isSet = true;
      return;
    } 
    this.setDefaultValueForSpecificCompany(this.selectedCompany?.companyId);
  }
  
  /**
   * Retrieves the growth configuration and initializes the values.
   * It sets the loader to true, makes a service call to get the growth configuration,
   * and updates the growthId and growthConfigList based on the result.
   * If an error occurs, it sets the growthConfigList to null and initializes the values.
   */
  setDefaultForChange(companyId: number){
    let indexCompany = this.companySections?.controls?.findIndex((companySection: any) => companySection?.get('companyInfo.companyId')?.value == companyId);
    const companyRowHeaders = this.selectedCompanySection?.get('rowHeaders') as FormArray;
    companyRowHeaders?.clear();
    const kpiSections = this.selectedCompanySection?.get('columnHeaders.kpiSections') as FormArray;
    kpiSections?.clear();
    const rowHeaders = this.companySections?.at(indexCompany)?.get("rowHeaders") as FormArray;
    // Re-add row headers
    this.rowHeaderList?.forEach(header => {
      if(!rowHeaders?.controls?.some(x => x?.value?.headerId === header?.headerId)){
      this.addRowHeader(indexCompany, header, companyId);
      }
    });
    const companyColumnHeader = this.selectedCompanySection?.get('columnHeaders') as FormGroup;
    companyColumnHeader?.setControl('kpiSections', this.fb.array(this.getKpiSections(companyId)));
    this.isDirty = false;
  }
  /**
   * Retrieves the growth configuration and initializes the values.
   * It sets the loader to true, makes a service call to get the growth configuration,
   * and updates the growthId and growthConfigList based on the result.
   * If an error occurs, it sets the growthConfigList to null and initializes the values.
   */
  private saveConfig() {
    let formValue = this.form.value;
    let selectedCompanyIds = new Set(this.selectedCompanyList.map((company: any) => company.companyId));
    let model = {
      fundIds: this.selectedFundList.map((fund: any) => fund.fundID).join(",").replace(/^,|,$/g, ''),
      companyIds: Array.from(
      selectedCompanyIds
      ).join(",").replace(/^,|,$/g, ''),
      GrowthId: this.growthId,
      Companies: formValue?.companySections.filter((section: any) => 
      selectedCompanyIds.has(section.companyInfo?.companyId)
      )
    };
    this.growthReportService.addGrowthReportConfig(model).subscribe({
      next: (result) => {
        this.isLoader = false;
        if (result) {
          let message =
            this.growthId > 0
              ? "Changes have been successfully updated for all companies"
              : "Changes have been successfully saved for all companies";
          this.toasterService.success(message, "", {
            positionClass: "toast-center-center",
          });
          this.getTemplateConfig(false);
          this.isDirty = false;
          this.isFundChange = false;
          this.isRowHeaderAdded = false;
          this.isSaveEnabled = false;
        } else {
          this.toasterService.error("something went wrong", "", {
            positionClass: "toast-center-center",
          });
        }
      },
      error: (error) => {
        this.isLoader = false;
      },
    });
  }
  /**
   * Retrieves the growth configuration and initializes the values.
   * It sets the loader to true, makes a service call to get the growth configuration,
   * and updates the growthId and growthConfigList based on the result.
   * If an error occurs, it sets the growthConfigList to null and initializes the values.
   */
  getTemplateConfig(isLoad:boolean = true) {
    this.isLoader = true;
    this.growthReportService.getGrowthConfig().subscribe({
      next: (result) => {
        this.isLoader = false;
        if (result != null) {
          this.growthId = result?.growthReportConfigModel?.growthId;
          this.growthConfigList = result;
        } 
        else 
          this.growthConfigList = null;
        if(isLoad)
          this.initializeValues();
      },
      error: (error) => {
        this.isLoader = false;
        this.growthConfigList = null;
        this.initializeValues();
      },
    });
  }
  /**
   * Updates the selection state based on the selected list and total list.
   * It checks if all items are selected or none are selected and updates the
   * corresponding control value.
   *
   * @param selectedList - The list of selected items.
   * @param totalList - The total number of items.
   * @param checkAllProperty - The property name to check if all items are selected.
   * @param index - The index of the current section.
   * @param section - The section containing the control to update.
   */
  getSelectedObjects(
    selectedList: any[],
    totalList: number,
    checkAllProperty: string,
    index: number,
    section: any
  ) {
    this.updateSelectionState(selectedList, totalList, checkAllProperty);
    const isCheckAllControl = section?.get(checkAllProperty);
    if (selectedList.length == totalList) {
      isCheckAllControl?.setValue(true);
    }
    if (selectedList.length == 0) {
      isCheckAllControl?.setValue(false);
    }
  }

  isCheAllchecked(kpiSection): boolean{
      var selectedList = kpiSection?.get('lineItemId')?.value;
       var totalList = this.kpiLineItemList[kpiSection.get('kpiTypeId').value].length;
       var checkAllProperty = 'isCheckAll';
       var section = kpiSection;
      const isCheckAllControl = section?.get(checkAllProperty);
      if (selectedList.length == totalList) {
        isCheckAllControl?.setValue(true);
        return true;
      }
      return false;
    }
    
  /**
   * Sets the default values for the company sections based on the company list.
   * It retrieves the unique company IDs, finds the corresponding fund ID for each company,
   * and creates a form group for each company with its associated row and column headers.
   * The form groups are then pushed to the companySections array.
   */
  setDefaultValues() {
    let companyList = this.getCompanies();
    companyList?.forEach((companyId: any) => {
      let fundId = this.companyList.find((companyItem: any) => companyItem.companyId == companyId)?.fundID;
      const companySection = this.createCompanySection(parseInt(companyId), fundId);
      this.companySections.push(companySection);
      this.rowHeaderList.forEach(header => {
        this.addRowHeader(this.companySections.length - 1, header, companyId);
      });
    });
  }
  setDefaultValueForSpecificCompany(companyId: number) {
    let index = this.companySections?.controls?.findIndex((companySection: any) => companySection?.get('companyInfo.companyId')?.value == companyId);
    const rowHeaders = this.selectedCompanySection?.get('rowHeaders') as FormArray;
    rowHeaders?.clear();
    const kpiSections = this.selectedCompanySection?.get('columnHeaders.kpiSections') as FormArray;
    kpiSections?.clear();
    // Re-add row headers
    this.rowHeaderList?.forEach(header => {
      this.addRowHeader(index, header, companyId);
    });
    const columnHeader = this.selectedCompanySection?.get('columnHeaders') as FormGroup;
    columnHeader?.setControl('kpiSections', this.fb.array(this.getKpiSections(companyId)));
    this.initializeValues(); 
  }
  addKpiSectionsToColumnHeaders(index: number = 0,companyId) {
    const columnHeader = this.companySections.at(index).get('columnHeaders') as FormGroup;
    columnHeader.addControl('kpiSections', this.fb.array(this.getKpiSections(companyId)));
    const kpiSections = columnHeader.get('kpiSections') as FormArray;
    kpiSections.controls.push(...this.getKpiSections(companyId));
}
  private createCompanySection(companyId: number, fundId: number): FormGroup {
    return this.fb.group({
      companyInfo: this.fb.group({
        companyId: [companyId, Validators.required],
        fundId: [fundId || 0, Validators.required],
      }),
      rowHeaders: this.fb.array([]),
      columnHeaders: this.fb.group({
        kpiSections: this.fb.array(this.getKpiSections(companyId)),
      }),
    });
  }
  /**
   * Retrieves a list of unique company IDs from the rowHeaders and columnHeaders
   * in the growthConfigList. It combines the company IDs from both headers and
   * returns a unique set of company IDs.
   */
  getCompanies() {
    let rowCompany = Array.from(
      new Set(
        this.growthConfigList?.rowHeaders?.map(
          (header: any) => header.companyId
        ) ?? []
      )
    );
    let columnCompany = Array.from(
      new Set(
        this.growthConfigList?.columnHeaders?.map(
          (header: any) => header.companyId ?? []
        )
      )
    );
    return Array.from(new Set([...rowCompany, ...columnCompany]));
  }
  /**
   * Sets the selected funds based on the growthId and updates the selection state.
   * If growthId is greater than 0, it splits the fundIds string into an array,
   * filters the fundList to get the selected funds, updates the selection state,
   * and fetches the company list based on the selected funds.
   */
  setFundSelected(isFirstLoad: boolean = false) {
    if (this.growthId > 0) {
      let fundIds = this.growthConfigList?.growthReportConfigModel?.fundIds.split(",");
      this.selectedFundList = this.fundList?.filter((fund: any) =>
        fundIds.includes(fund.fundID.toString())
      );
      this.updateSelectionState(
        this.selectedFundList,
        this.fundList.length,
        "isCheckedCopyFundAll"
      );
      this.getCompanyList(isFirstLoad);
    }
  }
}
/**
 * Subscribes to an observable and forwards its emissions to a subject.
 *
 * @template T - The type of the items emitted by the observable.
 * @param {Observable<T>} from - The source observable to subscribe to.
 * @param {Subject<T>} to - The target subject to forward the emissions to.
 * @returns {Subscription} - The subscription object representing the subscription to the source observable.
 */
function feed<T>(from: Observable<T>, to: Subject<T>): Subscription {
  return from.subscribe({
    next: data => to.next(data),
    error: err => to.error(err),
    complete: () => to.complete(),
  });
}
