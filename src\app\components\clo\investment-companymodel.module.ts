import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { InvestmentCompanyComponent } from './investmentcompany/investment-company.component';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { AngularResizeEventModule } from 'angular-resize-event';
import { QuillModule } from 'ngx-quill';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { InvestCompanyService } from './investmentcompany/investmentcompany.service';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';

@NgModule({
  declarations: [
    InvestmentCompanyComponent,

  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MaterialModule,
    PrimeNgModule,
    AngularResizeEventModule,
    QuillModule,
    SharedDirectiveModule,
    SharedComponentModule,
    ReactiveFormsModule, // Import ReactiveFormsModule here

    RouterModule.forChild([
        { path: '', component: InvestmentCompanyComponent}
    ]),
    KendoModule
  ],
  providers: [
    InvestCompanyService
  ],
  exports: [
    InvestmentCompanyComponent
  ]
})
export class InvestmentCompanyModule { }