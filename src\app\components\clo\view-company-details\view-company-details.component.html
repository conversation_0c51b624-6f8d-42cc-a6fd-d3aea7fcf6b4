<div class="comp-data">
  <div class="company-name-detail">{{ compData?.companyName }}</div>
  <div class="company-facts-tab">
    <ng-container *ngFor="let tab of tabs">
      <button id="{{tab.tabId}}" 
              [ngClass]="{ highlight: selectedTab === tab.tabId }"
              class="clo-fs-tab"
              (click)="selectTab(tab)">
        {{tab.aliasName}}
      </button>
    </ng-container>
  </div>
  <div *ngIf="selectedTab === TAB_NAMES.Investment_Page" class="company-facts-invest-summary">
    <div style="padding-bottom: 20px;">
      <ng-container *ngFor="let table of selectedTabData?.tableList">
            <div *ngIf="(table.tableId==companyFactsTableId  || table.tableId== summaryTableId) && table.isShow" >             
            

             <div class="company-facts-staticDataContainer"> 
              <div *ngIf="canViewCompanyFacts && table.tableId==companyFactsTableId"> 
               <div class="company-facts">
                 <span class="company-facts-section">{{table.aliasName}}</span>
                 <span class="edit-icon" id="edit-company-summary" (click)="redirectToInvestmentPage(1)" (keypress)="redirectToInvestmentPage(1)">
                   <img alt="" src="assets/dist/images/clo_edit.svg" />
                 </span>
               </div>
               <div class="container-fluid p-4 custom-border">
                 <div class="row">
                   <!-- Left Column -->
                   <div class="col-md-6">
                       <div class="card h-100 border-0">
                       <div class="card-body">
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">Company Name</div>
                           <div class="col-8 text-truncate"
                                [title]="compData.companyName.length > 35 ? compData.companyName : ''"
                                >
                             {{compData.companyName}}
                           </div>
                         </div>
                         
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">Domicile</div>
                           <div class="col-8 text-truncate"
                                [title]="compData.domicile.length > 35 ? compData.domicile : ''"
                                >
                             {{compData.domicile}}
                           </div>
                         </div>
               
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">First Closing</div>
                           <div class="col-8 text-truncate">{{compData.firstClose}}</div>
                         </div>
               
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">Final Close</div>
                           <div class="col-8 text-truncate">{{compData.finalClose}}</div>
                         </div>
               
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">Investment Period End Date</div>
                           <div class="col-8 text-truncate">{{compData.investmentPeriodEndDate}}</div>
                         </div>
               
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">Incorporation Date</div>
                           <div class="col-8 text-truncate">{{compData.incorporationDate}}</div>
                         </div>
               
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">Maturity Date</div>
                           <div class="col-8 text-truncate">{{compData.maturityDate}}</div>
                         </div>
                       </div>
                     </div>
                   </div>
               
                   <!-- Right Column -->
                   <div class="col-md-6">
                     <div class="card h-100 border-0">
                       <div class="card-body">
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">Commitments</div>
                           <div class="col-8 text-truncate"
                                [title]="compData.commitments.length > 35 ? compData.commitments : ''"
                                >
                             {{compData.commitments}}
                           </div>
                         </div>
               
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">Base Currency</div>
                           <div class="col-8 text-truncate"
                                [title]="compData.baseCurrency.length > 35 ? compData.baseCurrency : ''"
                                >
                             {{compData.baseCurrency}}
                           </div>
                         </div>
               
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">Custodian</div>
                           <div class="col-8 text-truncate"
                                [title]="compData.custodian.length > 35 ? compData.custodian : ''"
                                >
                             {{compData.custodian}}
                           </div>
                         </div>
               
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">Administrator</div>
                           <div class="col-8 text-truncate"
                                [title]="compData.administrator.length > 35 ? compData.administrator : ''"
                                >
                             {{compData.administrator}}
                           </div>
                         </div>
               
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">Listing Agent</div>
                           <div class="col-8 text-truncate"
                                [title]="compData.listingAgent.length > 35 ? compData.listingAgent : ''"
                                >
                             {{compData.listingAgent}}
                           </div>
                         </div>
               
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">Legal Counsel</div>
                           <div class="col-8 text-truncate"
                                [title]="compData.legalCounsel.length > 35 ? compData.legalCounsel : ''"
                                >
                             {{compData.legalCounsel}}
                           </div>
                         </div>
               
                         <div class="row mb-2">
                           <div class="col-4 text-secondary">Portfolio Advisor</div>
                           <div class="col-8 text-truncate"
                                [title]="compData.portfolioAdvisor.length > 35 ? compData.portfolioAdvisor : ''"
                                >
                             {{compData.portfolioAdvisor}}
                           </div>
                         </div>
                       </div>
                     </div>
                   </div>
                 </div>
               </div>
             </div> 
             <div *ngIf="canViewInvestmentSummary && table.tableId== summaryTableId">
               <div class="company-facts">
                 <span class="company-facts-section">{{table.aliasName}}</span>
                 <span id="edit-investment-summary" class="edit-icon" (click)="redirectToInvestmentPage(2)" (keypress)="redirectToInvestmentPage(2)"><img alt="" src="assets/dist/images/clo_edit.svg" /></span>
               </div>
               <div class="investment-summary">{{ compData.investmentSummary }}</div>
           </div>
             </div>
            </div>
            <div *ngIf="(table.tableId!=companyFactsTableId && table.tableId!= summaryTableId && table.isShow)" class="company-facts-staticDataContainer investment-summary-no-borders">
              <div class="company-facts-glo-container">
                <app-flat-table [isShouldFetchDataOnLoad]="true"
                [companyID]="companyName.id"
                [tableTitle]="table.aliasName"
                [tableName]="table.tableName"
                [isStaticTable] = "table.isStaticTable"
                [tableType]="table.tableType"
                [tableId]="table.tableId"
                [canImport] = "checkTablePermissions(table.tableId,CAN_IMPORT)"
                [canExport] = "checkTablePermissions(table.tableId,CAN_EXPORT)"
                [canEdit] = "checkTablePermissions(table.tableId,CAN_EDIT)"
                [isCompositeRowFilterRequired]="table.tableId==Aggregate_CLO_Metrics?true:false">
                </app-flat-table>
              </div> 
            </div>
      </ng-container>
    </div>
  </div>
<!-- Commentaries -->
  <div *ngIf="selectedTab === TAB_NAMES.Commentaries" class="clo-table-body col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-4 pl-4 Heading2-M">
    <div class="row mr-0 ml-0 clo-table-content">
      <ng-container *ngFor="let clo of commentarylist">
        <div class="custom-footnote col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 clo-item TextTruncate" *ngIf="checkTablePermissions(clo.tableId)" [ngClass]="clo.isExpanded ? 'clo-active':'clo-in-active'">
          <div title="{{clo.name}}" class="float-left TextTruncate Heading2-M">{{clo.name}}</div>
          <div class="float-right">
            <span *ngIf="clo.isExpanded" id="edit-footnote-commenteries" (click)="toggleEdit(clo)" (keypress)="toggleEdit()">
              <img class="custom-size" alt="" src="assets/dist/images/clo_edit.svg" />
            </span>
            <a (click)="expandPanel(clo)">
              <img src="assets/dist/images/{{clo.isExpanded ? 'arrow-down.svg' :'chevron-down-i.svg'}}" alt="Sort left" />
            </a>
          </div>
        </div>
        <ng-container *ngIf="clo.isExpanded && checkTablePermissions(clo.tableId)" >
          <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0 child-add-item TextTruncate">
            <div class="textarea-container">
              <ng-container *ngIf="!clo.isEdit">
                <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0 child-add-item custom-box TextTruncate">

                  <ng-container *ngIf="clo.newComment; else noComment">
                    <app-custom-quill-editor [readOnly]="true" [charCount]="charCount" class="custom-quill-editor"  [noteText]="clo.newComment" [ngModel]="clo.newComment" [modules]="quillConfig"></app-custom-quill-editor>
                  </ng-container>
                  </div>
                  <ng-template #noComment>
                    <div class="empty-text Body-R pt-2 pl-3">N/A</div>
                  </ng-template>
              </ng-container>
              <ng-container *ngIf="clo.isEdit">
                <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0 child-add-item custom-box TextTruncate">

                <app-custom-quill-editor  [editorPlaceholder]="editorPlaceholder"  [charCount]="charCount" [(ngModel)]="clo.newComment" [noteText]="clo.newComment" [showCharCount]="true" class="custom-quill-editor"></app-custom-quill-editor>
                <div class="d-flex justify-content-between custom-quillcontainer pb-3 pl-4 pr-4 pt-3">
                  <button id="btn-reset" class="btn TextTruncate btn-warning mr-2 TextTruncate" (click)="onReset(clo)">Clear</button>
                  <div class="btn-controls float-right">
                    <button id="btn-cancel-footnote" (click)="onCancel(clo)" class="btn TextTruncate btn-warning mr-2 TextTruncate">Cancel</button>
                    <button id="btn-save-footnote" class="btn-save-clo btn btn-primary" (click)="onSave(clo, clo.commentaryType)">Save</button>
                  </div>
                </div>
                </div>
              </ng-container>
            </div>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>

  <div *ngIf="selectedTab === TAB_NAMES.Performance_Data" class="clo-table-body col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-4 pl-4 pt-4 Heading2-M">
    <div class="row mr-0 ml-0 clo-table-content">
      <kendo-tabstrip #tabStrip id="{{selectedPerformanceTab}}" (tabSelect)="onTabSelect($event)">
        <ng-container *ngFor="let tab of performanceTabs">
            <kendo-tabstrip-tab  
            class="pr-0 pl-0" 
            *ngIf="checkTabPermission(tab.tabId)" 
            [title]="tab.aliasName" 
            [selected]="performanceTabs[0] === tab" >
            <ng-template kendoTabContent>
              <app-flat-table *ngIf="tab.tableList[0].isShow" [isStaticTable]="tab.tableList[0].isStaticTable" 
                      [companyID]="companyName.id"
                      [data]="performanceData" 
                      [tableType]="tab.tableList[0].tableType"
                      [columns]="performanceColumns"
                      [tableTitle]="tab.tableList[0].aliasName"
                      [tableName]="tab.tableList[0].tableName"
                      [tableId]="tab.tableList[0].tableId"
                      [canImport] = "checkTablePermissions(tab.tableList[0].tableId,CAN_IMPORT)"
                      [canExport] = "checkTablePermissions(tab.tableList[0].tableId,CAN_EXPORT)"
                      [canEdit] = "checkTablePermissions(tab.tableList[0].tableId,CAN_EDIT)"
                      >
              </app-flat-table>
            </ng-template>
            </kendo-tabstrip-tab>
        </ng-container>
      </kendo-tabstrip>
    </div>
  </div>
</div>

<app-loader-component *ngIf="isLoading"></app-loader-component>