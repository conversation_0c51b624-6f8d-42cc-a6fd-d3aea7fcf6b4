import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { EmailConfigurationPageComponent } from './email-configuration-page.component';
import { EmailConfigurationCompanyListComponent } from './email-configuration-company-list/email-configuration-company-list.component';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { EmailConfigurationUsersComponent } from './email-configuration-user-info-by-company/email-configuration-users.component';
import { EmailGroupComponent } from './email-group/email-group.component';
import { GridModule } from '@progress/kendo-angular-grid';
import { ButtonsModule } from '@progress/kendo-angular-buttons';
import { CheckBoxModule } from '@progress/kendo-angular-inputs';

import { MultiSelectModule, DropDownListModule } from '@progress/kendo-angular-dropdowns';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
@NgModule({
    declarations: [
        EmailConfigurationPageComponent,
        EmailConfigurationCompanyListComponent,
        EmailConfigurationUsersComponent,
        EmailGroupComponent
    ],
    imports: [
        CommonModule,
        SharedComponentModule,
        FormsModule,
        MultiSelectModule,
        KendoModule,
        DropDownListModule,
        CheckBoxModule,
        GridModule,
        RouterModule.forChild([
            {
                path: '',
                component: EmailConfigurationPageComponent
            }
        ])
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    exports: [EmailConfigurationPageComponent
    ]
})
export class EmailConfigurationPageModule { }