import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CloKpiHistoryComponent } from './clo-kpi-history.component';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonSubFeaturePermissionService } from 'src/app/services/subPermission.service';
import { CloService } from '../../../services/clo.service';
import { ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';

describe('CloKpiHistoryComponent', () => {
  let component: CloKpiHistoryComponent;
  let fixture: ComponentFixture<CloKpiHistoryComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;
  let mockCloService: jasmine.SpyObj<CloService>;
  let mockPermissionService: jasmine.SpyObj<CommonSubFeaturePermissionService>;
  let mockToastrService: jasmine.SpyObj<ToastrService>;

  const mockCloDetails = {
    companyID: '789',
    name: 'Test CLO'
  };

  const mockPermissionResult = [
    { subFeature: 'KPIHistory', CAN_VIEW: true },
    { subFeature: 'Summary', CAN_VIEW: true }
  ];

  beforeEach(async () => {
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockCloService = jasmine.createSpyObj('CloService', ['getCloById']);
    mockPermissionService = jasmine.createSpyObj('CommonSubFeaturePermissionService', ['getCommonSubFeatureAccessPermissions']);
    mockToastrService = jasmine.createSpyObj('ToastrService', ['error', 'success']);

    mockActivatedRoute = {
      queryParams: of({
        kpiname: 'Test KPI',
        cloid: '123',
        dmcl: 'EU'
      })
    };

    mockCloService.getCloById.and.returnValue(of(mockCloDetails));
    mockPermissionService.getCommonSubFeatureAccessPermissions.and.returnValue(of(mockPermissionResult));

    await TestBed.configureTestingModule({
      declarations: [ CloKpiHistoryComponent ],
      imports: [ HttpClientTestingModule ],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: CloService, useValue: mockCloService },
        { provide: CommonSubFeaturePermissionService, useValue: mockPermissionService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: 'BASE_URL', useValue: 'http://localhost/' } // <-- Add this line
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CloKpiHistoryComponent);
    component = fixture.componentInstance;
    mockPermissionService.getCommonSubFeatureAccessPermissions.and.returnValue(of(mockPermissionResult));
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with query params and fetch CLO details', () => {
    expect(component.cloName).toBe('Test KPI');
    expect(component.cloId).toBe('123');
    expect(component.isusdomicile).toBeFalse();
    expect(mockCloService.getCloById).toHaveBeenCalledWith('123');
  });

  it('should set company ID from CLO details', () => {
    expect(component.companyId).toBe('789');
    expect(mockPermissionService.getCommonSubFeatureAccessPermissions).toHaveBeenCalled();
  });

  it('should navigate to CLO list', () => {
    component.redirectToCloPage();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/clo-list']);
  });

  it('should navigate to CLO summary', () => {
    component.cloId = '123';
    component.redirecttoCloSummary();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/view-clo-summary', '123']);
  });

  it('should check permission access correctly', () => {
    const mockPermissions = [{ CAN_VIEW: true }];
    const result = component.checkPermissionAccess(mockPermissions, 'CAN_VIEW');
    expect(result).toBeTrue();
  });
});
