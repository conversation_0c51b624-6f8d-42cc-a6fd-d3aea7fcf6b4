import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { InvestCompanyService } from '../investmentcompany/investmentcompany.service';
import { ToastrService } from 'ngx-toastr';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

// Interface for type safety (adjust properties based on your API response)
interface GridData {
  id: number;
  [key: string]: any; // for dynamic columns
}

@Component({
  selector: 'app-upload',
  templateUrl: './upload-performance-data.component.html',
  styleUrls: ['./upload-performance-data.component.scss']
})
export class UploadPerformanceData implements OnInit {
  @Input() data: GridData[] = [];
  @Input() columns: Array<{
    field: string;
    title: string;
    width?: number;
  }> = [];

  @Input() tableName: string = '';  
  @Output() fileUploaded = new EventEmitter<File>();
  @Input() companyID: string = ''; 

  openUploadDialog: boolean = false;
  uploadedFiles: File[] = [];
  isUploading: boolean = false;
  fileSize: number = 0;
  invalidFile: boolean = false;
  

  constructor(
    private readonly investCompanyService: InvestCompanyService,
    private readonly toastrService: ToastrService,
    private readonly modalService: NgbModal,
  ) { }

  downloadTemplate(): void {
    if (!this.tableName) {
      this.toastrService.error('Table name is required');
      return;
    }

    this.investCompanyService.downloadTemplate(this.tableName).subscribe({
      next: (blob: Blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${this.tableName}_template.xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.toastrService.error('Error downloading template');
        console.error('Download error:', error);
      }
    });
  }

  ngOnInit(): void {
    // If you receive data from API, you can set it like this:
    // this.data = apiResponse.map(item => ({
    //   id: item.id,
    //   ...item // spread other properties
    // }));
  }
//on browse this has to be called
  onFileSelected(): void {
    const file = this.uploadedFiles[0];
    if (file) {
      this.fileUploaded.emit(file);
      this.modalService.dismissAll();
    }
  }
  openUpload(){
    this.openUploadDialog = true;
  }
  closeModal(){
    this.modalService.dismissAll();
    this.openUploadDialog = false;
    this.uploadedFiles=[];    
  }
 
  onBrowsed(event: any): void {
    const file = event.target.files[0];
    this.processFile(file);
  }

  onFileDropped(files: any[]): void {
    const file = files[0];
    this.processFile(file);
  }
  processFile(file: File): void {
    this.isUploading = true;
    if (file) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      const fileSizeInKB = (file.size / 1024).toFixed(2);
      const fileSizeInMB = file.size / (1024 * 1024);
      if (fileSizeInMB > 20) {
        this.invalidFile = true;
        this.isUploading = false;
      }
      if (fileExtension === 'xlsx' || fileExtension === 'xls') {
        this.uploadedFiles = [file];
        this.fileSize = parseFloat(fileSizeInKB);
        console.log('Selected file:', file);
      } else {
        this.toastrService.warning('Please select only Excel files');
        this.isUploading = false;
      }
    }
  }
  removeFile(){
    this.uploadedFiles=[];
  }
}