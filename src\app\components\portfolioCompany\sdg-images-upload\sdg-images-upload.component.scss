@import "../../../../variables";

.sdg-section {
  background: #FAFAFC 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.0784313725);
  border: 1px solid #DEDFE0;
  border-radius: 4px;
  opacity: 1;
  margin: 0px;

  .sdg-header {
    margin: 0px;
    height: 40px;
    align-content: center;
  }

  .sdg-footer {
    margin: 0;
    border-bottom: 0px white;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    border-top: 1px solid #E1E1E8;
    height: 50px;
    background: #FAFAFA;
  }

  .sdg-panel {
    background: white;
    height: 300px;
    align-content: center;

    .upload {
      width: 125px !important;
      bottom: 1px;
    }
  }

  .c-header {
    border: 1px solid #E1E1E8;
    border-top: none;
    border-right: none;
    border-left: none;
    margin: 10px 0px 0px 0px;
  }

  .button-footer {
    height: 3.25rem;
    border-bottom: 1px solid #E1E1E8;
    background: #FAFAFA;
  }

  .btn-comment-section {
    padding: 0.5rem 0.9375rem;
  }

  .no-comment-font {
    color: #666666;
    font-weight: 400 !important;
  }
}

.sdg-image-container {
  height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  /* Disable horizontal scrolling */
  display: flex;
  flex-wrap: wrap;
  margin-left: 15px;

  .sdg-image {
    width: 260px;
    /* Fixed width */
    padding: 15px;
    box-sizing: border-box;

    .uploaded-image {
      width: 100%;
      height: 140px;
      border: dashed 1px #DEDFE0;
      border-radius: 4px;
    }
  }
}

.sdg-image-container-div {
  margin-right: 10px;
  width: 100% !important;
}

.browse-button[for="sdgUpload"] {
  color: white !important;
  text-align: center;
}

.remove-image-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #ffffff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  content-visibility: auto;
}
.allowed-ext{
  font-style: italic;
  font-weight: 400;
}