﻿<div class="row mr-0 ml-0">
    <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0">
        <div class="add-user-component">
            <div class="row mr-0 ml-0">
                <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0">
                    <div class="performance-header pt-2 pb-2 float-left">
                        {{fundName}}
                        <!-- <tooltip [iconClass]="'fa-question-circle help'">
                            <div class="row">
                                <div class="col-12">
                                    <h6>Keyword Used</h6>
                                </div>
                            </div>
                            <div class="col-sm-12 content-bg">
                                <div class="row detail-border">
                                    <div class="col-sm-3"><label>Realized Value:</label></div>
                                    <div class="col-sm-9">
                                        Value realized as of date
                                    </div>
                                </div>
                                <div class="row detail-border">
                                    <div class="col-sm-3"><label>Unrealized Value:</label></div>
                                    <div class="col-sm-9">
                                        Value unrealized as of date
                                    </div>
                                </div>
                                <div class="row detail-border">
                                    <div class="col-sm-3"><label>Total Value:</label></div>
                                    <div class="col-sm-9">
                                        Sum of realized and unrealized value
                                    </div>
                                </div>
                                <div class="row detail-border">
                                    <div class="col-sm-3"><label>IRR:</label></div>
                                    <div class="col-sm-9">
                                        Internal rate of return
                                    </div>
                                </div>
                                <div class="row detail-border">
                                    <div class="col-sm-3"><label>Total Value Paid In(TVPI):</label></div>
                                    <div class="col-sm-9">
                                        Total value divided by invested cost
                                    </div>
                                </div>

                            </div>
                        </tooltip> -->
                    </div>
                    <div class="float-right pb-2">
                        <div class="download-fund-excel"><img alt="" src="assets/dist/images/FileDownloadWhite.svg" class="showHandIcon pr-1 mt-0"> Cashflow Report </div>
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0">
                    <div class="table-responsive shadow">
                        <table id="cashflowTotalHeader" class='table cashflow-total-header fund-header-tbl bordered' *ngIf="fundData">
                            <thead>
                                <tr>
                                    <th scope="col">Capital Invested</th>
                                    <th scope="col">Realized Value</th>
                                    <th scope="col">Unrealized Value</th>
                                    <th scope="col">Total Value</th>
                                    <th scope="col">Gross TVPI</th>
                                    <th scope="col">Gross IRR</th>
                                    <th scope="col">Net TVPI</th>
                                    <th scope="col">Net IRR</th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr>
                                    <td id="capitalInvested"><span>{{fundData["Capital Invested"]}}</span>
                                    </td>
                                    <td id="realized"><span>{{fundData["Realized Value"]}}</span>
                                    </td>
                                    <td id="unrealized"><span>{{fundData["Unrealized Value"]}}</span>
                                    </td>
                                    <td id="totalValue"><span>{{fundData["Total Value"]}}</span>
                                    </td>
                                    <td id="grossTVPI"><span>{{fundData["Gross TVPI"]+'x'}}</span>
                                    </td>
                                    <td id="grossIRR"><span [innerHtml]="miscService.formatFloatNumber(fundData['Gross IRR']==null?'': fundData['Gross IRR'])|minusSignToBrackets"></span>
                                    </td>
                                    <td id="netTVPI"><span [innerHtml]="(fundData['Net TVPI']|number:NumberDecimalConst.multipleDecimal)+'x'"></span>
                                    </td>
                                    <td id="netIRR"><span [innerHtml]="miscService.formatFloatNumber(fundData['Net IRR']==null?'': fundData['Net IRR'])|minusSignToBrackets"></span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="row  mr-0 ml-0 mt-3">
    <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0">
        <div class="performance-header pt-2 pb-2">
            Fund Performance
        </div>
    </div>
    <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 outer-section pl-0 pr-0">
        <div class="panel panel-default border-0 pt-2 tab-bg">
            <div class="panel-heading">
                <div class="panel-title custom-tabs">
                    <ul class="nav nav-tabs ">
                        <li id="cashflow-tabs"class="nav-item" role="presentation" *ngFor="let tab of fundPerformancetabList;" (click)="selectTab(tab);">
                            <button class="nav-link nav-custom" [ngClass]="tab.active?'active':''" id="home-tab" data-bs-toggle="tab" type="button" role="tab" aria-controls="home" aria-selected="true">
                                {{tab.name}}
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="content-bg">
            <div *ngIf="tabNameFP =='Fund Currency'">
                <div class="col-lg-12 cashflowcol-p">
                    <div class="filter-bg border-bottom">
                        <div class="row mr-0 ml-0">
                            <div class=" col-lg-12 col-md-12 col-sm-12 pl-0 pr-0">
                                <div class="float-left">
                                    <div class="allvalues">All values in: {{fundCurrency}}</div>
                                </div>
                                <div class="pull-right headerfontsize">
                                    <div class="d-inline-block search">
                                        <span class="fa fa-search searchicon"></span>
                                        <input #gb pInputText (input)="dt.filterGlobal($event.target.value, 'contains')" type="text" class="search-text-company fund-currency-text TextTruncate" placeholder="Search" [(ngModel)]="globalFilter">
                                    </div>
                                    <div class="d-inline-block cloud_download">
                                        <div class="d-inline-block" *ngIf="fundPerformanceData.Results != null && fundPerformanceData.Results.length > 0">
                                            <img id="download-kpi"src="assets/dist/images/Cloud-download.svg" class="cursor-filter" title="Export KPI (Excel file)" alt="" (click)="CashflowDataExport(fundPerformanceData.cols,fundPerformanceData.Results,'FundPerformance Fund Currency',fundCurrency);isFundPerformance=true"  />
                                            <span class="excel-load" *ngIf="isFundPerformance">
                                                <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                             </span>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <kendo-grid  [kendoGridBinding]="fundPerformanceData.Results" scrollable="virtual" [rowHeight]="44" [resizable]="true"
                        class="k-grid-border-right-width k-grid-outline-none custom-kendo-cab-table-grid grid-row-no-padding">
                        <kendo-grid-column [width]="300" title="{{col.header}}" *ngFor="let col of frozenCols; index as i">
                            <ng-template kendoGridHeaderTemplate>
                                <span class="S-M TextTruncate">{{col.header}}</span>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-rowData>
                                <div class="TextTruncate cell-padding"  
                                [class.realized]="(col.header =='Company Name' && rowData['isRealizedValue'])" 
                                [class.unrealized]="(col.header =='Company Name' && !rowData['isRealizedValue']&&rowData['name']!='Total Realized')" 
                                [class.expenses]="(col.header =='Company Name' && rowData['isExpense'])" 
                                [class.font-weight-bold]="rowData['isTotal']"
                                [class.totalRealized]="(col.header =='Company Name' && rowData['name']=='Total Realized')"
                                >
                                <span class="TextTruncate" [title]="rowData[col.field]">{{rowData[col.field]}}</span>
                                </div>
                            </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column [width]="200" title="{{col.header}}" *ngFor="let col of fundPerformanceData.cols;">
                            <ng-template kendoGridHeaderTemplate>
                                <span class="S-M TextTruncate">{{col.header}}</span>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-rowData >
                                <div class="TextTruncate cell-padding" 
                                [class.realized]="(col.header =='Company Name' && rowData['isRealizedValue'])" 
                                [class.unrealized]="(col.header =='Company Name' && !rowData['isRealizedValue']&&rowData['name']!='Total Realized')" 
                                [class.expenses]="(col.header =='Company Name' && rowData['isExpense'])" 
                                [class.font-weight-bold]="rowData['isTotal']"
                                [class.totalRealized]="(col.header =='Company Name' && rowData['name']=='Total Realized')">
                                <span *ngIf="col.header =='Gross IRR'" [innerHtml]="miscService.formatFloatNumber(rowData[col.field]==null?'': rowData[col.field])|minusSignToBrackets"></span>
                                <span *ngIf="col.header !=='Gross IRR'&&col.header !='Gross TVPI'">{{rowData[col.field]}}</span>
                                <span *ngIf="col.header =='Gross TVPI'">{{rowData[col.field]+'x'}}</span>
                                </div>
                            </ng-template>
                        </kendo-grid-column>
                        <ng-template kendoGridNoRecordsTemplate>
                            <app-empty-state class="finacials-beta-empty-state"
                                [isGraphImage]="false"></app-empty-state>
                        </ng-template>
                    </kendo-grid>
                </div>
            </div>
            <div *ngIf="tabNameFP =='Reporting Currency'">
                <div class="col-lg-12 cash-col-p">
                    <div class="filter-bg border-bottom">
                        <div class="row mr-0 ml-0">
                            <div class=" col-lg-12 col-md-12 col-sm-12 pl-0 pr-0">
                                <div class="pull-right headerfontsize">
                                    <div class="d-inline-block search">
                                        <span class="fa fa-search searchicon"></span>
                                        <input #gb pInputText (input)="dt.filterGlobal($event.target.value, 'contains')" type="text" class="TextTruncate search-text-company fund-currency-text" placeholder="Search" [(ngModel)]="globalFilter">
                                    </div>
                                    <div class="d-inline-block cloud_download">
                                        <div class="d-inline-block" *ngIf="fundPerformanceReportingData != null && fundPerformanceReportingData.length > 0">
                                           
                                            <img src="assets/dist/images/Cloud-download.svg" class="cursor-filter" title="Export KPI (Excel file)" alt="" (click)="CashflowDataExport(fundPerformanceData.cols,fundPerformanceReportingData,'FundPerformance Reporting Currency','');isFundPerformance=true"  />
                                            <span class="excel-load" *ngIf="isFundPerformance">
                                                <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                             </span>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <kendo-grid id="cashflowFundPerformance2" [kendoGridBinding]="fundPerformanceReportingData" scrollable="virtual"
                        [rowHeight]="44" [resizable]="true"
                        class="k-grid-border-right-width k-grid-outline-none custom-kendo-cab-table-grid grid-row-no-padding">
                        <kendo-grid-column [width]="300" title="{{col.header}}" *ngFor="let col of frozenCols; index as i">
                            <ng-template kendoGridHeaderTemplate>
                                <span class="S-M TextTruncate">{{col.header}}</span>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-rowData>
                                <div class="TextTruncate cell-padding"
                                    [class.realized]="(col.header =='Company Name' && rowData['isRealizedValue'])"
                                    [class.unrealized]="(col.header =='Company Name' && !rowData['isRealizedValue'])"
                                    [class.expenses]="(col.header =='Company Name' && rowData['isExpense'])"
                                    [class.font-weight-bold]="rowData['isTotal']">
                                    <span [title]="rowData[col.field]">{{rowData[col.field]}}</span>
                                </div>
                            </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column [width]="200" title="{{col.header}}" *ngFor="let col of fundPerformanceReportingCols;">
                            <ng-template kendoGridHeaderTemplate>
                                <span class="S-M TextTruncate">{{col.header}}</span>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-rowData>
                                <div class="TextTruncate cell-padding"
                                    [class.realized]="(col.header =='Company Name' && rowData['isRealizedValue'])"
                                    [class.unrealized]="(col.header =='Company Name' && !rowData['isRealizedValue'])"
                                    [class.expenses]="(col.header =='Company Name' && rowData['isExpense'])"
                                    [class.font-weight-bold]="rowData['isTotal']">
                                    <span *ngIf="col.header =='Gross IRR'"
                                        [innerHtml]="miscService.formatFloatNumber(rowData[col.field]==null?'': rowData[col.field])+'%'|minusSignToBrackets | formatNumbers"></span>
                                    <span *ngIf="col.header !=='Gross IRR'">{{rowData[col.field] |minusSignToBrackets |
                                        formatNumbers}}</span>
                                </div>
                            </ng-template>
                        </kendo-grid-column>
                        <ng-template kendoGridNoRecordsTemplate>
                            <app-empty-state class="finacials-beta-empty-state" [isGraphImage]="false"></app-empty-state>
                        </ng-template>
                    </kendo-grid>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row mr-0 ml-0">
<div class="col-md-12 pr-0 pl-0">
    <div class="pt-3">
        <span class="rLegend"></span>
        <span class="pl-2 pr-3">Realised Investments</span>
        <span class="urLegend"></span><span class="pl-2 pr-3">Unrealised Investments</span>
        <span *ngIf="tabNameFP !=='Reporting Currency'" class="urLegend others"></span><span *ngIf="tabNameFP !=='Reporting Currency'"  class="pl-2">Others</span>
    </div>
</div>
</div>



<div class="row  mr-0 ml-0 mt-3">
    <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0">
        <div class="performance-header pt-2 pb-2">
            Fund Cashflow
        </div>
    </div>
    <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 outer-section pl-0 pr-0">
        <div class="panel panel-default border-0 pt-2 tab-bg">
            <div class="panel-heading">
                <div class="panel-title custom-tabs">
                    <ul class="nav nav-tabs ">
                        <li id="cashflow-selected-tab" class="nav-item" role="presentation" *ngFor="let tab of tabList;" (click)="selectTab(tab);">
                            <button class="nav-link nav-custom" [ngClass]="tab.active?'active':''" id="home-tab" data-bs-toggle="tab" type="button" role="tab" aria-controls="home" aria-selected="true">
                                {{tab.name}}
                            </button>
                        </li>
                        <li class="fundCashflowTypes">
                            <kendo-combobox [clearButton]="false" [(ngModel)]="selectedFundcashflowType.name" [fillMode]="'flat'"
                                [filterable]="true" name="fundcashflowTypes" 
                                class="k-dropdown-width-270 k-select-medium k-select-flat-custom k-dropdown-height-32" [size]="'medium'"
                                [data]="fundcashflowTypes" [filterable]="false" textField="name" valueField="name"
                                [placeholder]="'Select Cashflow Type'" [valuePrimitive]="true" (valueChange)="onSelectfundcashflowTypes($event)">
                            </kendo-combobox>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="content-bg">
            <div *ngIf="tabName=='Fund Currency'">
                <div class="col-lg-12 cashflow-padding">
                    <div class="filter-bg border-bottom">
                        <div class="row mr-0 ml-0">
                            <div class=" col-lg-12 col-md-12 col-sm-12 pl-0 pr-0">
                                <div class="float-left">
                                    <div class="allvalues">All values in: {{fundCurrency}}</div>
                                </div>
                                <div class="pull-right headerfontsize">
                                    <div class="d-inline-block search">
                                        <span class="fa fa-search searchicon"></span>
                                        <input #gb pInputText (input)="dt.filterGlobal($event.target.value, 'contains')" type="text" class="TextTruncate search-text-company fund-currency-text" placeholder="Search" [(ngModel)]="globalFilter">
                                    </div>
                                    <div class="d-inline-block cloud_download">
                                        <div class="d-inline-block" *ngIf="FundCashflowData.Results != null && FundCashflowData.Results.length > 0">
                                            <img src="assets/dist/images/Cloud-download.svg" class="cursor-filter" title="Export KPI (Excel file)" alt="" (click)="CashflowDataExport(FundCashflowData.cols,FundCashflowData.Results,'FundCashflow Fund Currency',fundCurrency);isFundCashFlow=true" />
                                            <span class="excel-load" *ngIf="isFundCashFlow">
                                                <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                             </span>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <kendo-grid id="cashflowFundCashflow1" [kendoGridBinding]="FundCashflowData.Results" scrollable="virtual"
                    [rowHeight]="44" [resizable]="true"
                    class="k-grid-border-right-width k-grid-outline-none custom-kendo-cab-table-grid grid-row-no-padding">
                    <kendo-grid-column [width]="200" title="{{col.header}}" *ngFor="let col of FundCashflowData.cols;">
                        <ng-template kendoGridHeaderTemplate>
                            <span class="S-M TextTruncate">{{col.header}}</span>
                        </ng-template>
                        <ng-template kendoGridCellTemplate let-rowData>
                            <div class="TextTruncate cell-padding" [class.realized]="(col.header =='Company Name' && rowData['isRealizedValue'])" [class.unrealized]="(col.header =='Company Name' && !rowData['isRealizedValue'])" [class.expenses]="(col.header =='Company Name' && rowData['isExpense'])" id="{{col.header}}_{{ri+1}}">
                                <span *ngIf="col.header!='Transaction Type'"  [innerHtml]="miscService.formatFloatNumber(rowData[col.field]==null?'': rowData[col.field])|minusSignToBrackets" [title]="col.header =='Company Name'?rowData[col.field]:''"></span>
                                <span *ngIf="col.header=='Transaction Type'" [innerHtml]="pageConfigTransactionTypes|matchObject:rowData[col.field]:'displayName':'name'" [title]="pageConfigTransactionTypes|matchObject:rowData[col.field]:'displayName':'name'"></span>
                            </div>
                        </ng-template>
                    </kendo-grid-column>
                    <ng-template kendoGridNoRecordsTemplate>
                        <app-empty-state class="finacials-beta-empty-state" [isGraphImage]="false"></app-empty-state>
                    </ng-template>
                </kendo-grid>
                </div>
            </div>
            <div *ngIf="tabName=='Reporting Currency'">
                <div class="col-lg-12 cashflow-pad">
                    <div class="filter-bg border-bottom">
                        <div class="row mr-0 ml-0">
                            <div class=" col-lg-12 col-md-12 col-sm-12 pl-0 pr-0">
                                <div class="pull-right headerfontsize">
                                    <div class="d-inline-block search">
                                        <span class="fa fa-search searchicon"></span>
                                        <input #gb pInputText (input)="dt.filterGlobal($event.target.value, 'contains')" type="text" class="TextTruncate search-text-company fund-currency-text" placeholder="Search" [(ngModel)]="globalFilter">
                                    </div>
                                    <div class="d-inline-block cloud_download" *ngIf="FundReportingCashflowData.Results != null != null && FundReportingCashflowData?.Results?.length > 0 && this.selectedFundcashflowType?.name!='Others'">
                                        <div class="d-inline-block" >
                                            <img src="assets/dist/images/Cloud-download.svg" class="cursor-filter" title="Export KPI (Excel file)" alt="" (click)="CashflowDataExport(FundReportingCashflowData.cols,FundReportingCashflowData.Results,'FundCashflow Reporting Currency','');isFundCashFlow=true" />
                                      <span class="excel-load" *ngIf="isFundCashFlow">
                                            <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                         </span>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <kendo-grid id="cashflowFundCashflow2" [kendoGridBinding]="FundReportingCashflowData.Results" scrollable="virtual"  *ngIf="FundReportingCashflowData.Results!=null && FundReportingCashflowData.Results.length>0 && this.selectedFundcashflowType?.name!='Others'"
                    [rowHeight]="44" [resizable]="true"
                    class="k-grid-border-right-width k-grid-outline-none custom-kendo-cab-table-grid grid-row-no-padding">
                    <kendo-grid-column [width]="200"  title="{{col.header}}" *ngFor="let col of FundReportingCashflowData.cols;">
                        <ng-template kendoGridHeaderTemplate>
                            <span class="S-M TextTruncate">{{col.header}}</span>
                        </ng-template>
                        <ng-template kendoGridCellTemplate let-rowData>
                            <div class="TextTruncate cell-padding" [class.realized]="(col.header =='Company Name' && rowData['isRealizedValue'])" [class.unrealized]="(col.header =='Company Name' && !rowData['isRealizedValue'])" id="{{col.header}}_{{ri+1}}">
                                <span *ngIf="col.header!='Transaction Type'" [innerHtml]="miscService.formatFloatNumber(rowData[col.field]==null?'': rowData[col.field])|minusSignToBrackets" [title]="col.header =='Company Name'?rowData[col.field]:''"></span>
                                <span *ngIf="col.header=='Transaction Type'" [innerHtml]="pageConfigTransactionTypes|matchObject:rowData[col.field]:'displayName':'name'" [title]="pageConfigTransactionTypes|matchObject:rowData[col.field]:'displayName':'name'"></span>
                            </div>
                        </ng-template>
                    </kendo-grid-column>
                    <ng-template kendoGridNoRecordsTemplate>
                        <app-empty-state class="finacials-beta-empty-state" [isGraphImage]="false"></app-empty-state>
                    </ng-template>
                </kendo-grid>
                    <div class="zero-state-rp" *ngIf="FundReportingCashflowData.Results.length==0 || this.selectedFundcashflowType?.name=='Others'"><zero-state-kpi ></zero-state-kpi>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<div class="row mr-0 ml-0 mb-4">
<div class="col-md-12 pr-0 pl-0">
    <div class="pt-3 mb-3">
        <span class="rLegend"></span>
        <span class="pl-2 pr-3">Realised Investments</span>
        <span class="urLegend"></span><span class="pl-2 pr-3">Unrealised Investments</span>
        <span class="urLegend others"></span><span class="pl-2">Others</span>
    </div>
</div>
</div>
<app-loader-component *ngIf="isLoading"></app-loader-component>