import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA, NgZone } from '@angular/core';
import { PortfolioCompanyDraftService } from 'src/app/services/portfolio-company-draft.service';
import { PageConfigurationService } from 'src/app/services/page-configuration.service';
import { Router } from '@angular/router';
import { AppSettingService } from 'src/app/services/appsettings.service';
import { FormsModule } from '@angular/forms';
import { PortfolioCompanyDraftComponent } from './portfolio-company-draft-list.component';
import { of, throwError } from 'rxjs';

describe('PortfolioCompanyDraftComponent', () => {
  let component: PortfolioCompanyDraftComponent;
  let fixture: ComponentFixture<PortfolioCompanyDraftComponent>;

  beforeEach(() => {
    const portfolioCompanyDraftServiceStub = () => ({
      getPortfolioCompanyDraftList: event => ({ subscribe: f => of({}) })
    });
    const pageConfigurationServiceStub = () => ({
      getPageConfiguration: () => ({ subscribe: f => of({}) })
    });
    const routerStub = () => ({ navigate: (array, object) => ({}) });
    const appSettingServiceStub = () => ({ getConfig: () => ({}) });
    TestBed.configureTestingModule({
      imports: [FormsModule],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [PortfolioCompanyDraftComponent],
      providers: [
        {
          provide: PortfolioCompanyDraftService,
          useFactory: portfolioCompanyDraftServiceStub
        },
        {
          provide: PageConfigurationService,
          useFactory: pageConfigurationServiceStub
        },
        { provide: Router, useFactory: routerStub },
        { provide: AppSettingService, useFactory: appSettingServiceStub }
      ]
    });
    fixture = TestBed.createComponent(PortfolioCompanyDraftComponent);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  it(`isLoader has default value`, () => {
    expect(component.isLoader).toEqual(false);
  });

  it(`pcDraftListHeaderData has default value`, () => {
    expect(component.pcDraftListHeaderData).toEqual([]);
  });

  it(`pcDraftListData has default value`, () => {
    expect(component.pcDraftListData).toEqual([]);
  });

  it(`masterSectionList has default value`, () => {
    expect(component.masterSectionList).toEqual([]);
  });

  it(`tablePosition has default value`, () => {
    expect(component.tablePosition).toEqual(`unset`);
  });

  it('#ngOnInit should initialize component', () => {
    component.paginationFilterDraftClone = {};
    component.pageNumber = 1;
    component.RecordsPerPage = 10;

    spyOn(component, 'getConfigurationList');
    spyOn(component, 'getWidth');
    component.ngOnInit();

    const expectedEvent = {
      PageNumber: 1,
      RecordsPerPage: 10,
      SearchText: ''
    };

    expect(component.getConfigurationList).toHaveBeenCalledWith(expectedEvent);
    expect(component.getWidth).toHaveBeenCalled();
  });

  it('#searchDraftList should search draft list', () => {
    component.paginationFilterDraftClone = {};
    component.RecordsPerPage = 10;
    component.filterSearch = 'test';

    spyOn(component, 'getPcDraftList');
    component.searchDraftList();

    const expectedEvent = {
      PageNumber: 1,
      RecordsPerPage: 10,
      SearchText: 'test'
    };

    expect(component.searching).toBe(true);
    expect(component.tablePosition).toBe('unset');
    expect(component.getPcDraftList).toHaveBeenCalledWith(expectedEvent);
  });

  xit('#getWidth should set panelWidth based on window innerWidth', () => {
    const windowSpy = spyOnProperty(window, 'innerWidth').and.returnValue(500);
    component.getWidth();
    const expectedWidth = (((500 - 108) - 16 * 5) / 4) + 16 + "px";
    expect(component.panelWidth).toEqual(expectedWidth);
  });

  it('#mouseEnter should set toolTipString based on data', () => {
    const data = {
      workFlowSubfeatureModel: [
        { displayName: 'test1' },
        { displayName: 'test2' }
      ]
    };

    component.mouseEnter(data);

    const expectedString = '<ul style="padding-left:10px;margin-bottom: 0px !important;"><li>test1</li><li>test2</li></ul>';
    expect(component.toolTipString).toEqual(expectedString);
  });

  it('#mouseEnter should set toolTipString to empty string if data is empty', () => {
    const data = {};

    component.mouseEnter(data);

    expect(component.toolTipString).toEqual('');
  });

  xit('#draftHeaderDataFactory should process rawData and columnCount', () => {
    const rawData = [
      { statusId: 1 },
      { statusId: 2 }
    ];
    const columnCount = [
      { statusId: 1, count: 10 },
      { statusId: 2, count: 20 }
    ];

    component.draftHeaderDataFactory(rawData, columnCount);

    const expectedData = [
      { statusId: 1, totalRecord: 10 },
      { statusId: 2, totalRecord: 20 },
      {}, {}
    ];
    expect(component.pcDraftListHeaderData).toEqual(expectedData);
  });

  it('#getPcDraftListPageWise should handle error', (done) => {
    const event = {};
    const portfolioCompanyDraftService = TestBed.inject(PortfolioCompanyDraftService);
    spyOn(portfolioCompanyDraftService, 'getPortfolioCompanyDraftList').and.returnValue(throwError(() => 'error'));

    component.getPcDraftListPageWise(event);

    setTimeout(() => {
      expect(component.isLoader).toBeFalse();
      done();
    });
  });

  it('#getPcDraftList should process event and update data', (done) => {
    const event = {};
    const result = {
      code: 'OK',
      body: {
        columns: [],
        statuscount: [],
        draftData: []
      }
    };
    const portfolioCompanyDraftService = TestBed.inject(PortfolioCompanyDraftService);
    spyOn(component, 'draftHeaderDataFactory');
    spyOn(portfolioCompanyDraftService, 'getPortfolioCompanyDraftList').and.returnValue(of(result));

    component.getPcDraftList(event);

    setTimeout(() => {
      expect(component.isLoader).toBeFalse();
      expect(component.draftHeaderDataFactory).toHaveBeenCalledWith(result.body.columns, result.body.statuscount);
      expect(component.pcDraftListData).toEqual([result.body.draftData]);
      expect(component.tablePosition).toEqual('sticky');
      done();
    });
  });

  it('#getPcDraftList should handle error', (done) => {
    const event = {};
    const portfolioCompanyDraftService = TestBed.inject(PortfolioCompanyDraftService);
    spyOn(portfolioCompanyDraftService, 'getPortfolioCompanyDraftList').and.returnValue(throwError(() =>'error'));

    component.getPcDraftList(event);

    setTimeout(() => {
      expect(component.isLoader).toBeFalse();
      done();
    });
  });

  it('#getSubpageList should process pageConfigData and update masterSectionList', () => {
    const pageConfigData = [
      { name: 'Static Information' },
      { name: 'TradingRecords' },
      { name: 'Nonexistent' }
    ];

    component.getSubpageList(pageConfigData);

    const expectedData = [
      { name: 'Static Information', source: 'Static Data Business Desciption Investment Professional', target: 'Static Information' },
      { name: 'TradingRecords', source: 'Trading Records', target: 'TradingRecords' }
    ];
    expect(component.masterSectionList).toEqual(expectedData);
  });

  
  it('#parseJsonResponse should process result and return finalPCSections', () => {
    const result = [
      {
        isActive: true,
        name: 'Portfolio Company',
        subPageDetailList: [
          {
            isActive: true,
            name: 'Static Information',
            displayName: 'Static Information',
            id: 1,
            subPageFieldList: []
          },
          {
            isActive: true,
            name: 'Key Performance Indicator',
            displayName: 'Key Performance Indicator',
            id: 2,
            subPageFieldList: [
              {
                isActive: true,
                name: 'KPI 1',
                displayName: 'KPI 1',
                id: 3
              }
            ]
          }
        ]
      }
    ];

    const finalPCSections = component.parseJsonResponse(result);

    const expectedData = [
      { id: 1, name: 'Static Information', displayName: 'Static Information' },
      { id: 3, name: 'KPI 1', displayName: 'KPI 1' }
    ];
    expect(finalPCSections).toEqual(expectedData);
  });

  it('#getPageConfiguration should process result and resolve with finalPCSections', (done) => {
    const result = [];
    const finalPCSections = [];
    const pageConfigurationService = TestBed.inject(PageConfigurationService);
    spyOn(pageConfigurationService, 'getPageConfiguration').and.returnValue(of(result));
    spyOn(component, 'parseJsonResponse').and.returnValue(finalPCSections);

    component.getPageConfiguration().then(data => {
      expect(data).toBe(finalPCSections);
      expect(component.parseJsonResponse).toHaveBeenCalledWith(result);
      done();
    });
  });

});
