<div class="clo-page-header">
  <div class="clo-container">
    <div class="card clo-summary-table-container">
      <div class="card-header text-#1A1A1A clo-summary-title">
        <span
          ><h3 class="header-title">
            {{ CLOModel.issuer }} ({{ CLOModel.companyName }})
          </h3></span
        >
      </div>
      <div class="card-body">
        <!-- Tabbed Navigation -->
        <ul class="nav nav-tabs tab-background-color" id="tabMenu" >
          <li class="nav-item" *ngFor="let tab of tabs">
            <a
              id="tab-clo-summary"
              class="nav-link"            
              [ngClass]="{'selected-clo-tab tab-background-color': activeTab === tab.tabId, 'unselected-tab-background': activeTab !== tab.tabId}"
              data-toggle="tab"
              (click)="setActiveTab(tab)"
            >
              {{tab.aliasName}}</a
            >
          </li>
        </ul>
        <div class="tab-content mt-4">
          <!-- CLO Summary Tab -->
        <ng-container [ngSwitch]="activeTab">
        <div *ngFor="let table of selectedTabData?.tableList;let i=index">
          <div  *ngIf="canViewCLOSummary && table.tableId==23 && table.isShow" >
            <div class="company-facts-glo-container" >
              <div class="company-facts">
                <span class="company-facts-section">{{table.aliasName}}</span>
                <span class="edit-icon" id="edit-clo-summary" (click)="redirectToEditClo()" (keypress)="redirectToEditClo()">
                  <img alt="" src="assets/dist/images/clo_edit.svg" />
                </span>
              </div>
            
            <div [ngClass]="{'clo-summary-table': i > 0, '': i == 0}">
              <div class="table-layout-fixed p-4">
                <div class="row">
                  <div class="col-3 clo-cell-padding cell-height Body-R text-color">Issuer</div>
                  <div class="col-3 clo-cell-padding cell-height Body-B text-color" [title]="CLOModel.issuer">{{ CLOModel.issuer }}</div>
                  <div class="col-3 clo-cell-padding cell-height Body-R text-color">Last Refi Date</div>
                  <div class="col-3 clo-cell-padding cell-height Body-B text-color">{{ CLOModel.lastRefiDate || "N/A" }}</div>
                </div>
                <div class="row">
                  <div class="col-3 clo-cell-padding cell-height Body-R text-color">Arranger</div>
                  <div class="col-3 clo-cell-padding cell-height Body-B text-color" [title]="CLOModel.arranger">{{ CLOModel.arranger || "N/A" }}</div>
                  <div class="col-3 clo-cell-padding cell-height Body-R text-color">Last Reset Date</div>
                  <div class="col-3 clo-cell-padding cell-height Body-B text-color">{{ CLOModel.lastResetDate || "N/A" }}</div>
                </div>
                <div class="row">
                  <div class="col-3 clo-cell-padding cell-height Body-R text-color">Trustee</div>
                  <div class="col-3 clo-cell-padding cell-height Body-B text-color" [title]="CLOModel.trustee">{{ CLOModel.trustee || "N/A" }}</div>
                  <div class="col-3 clo-cell-padding cell-height Body-R text-color">Last Refi/Reset Arranger</div>
                  <div class="col-3 clo-cell-padding cell-height Body-B text-color">{{ CLOModel.lastRefiResetArranger || "N/A" }}</div>
                </div>
                <div class="row">
                  <div class="col-3 clo-cell-padding cell-height Body-R text-color">Priced</div>
                  <div class="col-3 clo-cell-padding cell-height Body-B text-color">{{ CLOModel.priced || "N/A" }}</div>
                  <div class="col-3 clo-cell-padding cell-height Body-R text-color">Call End Date</div>
                  <div class="col-3 clo-cell-padding cell-height Body-B text-color">{{ CLOModel.callEndDate || "N/A" }}</div>               
                </div>
                <div class="row">
                  <div class="col-3 clo-cell-padding cell-height Body-R text-color">Closed</div>
                  <div class="col-3 clo-cell-padding cell-height Body-B text-color">{{ CLOModel.closed || "N/A" }}</div>
                  <div class="col-3 clo-cell-padding cell-height Body-R text-color">Original End of Reinvestment Date</div>
                  <div class="col-3 clo-cell-padding cell-height Body-B text-color">{{ CLOModel.originalEndOfReinvestmentDate || "N/A" }}</div>                
                </div>
                <div class="row">
                  <div class="col-3 clo-cell-padding cell-height Body-R text-color">Current Maturity Date</div>
                  <div class="col-3 clo-cell-padding cell-height Body-B text-color">{{ CLOModel.currentMaturityDate || "N/A" }}</div>
                  <div class="col-3 clo-cell-padding cell-height Body-R text-color">Current End of Reinvestment Date</div>
                  <div class="col-3 clo-cell-padding cell-height Body-B text-color">{{ CLOModel.currentEndOfReinvestmentDate || "N/A" }}</div>
                </div>
              </div>
            </div>
          </div>
          </div>
          <div class="company-facts-glo-container" *ngIf="table.tableId!=23 && table.isShow">
            <app-flat-table [isShouldFetchDataOnLoad]="true"
            [companyID]="uniqueId"
            [tableTitle]="table.aliasName"
            [tableName]="table.tableName"
            [isStaticTable] = "table.isStaticTable"
            [tableId]="table.tableId"
            [canImport] = "checkTablePermissions(table.tableId,CAN_IMPORT)"
            [canExport] = "checkTablePermissions(table.tableId,CAN_EXPORT)"
            [canEdit] = "checkTablePermissions(table.tableId,CAN_EDIT)"
            [isCLO]="true">
            </app-flat-table>
          </div>                        
        </div>
          <div class="clo-table-body col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pl-0 pr-0 Heading2-M" *ngSwitchCase="TAB_NAMES.CLO_PERFORMANCE_INDICATOR">
            <div class="row mr-0 ml-0 clo-table-content">
              <kendo-tabstrip #tabStrip id="{{activePerformanceIndicatorTab}}" [ngModel]="activePerformanceIndicatorTab" (tabSelect)="onTabSelect($event)">
                <ng-container *ngFor="let tab of CloPerformanceIndicatorTabs">
                    <kendo-tabstrip-tab  class="pr-0 pl-0" *ngIf="checkTabPermission(tab.tabId)" [title]="tab.aliasName" [selected]="CloPerformanceIndicatorTabs[0] === tab" >
                    <ng-template kendoTabContent>
                      <div *ngIf="activePerformanceIndicatorTab!=TAB_NAMES.CLO_Tests">
                        <app-flat-table *ngIf="checkTablePermissions(tab.tableList[0].tableId)" [isStaticTable]="tab.tableList[0].isStaticTable"
                                      [companyID]="uniqueId"
                                      [tableType]="tab.tableList[0].tableType"
                                      [tableTitle]="tab.tableList[0].aliasName"
                                      [tableName]="tab.tableList[0].tableName"
                                      [isCompositeRowFilterRequired]="true"
                                      [cloId]="uniqueId"
                                      [cloDomcile]="CLOModel.domicile"
                                      [tableId]="tab.tableList[0].tableId"
                                      [canImport] = "checkTablePermissions(tab.tableList[0].tableId,CAN_IMPORT)"
                                      [canExport] = "checkTablePermissions(tab.tableList[0].tableId,CAN_EXPORT)"
                                      [canEdit] = "checkTablePermissions(tab.tableList[0].tableId,CAN_EDIT)"
                                      [isCLO]="true">
                      </app-flat-table>
                      </div>
                      <div *ngIf="activePerformanceIndicatorTab==TAB_NAMES.CLO_Tests">
                        <div *ngFor="let table of tab.tableList; let i=index">
                          <div [ngClass]="{'company-facts-glo-container': i > 0, '': i == 0}">
                            <app-flat-table *ngIf="checkTablePermissions(table.tableId)" [isShouldFetchDataOnLoad]="true"
                            [companyID]="uniqueId"
                            [tableTitle]="table.aliasName"
                            [tableName]="table.tableName"
                            [tableId]="table.tableId"
                            [canImport] = "checkTablePermissions(table.tableId,CAN_IMPORT)"
                            [canExport] = "checkTablePermissions(table.tableId,CAN_EXPORT)"
                            [canEdit] = "checkTablePermissions(table.tableId,CAN_EDIT)"
                            [isCLO]="true">
                            </app-flat-table>
                          </div>                        
                        </div>
                        
                      </div>
                    </ng-template>
                    </kendo-tabstrip-tab>
                </ng-container>
              </kendo-tabstrip>            
            </div>
          </div>  
        </ng-container>
      </div>
    </div>
  </div>
  </div>
  </div>
  <app-loader-component *ngIf="isLoading"></app-loader-component>