<div class="nep-modal nep-modal-show custom-modal" style="display: block; background: rgba(0, 0, 0, 0.25);">
    <div class="nep-modal-mask"></div>
    <div [style.width]="customwidth" class="nep-card nep-card-shadow nep-modal-panel nep-modal-default" style="position: relative; display: inline-flex; top: 35%;">
        <div class="nep-card-header nep-modal-title">
            {{modalTitle}}
        </div>
        <div class="nep-card-body">
            <ng-content></ng-content>
        </div>
        <div class="nep-card-footer nep-card-right nep-modal-footer">
            <div *ngIf="!IsInfoPopup">
                <nep-button Type="Secondary" (click)="onSecondaryEvent()" [Name]="secondaryButtonName">
                    {{secondaryButtonName}}
                </nep-button>
                <nep-button Type="Primary" [disabled]="disablePrimaryButton" style="padding-left:12px" (click)="onPrimaryEvent()" [Name]="primaryButtonName">
                    {{primaryButtonName}}
                </nep-button>
            </div>
            <div *ngIf="IsInfoPopup">
                <nep-button Type="Primary" style="padding-left:12px" (click)="onPrimaryEvent()" [Name]="primaryButtonName">
                    {{primaryButtonName}}
                </nep-button>
            </div>
        </div>
    </div>
</div>