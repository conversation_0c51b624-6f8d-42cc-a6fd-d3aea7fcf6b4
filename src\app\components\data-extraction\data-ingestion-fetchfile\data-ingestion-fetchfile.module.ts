import { CommonModule } from "@angular/common";
import { HTTP_INTERCEPTORS, HttpClientModule } from "@angular/common/http";
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import { AngularResizeEventModule } from "angular-resize-event";
import { KendoModule } from "src/app/custom-modules/kendo.module";
import { SharedComponentModule } from "src/app/custom-modules/shared-component.module";
import { SharedDirectiveModule } from "src/app/directives/shared-directive.module";
import { HttpServiceInterceptor } from "src/app/interceptors/http-service-interceptor";
import { DataIngestionService } from "src/app/services/data-ingestion.service";
import { DataIngestionFetchFileComponent } from "./data-ingestion-fetchfile.component";
import { PdfPreviewComponent } from './pdf-preview/pdf-preview.component';
import { ExtractionSharedService } from "src/app/services/extraction-shared.service";
import { NumbersCommaOnlyDirective } from "./number-only-directive";
import { ExtractionIngestionService } from "src/app/services/extraction-ingestion.service";
import { RepositoryConfigService } from "src/app/services/repository.config.service";

@NgModule({
  imports: [
    CommonModule,
    HttpClientModule,
    SharedComponentModule,
    KendoModule,
    SharedDirectiveModule,
    ReactiveFormsModule,
    FormsModule,
    AngularResizeEventModule,
    RouterModule.forChild([
      { path: "", component: DataIngestionFetchFileComponent },
    ]),
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [DataIngestionFetchFileComponent, PdfPreviewComponent,NumbersCommaOnlyDirective],
  providers: [
    DataIngestionService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpServiceInterceptor,
      multi: true,
    },
    ExtractionSharedService,
    ExtractionIngestionService,
    RepositoryConfigService
  ],
})
export class DataIngestionFetchFileModule {}
