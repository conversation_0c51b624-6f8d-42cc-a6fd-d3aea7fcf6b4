import { trigger, state, style, transition, animate } from '@angular/animations';

export const fadeAnimation = trigger('fadeAnimation', [
  state('void', style({
    opacity: 0
  })),
  transition('void <=> *', animate('150ms ease-in')),
]);

export const slideAnimation = trigger('slideAnimation', [
  state('void', style({
    transform: 'translateX(100%)'
  })),
  state('*', style({
    transform: 'translateX(0)'
  })),
  transition('void <=> *', animate('200ms ease-in-out')),
]);
