<div class="row request-config-section mr-0 ml-0">
  <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 config-header">
    <div class="float-left">
      <kendo-textbox  (input)="filterGrid($event.target.value)" class="custom-kendo-text custom-search"  selectOnFocus="false" placeholder="Search files & keywords...">
        <ng-template kendoTextBoxSuffixTemplate>
          <button class="kendo-text-button" kendoButton [svgIcon]="searchSVG"></button>
        </ng-template>
      </kendo-textbox>
    </div>
    <div class="float-right">
      <button kendoButton [routerLink]="['/request-configuration-details/0']" [svgIcon]="plusSVG" themeColor="primary">
        Create Request
      </button>
    </div>
  </div>
  <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
    <kendo-grid [data]="configRequest"  [filter]="gridFilter" [pageSize]="state.take" [skip]="state.skip" [sortable]="true" [sort]="sort"
       
     class="custom-kendo-list-grid k-grid-border-right-width k-grid-outline-none" scrollable="scrollable">
      <kendo-grid-column *ngFor="let columns of requestConfigColumns" field="{{ columns.field }}">
        <ng-template kendoGridHeaderTemplate>
          <div class="header-icon-wrapper wd-98">
            <span class="TextTruncate S-M" [title]="columns.header">
              {{ columns.header }}
            </span>
          </div>
        </ng-template>
        <ng-template *ngIf="columns.field != 'createdBy'; else createdByTemplate" kendoGridCellTemplate let-rowData let-column="column">
          <div class="content"  [title]="rowData[column.field]">{{ rowData[column.field] }} </div>
        </ng-template>
        <ng-template #createdByTemplate kendoGridCellTemplate let-rowData>
          <div title="{{rowData.createdBy}}">
            <div class="text-above-image XS-R">
              {{getInitials(rowData.createdBy)}}
            </div>
            <img src="assets/dist/images/profile-icon.svg" alt="text-image" />
          </div>
        </ng-template>
        <ng-template kendoGridCellTemplate let-rowData let-column="column">
          <div class="content"  [title]="rowData[column.field]">{{ rowData[column.field] }} </div>
        </ng-template>
      </kendo-grid-column>

      <ng-template kendoGridNoRecordsTemplate>
        <app-empty-state class="finacials-beta-empty-state custom-txtclr" [imageHeight]="'76vh'"
          [isGraphImage]="false" 
          [isMessageHtml]="true" [message]="'No request found.<br>Click on “Create Request” button to start creating requests'"></app-empty-state>
      </ng-template>
    </kendo-grid>
    
  </div>
</div>