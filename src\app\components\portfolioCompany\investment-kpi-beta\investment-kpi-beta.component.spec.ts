import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InvestmentKpiBetaComponent } from './investment-kpi-beta.component';
import { PortfolioCompanyService } from '../../../services/portfolioCompany.service';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { FormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import {  FinancialValueUnitsEnum, MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { of } from 'rxjs';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { OidcAuthService } from "src/app/services/oidc-auth.service";
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { PeriodType, InvestmentKpiConstants } from 'src/app/common/constants';
import { KPIModulesEnum, PermissionService } from 'src/app/services/permission.service';
import { AuditService } from "src/app/services/audit.service";
import { Audit, MappedDocuments } from "src/app/components/file-uploads/kpi-cell-edit/kpiValueModel";
import { SwitchModule } from '@progress/kendo-angular-inputs';
import { DatePipe } from '@angular/common';

describe('InvestmentKpiBetaComponent', () => {
  let component: InvestmentKpiBetaComponent;
  let fixture: ComponentFixture<InvestmentKpiBetaComponent>;
  let toastrService: ToastrService;

  beforeEach(() => {
    const mockPortfolioCompanyServicestub = () => ({
        getfinancialsvalueTypes: object => ({ subscribe: f => f({}) }),
        getInvestmentKPIValues: object => ({subscribe: f => of({}) }),
      });

      const miscServiceStub = () => ({
        createDateFromPeriod: (periodType, startPeriod, endPeriod) => ({})
    });
    const auditServiceStub = () => ({
        auditLog: (auditLog) => ({}),
        getAuditLog: (auditLog) => ({})
    });
    const toastrServiceStub = () => ({
      overlayContainer: {},
      success: (toasterMessage, string, object) => ({}),
      error: (toasterMessage, string, object) => ({})
  });
    const OidcAuthServiceStub = () => ({
      getEnvironmentConfig: () => ({})
    });
 
     TestBed.configureTestingModule({
      imports: [FormsModule, MatMenuModule, PrimeNgModule, HttpClientModule,
        ToastrModule.forRoot(), // import the ToastrModule
        SwitchModule
      ],
      schemas: [
        NO_ERRORS_SCHEMA
      ],
      providers: [
        DatePipe,
        { provide: PortfolioCompanyService, useFactory: mockPortfolioCompanyServicestub },
        { provide: MiscellaneousService, useFactory: miscServiceStub },
        { provide: 'BASE_URL', useValue: 'http://localhost:4200/' },
        { provide: ToastrService, useFactory: toastrServiceStub },
        { provide: ToastrService, useValue: jasmine.createSpyObj('ToastrService', ['success', 'error']) },
        { provide: OidcAuthService, useFactory: OidcAuthServiceStub },
        { provide: AuditService, useFactory: auditServiceStub },
        { provide: PermissionService, useFactory: miscServiceStub },
      ],
      declarations: [InvestmentKpiBetaComponent]
    });
    fixture = TestBed.createComponent(InvestmentKpiBetaComponent);
    component = fixture.componentInstance;
    component.pageConfigData = [
      {
          "kpiConfigurationData": [
              {
                  "sectionID": 13,
                  "fieldID": 15,
                  "aliasName": "Actual",
                  "subPageID": 2,
                  "options": null,
                  "chartValue": [
                      "Monthly",
                      "Quarterly",
                      "Annual"
                  ]
              },
              {
                  "sectionID": 14,
                  "fieldID": 15,
                  "aliasName": "Budget",
                  "subPageID": 2,
                  "options": null,
                  "chartValue": [
                      "Monthly",
                      "Quarterly",
                      "Annual"
                  ]
              },
              {
                  "sectionID": 15,
                  "fieldID": 15,
                  "aliasName": "Forecast",
                  "subPageID": 2,
                  "options": null,
                  "chartValue": [
                      "Monthly",
                      "Quarterly",
                      "Annual"
                  ]
              },
              {
                  "sectionID": 16,
                  "fieldID": 15,
                  "aliasName": "IC",
                  "subPageID": 2,
                  "options": null,
                  "chartValue": [
                      "Annual"
                  ]
              }
          ],
          "hasChart": true,
          "kpiType": "ImpactKPIs"
      }
  ];
  component.tabName = 'Tab1'; // Set the tabName for testing purposes
    component.modelList = {
      portfolioCompanyID: '12345' // Set the portfolioCompanyID for testing purposes
    };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call getInvestmentKPIValues method of portfolioCompanyService', () => {
    // Arrange
    const searchFilter = {};
    const event = {};
    const response = {
        headers: [],
        rows: [],
        companyKpiAuditLog: [],
        isMonthly: false,
        isQuarterly: false,
        isAnnually: false
    };
    const service: PortfolioCompanyService = TestBed.inject(PortfolioCompanyService);
    spyOn(service, 'getInvestmentKPIValues').and.returnValue(of(response));

    // Act
    component.getInvestmentKPIValues(searchFilter, event);

    // Assert
    expect(service.getInvestmentKPIValues).toHaveBeenCalled();
});

it('should set masterKpiValueUnit, searchFilter and call getPortfolioCompanyMasterKPIValues, closeMenu when kpiTable_GlobalFilter is called', () => {
  // Arrange
  const event = {
    UnitType: {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions]
    }
  };
  const expectedMasterKpiValueUnit = event.UnitType;
  spyOn(component, 'getPortfolioCompanyMasterKPIValues');
  spyOn(component.menuTrigger, 'closeMenu');
  // Act
  component.kpiTable_GlobalFilter(event);

  // Assert
  // expect(component.masterKpiValueUnit).toEqual(expectedMasterKpiValueUnit);
  expect(component.searchFilter).toEqual(event);
  expect(component.getPortfolioCompanyMasterKPIValues).toHaveBeenCalledWith(null);
  expect(component.menuTrigger.closeMenu).toHaveBeenCalled();
});

it('should reset properties to initial state when clearData is called', () => {
  // Arrange
  component.loading = true;
  component.isLoader = true;
  component.tableColumns = ['column1', 'column2'];
  component.tableResult = ['result1', 'result2'];
  component.tableResultClone = ['clone1', 'clone2'];
  component.isPageLoad = true;

  // Act
  component.clearData();

  // Assert
  expect(component.loading).toBe(false);
  expect(component.isLoader).toBe(false);
  expect(component.tableColumns).toEqual([]);
  expect(component.tableResult).toEqual([]);
  expect(component.tableResultClone).toEqual([]);
  expect(component.isPageLoad).toBe(false);
});

it('should set filterOptions and call setDefaultTypeTab when SetFilterOptionsKeys is called', () => {
  // Arrange
  const result = 'result';
  const initialFilterOptions: any[] = [];
  component.filterOptions = initialFilterOptions;
  spyOn(component, 'setDefaultTypeTab');

  // Act
  component.SetFilterOptionsKeys(result);

  // Assert
  expect(component.filterOptions).toEqual([]);
  expect(component.setDefaultTypeTab).toHaveBeenCalled();
});

it('should set tabValueTypeList, tabName and call setPeriodsOptions, getPortfolioCompanyMasterKPIValues when selectValueTab is called', () => {
  // Arrange
  const tab: ITab = { name: 'Tab3', active: false }; // Create a new tab that is not in tabValueTypeList
  component.tabValueTypeList = [{ name: 'Tab1', active: true }, { name: 'Tab2', active: true }];
  component.subSectionFields = [];
  spyOn(component, 'setPeriodsOptions');
  spyOn(component, 'getPortfolioCompanyMasterKPIValues');

  // Act
  component.selectValueTab(tab);

  // Assert
  expect(component.tabValueTypeList).toEqual([{ name: 'Tab1', active: false }, { name: 'Tab2', active: false }]); // All tabs in tabValueTypeList should have active = false
  expect(tab.active).toBe(true); // The selected tab should have active = true
  expect(component.tabName).toEqual(tab.name);
  expect(component.setPeriodsOptions).toHaveBeenCalledWith(component.subSectionFields);
  expect(component.getPortfolioCompanyMasterKPIValues).toHaveBeenCalledWith(null);
});

it('should set defaultType when setDefaultTypeTab is called', () => {
  // Arrange
  component.isMonthly = true;
  component.isQuarterly = false;
  const expectedDefaultType = 'Monthly';

  // Act
  component.setDefaultTypeTab();

  // Assert
  expect(component.defaultType).toEqual(expectedDefaultType);
});

it('should set filterOptions, isMonthly, isQuarterly, isAnnually and call setDefaultTypeTab, getPortfolioCompanyMasterKPIValues when onChangePeriodOption is called', () => {
  // Arrange
  const type = { field: 'Type3', key: false }; // Create a new type that is not in filterOptions
  component.filterOptions = [{ field: 'Type1', key: true }, { field: 'Type2', key: true }];
  spyOn(component, 'setDefaultTypeTab');
  spyOn(component, 'getPortfolioCompanyMasterKPIValues');

  // Act
  component.onChangePeriodOption(type);

  // Assert
  expect(component.filterOptions).toEqual([{ field: 'Type1', key: false }, { field: 'Type2', key: false }]); // All types in filterOptions should have key = false
  expect(type.key).toBe(true); // The selected type should have key = true
  expect(component.isMonthly).toBe(false);
  expect(component.isQuarterly).toBe(false);
  expect(component.isAnnually).toBe(true);
  expect(component.setDefaultTypeTab).toHaveBeenCalled();
  expect(component.getPortfolioCompanyMasterKPIValues).toHaveBeenCalledWith(null);
});

 it('should set filterOptions and call onChangePeriodOption, getPortfolioCompanyMasterKPIValues when setPeriodsOptions is called', () => {
    // Arrange
    const pageConfigTabs = [{ aliasName: 'tab1', chartValue: ['Type1'] }, { aliasName: 'tab2', chartValue: ['Type2'] }];
    const periodOptions = [{ field: 'Type1', key: true }, { field: 'Type2', key: false }];
    component.tabName = 'tab1';
    PeriodType.filterOptions = periodOptions;
    spyOn(component, 'onChangePeriodOption');
    spyOn(component, 'getPortfolioCompanyMasterKPIValues');

    // Act
    component.setPeriodsOptions(pageConfigTabs);

    // Assert
    expect(component.filterOptions).toEqual([{ field: 'Type1', key: true }]);
    expect(component.onChangePeriodOption).toHaveBeenCalledWith({ field: 'Type1', key: true });
    expect(component.getPortfolioCompanyMasterKPIValues).toHaveBeenCalledWith(null);
  });

  it('should set tabValueTypeList and call setPeriodsOptions when getValueTypeTabList is called', () => {
    // Arrange
    const financialTypesModelList = [{ name: 'Type1' }, { name: 'Type2' }];
    const subSectionFields = [{ aliasName: 'Type1', chartValue: ['Value1'] }, { aliasName: 'Type2', chartValue: [] }];
    const portfolioCompanyService: PortfolioCompanyService = TestBed.inject(PortfolioCompanyService);
    spyOn(portfolioCompanyService, 'getfinancialsvalueTypes').and.returnValue(of({ body: { financialTypesModelList } }));
    spyOn(component, 'setPeriodsOptions');
    component.subSectionFields = subSectionFields;

    // Act
    component.getValueTypeTabList();

    // Assert
    expect(component.tabValueTypeList).toEqual([{ name: 'Type1', active: true }]);
    expect(component.tabName).toBe('Type1');
    expect(component.setPeriodsOptions).toHaveBeenCalledWith(subSectionFields);
  });

  it('should return true when isNumberCheck is called with a numeric value', () => {
    // Arrange
    const str = '123';

    // Act
    const result = component.isNumberCheck(str);

    // Assert
    expect(result).toBe(true);
  });

  it('should return false when isNumberCheck is called with a non-numeric value', () => {
    // Arrange
    const str = 'abc';

    // Act
    const result = component.isNumberCheck(str);

    // Assert
    expect(result).toBe(false);
  });

  it('should call getValueTypeTabList when ngOnInit is called', () => {
    // Arrange
    spyOn(component, 'getValueTypeTabList');

    // Act
    component.ngOnInit();

    // Assert
    expect(component.getValueTypeTabList).toHaveBeenCalled();
  });
    it('should update ErrorNotation when handleChange is called', () => {
      // Arrange
      const event = { checked: true };
    
      // Act
      component.handleChange(true);
    
      // Assert
      expect(component.ErrorNotation).toBe(true);
    });
    it('should call error method of toastrService when showErrorToast is called', () => {
      // Arrange
      const message = 'error message';
      const title = 'error title';
      const position = 'toast-center-center';
      const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(
        ToastrService
      );

      // Act
      component.showErrorToast(message, title, position);

      // Assert
      expect(toastrServiceStub.error).toHaveBeenCalledWith(message, title, { positionClass: position });
  });
  it('should return the correct Audit object', () => {
    // Arrange
    const rowData = {
      KpiId: 575,
      ParentId: 0,
      DisplayOrder: 5,
      KPIValue: "567",
      PortfolioCompanyID: 3,
      KPI: "mmm",
      "KPI Info": "x",
      IsBoldKPI: false,
      IsHeader: false,
      KPIActualValue: null,
      KPIBudgetValue: null,
      CompanyId: 3,
      ValuesKpiId: 575,
      IdentityId: 37,
      AuditLog: null,
      BudgetAuditLog: null,
      ActualAuditLog: null,
      Formula: null,
      IsFormula: false,
      MasterFormula: null,
      EsgKpiRecordId: 0,
      "Jan 2023": "567",
      "Feb 2023": "567",
      "Mar 2023": "567",
    };
    const dateComponents = {
      year: "2023",
      month: 3,
      quarter: null,
    };
    const expectedAudit = {
      quarter: null,
      year: 2023,
      month: 3,
      kpiValueId: 0,
      mappingId:0,
      valueType: "Actual",
      kpiId: 575,
      moduleId: 4,
      companyId: 3,
  };

    // Act
    const result = component.getAuditLogFilter(rowData, dateComponents);
    // Assert
    expect(result.valueType).toEqual("Tab1");
    expect(result.moduleId).toEqual(expectedAudit.moduleId);
  });
  it('should redirect to audit log page', () => {
    // Arrange
    const field = { header: 'Field Header' };
    const attributeName = 'Attribute Name';
    let data :MappedDocuments;
    data = {
      documentModels: [
        {
          id: 10526,
          documentId: "eac0c6df-8009-48f8-a77f-c65e6ef0b6ca.xlsx",
          documentName: "company 2_Operational KPI_Import (3).xlsx",
          extension: "xlsx",
          isExisting: false,
        },
        {
          id: 10527,
          documentId: "c0a0d73b-113a-49dd-8dc4-a933883e5d11.xlsx",
          documentName: "company 2_Operational KPI_Import (2).xlsx",
          extension: "xlsx",
          isExisting: false,
        },
        {
          id: 10528,
          documentId: "9beaa608-d796-42d2-9237-753deb0936d9.xlsx",
          documentName: "company 2_Operational KPI_Import (1).xlsx",
          extension: "xlsx",
          isExisting: false,
        },
      ],
      valueId: 39,
      commentId: 10136,
      supportingDocumentsId: "10526,10527,10528",
      documentId: 10529,
      mappingId: 39,
      auditLogId: 39,
      comments: "test",
      auditLogCount: 0,
    };
    let auditLogFilter: Audit;
    auditLogFilter = {
      valueType: "Actual",
      kpiId: 575,
      mappingId: undefined,
      quarter: null,
      year: 2023,
      month: 3,
      moduleId: 4,
      companyId: 3,
      kpiValueId: 1,
      FieldName: "Field Header",
    };
    spyOn(sessionStorage, 'setItem');
    const oidcAuthServiceStub: OidcAuthService = fixture.debugElement.injector.get(
      OidcAuthService
    );
    spyOn(oidcAuthServiceStub, 'getEnvironmentConfig').and.returnValue({ redirect_uri: 'http://example.com/in' });
    spyOn(window, 'open');
  
    // Act
    component.redirectToAuditLogPage(field, attributeName, data, auditLogFilter);
    // Assert
    expect(oidcAuthServiceStub.getEnvironmentConfig).toHaveBeenCalled();
  });
  it('should set infoUpdate to false when CloseInfo is called', () => {
    component.infoUpdate = true; // set it to true first
    component.CloseInfo(); // call the method
    expect(component.infoUpdate).toBe(false); // expect it to be false
  });
  it('should return correct boolean value when checkCalcColumn is called', () => {
    const rowData = { 'Calc testField': 'true' };
    const column = { field: 'testField' };

    const result = component.checkCalcColumn(rowData, column);

    expect(result).toBe(true);
  });
  it('should set isUploadPopupVisible to false when cancelButtonEvent is called', () => {
    component.isUploadPopupVisible = true; // set it to true first
    component.cancelButtonEvent(); // call the method
    expect(component.isUploadPopupVisible).toBe(false); // expect it to be false
  });
  it('should initialize isUploadPopupVisible to false', () => {
    expect(component.isUploadPopupVisible).toBe(false);
  });

  it('should initialize uniqueModuleCompany to undefined', () => {
    expect(component.uniqueModuleCompany).toBeUndefined();
  });

  it('should initialize dataRow to undefined', () => {
    expect(component.dataRow).toBeUndefined();
  });

  it('should initialize dataColumns to undefined', () => {
    expect(component.dataColumns).toBeUndefined();
  });

  it('should call exportInvestmentKPIList and downloadExcelFile on successful response', () => {
    component.model = {
      companyName: 'Test Company',
      portfolioCompanyID: '1',
    };
    component.investmentKpiSearchFilter = 'search filter';
    // component.modelMasterKpi = {
    //   decimalPlaces: { type: 'decimal' },
    // };
    // component.masterKpiValueUnit = { typeId: '1' };

    // Act
    component.exportInvestmentKpiValues();

    // Assert
    expect(component.exportMasterKPILoading).toBeFalse();
  });

});