import { Activated<PERSON>oute, Router } from '@angular/router';
import { Component, OnInit, ViewChild } from '@angular/core';
import { CloService } from '../../../services/clo.service';
import { FormBuilder} from '@angular/forms';
import { DatePipe } from '@angular/common';
import { FeatureTableMapping, TOASTER_MSG } from 'src/app/common/constants';
import { CommonSubFeaturePermissionService } from 'src/app/services/subPermission.service';
import { PermissionActions } from 'src/app/common/constants';
import { FeaturesEnum } from 'src/app/services/permission.service';
import { ErrorMessage } from 'src/app/services/miscellaneous.service';
import { ToastrService } from 'ngx-toastr';
import { PanelbarItemService } from '../shared/panelbar-item/panelbar-item.service';
import { TabStripComponent } from '@progress/kendo-angular-layout';
import { Subscription } from 'rxjs';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';

@Component({
  selector: 'view-clo-summary',
  templateUrl: './view-clo-summary.component.html',
  styleUrls: ['./view-clo-summary.component.scss'],
  providers: [DatePipe]
})
export class ViewCloSummaryComponent implements OnInit {
  public TAB_NAMES = {
    CLO_SUMMARY: 4,
    CLO_PERFORMANCE_INDICATOR: 5,
    CLO_DISTRIBUTION: 6,
    Key_KPI:12,
    CLO_Tests:13,
    CLO_Versus_CLO_Sector:14,
  };
  activeTab: number = this.TAB_NAMES.CLO_SUMMARY;
  uniqueId: string = '';
  CLOModel: any=[];
  @ViewChild('tabStrip') public tabStrip: TabStripComponent;
  pageId:number=2;
  CAN_IMPORT=PermissionActions.CAN_IMPORT;
  CAN_EXPORT = PermissionActions.CAN_EXPORT;
  CAN_EDIT= PermissionActions.CAN_EDIT;
  isLoading:boolean = false;
  constructor(    
    private readonly toastrService: ToastrService,
    private readonly route: ActivatedRoute,
    private readonly fb: FormBuilder,
    private readonly datePipe: DatePipe,
    private readonly router: Router,
    private readonly viewCloService: CloService,
    private readonly subPermissionService: CommonSubFeaturePermissionService,
    private pagePanelService:PanelbarItemService,
    private breadcrumbService: BreadcrumbService
  ) {
    this.getCompanyName();
  }
  tabs:any=[];
  activePerformanceIndicatorTab: number = this.TAB_NAMES.Key_KPI;
  companyName: string = '';
  isEdited:boolean = false;
  public CloPerformanceIndicatorTabs:any[]=[];

  selectedTabData: any;
  private subscription: Subscription;

  ngOnInit() {
    this.isLoading=true;
    this.viewCloService.currentData.subscribe(
      (data) => {
        this.uniqueId = data;
      }
    );   
    this.route.paramMap.subscribe(params => {
        this.uniqueId = params.get('id'); 
        this.redirectToCloData(this.uniqueId);
    });
  }

  updateBreadcrumbs(cloname:string) {
    let newBreadcrumbs: any[] = [];
      newBreadcrumbs.push( { label: 'Collateral Loan Obligation(CLO)', url: '/clo-list' });
    newBreadcrumbs.push( { label: cloname});
    this.breadcrumbService.setBreadcrumbs(newBreadcrumbs);
  }
  redirectToCloData(uniqueId: string){  
    this.isLoading=true;  
    this.viewCloService.getCloById(uniqueId).subscribe({
      next: (data) => {  
          
        if(data == null){
          this.toastrService.error(TOASTER_MSG.CLO_NOT_FOUND, "", { positionClass: TOASTER_MSG.POS_CENTER });
          this.router.navigate(['/clo-list']);
          return;
        }
             
        this.CLOModel=data;
        this.updateBreadcrumbs(this.CLOModel.issuer);
        
                      
        if (this.CLOModel.companyID) {
          this.getSubFeatureAccessPermissions();
        }
        this.uniqueId = uniqueId; 
        this.isLoading=false;   
      },
      error: (error) => {
        console.error('Error fetching investment company list', error);
      }
    });
}

 getCompanyName(){
  this.route.queryParams.subscribe(params => {
    this.companyName = params['name'];
  });
 }


 getConfiguration() {
  this.viewCloService.getTabList(this.pageId, this.CLOModel.companyID.toString(), this.CLOModel.clO_ID).subscribe(data=>{
    const tempTabs = data;
    this.tabs = tempTabs?.map(tab => ({
      ...tab,
      onClick: () => {}
    }));
    this.tabs.forEach(page => {
      this.pagePanelService.updateTableVisibility(page.tableList,this.permissions);
      page.subTabList.forEach(tab => {
          this.pagePanelService.updateTableVisibility(tab.tableList,this.permissions);
      });
  });
    this.selectedTabData=this.tabs.find(x=>x.tabId==this.activeTab);

    const index = this.tabs.findIndex(tab => tab.tabId === this.TAB_NAMES.CLO_PERFORMANCE_INDICATOR);
    if (index >= 0) {
      this.CloPerformanceIndicatorTabs = this.tabs[index].subTabList;
      this.activePerformanceIndicatorTab = this.CloPerformanceIndicatorTabs[0]?.tabId;
      this.CloPerformanceIndicatorTabs = this.CloPerformanceIndicatorTabs?.map((x) =>
        Object.assign(x, {
        })
      );
      this.configureDomicile(index);
    }
  })
}

private configureDomicile(index: number) {
  const updateTabName = (tab, name) => {
    tab.name = tab.name.replace('Domicile', name);
    if (tab.id === 0) {
      tab.aliasName = tab.name;
    }
  };

  const domicileTab = this.CloPerformanceIndicatorTabs.find(tab => tab.tabId === this.TAB_NAMES.CLO_Versus_CLO_Sector);
  if (domicileTab) {
    updateTabName(domicileTab, this.CLOModel.domicile);
  }
  const kpiTab = this.CloPerformanceIndicatorTabs.find(tab => tab.tabId === this.TAB_NAMES.Key_KPI);
if (kpiTab) {
  let usTable = kpiTab.tableList?.find(x=>x.tableId==FeatureTableMapping.EDIT_DOMICILE_TABLES_NAME.Key_KPIs);
  let euTable = kpiTab.tableList?.find(x=>x.tableId==FeatureTableMapping.TOBE_CHANGE_DOMICILE_TABLES_NAME.Key_KPIs);
  usTable.tableName=this.CLOModel.domicile === 'US'?usTable.tableName:euTable.tableName;
}

  this.tabs[index].subTabList = this.CloPerformanceIndicatorTabs;
}


  getFirstAvailableTab() {
    if (this.canViewCLOSummary || this.canViewCapitalStructure || this.canViewCollateral || this.canViewLeverage) {
      return this.TAB_NAMES.CLO_SUMMARY;
    } else if (this.canViewKeyKPI || this.canViewOvercollateralisationTest || this.canViewCollateralQualityTest || this.canViewcCLOVersusCLOSector) {
      return this.TAB_NAMES.CLO_PERFORMANCE_INDICATOR;
    } else if (this.canViewCLODistributionsToDate) {
      return this.TAB_NAMES.CLO_DISTRIBUTION;
    }
    return null;
  }

  setActiveTab(tabSelected: any) {
    this.selectedTabData = tabSelected;
    if (tabSelected.tabId === this.TAB_NAMES.CLO_SUMMARY && 
        (!this.canViewCLOSummary || !this.canViewCapitalStructure || !this.canViewCollateral)) {
      const nextAvailableTab = this.getFirstAvailableTab();
      if (nextAvailableTab) {
        this.activeTab = nextAvailableTab;
      }
      return;
    }
    this.activeTab = tabSelected.tabId;
  }

  redirectToCloPage(){
    this.router.navigate(['/clo-list']);
  }

  redirectToEditClo(){   
    if (!this.canEditCLOSummary) {
      this.showNoAccessError();
    } 
    else{
    this.router.navigate(['/add-clo'], { queryParams: { name: this.CLOModel.companyName, id: this.CLOModel.companyID, uniqueId: this.CLOModel.uniqueID} });
  }
}

  redirectToCloKpiHistory( selectedCloName : string ){
    this.router.navigate(['/clo-kpi-history'], { queryParams: { kpiname: selectedCloName , cloid: this.uniqueId, dmcl : this.CLOModel.domicile } });
  }
  canViewCLOSummary: boolean = false;
  canEditCLOSummary: boolean = false;
  canViewCapitalStructure: boolean = false;
  canViewCollateral: boolean = false;
  canViewKeyKPI: boolean = false;
  canViewOvercollateralisationTest: boolean = false;
  canViewCollateralQualityTest: boolean = false;
  canViewcCLOVersusCLOSector: boolean = false;
  canViewCLODistributionsToDate: boolean = false;
  canViewLeverage : boolean = false;
  permissions:any=[];
 

  getSubFeatureAccessPermissions() {
    this.subPermissionService.getCommonSubFeatureAccessPermissions(this.CLOModel.companyID.toString(), FeaturesEnum.CLOPage).subscribe({
        next: (result) => {
          this.getConfiguration();
          if (result.length > 0) {
            this.permissions=result;
            
           
            this.canViewCLOSummary = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.CLOSummary.FeatureId), PermissionActions.CAN_VIEW);
            this.canEditCLOSummary = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.CLOSummary.FeatureId), PermissionActions.CAN_EDIT);
            this.canViewCapitalStructure = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.CapitalStructure.FeatureId), PermissionActions.CAN_VIEW);
            this.canViewCollateral = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.Collateral.FeatureId), PermissionActions.CAN_VIEW);
            this.canViewKeyKPI = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.KeyKPI.FeatureId), PermissionActions.CAN_VIEW);
            this.canViewOvercollateralisationTest = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.OvercollateralisationTest.FeatureId), PermissionActions.CAN_VIEW);
            this.canViewCollateralQualityTest = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.CollateralQualityTest.FeatureId), PermissionActions.CAN_VIEW);
            this.canViewcCLOVersusCLOSector = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.CLOVersusCLOSector.FeatureId), PermissionActions.CAN_VIEW);
            this.canViewCLODistributionsToDate = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.CLODistributionsToDate.FeatureId), PermissionActions.CAN_VIEW);
            this.canViewLeverage = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.Leverage.FeatureId), PermissionActions.CAN_VIEW);

            // Set initial active tab based on permissions
            const firstAvailableTab = this.getFirstAvailableTab();
            if (firstAvailableTab) {
              this.activeTab = firstAvailableTab;
            }
          }
        },
        error: (error) => {
            console.error('Error fetching permissions:', error);
        }
    });
} 

    checkPermissionAccess(permission:any[], permissionType): boolean {
      return permission.map(x => x[permissionType]).includes(true);
    }

    showNoAccessError() {
      this.toastrService.error(ErrorMessage.NoAccess, "", { positionClass: "toast-center-center" });
    }

    isTabVisible(tabName: number): boolean {
      switch (tabName) {
        case this.TAB_NAMES.CLO_SUMMARY:
          return this.canViewCLOSummary || this.canViewCapitalStructure || this.canViewCollateral;
        
        case this.TAB_NAMES.CLO_PERFORMANCE_INDICATOR:
          return this.canViewKeyKPI || 
                 this.canViewOvercollateralisationTest || 
                 this.canViewCollateralQualityTest || 
                 this.canViewcCLOVersusCLOSector;
        
        case this.TAB_NAMES.CLO_DISTRIBUTION:
          return this.canViewCLODistributionsToDate;
        
        default:
          return false;
      }
    }
    onTabSelect(e: any): void {
      this.activePerformanceIndicatorTab = this.CloPerformanceIndicatorTabs[e.index].tabId;
    }

    checkTabPermission(tabParam: number): boolean {
      switch (tabParam) {
        case this.TAB_NAMES.Key_KPI:
          return this.canViewKeyKPI;
        case this.TAB_NAMES.CLO_Tests:
          return this.canViewOvercollateralisationTest && this.canViewCollateralQualityTest;
        case this.TAB_NAMES.CLO_Versus_CLO_Sector:
          return this.canViewcCLOVersusCLOSector;
        default:
          return false;
      }
    }

    checkTablePermissions(tableId:number,permissionType:string=null):boolean{
      return this.pagePanelService.checkTablePermissions(tableId,this.permissions,permissionType);
     }
}
