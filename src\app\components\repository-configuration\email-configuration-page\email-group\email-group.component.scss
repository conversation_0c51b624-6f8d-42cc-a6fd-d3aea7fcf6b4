@import "../../../../../variables.scss";

$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-xl: 20px;
$padding-std: 6px;
$border-radius-sm: 4px;
$border-radius-lg: 8px;
$icon-size: 24px;
$btn-padding-y: 0px;
// Color variables
$transparent: transparent;
$disabled-opacity: 0.7;
.text-alignright {
    padding-right: 12px;
}

.chkbx-border{
  border-color: $Neutral-Gray-80;
}

.email-list-container {
    height: calc(100vh - 200px);
    overflow-y: auto;
}

.action-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
}

.cursor-pointer {
    cursor: pointer;
}

.custom-email-grid {
    height: calc(100vh - 200px);
    border: 1px solid $Neutral-Gray-05;
    border-radius: 4px;
}

.email-list-header {
    border-bottom: 1px solid $Neutral-Gray-05;
}

// Floating selection popup
.selection-popup {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background-color: $Neutral-Gray-00;
  color: $Neutral-Gray-90;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-short;

  .selection-content {
    display: flex;
    align-items: center;
    padding: $spacing-md $spacing-xl;
    position: relative;

    .selected-count {
      margin-right: $spacing-xl;
      font-weight: 500;
    }

    .delete-selected-btn {
      background-color: $Neutral-Gray-00;
      border: none;
      color: $Negative-110;
      padding: $padding-std $btn-padding-y;
      border-radius: $border-radius-sm;
      cursor: pointer;
      transition: background-color 0.2s;

      &:disabled {
        background-color: $Neutral-Gray-00;
        cursor: not-allowed;
        opacity: $disabled-opacity;
      }
    }

    .close-selection-btn {
      position: absolute;
      right: $spacing-sm;
      top: 50%;
      transform: translateY(-50%);
      background: $transparent;
      border: none;
      font-size: 14px;
      line-height: 1;
      cursor: pointer;
      color: $Neutral-Gray-70;
      display: flex;
      align-items: center;
      justify-content: center;
      width: $icon-size;
      height: $icon-size;
      padding-right: $spacing-sm;

      span {
        display: block;
        line-height: 0.8;
      }
    }
  }
}

// delete confirmation popup
.warning-text {
    color: $Negative-100 !important;
}

.d-grid{
  display: grid;
}

.email-group-container {
    background-color: $Neutral-Gray-02;
    border-radius: $border-radius-lg;
    color: $Neutral-Gray-100;
    border: 1px solid $Neutral-Gray-02;
}

.active{
  border: 1px solid $Primary-78;
}

.dataitem-label{
  color: $content-label-color;
  margin-bottom: 4px;
}

.btn-icon{
  border: $border-color-dark;
  border-radius: $border-radius-sm;
  background-color: $Neutral-Gray-00;
  padding: 0px !important;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.company-pill{
  color: $Primary-78;
  border: 1px solid $Primary-43;
  background-color: $Primary-35;
  border-radius: 16px;
  font-size: $font-size-medium;
}

.doc-tooltip-item {
  border-bottom: 1px solid $Neutral-Gray-10;
  padding: $spacing-md $padding-large;
  font-size: $font-size-medium;
  font-weight: 400;
}

.flex-item-27{
  flex: 0 0 27%;
}
.flex-item-45{
  flex: 0 0 45%;
}

