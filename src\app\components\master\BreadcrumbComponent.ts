import { Component, ElementRef, Input, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute, NavigationEnd, NavigationStart, Router } from "@angular/router";
import { distinctUntilChanged, filter, Subscription } from "rxjs";
import { PermissionService } from "../../services/permission.service";
import { Location } from "@angular/common";
import { Title } from "@angular/platform-browser";
import { BreadcrumbService } from "src/app/services/breadcrumb-service.service";
interface Breadcrumb {
  label: string;
  url: string;
  isIdRoute:boolean;
}
@Component({
  selector: "app-breadcrumb",
  templateUrl: "breadcrumb.component.html",
  styleUrls: ["breadcrumb.component.scss"],
})
export class BreadcrumbComponent implements OnInit {
  breadcrumbs: { label: string; url: string }[] = [];
  @Input() name: string;
  @ViewChild("navBreadcrumb") navBreadcrumb: ElementRef;
  private subscription: Subscription;
  links: any[] = [];
  currentItem: any = null;
  EnableBack: boolean = false;
  customHeader: string;
  customHeaderEnable: boolean = false;
  isFromService:boolean=false;

  constructor(
    private router: Router,
    private location: Location,
    private activatedRoute: ActivatedRoute,
    private  titleService: Title,
    private permissionService: PermissionService,
    private breadcrumbService: BreadcrumbService
  ) {
    this.breadcrumbs = [];
    this.router.events
     .pipe(filter(event => event instanceof NavigationEnd))
     .subscribe(() => {
       this.breadcrumbs = this.createBreadcrumbs(this.activatedRoute.root);
     });
  }
  ngOnInit() {
    if(this.isFromService){
      this.subscription = this.breadcrumbService.breadcrumbs.subscribe(
        (breadcrumbs) => {
          if(breadcrumbs?.length>0)
          this.breadcrumbs = breadcrumbs;
        }
      );
    }
    
  }

  private createBreadcrumbs(route: ActivatedRoute, url: string = '', breadcrumbs: Breadcrumb[] = []): Breadcrumb[] {
    const children: ActivatedRoute[] = route.children;
    if (children.length === 0) {
      return breadcrumbs;
    }
    for (const child of children) {
      if (child.snapshot.data['breadcrumb']) {
        breadcrumbs =  child.snapshot.data['breadcrumb'];
        this.setThirdLevel(breadcrumbs);
      }
      if(child.snapshot.data['isFromService']){
        this.isFromService=true;
      }
      return this.createBreadcrumbs(child, url, breadcrumbs);
    }
    return breadcrumbs; 
  }
  private setThirdLevel(breadcrumbs: Breadcrumb[]) {
    if (breadcrumbs.length > 2 && breadcrumbs[2].isIdRoute) {
      const headerName = localStorage.getItem("headerName");
      if (headerName && headerName !== "undefined") {
      breadcrumbs[2].label = headerName;
      }
    }
  }

  getBreadcrumbValue(breadcrumb: Breadcrumb) {
    if(breadcrumb.isIdRoute)
    {
      return localStorage.getItem("headerName")!="undefined"? localStorage.getItem("headerName"):null;
    }
    else
    {
      return breadcrumb.label;
    }
  }
}
