import { TableSuggestionResponse, TableSuggestion, TableType, PageDetails, TablePageDetails, FilePageDetails } from './table-suggestions.model';

export class TableSuggestionsUtility {
    /**
     * Gets page details organized by file ID for a specific table label
     * @param response The extracted response containing table suggestions
     * @param label The label to filter tables by
     * @returns Array of file details with their pages
     */
    static getFilePageDetailsByLabel(response: TableSuggestionResponse, label: string): FilePageDetails[] {
        const tableType = response.tables.find(t => t.label === label);
        if (!tableType) return [];
        
        const fileMap = new Map<string, PageDetails[]>();
        
        tableType.suggestions.forEach(s => {
            if (!fileMap.has(s.file_id)) {
                fileMap.set(s.file_id, []);
            }
            
            fileMap.get(s.file_id).push({
                page: s.page,
                score: s.score
            });
        });
        
        // Convert map to array and sort pages within each file
        return Array.from(fileMap.entries()).map(([file_id, pages]) => ({
            file_id,
            pages: pages.sort((a, b) => a.page - b.page)
        }));
    }

    /**
     * Gets all page details organized by label and file ID
     * @param response The extracted response containing table suggestions
     * @returns Array of table details with pages organized by file ID
     */
    static getAllFilePageDetails(response: TableSuggestionResponse): TablePageDetails[] {
        return response.tables.map(table => ({
            label: table.label,
            files: this.getFilePageDetailsByLabel(response, table.label)
        }));
    }

    /**
     * Adds a new table suggestion to the response
     * @param response The extracted response to modify
     * @param label The label for the table
     * @param suggestion The new suggestion to add
     * @returns Modified response with new suggestion
     */
    static addTableSuggestion(response: TableSuggestionResponse, label: string, suggestion: TableSuggestion): TableSuggestionResponse {
        const newResponse = { ...response };
        const tableType = newResponse.tables.find(t => t.label === label);
        
        if (tableType) {
            tableType.suggestions.push(suggestion);
        } else {
            newResponse.tables.push({
                label,
                suggestions: [suggestion]
            });
        }
        
        return newResponse;
    }

    /**
     * Filters response to only include suggestions for specified files and pages within labels
     * @param response The extracted response to filter
     * @param labelFilePageMap Object mapping labels to arrays of file details with their pages
     * @returns Filtered response containing only specified suggestions
     */
    static filterByLabelFileAndPages(
        response: TableSuggestionResponse, 
        labelFilePageMap: { [label: string]: { fileId: string, pages: number[] }[] }
    ): TableSuggestionResponse {
        const newResponse = { ...response };
        
        newResponse.tables = response.tables
            .filter(table => labelFilePageMap.hasOwnProperty(table.label))
            .map(table => ({
                ...table,
                suggestions: table.suggestions.filter(s => {
                    const fileConfig = labelFilePageMap[table.label]
                        .find(f => f.fileId === s.file_id);
                    return fileConfig?.pages.includes(s.page);
                })
            }))
            .filter(table => table.suggestions.length > 0);
            
        return newResponse;
    }

    /**
     * Removes specific pages from table suggestions for a given label
     * @param response The extracted response to modify
     * @param label The label for the table
     * @param pagesToRemove Array of page numbers to remove
     * @param fileId Optional file ID to remove pages from specific file only
     * @returns Modified response with specified pages removed
     */
    static removeTableSuggestionPages(
        response: TableSuggestionResponse,
        label: string,
        pagesToRemove: number[],
        fileId?: string
    ): TableSuggestionResponse {
        const newResponse = { ...response };
        const tableType = newResponse.tables.find(t => t.label === label);
        
        if (tableType) {
            tableType.suggestions = tableType.suggestions.filter(s => {
                if (fileId && s.file_id !== fileId) {
                    return true;
                }
                return !pagesToRemove.includes(s.page);
            });
        }
        
        // Remove table type if no suggestions left
        newResponse.tables = newResponse.tables.filter(t => t.suggestions.length > 0);
        
        return newResponse;
    }

    /**
     * Adds suggestions for newly added pages to the response
     * @param response The extracted response to modify
     * @param label The label for the table
     * @param pagesToAdd Array of page numbers to add
     * @param fileId File ID to add pages to
     * @param score Optional confidence score (defaults to 0.9)
     * @returns Modified response with new page suggestions added
     */
    static addTableSuggestionPages(
        response: TableSuggestionResponse,
        label: string,
        pagesToAdd: number[],
        fileId: string,
        score: number = 0.0
    ): TableSuggestionResponse {
        let newResponse = { ...response };
        
        if (pagesToAdd.length === 0) {
            return newResponse;
        }
        
        // Create a new suggestion for each page and add it to the response
        pagesToAdd.forEach(page => {
            const newSuggestion: TableSuggestion = {
                bbox: {
                    x1: 0.0,
                    y1: 0.0,
                    x2: 0.0,
                    y2: 0.0
                },
                page: page,
                file_id: fileId,
                score: score
            };
            
            newResponse = this.addTableSuggestion(newResponse, label, newSuggestion);
        });
        
        return newResponse;
    }
    
    /**
     * Handles page changes by identifying and processing added and removed pages
     * @param response The extracted response to modify
     * @param label The label for the table
     * @param oldPages Original list of page numbers
     * @param newPages Updated list of page numbers
     * @param fileId File ID to update
     * @returns Modified response with updated page suggestions
     */
    static handlePageChanges(
        response: TableSuggestionResponse,
        label: string,
        oldPages: number[],
        newPages: number[],
        fileId: string
    ): TableSuggestionResponse {
        // Find pages that were removed
        const removedPages = oldPages.filter(p => !newPages.includes(p));
        
        // Find pages that were added
        const addedPages = newPages.filter(p => !oldPages.includes(p));
        
        let updatedResponse = response;
        
        // Handle removed pages
        if (removedPages.length > 0) {
            updatedResponse = this.removeTableSuggestionPages(
                updatedResponse,
                label,
                removedPages,
                fileId
            );
        }
        
        // Handle added pages
        if (addedPages.length > 0) {
            updatedResponse = this.addTableSuggestionPages(
                updatedResponse,
                label,
                addedPages,
                fileId
            );
        }
        
        return updatedResponse;
    }
    
    /**
     * Parses a comma-separated string into an array of page numbers
     * @param pagesString Comma-separated string of page numbers
     * @returns Array of validated page numbers
     */
    static parsePageString(pagesString: string): number[] {
        return pagesString 
            ? pagesString.split(',')
                .map(p => parseInt(p.trim()))
                .filter(p => !isNaN(p))
            : [];
    }
}