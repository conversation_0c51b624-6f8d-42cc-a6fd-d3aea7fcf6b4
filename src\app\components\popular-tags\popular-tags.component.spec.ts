import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { DocumentService } from '../../services/document.service';
import { PopularTagsComponent } from './popular-tags.component';

describe('PopularTagsComponent', () => {
  let component: PopularTagsComponent;
  let fixture: ComponentFixture<PopularTagsComponent>;
  let mockDocumentService;

  beforeEach(() => {
    mockDocumentService = jasmine.createSpyObj(['getPopularTags']);

    TestBed.configureTestingModule({
      declarations: [ PopularTagsComponent ],
      providers: [
        { provide: DocumentService, useValue: mockDocumentService }
      ]
    });

    fixture = TestBed.createComponent(PopularTagsComponent);
    component = fixture.componentInstance;
  });

  it('should get popular tags on init', () => {
    spyOn(component, 'getPopularTags');

    component.ngOnInit();

    expect(component.getPopularTags).toHaveBeenCalled();
  });

  it('should get popular tags and reset updatePopularTags when foldername or updatePopularTags changes', () => {
    spyOn(component, 'getPopularTags');
    spyOn(component.resetUpdatePopularTags, 'emit');

    component.foldername = 'New folder';
    component.updatePopularTags = true;
    component.ngDoCheck();

    expect(component.getPopularTags).toHaveBeenCalled();
    expect(component.updatePopularTags).toBe(false);
    expect(component.resetUpdatePopularTags.emit).toHaveBeenCalled();
  });

  it('should get popular tags from the document service', () => {
    const mockTags = ['tag1', 'tag2'];
    mockDocumentService.getPopularTags.and.returnValue(of(mockTags));

    component.getPopularTags();

    expect(component.loader).toBe(false);
    expect(component.tags).toEqual(mockTags);
  });
});