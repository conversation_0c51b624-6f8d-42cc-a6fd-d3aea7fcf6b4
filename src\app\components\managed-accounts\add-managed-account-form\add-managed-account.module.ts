import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { AddManagedAccountComponent } from './add-managed-account.component';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { AngularResizeEventModule } from 'angular-resize-event';
import { QuillModule } from 'ngx-quill';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { ManagedAccountService } from '../managed-account.service';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';

@NgModule({
  declarations: [
    AddManagedAccountComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MaterialModule,
    PrimeNgModule,
    AngularResizeEventModule,
    QuillModule,
    SharedDirectiveModule,
    ReactiveFormsModule,
    SharedComponentModule,
    DateInputsModule,
    RouterModule.forChild([
      { path: '', component: AddManagedAccountComponent }
    ]),
    KendoModule
  ],
  providers: [
    ManagedAccountService
  ]
})
export class AddManagedAccountModule { }
