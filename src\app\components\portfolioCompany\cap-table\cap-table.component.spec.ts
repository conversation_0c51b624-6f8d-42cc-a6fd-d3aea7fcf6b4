import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CapTableComponent } from './cap-table.component';
import { CapTableService } from 'src/app/services/cap-table.service';
import { HttpClientModule } from '@angular/common/http';
import { of } from 'rxjs';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { FormsModule } from '@angular/forms';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { OidcAuthService } from 'src/app/services/oidc-auth.service';
import { AuditService } from 'src/app/services/audit.service';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('CapTableComponent', () => {
  let component: CapTableComponent;
  let fixture: ComponentFixture<CapTableComponent>;
  let capTableService: jasmine.SpyObj<CapTableService>;
  const spyPageConfigService = jasmine.createSpyObj('CapTableService', ['getCapTableValues','exportCapTableValues']);
  let oidcAuthService:jasmine.SpyObj<OidcAuthService>;
  const spyOidcAuthService = jasmine.createSpyObj('OidcAuthService', ['getEnvironmentConfig']);
  let auditService: jasmine.SpyObj<AuditService>;
  const auditServiceSpy = jasmine.createSpyObj('AuditService', ['getPortfolioEditSupportingCommentsData']);
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientModule,FormsModule,MaterialModule,PrimeNgModule,ToastrModule.forRoot()],
      declarations: [CapTableComponent],
      providers:[
        { provide: OidcAuthService, useValue: spyOidcAuthService },
        { provide: AuditService, useValue: auditServiceSpy },
        {
        provide: CapTableService,
        useValue: spyPageConfigService
      },
      { provide: 'BASE_URL', useValue: 'http://localhost:5000/' },
      { provide: MiscellaneousService, userValue: jasmine.createSpyObj('MiscellaneousService', ['downloadExcelFile']) },
      ToastrService,
      { provide: 'ToastConfig', useValue: {} }
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
    });
    fixture = TestBed.createComponent(CapTableComponent);
    component = fixture.componentInstance;
    capTableService = TestBed.inject(CapTableService) as jasmine.SpyObj<CapTableService>;
    auditService = TestBed.inject(AuditService) as jasmine.SpyObj<AuditService>;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the component', () => {
    // Arrange
    const mockTabList = [{ isActive: true, moduleId: 1 }];
    const mockPeriodType = [{ period: 'Monthly' }];
    const mockResult = { headers: [], rows: [], isMonthly: true, isQuarterly: false, isAnnually: false };

    spyOn(component, 'setPeriodsOptions');
    spyOn(component, 'setPeriodType');
    spyOn(component, 'getCapTableValues');

    // Act
    component.config = { activeTabs: mockTabList, capTablePeriods: mockPeriodType, capTableSections: [] };
    component.ngOnInit();

    // Assert
    expect(component.isLoader).toBeTrue();
    expect(component.setPeriodType).toHaveBeenCalled();
    expect(component.getCapTableValues).toHaveBeenCalled();
  });

  it('should change the active tab', () => {
    // Arrange
    const mockTab = { isActive: false, moduleId: 2 };

    spyOn(component, 'setPeriodsOptions');
    spyOn(component, 'setPeriodType');
    spyOn(component, 'getCapTableValues');

    // Act
    component.changeTabType(mockTab);

    // Assert
    expect(component.isLoader).toBeTrue();
    expect(mockTab.isActive).toBeTrue();
    expect(component.activeTab).toEqual(mockTab);
    expect(component.setPeriodsOptions).toHaveBeenCalledWith(mockTab);
    expect(component.setPeriodType).toHaveBeenCalled();
    expect(component.getCapTableValues).toHaveBeenCalled();
  });

});