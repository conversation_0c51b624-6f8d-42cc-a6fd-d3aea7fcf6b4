import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EmailReminderDetailsComponent } from './email-reminder-details.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ToastrService } from 'ngx-toastr';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RepositoryConfigService } from '../../../../services/repository.config.service';
import { of, throwError } from 'rxjs';
import { fakeAsync, tick } from '@angular/core/testing';

describe('EmailReminderDetailsComponent', () => {
  let component: EmailReminderDetailsComponent;
  let fixture: ComponentFixture<EmailReminderDetailsComponent>;
  let repoService: jasmine.SpyObj<RepositoryConfigService>;
  let toastr: jasmine.SpyObj<ToastrService>;

  beforeEach(async () => {
    repoService = jasmine.createSpyObj('RepositoryConfigService', ['skipReminderCycle', 'getEmailReminderDetails']);
    toastr = jasmine.createSpyObj('ToastrService', ['success', 'error']);

    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      declarations: [ EmailReminderDetailsComponent ],
      providers: [
        { provide: RepositoryConfigService, useValue: repoService },
        { provide: ToastrService, useValue: toastr },
        { provide: 'BASE_URL', useValue: 'http://localhost' }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(EmailReminderDetailsComponent);
    component = fixture.componentInstance;
  });

  const mockReminderDetails = {
    toRecipients: [ { internalID: 1, name: 'John Doe', emailAddress: '<EMAIL>' } ],
    ccRecipients: [ { internalID: 2, name: '', emailAddress: 'Admin Group' } ],
    subject: 'Test Subject',
    messageBody: 'Test Email Body',
    totalNumberOfReminders: 3,
    totalRemindersSent: 1,
    totalErrorAndSkippedReminders: 0,
    lastReminderSentDate: '2025-06-09T10:00:00Z',
    nextReminderScheduledDate: '2025-06-10T10:00:00Z'
  };

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('should identify group recipients correctly', () => {
    const emailRecipient = { internalID: 1, name: 'John', emailAddress: '<EMAIL>' };
    const groupRecipient = { internalID: 2, name: '', emailAddress: 'Admin Group' };

    expect(component.isGroupRecipient(emailRecipient)).toBeFalsy();
    expect(component.isGroupRecipient(groupRecipient)).toBeTruthy();
  });

  it('should return correct display text for recipients', () => {
    const emailRecipient = { internalID: 1, name: 'John', emailAddress: '<EMAIL>' };
    const groupRecipient = { internalID: 2, name: '', emailAddress: 'Admin Group' };

    expect(component.getRecipientDisplayText(emailRecipient)).toBe('<EMAIL>');
    expect(component.getRecipientDisplayText(groupRecipient)).toBe('Admin Group');
  });

  it('should load reminder data when reminderDetails is provided', () => {
    const mockReminderDetails = {
      toRecipients: [ { internalID: 1, name: 'John Doe', emailAddress: '<EMAIL>' } ],
      ccRecipients: [ { internalID: 2, name: '', emailAddress: 'Admin Group' } ],
      subject: 'Test Subject',
      messageBody: 'Test Email Body',
      totalNumberOfReminders: 3,
      totalRemindersSent: 1,
      totalErrorAndSkippedReminders: 0,
      lastReminderSentDate: '2025-06-09T10:00:00Z',
      nextReminderScheduledDate: '2025-06-10T10:00:00Z'
    };
    component.reminderDetails = mockReminderDetails as any;
    component.ngOnInit();
    expect(component.subject).toBe('Test Subject');
    expect(component.emailBody).toBe('Test Email Body');
    expect(component.totalReminders).toBe(3);
    expect(component.toRecipients.length).toBe(1);
    expect(component.ccRecipients.length).toBe(1);
    expect(component.lastReminderDate).toContain('09 Jun 2025');
    expect(component.nextReminderDate).toContain('10 Jun 2025');
  });

  it('should call loadReminderData on ngOnChanges', () => {
    spyOn(component as any, 'loadReminderData');
    component.reminderDetails = { subject: 'Test' } as any;
    component.ngOnChanges();
    expect((component as any).loadReminderData).toHaveBeenCalled();
  });

  it('should not call loadReminderData on ngOnChanges if reminderDetails is null', () => {
    spyOn(component as any, 'loadReminderData');
    component.reminderDetails = null;
    component.ngOnChanges();
    expect((component as any).loadReminderData).not.toHaveBeenCalled();
  });

  it('should handle loadReminderData with missing fields', () => {
    component.reminderDetails = {} as any;
    component['loadReminderData']();
    expect(component.toRecipients).toEqual([]);
    expect(component.ccRecipients).toEqual([]);
    expect(component.subject).toBe('');
    expect(component.emailBody).toBe('');
    expect(component.totalReminders).toBe(0);
    expect(component.remindersSent).toBe(0);
    expect(component.totalErrorandSkips).toBe(0);
    expect(component.lastReminderDate).toBe('NA');
    expect(component.nextReminderDate).toBe('NA');
  });

  it('should return name if emailAddress is missing in getRecipientDisplayText', () => {
    const recipient = { name: 'GroupName' };
    expect(component.getRecipientDisplayText(recipient)).toBe('GroupName');
  });

  it('should return empty string if recipient has no email or name', () => {
    expect(component.getRecipientDisplayText({})).toBe('');
  });

  it('should identify group recipient correctly for all cases', () => {
    expect(component.isGroupRecipient({ name: '', emailAddress: '<EMAIL>' })).toBeTruthy();
    expect(component.isGroupRecipient({ name: 'User', emailAddress: '<EMAIL>' })).toBeFalsy();
    expect(component.isGroupRecipient({ name: '', emailAddress: '' })).toBeFalsy();
    expect(component.isGroupRecipient({})).toBeFalsy();
  });

  it('should not call skipReminderCycle when onSkipCycleChange is called with false', () => {
    spyOn(component as any, 'skipReminderCycle');
    component.onSkipCycleChange(false);
    expect((component as any).skipReminderCycle).not.toHaveBeenCalled();
  });

  it('should update stats on updateNextCycleRemainderStats success', fakeAsync(() => {
    const details = {
      totalNumberOfReminders: 5,
      totalRemindersSent: 2,
      totalErrorAndSkippedReminders: 1,
      lastReminderSentDate: '2025-06-09T10:00:00Z',
      nextReminderScheduledDate: '2025-06-10T10:00:00Z'
    };
    repoService.getEmailReminderDetails.and.returnValue(of(details));
    component['updateNextCycleRemainderStats']('id');
    expect(repoService.getEmailReminderDetails).toHaveBeenCalledWith('id');

    // Allow async subscribe to complete
    tick();

    expect(component.totalReminders).toBe(5);
    expect(component.remindersSent).toBe(2);
    expect(component.totalErrorandSkips).toBe(1);
    expect(component.lastReminderDate).toContain('09 Jun 2025');
    expect(component.nextReminderDate).toContain('10 Jun 2025');
  }));

  it('should show error on updateNextCycleRemainderStats error', () => {
    repoService.getEmailReminderDetails.and.returnValue(throwError(() => ({ message: 'Error!' })));
    component['updateNextCycleRemainderStats']('id');
    expect(toastr.error).toHaveBeenCalledWith('Failed to load next cycle reminder details', '', { positionClass: 'toast-center-center' });
  });

  // Edge case: skipReminderCycle with undefined reminderId
  it('should handle skipReminderCycle with undefined reminderId', () => {
    component.reminderId = undefined as any;
    component['skipReminderCycle']();
    expect(toastr.error).toHaveBeenCalledWith('Reminder ID is required to skip cycle', '', { positionClass: 'toast-center-center' });
    expect(component.isSkipCycleEnabled).toBe(false);
  });

    it('should call skipReminderCycle when onSkipCycleChange is called with true', () => {
    spyOn(component as any, 'skipReminderCycle');
    component.onSkipCycleChange(true);
    expect((component as any).skipReminderCycle).toHaveBeenCalled();
  });

  it('should handle skipReminderCycle success', fakeAsync(() => {
    component.reminderId = '123';
    repoService.skipReminderCycle.and.returnValue(of({ message: 'Skipped!' }));
    spyOn(component as any, 'updateNextCycleRemainderStats');
    component['skipReminderCycle']();
    tick();
    expect(repoService.skipReminderCycle).toHaveBeenCalledWith({ reminderId: '123' });
    expect(toastr.success).toHaveBeenCalledWith('Skipped!', '', { positionClass: 'toast-center-center' });
    expect(component.isSkipCycleEnabled).toBeTrue();
    expect((component as any).updateNextCycleRemainderStats).toHaveBeenCalledWith('123');
  }));

  it('should handle skipReminderCycle error', fakeAsync(() => {
    component.reminderId = '123';
    repoService.skipReminderCycle.and.returnValue(throwError(() => ({ message: 'Failed!' })));
    component['skipReminderCycle']();
    tick();
    expect(toastr.error).toHaveBeenCalledWith('Failed!', '', { positionClass: 'toast-center-center' });
    expect(component.isSkipCycleEnabled).toBeFalse();
  }));

  it('should handle skipReminderCycle with empty reminderId', () => {
    component.reminderId = '';
    component['skipReminderCycle']();
    expect(toastr.error).toHaveBeenCalledWith('Reminder ID is required to skip cycle', '', { positionClass: 'toast-center-center' });
    expect(component.isSkipCycleEnabled).toBeFalse();
  });

  it('should handle updateNextCycleRemainderStats with missing fields', fakeAsync(() => {
    const partialDetails = { totalNumberOfReminders: 2 };
    repoService.getEmailReminderDetails.and.returnValue(of(partialDetails));
    component['updateNextCycleRemainderStats']('id');
    tick();
    expect(component.totalReminders).toBe(2);
    expect(component.remindersSent).toBeUndefined();
    expect(component.totalErrorandSkips).toBeUndefined();
    expect(component.lastReminderDate).toBe('NA');
    expect(component.nextReminderDate).toBe('NA');
  }));
});
