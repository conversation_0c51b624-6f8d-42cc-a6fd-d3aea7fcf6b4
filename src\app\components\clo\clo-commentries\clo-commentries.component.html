<div class="row mr-0 ml-0 clo-table-content">
  <div class="clo-table-body col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0 Heading2-M">
  <div class="row mr-0 ml-0 clo-table-content">
    <ng-container *ngFor="let clo of footnotes">
      <div class="custom-footnote col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pt-3 pb-3 pr-3 pl-3 clo-item TextTruncate"
        [ngClass]="clo.isExpanded ? 'clo-active':'clo-in-active'">
        <div title="{{clo.name}}" class="float-left TextTruncate Heading2-M">{{clo.name}}</div>
        <div class="float-right">
          <span *ngIf="clo.isExpanded" id="edit-footnote" (click)="toggleEdit(clo)" (keypress)="toggleEdit()">
            <img class="custom-size" alt="" src="assets/dist/images/clo_edit.svg" />
          </span>
          <a (click)="expandPanel(clo)">
            <img src="assets/dist/images/{{clo.isExpanded ? 'arrow-down.svg' :'chevron-down-i.svg'}}"
              alt="Sort left" />
          </a>
        </div>
      </div>
      <ng-container *ngIf="clo.isExpanded">
        <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0 child-add-item TextTruncate">
          <div class="textarea-container">
            <ng-container *ngIf="!clo.isEdit">
              <div>
                <ng-container *ngIf="clo.newComment; else noComment">
                  <app-custom-quill-editor [readOnly]="true" class="custom-quill-editor"  [noteText]="clo.newComment" [ngModel]="clo.newComment" [modules]="quillConfig"></app-custom-quill-editor>
                </ng-container>
                <ng-template #noComment>
                  <div class="empty-text Body-R pt-2 pl-3">N/A</div>
                </ng-template>
              </div>
            </ng-container>
            <ng-container *ngIf="clo.isEdit">
              <app-custom-quill-editor [editorPlaceholder]="editorPlaceholder" [(ngModel)]="clo.newComment"  [noteText]="clo.newComment"  [showCharCount]="true" class="custom-quill-editor"></app-custom-quill-editor>
              <div class="d-flex justify-content-between custom-quillcontainer pb-3 pl-4 pr-4 pt-3">
                <button id="btn-reset" class="btn TextTruncate btn-warning mr-2 TextTruncate" (click)="onReset(clo)">Clear</button>
                <div class="btn-controls float-right">
                  <button id="btn-cancel" (click)="onCancel(clo)" class="btn TextTruncate btn-warning mr-2 TextTruncate">Cancel</button>
                  <button id="btn-save" class="btn-save-clo btn btn-primary" (click)="onSave(clo)">Save</button>
                </div>
              </div>
            </ng-container>
          </div>
        </div>
      </ng-container>
    </ng-container>
  </div>
</div>
</div>