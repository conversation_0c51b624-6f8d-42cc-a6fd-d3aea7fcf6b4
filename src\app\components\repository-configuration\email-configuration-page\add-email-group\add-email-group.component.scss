@import "../../../../../variables.scss";

// Custom variables for pixel values
$border-radius-sm: 4px;
$spacing-md: 10px;
$input-height: 32px;
$container-height: calc(100vh - 321px);
$space-7: 7px;

// Styling for the email group component
.add-email-group-container {
    border: 1px solid $Neutral-Gray-10;
    border-radius: $border-radius-sm;
}

.create-new-group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid $nep-dark-b-title;

    .header-actions {
        display: flex;
        gap: $spacing-md; // Adjusted spacing between buttons       

        .btn-custom-width {
            min-width: 94px;
        }

    }
}

.create-new-group-content {

    .group-name-header {
        input.form-control {
            width: 100%;
            border: 1px solid $Neutral-Gray-10;
            border-radius: $border-radius-sm;
        }

        .required-field {
            color: $Negative-100;
        }
    }

    .companies-list-container {
        display: flex;
        flex-direction: column;
        height: $container-height;
        overflow: hidden;
        border-bottom: 1px solid $Neutral-Gray-10;
        border-radius: $border-radius-sm;

        /* Fixing the overflow issue by ensuring direct descendants maintain proper sizing */
        &>* {
            flex: 1;
            height: 100%;
            overflow: hidden;
        }
    }
    // Email list container styling    
    .email-list-container {
        border: 1px solid $Neutral-Gray-10;
        border-radius: $border-radius-sm;
        max-height: $container-height;
        height: 100%;
        overflow-y: auto;

        .email-list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .btn-add {
                background: none;
                border: none;
            }
        }
        .email-list-content {
            height: calc(100% - 40px); /* Subtract the height of the header */
            overflow: visible;
            
            .list-columns {
                display: flex;
                padding: 8px 15px;
                background-color: $Neutral-Gray-02;
                border: 1px solid $Neutral-Gray-05;

                .name-column {
                    width: 40%;
                }

                .email-column {
                    width: 40%;
                }
            }
            // Kendo Grid Styling
            .email-grid {
                border: none !important;
                height: auto !important; /* Allow grid to grow with content */

                // Input with clear button
                .input-with-clear {
                    position: relative;
                    display: flex;
                    align-items: center;

                    input {
                        width: 100%;
                        border: none;
                        height: $input-height;

                        &::placeholder {
                            color: $Neutral-Gray-30;
                        }
                    }

                    .btn-clear {
                        position: absolute;
                        right: $spacing-md;
                        background: none;
                        border: none;
                        cursor: pointer;
                        padding: 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }

                .btn-remove {
                    background: none;
                    border: none;
                    padding: 0;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto;
                }
            }
        }
    }

    // Company list component styles
    .company-list-component {
        width: 100%;
        height: 100%;
    }
}

.border-danger{
    border: 1px solid $Negative-100 !important;
}

.info-icon{
    margin-top: $space-7;    
}