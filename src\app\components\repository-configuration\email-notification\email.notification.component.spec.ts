import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EmailNotificationComponent } from './email.notification.component';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { DataIngestionService } from 'src/app/services/data-ingestion.service';
import { of, throwError } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ReminderModel } from '../model/config-model';

describe('EmailNotificationComponent', () => {
  let component: EmailNotificationComponent;
  let fixture: ComponentFixture<EmailNotificationComponent>;
  let mockRepositoryConfigService: jasmine.SpyObj<RepositoryConfigService>;
  let mockDataIngestionService: jasmine.SpyObj<DataIngestionService>;
  let mockToastrService: jasmine.SpyObj<ToastrService>;

  beforeEach(async () => {
    // Create spy objects for the services
    mockRepositoryConfigService = jasmine.createSpyObj('RepositoryConfigService', ['getPortfolioCompanies', 'deleteEmailReminder']);
    mockDataIngestionService = jasmine.createSpyObj('DataIngestionService', ['getDataExtractionTypes']);
    mockToastrService = jasmine.createSpyObj('ToastrService', ['error', 'info', 'success']);

    await TestBed.configureTestingModule({
      declarations: [ EmailNotificationComponent ],
      imports: [ ReactiveFormsModule ],
      providers: [
        FormBuilder,
        { provide: RepositoryConfigService, useValue: mockRepositoryConfigService },
        { provide: DataIngestionService, useValue: mockDataIngestionService },
        { provide: ToastrService, useValue: mockToastrService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(EmailNotificationComponent);
    component = fixture.componentInstance;
  });

  describe('fetchPortfolioCompanies', () => {
    it('should successfully fetch portfolio companies', () => {
      // Mock data to return from service
      const mockCompanies = [
        { portfolioCompanyID: 1, companyName: 'Company A' },
        { portfolioCompanyID: 2, companyName: 'Company B' }
      ];

      // Setup the spy to return the mock data
      mockRepositoryConfigService.getPortfolioCompanies.and.returnValue(of(mockCompanies));

      // Call the method
      component.fetchPortfolioCompanies();

      // Assertions
      expect(mockRepositoryConfigService.getPortfolioCompanies).toHaveBeenCalled();      expect(component.portfolioCompanies.length).toBe(2);
      expect(component.portfolioCompanies[0].companyId).toBe(1);
      expect(component.portfolioCompanies[0].companyName).toBe('Company A');
    });

    it('should handle error when fetching portfolio companies fails', () => {
      // Setup spy to throw an error
      mockRepositoryConfigService.getPortfolioCompanies.and.returnValue(
        throwError(() => new Error('Test error'))
      );
      
      // Spy on console.error
      spyOn(console, 'error');

      // Call the method
      component.fetchPortfolioCompanies();

      // Assertions
      expect(mockRepositoryConfigService.getPortfolioCompanies).toHaveBeenCalled();
      expect(console.error).toHaveBeenCalled();
      expect(component.portfolioCompanies.length).toBe(0); // Should remain empty
    });
  });

  describe('loadDocumentTypes', () => {
    it('should successfully load document types', () => {
      // Mock data to return from service
      const mockDocTypes = [
        { id: 1, documentName: 'Type A' },
        { id: 2, documentName: 'Type B' }
      ];

      // Setup the spy to return the mock data
      mockDataIngestionService.getDataExtractionTypes.and.returnValue(of(mockDocTypes));

      // Call the method
      component.loadDocumentTypes();

      // Assertions
      expect(mockDataIngestionService.getDataExtractionTypes).toHaveBeenCalled();
      expect(component.documentTypes.length).toBe(2);      expect(component.documentTypes[0].id).toBe(1);
      expect(component.documentTypes[0].documentName).toBe('Type A');
    });

    it('should handle error when loading document types fails', () => {
      // Setup spy to throw an error
      mockDataIngestionService.getDataExtractionTypes.and.returnValue(
        throwError(() => new Error('Test error'))
      );

      // Call the method
      component.loadDocumentTypes();

      // Assertions
      expect(mockDataIngestionService.getDataExtractionTypes).toHaveBeenCalled();
      expect(component.documentTypes).toEqual([]);
    });
  });

  describe('deleteCLO', () => {
    function createReminderModel(id: number, reminderId: string): ReminderModel {
      return {
        id,
        reminderId,
        companies: [],
        documentType: { id: 1, name: 'DocType' },
        documentTypes: [],
        subject: 'Test',
        date: '2024-01-01',
        isActive: true,
        isExpanded: false
      };
    }

    beforeEach(() => {
      // Reset spies for each test
      try {
        if (mockRepositoryConfigService.deleteEmailReminder && typeof mockRepositoryConfigService.deleteEmailReminder.and === 'object' && mockRepositoryConfigService.deleteEmailReminder.and !== undefined && mockRepositoryConfigService.deleteEmailReminder.calls) {
          mockRepositoryConfigService.deleteEmailReminder.calls.reset();
        }
      } catch {}
      try {
        if (mockToastrService.success && typeof mockToastrService.success.and === 'object' && mockToastrService.success.and !== undefined && mockToastrService.success.calls) {
          mockToastrService.success.calls.reset();
        }
      } catch {}
      try {
        if (mockToastrService.error && typeof mockToastrService.error.and === 'object' && mockToastrService.error.and !== undefined && mockToastrService.error.calls) {
          mockToastrService.error.calls.reset();
        }
      } catch {}
    });

    it('should delete the reminder and show success toast on success', () => {
      // Arrange
      component.reminderIdToDelete = 1;
      const reminder = createReminderModel(1, 'abc-123');
      const otherReminder = createReminderModel(2, 'def-456');
      component.reminderData = [reminder, otherReminder];
      mockRepositoryConfigService.deleteEmailReminder.and.returnValue(of({}));

      // Act
      component.deleteEmailReminder();

      // Assert
      expect(mockRepositoryConfigService.deleteEmailReminder).toHaveBeenCalledWith('abc-123');
      expect(mockToastrService.success).toHaveBeenCalledWith('Email reminder deleted successfully!', '', { positionClass: 'toast-center-center' });
      expect(component.reminderData.length).toBe(1);
      expect(component.reminderData[0].id).toBe(2);
      expect(component.reminderIdToDelete).toBeNull();
      expect(component.confirmDelete).toBeFalse();
    });

    it('should show error toast and call cancelDelete on error', () => {
      // Arrange
      component.reminderIdToDelete = 1;
      const reminder = createReminderModel(1, 'abc-123');
      component.reminderData = [reminder];
      mockRepositoryConfigService.deleteEmailReminder.and.returnValue(throwError(() => new Error('Delete failed')));
      spyOn(component, 'cancelDelete').and.callThrough();

      // Act
      component.deleteEmailReminder();

      // Assert
      expect(mockRepositoryConfigService.deleteEmailReminder).toHaveBeenCalledWith('abc-123');
      expect(mockToastrService.error).toHaveBeenCalledWith('Failed to delete email reminder', '', { positionClass: 'toast-center-center' });
      expect(component.cancelDelete).toHaveBeenCalled();
    });

    it('should call cancelDelete if reminder not found', () => {
      // Arrange
      component.reminderIdToDelete = 99;
      const reminder = createReminderModel(1, 'abc-123');
      component.reminderData = [reminder];
      spyOn(component, 'cancelDelete').and.callThrough();

      // Act
      component.deleteEmailReminder();

      // Assert
      expect(component.cancelDelete).toHaveBeenCalled();
    });

    it('should not call cancelDelete if reminderIdToDelete is null', () => {
      // Arrange
      component.reminderIdToDelete = null;
      spyOn(component, 'cancelDelete').and.callThrough();

      // Act
      component.deleteEmailReminder();

      // Assert
      expect(component.cancelDelete).not.toHaveBeenCalled();
    });
  });

  describe('cancelDelete', () => {
    it('should reset delete modal state', () => {
      // Arrange
      component.confirmDelete = true;
      component.reminderIdToDelete = 5;
      component.deletedCloName = 'Test';
      component.activeRemindersLeft = 2;

      // Act
      component.cancelDelete();

      // Assert
      expect(component.confirmDelete).toBeFalse();
      expect(component.reminderIdToDelete).toBeNull();
      expect(component.deletedCloName).toBe('');
      expect(component.activeRemindersLeft).toBe(0);
    });
  });

  describe('ngOnInit', () => {
    it('should call fetchPortfolioCompanies, loadDocumentTypes, and loadEmailReminders', () => {
      spyOn(component, 'fetchPortfolioCompanies');
      spyOn(component, 'loadDocumentTypes');
      spyOn(component, 'loadEmailReminders');
      component.ngOnInit();
      expect(component.fetchPortfolioCompanies).toHaveBeenCalled();
      expect(component.loadDocumentTypes).toHaveBeenCalled();
      expect(component.loadEmailReminders).toHaveBeenCalled();
    });
  });

  describe('toggleReminderExpand', () => {
    it('should toggle expanded state and fetch details if expanding', () => {
      component.reminderData = [{ id: 1, reminderId: 'uuid', companies: [], documentType: { id: 1, name: 'DocType' }, documentTypes: [], subject: '', date: '', isActive: true, isExpanded: false }];
      spyOn(component, 'fetchReminderDetails');
      component.toggleReminderExpand(1);
      expect(component.expandedReminders[1]).toBeTrue();
      expect(component.fetchReminderDetails).toHaveBeenCalledWith('uuid');
    });
  });

  describe('onStatusChange', () => {
    it('should show info toast with correct status', () => {
      component.reminderData = [{ id: 1, reminderId: 'uuid', companies: [], documentType: { id: 1, name: 'DocType' }, documentTypes: [], subject: '', date: '', isActive: true, isExpanded: false }];
      component.reminderStatus[1] = true;
      component.onStatusChange(1);
      expect(mockToastrService.info).toHaveBeenCalled();
    });
  });

  describe('Selection logic', () => {
    it('onPortfolioCompanySelectionChange should update isPortfolioCompanyCheckAll', () => {
      component.portfolioCompanies = [{}, {}];
      component.selectedPortfolioCompanies = [{}, {}];
      component.onPortfolioCompanySelectionChange();
      expect(component.isPortfolioCompanyCheckAll).toBeTrue();
    });

    it('onSelectAllPortfolioCompanies should select all', () => {
      component.portfolioCompanies = [{}, {}];
      component.onSelectAllPortfolioCompanies();
      expect(component.selectedPortfolioCompanies.length).toBe(2);
    });

    it('isPortfolioCompanyIndet should return true for partial selection', () => {
      component.portfolioCompanies = [{}, {}];
      component.selectedPortfolioCompanies = [{}];
      expect(component.isPortfolioCompanyIndet()).toBeTrue();
    });    
  });

  describe('cancel', () => {
    it('should reset selections and check-all flags', () => {
      component.selectedPortfolioCompanies = [{}];
      component.selectedDocumentTypes = [{}];
      component.isPortfolioCompanyCheckAll = true;
      component.isDocumentTypeCheckAll = true;
      component.cancel();
      expect(component.selectedPortfolioCompanies).toEqual([]);
      expect(component.selectedDocumentTypes).toEqual([]);
      expect(component.isPortfolioCompanyCheckAll).toBeFalse();
      expect(component.isDocumentTypeCheckAll).toBeFalse();
    });
  });

  describe('setupEmailReminder', () => {
    it('should call API and navigate on success', () => {
      component.selectedPortfolioCompanies = [{ companyId: 1 }];
      component.selectedDocumentTypes = [{ id: 2 }];
      mockRepositoryConfigService.createEmailRemainder = jasmine.createSpy().and.returnValue(of({ reminderId: 'abc' }));
      const routerSpy = spyOn(component['router'], 'navigate');
      component.setupEmailReminder();
      expect(mockRepositoryConfigService.createEmailRemainder).toHaveBeenCalled();
      expect(routerSpy).toHaveBeenCalledWith(['/email-reminder/abc']);
    });

    it('should show error on failure', () => {
      component.selectedPortfolioCompanies = [{ companyId: 1 }];
      component.selectedDocumentTypes = [{ id: 2 }];
      mockRepositoryConfigService.createEmailRemainder = jasmine.createSpy().and.returnValue(throwError(() => new Error('fail')));
      component.setupEmailReminder();
      expect(mockToastrService.error).toHaveBeenCalled();
    });
  });

  describe('editReminder', () => {
    it('should navigate to edit page', () => {
      component.reminderData = [{ id: 1, reminderId: 'abc', companies: [], documentType: { id: 1, name: 'DocType' }, documentTypes: [], subject: '', date: '', isActive: true, isExpanded: false }];
      const routerSpy = spyOn(component['router'], 'navigate');
      component.editReminder(1);
      expect(routerSpy).toHaveBeenCalledWith(['/email-reminder-edit/abc']);
    });
  });

  describe('deleteReminder', () => {
    it('should set modal state for deletion', () => {
      component.reminderData = [{ id: 1, reminderId: 'abc', companies: [], documentType: { id: 1, name: 'DocType' }, documentTypes: [], subject: '', date: '', isActive: true, isExpanded: false }];
      component.deleteReminder(1);
      expect(component.confirmDelete).toBeTrue();
      expect(component.reminderIdToDelete).toBe(1);
    });
  });
});
