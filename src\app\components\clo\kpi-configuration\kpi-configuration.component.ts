import { Component, OnInit } from '@angular/core';
import { PanelbarItemService } from '../shared/panelbar-item/panelbar-item.service';

@Component({
  selector: 'app-kpi-configuration',
  templateUrl: './kpi-configuration.component.html',
  styleUrls: ['./kpi-configuration.component.scss']
})
export class KpiConfigurationComponent implements OnInit {
  selectedCategory: string = '';
  selectedSubCategory: string = '';

  categories = [
    { id: '1', name: 'Option 1' },
    { id: '2', name: 'Option 2' }
  ];

  subCategories = [
    { id: '1', name: 'Option 1' },
    { id: '2', name: 'Option 2' }
  ];
  compnayList: any;
  selectedCompanyItem: any = null;
  selectedTableItem: any = null;
  CloTableList: any;
  // selectedTableItem: any;
  constructor(private readonly panelbarItemService: PanelbarItemService) {}

  ngOnInit() {
    this.getInvestCompanies();
    this.getCLOTableDetails();
    // Initialize your dropdowns here
  }
  getInvestCompanies(){
    this.panelbarItemService.getInvestCompanyListForKPI().subscribe((data) => {
        if(data.length == 0){
          this.compnayList = undefined;
          return;
        }
        this.compnayList=data;
        this.panelbarItemService.setCompany(this.selectedCompanyItem);       
    });
  }
  getCLOTableDetails(){
    this.panelbarItemService.getCLOTableDetails().subscribe((data) => {
        if(data.length == 0){
          this.CloTableList = undefined;
          return;
        }
        this.CloTableList=data;
    });
  }
  onCategoryChange(event: any) {
    this.selectedCategory = event.target.value;
    // Add logic to update sub-categories based on selected category
  }

  onSubCategoryChange(event: any) {
    this.selectedSubCategory = event.target.value;
  }
}
