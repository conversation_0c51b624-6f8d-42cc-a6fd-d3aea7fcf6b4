@import "../../../../variables";
.cap-table-section
{
    .excel-load{
        color: $nep-primary !important;
    }
    .headerfontsize
    {
        img{
            margin-top: -4px !important;
        }
    }
    .cap-header
    {
        color: $nep-dark-black;
    }
    .outer-section
    {
        border: 1px solid $nep-divider;
        border-radius: 0.25rem;
        .panel-default
        {
            border-radius:0.25rem !important;
        }
        .period-bg
        {
            background: $nep-white;
            padding: 0rem 1rem;
            .period-title
            {
                color: $nep-dark-light;
                padding: 0.75rem 1rem 0.625rem 1rem;
            }
        }
        .QMY_Container{
            background: $nep-white 0% 0% no-repeat padding-box;
            border: 1px solid $nep-divider;
            border-radius: 4px;
            opacity: 1;
            cursor: pointer;
        }
        .headerSize {
            font-size: 1.5rem;
            padding-top:4px;
        }
        .QMY_Text{
            text-align: left;
            font-size: 14px;
            letter-spacing: 0px;
            color: $nep-text-grey;
            opacity: 1;
        }
        .QMYStyle{
            position: relative;
            top: -0.25rem;
            padding: 0.2rem 0.75rem 0.2rem 0.75rem;
            border-radius: 4px;
        }
        .MStyle{
            margin-left: 0.25rem !important;
        }
        .YStyle{
            margin-right: 0.25rem !important;
        }
        
        .activeQMY{
            background: $nep-button-hover-color 0% 0% no-repeat padding-box !important;
            color: $nep-primary !important;
        }
        .allvalues{
           color:$nep-text-grey !important;
           padding-top: 0.375rem;
           padding-left: 1rem;
        }

    }
}
.filter-footer {
    padding-top: 0.75rem;
    float: right;
}
.btn-reset {
    color: $nep-primary;
    background-color: $nep-white;
    border-color: $nep-primary !important;
    border-radius: 4px;
    width: 76px;
    height: 24px;
    text-align: center;
    font-size: 0.75rem;
    border: 1px solid $nep-primary;
    padding-bottom: 0.063rem !important;
}
.btn-app {
    color: $nep-white;
    background-color: $nep-primary;
    border-color: $nep-white !important;
    border-radius: 4px;
    width: 76px;
    height: 24px;
    text-align: center;
    font-size: 0.75rem;
    border: 1px solid $nep-primary !important;
    padding-bottom: 0.063rem !important;
}
.w100Percent{
    width: 100% !important;
}
.comm-footnote{
  position: relative !important;
  z-index: 1;
}