import { Component, OnInit, Input, OnChanges, SimpleChanges } from '@angular/core';
import { DialogService } from '@progress/kendo-angular-dialog';
import { ToastrService } from 'ngx-toastr';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { 
  UserInfo, 
  DocumentTypeDto, 
  UserInformationResponseDto, 
  CreateUserInformationDto, 
  DropdownItem,
  UpdateUserInformationDto 
} from '../../../repository-configuration/model/config-model';

@Component({
  selector: 'app-email-configuration-users',
  templateUrl: './email-configuration-users.component.html',
  styleUrls: ['./email-configuration-users.component.scss']
})
export class EmailConfigurationUsersComponent implements OnInit, OnChanges {
  public userInfoItems: UserInfo[] = [];
  public isLoading: boolean = false;
  public selectedDocumentTypes: [];
  isDocumentTypeCheckAll: boolean = false;
  showDeletePopup: boolean = false
  @Input() selectedCompanies: any[] = [];

  // Side overlay properties
  public showAddUser: boolean = false;
  public newUserInfo: UserInfo = this.initializeNewUserInfo();
  public hasError: boolean = false;
  public errorMessage: string = '';
  public selectedDocTypes: any[] = [];
  public selectedCategory: any = null;
  public selectedRecipient: any = null;

  // Form validation
  public formValid: boolean = false;

  // Edit mode properties
  public isEditMode: boolean = false;
  public currentUserInfoId: number = 0;

  // Dropdown data
  public categories: any[] = [];
  public documentTypes: any[] = [];

  public recipientTypes: DropdownItem[] = [
    { text: 'To', value: 1 },
    { text: 'CC', value: 2 }
  ];
  selectedUserInfo: UserInfo;
  confirmationDialogContent : string = "This user info will be deleted permanently from your list";
  deleteConfirmationModalTitle : string = "Delete Confirmation";
  constructor(
    private dialogService: DialogService,
    private toastrService: ToastrService,
    private readonly repositoryConfigService: RepositoryConfigService,
  ) {
  }
  ngOnInit(): void {
    if (this.selectedCompanies?.length > 0) {
      this.loadUserInformation();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.selectedCompanies &&
      this.selectedCompanies?.length > 0) {

      // Load user info for selected company
      this.loadUserInformation();
      this.showAddUser = false;
    }
  }

  /**
   * Load user information for the selected company
   */
  private loadUserInformation(): void {
    if (!this.selectedCompanies || this.selectedCompanies.length === 0) {
      return;
    }

    this.isLoading = true;
    const companyId = this.selectedCompanies[0].companyId;

    this.repositoryConfigService.getUserInformationByCompany(companyId).subscribe({
      next: (response) => {
        // Map the API response to the grid format
        this.userInfoItems = response.map((item: UserInformationResponseDto) => {
          // Join document names with comma for display
          const documentTypes = item.documentTypes?.map(dt => dt.documentName).join(',') || '';

          return {
            id: item.userInformationID,
            employeeName: item.name || '',
            category: item.category,
            documentType: documentTypes,
            recipientType: item.recipient,
            email: item.email
          };
        });
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading user information:', error);
        this.isLoading = false;
      }
    });
  }

  /**
   * Initialize a new empty user info object
   */
  private initializeNewUserInfo(): UserInfo {
    return {
      employeeName: '',
      category: '',
      documentType: '',
      recipientType: '',
      email: ''
    };
  }
  
  /**
   * Show the add user overlay
   */
  public showAddUserOverlay(): void {
    this.showAddUser = true;
    this.isEditMode = false;
    this.currentUserInfoId = 0;
    this.newUserInfo = this.initializeNewUserInfo();
    this.selectedDocTypes = [];
    this.selectedCategory = null;
    this.selectedRecipient = null;
    this.formValid = false;
    this.loadCategoriesAndDocumentTypes();

    this.clearError();
  }
  /**
   * Load categories and document types
   * @returns Promise that resolves when data is loaded
   */
  public loadCategoriesAndDocumentTypes(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.repositoryConfigService.getCategoriesAndDocumentTypes(this.selectedCompanies[0].companyId).subscribe({
        next: (result) => {
          this.categories = result.categories;
          this.documentTypes = result.documentTypes;
          resolve();
        },
        error: (error) => {
          this.toastrService.error('Failed to get data collection', "", {
            positionClass: "toast-center-center",
          });
          reject(error);
        }
      });
    });
  }

  /**
   * Cancel and close the add user overlay
   */
  public cancelAddUser(): void {
    this.showAddUser = false;
    this.clearError();
  }


  /**
   * Clear error message
   */
  public clearError(): void {
    this.hasError = false;
    this.errorMessage = '';
  }  /**
   * Check if form is valid for submission
   */
  public isFormValid(): boolean {
    return !!(
      // Removed name validation check - name can be empty
      this.selectedCategory &&
      this.selectedDocTypes && this.selectedDocTypes.length > 0 &&
      this.selectedRecipient &&
      this.validateEmail(this.newUserInfo.email)
    );
  }

  /**
   * Check form validity and update formValid flag
   */
  public checkFormValidity(): void {
    this.formValid = this.isFormValid();
  }

  /**
   * Validate email format
   */
  private validateEmail(email: string): boolean {
    if (!email) {
      return false;
    }

    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
    return emailRegex.test(email);
  }
  /**
   * Add new user information
   */
  public addNewUserInfo(): void {
    this.checkFormValidity();
    if (!this.formValid) {
      if (!this.validateEmail(this.newUserInfo.email)) {
        this.hasError = true;
        this.errorMessage = 'Please enter a valid email address';
      }
      return;
    }    // Create data to send to API
    const documentTypeIds: number[] = this.selectedDocTypes.map(dt => dt.documentTypeID);

    const userInfoData: CreateUserInformationDto = {
      entityId: this.selectedCompanies[0].companyId,
      name: this.newUserInfo.employeeName || null, // Allow null for empty name
      email: this.newUserInfo.email,
      categoryId: this.selectedCategory.categoryId,
      recipient: this.selectedRecipient.text,
      documentTypeIds: documentTypeIds
    };

    this.isLoading = true;
    // Call the API to create user information
    this.repositoryConfigService.createUserInformation(userInfoData).subscribe({
      next: (response) => {
        // Handle the new response structure
        const result = response?.userInformationId;
        if (result?.isSuccess) {
          this.toastrService.success(result.message, "", { positionClass: "custom-toast" });
          this.loadUserInformation();
          this.cancelAddUser();
          this.isLoading = false;
        }

        if (!result?.isSuccess) {
          this.toastrService.error(result.message, "", { positionClass: "custom-toast" });
        }
      },
      error: (error) => {
        console.error('Error creating user information', error);
        this.toastrService.error('Failed to add user information');
        this.isLoading = false;
      }
    });
  }

  /**
   * Update existing user information
   */
  public updateUserInfo(): void {
    this.checkFormValidity();
    if (!this.formValid) {
      if (!this.validateEmail(this.newUserInfo.email)) {
        this.hasError = true;
        this.errorMessage = 'Please enter a valid email address';
      }
      return;
    }

    // Create data to send to API
    const documentTypeIds: number[] = this.selectedDocTypes.map(dt => dt.documentTypeID);

    const updateData: UpdateUserInformationDto = {
      userInformationID: this.currentUserInfoId,
      entityId: this.selectedCompanies[0].companyId,
      name: this.newUserInfo.employeeName || null, // Allow null for empty name
      email: this.newUserInfo.email,
      categoryId: this.selectedCategory.categoryId,
      recipient: this.selectedRecipient.text,
      documentTypeIds: documentTypeIds
    };    this.isLoading = true;
    // Call the API to update user information
    this.repositoryConfigService.updateUserInformation(updateData).subscribe({
  next: (response) => {
    if (response?.success) {
      this.toastrService.success(response.message, "", { positionClass: "custom-toast" });
      this.loadUserInformation();
      this.cancelAddUser();
      this.isEditMode = false;
      this.currentUserInfoId = 0;
    } else {
      this.toastrService.error(response?.message, "", { positionClass: "custom-toast" });
    }
    this.isLoading = false;
  },
  error: (error) => {
    const backendMsg = error?.error?.message;
    this.toastrService.error(backendMsg ,'', { positionClass: "custom-toast" });
    this.isLoading = false;
  }
});
  }

  /**
   * Tag mapper for document type multiselect
   * For multiple selected items, return a single tag with count
   */
  public tagMapper(tags: any[]): any[] {
    if (tags.length === 0) return [];

    // For multiple selected items, return a single tag with count
    if (tags.length > 1) {
      return [{
        documentName: `${tags.length} Items Selected`,
        documentTypeID: 'multiple',
        isMultiple: true
      }];
    }

    // For a single item, return the item as is
    return tags;
  }  /**
   * Handle select all for document types
   */
  public onSelectAllDocumentTypes() {
    this.isDocumentTypeCheckAll = !this.isDocumentTypeCheckAll;
    this.selectedDocTypes = this.isDocumentTypeCheckAll ? this.documentTypes : [];
  }

  /**
   * Check if some but not all document types are selected
   */
  public isDocumentTypeIndet() {
    let isIndet = this.selectedDocTypes?.length !== 0 &&
      this.selectedDocTypes?.length !== this.documentTypes?.length;
    if (this.isDocumentTypeCheckAll != isIndet) {
      this.isDocumentTypeCheckAll = !isIndet;
    }
    return isIndet;
  }
  /**
   * Handle document type selection change
   */
  public onDocumentTypeSelectionChange(event: any): void {
    if (!event) {
      return;
    }

    this.isDocumentTypeCheckAll = this.selectedDocTypes.length === this.documentTypes.length;

    // Update the selected document types
    this.selectedDocTypes = event;
    this.checkFormValidity();
  }
  /**
   * Cancel adding document
   */
  public cancelAddDoc(): void {
    this.isEditMode = false;
    this.currentUserInfoId = 0;
    this.cancelAddUser();
  }
  /**
   * Add new document type or update existing one
   */
  public addNewDocumentType(): void {
    if (this.isEditMode) {
      this.updateUserInfo();
    } else {
      this.addNewUserInfo();
    }
  }
  /**
   * Get additional document types for tooltip display
   * @param documentType Comma-separated string of document types
   * @returns Array of additional document types (excluding the first one)
   */
  public getAdditionalDocumentTypes(documentType: string): string[] {
    if (!documentType) {
      return [];
    }
    const types = documentType.split(',');
    return types.length > 1 ? types.slice(1) : [];
  }

  /**
   * Edit user information
   * @param dataItem The user information to edit
   */
  public editUserInfo(dataItem: UserInfo): void {
    this.isEditMode = true;
    this.currentUserInfoId = dataItem.id;
    this.showAddUser = true;

    // Wait for categories and document types to load before loading user info
    this.loadCategoriesAndDocumentTypes().then(() => {
      this.repositoryConfigService.getUserInformationById(dataItem.id).subscribe({
        next: (response) => {
          // Set the form data
          this.newUserInfo = {
            employeeName: response.name || '',
            category: response.category,
            documentType: response.documentTypes.map((dt: any) => dt.documentName).join(','),
            recipientType: response.recipient,
            email: response.email
          };

          // Set selected category
          this.selectedCategory = this.categories.find(cat => cat.category === response.category);

          // Set selected recipient type
          this.selectedRecipient = this.recipientTypes.find(rt => rt.text === response.recipient);

          // Set selected document types
          this.selectedDocTypes = response.documentTypes.map((dt: any) => {
            return this.documentTypes.find(docType => docType.documentTypeID === dt.documentTypeID);
          }).filter(item => item !== undefined);

          // Check form validity
          this.checkFormValidity();
        },
        error: (error) => {
          console.error('Error loading user information for edit:', error);
          this.toastrService.error('Failed to load user information for editing');
        }
      });
    });
  }

  /**
   * Delete user information by ID
   */
  public deleteUserInfo(dataItem: UserInfo): void {
    if (dataItem && !dataItem.id) {
      return;
    }
    this.selectedUserInfo = dataItem;
    this.showDeletePopup = true;    
  }

  public triggerDeleteAction() : void {
    this.showDeletePopup = false;
    this.isLoading = true;
    this.repositoryConfigService.deleteUserInformation(this.selectedUserInfo.id).subscribe({
      next: (response) => {
        if (response?.isSuccess) {
          this.toastrService.success(response.message || 'User deleted successfully', '', { positionClass: 'custom-toast' });
          this.loadUserInformation();
        } else {
          this.toastrService.error(response?.message || 'Failed to delete user', '', { positionClass: 'custom-toast' });
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastrService.error(error.error?.message || 'Failed to delete user', '', { positionClass: 'custom-toast' });
        this.isLoading = false;
      }
    });
  }
  
  public cancelDelete() : void {
     this.selectedUserInfo = null;
    this.showDeletePopup = false;    
  }
}