import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from "@angular/core";
import { HelperService } from "src/app/services/helper.service";
import { ToastrService } from "ngx-toastr";
import { CellEditConstants, GlobalConstants, KpiInfo, KpiModuleAlias } from "src/app/common/constants";
import { FileExtension } from "../../../common/enums";
import { Audit, DataAuditLogValueModel, DocumentModel, MappedDocuments, ModuleCompanyModel, TableHeader, kpiValueModel, UpdatedFiles } from '../kpi-cell-edit/kpiValueModel';
import { getCellEditTabName, extractDateComponents, ModelToFormData } from '../kpi-cell-edit/cell-edit-utils';
import { AuditService } from "src/app/services/audit.service";
import { generateSecureId } from "src/app/utils/utils";
@Component({
  selector: 'app-kpi-cell-edit',
  templateUrl: './kpi-cell-edit.component.html',
  styleUrls: ['../all-upload-popup/all-popup-bulk-upload.component.scss', './kpi-cell-edit.component.scss']
})
export class KpiCellEditComponent implements OnInit {
  @ViewChild("fileDropRef") fileDropEl: ElementRef;
  files: any = [];
  existingFiles: DocumentModel[] = [];
  FileExtension = FileExtension;
  @Output() cancelButtonEvent: EventEmitter<any> = new EventEmitter();
  @Output() confirmButtonEvent: EventEmitter<any> = new EventEmitter();
  @Input() moduleCompanyModel: ModuleCompanyModel;
  @Input() dataRow: object;
  @Input() tableColumns: TableHeader;
  @Input() kpiType: string;
  isLoader: boolean = false;
  kpiModel: kpiValueModel;
  kpiInfo: string;
  kpi: string;
  auditLogFilter: Audit;
  updateDataAuditLogValueModel: DataAuditLogValueModel;
  updatedFiles: UpdatedFiles[] = [];
  removeSupportingDocument = [];
  infoExtension=KpiInfo;
  mappingId:number;
  previousComments:string = '';
  isColumnField:boolean=false;
  constructor(
    private helperService: HelperService,
    private auditService: AuditService,
    public toasterService: ToastrService
  ) {
    this.auditLogFilter = <Audit>{};
    this.auditLogFilter.valueTypeId = 0;
    this.auditLogFilter.periodId = 0;
    this.auditLogFilter.columnKpiId = 0;
    this.kpiModel = <kpiValueModel>{};
    this.updateDataAuditLogValueModel = <DataAuditLogValueModel>{};
    this.updateDataAuditLogValueModel.valueTypeId = 0;
    this.updateDataAuditLogValueModel.periodId = 0;
    this.updateDataAuditLogValueModel.columnKpiId = 0;
    this.updateDataAuditLogValueModel.columnNumber = 0;
  }
  ngOnInit() {
    this.isColumnField=false;
    this.initializeKpiInfo();
    this.initializeAuditLogFilter();
    this.initializeKpiModel();
    this.initializeUpdateDataAuditLogValueModel();
  }
  /**
   * Initializes the audit log filter and retrieves supporting comments data.
   */
  initializeAuditLogFilter() {
    let periodInput = this.tableColumns.field;
    periodInput = this.setCapTableColumKpiPeriodInput(periodInput);
    const dateComponents = extractDateComponents(periodInput);
    this.auditLogFilter = <Audit>{
      valueType: this.moduleCompanyModel.valueType,
      kpiId: this.dataRow["LineItemId"] ? this.dataRow["LineItemId"]:this.dataRow["KpiId"],
      mappingId: this.dataRow["MappingId"],
      quarter: dateComponents.quarter,
      year: dateComponents.year,
      month: dateComponents.month,
      moduleId: this.moduleCompanyModel.moduleId,
      companyId: this.moduleCompanyModel.companyId,
     
    };
    if (this.kpiType == CellEditConstants.CapTable) {
      this.capTableInitialize();
    }
    this.auditService.getPortfolioEditSupportingCommentsData(this.auditLogFilter).subscribe({
      next: (data: MappedDocuments) => {
        this.handleNext(data)
      },
      error: (error: any) => {
      },
    });
  }
  handleNext(data: MappedDocuments) {
    this.updateDataAuditLogValueModel.attributeID = data.valueId || 0;
    this.updateDataAuditLogValueModel.commentId = data.commentId || 0;
    this.updateDataAuditLogValueModel.documentId = data.documentId || 0;
    this.kpiModel.comments = data.comments || '';
    this.previousComments = data.comments || '';
    this.existingFiles = data.documentModels || [];
    this.existingFiles = this.existingFiles.map(file => ({ ...file, isExisting: true }));
    this.updateDataAuditLogValueModel.supportingDocumentsId = data.supportingDocumentsId || null;
    this.mappingId = data.mappingId||0;
  }
  /**
   * Sets the CapTable KPI information based on the table columns and data row.
   */
  setCapTableKpiInfo(){
    this.kpiInfo =
    this.tableColumns[CellEditConstants.KPIInfo] == null
      ? this.dataRow[CellEditConstants.KPIInfoSpace] || ""
      : this.tableColumns[CellEditConstants.KPIInfo] || "";
    if (
      this.tableColumns[CellEditConstants.KPIInfo] != null &&
      this.dataRow[CellEditConstants.CapTableIsOverrule]
    ) {
      this.kpiInfo = this.dataRow[CellEditConstants.KPIInfoSpace];
    }
    if (this.tableColumns[CellEditConstants.KPIInfo] != null) {
      this.kpi = this.tableColumns.field;
    }
  }
  /**
   * Initializes the KPI information.
   */
  initializeKpiInfo() {
    this.kpi = this.dataRow[CellEditConstants.Kpi] || "";
    switch (this.kpiType) {
        case CellEditConstants.CapTable:
            this.setCapTableKpiInfo();
            break;
        case CellEditConstants.CompanyKPIs: case CellEditConstants.OperationalKPIs: case CellEditConstants.ImpactKPIs: case CellEditConstants.InvestmentKPIs: 
        case KpiModuleAlias.Credit: case KpiModuleAlias.TradingRecords: case KpiModuleAlias.CustomTable1:case KpiModuleAlias.CustomTable2:case KpiModuleAlias.CustomTable3:case KpiModuleAlias.CustomTable4:
        case KpiModuleAlias.OtherKPI1:case KpiModuleAlias.OtherKPI2:case KpiModuleAlias.OtherKPI3:case KpiModuleAlias.OtherKPI4:case KpiModuleAlias.OtherKPI5:
        case KpiModuleAlias.OtherKPI6:case KpiModuleAlias.OtherKPI7:case KpiModuleAlias.OtherKPI8:case KpiModuleAlias.OtherKPI9:case KpiModuleAlias.OtherKPI10:
          this.kpiInfo = this.dataRow[CellEditConstants.KPIInfoSpace] || "";
          this.kpi = this.dataRow[CellEditConstants.Kpi.toUpperCase()] || "";
            break;
        default:
            this.kpiInfo = this.dataRow[CellEditConstants.KpiInfo] || "";
            break;
    }
}

  /**
   * Initializes the KPI model based on the KPI info and data row.
   * If the KPI info is not "Text", sets the old value of the KPI model to the value of the data row's table column,
   * or 'NA' if the value is null or undefined.
   * If the KPI info is "Text", sets the old text of the KPI model to the value of the data row's table column,
   * or 'NA' if the value is null or undefined.
   */
  initializeKpiModel() {
    const value = (this.dataRow[this.tableColumns.field] !== null && this.dataRow[this.tableColumns.field] !== undefined && this.dataRow[this.tableColumns.field] !== '') ? this.dataRow[this.tableColumns.field] : 'NA';
    if (this.kpiInfo != CellEditConstants.TextInfo) {
      this.kpiModel.oldValue = value;
    } else {
      this.kpiModel.oldText = value;
    }
  }
  /**
   * Initializes the update data audit log value model.
   * Sets the values of the updateDataAuditLogValueModel properties based on the current data row and module company model.
   */
  initializeUpdateDataAuditLogValueModel() {
    this.updateDataAuditLogValueModel.oldValue = this.dataRow[this.tableColumns.field];
    this.updateDataAuditLogValueModel.moduleId = this.moduleCompanyModel.moduleId;
    this.updateDataAuditLogValueModel.kpiId = this.dataRow["LineItemId"] != undefined ? this.dataRow["LineItemId"] : this.dataRow["KpiId"];
    this.updateDataAuditLogValueModel.valueType = getCellEditTabName(this.moduleCompanyModel.valueType);
    this.updateDataAuditLogValueModel.description = CellEditConstants.Manual;
    let periodInput = this.tableColumns.field;
    periodInput = this.setCapTableColumKpiPeriodInput(periodInput);
    const dateComponents = extractDateComponents(periodInput);
    this.updateDataAuditLogValueModel.quarter = dateComponents.quarter;
    this.updateDataAuditLogValueModel.year = dateComponents.year;
    this.updateDataAuditLogValueModel.month = dateComponents.month;
    this.updateDataAuditLogValueModel.portfolioCompanyId = this.moduleCompanyModel.companyId;
    this.updateDataAuditLogValueModel.oldCurrencyType = this.dataRow["KpiInfo"] || '';
    this.updateDataAuditLogValueModel.subPageId = this.moduleCompanyModel.subPageId;
    this.capTableValueSubmit();
  }
   /**
   * Validates the maximum length of the input value based on its type.
   * If the input value is not an integer, it should have a maximum length of 21 characters.
   * If the input value is an integer, it should have a maximum length of 16 characters.
   * 
   * @param event - The event object representing the input event.
   * @returns A boolean value indicating whether the input value is valid or not.
   */
  validateInputMaxLength(event: any): boolean {
    const pattern = /[0-9\.\-]/; 
    let inputChar = String.fromCharCode(event.keyCode);
    if (event.keyCode != 8 && !pattern.test(inputChar)) {
      event.preventDefault();
      return false;
    }
    const cursorPosition = event.target.selectionStart;
    let inputValue: string =event.target.value.slice(0, cursorPosition) + event.key + event.target.value.slice(cursorPosition); event.target.value.slice(0, cursorPosition) + event.key;
    if (event instanceof ClipboardEvent) {
        let pasteValueLength = event.clipboardData.getData('text/plain').length;
        if (pasteValueLength + inputValue.length > 16) return false;
    }
    let isNegative = inputValue.startsWith('-');
    let numberParts = inputValue.replace(/-/g, '').split('.');
    if (numberParts.length > 2) return false;
    if ((numberParts.length === 1 && numberParts[0].length > 16 && !isNegative) || (isNegative && numberParts[0].length > 16)) return false;
    if (numberParts.length === 2 && (numberParts[0].length > 15 || numberParts[1].length > 5)) return false;
    return true;
}
  onClose(): void {
    this.cancelButtonEvent.emit();
  }
  getIcons(name: string) {
    return this.helperService.getstaticIconPath(name);
  }
  /**
   * Handles the file change event.
   * @param value - The selected file(s).
   */
  onFileChange(value: any) {
    for (let file of value) {
      const fileExtension = file?.name?.split(".").pop();
      file.extension = fileExtension.toLowerCase();
      if (fileExtension == CellEditConstants.EXE) {
        this.toasterService.error(CellEditConstants.ValidateSupportingFileFormat, '', {
          positionClass: CellEditConstants.ToasterMessagePosition,
        });
      } else {
        this.files.push(file);
        let newDocument: DocumentModel = {
          documentName: file.name,
          extension: file.extension,
          isExisting: false,
          documentId: generateSecureId(15)
        };
        this.existingFiles.push(newDocument);
        this.updatedFiles.push({ documentId: newDocument.documentId, file: file });
      }
    }
    this.fileDropEl.nativeElement.value = '';
  }
  removeFile(documentId: string) {
    // Find the index of the file in the existingFiles array
    const index = this.existingFiles.findIndex(file => file.documentId === documentId);
    // If the file is found, remove it from the existingFiles array
    if (index !== -1) {
      this.findIdByDocumentId(documentId);
      this.existingFiles.splice(index, 1);
      // Also remove the file from the updatedFilesTemp array
      const tempIndex = this.updatedFiles.findIndex(file => file.documentId === documentId);
      if (tempIndex !== -1) {
        this.updatedFiles.splice(tempIndex, 1);
      }
    }
  }
  /**
   * Finds the id of a document by its documentId.
   * @param documentId The documentId to search for.
   */
  findIdByDocumentId(documentId: string) {
    const document = this.existingFiles.find(doc => doc.documentId === documentId);
    const matchId = document?.id || 0;
    if (matchId > 0)
      this.removeSupportingDocument.push(matchId.toString());
  }
  /**
   * Removes the existing supporting document by ID.
   * If the supportingDocumentsId is not null and there are items in the removeSupportingDocument array,
   * it filters out the IDs that are present in the removeSupportingDocument array from the supportingDocumentsId.
   */
  removeExistingSupportingDocumentById() {
    if (this.updateDataAuditLogValueModel.supportingDocumentsId != null && this.removeSupportingDocument.length > 0) {
      let ids = this.updateDataAuditLogValueModel.supportingDocumentsId.split(',');
      ids = ids.filter(id => !this.removeSupportingDocument.includes(id));
      this.updateDataAuditLogValueModel.supportingDocumentsId = ids.join(',');
    }
  }
  /**
   * Handles the form submission for the KpiCellEditComponent.
   * If the old value is 'NA', it is replaced with an empty string.
   * Sets the new value and comments from the kpiModel to the updateDataAuditLogValueModel.
   * Converts the updateDataAuditLogValueModel to FormData.
   * If there are any files selected, appends them to the FormData.
   * Calls the OnSubmitKpiAuditFilesUpload method of the auditService and emits the result or error through the confirmButtonEvent.
   */
  onSubmit() {
    this.isLoader = true;
    this.removeExistingSupportingDocumentById();
    this.addTextKpiValues();
    if (this.updateDataAuditLogValueModel.oldValue === 'NA') {
      this.updateDataAuditLogValueModel.oldValue = '';
    }
    if (this.kpiModel?.newValue == "NA" || this.kpiModel?.newValue === undefined || this.kpiModel?.newValue === null) {
      this.kpiModel.newValue = '';
    }
    this.updateDataAuditLogValueModel.mappingId = this.dataRow["MappingId"]==0?this.mappingId:this.dataRow["MappingId"];
    if(this.dataRow["IsEsg"]){
      this.updateDataAuditLogValueModel.attributeID = this.dataRow["MappingId"]==0?this.mappingId:this.dataRow["MappingId"];
    }
    this.updateDataAuditLogValueModel.newValue = this.kpiModel.newValue;
    this.updateDataAuditLogValueModel.comments = this.kpiModel.comments;
    this.updateDataAuditLogValueModel.fieldName = this.dataRow["KPI"];
    this.updateDataAuditLogValueModel.attributeName = this.kpiType;
    this.updateDataAuditLogValueModel.monthyear = this.tableColumns.field;
    
    const formData = ModelToFormData(this.updateDataAuditLogValueModel);
    if (this.updatedFiles?.length > 0) {
      for (const file of this.updatedFiles) {
        formData.append(CellEditConstants.UpdatedSupportingDocuments, file.file);
      }
    }
    this.auditService
      .OnSubmitKpiAuditFilesUpload(formData)
      .subscribe({
        next: (result) => {
          this.isLoader = false;
          this.confirmButtonEvent.emit(result);
        },
        error: (error) => {
          this.isLoader = false;
          this.confirmButtonEvent.emit(error);
        },
      });
  }
  addTextKpiValues() {
    if (this.kpiInfo == CellEditConstants.TextInfo) {
      this.kpiModel.newValue = this.kpiModel.newText;
    }
  }
  onColumnEditComplete(event) {
    this.kpiModel.newValue = event.target.value;
  }
  /**
 * Initializes the CapTable based on the current KPI type.
 * 
 * If the KPI type is CapTable, it sets the valueTypeId, periodId, and columnKpiId of the auditLogFilter object.
 * Otherwise, it sets these properties to 0.
 * 
 * The valueTypeId is set to the ValueTypeId of the dataRow object, or 0 if ValueTypeId is not defined.
 * The periodId is set to the capTablePeriodId of the moduleCompanyModel object.
 * The columnKpiId is set to the kpiId of the tableColumns object, if the KPIInfo property of the tableColumns object is not null.
 */
  capTableInitialize() {
    this.auditLogFilter.valueTypeId =(this.kpiType == CellEditConstants.CapTable)?(this.dataRow[CellEditConstants.ValueTypeId] || 0):0;
    this.auditLogFilter.periodId = (this.kpiType == CellEditConstants.CapTable)?this.moduleCompanyModel.capTablePeriodId:0;
    this.auditLogFilter.columnKpiId = (this.kpiType == CellEditConstants.CapTable && this.tableColumns[CellEditConstants.KPIInfo] !== null)?this.tableColumns[CellEditConstants.kpiId]:0;
    this.auditLogFilter.valueTypeId=this.tableColumns.field.includes(CellEditConstants.CapTableLTM) ? CellEditConstants.CapTableActualLTM : this.tableColumns.field.includes(CellEditConstants.CapTableYTD) ? CellEditConstants.CapTableActualYTD : CellEditConstants.CapTableActual;
  }
  /**
 * Sets the period input for the CapTable column KPI based on the current KPI type.
 * 
 * If the KPI type is CapTable and the KPIInfo property of the tableColumns object is not null, 
 * it sets the period input to the capTableColPeriod of the moduleCompanyModel object.
 * 
 * @param periodInput - The initial period input.
 * @returns The updated period input.
 */
  setCapTableColumKpiPeriodInput(periodInput: string) {
    if (this.kpiType == CellEditConstants.CapTable && this.tableColumns[CellEditConstants.KPIInfo] !== null) {
      periodInput = this.moduleCompanyModel.capTableColPeriod;
      this.isColumnField=true;
    }
    return periodInput;
  }
  /**
 * Submits the CapTable value based on the current KPI type.
 * 
 * If the KPI type is CapTable, it sets the valueTypeId, periodId, and columnKpiId of the updateDataAuditLogValueModel object.
 * Otherwise, it does nothing.
 * 
 * The valueTypeId is set to the ValueTypeId of the dataRow object, or 0 if ValueTypeId is not defined.
 * The periodId is set to the capTablePeriodId of the moduleCompanyModel object.
 * The columnKpiId is set to the kpiId of the tableColumns object, if the KPIInfo property of the tableColumns object is not null.
 */
  capTableValueSubmit() {
    if (this.kpiType == CellEditConstants.CapTable) {
      this.updateDataAuditLogValueModel.valueTypeId =
        this.kpiType == CellEditConstants.CapTable
          ? this.dataRow[CellEditConstants.ValueTypeId] || 0
          : 0;
      this.updateDataAuditLogValueModel.periodId =
        this.kpiType == CellEditConstants.CapTable
          ? this.moduleCompanyModel.capTablePeriodId
          : 0;
      this.updateDataAuditLogValueModel.columnKpiId =
        this.kpiType == CellEditConstants.CapTable &&
        this.tableColumns[CellEditConstants.KPIInfo] !== null
          ? this.tableColumns[CellEditConstants.kpiId]
          : 0;
        this.updateDataAuditLogValueModel.valueTypeId=this.tableColumns.field.includes(CellEditConstants.CapTableLTM) ? CellEditConstants.CapTableActualLTM : this.tableColumns.field.includes(CellEditConstants.CapTableYTD) ? CellEditConstants.CapTableActualYTD : CellEditConstants.CapTableActual;
        this.updateDataAuditLogValueModel.columnNumber=this.tableColumns["columnNumber"];
      }
  }
  /**
 * Validates and processes pasted input in an input field.
 * 
 * This method handles the paste event in an input field, ensuring that the pasted data is numeric and 
 * conforms to the input's validation rules. It prevents the default paste action if the data is valid 
 * and updates the input field with the processed data.
 * 
 * @param event - The paste event triggered by the user.
 * @returns {boolean} - Returns true if the pasted data is valid and the input field is updated, otherwise false.
 */
  errorMessage: string = '';
  isConfirmDisabled: boolean = false;
  validateInputMaxLengthPaste(event: any): boolean {
    let pasteData = event.clipboardData.getData('text/plain');
    if(!pasteData) return false;
    pasteData = pasteData.replace(/,/g, '').replace(/\s+/g, '');
    const filterRegex = /^-?\d+(\.\d*)?$/;
    if (!filterRegex.test(pasteData)) {
        this.showErrorMessage(GlobalConstants.onlyNumericValuesAllowed);
        event.preventDefault();
        return false;
    }
    const inputElement = event.target as HTMLInputElement;
    const cursorStart = inputElement.selectionStart;
    const cursorEnd = inputElement.selectionEnd;
    // Replace the selected text with the pasted value
    let inputValue: string = inputElement.value.slice(0, cursorStart) + pasteData + inputElement.value.slice(cursorEnd);
    if (this.validateInputValue(inputValue, event)) {
        event.preventDefault(); // Prevent the default paste action
        inputElement.value = inputValue; // Update the input value with the valid pasted data
        return true;
    }
    return false;
}
/**
 * Validates the input value in an input field.
 * 
 * This method handles the input event in an input field, extracting the current value and passing it 
 * to the validation function to ensure it meets the required criteria.
 * 
 * @param event - The input event triggered by the user.
 */
validateInput(event: any): void {
    const inputValue: string = event.target.value;
    this.validateInputValue(inputValue, event);
}
/**
 * Validates the input value based on specific criteria.
 * 
 * This method checks if the input value meets the required format and length constraints. It ensures 
 * that there is at most one minus sign at the beginning, and that the numeric part does not exceed 
 * specified length limits. If the input is invalid, an error message is shown and the default event 
 * action is prevented.
 * 
 * @param inputValue - The value of the input field to be validated.
 * @param event - The event triggered by the user.
 * @returns {boolean} - Returns true if the input value is valid, otherwise false.
 */
validateInputValue(inputValue: string, event: any): boolean {
    if ((inputValue.match(/-/g) || []).length > 1 || (inputValue.indexOf('-') > 0)) {
        this.showErrorMessage(GlobalConstants.onlyOneMinusSignAllowed);
        event.preventDefault();
        return false;
    }
    let isNegative = inputValue.startsWith('-');
    let numberParts = inputValue.replace(/-/g, '').split('.');
    if (numberParts.length > 2 || 
        (numberParts.length === 1 && numberParts[0].length > 16 && !isNegative) || 
        (isNegative && numberParts[0].length > 16) || 
        (numberParts.length === 2 && (numberParts[0].length > 15 || numberParts[1].length > 5))) {
        this.showErrorMessage(GlobalConstants.valueExceedsLimit);
        event.preventDefault();
        return false;
    }
    this.clearErrorMessage();
    return true;
}
/**
 * Displays an error message and disables the confirm action.
 * 
 * This method sets the error message to be displayed and disables the confirm button to prevent 
 * further actions until the error is resolved.
 * 
 * @param message - The error message to be displayed.
 */
showErrorMessage(message: string) {
    this.errorMessage = message;
    this.isConfirmDisabled = true;
}
/**
 * Clears the error message and enables the confirm action.
 * 
 * This method clears any displayed error message and re-enables the confirm button, allowing 
 * further actions to be taken.
 */
clearErrorMessage() {
    this.errorMessage = '';
    this.isConfirmDisabled = false;
}
}