import { generateSecureId } from "src/app/utils/utils";
import {
  CompanyKpiTreeItem,
  FileConfig,
  FileValidationResult,
  KpiTreeGroup,
  SelectedFile,
} from "./data-ingestion.model";
import { CommonConstants, DataIngestionConstants } from "src/app/common/constants";
import { 
  filePdfIcon, 
  fileExcelIcon, 
  fileWordIcon, 
  fileImageIcon,
  SVGIcon
} from "@progress/kendo-svg-icons";
import { KPIModulesEnum, FundKPIModulesEnum } from "src/app/services/permission.service";
/**
 * Utility class for handling data ingestion operations
 * Provides methods for date/period handling and file processing
 */
export class DataIngestionUtility {
  /**
   * Returns an array of month names in English
   * @returns {string[]} Array of month names (January to December)
   */
  static getMonthsList(): string[] {
    return CommonConstants.monthOptions
      .sort((a, b) => a.number - b.number)
      .map((month) => month.text);
  }

  /**
   * Generates a list of years from the specified start year to the current year
   * @param {number} startYear - The starting year for the list
   * @returns {number[]} Array of years in descending order
   */
  static getYearsList(startYear: number): number[] {
    const currentYear = new Date().getFullYear();
    return Array.from(
      { length: currentYear - startYear + 1 },
      (_, index) => currentYear - index
    );
  }

  /**
   * Returns a list of quarters (Q1-Q4)
   * @returns {string[]} Array of quarter labels
   */
  static getQuartersList(): string[] {
    return CommonConstants.quarterOptions
      .sort((a, b) => a.number - b.number)
      .map((q) => q.text);
  }

  /**
   * Converts period type to display text for selection
   * @param {string | null} period - The period type ('Quarter', 'Month', 'Year')
   * @returns {string} Display text for the period selection
   */
  static getPeriodText(period: string | null): string {
    switch (period) {
      case "Quarter":
        return DataIngestionConstants.QUARTER_SELECTION;
      case "Month":
        return DataIngestionConstants.MONTH_SELECTION;
      case "Year":
        return DataIngestionConstants.YEAR_SELECTION;
      default:
        return DataIngestionConstants.DEFAULT_PERIOD_SELECTION;
    }
  }

  /**
   * Sorts files by invalid status first, then by document name alphabetically
   * @param {SelectedFile[]} files - Array of files to sort
   * @param {boolean} ascending - Sort direction for names (true for ascending, false for descending)
   * @returns {SelectedFile[]} Sorted array of files
   */
  static sortFilesByStatusAndName(
    files: SelectedFile[],
    ascending: boolean = true
  ): SelectedFile[] {
    const alphabeticalSort = (a: string, b: string): number => {
      // For pure alphabetical names
      const isNumbered = (name: string) => /\(\d+\)/.test(name);

      if (!isNumbered(a) && !isNumbered(b)) {
        // Use simple alphabetical sort for non-numbered files
        const nameA = a.replace(/\.[^/.]+$/, "").toLowerCase();
        const nameB = b.replace(/\.[^/.]+$/, "").toLowerCase();
        return nameA.localeCompare(nameB, undefined, { sensitivity: "base" });
      }
      // For numbered filenames
      const parseFileName = (name: string) => {
        const baseName = name
          .replace(/\.[^/.]+$/, "")
          .replace(/(?:\s*\(\d+\))$/, "")
          .toLowerCase();
        const numberMatch = name.match(/\((\d+)\)/);
        const number = numberMatch ? parseInt(numberMatch[1]) : 0;
        return { baseName, number };
      };

      const fileA = parseFileName(a);
      const fileB = parseFileName(b);

      if (fileA.baseName === fileB.baseName) {
        return fileA.number - fileB.number;
      }
      return fileA.baseName.localeCompare(fileB.baseName, undefined, {
        sensitivity: "base",
      });
    };

    return [...files].sort((a, b) => {
      if (a.status !== b.status) {
        return a.status === DataIngestionConstants.INVALID ? -1 : 1;
      }
      return ascending
        ? alphabeticalSort(a.name, b.name)
        : alphabeticalSort(b.name, a.name);
    });
  }

  /**
   * Validates a file against configured constraints
   * @param {File} file - The file to validate
   * @param {FileConfig} fileConfig - Configuration for file validation
   * @param {Set<string>} existingFiles - Set of existing file names to check duplicates
   * @returns {FileValidationResult} Validation result containing isValid flag and error messages
   */
  static validateFile(
    file: File,
    fileConfig: FileConfig,
    existingFiles: Set<string>,
    extractionType: string = ""
  ): FileValidationResult {
    const errors: string[] = [];
    const extension = file.name.split(".").pop()?.toLowerCase() || "";
    const maxSizeMB = fileConfig.maxSize / (1024 * 1024);
    if (existingFiles.has(file.name)) {
      errors.push(DataIngestionConstants.DUPLICATE_FILE_ERROR);
    }
    let isValidFormat = false;
    if (extractionType === "Specific KPI") {
      isValidFormat = ["pdf", "xls", "xlsx", "csv"].includes(extension);
      if (!isValidFormat) {
        errors.push("File format is not PDF or Excel file");
      }
    } else {
      isValidFormat = extension === DataIngestionConstants.PDF;
      if (!isValidFormat) {
        errors.push(DataIngestionConstants.INVALID_FORMAT_ERROR);
      }
    }
    // const isPDF = extension === DataIngestionConstants.PDF;
    // if (!isPDF) {
    //   errors.push(DataIngestionConstants.INVALID_FORMAT_ERROR);
    // }
    const isOverSize = file.size > fileConfig.maxSize;
    if (isOverSize) {
      errors.push(DataIngestionConstants.FILE_SIZE_ERROR(maxSizeMB));
    }
    if (isOverSize && !isValidFormat) {
      errors.push(DataIngestionConstants.SIZE_AND_FORMAT_ERROR(maxSizeMB));
    }
    return {
      isValid: errors.length === 0,
      errors: errors,
    };
  }

  /**
   * Processes an array of files, validating each and creating SelectedFile objects
   * @param {File[]} files - Array of files to process
   * @param {FileConfig} fileConfig - Configuration for file validation
   * @param {Set<string>} existingFiles - Set of existing file names
   * @returns {{ newFiles: SelectedFile[], totalFiles: number }} Processed files and total count
   */
  static processFiles(
    files: File[],
    fileConfig: FileConfig,
    existingFiles: Set<string>,
    extractionType: string = ""
  ): {
    newFiles: SelectedFile[];
    totalFiles: number;
  } {
    const newFiles: SelectedFile[] = files.map((file) => {
      const validationResult = this.validateFile(
        file,
        fileConfig,
        existingFiles,
        extractionType
      );
      return {
        id: generateSecureId(),
        file: file,
        name: file.name,
        size: file.size,
        type: file.type,
        status: validationResult.isValid
          ? DataIngestionConstants.VALID
          : DataIngestionConstants.INVALID,
        errors: validationResult.errors,
        documentType: null,
      };
    });

    return {
      newFiles,
      totalFiles: existingFiles.size + files.length,
    };
  }
  static extractCompanyInitials(input: string): string {
    if (!input) {
      return ""; // Return empty string if input is null/undefined/empty
    }
    return input
      .split(/\s+/)
      .map((word) => word[0])
      .join("")
      .substring(0, 2)
      .toUpperCase();
  }
  private static readonly fileIcons = {
    pdf: filePdfIcon,
    excel: fileExcelIcon,
    word: fileWordIcon,
    image: fileImageIcon,
    default: filePdfIcon, // Using PDF icon as default
  };
  /**
   * Returns the appropriate icon for a given filename based on its extension
   * @param {string} fileName - The name of the file
   * @returns {SvgIcon} The corresponding Kendo SVG icon
   */
  static getFileIcon(fileName: string): SVGIcon {
    if (!fileName) return this.fileIcons.default;

    const extension = fileName.split(".").pop()?.toLowerCase();

    switch (extension) {
      case "pdf":
        return this.fileIcons.pdf;
      case "xlsx":
      case "xls":
      case "csv":
        return this.fileIcons.excel;
      case "doc":
      case "docx":
        return this.fileIcons.word;
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return this.fileIcons.image;
      default:
        return this.fileIcons.default;
    }
  }
  static getMonthsListwithAsOfDate(): string[] {
    const months = CommonConstants.monthOptions
      .sort((a, b) => a.number - b.number)
      .map((month) => month.text);
    return ["As of Date", ...months];
  }

  /**
   * Generates a list of years from the specified start year to the current year
   * @param {number} startYear - The starting year for the list
   * @param {number} maxYear - The maximum year (defaults to current year)
   * @returns {number[]} Array of years in descending order
   */
  static getAnnualsList(
    startYear: number,
    maxYear: number = new Date().getFullYear()
  ): number[] {
    return Array.from(
      { length: maxYear - startYear + 1 },
      (_, index) => maxYear - index
    );
  }

  /**
   * Generates a list of years with "As of Date" option from start year to max year
   * @param {number} startYear - The starting year for the list
   * @param {number} maxYear - The maximum year (defaults to current year)
   * @returns {(number | string)[]} Array with "As of Date" and years in descending order
   */
  static getYearsWithAsOfDateList(
    startYear: number,
    maxYear: number = new Date().getFullYear()
  ): (number | string)[] {
    const years = Array.from(
      { length: maxYear - startYear + 1 },
      (_, index) => maxYear - index
    );
    return ["As of Date", ...years];
  }

  /**
   * Returns a list of quarters (Q1-Q4)
   * @returns {string[]} Array of quarter labels
   */
  static getQuartersWithAsOfDateList(): string[] {
    const quarters = CommonConstants.quarterOptions
      .sort((a, b) => a.number - b.number)
      .map((q) => q.text);
    return ["As of Date", ...quarters];
  }
  static filterArray<T>(
    searchValue: string,
    sourceArray: T[],
    targetSetter: (items: T[]) => void
  ): void {
    if (!searchValue || !searchValue.trim()) {
      targetSetter([...sourceArray]);
      return;
    }

    const searchTerm = searchValue.trim().toLowerCase();
    const filtered = sourceArray.filter((item) =>
      item.toString().toLowerCase().includes(searchTerm)
    );

    targetSetter(filtered);
  }
  /**
   * Filters an array of objects based on a search term
   * @param {string} searchValue - The search term
   * @param {T[]} sourceArray - The array to filter
   * @param {(items: T[]) => void} targetSetter - Function to set the filtered results
   * @param {string[]} [searchKeys] - Optional array of object keys to search in (if not provided, searches all string properties)
   */
  static filterArrayObjects<T>(
    searchValue: string,
    sourceArray: T[],
    targetSetter: (items: T[]) => void,
    searchKeys?: (keyof T)[]
  ): void {
    if (!searchValue || !searchValue.trim()) {
      targetSetter([...sourceArray]);
      return;
    }

    const searchTerm = searchValue.trim().toLowerCase();

    const filtered = sourceArray.filter((item) => {
      // If item is not an object or is null, fall back to toString method
      if (typeof item !== "object" || item === null) {
        return String(item).toLowerCase().includes(searchTerm);
      }

      // If search keys are provided, only search in those properties
      if (searchKeys && searchKeys.length > 0) {
        return searchKeys.some((key) => {
          const value = item[key];
          return (
            typeof value === "string" &&
            value.toLowerCase().includes(searchTerm)
          );
        });
      }

      // Otherwise search in all string properties
      return Object.values(item).some(
        (value) =>
          typeof value === "string" && value.toLowerCase().includes(searchTerm)
      );
    });

    targetSetter(filtered);
  }
  static createSelectedIssuerDetailsHierarchy(
    source: CompanyKpiTreeItem[],
    selectedIds: number[]
  ): CompanyKpiTreeItem[] {
    if (!source || !source.length) return [];

    return source
      .map((issuer) => {
        const isIssuerSelected = selectedIds.includes(issuer.id);
        const selectedGroups = issuer.items
          ? (issuer.items
              .map((group) => {
                const isGroupSelected = selectedIds.includes(group.id);
                const selectedKpis = group.items
                  ? group.items
                      .filter((kpi) => selectedIds.includes(kpi.id))
                      .map((kpi) => ({ ...kpi }))
                  : [];
                return isGroupSelected || selectedKpis.length > 0
                  ? ({
                      id: group.id,
                      itemId: group.itemId,
                      text: group.text,
                      name: group.name,
                      items: selectedKpis,
                    } as KpiTreeGroup)
                  : null;
              })
              .filter(Boolean as any) as KpiTreeGroup[])
          : [];
        return isIssuerSelected || selectedGroups.length > 0
          ? ({
              id: issuer.id,
              fieldId: issuer.fieldId,
              text: issuer.text,
              items: selectedGroups,
            } as CompanyKpiTreeItem)
          : null;
      })
      .filter(Boolean as any) as CompanyKpiTreeItem[];
  }
  static getFileExtension(fileName: string): string {
    if (!fileName) return "pdf";

    const extension = fileName.split(".").pop()?.toLowerCase();

    switch (extension) {
      case "pdf":
        return "pdf";
      case "xlsx":
      case "xls":
      case "csv":
        return "xlsx";
      default:
        return "pdf";
    }
  }
  static getQuartersArrayList(): any[] {
    return CommonConstants.quarterOptions.map(quarter => ({
      id: quarter.number,
      name: quarter.value,
      text: quarter.text
    }));
  }
  static getMonthsArrayList(): any[] {
    return CommonConstants.monthOptions.map(quarter => ({
      id: quarter.value,
      name: quarter.value,
      number: quarter.number
    }));
  }
  static getYearsArrayList(startYear: number = 2000): any[] {
    const currentYear = new Date().getFullYear();
    return Array.from(
      { length: currentYear - startYear + 1 },
      (_, index) => ({
        id: currentYear - index,
        name: (currentYear - index).toString(),
        text: (currentYear - index).toString()
      })
    );
  }
  /**
   * Returns the module name for a given moduleId using the provided alias map.
   * @param {number} moduleId - The module enum value
   * @param {any} kpiModuleAlias - The alias object (usually this.kpiModuleAlias)
   * @returns {string} The module name or empty string if not found
   */
  static getModuleNameFromMap(moduleId: number, kpiModuleAlias: any): string {
    const moduleNameMap = {
      [KPIModulesEnum.TradingRecords]: kpiModuleAlias.TradingRecords,
      [KPIModulesEnum.CreditKPI]: kpiModuleAlias.Credit,
      [KPIModulesEnum.Operational]: kpiModuleAlias.Operational,
      [KPIModulesEnum.Investment]: kpiModuleAlias.Investment,
      [KPIModulesEnum.Company]: kpiModuleAlias.Company,
      [KPIModulesEnum.Impact]: kpiModuleAlias.Impact,
      [KPIModulesEnum.ProfitAndLoss]: kpiModuleAlias.ProfitAndLoss,
      [KPIModulesEnum.BalanceSheet]: kpiModuleAlias.BalanceSheet,
      [KPIModulesEnum.CashFlow]: kpiModuleAlias.CashFlow,
      [KPIModulesEnum.CapTable1]: kpiModuleAlias.CapTable1,
      [KPIModulesEnum.CapTable2]: kpiModuleAlias.CapTable2,
      [KPIModulesEnum.CapTable3]: kpiModuleAlias.CapTable3,
      [KPIModulesEnum.CapTable4]: kpiModuleAlias.CapTable4,
      [KPIModulesEnum.CapTable5]: kpiModuleAlias.CapTable5,
      [KPIModulesEnum.CustomTable1]: kpiModuleAlias.CustomTable1,
      [KPIModulesEnum.CustomTable2]: kpiModuleAlias.CustomTable2,
      [KPIModulesEnum.CustomTable3]: kpiModuleAlias.CustomTable3,
      [KPIModulesEnum.CustomTable4]: kpiModuleAlias.CustomTable4,
      [KPIModulesEnum.OtherKPI1]: kpiModuleAlias.OtherKPI1,
      [KPIModulesEnum.OtherKPI2]: kpiModuleAlias.OtherKPI2,
      [KPIModulesEnum.OtherKPI3]: kpiModuleAlias.OtherKPI3,
      [KPIModulesEnum.OtherKPI4]: kpiModuleAlias.OtherKPI4,
      [KPIModulesEnum.OtherKPI5]: kpiModuleAlias.OtherKPI5,
      [KPIModulesEnum.OtherKPI6]: kpiModuleAlias.OtherKPI6,
      [KPIModulesEnum.OtherKPI7]: kpiModuleAlias.OtherKPI7,
      [KPIModulesEnum.OtherKPI8]: kpiModuleAlias.OtherKPI8,
      [KPIModulesEnum.OtherKPI9]: kpiModuleAlias.OtherKPI9,
      [KPIModulesEnum.OtherKPI10]: kpiModuleAlias.OtherKPI10,
    };
    return moduleNameMap[moduleId] || "";
  }
  /**
   * Retrieves the fund module name corresponding to the given module ID from the provided KPI module alias map.
   *
   * @param moduleId - The numeric identifier of the fund module, typically from `FundKPIModulesEnum`.
   * @param kpiModuleAlias - An object containing mappings of fund module keys to their display names or aliases.
   * @returns The display name or alias of the fund module if found; otherwise, an empty string.
   */
  static getFundModuleNameFromMap(moduleId: number, kpiModuleAlias: any): string {
    const moduleNameMap = {
      [FundKPIModulesEnum.FundFinancials8]: kpiModuleAlias.FundFinancials,
      [FundKPIModulesEnum.FundFinancials1]: kpiModuleAlias.FundFinancials1,
      [FundKPIModulesEnum.FundFinancials2]: kpiModuleAlias.FundFinancials2,
      [FundKPIModulesEnum.FundFinancials3]: kpiModuleAlias.FundFinancials3,
      [FundKPIModulesEnum.FundFinancials4]: kpiModuleAlias.FundFinancials4,
      [FundKPIModulesEnum.FundFinancials5]: kpiModuleAlias.FundFinancials5,
      [FundKPIModulesEnum.FundFinancials6]: kpiModuleAlias.FundFinancials6,
      [FundKPIModulesEnum.FundFinancials7]: kpiModuleAlias.FundFinancials7,
      [FundKPIModulesEnum.FundKPI1]: kpiModuleAlias.FundKPI1,
      [FundKPIModulesEnum.FundKPI2]: kpiModuleAlias.FundKPI2,
      [FundKPIModulesEnum.FundKPI3]: kpiModuleAlias.FundKPI3,
      [FundKPIModulesEnum.FundKPI4]: kpiModuleAlias.FundKPI4,
      [FundKPIModulesEnum.FundKPI5]: kpiModuleAlias.FundKPI5,
      [FundKPIModulesEnum.FundKPI6]: kpiModuleAlias.FundKPI6,
      [FundKPIModulesEnum.FundKPI7]: kpiModuleAlias.FundKPI7,
      [FundKPIModulesEnum.FundKPI8]: kpiModuleAlias.FundKPI8,
    };
    return moduleNameMap[moduleId] || "";
  }
  /**
   * Returns the KPI type display name for a given KPI info symbol.
   * @param {string} kpiInfo - The KPI info symbol ('$','x','%','#','text')
   * @returns {string} The display name for the KPI type
   */
  static getKpiType(kpiInfo: string): string {
    const kpiTypeMap: Record<string, string> = {
      '$': 'Number',
      'x': 'String',
      '%': 'Percentage',
      '#': 'Number',
      'text': 'String'
    };
    return kpiTypeMap[kpiInfo] || '';
  }
}
