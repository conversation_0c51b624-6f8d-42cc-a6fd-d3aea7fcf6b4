export enum PageConfigurationSubFeature
{
   
    StaticInformation = 1,
  
    KeyPerformanceIndicator = 2,
   
    CompanyFinancials = 3,
   
    Commentary = 4,
    
    BasicDetails = 5,
   
    PortfolioCompanyFundHoldingDetails = 6,
   
    FundStaticInformation = 7,
    
    FundTerms = 8,
    
    TrackRecord = 9,
    
    GeographicLocations = 10,
  
    InvestmentProfessionals = 11,
    FundGeographicLocations = 12
}

export enum UserPermissionEntity {  
    PortfolioCompany = 1,   
    Funds = 2,     
    Firms = 3,  
    Deals = 4
}

export enum PageConfigurationPageDetails{
    ESG = 9,
}
export enum PageConfigurationDocumentPageDetails{
    AllFolders='All Folders',
    DocumentsList='Documents List',
    Documents='Documents',
    DataCollection='Data Collection',
    EmailNotification='Email Notification',
}
export enum FileUploadModule{
    ESG = 47,
    FundofFunds = 13,
}
export enum FileStatus{
    Uploaded = 1,
    Processing = 2,
    Successful = 3,
    Failed = 4,
    Cancelled = 5
}
export enum FileExtension {
    XLS = 'xls',
    XLSX = 'xlsx',
    PDF = 'pdf',
    ZIP = 'zip',
    PNG = 'png',
    JPG = 'jpg',
    TXT = 'txt',
    DOC = 'doc',
    DOCX = 'docx',
    PPTX = 'pptx',
    PPT = 'ppt'
  }
  export enum KpiInfo {
    Text = "Text",
    Number = "#",
    Percentage = "%",
    Multiple = "x",
    Currency = "$"
  }

  export enum HttpStatusCodes {
    OK = 200,
    CREATED = 201,
    ACCEPTED = 202,
    NO_CONTENT = 204,
    BAD_REQUEST = 400,
    UNAUTHORIZED = 401,
    FORBIDDEN = 403,
    NOT_FOUND = 404,
    INTERNAL_SERVER_ERROR = 500,
    BAD_GATEWAY = 502,
    SERVICE_UNAVAILABLE = 503,
    GATEWAY_TIMEOUT = 504
  }
  export enum DataIngestion{
    SpecificKpiExtraction='Specific Kpi Extraction',
    AsIsExtraction='AsIs Extraction',
   
}