import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { GlobalConstants } from 'src/app/common/constants';
import { PageConfigurationDocumentPageDetails } from 'src/app/common/enums';
import { FeaturesEnum } from 'src/app/services/permission.service';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';

interface TreeNode {
  name: string;
  isExpanded: boolean;
  children?: TreeNode[];
  path?: string;
  type?: string;
  index?: number;
}

@Component({
  selector: 'app-pcdocument-folders',
  templateUrl: './pcdocument-folders.component.html',
  styleUrls: ['./pcdocument-folders.component.scss']
})
export class PcdocumentFoldersComponent implements OnInit {
  @Input() documentsFieldPageConfig: any;
  constructor(
    private toastService: ToastrService,
    private readonly repositoryconfigService: RepositoryConfigService
  ) { }

  @Input() PortfolioCompanyId: string = '';
  @Input() IsFund: boolean = false;
  @Output() folderSelected = new EventEmitter<any>();
  allFoldersAlias: string = 'All Folders';


  treeData: TreeNode[] = [];
  filteredTreeData: TreeNode[] = [];
  searchTerm: string = '';
  isLoading: boolean = false;

  selectedNode: TreeNode | null = null;

  unconfigured: string = "Un-Configured";

  sortMode: string = GlobalConstants.SortModeDesc;

  ngOnInit() {
    this.allFoldersAlias = this.documentsFieldPageConfig?.find((x: any) => x.name == PageConfigurationDocumentPageDetails.AllFolders)?.displayName;
    if (this.PortfolioCompanyId !== '') {
      this.isLoading = true;
      this.repositoryconfigService.getRepositoryStructureData(this.PortfolioCompanyId, this.IsFund ? FeaturesEnum.Fund : FeaturesEnum.PortfolioCompany).subscribe(
        (response) => {
          this.isLoading = false;
          if (response.isSuccess) {
            this.treeData = response.data;
            this.filteredTreeData = [...this.treeData];
          }
          else
            this.toastService.error(response.message);
        },
        (error) => {
          this.isLoading = false;
          this.toastService.error('Error while fetching data');
        }
      );
    }
  }

  filterFolders() {
    if (!this.searchTerm) {
      this.filteredTreeData = [...this.treeData];
      return;
    }

    const search = this.searchTerm.toLowerCase();
    this.filteredTreeData = this.filterNodes(this.treeData, search);
  }

  private filterNodes(nodes: TreeNode[], searchTerm: string): TreeNode[] {
    return nodes.filter(node => {
      const matchesName = node.name.toLowerCase().includes(searchTerm);

      // Check children recursively
      let filteredChildren: TreeNode[] = [];
      if (node.children && node.children.length > 0) {
        filteredChildren = this.filterNodes(node.children, searchTerm);
      }

      // Clone the node and assign filtered children
      if (matchesName || filteredChildren.length > 0) {
        const clonedNode = { ...node };
        if (filteredChildren.length > 0) {
          clonedNode.children = filteredChildren;
          clonedNode.isExpanded = true; // Auto-expand when filtering
        }
        return true;
      }
      return false;
    });
  }

  toggleFolder(node: TreeNode): void {
    if (node.children) {
      node.isExpanded = !node.isExpanded;
    }
    this.selectedNode = node;
    let isUnconfig: boolean = false;

    // Emit the folder path when a node is selected
    let nodePath: string = this.buildFolderPath(node);

    if (nodePath.indexOf(this.unconfigured) > -1) {
      nodePath = nodePath.replace(this.unconfigured + "/", '');
      isUnconfig = true;
    }
    this.folderSelected.emit({ folderPath: nodePath, isFromUnconfig: isUnconfig });
  }

  toggleSort() {
    this.sortMode = this.sortMode === GlobalConstants.SortModeDesc ? GlobalConstants.SortModeAsc : GlobalConstants.SortModeDesc;
    this.filteredTreeData.forEach(node => {
      if (node.children) {
        //annual sort 
        node.children.sort((a: any, b: any) => {
          if (this.sortMode === GlobalConstants.SortModeDesc) {
            return b.name.localeCompare(a.name);
          } else {
            return a.name.localeCompare(b.name);
          }
        });

        // Sort children's children by type and index
        node.children.forEach(child => {

          if (child.children && child.children.length > 0) {
            // Filter by type if specified
            const quarterlyChildren = child.children.filter(f => f.type?.toLowerCase() === GlobalConstants.Quarterly);
            const monthlyChildren = child.children.filter(f => f.type?.toLowerCase() === GlobalConstants.Monthly);

            // Sort each type group by index
            const sortByIndex = (a: TreeNode, b: TreeNode) => {
              if (this.sortMode === GlobalConstants.SortModeDesc) {
                return (b.index || 0) - (a.index || 0);
              } else {
                return (a.index || 0) - (b.index || 0);
              }
            };
            quarterlyChildren.sort(sortByIndex);
            monthlyChildren.sort(sortByIndex);
            // Reconstruct children array with sorted groups
            child.children = [...quarterlyChildren, ...monthlyChildren];
          }
        });
      }
    });
    this.filteredTreeData = [...this.filteredTreeData];
    this.folderSelected.emit({ folderPath: 0, isFromUnconfig: true });
  }

  private buildFolderPath(node: TreeNode): string {
    // You may need to customize this based on your folder structure format
    return node.path || node.name;
  }
}
