import { Component, Input,EventEmitter,Output,ViewChild,OnChanges } from '@angular/core';
import { KPIModulesEnum } from 'src/app/services/permission.service';
import { ActivatedRoute } from '@angular/router';
import {  ErrorMessage, FinancialValueUnitsEnum, MiscellaneousService,PeriodType } from 'src/app/services/miscellaneous.service';
import { CashflowBetaService } from 'src/app/services/cashflow-beta.service';
import { Table } from 'primeng/table';
import { isNumeric } from "@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric";
import { NumberDecimalConst, GlobalConstants, BulkuploadConstants, KpiTypesConstants } from "src/app/common/constants";
import { KpiConfig } from 'src/app/components/portfolioCompany/models/Kpi-Configuartion.model';
import { ModuleCompanyModel,MappedDocuments, Audit, TableHeader } from 'src/app/components/file-uploads/kpi-cell-edit/kpiValueModel';
import { ToastrService } from 'ngx-toastr';
import { extractDateComponents } from 'src/app/components/file-uploads/kpi-cell-edit/cell-edit-utils';
import { AuditService } from 'src/app/services/audit.service';
import { OidcAuthService } from 'src/app/services/oidc-auth.service';
import { isNil } from 'src/app/utils/utils';
import { DatePipe } from '@angular/common';
import { getConversionErrorMessage } from 'src/app/utils/utils';

@Component({
  selector: 'app-cashflow-beta',
  templateUrl: './cashflow-beta.component.html',
  styleUrls: ['./cashflow-beta.component.scss']
})
export class CashflowBetaComponent implements OnChanges {
  infoUpdate:boolean = false;
  @Input() isDownload:boolean = false;
  NumberDecimalConst = NumberDecimalConst;
  cashflowSearchFilter: any;
  kpiModuleId = KPIModulesEnum.CashFlow;
  @Input() tabName:string =null;
  @Input() model;
  @Input() selectedCurrency = "";
  @Input() valueType="";
  @Input() periodType:number = 0;
  @Input() isErrorLog:boolean = false;
  @Input() sectionId: number = 0;
  subtabName: string = "Actual";
  portfolioCompanyID: any;
  currencyCode = "";
  id: any;
  isexport: boolean = false;
  isLoader: boolean = false;
  currencyRateSource = "";
  @Output() isDownloading: EventEmitter<any> = new EventEmitter();
  @Output() changeOptionType: EventEmitter<any> = new EventEmitter();
  cashflowValuesMultiSortMeta: any[] = [
    { field: "year", order: -1 },
    { field: "month", order: -1 },
  ];
  frozenHeader: any = [{ field: "Kpi", header: "KPI" }];
  tableColumns = [];
  tableFrozenColumns = [];
  tableResult = [];
  tableResultClone = [];
  @ViewChild("dt") dt: Table | undefined;
  @Input() isFilter:boolean = false;
  @Output() onChangeValueType: EventEmitter<any> = new EventEmitter();
  auditLogList:any=[];
  isPageLoad:boolean = true;
  @Input() loadData:boolean=false;
  @Input() pageConfigData : KpiConfig;
  currentPeriodType : number = undefined;
  currentValueType : string = undefined;
  @Input() isYtd:boolean = false;
  @Input() isLtm:boolean = false;
  @Input() valueTypeString : string;
  isYtdPageLoad:boolean = true;
  isLtmPageLoad:boolean = true;
  showEditPopup: boolean = false;
  uniqueModuleCompany: ModuleCompanyModel;
  dataRow: object;
  dataColumns: TableHeader;
  @Input() cashFlowPermission: any = [];
  cfEditPermission: boolean = false;
  editErrorForConvertedValue: string = GlobalConstants.EditgErrorForConvertedValue;
  auditLogErrorForConvertedValue: string = GlobalConstants.AuditLogErrorForConvertedValue;
  editSpotRateConversionError: string = GlobalConstants.EditSpotRateConversionError;
  auditLogSpotRateConversionError: string = GlobalConstants.AuditLogSpotRateConversionError;
  constructor(private _avRoute: ActivatedRoute,
    private miscService: MiscellaneousService,
    private identityService: OidcAuthService,
    private cashflowBetaService: CashflowBetaService,
    public toasterService: ToastrService,
    private auditService: AuditService,
    private datePipe: DatePipe
  ) { 
      if (this._avRoute.snapshot.params["id"]) {
        this.id = this._avRoute.snapshot.params["id"];
      }
    }
    ngOnChanges(changes:any){ 
      if(this.pageConfigData != undefined){
        if(changes["valueType"] || changes["periodType"] || changes["isFilter"] || changes["loadData"]|| changes["isYtd"] || changes["isLtm"])
          {
            this.cfEditPermission = this.cashFlowPermission?.some(x => x.canEdit);
            this.isPageLoad = this.isPageLoad ? true:false;
            this.isYtdPageLoad = this.isYtdPageLoad ? true:false;
            this.isLtmPageLoad = this.isLtmPageLoad ? true:false;
            if(changes["isYtd"] || changes["isLtm"] || (this.loadData && (changes["periodType"] && (this.currentPeriodType != this.periodType || this.currentValueType != this.valueType)) || !changes["periodType"]))
              this.getCashflowData();
          }
          if (changes["isDownload"] && this.isDownload) {
            this.isexport=true;
            this.exportCashflowData();
          }
      }
    }
    showNoAccessError() {
      this.toasterService.error(ErrorMessage.NoAccess, "", { positionClass: "toast-center-center" });
    }
    getCashflowData() {
      let periodType =
        this.model.periodType != undefined ? this.model.periodType.type : null;
      let searchFilter = {
        sortOrder: null,
        periodType: periodType,
        startPeriod:
          this.model.startPeriod != undefined ? this.model.startPeriod[0] : null,
        endPeriod:
          this.model.startPeriod != undefined ? this.model.startPeriod[1] : null,
      };
      this.getCashflowValues(
        null,
        searchFilter,
        this.model.portfolioCompany.portfolioCompanyID,
        this.currencyCode
      );
      this.cashflowSearchFilter = searchFilter;
    }
      /**
   * Converts the values in the given object from thousands to absolute values.
   *
   * @param valueClone - The object containing the values to be converted.
   * @param local - An object containing the table columns.
   * @param value - The multiplier value to convert thousands to absolute values.
   * @returns The object with converted values.
   */
  convertUnits() {
    this.tableResult = [];
    let local = this;
    let masterValueUnit = local.model.currecyUnit;
    this.tableResultClone.forEach(function (value: any) {
      let valueClone = JSON.parse(JSON.stringify(value));
      if (
        valueClone["KpiInfo"] != "%" &&
        valueClone["KpiInfo"] != "x" &&
        valueClone["KpiInfo"] != "#" &&
        valueClone["KpiInfo"] != "Text" &&
        valueClone["KpiInfo"] != ""
      ) {
        switch (Number(masterValueUnit.typeId)) {
          case FinancialValueUnitsEnum.Absolute:
            valueClone = local.conversionThousandToAbsoluteValue(
              valueClone,
              local,
              1000
            );
            break;
          case FinancialValueUnitsEnum.Thousands:
            break;
          case FinancialValueUnitsEnum.Millions:
            valueClone = local.conversionValue(valueClone, local, 1000000);
            break;
          case FinancialValueUnitsEnum.Billions:
            valueClone = local.conversionValue(valueClone, local, 1000000000);
            break;
        }
      }
      local.tableResult.push(valueClone);
    });
  }
  /**
   * Converts the values in the given object from thousands to absolute values.
   *
   * @param valueClone - The object containing the values to be converted.
   * @param local - An object containing the table columns.
   * @param value - The multiplier value to convert thousands to absolute values.
   * @returns The object with converted values.
   */
  conversionValue(valueClone: any, local: any, value: any) {
    local.tableColumns.forEach((col: any, index: any) => {
      if (valueClone[col.field] != 0) {
        valueClone[col.field] = !isNil(valueClone[col.field])
          ? !isNaN(
            parseFloat(
              valueClone[col.field].indexOf(",") > -1
                ? valueClone[col.field].replace(/,/g, "")
                : valueClone[col.field]
            )
          )
            ? ((valueClone[col.field].indexOf(",") > -1
              ? valueClone[col.field].replace(/,/g, "")
              : valueClone[col.field]) *1000) / value
            : valueClone[col.field]
          : valueClone[col.field];
      }
    });
    return valueClone;
  }
  /**
   * Converts the values in the given object from thousands to absolute values.
   *
   * @param valueClone - The object containing the values to be converted.
   * @param local - An object containing the table columns.
   * @param value - The multiplier value to convert thousands to absolute values.
   * @returns The object with converted values.
   */
  conversionThousandToAbsoluteValue(valueClone: any, local: any, value: any) {
    local.tableColumns.forEach((col: any, index: any) => {
      if (valueClone[col.field] != 0) {
        valueClone[col.field] = !isNil(valueClone[col.field])
          ? !isNaN(
            parseFloat(
              valueClone[col.field].indexOf(",") > -1
                ? valueClone[col.field].replace(/,/g, "")
                : valueClone[col.field]
            )
          )
            ? (valueClone[col.field].indexOf(",") > -1
              ? valueClone[col.field].replace(/,/g, "")
              : valueClone[col.field]) * value
            : valueClone[col.field]
          : valueClone[col.field];
      }
    });
    return valueClone;
  }
    getCashflowValues(
      event: any,
      searchFilter: any,
      companyId: any,
      currencyCode: any
    ) {
      this.isLoader = true;
      if (event == null) {
        event = {
          first: 0,
          rows: 1000,
          globalFilter: null,
          sortField: "displayOrder",
          multiSortMeta: this.cashflowValuesMultiSortMeta,
          sortOrder: -1,
        };
      }
      if (searchFilter == null) {
        searchFilter = {
          sortOrder: null,
          periodType: this.model.periodType.type,
        };
        if (searchFilter.periodType == "Date Range") {
          searchFilter.startPeriod = new Date(
            Date.UTC(
              this.model.startPeriod.getFullYear(),
              this.model.startPeriod.getMonth(),
              this.model.startPeriod.getDate()
            )
          );
          searchFilter.endPeriod = new Date(
            Date.UTC(
              this.model.endPeriod.getFullYear(),
              this.model.endPeriod.getMonth(),
              this.model.endPeriod.getDate()
            )
          );
        }
      } else {
        if (searchFilter.periodType == "Date Range") {
          searchFilter.startPeriod = new Date(
            Date.UTC(
              searchFilter.startPeriod.getFullYear(),
              searchFilter.startPeriod.getMonth(),
              searchFilter.startPeriod.getDate()
            )
          );
          searchFilter.endPeriod = new Date(
            Date.UTC(
              searchFilter.endPeriod.getFullYear(),
              searchFilter.endPeriod.getMonth(),
              searchFilter.endPeriod.getDate()
            )
          );
        }
      }
      let model = this.getDataModel(event, searchFilter);
      this.cashflowBetaService.getPCCashFlowValues(model).subscribe({
        next:(result) => {
          if (result?.rows?.length > 0) {
            this.currentValueType = this.valueType;
            this.setCurrentPeriodType(result.isMonthly,result.isQuarterly,result.isAnnually);
            this.setValueType(result.isMonthly,result.isQuarterly,result.isAnnually);
            this.isLoader = false;
            this.isPageLoad = false;
            if(this.isYtd)
              this.isYtdPageLoad = false;
            if(this.isLtm)
              this.isLtmPageLoad = false;
            this.tableColumns = result?.headers || [];
            this.tableResult = result?.rows || [];
            this.tableResultClone = result?.rows || [];
            this.convertUnits();
            this.auditLogList = result?.financialKpiAuditlog|| [];
            this.tableFrozenColumns = this.frozenHeader;
          } else {
            this.currentValueType = undefined;
            this.resetTable();
            this.isLoader = false;
          }
        },
        error:(error) => {
          this.currentValueType = undefined;
          this.resetTable();
          this.isLoader = false;
        }
    });
    }
    setCurrentPeriodType (isMonthly:boolean,isQuarterly:boolean,isAnnually:boolean) {
      switch (true) {
        case isMonthly:
          this.currentPeriodType = PeriodType.Monthly;
          break;
        case isQuarterly:
          this.currentPeriodType = PeriodType.Quarterly;
          break;
        case isAnnually:
          this.currentPeriodType = PeriodType.Annually;
          break;
      }
    }
    resetTable() {
      this.isLoader = false;
      this.tableResult = [];
      this.tableResultClone = [];
      this.tableFrozenColumns = [];
      this.auditLogList=[];
    }
    setValueType(isMonthly:boolean,isQuarterly:boolean,isAnnually:boolean)
  {
    let valueTypes: any = {
      isMonthly:isMonthly,
      isQuarterly:isQuarterly,
      isAnnually:isAnnually
    };
    this.onChangeValueType.emit(valueTypes);
  }
    getDataModel(event: any, searchFilter: any) {
      let model = {
        CompanyId: this.model.portfolioCompany.portfolioCompanyID,
        paginationFilter: event,
        searchFilter: searchFilter,
        segmentType: null,
        currencyCode: this.model?.currencyCode?.currencyCode,
        reportingCurrencyCode: this.model.portfolioCompany.companyCurrency,
        currencyRateSource: this.model?.fxRates?.type,
        valueType: this.valueTypeString != undefined ?  this.valueTypeString : this.valueType,
        isMonthly: this.periodType == PeriodType.Monthly ? true:false,
        isQuarterly: this.periodType == PeriodType.Quarterly ? true:false,
        isAnnually: this.periodType == PeriodType.Annually ? true:false,
        PeriodType: this.periodType,
        ModuleId: 9,
        Kpis:null,
        isPageLoad:this.isPageLoad,
        isYtdPageLoad:this.isYtdPageLoad,
        isLtmPageLoad:this.isLtmPageLoad,
        kpiConfigurationData:this.pageConfigData.kpiConfigurationData,
        isYtd : this.isYtd,
        isLtm : this.isLtm,
        IsSpotRate : this.model.isSpotRate == null ? false : this.model.isSpotRate,
        SpotRateDate:this.model.isSpotRate ? this.datePipe.transform(this.model.spotRateDate, 'yyyy-MM-dd') : null,
        SpotRate:this.model.isSpotRate ? this.model.spotRate : null
      };
      return model;
    }
    isNumberCheck(str: any) {
      return isNumeric(str);
    }
    exportCashflowData() {
      this.isDownloading.emit(true);
      let event = {
        first: 0,
        rows: 1000,
        globalFilter: null,
        sortField: "displayOrder",
        multiSortMeta: this.cashflowValuesMultiSortMeta,
        sortOrder: -1,
      };
      this.cashflowBetaService
        .exportCompanyCashflow({
          currency: this.model.portfolioCompany.companyCurrency,
          currencyCode: this.model?.currencyCode?.currencyCode,
          reportingCurrencyCode: this.model.portfolioCompany.companyCurrency,
          CompanyId: this.model.portfolioCompany.portfolioCompanyID,
          paginationFilter: event,
          segmentType: null,
          searchFilter: this.cashflowSearchFilter,
          currencyRateSource: this.model?.fxRates?.type,
          valueType:this.valueTypeString != undefined ?  this.valueTypeString : this.valueType,
          isMonthly: this.periodType == PeriodType.Monthly ? true:false,
          isQuarterly: this.periodType == PeriodType.Quarterly ? true:false,
          isAnnually: this.periodType == PeriodType.Annually ? true:false,
          ModuleId: 9,
          Kpis:null,
          isPageLoad:this.isPageLoad,
          kpiConfigurationData:this.pageConfigData.kpiConfigurationData,
          unit:this.model.currecyUnit.typeId,
          IsSpotRate : this.model.isSpotRate == null ? false : this.model.isSpotRate,
          SpotRateDate:this.model.isSpotRate ? this.datePipe.transform(this.model.spotRateDate, 'yyyy-MM-dd') : null,
          SpotRate:this.model.isSpotRate ? this.model.spotRate : null
        })
        .subscribe(
          (response) => {
            this.miscService.downloadExcelFile(response);
            this.isDownloading.emit(false);
          },
          (error) => {
            this.isDownloading.emit(false);
          }
        );
    }
    getFilterAuditValue(rowdata: any, column: any) {
      let calccolumn = "Calc"+" "+column.field;
      if(rowdata[calccolumn] !=undefined && rowdata[calccolumn])
      {
        return [];
      }
      else{
        let headers = column.field.split(' ');
        let auditList = this.auditLogList;
        let periodHeader = null;
        let yearHeader = null;
        let monthValue = null;
        if (headers?.length > 0 && auditList?.length > 0) {
          if (headers.length == 1)
            yearHeader = parseInt(headers[0]);
          else {
            periodHeader = headers[0];
            yearHeader = parseInt(headers[1]);
            if (!periodHeader.toLowerCase().includes("q"))
              monthValue = this.miscService.getMonthNumber(periodHeader);
          }
        }
        return this.filterAuditValue(yearHeader, monthValue, auditList, periodHeader, rowdata);
      }
    }
    filterAuditValue(yearHeader: any, monthValue: any, auditList: any, periodHeader: any, rowdata: any) {
      let result = [];
      if (periodHeader == "Q1" || periodHeader == "Q2" || periodHeader == "Q3" || periodHeader == "Q4")
        result = auditList.filter(x => x.quarter == periodHeader && x.year == yearHeader && x.mappingId == rowdata.MappingId && x.kpiValueId > 0);
      else if (monthValue != null && monthValue > 0)
        result = auditList.filter(x => x.month == monthValue && x.year == yearHeader && x.mappingId == rowdata.MappingId && x.kpiValueId > 0);
      else
        result = auditList.filter(x => (x.month == null || x.month == 0) && x.year == yearHeader && (x.Quarter == null || x.Quarter == '') && x.mappingId == rowdata.MappingId && x.kpiValueId > 0);
      return result
    }
    printColumn(rowData: any, column: any) {
      let result = this.getFilterAuditValue(rowData, column);
      if (result.length > 0) 
        return result[0].acutalAuditLog ? result[0].acutalAuditLog : false;
      else
        return false;
    }
    printCalcColumn(rowData: any, column: any) {
      let calccolumn = "Calc"+" "+column.field;
      if(rowData[calccolumn] !=undefined && rowData[calccolumn])
      {
        return true;
      }
        return false;
    }
    onAuditLog(rowData: any, field: any) {
      if (!this.isErrorLog || rowData.IsHeader) {
        if (this.isErrorLog) {
          this.showErrorToast(this.auditLogErrorForConvertedValue);
        }
        return;
      }
          if (this.checkCalcColumn(rowData, field)) {
      const message = getConversionErrorMessage(
        this.model.isSpotRate,
        this.auditLogSpotRateConversionError,
        this.auditLogErrorForConvertedValue
      );
      this.showErrorToast(message);
      return;
    }
      const dateComponents = extractDateComponents(field.header);
      let auditLogFilter = this.getAuditLogFilter(rowData, dateComponents);
      this.auditService.getPortfolioEditSupportingCommentsData(auditLogFilter).subscribe({
        next: (data: MappedDocuments) => {
          if (data?.auditLogId > 0 && data?.auditLogCount > 0) {
            let attributeName = rowData.Kpi;
            this.redirectToAuditLogPage(field, attributeName, data, auditLogFilter);
          } else if (data?.auditLogCount == 0) {
            this.showErrorToast(GlobalConstants.AuditLogNAMessage);
          }
        },
        error: (error: any) => {
          this.showErrorToast(this.auditLogErrorForConvertedValue);
        },
      });
    }

    redirectToAuditLogPage(field: any, attributeName: any, data: MappedDocuments, auditLogFilter: Audit) {
      let params = {
        KPI: this.tabName,
            header: field.header,
            PortfolioCompanyID: this.model.portfolioCompany.portfolioCompanyID,
            AttributeName: attributeName,
            ModuleId: KPIModulesEnum.CashFlow,
            Comments: this.valueTypeString != undefined ?  this.valueTypeString : this.valueType,
            currency: this.model.portfolioCompany.companyCurrency,
            AttributeId: data.valueId,
            isNewAudit: true,
            KpiId: auditLogFilter.kpiId,
            MappingId: auditLogFilter.mappingId,
            SectionId: this.sectionId
      };
      sessionStorage.setItem(GlobalConstants.CurrentModule, KpiTypesConstants.CASHFLOW_KPI);
      sessionStorage.setItem(GlobalConstants.CashFlowAuditLocalStorage, JSON.stringify(params));
      let config = this.identityService.getEnvironmentConfig();
      if (config.redirect_uri != "") {
        let myAppUrl = config.redirect_uri.split("/in")[0] + GlobalConstants.FinancialsKpiAuditUrl;
        window.open(myAppUrl, '_blank');
      }
    }

    getAuditLogFilter(rowData: any, dateComponents: { year: any; month: number; quarter: any; }) {
      return <Audit>{
        valueType: this.valueTypeString != undefined ?  this.valueTypeString : this.valueType,
        kpiId: rowData["LineItemId"],
        mappingId: rowData["MappingId"],
        quarter: dateComponents.quarter,
        year: dateComponents.year,
        month: dateComponents.month,
        moduleId: KPIModulesEnum.CashFlow,
        companyId: this.model.portfolioCompany.portfolioCompanyID
      };
    }
    getValues(rowdata: any, column: any) {
      let result = this.getFilterAuditValue(rowdata,column);
      if (result.length > 0) {
        return result[0];
      }
      else  
      return null;
  }

  onEditInit(rowData: any, column: any) {
    if(!this.cfEditPermission){
      this.showNoAccessError();
      return;
    }
    if(this.checkCalcColumn(rowData, column) &&  (!this.isErrorLog)){
      const message = getConversionErrorMessage(
        this.model.isSpotRate,
        this.editSpotRateConversionError,
        this.editErrorForConvertedValue
      );
      this.showErrorToast(message);
      return;
    }
    if (!this.isErrorLog) {
        if (!rowData.IsHeader && this.model.currecyUnit.typeId == FinancialValueUnitsEnum.Thousands) {
            this.uniqueModuleCompany = {
                moduleId: this.kpiModuleId,
                companyId: this.model?.portfolioCompany?.portfolioCompanyID,
                valueType: this.valueTypeString != undefined ?  this.valueTypeString : this.valueType,
                subPageId: 0
            };
            this.dataRow = rowData;
            this.dataColumns = column;
            this.showEditPopup = true;
        } else {
          if (this.model.currecyUnit.typeId != FinancialValueUnitsEnum.Thousands) {
            this.infoUpdate = true;
          }
          else
            this.showErrorToast(this.editErrorForConvertedValue);
        }
    }
  }
  checkCalcColumn(rowData: any, column: any): boolean {
    let calColumn = `Calc ${column.field}`;
    return Boolean(rowData[calColumn]);
  }
  onSubmitButtonEvent(results: any) {
    if (results.code != null && results.code.trim().toLowerCase() == BulkuploadConstants.ok) {
      this.showSuccessToast(results.message);
      this.showEditPopup = false;
    } else {
      this.showErrorToast(results.message);
      this.showEditPopup = false;
    }
    this.getCashflowData();
  }
  showErrorToast(message: string, title: string = '', position: string = BulkuploadConstants.ToasterMessagePossition): void {
    this.toasterService.error(message, title, { positionClass: position });
  }
  showSuccessToast(message: string, title: string = '', position: string = BulkuploadConstants.ToasterMessagePossition): void {
    this.toasterService.success(message, title, { positionClass: position });
  }
  cancelButtonEvent() {
    this.showEditPopup = false;
  }
}
