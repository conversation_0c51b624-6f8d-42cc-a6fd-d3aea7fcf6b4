export class ExtractionUiConstants {
    public static readonly EXTRACT_SPREAD_DETAILS = 'extraction/api/spread-details';
    public static readonly EXTRACT_UPLOAD= "extraction/api/upload";
    public static readonly EXTRACT_DOWNLOAD_FILE="extraction/api/download-file";
    public static readonly EXTRACT_FILE_DELETE="extraction/api/delete-file";
    public static readonly UPDATE_FILE_CONFIGURATIONS="extraction/api/update-file-configs";
    public static readonly DOC_INGEST="api/ai/doc/ingest";
    public static readonly FINAL_DOC_INGEST="api/ai/doc/ingest";
    public static readonly APP_Key ="app_name";
    public static readonly API_X_Key ="x-api-key";
    public static readonly API_Name = "foliosure";
    public static readonly SPREAD_FS_START = (job_id: string) => `api/ai/spread/fs/${job_id}/start`;
    public static readonly SPREAD_FS_CLASSIFIER = (job_id: string) => `api/ai/spread/fs/${job_id}/classifier`;
    public static readonly EXTRACT_DATA_STATUS = (job_id: string) => `api/ai/extract/${job_id}/data/status`;
    public static readonly EXTRACT_DOC_EXTRACTION = (job_id: string) => `api/ai/spread/extract/${job_id}/start`;
    public static readonly Job_CREATE_UPDATE="extraction/api/job";
    public static readonly Job_Properties=['jobId', 'processId', 'tenantId', 'statusId', 'parentJobId'];
    public static readonly StateAndStatus="extraction/api/status";
    public static readonly JOB_ID_STATUS = (parentJobId: string): string => `api/ai/extract/${parentJobId}/data/status`;
    public static readonly AddOrUpdateClassifier = (processId: string): string => `extraction/api/classifier-data/${processId}`;
    public static readonly GET_PROCESS_DeTAILS = (processId: string): string => `extraction/api/process-details/${processId}`;
    public static readonly EMPTY_GUID="00000000-0000-0000-0000-000000000000";
    public static readonly FetchClassifier = (processId: string): string => `extraction/api/classifier-info/${processId}`;
    public static readonly STATUS_COUNT= "extraction/api/status-count";
    public static readonly KPI_EXTRACT_DOC_EXTRACTION = (job_id: string) => `api/ai/spread/kpi/${job_id}/start`;
    public static readonly SPECIFIC_KPI_EXTRACTION = (processId: string): string => `extraction/api/add-specific-kpi/${processId}`;
}
export const StateDescriptions = {
    DATA_INGESTION_COMPLETED: 'Data Ingestion Completed',
    DATA_INGESTION_FAILED: 'Data Ingestion Failed',
    EXTRACTION_DRAFT: 'Extraction Draft',
    FILE_DRAFT_BEFORE_EXTRACTION: 'File Draft before Extraction',
    DATA_INGESTION_IN_PROGRESS: 'Data Ingestion in progress',
    EXTRACTION_COMPLETED: 'Extraction Completed',
    EXTRACTION_IN_PROGRESS: 'Extraction in progress',
    API_FAILED:"Api Failed"
} as const;
export const StatusConstants={
    RUNNING: "RUNNING",
    COMPLETED: "COMPLETED",
}
export const ExtractionDefaultPeriodTypes = [
    { id: 'year', name: 'Year' }, 
    { id: 'month', name: 'Month' },
    { id: 'quarter', name: 'Quarter' }
];
