import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { DataIngestionComponent } from './data-ingestion.component';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { FundService } from 'src/app/services/funds.service';
import { of, throwError } from 'rxjs';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { GridModule } from '@progress/kendo-angular-grid';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { DataIngestionService } from 'src/app/services/data-ingestion.service';
import { Router } from '@angular/router';
import { FileSharingService } from 'src/app/services/file-sharing.service';
import { ExtractionSharedService } from 'src/app/services/extraction-shared.service';
import { DataIngestion } from 'src/app/common/enums';
import { AccountService } from 'src/app/services/account.service';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { KPIDataService } from 'src/app/services/kpi-data.service';

describe('DataIngestionComponent', () => {
  let component: DataIngestionComponent;
  let fixture: ComponentFixture<DataIngestionComponent>;
  let fundServiceSpy: jasmine.SpyObj<FundService>;
  let dataIngestionServiceSpy: jasmine.SpyObj<DataIngestionService>;
  let routerSpy: jasmine.SpyObj<Router>;
  let fileSharingServiceSpy: jasmine.SpyObj<FileSharingService>;
  let extractionSharedServiceSpy: jasmine.SpyObj<ExtractionSharedService>;

  const mockDealPCFunds = [
    { 
      fundName: 'Fund 1', 
      companyName: 'Company 1', 
      id: 1 
    },
    { 
      fundName: 'Fund 1', 
      companyName: 'Company 2', 
      id: 2 
    }
  ];

  beforeEach(async () => {
    const fundSpy = jasmine.createSpyObj('FundService', ['getFundsAndPcs']);
    fundSpy.getFundsAndPcs.and.returnValue(of(mockDealPCFunds));
    
    const dataIngestionSpy = jasmine.createSpyObj('DataIngestionService', [
      'getDataSourceTypes',
      'getInvestmentDetails',
      'getSubPageFieldsBySubPageId',
      'getFundKpiDetails',
      'getCompaniesKpiDetails'
    ]);
    dataIngestionSpy.getDataSourceTypes.and.returnValue(of([]));
    dataIngestionSpy.getInvestmentDetails.and.returnValue(of([]));
    dataIngestionSpy.getSubPageFieldsBySubPageId.and.returnValue(of([]));
    dataIngestionSpy.getFundKpiDetails.and.returnValue(of([]));
    dataIngestionSpy.getCompaniesKpiDetails.and.returnValue(of([]));
    
    const routerMock = jasmine.createSpyObj('Router', ['navigate']);
    const fileSharingMock = jasmine.createSpyObj('FileSharingService', ['shareFiles']);
    
    // Create mock for ExtractionSharedService
    const extractionSharedMock = jasmine.createSpyObj('ExtractionSharedService', ['get']);
    extractionSharedMock.get.and.returnValue(of([]));

    await TestBed.configureTestingModule({
      declarations: [ DataIngestionComponent ],
      imports: [
        ReactiveFormsModule,
        DropDownsModule,
        GridModule,
        BrowserAnimationsModule,
        ToastrModule.forRoot(),
        HttpClientTestingModule
      ],
      providers: [
        FormBuilder,
        { provide: FundService, useValue: fundSpy },
        { provide: DataIngestionService, useValue: dataIngestionSpy },
        { provide: Router, useValue: routerMock },
        { provide: FileSharingService, useValue: fileSharingMock },
        { provide: ExtractionSharedService, useValue: extractionSharedMock },
        { provide: AccountService, useValue: {} },
        { provide: PortfolioCompanyService, useValue: {} }, // <-- Add mock provider for PortfolioCompanyService
        { provide: KPIDataService, useValue: {} }, // <-- Add mock provider for KPIDataService
        { provide: 'BASE_URL', useValue: 'http://localhost/' }, // <-- Add mock provider for BASE_URL
        { provide: 'apiIngestionBaseUrl', useValue: 'http://localhost/' }, // <-- Add mock provider for apiIngestionBaseUrl
      ]
    }).compileComponents();

    fundServiceSpy = TestBed.inject(FundService) as jasmine.SpyObj<FundService>;
    dataIngestionServiceSpy = TestBed.inject(DataIngestionService) as jasmine.SpyObj<DataIngestionService>;
    routerSpy = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    fileSharingServiceSpy = TestBed.inject(FileSharingService) as jasmine.SpyObj<FileSharingService>;
    extractionSharedServiceSpy = TestBed.inject(ExtractionSharedService) as jasmine.SpyObj<ExtractionSharedService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DataIngestionComponent);
    component = fixture.componentInstance;
    spyOn(component, 'initializeSignalRConnection').and.stub(); // Prevent real SignalR connection
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load companies on init', () => {
    expect(fundServiceSpy.getFundsAndPcs).toHaveBeenCalled();
    expect(component.companyList.length).toEqual(2);
  });

  it('should handle error when loading companies fails', fakeAsync(() => {
    fundServiceSpy.getFundsAndPcs.and.returnValue(throwError(() => new Error('Error')));
    component.getFundWiseCompanies();
    tick();
    expect(component.groupedCompanyList).toEqual([]);
    expect(component.companyLoading).toBeFalse();
  }));

  it('should filter companies based on search text', () => {
    component.customFilter('Company 1');
    expect(component.groupedCompanyList[0].items.length).toBe(1);
  });
  it('should handle period changes correctly', () => {
    component.ingestionForm.get('period')?.setValue('Month');
    expect(component.periodOptions).toEqual(component.months);

    component.ingestionForm.get('period')?.setValue('Quarter');
    expect(component.periodOptions).toEqual(component.quarters);

    component.ingestionForm.get('period')?.setValue('Year');
    expect(component.periodOptions).toEqual([]);
  });

  it('should reset form and clear files', () => {
    component.ingestionForm.get('module')?.setValue('test');
    component.selectedFilesList = [{ documentType:null,type:'', name: 'test', size: 100,id:"abc",status:'invalid',errors:[],file:new File([''], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }) }];
    
    component.reset();
    
    expect(component.ingestionForm.get('module')?.value).toBeNull();
    expect(component.selectedFilesList.length).toBe(0);
  });

  it('should handle file selection with maximum files limit', () => {
    const event = {
      target: {
        files: Array(26).fill(new File([''], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }))
      }
    };
    
    component.onFileSelected(event);
    expect(component.fileInput.nativeElement.value).toEqual('');
  });
  
  it('should update pagination correctly', () => {
    component.pageChange({ skip: 10 });
    expect(component.skip).toBe(10);

    component.pageSizeChange({ target: { value: 20 } });
    expect(component.pageSize).toBe(20);
  });

  describe('getPageConfigSetting', () => {
    let pageConfigurationServiceSpy: any;
    beforeEach(() => {
      // Add a spy for pageConfigurationService
      pageConfigurationServiceSpy = jasmine.createSpyObj('PageConfigurationService', ['getPageConfigSettingById']);
      (component as any).pageConfigurationService = pageConfigurationServiceSpy;
      // Use the actual DataIngestion enum
      component.DataIngestion = DataIngestion;
    });

    it('should set display names when result contains expected fields', fakeAsync(() => {
      const mockResult = {
        fieldValueList: [
          { name: DataIngestion.SpecificKpiExtraction, displayName: 'Specific KPI Extraction' },
          { name: DataIngestion.AsIsExtraction, displayName: 'As Is Extraction' }
        ]
      };
      pageConfigurationServiceSpy.getPageConfigSettingById.and.returnValue(of(mockResult));
      component.getPageConfigSetting();
      tick();
      expect(component.specificKpiDisplayName).toBe('Specific KPI Extraction');
      expect(component.asIsExtractionDisplayName).toBe('As Is Extraction');
    }));

    it('should set display names to empty string if fields are missing', fakeAsync(() => {
      const mockResult = { fieldValueList: [] };
      pageConfigurationServiceSpy.getPageConfigSettingById.and.returnValue(of(mockResult));
      component.getPageConfigSetting();
      tick();
      expect(component.specificKpiDisplayName).toBe('');
      expect(component.asIsExtractionDisplayName).toBe('');
    }));

    it('should not throw if result is null', fakeAsync(() => {
      pageConfigurationServiceSpy.getPageConfigSettingById.and.returnValue(of(null));
      expect(() => {
        component.getPageConfigSetting();
        tick();
      }).not.toThrow();
    }));
  });
});
