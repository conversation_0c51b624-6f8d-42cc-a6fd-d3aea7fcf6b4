import { Component, Input, OnInit, Output, EventEmitter } from "@angular/core";

@Component({
  selector: "nep-button",
  templateUrl: "./button.component.html",
  styleUrls: ["./button.component.scss"],
})
export class Button implements OnInit {
  @Input() Type: String = "";
  @Input() Name: String = "";
  @Input() disabled: boolean = false;
  @Input() ngClass: any; // Accept ngClass from parent
  ngOnInit(): void {}
  @Output() onClick: EventEmitter<any> = new EventEmitter();
  @Output() onFocus: EventEmitter<any> = new EventEmitter();
  @Output() onBlur: EventEmitter<any> = new EventEmitter();

  getStyleClass() {
    return {
      "nep-button": true,
      "btn-space": true,
      "nep-button-primary": this.Type === "Primary",
      "nep-button-secondary": this.Type === "Secondary",
      "nep-button-danger":this.Type =="Danger",
    };
  }
  getMergedClass() {
    const base = this.getStyleClass();
    if (!this.ngClass) return base;
    if (typeof this.ngClass === 'string') {
      // Split string by space and set true for each class
      this.ngClass.split(' ').forEach((cls: string) => { if (cls) base[cls] = true; });
      return base;
    }
    if (Array.isArray(this.ngClass)) {
      this.ngClass.forEach((cls: string) => { if (cls) base[cls] = true; });
      return base;
    }
    if (typeof this.ngClass === 'object') {
      return { ...base, ...this.ngClass };
    }
    return base;
  }
}
