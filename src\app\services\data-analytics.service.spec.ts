import { TestBed } from "@angular/core/testing";
import {
  HttpClientTestingModule,
  HttpTestingController
} from "@angular/common/http/testing";
import { Router } from "@angular/router";
import { DataAnalyticsService } from "./data-analytics.service";

describe("DocumentService", () => {
  let service: DataAnalyticsService;

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        DataAnalyticsService, 
        { provide: 'BASE_URL', useValue: 'http://localhost'},
        { provide: Router, useValue: routerSpy }
      ]
    });
    service = TestBed.get(DataAnalyticsService);
  });

  it("can load instance", () => {
    expect(service).toBeTruthy();
  });
  describe('errorHandler', () =>{
    it('spyOn errorHandler', () =>{
      let error = 'Errors occured'
      spyOn(service, 'errorHandler').and.callThrough();
      service.errorHandler(error);
      expect(service.errorHandler).toHaveBeenCalled();
    })
  });
  describe('getDataAnalyticsList', () =>{
    it('spyOn getDataAnalyticsList', () =>{
      const isCustom = true;
      spyOn(service, 'getDataAnalyticsList').and.callThrough();
      service.getDataAnalyticsList(isCustom);
      expect(service.getDataAnalyticsList).toHaveBeenCalled();
    })
  });
  describe('http getDataAnalyticsList',() => {
    it('http getDataAnalyticsList', () => {
      const httpTestingController = TestBed.get(HttpTestingController);
        const expected = 'Reportedone';
        const isCustom = true;
        service.getDataAnalyticsList(isCustom).subscribe((data) =>{
          expect(data).toEqual(expected);
        });
         const req = httpTestingController.expectOne(service.myAppUrl +  "api/dataAnalytics/get/" + isCustom);
         req.flush(expected);
         httpTestingController.verify();
      }
    )}
  );
  describe('GetAllFilterData', () =>{
    it('spyOn GetAllFilterData', () =>{
      spyOn(service, 'GetAllFilterData').and.callThrough();
      service.GetAllFilterData();
      expect(service.GetAllFilterData).toHaveBeenCalled();
    })
  });
  describe('http GetAllFilterData',() => {
    it('http GetAllFilterData', () => {
      const httpTestingController = TestBed.get(HttpTestingController);
        const expected = 'Reportedone';
        service.GetAllFilterData().subscribe((data) =>{
          expect(data).toEqual(expected);
        });
         const req = httpTestingController.expectOne(service.myAppUrl +  "api/data-analytics/filters",{});
         req.flush(expected);
         httpTestingController.verify();
      }
    )}
  );
  describe('GetCompanies', () =>{
    it('spyOn GetCompanies', () =>{
        let filter = 'test';
      spyOn(service, 'GetCompanies').and.callThrough();
      service.GetCompanies(filter);
      expect(service.GetCompanies).toHaveBeenCalled();
    })
  });
  describe('http GetCompanies',() => {
    it('http GetCompanies', () => {
      const httpTestingController = TestBed.get(HttpTestingController);
        const expected = 'Reportedone';
        let filter = 'test';
        service.GetCompanies(filter).subscribe((data) =>{
          expect(data).toEqual(expected);
        });
         const req = httpTestingController.expectOne(service.myAppUrl +  "api/data-analytics/filters/companies", filter);
         req.flush(expected);
         httpTestingController.verify();
      }
    )}
  );
  describe('GetFundsByInvestor', () =>{
    it('spyOn GetFundsByInvestor', () =>{
        let filter = 'test';
      spyOn(service, 'GetFundsByInvestor').and.callThrough();
      service.GetFundsByInvestor(filter);
      expect(service.GetFundsByInvestor).toHaveBeenCalled();
    })
  });
  describe('http GetFundsByInvestor',() => {
    it('http GetFundsByInvestor', () => {
      const httpTestingController = TestBed.get(HttpTestingController);
        const expected = 'Reportedone';
        let filter = 'test';
        service.GetFundsByInvestor(filter).subscribe((data) =>{
          expect(data).toEqual(expected);
        });
         const req = httpTestingController.expectOne(service.myAppUrl +  "api/data-analytics/filters/funds", filter);
         req.flush(expected);
         httpTestingController.verify();
      }
    )}
  );
  describe('getInvestorsList', () =>{
    it('spyOn getInvestorsList', () =>{
      spyOn(service, 'getInvestorsList').and.callThrough();
      service.getInvestorsList();
      expect(service.getInvestorsList).toHaveBeenCalled();
    })
  });
  describe('http getInvestorsList',() => {
    it('http getInvestorsList', () => {
      const httpTestingController = TestBed.get(HttpTestingController);
        const expected = 'Reportedone';
        service.getInvestorsList().subscribe((data) =>{
          expect(data).toEqual(expected);
        });
         const req = httpTestingController.expectOne(service.myAppUrl + "api/data-analytics/filters/investors");
         req.flush(expected);
         httpTestingController.verify();
      }
    )}
  );
  describe('GetKpisByModuleId', () =>{
    it('spyOn GetKpisByModuleId', () =>{
        let filter = 'test';
      spyOn(service, 'GetKpisByModuleId').and.callThrough();
      service.GetKpisByModuleId(filter);
      expect(service.GetKpisByModuleId).toHaveBeenCalled();
    })
  });
  describe('http GetKpisByModuleId',() => {
    it('http GetKpisByModuleId', () => {
      const httpTestingController = TestBed.get(HttpTestingController);
        const expected = 'Reportedone';
        let filter = 'test';
        service.GetKpisByModuleId(filter).subscribe((data) =>{
          expect(data).toEqual(expected);
        });
         const req = httpTestingController.expectOne(service.myAppUrl +  "api/data-analytics/filters/kpis", filter);
         req.flush(expected);
         httpTestingController.verify();
      }
    )}
  );

  describe('Filter Data Communication', () => {
    describe('updateFilterData', () => {
      it('should update filter data subject with provided data', () => {
        const testData = { filters: { companies: ['test'], funds: ['fund1'] } };
        
        spyOn(service, 'updateFilterData').and.callThrough();
        service.updateFilterData(testData);
        
        expect(service.updateFilterData).toHaveBeenCalledWith(testData);
      });

      it('should emit new data through filterData$ observable', (done) => {
        const testData = { filters: { companies: ['company1'], funds: ['fund1'] } };
        let callCount = 0;
        
        const subscription = service.filterData$.subscribe(data => {
          callCount++;
          if (callCount === 2) { // Skip initial null value, check second emission
            expect(data).toEqual(testData);
            subscription.unsubscribe();
            done();
          }
        });
        
        service.updateFilterData(testData);
      });

      it('should handle null data correctly', (done) => {
        let callCount = 0;
        
        const subscription = service.filterData$.subscribe(data => {
          callCount++;
          if (callCount === 2) { // Skip initial null, check second null emission
            expect(data).toBeNull();
            subscription.unsubscribe();
            done();
          }
        });
        
        service.updateFilterData(null);
      });

      it('should handle empty object data', (done) => {
        const emptyData = {};
        let callCount = 0;
        
        const subscription = service.filterData$.subscribe(data => {
          callCount++;
          if (callCount === 2) { // Skip initial null value, check second emission
            expect(data).toEqual(emptyData);
            subscription.unsubscribe();
            done();
          }
        });
        
        service.updateFilterData(emptyData);
      });
    });

    describe('clearFilterData', () => {
      it('should clear filter data by setting it to null', () => {
        spyOn(service, 'clearFilterData').and.callThrough();
        service.clearFilterData();
        
        expect(service.clearFilterData).toHaveBeenCalled();
      });

      it('should emit null through filterData$ observable when cleared', (done) => {
        // First set some data
        const testData = { filters: { companies: ['test'] } };
        
        let callCount = 0;
        const subscription = service.filterData$.subscribe(data => {
          callCount++;
          if (callCount === 1) {
            expect(data).toBeNull(); // Initial null
          } else if (callCount === 2) {
            expect(data).toEqual(testData); // Test data
          } else if (callCount === 3) {
            expect(data).toBeNull(); // Cleared null
            subscription.unsubscribe();
            done();
          }
        });
        
        // Set data first, then clear
        service.updateFilterData(testData);
        service.clearFilterData();
      });

      it('should call next with null on filterDataSubject', () => {
        const nextSpy = spyOn(service['filterDataSubject'], 'next');
        service.clearFilterData();
        
        expect(nextSpy).toHaveBeenCalledWith(null);
      });
    });

    describe('filterData$ observable', () => {
      it('should be defined', () => {
        expect(service.filterData$).toBeDefined();
      });

      it('should emit multiple updates in sequence', (done) => {
        const testData1 = { filters: { companies: ['company1'] } };
        const testData2 = { filters: { funds: ['fund1'] } };
        let callCount = 0;
        
        const subscription = service.filterData$.subscribe(data => {
          callCount++;
          if (callCount === 1) {
            expect(data).toBeNull(); // Initial value
          } else if (callCount === 2) {
            expect(data).toEqual(testData1);
          } else if (callCount === 3) {
            expect(data).toEqual(testData2);
            subscription.unsubscribe();
            done();
          }
        });
        
        service.updateFilterData(testData1);
        service.updateFilterData(testData2);
      });
    });
  });
});