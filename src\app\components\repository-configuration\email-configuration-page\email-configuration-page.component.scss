@import "../../../../_variables.scss";
@import "../../../../assets/dist/css/font.scss";

.content-container {
  height: calc(100% - 50px);
  .email-notification-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;
    border: $border-color-dark;
    border-radius: $space-4;
  }
}

.email-container {
  .btn-primary {
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: $nep-padding;
      height: $nep-padding;
    }
  }
  
}