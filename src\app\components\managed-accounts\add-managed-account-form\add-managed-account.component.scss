@import "../../../../variables.scss";

button {
  background: var(--Color-Primary-Primary---78, #4061C7);
  color: white;
  border: none;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
}

.btn-save {
  height: 32px !important;
  padding: 6px 16px !important;
}

.btn-save:disabled {
  opacity: 0.5;
}

.heading {
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  text-align: left;
  padding: 24px;
}

.form-container {
  border: 1px solid #E6E6E6; 
  border-radius: 8px; 
  height: 580px;
}

.footer {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  background-color: #fff;
  position: absolute;
  bottom: -37px;
  width: -webkit-fill-available;
}

button[kendoButton] {
  margin-right: 10px;
}

.btn-reset-container {
  display: flex;
  justify-content: flex-start;
}

.btn-warning {
  background: #FFFFFF 0% 0% no-repeat padding-box !important;
  border: 1px solid #4061C7;
  border-radius: 4px;
  opacity: 1;
  color: var(--Color-Primary-Primary---78, #4061C7);
}

.custom-Fixedheight {
  height: 540px;
  padding-top: 2.5rem;
}

.custom-marginbottom {
  margin-bottom: 4px;
}

.disabled-save-btn {
  background: var(--Color-Primary-Primary---60, #93B0ED) !important;
  color: white !important; 
}

.disabled-btn {
  border-radius: 4px;
  color: #93B0ED !important; 
  border-color: #93B0ED !important; 
  background-color: transparent !important;
}

.label-error {
  color: #DE3139;
}

.main-Container-height {
  height: 100%;
}

.popup-text {  
  font-weight: 400;
  line-height: $popup-text-line-height;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}
