@import "../../../variables";
.portfolio-company-list-section {
    .performance-section {
        .outer-section {
            background: $nep-white 0% 0% no-repeat padding-box;
            border: 1px solid $nep-divider;
            border-radius: 4px 4px 4px 4px;
            opacity: 1;
            box-shadow: 0px 0px 12px $nep-shadow-color;
        }
        .nav-link {
            background-color: $nep-white !important;
            letter-spacing: 0px;
            color: $nep-text-grey;
            font-size: 0.9rem !important;
            padding-top: 9px;
            padding-bottom: 9px;
            &.active {
                background-color: $nep-white !important;
                color: $nep-primary !important;
               // @extend .Body-R;
            }
        }
        .tab-bg,.content-bg {
            background-color: $nep-white !important;
        }
    }
}