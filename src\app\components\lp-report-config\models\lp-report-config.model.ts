export interface FundTemplateModel {
    fundId: number;
    fundName: string;
}

export interface FundCompanyModel {
    fundId: number;
    fundName: string;
    companyId: number;
    companyName: string;
}

export interface TemplateSectionModel {
    sectionId: number;
    sectionName: string;
    isKpi: boolean;
    isStatic: boolean;
    isCommentary: boolean;
    kpiTypeId: number;
    moduleId: number;
    pageConfigAliasName: string;
    mappingSectionId:number;
}

export interface SectionStaticModel {
    fieldId: number;
    aliasName: string;
    subPageId: number;
}

export interface AlignmentModel {
    alignmentId: number;
    alignment: string;
}

export interface CurrencyModel {
    currencyId: number;
    currencyCode: string;
    currency: string;
}

export interface LpReportPeriodTypeModel {
    id: number;
    periodType: string;
    periodStatus: string;
}

export interface UnitTypeModel {
    unitTypeId: number;
    unitType: string;
}

export interface KpiPeriodTypeModel {
    moduleId: number;
    valueTypeOptions: string;
    valueType: string;
    valueTypeAlias: string;
    valueTypeId: number;
}

export interface LpReportKpiModel {
    mappingId: number;
    companyId: number;
    kpi: string;
    isHeader: boolean;
    isBoldKpi: boolean;
    companyName: string;
    moduleId: number;
}

export interface TemplateKpiModel {
    companyIds: string;
    moduleIds: string;
}

// Define the main interface
export interface LpReportConfigModel {
    fundList: FundTemplateModel[];
    companyList: FundCompanyModel[];
    alignments: AlignmentModel[];
    sectionFields: SectionStaticModel[];
    templateSections: TemplateSectionModel[];
    currencies: CurrencyModel[];
    periodTypeModels: LpReportPeriodTypeModel[];
    kpiPeriodTypes: KpiPeriodTypeModel[];
    unitTypes: UnitTypeModel[];
    frequencyModel: FrequencyModel[];
    companyModel: CompanyModel[];
    financialMetricsModel: FinancialMetricsModel[];
    dataOrder:DataOrder[]
}
export interface TemplateKpiModel {
    companyIds: string;
    moduleIds: string;
}
export interface LpReportKpiModel {
    mappingId: number;
    companyId: number;
    kpi: string;
    isHeader: boolean;
    isBoldKpi: boolean;
    companyName: string;
    moduleId: number;
}
export interface MappingLpReportSectionModel {
    sectionId: number;
    displayOrder: number;
    alignmentId: number;
    currencyId: number;
    unitTypeId: number;
    companyIds: string;
    valueTypeIds: string;
    mappingSectionId:number;
}

export interface MappingLpReportTemplateModel {
    templateId: number;
    fundId: string;
    companyId: string;
}
export interface MappingLpReportCommentarySectionModel {
    sectionId: number;
    commentaryIds: string;
    frequencyId:number;
    mappingSectionId:number;
}
export interface MappingLpReportKpiSectionModel {
    sectionId: number;
    companyId: number;
    kpiIds: string;
    mappingSectionId:number;
    periodCompareIds: string;
    dataOrder:string;
}
export interface MappingLpReportPeriodSectionModel {
    sectionId: number;
    valueTypeId: number;
    periodIds: string;
    mappingSectionId:number;
}
export interface LpTemplateModel {
    templateId: number;
    templateName: string;
}
export interface TemplateConfigModel {
    lpTemplateModel: LpTemplateModel;
    mappingLpReportSectionModels: MappingLpReportSectionModel[];
    mappingLpReportTemplateModel: MappingLpReportTemplateModel;
    mappingLpReportCommentarySectionModel: MappingLpReportCommentarySectionModel[];
    mappingLpReportKpiSectionModel: MappingLpReportKpiSectionModel[];
    mappingLpReportPeriodSectionModel: MappingLpReportPeriodSectionModel[];
}
export interface PdfDownloadModel extends LpTemplateModel {
    encryptedCompanyId: string; 
    encryptedFundId: string;
    downloadType:string; 
}
export interface FrequencyModel {
    label: string;
    id: number;
}
export interface CompanyModel {
    companyName: string;
    companyId: number;
}
export interface FinancialMetricsModel {
    lpReportMetricId: number;
    moduleId: number;
    headerValue: string;
    groupName: string;
}
export interface DataOrder extends FrequencyModel {
    sequence: number;
}