import { Component } from '@angular/core';
import { InvestCompanyService } from '../investmentcompany/investmentcompany.service';
import { Router } from '@angular/router';
import { CloListService } from './clo-list.service';
import { ToastrService } from 'ngx-toastr';
import { CLOConstants } from 'src/app/common/constants';
import { FeaturesEnum } from 'src/app/services/permission.service';
import { CommonSubFeaturePermissionService } from 'src/app/services/subPermission.service';
import { ErrorMessage } from 'src/app/services/miscellaneous.service';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';
@Component({
  selector: 'app-clo-list',
  templateUrl: './clo-list.component.html',
  styleUrls: ['./clo-list.component.scss']
})
export class CloListComponent {
  cloCompanyList: any[] = [];
  companyname: string = "";
  clo: any[] = [];
  confirmDelete:boolean=false;
  deletedCloName: string = "";
  deletedCloId: string = "";
  ShowDeletedSuccess:boolean=false;
  modalTitle: string = CLOConstants.Modal_Title;
  isLoading:boolean = true;

  constructor(private readonly gridDataService: InvestCompanyService,private readonly router: Router,
  private readonly cloListService: CloListService,
  private readonly toastrService: ToastrService,private readonly subPermissionService: CommonSubFeaturePermissionService,
private breadcrumbService: BreadcrumbService) {
  let newBreadcrumbs: any[] = [];
          this.breadcrumbService.setBreadcrumbs(newBreadcrumbs);
  
   }
   

  ngOnInit() {
    this.updateBreadcrumbs();
    this.fetchInvestmentCompanyList();
    this.getSubFeatureAccessPermissions();
  }
  fetchInvestmentCompanyList() {
    const filter = {};
    this.isLoading = true;
    this.gridDataService.getInvestCompanyListForClo(filter).subscribe(      
      (data) => {    
        this.isLoading = false;    
        this.cloCompanyList = data?.map(clo => ({
          name: clo.companyName, // Ensure this matches the property name in your data
          isExpanded: false,
          items: clo.items || [], // Ensure this matches the property name in your data
          id: clo.id
        }));
      },
      (error) => {
        this.isLoading = false;
        console.error('Error fetching investment company list', error);
      }
    );
  }

  expandPanel(clo: any) {
    this.cloCompanyList.forEach(comp => {
      if (comp !== clo) {
        comp.isExpanded = false;
      }
    });
    clo.isExpanded = !clo.isExpanded;

    this.cloListService.getClos(clo.id).subscribe(
      (response) => {   
                     
        clo.items = Array.isArray(response) ? response : []; 
      },
      error => {
        console.error('Error fetching CLO', error);
      }
    );
  }

  addCLO(clo: any) {  
    if (this.canAddCloList) {
      this.router.navigate(['/add-clo'], { queryParams: { name: clo.name, id: clo.id, uniqueId:'' } });
    } else {
      this.showNoAccessError();
    }  
  }

  cancelDelete() {
      this.confirmDelete = false;
    }

    showDelete(clo: any) {
      if (!this.canEditCloList) {
        this.showNoAccessError();
        return;
      }
      this.confirmDelete = true;
      this.deletedCloName=clo.issuer;
      this.deletedCloId=clo.uniqueID;
    }

    deleteCLO(){
      this.isLoading=true;
      this.confirmDelete = false;       
      this.cloListService
        .DeleteClo(this.deletedCloId)
        .subscribe({          
          next: (_response) => {
            this.isLoading=false;
            if(_response){
              this.fetchInvestmentCompanyList();
            this.ShowDeletedSuccess=true;
            const mess=`${CLOConstants.Success_message} "${this.deletedCloName}"`;
            this.toastrService.success(mess, "", { positionClass: "toast-center-center" }); 
            }
          },
          error: (_error) => {
            this.isLoading=false;
            this.toastrService.error(`${CLOConstants.Error_message} "${this.deletedCloName}"`,"", { positionClass: "toast-center-center" });
          },
        });
    }

  redirectToCloViewPage(uniqueId: string) : void{    
  this.router.navigate(['/view-clo-summary',uniqueId] );
  //call the end point to get the data
  }
  canAddCloList: boolean = false;
    canEditCloList:boolean = false;
    getSubFeatureAccessPermissions() {
      this.subPermissionService
        .getCommonFeatureAccessPermissions(FeaturesEnum.CLOPage)
        .subscribe({
          next: (result) => {
            if (result.length > 0) {
              this.canAddCloList = result?.map((x) => x.canAdd).includes(true);
              this.canEditCloList = result?.map((x) => x.canEdit).includes(true);
            }
          },
          error: (_error) => {},
        });
    }
  
        checkPermissionAccess(permission:any[], permissionType): boolean {
  
          return permission.map(x => x[permissionType]).includes(true);
        }
        showNoAccessError() {
          this.toastrService.error(ErrorMessage.NoAccess, "", { positionClass: "toast-center-center" });
        }
        updateBreadcrumbs() {
          let newBreadcrumbs: any[] = [];
      newBreadcrumbs.push( { label: 'Collateral Loan Obligation(CLO)', url: '/clo-list' });
    this.breadcrumbService.setBreadcrumbs(newBreadcrumbs);
        }
}
