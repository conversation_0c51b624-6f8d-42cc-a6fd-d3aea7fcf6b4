// import { ComponentFixture, TestBed } from '@angular/core/testing';

// import { GrowthReportComponent } from './growth-report.component';
// import { ReportDownloadService } from 'src/app/services/report-download.service';
// import { of } from 'rxjs';
// import { MatMenuModule } from '@angular/material/menu';
// import { EventEmitter, NO_ERRORS_SCHEMA } from '@angular/core';

// describe('GrowthReportComponent', () => {
//   let component: GrowthReportComponent;
//   let fixture: ComponentFixture<GrowthReportComponent>;

//   beforeEach(() => {
//     TestBed.configureTestingModule({
//       declarations: [GrowthReportComponent]
//     });
//     fixture = TestBed.createComponent(GrowthReportComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });

//   describe('GrowthReportComponent', () => {
//     let component: GrowthReportComponent;
//     let fixture: ComponentFixture<GrowthReportComponent>;
//     let reportDownloadService: jasmine.SpyObj<ReportDownloadService>;

//     beforeEach(() => {
//       const spy = jasmine.createSpyObj('ReportDownloadService', ['getFundList', 'GetCompanies']);

//       TestBed.configureTestingModule({
//         declarations: [GrowthReportComponent],
//         imports: [MatMenuModule],
//         providers: [
//           { provide: ReportDownloadService, useValue: spy }
//         ],
//         schemas: [NO_ERRORS_SCHEMA]
//       });

//       fixture = TestBed.createComponent(GrowthReportComponent);
//       component = fixture.componentInstance;
//       reportDownloadService = TestBed.inject(ReportDownloadService) as jasmine.SpyObj<ReportDownloadService>;
//       fixture.detectChanges();
//     });

//     it('should create', () => {
//       expect(component).toBeTruthy();
//     });

//     it('should initialize fund list on ngOnInit', () => {
//       const mockFunds = [{ fundID: 1, fundName: 'Fund 1' }];
//       reportDownloadService.getFundList.and.returnValue(of(mockFunds));

//       component.ngOnInit();

//       expect(reportDownloadService.getFundList).toHaveBeenCalled();
//       expect(component.fundList).toEqual(mockFunds);
//       expect(component.isLoader).toBeFalse();
//     });

//     it('should handle fund selection', () => {
//       spyOn(component, 'getCompanyList');
//       component.fundList = [{ fundID: 1, fundName: 'Fund 1' }];
//       component.selectedFundList = [{ fundID: 1, fundName: 'Fund 1' }];

//       component.getFundSelected();

//       expect(component.getCompanyList).toHaveBeenCalled();
//       expect(component.isCheckedCopyFundAll).toBeTrue();
//     });

//     it('should clear selected and grouped company lists', () => {
//       component.selectedCompanyList = [{ companyId: 1, companyName: 'Company 1' }];
//       component.groupedCompanyList = [{ fundName: 'Fund 1', items: [] }];

//       component.onClear();

//       expect(component.selectedCompanyList).toEqual([]);
//       expect(component.groupedCompanyList).toEqual([]);
//     });

//     it('should toggle panel expansion state', () => {
//       const panel = { isExpanded: false };

//       component.expandPanel(panel);

//       expect(panel.isExpanded).toBeTrue();
//     });

//     it('should set company state and deactivate all items', () => {
//       const company = { isActive: false };
//       const items = [{ isActive: true }, { isActive: true }];

//       component.setCompanyState(company);

//       expect(company.isActive).toBeTrue();
//       items.forEach(item => expect(item.isActive).toBeFalse());
//     });

//     it('should map tags correctly', () => {
//       const tags = ['tag1', 'tag2'];

//       const result = component.tagMapper(tags);

//       expect(result).toEqual([tags]);
//     });

//     it('should clear search filter', () => {
//       const event = {
//         clearFilter: jasmine.createSpy('clearFilter'),
//         filterChange: { emit: jasmine.createSpy('emit') }
//       };

//       component.clearSearch(event);

//       expect(event.clearFilter).toHaveBeenCalled();
//       expect(event.filterChange.emit).toHaveBeenCalledWith('');
//     });

//     it('should handle form submission', () => {
//       component.selectedCompanyList = [
//         { fundName: 'Fund 1', fundId: 1, companyId: 1, companyName: 'Company 1' },
//         { fundName: 'Fund 1', fundId: 1, companyId: 2, companyName: 'Company 2' }
//       ];

//       component.onSubmit();

//       expect(component.panelList.length).toBe(1);
//       expect(component.panelList[0].fundName).toBe('Fund 1');
//       expect(component.isLoader).toBeFalse();
//     });

//     it('should clear fields and reset state', () => {
//       component.selectedFundList = [{ fundID: 1, fundName: 'Fund 1' }];
//       component.selectedCompanyList = [{ companyId: 1, companyName: 'Company 1' }];
//       component.isCheckedCopyFundAll = true;
//       component.isCheckedCompanyAll = true;
//       component.panelList = [{ fundName: 'Fund 1', items: [] }];
//       component.groupedCompanyList = [{ fundName: 'Fund 1', items: [] }];

//       component.clearFields();

//       expect(component.selectedFundList).toEqual([]);
//       expect(component.selectedCompanyList).toEqual([]);
//       expect(component.isCheckedCopyFundAll).toBeFalse();
//       expect(component.isCheckedCompanyAll).toBeFalse();
//       expect(component.panelList).toEqual([]);
//       expect(component.groupedCompanyList).toEqual([]);
//       expect(component.isNoData).toBeTrue();
//     });

//     it('should fetch fund list and handle response', () => {
//       const mockFunds = [{ fundID: 1, fundName: 'Fund 1' }];
//       reportDownloadService.getFundList.and.returnValue(of(mockFunds));

//       component.getFundList();

//       expect(reportDownloadService.getFundList).toHaveBeenCalled();
//       expect(component.fundList).toEqual(mockFunds);
//       expect(component.isLoader).toBeFalse();
//     });

//     it('should determine if fund list is indeterminate', () => {
//       component.fundList = [{ fundID: 1, fundName: 'Fund 1' }];
//       component.selectedFundList = [{ fundID: 1, fundName: 'Fund 1' }];

//       expect(component.isFundIndet).toBeFalse();

//       component.selectedFundList = [];

//       expect(component.isFundIndet).toBeFalse();

//       component.selectedFundList = [{ fundID: 1, fundName: 'Fund 1' }];
//       component.fundList = [{ fundID: 1, fundName: 'Fund 1' }, { fundID: 2, fundName: 'Fund 2' }];

//       expect(component.isFundIndet).toBeTrue();
//     });

//     it('should handle fund click and toggle selection state', () => {
//       spyOn(component, 'getCompanyList');
//       component.fundList = [{ fundID: 1, fundName: 'Fund 1' }];

//       component.onFundClick();

//       expect(component.selectedFundList).toEqual(component.fundList);
//       expect(component.isCheckedCopyFundAll).toBeTrue();
//       expect(component.getCompanyList).toHaveBeenCalled();

//       component.onFundClick();

//       expect(component.selectedFundList).toEqual([]);
//       expect(component.isCheckedCopyFundAll).toBeFalse();
//     });

//     it('should group company list by fund name', () => {
//       component.companyList = [
//         { fundName: 'Fund 1', companyId: 1, companyName: 'Company 1' },
//         { fundName: 'Fund 1', companyId: 2, companyName: 'Company 2' }
//       ];

//       component.groupByCompany();

//       expect(component.groupedCompanyList.length).toBe(1);
//       expect(component.groupedCompanyList[0].field).toBe('fundName');
//     });

//     it('should fetch company list and handle response', () => {
//       const mockCompanies = [
//         { fundID: 1, fundName: 'Fund 1', companyId: 1, companyName: 'Company 1' },
//         { fundID: 1, fundName: 'Fund 1', companyId: 2, companyName: 'Company 2' }
//       ];
//       reportDownloadService.GetCompanies.and.returnValue(of(mockCompanies));
//       component.selectedFundList = [{ fundID: 1, fundName: 'Fund 1' }];

//       component.getCompanyList();

//       expect(reportDownloadService.GetCompanies).toHaveBeenCalled();
//       expect(component.companyList).toEqual(mockCompanies);
//       expect(component.groupedCompanyList.length).toBe(1);
//       expect(component.isLoader).toBeFalse();
//     });

//     it('should configure menu close behavior', () => {
//       const oldEmitter = new EventEmitter();
//       const newEmitter = component.configureMenuClose(oldEmitter);

//       expect(newEmitter).toBeDefined();
//     });

//     it('should filter grid based on event', () => {
//       const event = { filter: 'test' };

//       component.filterGrid(event);

//       // Add your assertions here based on the expected behavior of filterGrid
//     });
//   });
// });
