<div class="repo-container">
    <div class="list-group" [class.disabled]="resetInProgress">
        <div class="selection-container" *ngIf="showSelectionContainer">
            <div class="row m-0 p-0 bottom-border">                
                <!-- Portfolio Company Section (handles both single and dual mode) -->
                <ng-container *ngIf="showPCSection">
                    <div [ngClass]="{
                                    'col-6': showFundSection,
                                    'col-12 d-flex justify-content-center': !showFundSection,
                                    'p-0 cursor-pointer': true
                                    }" (click)="getFundsAndPcs()">
                        <div class="d-flex align-items-center pc-section-content mt-2 mb-2 border-gray" [ngClass]="{
                                        'w-100': !showFundSection,
                                        'selected-container': !showFunds,
                                        'deselected-container': showFunds
                                    }">
                            <img src="assets/dist/images/portfolio-company-icon.svg" alt="Portfolio Company" class="ml-12 mt-2 mb-2"
                                *ngIf="showFunds" />
                            <img src="assets/dist/images/primary-pc-icon.svg" alt="Portfolio Company" class="ml-12 mt-2 mb-2"
                                *ngIf="!showFunds" />
                            <span class="Body-R ml-2 truncate-ellipsis" [title]="pcDisplayName">{{ pcDisplayName }}</span>
                        </div>
                    </div>
                </ng-container>
                
                <!-- Fund Section (handles both single and dual mode) -->
                <ng-container *ngIf="showFundSection">
                    <div [ngClass]="{
                                    'col-6': showPCSection,
                                    'col-12 d-flex justify-content-center': !showPCSection,
                                    'p-0 cursor-pointer': true
                                    }" (click)="getFundListData()">
                        <div class="d-flex align-items-center fund-section-content mt-2 mb-2 border-gray" [ngClass]="{
                                        'w-100': !showPCSection,
                                        'selected-container': showFunds,
                                        'deselected-container': !showFunds
                                    }">
                            <img src="assets/dist/images/primary-fund-icon.svg" alt="Funds" class="ml-12 mt-2 mb-2" *ngIf="showFunds" />
                            <img src="assets/dist/images/light-fund-icon.svg" alt="Funds" class="ml-12 mt-2 mb-2" *ngIf="!showFunds" />
                            <span class="Body-R ml-2 truncate-ellipsis" [title]="fundDisplayName">{{ fundDisplayName }}</span>
                        </div>
                    </div>
                </ng-container>
            </div>
            <div class="bottom-border">
                <div class="d-flex align-items-center search-box-container">
                    <input class="form-control border-0 flex-grow-1 Body-R pl-3" type="text"
                        placeholder="Search here..." [(ngModel)]="searchTerm"
                        (input)="debounce(onInputChange, 500)($event)" />
                    <img src="assets/dist/images/search-icon.svg" alt="Search" class="ml-3 mr-3" />
                </div>
            </div>
        </div>
        <div class="select-all-container list-group-item d-flex align-items-center pl-3 pr-3 all-company"
            *ngIf="!showSelectionContainer">
            <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkbx-border" [(ngModel)]="selectAll"
                (ngModelChange)="toggleSelectAll()" disabled />
            <input class="list-group-item all-company flex-grow-1 border-0 p-0 mb-0 ml-4 Body-R" type="text"
                [(ngModel)]="searchTerm" (input)="debounce(onInputChange, 500)($event)"
                value="All Companies ({{ companies.length }})" />
            <img src="assets/dist/images/FiSearch.svg" alt="Search" class="company-logo ml-2">
        </div>
        <div class="companies-list-container" *ngIf="!showFunds && !isLoader">
            <!-- Select All for Companies -->
            <div class="list-group-item d-flex align-items-center pl-3 pr-3 repo-confis">
                <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkbx-border"
                    [(ngModel)]="selectAllCompanies" (change)="toggleSelectAllCompanies()"
                    [indeterminate]="isCompaniesIndeterminate" />
                <div class="ml-3 Body-R pr-3 font-weight-bold">Select All</div>
            </div>
            <div *ngFor="let company of (searchTerm ? searchedCompanies : companies); let i = index"
                [ngClass]="{'list-group-item d-flex align-items-center pl-3 pr-3 repo-confis': true, 'selected-portfolio': company.selected}">
                <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkbx-border"
                    [(ngModel)]="company.selected" (ngModelChange)="checkSelection()" />
                <div class="ml-3 TextTruncate Body-R pr-3" title="{{ company.name }}">{{ company.name }}</div>
            </div>
        </div>

        
        <div class="fund-list-container" *ngIf="showFunds && !isLoader">
            <!-- Select All for Funds -->
            <div class="list-group-item d-flex align-items-center pl-3 pr-3 repo-confis">
                <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkbx-border"
                    [(ngModel)]="selectAllFunds" (change)="toggleSelectAllFunds()"
                    [indeterminate]="isFundsIndeterminate" />
                <div class="ml-3 Body-R pr-3 font-weight-bold">Select All</div>
            </div>
            <!-- Funds List Section (all funds, no selected section) -->
            <div *ngFor="let fund of (searchTerm ? searchedFunds : funds)"
                [ngClass]="{'list-group-item d-flex align-items-center pl-3 pr-3 repo-confis': true, 'selected-portfolio': fund.selected}">
                <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkbx-border"
                    [(ngModel)]="fund.selected" (ngModelChange)="fundCheckSelection()" />
                <div class="ml-3 TextTruncate Body-R pr-3" title="{{ fund.fundName }}">{{ fund.fundName }}</div>
            </div>
        </div>
    </div>
    <app-loader-component *ngIf="isLoader"></app-loader-component>
</div>