.btn-save-clo {
  height: 32px;
}
.btn-save-clo:disabled {
  opacity: 0.5;
}
.clo-summary {
  height: 54px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e6e6e6;
  padding: 10px;
}
.clo-summary-container {
  height: 540px;
  border: 1px solid #e6e6e6;
  margin: 20px;
  border-radius: 4px;
}
.clo-btn-container {
  padding: 10px;
  justify-content: space-between;
  align-items: baseline;
  flex-direction: row;
  display: flex;
}
.row-container {
  width: 300px;
}
.disabled-btn {
  border-radius: 4px;
  color: #93b0ed !important;
  border-color: #93b0ed !important;
  background-color: transparent !important;
}
.btn-warning {
  background: #ffffff 0% 0% no-repeat padding-box !important;
  border: 1px solid #4061c7;
  border-radius: 4px;
  opacity: 1;
  color: var(--Color-Primary-Primary---78, #4061c7);
}
.disabled-save-btn {
  background: var(--Color-Primary-Primary---60, #93b0ed) !important;
  color: white !important;
}
.row-height {
  height: 32px !important;
  opacity: 1 !important;
}
.input-container {
  padding: 10px;
  border: 1px solid #cccccc;
  border-radius: 4px;
}
.input-dropdown {
  display: flex;
  border: 1px solid #cccccc;
  border-radius: 4px;
}
.row-container {
  margin-right: 0px;
  margin-left: 0px;
}
.save-enabled {
  background-color: #4061c7;
}
.form-row {
  margin-right: 10px !important;
  margin-left: 24px !important;
}
.k-input {
  opacity: 1 !important;
}
kendo-textbox .k-input,
kendo-dropdownlist .k-input {
  opacity: 1 !important;
}
.clo-container {
  margin-top: 0.625rem;
  margin-left: 1.875rem;
  margin-right: 1.875rem;
  height: 528px;
  width: 96%;
}
.required {
  color: #DE3139;
}
.heading-clo {
  font-size: 20px;
}
.font-colateral {
  font-weight: 600;
}
.font-clo {
  font-weight: 400;
}

.k-list-item:hover {
  background-color: blue !important;
  color: white !important;
}
.card-container{
  margin-top: 0;
}
.header-section{
  height: 42px; 
  padding: 10px;
}
.clo-section{
  height: 380px
}
.clo-card .card-body>form {
 padding:0;
}
.domicile-combobox{
  outline: 1px solid #CCCCCC;
}

