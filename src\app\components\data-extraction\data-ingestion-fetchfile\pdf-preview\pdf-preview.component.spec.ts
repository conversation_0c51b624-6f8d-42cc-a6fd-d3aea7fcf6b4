import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

import { PdfPreviewComponent } from './pdf-preview.component';
import { PdfSourceModel } from './pdf.model';

describe('PdfPreviewComponent', () => {
  let component: PdfPreviewComponent;
  let fixture: ComponentFixture<PdfPreviewComponent>;
  let mockDomSanitizer: jasmine.SpyObj<DomSanitizer>;

  beforeEach(() => {
    mockDomSanitizer = jasmine.createSpyObj('DomSanitizer', ['bypassSecurityTrustResourceUrl']);
    const mockSafeUrl = { changingThisBreaksApplicationSecurity: 'trusted-url' } as SafeResourceUrl;
    mockDomSanitizer.bypassSecurityTrustResourceUrl.and.returnValue(mockSafeUrl);

    TestBed.configureTestingModule({
      declarations: [PdfPreviewComponent],
      providers: [
        { provide: DomSanitizer, useValue: mockDomSanitizer }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    });
    fixture = TestBed.createComponent(PdfPreviewComponent);
    component = fixture.componentInstance;

    // Set up required inputs before detectChanges
    const mockPdfSource: PdfSourceModel = {
      fileName: 'test.pdf',
      fileUrl: 'test-url',
      fileType: 'pdf',
      processId: 'test-process-id',
      pageList: []
    };
    component.pdfSource = mockPdfSource;

    // Don't call detectChanges to avoid lifecycle issues in test
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
