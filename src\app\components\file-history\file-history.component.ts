import { Component, OnInit, On<PERSON><PERSON>roy, ViewEncapsulation, ElementRef, Input } from '@angular/core';
import { Subscription } from 'rxjs';
import { FormBuilder, FormGroup } from '@angular/forms';
import { FileHistoryService } from '../../services/file-history.service';

@Component({
  selector: 'app-file-history',
  templateUrl: './file-history.component.html',
  styleUrls: ['./file-history.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class FileHistoryComponent implements OnInit, OnDestroy {
  @Input() anchor: ElementRef;
  
  show = false;
  fileGroups = [];
  viewingAll = false;
  showFilter = false;
  
  // Filter options
  statusOptions = [
    { text: 'All Files', value: 'all' },
    { text: 'Uploaded', value: 'uploaded' },
    { text: 'In Progress', value: 'progress' },
    { text: 'Failed', value: 'failed' }
  ];
  
  moduleTypeOptions = [
    { text: 'ESG', value: 'ESG' },
    { text: 'Financials', value: 'Financials' },
    { text: 'Credit KPI', value: 'CreditKPI' },
    { text: 'Operational KPI', value: 'OperationalKPI' },
    { text: 'Investment KPI', value: 'InvestmentKPI' },
    { text: 'Monthly Report', value: 'MonthlyReport' }
  ];
  
  filterForm: FormGroup;
  private subscriptions: Subscription[] = [];

  constructor(
    private fileHistoryService: FileHistoryService,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      status: ['all'],
      moduleTypes: [[]],
      startDate: [null],
      endDate: [null]
    });
  }  ngOnInit(): void {
    // Subscribe to popup visibility changes
    this.subscriptions.push(
      this.fileHistoryService.showPopup$.subscribe(show => {
        this.show = show;
        if (show) {
          this.loadFileHistory();
        }
      })
    );
    
    // Subscribe to anchor element changes for positioning
    this.subscriptions.push(
      this.fileHistoryService.anchorElement$.subscribe(element => {
        if (element) {
          this.anchor = element;
          console.log('Anchor element set', this.anchor);
        }
      })
    );
    
    // Initialize with mock data
    this.createMockData();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Close the file history popup
   */
  closePopup(): void {
    this.fileHistoryService.hideFileHistoryPopup();
  }

  /**
   * Toggle view all files
   */
  viewAll(): void {
    this.viewingAll = !this.viewingAll;
    this.loadFileHistory();
  }

  /**
   * Toggle filter visibility
   */
  toggleFilter(): void {
    this.showFilter = !this.showFilter;
  }

  /**
   * Toggle expand/collapse of a file group
   */
  toggleGroup(group: any): void {
    group.expanded = !group.expanded;
  }

  /**
   * Apply the selected filters
   */
  applyFilter(): void {
    const filterValues = this.filterForm.value;
    console.log('Applying filters:', filterValues);
    this.loadFileHistory();
  }

  /**
   * Clear all filters and reset to defaults
   */
  clearFilter(): void {
    this.filterForm.reset({
      status: 'all',
      moduleTypes: [],
      startDate: null,
      endDate: null
    });
    this.loadFileHistory();
  }
  
  /**
   * Get appropriate icon based on file extension
   */
  getFileIcon(fileName: string): string {
    if (!fileName) return 'file';
    
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    
    return extension;
  }

  /**
   * Calculate time difference for display
   */
  getTimeDifference(date: Date): string {
    if (!date) return '';
    
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
      return `${days} days ago`;
    } else if (hours > 0) {
      return `${hours} hours ago`;
    } else if (minutes > 0) {
      return `${minutes} minutes ago`;
    } else {
      return 'Just now';
    }
  }

  /**
   * Load file history data
   */
  private loadFileHistory(): void {
    // In a real app, this would call an API with the filter values
    console.log('Loading file history with filters:', this.filterForm.value);
    console.log('Viewing all:', this.viewingAll);
    
    // For now, just using the mock data created in constructor
    // In a real implementation, you would make API call here
  }

  /**
   * Refresh file status
   */
  refreshFile(item: any): void {
    console.log('Refreshing file:', item);
    // In a real app, this would check the latest status from the server
    // For demo, let's just update the progress if it's in-progress
    if (item.status === 'in-progress') {
      item.progress = Math.min(item.progress + 10, 99);
    }
  }

  /**
   * Retry failed file upload
   */
  retryFile(item: any): void {
    console.log('Retrying file:', item);
    // In a real app, this would retry the file upload
    item.status = 'in-progress';
    item.progress = 0;
    
    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      item.progress = progress;
      
      if (progress >= 100) {
        clearInterval(interval);
        item.status = 'success';
        item.message = 'Now you can see the information on table';
      }
    }, 1000);
  }

  /**
   * View file details
   */
  viewFile(item: any): void {
    console.log('Viewing file:', item);
    // In a real app, this would open the file for viewing
    // For demo, just log the action
  }
  
  /**
   * Create mock data for the file history component
   */
  private createMockData(): void {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - (60 * 60 * 1000));
    
    this.fileGroups = [
      {
        organization: 'Fidelity Investment',
        files: 15,
        expanded: true,
        items: [
          {
            fileName: 'Quarterly_Report.pdf',
            fileSize: '291.08',
            status: 'in-progress',
            uploadDate: oneHourAgo,
            progress: 80,
            message: 'Please wait, document is getting upload'
          },
          {
            fileName: 'Financial_Summary.xlsx',
            fileSize: '156.23',
            status: 'in-progress',
            uploadDate: oneHourAgo,
            progress: 29,
            message: 'Please wait, document is getting upload'
          },
          {
            fileName: 'Investment_Strategy.xlsx',
            fileSize: '125.47',
            status: 'failed',
            uploadDate: oneHourAgo,
            message: 'Due to network issues, file upload has failed'
          }
        ]
      },
      {
        organization: 'Advera',
        files: 2,
        expanded: true,
        items: [
          {
            fileName: 'Financial_Report.xlsx',
            fileSize: '345.92',
            status: 'success',
            uploadDate: new Date(now.getTime() - (3 * 24 * 60 * 60 * 1000)),
            message: 'Now you can see the information on table'
          },
          {
            fileName: 'Contract.xlsx',
            fileSize: '128.05',
            status: 'success',
            uploadDate: new Date(now.getTime() - (24 * 60 * 60 * 1000)),
            message: 'Now you can see the information on table'
          }
        ]
      }
    ];
  }
}
