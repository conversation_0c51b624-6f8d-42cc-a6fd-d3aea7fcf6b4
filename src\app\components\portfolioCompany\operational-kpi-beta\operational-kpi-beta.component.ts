import { AfterViewInit, Component, EventEmitter, Input, OnInit, ViewChild } from '@angular/core';
import { PortfolioCompanyService } from "../../../services/portfolioCompany.service";
import { ActivatedRoute, Router } from "@angular/router";
import { Observable, Subject, Subscription } from 'rxjs';
import { NgbModalOptions } from "@ng-bootstrap/ng-bootstrap";
import { Table } from 'primeng/table';
import { ToastContainerDirective, ToastrService } from "ngx-toastr";
import { MatMenu, MatMenuTrigger } from '@angular/material/menu';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { NumberDecimalConst, FinancialsSubTabs, KpiTypes, PeriodTypeFilterOptions, PeriodType, KpiTypesConstants, DataAnalyticsConstants, GlobalConstants,OperationalKPIConstants, CommonPCConstants, CellEditConstants } from "src/app/common/constants";
import { isNil } from 'src/app/utils/utils';
import { isNumeric } from "@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric";
import { ActionsEnum, FeaturesEnum, UserSubFeaturesEnum, PermissionService, KPIModulesEnum  } from "src/app/services/permission.service";
import { extractDateComponents } from "../../file-uploads/kpi-cell-edit/cell-edit-utils";
import { OidcAuthService } from "src/app/services/oidc-auth.service";

import {
  DecimalDigitEnum,
  FinancialValueUnitsEnum,
  MiscellaneousService,
  OrderTypesEnum,
  PeriodTypeQuarterEnum,
  ErrorMessage
} from "src/app/services/miscellaneous.service";
import { filter } from 'rxjs/operators';
import { AuditService } from "src/app/services/audit.service";
import { Audit, MappedDocuments, TableHeader } from '../../file-uploads/kpi-cell-edit/kpiValueModel';
import { DatePipe } from '@angular/common';
import { getConversionErrorMessage } from 'src/app/utils/utils';
@Component({
    selector: 'app-operational-kpi-beta',
    templateUrl: './operational-kpi-beta.component.html',
    styleUrls: ['./operational-kpi-beta.component.scss']
})
export class OperationalKpiBetaComponent implements OnInit, AfterViewInit {
  NumberDecimalConst = NumberDecimalConst;
  feature: typeof FeaturesEnum = FeaturesEnum;
  subFeature: typeof UserSubFeaturesEnum = UserSubFeaturesEnum;
  actions: typeof ActionsEnum = ActionsEnum;
    modelOperationalKpi: any = {};
    @Input() searchFilter: any = null;
    private eventsSubscription: Subscription;
    @Input() events: Observable<void>;
    @Input() modelList: any;
    dataTable: any;
    message: any;
    id: any;
    msgTimeSpan: number;
    loading = false;
    operationalKpiValueUnit: any;
    modalOption: NgbModalOptions = {};
    currentModelRef: any;
    globalFilter: string = "";
    tableReload = false;
    isLoader: boolean = false;
    portfolioInfoSectionModel: any = {};
    frozenCols: any = [{ field: "KPI", header: "KPI" }];
    operationalKpiSearchFilter: any;
    financialPeriodErrorMessage: string = "";
    unitOfCurrency: string = FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions];
    @ViewChild('dt') dt: Table | undefined;
    updateModel: any = {};
    confirmUpdate = false;
    @ViewChild(ToastContainerDirective, {})
    toastContainer: ToastContainerDirective;
    infoUpdate: boolean = false;
    ErrorNotation: boolean = false;
    isToasterMessage = false;
    ModuleName: string = "";
    @ViewChild('menu') uiuxMenu!: MatMenu;
    @ViewChild('menuTrigger') menuTrigger: MatMenuTrigger;
    ModuleCurrency: string;
    @Input() kpiName: string;
    exportOperationalKPILoading: boolean = false;
    isValueUpdated: boolean = false;
    tabValueTypeList: ITab[] = [];
    IsPageLoad: boolean = true;
    tabName: string = "";
    isMonthly: boolean = true;
    isQuarterly: boolean = false;
    isAnnually: boolean = false;
    filterOptions : any[] = [];
    tableColumns = [];
    tableFrozenColumns = [];
    tableResult = [];
    tableResultClone = [];
    kpiFilterCols: any = [];
    auditLogList: any = [];
    isToggleChecked:boolean=false;
    @Input() pageConfigData =[{kpiConfigurationData:[],hasChart:false,kpiType:""}];
    @Input() operationalKPIPermissions: any = [];
    subSectionFields=[];
    pageConfigResponse={kpiConfigurationData:[],hasChart:false,kpiType:""};
    defaultType:string="Monthly";
    CELL_LOGS_MESSAGE = OperationalKPIConstants.CELL_LOGS_MESSAGE;
    isBristol:boolean = false;
    isUploadPopupVisible: boolean = false;
    uniqueModuleCompany: any;
    dataRow: object;
    dataColumns: TableHeader;
    kpiCurrencyFilterModel: any = {};
    isValueConverted: boolean = false;
    auditLogErrorForConvertedValue: string = GlobalConstants.AuditLogErrorForConvertedValue;
    editErrorForConvertedValue: string = GlobalConstants.EditgErrorForConvertedValue;
    editSpotRateConversionError: string = GlobalConstants.EditSpotRateConversionError;
    auditLogSpotRateConversionError: string = GlobalConstants.AuditLogSpotRateConversionError;
    auditLogTitle: string = GlobalConstants.AuditLogTitle;

    constructor(
        private _avRoute: ActivatedRoute,
        private portfolioCompanyService: PortfolioCompanyService,
        private miscService: MiscellaneousService,
        private toastrService: ToastrService,
        private permissionService: PermissionService,
        private auditService: AuditService,
        private router: Router,
        private identityService: OidcAuthService,
        private datePipe: DatePipe
    ) {
        if (this._avRoute.snapshot.params["id"]) {
            this.id = this._avRoute.snapshot.params["id"];
        }
        this.modelOperationalKpi.periodType = {
          type: PeriodTypeQuarterEnum.Last1Year,
        };
        this.modelOperationalKpi.orderType = { type: OrderTypesEnum.LatestOnRight };
        this.modelOperationalKpi.decimalPlaces = {
          type: DecimalDigitEnum.Zero,
          value: "1.0-0",
        };
    
        this.operationalKpiValueUnit = {
          typeId: FinancialValueUnitsEnum.Millions,
          unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
        };
        if (window.location.host == CommonPCConstants.BristolHost) {
          this.isBristol = true;
        }
    }
    /**
     * Lifecycle hook that is called after Angular has fully initialized the component's view.
     * It is called only once after the first ngAfterContentChecked.
     */
    ngAfterViewInit() { 
      if (this.uiuxMenu != undefined) {
        (this.uiuxMenu as any).closed = this.uiuxMenu.closed
        this.configureMenuClose(this.uiuxMenu.closed);
      }
    }
    /**
     * Configures the close event for the menu.
     * @param old The previous close event handler.
     * @returns An EventEmitter that emits the updated close event.
     */
    configureMenuClose(old: MatMenu['closed']): MatMenu['closed'] {
      const upd = new EventEmitter();
      feed(upd.pipe(
        filter(event => {
          if (event === 'click') {
            return false;
          }
          return true;
        }),
      ), old);
      return upd;
    }
    /**
     * Initializes the component and sets up the initial state.
     */
    ngOnInit() { 
      this.pageConfigResponse = this.pageConfigData.find(x =>x.kpiType==KpiTypes.OperationalBeta.type);
      this.subSectionFields = this.pageConfigResponse?.kpiConfigurationData;
      this.getValueTypeTabList();
      if(this.filterOptions.length > 0)
        this.filterOptions[0].key=true;
      this.eventsSubscription = this.events?.subscribe((res) => {
        this.searchFilter = res;
        this.getPortfolioCompanyOperationalKPIValues(null);
      });
      this.msgTimeSpan = this.miscService.getMessageTimeSpan();
    }

    /**
     * Selects a tab and updates the active state and tab name.
     * Also sets the periods options and retrieves the operational KPI values for the selected tab.
     * @param tab - The tab to be selected.
     */
    selectValueTab(tab: ITab) {
      this.tabValueTypeList.forEach((tab) => (tab.active = false));
      tab.active = true;
      this.tabName = tab.name;
      this.setPeriodsOptions(this.subSectionFields);
      this.getPortfolioCompanyOperationalKPIValues(null);
    }

    /**
     * Returns the name of the current tab based on the value of `tabName`.
     * @returns The name of the current tab.
     */
    getTabName(){
      switch(this.tabName){
        case FinancialsSubTabs.Actual:
          return "actual";
        case FinancialsSubTabs.Budget:
          return "budget";
        case FinancialsSubTabs.Forecast:
          return "forecast";
        case FinancialsSubTabs.IC:
          return "ic";
      }
    }

    /**
     * Displays a success toast message indicating that the entry has been updated successfully.
     */
    successToaster() {
      this.toastrService.success(OperationalKPIConstants.EntryUpdatedSuccessfully, "", { positionClass: OperationalKPIConstants.ToastCenterCenter });
    }

    /**
     * Checks if the given value is a number.
     * @param str The value to be checked.
     * @returns True if the value is a number, false otherwise.
     */
    isNumberCheck(str: any) {
      return isNumeric(str);
    }

    /**
     * Retrieves the operational KPI values for the portfolio company.
     * @param event - The event object.
     */
    getPortfolioCompanyOperationalKPIValues(event: any) {
        this.isLoader = true;
        if (event == null) {
          event = GlobalConstants.KpiFillter;
        }
        let searchFilter = this.searchFilter;
        if (searchFilter == null) {
          let sortOrder =
            this.modelOperationalKpi.orderType.type == OrderTypesEnum.LatestOnRight
              ? [
                { field: "year", order: 1 },
                { field: "quarter", order: 1 },
              ]
              : [
                { field: "year", order: -1 },
                { field: "quarter", order: -1 },
              ];
          searchFilter = {
            sortOrder: sortOrder,
            periodType: this.modelOperationalKpi.periodType.type,
          };
    
          if (searchFilter.periodType == OperationalKPIConstants.DateRange) {
            searchFilter.startPeriod = new Date(
              Date.UTC(
                this.modelOperationalKpi.startPeriod.getFullYear(),
                this.modelOperationalKpi.startPeriod.getMonth(),
                this.modelOperationalKpi.startPeriod.getDate()
              )
            );
            searchFilter.endPeriod = new Date(
              Date.UTC(
                this.modelOperationalKpi.endPeriod.getFullYear(),
                this.modelOperationalKpi.endPeriod.getMonth(),
                this.modelOperationalKpi.endPeriod.getDate()
              )
            );
          }
        } else {
          if (searchFilter.periodType == OperationalKPIConstants.DateRange) {
            searchFilter.startPeriod = new Date(
              Date.UTC(
                searchFilter.startPeriod.getFullYear(),
                searchFilter.startPeriod.getMonth(),
                searchFilter.startPeriod.getDate()
              )
            );
            searchFilter.endPeriod = new Date(
              Date.UTC(
                searchFilter.endPeriod.getFullYear(),
                searchFilter.endPeriod.getMonth(),
                searchFilter.endPeriod.getDate()
              )
            );
          }
        }
       this.getBetaOperationalKPIValues(searchFilter,event)
      }

      /**
       * Retrieves the beta operational KPI values based on the provided search filter and event.
       * 
       * @param searchFilter - The search filter to apply.
       * @param event - The event object.
       */
      getBetaOperationalKPIValues(searchFilter:any,event:any){
        this.operationalKpiSearchFilter = searchFilter;
        this.portfolioCompanyService.getOperationalKpiData({
            portfolioCompanyID: this.modelList?.portfolioCompanyID,
            paginationFilter: event,
            searchFilter: searchFilter,
            valueType: this.tabValueTypeList.length == 0 ? "Actual" : this.tabName,
            isMonthly: this.isMonthly,
            isQuarterly: this.isQuarterly,
            isAnnually: this.isAnnually,
            isPageLoad: this.IsPageLoad,
            moduleID: this.modelList?.moduleId,
            companyId: this.modelList?.portfolioCompanyID?.toString(),
            kpiConfigurationData:this.pageConfigResponse.kpiConfigurationData,
            IsSpotRate : this.kpiCurrencyFilterModel.isSpotRate == null ? false : this.kpiCurrencyFilterModel.isSpotRate,
            SpotRateDate:this.kpiCurrencyFilterModel.isSpotRate ? this.datePipe.transform(this.kpiCurrencyFilterModel.spotRateDate, 'yyyy-MM-dd') : null,
            currencyCode: this.kpiCurrencyFilterModel?.currencyCode,
            reportingCurrencyCode: this.modelList?.reportingCurrencyDetail?.currencyCode,
            currencyRateSource: this.kpiCurrencyFilterModel?.currencyRateSource,
          })
          .subscribe({
            next: (result) => {
              if (result != null) {
                this.loading = false;
                this.ErrorNotation = false;
                this.isLoader = false;
                this.tableReload = true;
                this.tableColumns = result?.headers || [];
                this.tableFrozenColumns = this.frozenCols;
                this.tableResult = result?.rows || [];
                this.auditLogList = result?.companyKpiAuditLog || [];
                this.tableResultClone = result?.rows || [];
                this.convertUnits();
                this.kpiFilterCols = [...this.tableFrozenColumns, ...this.tableColumns];
                this.IsPageLoad = false;
                if(result != null){
                  this.isMonthly = result?.isMonthly; 
                  this.isQuarterly = result?.isQuarterly;
                  this.isAnnually  = result?.isAnnually;
                  this.SetFilterOptionsKeys(result);
                }
              } else {
                this.clearData();
              }
            },
            error: (error) => {
              this.clearData();
            }
          });
      }

      /**
       * Applies a global filter to the KPI table.
       * 
       * @param event The event object containing the filter information.
       */
      kpiTable_GlobalFilter(event) {
        this.operationalKpiValueUnit = event?.UnitType == undefined ? {
          typeId: FinancialValueUnitsEnum.Millions,
          unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
        } : event?.UnitType;
        this.searchFilter = event;
        this.kpiCurrencyFilterModel = event;
        if(this.modelList?.reportingCurrencyDetail?.currencyCode != null && event?.currencyCode != null && this.modelList?.reportingCurrencyDetail?.currencyCode != event?.currencyCode){
          this.isValueConverted = true;
        }
        else{
          this.isValueConverted = false;
        }
        this.getPortfolioCompanyOperationalKPIValues(null);
        this.menuTrigger?.closeMenu();
      }

      /**
       * Sets the filter options keys based on the provided result object.
       * @param result - The result object containing the filter options values.
       */
      private SetFilterOptionsKeys(result: any) {
        this.filterOptions?.forEach(element => {
          switch (element.field) {
            case PeriodTypeFilterOptions.Monthly:
              element.key = result?.isMonthly;
              break;
            case PeriodTypeFilterOptions.Quarterly:
              element.key = result?.isQuarterly;
              break;
            case PeriodTypeFilterOptions.Annual:
              element.key = result?.isAnnually;
              break;
          }
        });
        this.setDefaultTypeTab();
      }

      /**
       * Retrieves the value type tab list from the portfolio company service and sets the necessary properties.
       */
      getValueTypeTabList() {
        this.portfolioCompanyService.getfinancialsvalueTypes().subscribe((x) => {
          let tabList = x.body?.financialTypesModelList;
          let pageConfigTabs = this.subSectionFields;
          tabList = tabList?.filter((item: any) => pageConfigTabs?.some((otherItem: any) => item.name === otherItem.aliasName && otherItem.chartValue.length > 0));
          tabList = tabList?.filter(x => x.name != "IC");
          if(tabList != undefined && tabList.length > 0){
            this.tabValueTypeList = tabList;
            this.tabValueTypeList[0].active = true;
            this.tabName = this.tabValueTypeList[0].name;
            this.setPeriodsOptions(pageConfigTabs);
          }
        });
      }

      /**
       * Sets the period options for the operational KPI beta component.
       * @param pageConfigTabs The array of page configuration tabs.
       */
      private setPeriodsOptions(pageConfigTabs: any[]) {
        let periodOptions = PeriodType.filterOptions;
        let activeTabData = pageConfigTabs?.find(x => x.aliasName == this.tabName);
        this.filterOptions = periodOptions?.filter(item => activeTabData.chartValue.some(otherItem => otherItem === item.field));
        let periodType = this.filterOptions.find(x => x.key);
        if(periodType == undefined){
          for(const element of periodOptions){
            element.key = false;
          }
          this.filterOptions[0].key = true;
          periodType = this.filterOptions[0];
        }
        this.onChangePeriodOption(periodType);
      }

      /**
       * Handles the change event of the period option.
       * @param {any} type - The selected period option.
       */
      onChangePeriodOption(type) {
        this.filterOptions.forEach((x) => (x.key = false));
        if (type.field == PeriodTypeFilterOptions.Monthly) {
          type.key = this.isMonthly = true;
          this.isQuarterly = false;
          this.isAnnually = false;
          this.SetPeriodFilterOptions(PeriodTypeFilterOptions.Monthly);
        } else if (type.field == PeriodTypeFilterOptions.Quarterly ) {
          this.isMonthly = false;
          type.key = this.isQuarterly = true;
          this.isAnnually = false;
          this.SetPeriodFilterOptions(PeriodTypeFilterOptions.Quarterly);
        } else {
          this.isMonthly = false;
          this.isQuarterly = false;
          type.key = this.isAnnually = true;
          this.SetPeriodFilterOptions(PeriodTypeFilterOptions.Annual);
        }
        this.setDefaultTypeTab();
        this.getPortfolioCompanyOperationalKPIValues(null);
      }

  /**
   * Sets the period filter options based on the selected period type.
   * @param periodType The selected period type.
   */
  private SetPeriodFilterOptions(periodType: string) {
    PeriodType.filterOptions.forEach(x => {
      if (x.field !== periodType)
        x.key = false;
      else
        x.key = true;
    });
  }

      /**
       * Clears the data in the component.
       */
      clearData() {
        this.loading = false;
        this.isLoader = false;
        this.tableColumns = [];
        this.tableResult = [];
        this.tableResultClone = [];
        this.auditLogList = [];
        this.IsPageLoad = false;
      }

      /**
       * Converts the units of the table result based on the selected operational KPI value unit.
       */
      convertUnits() {
        this.tableResult = [];
        let local = this;
        let masterValueUnit = this.operationalKpiValueUnit;
        this.tableResultClone.forEach(function (
          value: any
        ) {
          let valueClone = JSON.parse(JSON.stringify(value));
          if (valueClone[OperationalKPIConstants.KPIInfo] != OperationalKPIConstants.Percentage && valueClone[OperationalKPIConstants.KPIInfo] != OperationalKPIConstants.X && valueClone[OperationalKPIConstants.KPIInfo] != "#" &&
            valueClone[OperationalKPIConstants.KPIInfo] != OperationalKPIConstants.Text && valueClone[OperationalKPIConstants.KPIInfo] != ""
          ) {
            switch (Number(masterValueUnit.typeId)) {
              case FinancialValueUnitsEnum.Absolute:
                break;
              case FinancialValueUnitsEnum.Thousands:
                valueClone = local.conversionValue(valueClone, local, 1000);
                break;
              case FinancialValueUnitsEnum.Millions:
                valueClone = local.conversionValue(valueClone, local, 1000000);
                break;
              case FinancialValueUnitsEnum.Billions:
                valueClone = local.conversionValue(valueClone, local, 1000000000);
                break;
            }
          }
          local.tableResult.push(valueClone)
        });
      }

      /**
       * Converts the values in the given object based on the provided conversion factor.
       * @param valueClone - The object containing the values to be converted.
       * @param local - The local object containing additional information.
       * @param value - The conversion factor.
       * @returns The modified object with converted values.
       */
      conversionValue(valueClone: any, local: any, value: any) {
        local.tableColumns.forEach((col: any, index: any) => {
          if (valueClone[col.field] != 0) {
            valueClone[col.field] =
              !isNil(valueClone[col.field]) ? !isNaN(parseFloat((valueClone[col.field].indexOf(',') > -1 ? valueClone[col.field].replace(/,/g, '') : valueClone[col.field])))
                ? (valueClone[col.field].indexOf(',') > -1 ? valueClone[col.field].replace(/,/g, '') : valueClone[col.field]) / value
                : valueClone[col.field] : valueClone[col.field];
          }
        });
        return valueClone;
      }

      /**
       * Handles the change event of the checkbox.
       * @param {Event} e - The event object.
       * @returns {void}
       */
      handleChange(e) {
        this.ErrorNotation = e;
        this.isToggleChecked = this.ErrorNotation;
      }

      /**
       * Retrieves the filter audit value based on the provided row data and column.
       * @param rowdata The row data object.
       * @param column The column object.
       * @returns The filter audit value.
       */
      getFilterAuditValue(rowdata: any, column: any) {
        let headers = column?.field?.split(' ');
        let auditList = this.auditLogList;
        let periodHeader = null;
        let yearHeader = null;
        let monthValue = null;
        if (headers?.length > 0 && auditList?.length > 0) {
          if (headers.length == 1)
            yearHeader = parseInt(headers[0]);
          else {
            periodHeader = headers[0];
            yearHeader = parseInt(headers[1]);
            if (!periodHeader.toLowerCase().includes("q"))
              monthValue = this.miscService.getMonthNumber(periodHeader);
          }
        }
        return this.filterAuditValue(yearHeader, monthValue, auditList, periodHeader, rowdata);
      }
      /**
       * Filters the audit values based on the specified criteria.
       * @param yearHeader - The year header value.
       * @param monthValue - The month value.
       * @param auditList - The list of audit values.
       * @param periodHeader - The period header value.
       * @param rowdata - The row data.
       * @returns The filtered audit values.
       */
      filterAuditValue(yearHeader: any, monthValue: any, auditList: any, periodHeader: any, rowdata: any) {
        let result = [];
        if (periodHeader == OperationalKPIConstants.Q1 || periodHeader ==  OperationalKPIConstants.Q2 || periodHeader ==  OperationalKPIConstants.Q3 || periodHeader ==  OperationalKPIConstants.Q4)
          result = auditList?.filter(x => x.quarter == periodHeader && x.year == yearHeader && x.kpiId == rowdata.KpiId);
        else if (monthValue != null)
          result = auditList?.filter(x => x.month == monthValue && x.year == yearHeader && x.kpiId == rowdata.KpiId);
        else
          result = auditList?.filter(x => x.month == null && x.year == yearHeader && (x.Quarter == null || x.Quarter == '') && x.kpiId == rowdata.KpiId);
        return result
      }
      /**
       * Initializes the edit mode for a specific row and column in the operational KPI table.
       * @param rowData - The data of the row being edited.
       * @param column - The column being edited.
       */
        onEditInit(rowData: any, column: any) {
          if (!this.canEdit()) {
            this.showErrorToast(ErrorMessage.NoAccess);
            return;
          }
          if (this.ErrorNotation) {
      return;
    }

          if (!this.hasPermissionToEdit(column)) {
            return;
          }

          if (this.isConvertedValue(rowData)) {
            const message = getConversionErrorMessage(
        this.kpiCurrencyFilterModel.isSpotRate,
        this.editSpotRateConversionError,
        this.editErrorForConvertedValue
      );
            this.showErrorToast(message);
            return;
          }

          if (this.shouldUpdateInfo(rowData)) {
            this.infoUpdate = true;
          } else if (this.shouldShowUploadPopup(rowData)) {
            this.showUploadPopup(rowData, column);
          } else {
            this.showErrorToast(this.editErrorForConvertedValue);
          }
        }

        /**
       * Determines if the user has edit permissions.
       *
       * This method checks the `KPIPermissions` array to see if any of the permissions
       * include the ability to edit. It returns `true` if at least one permission allows editing,
       * otherwise it returns `false`.
       *
       * @returns {boolean} `true` if the user can edit, `false` otherwise.
       */
        private canEdit(): boolean {
          const canEdit = this.operationalKPIPermissions?.map(access => access.canEdit);
          return canEdit.includes(true);
        }

        /**
         * Checks if the user has permission to edit the given row data.
         *
         * This method verifies if the user has the necessary permissions to edit
         * the Investment KPIs sub-feature and ensures that the row data is not
         * marked as a formula.
         *
         * @param rowData - The data of the row to check for edit permissions.
         * @returns `true` if the user has permission to edit and the row data is not a formula, otherwise `false`.
         */
        private hasPermissionToEdit(column: any): boolean {
          return this.permissionService.checkUserPermission(
            this.subFeature.OperationalKPIs,
            ActionsEnum[ActionsEnum.canEdit],
            this.id
          ) && column.header !== OperationalKPIConstants.KPI;
        }

        /**
         * Checks if the value in the given row data is converted.
         * 
         * @param rowData - The data of the row to check.
         * @returns `true` if the value is converted and the 'KPI Info' field is '$', otherwise `false`.
         */
        private isConvertedValue(rowData: any): boolean {
          return this.isValueConverted && rowData['KPI Info'] === '$';
        }

        /**
         * Determines whether the information should be updated based on the provided row data.
         *
         * @param rowData - The data of the row to be evaluated.
         * @returns A boolean indicating whether the information should be updated.
         */
        private shouldUpdateInfo(rowData: any): boolean {
          return Number(this.operationalKpiValueUnit?.typeId) !== FinancialValueUnitsEnum.Absolute &&
            !this.ErrorNotation &&
            !rowData.IsHeader;
        }

        /**
         * Determines whether the upload popup should be shown for a given row.
         * 
         * @param rowData - The data of the row to evaluate.
         * @returns `true` if the upload popup should be shown; otherwise, `false`.
         * 
         * The upload popup is shown if the row is not a header, not a formula, 
         * and the MasterFormula property is null.
         */
        private shouldShowUploadPopup(rowData: any): boolean {
          return !rowData.IsHeader && !rowData.IsFormula && rowData.MasterFormula === null;
        }

        /**
         * Displays the upload popup with the provided row data and column information.
         *
         * @param rowData - The data for the selected row.
         * @param column - The column information for the selected row.
         * @returns void
         */
        private showUploadPopup(rowData: any, column: any): void {
          this.uniqueModuleCompany = {
            moduleId: KPIModulesEnum.Operational,
            companyId: this.modelList.portfolioCompanyID,
            valueType: this.tabValueTypeList.length === 0 ? "Actual" : this.tabName
          };
          this.dataRow = rowData;
          this.dataColumns = column;
          this.isUploadPopupVisible = true;
        }


      /**
       * Handles the submit button event.
       * @param results - The results object containing the code and message.
       */
      onSubmitButtonEvent(results: any) {
        if (results.code != null && results.code.trim().toLowerCase() == "ok") {
          this.showSuccessToast(results.message);
          this.isUploadPopupVisible = false;
          this.isValueUpdated = !this.isValueUpdated;
        } else {
          this.showErrorToast(results.message);
          this.isUploadPopupVisible = false;
        }
        this.getPortfolioCompanyOperationalKPIValues(null);
      }
      /**
       * Displays a success toast notification.
       * 
       * @param message - The message to be displayed in the toast.
       * @param title - The title of the toast (optional).
       * @param position - The position of the toast on the screen (optional, default: "toast-center-center").
       */
      showSuccessToast(
        message: string,
        title: string = "",
        position: string = CellEditConstants.ToasterMessagePosition
      ): void {
        this.toastrService.success(message, title, { positionClass: position });
      }

      showErrorToast(message: string, title: string = '', position: string = CellEditConstants.ToasterMessagePosition): void {
        this.toastrService.error(message, title, { positionClass: position });
      }

      /**
       * Handles the cancel button event.
       */
      cancelButtonEvent() {
        this.isUploadPopupVisible = false;
      }

      /**
       * Validates the input value to ensure it is a number with up to 5 decimal places.
       * If the input value is not a valid number, it will be converted to a valid number with 5 decimal places.
       * 
       * @param event - The event object triggered by the input event.
       * @param KpiInfo - Additional information about the KPI.
       */
      validateNumber(event: any,KpiInfo:string) {
        if (event.which != 15) {
          let ex: RegExp = new RegExp(/^-*\d*(?:[.,]\d{1,5})?$/);
          if (ex.test(event.target.value) === false) {
            if (!Number.isInteger(Number(event.target.value) )) {
              event.target.value = parseFloat(event.target.value).toFixed(5);
            }
          }
        }
      }
      /**
       * Validates the maximum length of the input value based on its type.
       * If the value is not an integer, it checks if the length is 21 characters.
       * If the value is an integer, it checks if the length is 16 characters.
       * @param event - The event object containing the input value.
       * @returns A boolean indicating whether the input value is valid or not.
       */
      validateMaxLength(event:any): boolean {
        if (!Number.isInteger(Number(event.target.value))) {
          if (event.target.value.length == 21) return false;
        }else{
          if (event.target.value.length == 16) return false;
        }
        return true;
      }
    
      /**
       * Handles the event when a column is being edited.
       * @param event - The event object containing information about the edit event.
       */
      onColumnEdit(event: any) {
        event.target.blur();
      }
      /**
       * Closes the information update.
       */
      CloseInfo() {
        this.infoUpdate = false;
      }

      /**
       * Redirects to the audit log page with the specified parameters.
       *
       * @param field - The field object.
       * @param attributeName - The attribute name.
       * @param data - The mapped documents data.
       * @param auditLogFilter - The audit log filter.
       */
      redirectToAuditLogPage(field: any, attributeName: any, data: MappedDocuments, auditLogFilter: Audit) {
        let params = {
          KPI: this.tabName,
          header: field.header,
          PortfolioCompanyID: this.modelList.portfolioCompanyID,
          AttributeName: attributeName,
          ModuleId: KPIModulesEnum.Operational,
          Comments: this.tabValueTypeList.length == 0 ? "Actual" : this.tabName,
          currency: '',
          AttributeId: data.valueId,
          isNewAudit: true,
          KpiId: auditLogFilter.kpiId,
          MappingId: auditLogFilter.mappingId,
        };
        sessionStorage.setItem(GlobalConstants.CurrentModule, KpiTypesConstants.OPERATIONAL_KPI);
        sessionStorage.setItem(GlobalConstants.OperationalKpiAuditLocalStorage, JSON.stringify(params));
        let config = this.identityService.getEnvironmentConfig();
        if (config.redirect_uri != "") {
          let myAppUrl = config.redirect_uri.split("/in")[0] + OperationalKPIConstants.AuditLogRoute;
          window.open(myAppUrl, '_blank');
        }
      }

      /**
       * Returns an Audit object representing the filter criteria for the audit log.
       * @param rowData - The data of the row.
       * @param dateComponents - The date components (year, month, quarter).
       * @returns An Audit object with the filter criteria.
       */
      getAuditLogFilter(
        rowData: any,
        dateComponents: { year: any; month: number; quarter: any }
      ) {
        return <Audit>{
          valueType: this.tabValueTypeList.length == 0 ? "Actual" : this.tabName,
          kpiId: rowData.KpiId,
          mappingId: rowData["MappingId"],
          quarter: dateComponents.quarter,
          year: dateComponents.year,
          month: dateComponents.month,
          moduleId: KPIModulesEnum.Operational,
          companyId: this.modelList.portfolioCompanyID,
        };
      }

      /**
       * Handles the click event of the audit log button.
       * Navigates to the audit logs page with the necessary data.
       * @param rowData - The data of the clicked row.
       * @param field - The field of the clicked row.
       */
      onAuditLog(rowData: any, field: any) {
        if (this.ErrorNotation) {
          if (rowData.IsHeader || rowData.IsFormula || rowData.MasterFormula !== null) {
            this.showErrorToast(this.auditLogErrorForConvertedValue);
            return;
          }

          if (this.isConvertedValue(rowData)) {
            const message = getConversionErrorMessage(
        this.kpiCurrencyFilterModel.isSpotRate,
        this.auditLogSpotRateConversionError,
        this.auditLogErrorForConvertedValue
      );
            this.showErrorToast(message);
            return;
          }
          const dateComponents = extractDateComponents(field.header);
          let auditLogFilter = this.getAuditLogFilter(rowData, dateComponents);
          this.auditService
            .getPortfolioEditSupportingCommentsData(auditLogFilter)
            .subscribe({
              next: (data: MappedDocuments) => {
                if (data?.auditLogId > 0 && data?.auditLogCount > 0) {
                  let attributeName = rowData.KPI;
                  this.redirectToAuditLogPage(
                    field,
                    attributeName,
                    data,
                    auditLogFilter
                  );
                } else if (data?.auditLogCount == 0) {
                  this.showErrorToast(GlobalConstants.AuditLogNAMessage);
                }
              },
              error: (error: any) => {
                this.showErrorToast(this.auditLogErrorForConvertedValue);
              },
            });
        }
      }
      /**
       * Retrieves the kpiValuesId for the given rowdata and column.
       * @param rowdata - The data of the row.
       * @param column - The column.
       * @returns The kpiValuesId if found, otherwise 0.
       */
      getValues(rowdata: any, column: any) {
        let result = this.getFilterAuditValue(rowdata,column);
        if (result.length > 0) {
          return result[0].kpiValuesId;
        }
        else
          return 0;
    }
    /**
     * Sets the default type tab based on the values of `isMonthly` and `isQuarterly` properties.
     */
    setDefaultTypeTab = () => {
      switch (true) {
        case this.isMonthly:
          this.defaultType = PeriodTypeFilterOptions.Monthly;
          break;
        case this.isQuarterly:
          this.defaultType = PeriodTypeFilterOptions.Quarterly;
          break;
        default:
          this.defaultType = PeriodTypeFilterOptions.Annual;
          break;
      }
    };

    /**
     * Exports the operational KPI values.
     */
    exportOperationalKpiValues() {
      const canExport = this.operationalKPIPermissions?.map(access => access.canExport);
      if (canExport.includes(true)) {
        let paginationFilter = GlobalConstants.KpiFillter;
        let filter = this.GetFilter();
        this.exportOperationalKPILoading = true;
        this.portfolioCompanyService.exportOperationalKPIList(this.GetExportParameters(filter, paginationFilter)).subscribe(this.GetExportCallback());
      }
      else {
        this.toastrService.error(ErrorMessage.NoAccess, "", { positionClass: OperationalKPIConstants.ToastCenterCenter });
      }
    }
    
    /**
     * Retrieves the filter object used for querying operational KPI data.
     * @returns The filter object containing currency, decimal place, and value type.
     */
    GetFilter() {
      return {
        currency: this.modelList.reportingCurrencyDetail.currency,
        decimaPlace: this.modelOperationalKpi.decimalPlaces.type,
        valueType: this.operationalKpiValueUnit.typeId
      };
    }
    
    /**
     * Retrieves the export parameters for the operational KPIs.
     * @param {any} filter - The filter to apply to the KPIs.
     * @param {any} paginationFilter - The pagination filter to apply.
     * @returns {any} The export parameters.
     */
    GetExportParameters(filter, paginationFilter) {
      return {
        companyId: this.modelList?.portfolioCompanyID?.toString(),
        portfolioCompanyID: this.modelList?.portfolioCompanyID?.toString(),
        paginationFilter: paginationFilter,
        searchFilter: this.operationalKpiSearchFilter,
        kPIFilter: filter,
        moduleId : KPIModulesEnum.Operational,
        Unit:this.operationalKpiValueUnit.typeId,
        kpiConfigurationData:this.pageConfigResponse.kpiConfigurationData,
        IsSpotRate : this.kpiCurrencyFilterModel.isSpotRate == null ? false : this.kpiCurrencyFilterModel.isSpotRate,
        SpotRateDate:this.kpiCurrencyFilterModel.isSpotRate ? this.datePipe.transform(this.kpiCurrencyFilterModel.spotRateDate, 'yyyy-MM-dd') : null,
        currencyCode: this.kpiCurrencyFilterModel?.currencyCode,
        SpotRate:this.kpiCurrencyFilterModel.isSpotRate ? this.kpiCurrencyFilterModel.spotRate : null,
        reportingCurrencyCode: this.modelList?.reportingCurrencyDetail?.currencyCode,
        currencyRateSource: this.kpiCurrencyFilterModel?.currencyRateSource,
      };
    }
    
    /**
     * Returns a callback function for exporting operational KPI data.
     * The callback function handles the response from the export request.
     * If the request is successful, it disables the loading indicator and downloads the Excel file.
     * If there is an error, it disables the loading indicator and displays an error toast message.
     * @returns The callback function for exporting operational KPI data.
     */
    GetExportCallback() {
      return {
        next:(response) => {
          this.exportOperationalKPILoading = false;
          this.miscService.downloadExcelFile(response);
        },
        error() {
          this.exportOperationalKPILoading = false;
          this.toastrService?.error(ErrorMessage.SomethingWentWrong, "", { positionClass: OperationalKPIConstants.ToastCenterCenter });
        }
      };
    }
}

/**
 * Subscribes to an Observable and forwards its emissions to a Subject.
 * 
 * @template T The type of data emitted by the Observable and Subject.
 * @param {Observable<T>} from The Observable to subscribe to.
 * @param {Subject<T>} to The Subject to forward the emissions to.
 * @returns {Subscription} A Subscription object representing the subscription.
 */
function feed<T>(from: Observable<T>, to: Subject<T>): Subscription {
  return from.subscribe(
    data => to.next(data),
    err => to.error(err),
    () => to.complete(),
  );
}