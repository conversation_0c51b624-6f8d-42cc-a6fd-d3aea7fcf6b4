<div class="row mr-0 ml-0 foot-note-section">
    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 col-xs-12 pr-0 pl-0 foot-editor-section">
            <quill-editor #editor [(ngModel)]="value"  [readOnly]="readOnly"  placeholder="{{editorPlaceholder}}" [modules]="quillConfig"
            (ngModelChange)="onContentChanged($event)">
            </quill-editor>
            <div *ngIf="showCharCount" class="char-count">{{ charCount }}/6000</div>
    </div>
</div>