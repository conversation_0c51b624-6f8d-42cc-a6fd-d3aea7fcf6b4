import { Component, OnInit, Input, OnDestroy, Output, EventEmitter } from '@angular/core';
import { DashboardTrackerService } from '../../services/dashboard-tracker.service';
import { Observable, of, Subject } from 'rxjs';
import { ColGroupComponent, GridDataResult } from '@progress/kendo-angular-grid';
import { State, groupBy } from '@progress/kendo-data-query';
import { DashboardCellValueDto, SaveDashboardCellValuesDto, DashboardTrackerConfigDto } from './model/dashboard-tracker-config.model';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import { SVGIcon } from '@progress/kendo-svg-icons';
import { DashboardConfigurationConstants } from 'src/app/common/constants';

@Component({
  selector: 'app-dashboard-tracker',
  templateUrl: './dashboard-tracker.component.html',
  styleUrls: ['./dashboard-tracker.component.scss']
})
export class DashboardTrackerComponent implements OnInit, OnDestroy {
  @Input() passedClass: string = '';
  @Input() isDashboardConfigurationTab: boolean = false;
  @Output() cellChangesUpdated = new EventEmitter<number>();
  isLoading: boolean = true;
  moreColumn: boolean = false;
  gridColumns: any[] = [];
  // Track filtered columns (by name)
  filteredColumnNames: string[] = [];
  @Output() columnVisibilityChanged = new EventEmitter<any[]>();
  
  totalRecords: number = 0;
  defaultDateFormat: string = 'DD/MM/YYYY'; // placeholder text display only
  state: State = {
    skip: 0,
    take: 100
  };
  view: Observable<GridDataResult>;

  // Collection to store cell value changes
  cellValueChanges: DashboardCellValueDto[] = [];

  // Collection to store validation errors for textboxes
  validationErrors: Map<string, string> = new Map();

  // Subject for debouncing textbox changes
  private textboxChangeSubject = new Subject<{value: string, dataItem: any, column: any}>();
  private destroy$ = new Subject<void>();

  // Expose constants to template
  DashboardConfigurationConstants = DashboardConfigurationConstants;

  // New properties for selection and delete functionality
  showDeletePopup: boolean = false;
  showSelectionPopup: boolean = false;
  selectedDocumentIds: string[] = [];
  selectedDocumentCount: number = 0;
  documentToDelete: any = null;
  isDeleteDisabled: boolean = false;
  isMultipleDelete: boolean = true;
  deleteMessage: string = 'Deleting of columns will delete all the underlying data with it.';
  deleteHeader: string = 'Do you want to delete selected columns ?';
  public modalTitle: string = 'Delete Confirmation';
  constructor(
    private dashboardTrackerService: DashboardTrackerService,
    private toastrService: ToastrService,
  ) {}

  ngOnInit(): void {
    this.loadDashboardTableData(this.state);
    this.setupTextboxDebounce();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupTextboxDebounce(): void {
    this.textboxChangeSubject
      .pipe(
        debounceTime(500), // 2 second debounce
        takeUntil(this.destroy$)
      )
      .subscribe(({value, dataItem, column}) => {
        this.updateCellValue(dataItem, column, value);
      });
  }

  loadDashboardTableData(state: State): void {
    const filter = {
      First: state.skip,
      Rows: state.take
    };
    this.isLoading = true;
    this.dashboardTrackerService.getDashboardTableData(filter).subscribe((response) => {
      if (response && response.data && response.columns) {
        this.gridColumns = response.columns.map((col: any) => this.processGridColumn(col));
         // Filter and reorder columns to put SerialNo first
        const serialNoColumn = response.columns.find(col => col.name === this.DashboardConfigurationConstants.SerialNo);
        const otherColumns = response.columns.filter(col => col.name !== this.DashboardConfigurationConstants.SerialNo);
        if (serialNoColumn) {
          this.gridColumns = [serialNoColumn, ...otherColumns];
        } else {
         this.gridColumns = response.columns;
        }
        this.emitColumnVisibility();
        this.totalRecords = response.totalRecords || 0;
        this.view = of<GridDataResult>({
          data: response.data,
          total: this.totalRecords
        });
      } else {
        this.view = of<GridDataResult>({ data: [], total: 0 });
      }
      this.isLoading = false;
    });
  }

  // Helper to process grid column for dropdowns and visibility
  private processGridColumn(col: any): any {
    // Add hide property: default false for Fund/Portfolio, true for others
    col.hide = false;
    if (col.isDropDown && Array.isArray(col.dropDownValues)) {
      col.dropDownValues = col.dropDownValues.map((item: any) => {
        let typeText = item.type === 1 ? 'Icon' : 'Text';
        let svgIcon: SVGIcon | null = null;
        if (item.type === 1) {
          const iconObj = DashboardConfigurationConstants.IconList.find(icon => icon.text === item.displayText);
          svgIcon = iconObj ? iconObj.svgIcon : null;
        }
        return {
          ...item,
          typeText,
          svgIcon
        };
      });
      // Only group if both types are present
      const types = Array.from(new Set(col.dropDownValues.map((v: any) => v.typeText)));
      if (types.length > 1) {
        col.dropDownValues = groupBy(col.dropDownValues, [{ field: 'typeText' }]);
      }
    }
    return col;
  }

  dataStateChange(event: any): void {
    this.state = event;
    this.loadDashboardTableData(this.state);
  }

  navigateToDashboardConfig(): void {

  }

  // Handle dropdown value changes
  onDropdownValueChange(value: string, dataItem: any, column: any): void {
    this.updateCellValue(dataItem, column, value);
  }

  // Handle textbox value changes with debounce
  onTextboxValueChange(value: string, dataItem: any, column: any): void {
    // Validate input based on data type
    const validationKey = this.getValidationKey(dataItem, column);
    const validationResult = this.validateInput(value, column.dataType);

    if (validationResult.isValid) {
      // Clear any existing validation error
      this.validationErrors.delete(validationKey);
      // Immediately update the UI for better user experience
      dataItem[column.name] = value;
      // Send to debounced subject for actual processing
      this.textboxChangeSubject.next({value, dataItem, column});
    } else {
      // Store validation error
      this.validationErrors.set(validationKey, validationResult.errorMessage);
      // Still update UI to show user input, but don't process for saving
      dataItem[column.name] = value;
    }
  }

  // Handle clear button click for textbox
  clearTextboxValue(dataItem: any, column: any): void {
    const validationKey = this.getValidationKey(dataItem, column);
    // Clear validation error
    this.validationErrors.delete(validationKey);
    // Clear the value and process normally
    this.onTextboxValueChange('', dataItem, column);
  }

  // Update or add cell value to the collection
  private updateCellValue(dataItem: any, column: any, newValue: any): void {
    const portfolioCompanyId = dataItem['PCID'];
    const fundId = dataItem['FundID'];
    const columnId = column.id;
    const timeSeriesId = column.timeSeriesID || null;

    // Validate required fields
    if (!portfolioCompanyId || !columnId) {
      this.toastrService.error("Missing required IDs for cell value update.", '', { positionClass: 'toast-center-center' });
      return;
    }

    // Check if there are any validation errors for this field
    const validationKey = this.getValidationKey(dataItem, column);
    if (this.validationErrors.has(validationKey)) {
      // Don't save invalid data
      return;
    }

    // Find existing entry or create new one
    const existingIndex = this.cellValueChanges.findIndex(
      item => item.PortfolioCompanyId === portfolioCompanyId &&
              item.FundId === fundId &&
              item.ColumnId === columnId &&
              item.TimeSeriesID === timeSeriesId
    );

    const cellValueDto: DashboardCellValueDto = {
      PortfolioCompanyId: portfolioCompanyId,
      FundId: fundId,
      ColumnId: columnId,
      TimeSeriesID: timeSeriesId,
      CellValue: newValue || null,
    };

    if (existingIndex >= 0) {
      // Update existing entry
      this.cellValueChanges[existingIndex] = cellValueDto;
    } else {
      // Add new entry
      this.cellValueChanges.push(cellValueDto);
    }

    // Update the grid data item for immediate UI feedback
    dataItem[column.name] = newValue;

    // Emit change event to parent component
    this.cellChangesUpdated.emit(this.cellValueChanges.length);
  }

  // Save all cell value changes
  saveCellValues(): void {
    if (this.cellValueChanges.length === 0) {
      return;
    }

    // Check if there are any validation errors
    if (this.validationErrors.size > 0) {
      this.toastrService.error('Please fix validation errors before saving.', '', { positionClass: 'toast-center-center' });
      return;
    }

    const payload: SaveDashboardCellValuesDto = {
      CellValues: this.cellValueChanges
    };

    this.dashboardTrackerService.saveDashboardCellValues(payload).subscribe({
      next: (response) => {
        this.toastrService.success('Modified values saved successfully.', '', { positionClass: 'toast-center-center' });
        // Clear the changes collection after successful save
        this.cellValueChanges = [];
        // Emit change event to parent component
        this.cellChangesUpdated.emit(this.cellValueChanges.length);
      },
      error: (error) => {
        this.toastrService.error('Failed to save cell values.', '', { positionClass: 'toast-center-center' });
      }
    });
  }

  // Get pending changes count for UI display
  getPendingChangesCount(): number {
    return this.cellValueChanges.length;
  }

  // Clear all pending changes
  clearPendingChanges(): void {
    this.cellValueChanges = [];
    this.validationErrors.clear();
    // Emit change event to parent component
    this.cellChangesUpdated.emit(this.cellValueChanges.length);
  }

  // Get current changes for debugging
  getCurrentChanges(): DashboardCellValueDto[] {
    return [...this.cellValueChanges];
  }

  // Generate unique key for validation tracking
  private getValidationKey(dataItem: any, column: any): string {
    const portfolioCompanyId = dataItem['PCID'];
    const columnId = column.id;
    return `${portfolioCompanyId}_${columnId}`;
  }

  // Validate input based on data type
  private validateInput(value: string, dataType: number): {isValid: boolean, errorMessage: string} {
    // If empty value, consider it valid (user can clear the field)
    if (!value || value.trim() === '') {
      return { isValid: true, errorMessage: '' };
    }

    switch (dataType) {
      case 2: // Number
        return this.validateNumber(value);
      case 3: // Date
        return this.validateDate(value);
      default:
        return { isValid: true, errorMessage: '' };
    }
  }

  // Validate number input
  private validateNumber(value: string): {isValid: boolean, errorMessage: string} {
    const trimmedValue = value.trim();

    // Check if it's a valid number
    if (isNaN(Number(trimmedValue)) || trimmedValue === '') {
      return { isValid: false, errorMessage: 'Please enter a valid number' };
    }

    // Additional checks for number format
    // Improved regex to avoid super-linear backtracking and ReDoS
    const numberRegex = /^-?(?:\d+\.\d+|\d+|\.\d+)$/;
    if (!numberRegex.test(trimmedValue)) {
      return { isValid: false, errorMessage: 'Please enter a valid number format' };
    }

    return { isValid: true, errorMessage: '' };
  }

  // Validate date input
  private validateDate(value: string): {isValid: boolean, errorMessage: string} {
    const trimmedValue = value.trim();

    // Check DD/MM/YYYY format
    const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
    const match = trimmedValue.match(dateRegex);

    if (!match) {
      return { isValid: false, errorMessage: 'Please enter date in DD/MM/YYYY format' };
    }

    const day = parseInt(match[1], 10);
    const month = parseInt(match[2], 10);
    const year = parseInt(match[3], 10);

    // Basic date validation
    if (month < 1 || month > 12) {
      return { isValid: false, errorMessage: 'Invalid month (01-12)' };
    }

    if (day < 1 || day > 31) {
      return { isValid: false, errorMessage: 'Invalid day (01-31)' };
    }

    // More precise date validation
    const date = new Date(year, month - 1, day);
    if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
      return { isValid: false, errorMessage: 'Invalid date' };
    }

    return { isValid: true, errorMessage: '' };
  }

  public hasErrors(): boolean {
    return this.validationErrors.size > 0;
  }
  // Check if a specific textbox has validation error
  hasValidationError(dataItem: any, column: any): boolean {
    const validationKey = this.getValidationKey(dataItem, column);
    return this.validationErrors.has(validationKey);
  }

  // Get validation error message for a specific textbox
  getValidationError(dataItem: any, column: any): string {
    const validationKey = this.getValidationKey(dataItem, column);
    return this.validationErrors.get(validationKey) || '';
  }

  getSelectedDropDownValue(col, selected) {
    if (!col?.dropDownValues || selected == null) return null;
    return col.dropDownValues.find(item => item.value === selected) || null;
  }

  getDropDownValuesData(value: any, dropDownValues: any[]): string | { svgIcon: any, displayText: string } {
    if (!dropDownValues || value == null) return '';
    // Helper to extract item from group or flat array
    const findItem = (arr: any[]): any => arr.find(i => i.value === value);
    // If grouped (array of groups with .items), search in each group
    if (dropDownValues.length > 0 && dropDownValues[0].items) {
      for (const group of dropDownValues) {
        if (Array.isArray(group.items)) {
          const item = findItem(group.items);
          if (item) return item.type === 1 ? { svgIcon: item.svgIcon, displayText: item.displayText } : item.displayText;
        }
      }
    } else {
      // Not grouped, flat array
      const item = findItem(dropDownValues);
      if (item) return item.type === 1 ? { svgIcon: item.svgIcon, displayText: item.displayText } : item.displayText;
    }
    return '';
  }

  // Called when filter selection changes (from parent)
  setColumnVisibility(selectedNames: string[]): void {
    this.filteredColumnNames = selectedNames;
    this.gridColumns.forEach(col => {
      // Always show Fund/Portfolio columns
      if (col.name === 'Fund Name' || col.name === 'Portfolio Company Name') {
        col.hide = false;
      } else {
        // If selectedNames is empty, hide all except Fund/Portfolio
        col.hide = selectedNames.length === 0 ? true : !selectedNames.includes(col.name);
      }
    });
    this.emitColumnVisibility();
  }

  // Emit visible columns to parent
  emitColumnVisibility(): void {
    // Only send columns that are not hidden
    const visibleColumns = this.gridColumns.filter(col => !col.hide);
    this.columnVisibilityChanged.emit(visibleColumns);
  }



  onColumnSelectionClick(index: number): void {
    // Check bounds to prevent errors
    if (index < 0 || index >= this.gridColumns.length) {
      return;
    }
    
    this.gridColumns[index].selected = !this.gridColumns[index].selected;
    this.selectedDocumentCount = this.gridColumns.filter(column => column.selected).length;
    this.selectedDocumentIds = this.gridColumns.filter(column => column.selected).map(column => column.name);
    this.showSelectionPopup = this.selectedDocumentCount > 0;
    this.isDeleteDisabled = false; 
  }

  closeSelectionPopup(): void {
    this.showSelectionPopup = false;
    this.gridColumns.forEach(column => column.selected = false);
    this.selectedDocumentCount = 0;
  }

  handleDeleteSelectedClick(): void {
    this.showDeletePopup = true;
    this.showSelectionPopup = false;
  }
  deleteSelectedColumns(): void {
    this.showDeletePopup = false;
  }
  closeDeletePopup(): void {
    this.showDeletePopup = false;
  }
  
   // Called when user cancels deletion in modal
   public cancelDelete(): void {
    this.showDeletePopup = false;
    // Restore selection popup and keep selections when canceling delete
    this.showSelectionPopup = this.selectedDocumentCount > 0;
  }

  deleteDashboardTrackerColumns(): void {
    this.showDeletePopup = false;
    let selectedColumns = this.gridColumns.filter(column => column.selected);
    if (selectedColumns.length > 0) {
      
      // Convert selected columns to DashboardTrackerConfigDto objects
      const configDtos: DashboardTrackerConfigDto[] = selectedColumns.map(column => ({
        ID: column.id,
        FieldType: column.fieldType,
        DataType: column.dataType,
        Name: column.name,
        IsTimeSeries: column.isTimeSeries,
        TimeSeriesID: column.timeSeriesID
      }));
      this.isLoading = true;
      this.dashboardTrackerService.deleteDashboardTrackerConfigs(configDtos).subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success('Columns deleted successfully.', '', { positionClass: 'toast-center-center' });
            // Refresh the grid data after successful deletion
            this.loadDashboardTableData(this.state);
          } else {
            this.toastrService.error('Failed to delete columns.', '', { positionClass: 'toast-center-center' });
          }
        },
        error: (error) => {
          this.toastrService.error('Failed to delete columns.', '', { positionClass: 'toast-center-center' });
          console.error('Error deleting columns:', error);
          this.isLoading = false;
        }
      });
    } else {
      // No columns selected, ensure loading is false
      this.isLoading = false;
    }
  }
  
}
