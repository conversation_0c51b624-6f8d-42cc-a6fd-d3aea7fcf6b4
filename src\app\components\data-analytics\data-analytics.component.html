<div class="row mr-0 ml-0 tab-shadow-home-bi data-analytics-header">
    <div
        class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 horizontal-parent boxBorder-dashboard">
        <div class= "panel d-flex panel-default tab-bg tab-home-bg border-0">
            <div class="data-analytics-header-left  panel-heading pr-0 pl-0 custom-tab-panel-heading pb-0 border-bottom-0 panel-title custom-tabs custom-mat-tab">
                <nav mat-tab-nav-bar [tabPanel]="tabPanel" *ngIf="filterAnalyticsList?.length > 0 && enabledtab">

                    <a mat-tab-link [disableRipple]="true" *ngFor="let dashboard of filterAnalyticsList" id="change-tab-type"
                        (click)="changeTabType(dashboard);isCreateDashboard = false;" [active]="dashboard.isActive"
                        class="TextTruncate">
                        {{dashboard.dataSetName}} </a>

                </nav>
            </div>
            <div class="data-analytics-header-right data-analytics-tab-button data-analytics-dots" *ngIf="filterAnalyticsList?.length > 0">
                <div class="d-inline-block search-icon">
                    <span class="fa fa-search fasearchicon p-1"></span>
                </div>
                <div class="d-inline-block data-analytics-col-divider" *ngIf="deleteButtonEnabled">
                    <span class="col-divider">
                    </span>
                </div>
                <div id="delete-dashboard" title="Delete Dashboard" *ngIf="deleteButtonEnabled" class="d-inline-block float-right delete-icon">
                    <span (click)="deletePopupFunction()" id="delete-dashboard-func"> <img id="delete-dashboard-icon" alt="" src='assets/dist/images/delete.svg' /></span>
                </div>
            </div>
           
        </div>
        <mat-tab-nav-panel #tabPanel>
            <div class="col-12 pl-3 pr-3" (resized)="onResized($event)">
                <div #revealView class="reveal-bi-component-view"></div>
            </div>
        </mat-tab-nav-panel>
    </div>
    <div *ngIf="deletePopub">
        <confirm-modal [primaryButtonName]="primaryDeleteButtonName" [secondaryButtonName]="secondaryDeleteButtonName"
            [modalTitle]="deleteModalTitle" (primaryButtonEvent)="tabDelete()" (secondaryButtonEvent)="tabCancel()">
            <div class="modalBodyTextStyle">
                <div>
                    {{deleteModalBody}}
                </div>
            </div>
        </confirm-modal>
    </div>
</div>

<div *ngIf="displayModal" class="nep-modal nep-modal-show custom-modal" style="display: block;">
    <div class="nep-modal-mask"></div>
    <div [style.width]="fullViewWidth" class="nep-card nep-card-shadow nep-modal-panel nep-modal-default" style="position: relative; display: inline-flex; top: 20%;width: 50rem; height: 25rem;">
        <div class="nep-card-header nep-modal-title">
            <div class="float-left TextTruncate">Dashboard Details</div>
            <div class="float-right" (click)="hideModal()" id="hide-modal">
                <div class="close-icon"><i  class="pi pi-times"></i></div>
            </div>
        </div>
        <div class="nep-card-body">    
                <div class="model-custom-padding">
                    <form #analyticsForm="ngForm"> 
                        <div class="row mr-0 ml-0">
                                    <div class="col-12 pr-0 pl-0">
                                        <div class="col-12 pr-0 pl-0">
                                            <label class="pt-1 mandatory-label">
                                                Dashboard Name
                                            </label>
                                        </div>
                                        <div class="col-12 pr-0 pl-0">
                                            <input id="dashboard-name-field" type="text" required [(ngModel)]="analyticsName" #analytics="ngModel" autocomplete="off"
                                                name="analytics" class="form-control data-input-css pl-0" placeholder="Enter dashboard name"
                                                maxlength="100" />
                                            <div class="pt-2" *ngIf="analytics.invalid && (analytics.dirty || analytics.touched)">
                                                <div class="text-danger" *ngIf="analytics.errors?.required">
                                                    Enter the dashboard name
                                                </div>
                                            </div>
                                        </div><br/>
                                        <div class="col-12 pr-0 pl-0 personal-dash-label">
                                            <div class="float-left " style="color: #4061c7;">
                                                Personal Dashboard
                                            </div>
                                            <div class="float-right">
                                                <kendo-switch id="personal-dashboard-switch"  name="switch" [(ngModel)]="isPersonal"  [onLabel]="' '" [offLabel]="' '"> </kendo-switch>
                                            </div>
                                        </div><br/>
                                        <div class="col-12 pr-0 pl-0 pt-2 pb-3">
                                            <div class="popupcommon-labelpadding pb-2">Description</div>
                                            <div class="custom-nep-input">
                                                <div class="nep-input nep-input-textarea kpitext-label workflow-add-group pl-0">
                                                    <textarea id="description" class="group-text pl-0 pb-2" rows="1" spellcheck="false" name="desc"
                                                        #desc="ngModel" placeholder="Enter Description"
                                                        [(ngModel)]="description"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                        </div>
                    </form>
                        <div class="float-right">
                            <nep-button Name="save-as-reset" Type="Secondary" (click)="reset();" [disabled]="analyticsName==null" class="mr-2">
                                Reset
                            </nep-button>
                            <nep-button Name="save-as-confirm" Type="Primary" (click)="save()" [disabled]="analyticsName==null">
                                Confirm
                            </nep-button>
                        </div>
                </div>
        </div>
    </div>
</div>
<app-loader-component *ngIf="isLoader"></app-loader-component>