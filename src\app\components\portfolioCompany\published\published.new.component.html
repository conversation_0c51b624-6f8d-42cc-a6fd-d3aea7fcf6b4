<div class="portfolio-company-list-table">
    <div class="row  mr-0 ml-0 filter-bg border-top border-bottom" >
        <div class="col-lg-12 col-md-12 col-sm-12 pl-0 pr-0">
            <div class="float-left ml-3">
                <div class="d-inline-block search search-pccontainer">
                    <span class="fa fa-search fasearchicon p-1 search-icon"></span>
                    <input #gb pInputText type="text"
                        (input)="searchLoadPCLazy()"
                        class="search-text-company TextTruncate companyListSearchHeight search-box" [(ngModel)]="globalFilter"
                        placeholder="Search here..." id="pc-list-search">
                </div>
            </div> 
            <div class="float-right mr-3 mt-3 mb-3 pointer-cursor" [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"export"}' id="download-pc-list">
                <img title="Download  (Excel file)" src="assets/dist/images/download-icon.svg" (click)="exportPortfolioCompanyList()"/>
            </div>
        </div>
    </div>
    <kendo-grid [loading]="isLoader"
    [data]="view | async"
        [pageSize]="state.take"
        [skip]="state.skip"
        [sortable]="true"
        [sort]="sort"
        [pageable]="{
            buttonCount: 10,
            info: true,
            type: 'numeric',
            pageSizes: [300,500],
            previousNext: true  }"
        (dataStateChange)="dataStateChange($event)"
    class="custom-kendo-pc-list-grid k-grid-border-right-width k-grid-outline-none" >
      <ng-container *ngFor="let item of headers">
        <kendo-grid-column    class="TextTruncate" *ngIf="item.sortFieldName.toLowerCase()==('ValueColumn1.Value').toLowerCase()" field="ValueColumn1.Value">
            <ng-template  kendoGridHeaderTemplate>
                <div  class="header-icon-wrapper wd-98">
                  <span class="TextTruncate S-M">
                    {{item.header}}
                  </span>
                </div>
              </ng-template>
            <ng-template kendoGridCellTemplate let-pc >
                <div class="TextTruncate" *ngIf = "pc.valueColumn1 != null">
                    <a *ngIf = "pc.valueColumn1.link != '' && pc.valueColumn1.name != 'Website'" (click)="redirectToCompany(pc.valueColumn1.value,pc.valueColumn1.name)" class="click-view TextTruncate" title="{{pc.valueColumn1.value}}"
                    href="javascript:void(0);"
                    [routerLink]="[pc.valueColumn1.link, pc.valueColumn1.encryptedId]"
                    title="View Details"
                    [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"view"}'>{{pc.valueColumn1.value}}
                    </a>
                    <a id="pc-list-pc-valueColumn1-value" *ngIf = "pc.valueColumn1.link != '' && pc.valueColumn1.name == 'Website'" class="click-view TextTruncate linkStyle"
                    title="{{pc.valueColumn1.Value}}" href="//{{pc.valueColumn1.Value}}"  target="_blank"
                    [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"view"}'>{{pc.valueColumn1.value}}
                    </a>
                    <div class="float-left TextTruncate sector-width float-cur" *ngIf = "pc.valueColumn1.link == ''">
                        <span class="">{{pc.valueColumn1.value}}</span>
                    </div>
                    <div class="float-right" *ngIf="headers.length == 1">
                            <button type="button"
                                *ngIf="(pc.valueColumn1.workflowRequestId!=null && pc.valueColumn1.workflowRequestId>0) && (isEditDraft())"
                                class="nep-button nep-button-secondary"
                                [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"edit"}'
                                (click)="setCompanyValues(pc);encryptedPortfolioCompanyId=pc.valueColumn1.pcEncryptedId;draftName=pc.valueColumn1.draftName;redirectToDraft(pc.valueColumn1.workflowRequestId,true,pc.valueColumn1.pcEncryptedId)">
                                View Draft
                            </button>
                            <button type="button"
                                *ngIf="(pc.valueColumn1.workflowRequestId==null || pc.valueColumn1.workflowRequestId==0) && (isCreateDraft())"
                                class="nep-button nep-button-secondary"
                                [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"edit"}'
                                (click)="setCompanyValues(pc);isOpenNewDraft=false;encryptedPortfolioCompanyId=pc.valueColumn1.pcEncryptedId; creatingDraft(pc.valueColumn1.pcEncryptedId,pc.valueColumn1.value)">
                                New Draft
                            </button>
                    </div>
                </div>
             </ng-template>
        </kendo-grid-column>
        <kendo-grid-column   class="TextTruncate" *ngIf="item.sortFieldName.toLowerCase()==('ValueColumn2.Value').toLowerCase()"  field="ValueColumn2.Value">
            <ng-template  kendoGridHeaderTemplate>
                <div  class="header-icon-wrapper wd-98" >
                  <span class="TextTruncate S-M">
                    {{item.header}}
                  </span>
                </div>
              </ng-template>
            <ng-template kendoGridCellTemplate let-pc  >
                <a *ngIf = "pc.valueColumn2.link != ''  && pc.valueColumn2.name != 'Website'" (click)="redirectToCompany(pc.valueColumn2.value,pc.valueColumn2.name)" class="click-view TextTruncate" title="{{pc.valueColumn2.value}}" href="javascript:void(0);"
                [routerLink]="[pc.valueColumn2.link, pc.valueColumn2.encryptedId]"
                title="View Details"
                [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"view"}'>{{pc.valueColumn2.value}}</a>
                <a *ngIf = "pc.valueColumn2.link != '' && pc.valueColumn2.name == 'Website'" class="click-view TextTruncate linkStyle"
                title="{{pc.valueColumn2.Value}}" href="//{{pc.valueColumn2.Value}}" target="_blank"
                [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"view"}'>{{pc.valueColumn2.value}}
                </a>
                <div class="float-left TextTruncate sector-width float-cur" *ngIf = "pc.valueColumn2.link == ''">
                    <span class="">{{pc.valueColumn2.value}}</span>
                </div>
                <div class="float-right" *ngIf="headers.length == 2">
                    <span class="float-right company-buttons" *ngIf="isWorkflow">
                        <button type="button"
                            *ngIf="(pc.valueColumn2.workflowRequestId!=null && pc.valueColumn2.workflowRequestId>0) && (isEditDraft())"
                            class="nep-button nep-button-secondary"
                            [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"edit"}'
                            (click)="setCompanyValues(pc);encryptedPortfolioCompanyId=pc.valueColumn2.pcEncryptedId;draftName=pc.valueColumn2.draftName;redirectToDraft(pc.valueColumn2.workflowRequestId,true,pc.valueColumn2.pcEncryptedId)">
                            View Draft
                        </button>
                        <button type="button"
                            *ngIf="(pc.valueColumn2.workflowRequestId==null || pc.valueColumn2.workflowRequestId==0) && (isCreateDraft())"
                            class="nep-button nep-button-secondary"
                            [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"edit"}'
                            (click)="setCompanyValues(pc);isOpenNewDraft=false;encryptedPortfolioCompanyId=pc.valueColumn2.pcEncryptedId; creatingDraft(pc.valueColumn2.pcEncryptedId,pc.valueColumn1.value)">
                            New Draft
                        </button>
                    </span>
                </div>
        </ng-template>
        </kendo-grid-column>
        <kendo-grid-column   class="TextTruncate"  *ngIf="item.sortFieldName.toLowerCase()==('ValueColumn3.Value').toLowerCase()"  field="ValueColumn3.Value">
            <ng-template  kendoGridHeaderTemplate>
                <div  class="header-icon-wrapper wd-98" >
                  <span class="TextTruncate S-M">
                    {{item.header}}
                  </span>
                </div>
              </ng-template>
            <ng-template kendoGridCellTemplate let-pc  >
                <a *ngIf = "pc.valueColumn3.link != ''  && pc.valueColumn1.name != 'Website'" class="click-view TextTruncate" title="{{pc.valueColumn3.value}}" (click)="redirectToCompany(pc.valueColumn3.value,pc.valueColumn3.name)" href="javascript:void(0);"
                [routerLink]="[pc.valueColumn3.link, pc.valueColumn3.encryptedId]"
                title="View Details"
                [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"view"}'>{{pc.valueColumn3.value}}</a>
                <a id="portfolio-company-published-company-name" *ngIf = "pc.valueColumn3.link != '' && pc.valueColumn3.name == 'Website'"
                class="click-view TextTruncate linkStyle"
                title="{{pc.valueColumn3.value}}" href="//{{pc.valueColumn3.value}}" target="_blank"
                [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"view"}'>{{pc.valueColumn3.value}}
                </a>
                <div class="float-left TextTruncate sector-width float-cur" *ngIf = "pc.valueColumn3.link == ''">
                    <span class="">{{pc.valueColumn3.value}}</span>
                </div>
                <div class="float-right" *ngIf="headers.length == 3">
                    <span class="float-right company-buttons" *ngIf="isWorkflow">
                        <button type="button"
                            *ngIf="(pc.valueColumn3.workflowRequestId!=null && pc.valueColumn3.workflowRequestId>0) && (isEditDraft())"
                            class="nep-button nep-button-secondary"
                            [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"edit"}'
                            (click)="setCompanyValues(pc);encryptedPortfolioCompanyId=pc.valueColumn3.pcEncryptedId;draftName=pc.valueColumn3.draftName;redirectToDraft(pc.valueColumn3.workflowRequestId,true,pc.valueColumn3.pcEncryptedId)">
                            View Draft
                        </button>
                        <button type="button"
                            *ngIf="(pc.valueColumn3.workflowRequestId==null || pc.valueColumn3.workflowRequestId==0) && (isCreateDraft())"
                            class="nep-button nep-button-secondary"
                            [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"edit"}'
                            (click)="setCompanyValues(pc);isOpenNewDraft=false;encryptedPortfolioCompanyId=pc.valueColumn3.pcEncryptedId; creatingDraft(pc.valueColumn3.pcEncryptedId,pc.valueColumn1.value)">
                            New Draft
                        </button>
                    </span>
                </div>
        </ng-template>
        </kendo-grid-column>
        <kendo-grid-column   class="TextTruncate" *ngIf="item.sortFieldName.toLowerCase()==('ValueColumn4.Value').toLowerCase()"  field="ValueColumn4.Value">
            <ng-template  kendoGridHeaderTemplate>
                <div  class="header-icon-wrapper wd-98" >
                  <span class="TextTruncate S-M">
                    {{item.header}}
                  </span>
                </div>
              </ng-template>
            <ng-template kendoGridCellTemplate let-pc  >
                <a *ngIf = "pc.valueColumn4.link != ''  && pc.valueColumn4.name != 'Website'" (click)="redirectToCompany(pc.valueColumn4.value,pc.valueColumn4.name)" class="click-view TextTruncate" title="{{pc.valueColumn4.value}}" href="javascript:void(0);"
                [routerLink]="[pc.valueColumn4.link, pc.valueColumn4.encryptedId]"
                title="View Details"
                [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"view"}'>{{pc.valueColumn4.value}}</a>
               
                <a *ngIf = "pc.valueColumn4.link != '' && pc.valueColumn4.name == 'Website'" class="click-view TextTruncate linkStyle"
                title="{{pc.valueColumn4.Value}}" href="//{{pc.valueColumn4.value}}" target="_blank"
                [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"view"}'>{{pc.valueColumn4.value}}
                </a>
                <div class="float-left TextTruncate sector-width float-cur" *ngIf = "pc.valueColumn4.link == ''">
                    <span class="">{{pc.valueColumn4.value}}</span>
                </div>
                <div class="float-right" *ngIf="headers.length == 4">
                    <span class="float-right company-buttons" *ngIf="isWorkflow">
                        <button type="button"
                            *ngIf="(pc.valueColumn4.workflowRequestId!=null && pc.valueColumn4.workflowRequestId>0) && (isEditDraft())"
                            class="nep-button nep-button-secondary"
                            [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"edit"}'
                            (click)="setCompanyValues(pc);encryptedPortfolioCompanyId=pc.valueColumn4.pcEncryptedId;draftName=pc.valueColumn4.draftName;redirectToDraft(pc.valueColumn4.workflowRequestId,true,pc.valueColumn4.pcEncryptedId)">
                            View Draft
                        </button>
                        <button type="button"
                            *ngIf="(pc.valueColumn4.workflowRequestId==null || pc.valueColumn4.workflowRequestId==0) && (isCreateDraft())"
                            class="nep-button nep-button-secondary"
                            [hideIfUnauthorized]='{featureId:feature.PortfolioCompany,action:"edit"}'
                            (click)="setCompanyValues(pc);isOpenNewDraft=false;encryptedPortfolioCompanyId=pc.valueColumn4.pcEncryptedId; creatingDraft(pc.valueColumn4.pcEncryptedId,pc.valueColumn1.value)">
                            New Draft
                        </button>
                    </span>
                </div>
        </ng-template>
        </kendo-grid-column>
        </ng-container>
        <ng-template kendoGridNoRecordsTemplate>
            <app-empty-state class="finacials-beta-empty-state"  [imageHeight]="'calc(100vh - 309px) !important'" [isGraphImage]="false"></app-empty-state>
        </ng-template>
       <ng-template kendoGridLoadingTemplate>
            <app-loader-component ></app-loader-component>
          </ng-template> 
    </kendo-grid>
</div>
<!-- <mat-menu #menu="matMenu">
    <div>
        <button mat-menu-item>
            <span id="pc-list-export-list" class="pc-menu nav-link pl-0 pr-0 TextTruncate" (click)="exportPortfolioCompanyList()">
                <img title="Export  (Excel file)" src="assets/dist/images/MS_Excel.svg" />
                Export List </span>
        </button>
        <button mat-menu-item *ngIf="!isTaabo">
            <span id="pc-list-bulk-export"  class=" pc-menu nav-link pl-0 pr-0 TextTruncate" (click)="exportPortfolioCompanyKPIDataList()">
                <img title="Export (Excel file)" src="assets/dist/images/MS_Excel.svg" />
                Bulk Export </span>
        </button>
    </div>
</mat-menu> -->
<!-- <div class="nep-modal nep-modal-show nepmdl-d-bg" *ngIf="isOpenNewDraft">
    <div class="nep-modal-mask"></div>
    <div class="nep-card nep-card-shadow nep-modal-panel nep-modal-default nepcrd-p-d-t">
        <div class="nep-card-header nep-modal-title">
            <div class="row mr-0 ml-0 ">
                <div class="col-md-12 user-header TextTruncate M-M pr-0 pl-0" title="Enter Workflow Name">
                    Enter Workflow Name
                </div>
            </div>
        </div>
        <div class="nep-card-body">
            <form [formGroup]="form" name="form" (ngSubmit)="f.valid && saveDraft()">
                <div class="row mr-0 ml-0 form-group required">
                    <div class="col-12 pr-0 pl-0">
                        <label for="workflowName" class="control-label TextTruncate" title="Workflow Name">Workflow Name</label>
                    </div>
                    <div class="col-12 col-sm-12 col-xs-12 col-lg-12 col-xl-12 pr-0 pl-0">
                        <kendo-textbox
                            formControlName="workflowName"
                            [fillMode]="'solid'"
                            placeholder="Enter workflow name"
                            class="w-100">
                        </kendo-textbox>
                    </div>
                </div>
            </form>
        </div>
        <div class="nep-card-footer nep-modal-footer">
            <div class="float-right">
                <div class="loading-input-controls-manual" *ngIf="isLoading">
                    <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                </div>
                <nep-button class="TextTruncate" Type="Secondary" title="Cancel" (click)="isOpenNewDraft=false;form.reset();" Name="Cancel">
                    Cancel
                </nep-button>
                <nep-button [disabled]="!form.valid" class="TextTruncate txt-trunc-pl" title="Save" (click)="saveDraft()" Type="Primary" Name="Save">
                    Save
                </nep-button>
            </div>
        </div>
    </div>
</div> -->
