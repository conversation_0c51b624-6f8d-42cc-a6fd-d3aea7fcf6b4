<div class="folder-list-container">
    <div class="add-document">
        <div class="Heading2-M pl-3 doc-list-text">{{documentsListAlias}}</div>
        <div class="pr-3">
            <button *ngIf="!showOptions" type="button" class="kendo-custom-button Body-R apply-btn mr-2 no-hover-effect" fillMode="outline" kendoButton
            (click)="showDocumentOptions()" [disabled]="!isAddButtonEnabled" themeColor="primary" id="add-doc-btn">
                <img src="assets/dist/images/add-doc-btn.svg" alt="Add Document" class="mr-1"/>
                Add Documents
            </button>
              <button *ngIf="showOptions" class="back-btn" id="back-btn" (click)="hideDocumentOptions()">
                <span class="Body-R add-doc-text">Back</span>
            </button>
            </div>
    </div>
    <div *ngIf="showOptions">
        <div class="document-options pb-3 pr-3 pl-3 p-2">
            <div class="radio-options" *ngIf="!uploadedFiles || uploadedFiles.length === 0">
                <div class="Body-R">
                    Local Documents
                </div>
            </div>

            <div class="files-header pt-3 pb-3 pl-3 pr-3 d-flex justify-content-between align-items-center"
                *ngIf="uploadedFiles && uploadedFiles.length > 0">
                <div class="Heading2-M">Documents Fetched</div>
                <button class="btn btn-primary" id="id-ingestion" class="kendo-custom-button Body-R apply-btn"
                kendoButton themeColor="primary" [disabled]="!isAnyDocumentSelected" (click)="saveFilesToServer()">Add Documents</button>
            </div>
        </div>

        <div class="document-content pr-3 pl-3" *ngIf="selectedDocumentType === 'localDocuments'">
            <!-- Show file upload area when no files exist -->
            <ng-container *ngIf="!uploadedFiles || uploadedFiles.length === 0; else filesList">
                <ng-container *ngTemplateOutlet="noDataTemplate"></ng-container>
            </ng-container>

            <!-- Show grid of files when files exist -->
            <ng-template #filesList>
                <div class="files-grid mt-2">
                    <kendo-grid [data]="uploadedFiles" [sortable]="true">
                        <kendo-grid-column field="selected" title="" width="50">
                            <ng-template kendoGridHeaderTemplate>
                                <input kendoCheckBox 
                                    type="checkbox" 
                                    class="k-checkbox k-checkbox-md k-rounded-md custom-border"
                                    [checked]="areAllDocumentsSelected()"
                                    (click)="toggleAllDocumentsSelection()"
                                    (keydown.space)="toggleAllDocumentsSelection()">
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                                <input kendoCheckBox 
                                    type="checkbox" 
                                    class="k-checkbox k-checkbox-md k-rounded-md custom-border"
                                    [checked]="dataItem.selected"
                                    (click)="toggleDocumentSelection(dataItem)"
                                    (keydown.space)="toggleDocumentSelection(dataItem)">
                            </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="name" title="Document Name">
                            <ng-template kendoGridCellTemplate let-dataItem>
                                <div class="d-flex align-items-center">
                                    <img src="assets/dist/images/Doctype.svg" alt="Add Document" class="mr-2"/>
                                    <span>{{ dataItem.name }}</span>
                                </div>
                            </ng-template>
                        </kendo-grid-column>
                    </kendo-grid>
                </div>
            </ng-template>
        </div>
    </div>
    <div *ngIf="!showOptions && documents && documents.length > 0" class="doc-container pr-3 pl-3">
        <div class="files-grid mt-2">
            <kendo-grid [data]="documents" [sortable]="true" [reorderable]="false" [resizable]="false" [scrollable]="'none'">
                <kendo-grid-column field="selected" title="" width="50">
                    <ng-template kendoGridHeaderTemplate>
                        <input kendoCheckBox 
                            type="checkbox" 
                            class="k-checkbox k-checkbox-md k-rounded-md custom-border"
                            [checked]="areAllDocumentsSelected()"
                            (click)="toggleAllDocumentsSelection()"
                            (keydown.space)="toggleAllDocumentsSelection()">
                    </ng-template>
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                        <input kendoCheckBox 
                            type="checkbox" 
                            class="k-checkbox k-checkbox-md k-rounded-md custom-border"
                            [checked]="dataItem.selected"
                            (click)="toggleDocumentSelection(dataItem)"
                            (keydown.space)="toggleDocumentSelection(dataItem)">
                    </ng-template>
                </kendo-grid-column>
                <kendo-grid-column field="documentName" title="Document Name">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <div class="d-flex align-items-center">
                            <img src="assets/dist/images/Doctype.svg" alt="No Data" class="mr-2" />
                            <span class="Caption-M doc-name-text">{{ formatDocumentName(dataItem.documentName) }}</span>
                        </div>
                    </ng-template>
                </kendo-grid-column>
                <kendo-grid-column field="userName" title="User Name">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <div class="Body-R doc-date-text">
                            {{dataItem.userName || 'N/A'}}
                        </div>
                    </ng-template>
                </kendo-grid-column>
                <kendo-grid-column field="documentCreatedDate" title="Saved Date">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <div class="Body-R doc-date-text">
                            {{ formatDate(dataItem.documentCreatedDate) }}
                        </div>
                    </ng-template>
                </kendo-grid-column>
                 <kendo-grid-column field="documentUploadType" title="Upload Type">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <div class="Body-R doc-date-text">
                            {{dataItem.documentUploadType}}
                        </div>
                    </ng-template>
                </kendo-grid-column>
                
                <!-- Action Column -->
                <kendo-grid-column title="Actions" width="120">
                    <ng-template kendoGridCellTemplate let-dataItem>
                        <div class="action-column-right d-flex align-items-center">
                             <button class="action-btn custom-mr download-btn"
                                    title="Download Document"
                                    (click)="downloadDocument(dataItem)">
                                <img src="assets/dist/images/Download-img.svg" alt="Download" />
                            </button>
                            <button class="action-btn delete-btn mr-2"
                                    [ngClass]="{'disabled': showSelectionPopup}"
                                    id="delete-btn"
                                    title="Delete Document"
                                    [disabled]="showSelectionPopup"
                                    (click)="showDeleteConfirmation(dataItem)">
                                <img src="assets/dist/images/delete-doc.svg" alt="Delete" />
                            </button>
                        </div>
                    </ng-template>
                </kendo-grid-column>
            </kendo-grid>
        </div>
        
        <!-- Floating Selection Popup -->
        <div *ngIf="showSelectionPopup" class="selection-popup">
            <div class="selection-content">
                <div class="selected-count Body-M">{{ selectedDocumentCount }} {{ selectedDocumentCount === 1 ? 'file' : 'files' }} selected:</div>
                <div class="delete-selected-btn Body-R pr-4" [disabled]="isDeleteDisabled" (click)="handleDeleteSelectedClick()" id="delete-selected-btn">
                    Delete Selected Files
                </div>
                <div class="close-selection-btn" (click)="closeSelectionPopup()" id="close-selection-btn">
                    <span><img src="assets/dist/images/close-icon.svg" alt="Close" /></span>
                </div>
            </div>
        </div>
    </div>

    <div class="nodata-container" *ngIf="!showOptions && documents && documents.length === 0">
        <img src="assets/dist/images/no-document-found.svg" alt="No Data" />
    </div>

    <!-- No Data Template for Local Documents -->
    <ng-template #noDataTemplate>
        <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 pb-2 no-content-section text-center">
            <div class="drop-zone d-flex justify-content-center align-items-center flex-column"
                (dragover)="onDragOver($event)" (dragleave)="onDragLeave($event)" (drop)="onDrop($event)"
                [ngClass]="{'active-drop-zone': isDragging}">
                <div><img src="assets/dist/images/plus-header.svg" alt="No Content" class="no-content-image"></div>
                <div class="no-content-text pt-3 pb-2 Body-R">
                    Drag and drop documents or <button class="browse-link" (click)="triggerFileInput()"
                        (keydown.enter)="triggerFileInput()">Browse</button>
                </div>
                <div class="no-content-sub Caption-1 template-text break-word">
                    Supported max File Size is 20MB
                </div>
                <input type="file" id="file-upload" multiple accept=".pdf,.xls,.xlsx" (change)="onFileSelected($event)" style="display: none"
                    #fileInput>
            </div>
        </div>
    </ng-template>
</div>

<confirm-modal *ngIf="showDeletePopup" [modalTitle]="'Delete Confirmation'" [isCloseEnable]="true"
    (closeIconClick)="closeDeletePopup()" [secondaryButtonName]="'No, keep it'" [primaryButtonName]="'Yes, Delete'"
    (primaryButtonEvent)="deleteSelectedDocuments()" (secondaryButtonEvent)="closeDeletePopup()">
    <div class="delete-popup-content">
        <div class="Heading2-M delete-warning">
            Warning : Irreversible Action
        </div>
        <div class="Body-R warning-text">
            {{isMultipleDelete ? multipleDeleteMessage : singleDeleteMessage}}
        </div>
    </div>
</confirm-modal>
<app-loader-component *ngIf="isLoading"></app-loader-component>