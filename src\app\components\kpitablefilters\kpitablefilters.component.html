<form name="form" #form="ngForm" (ngSubmit)="form.form.valid && onSubmit($event)" (click)="$event.stopPropagation()">
    <div class="filter-first">
        <div class="row m-0 mr-0 ml-0 m-sm-0 m-md-0 m-lg-0 m-xl-0" *ngIf="tabname === 'esg'">
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pb-1 pt-3 label-align Caption-M">
                Decimal Count
            </div>
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 pr-0">
                <kendo-combobox id="decimal-count" [clearButton]="false" [(ngModel)]="decimalDigit" #Decimal="ngModel" [fillMode]="'solid'"
                    name="Decimal" class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32"
                    [size]="'medium'" [data]="decimalDigitOptions" [valuePrimitive]="false" textField="unitType"
                    placeholder="Select Decimal" id="select-decimal" (valueChange)="onDecimalSelection($event)" valueField="value">
                </kendo-combobox>

            </div>
        </div>

        <div class="row m-0 ">
            <div class="col-12 pb-1 pt-3 label-align Caption-M">
                Period
            </div>
            <div class="col-12 pl-3 pr-3">
                <kendo-combobox id="date-range-filter" [clearButton]="false" [(ngModel)]="model.periodType" #periodType="ngModel"
                    [fillMode]="'solid'" name="periodType"
                    class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                    [data]="periodTypes" [valuePrimitive]="false" textField="type" placeholder="Select Date Range"
                    (valueChange)="onPeriodChange($event)" valueField="type">
                </kendo-combobox>
                <div *ngIf="form.submitted && periodType.invalid" class="invalid-feedback">
                    <div *ngIf="periodType.errors.required">Date Range required</div>
                </div>
            </div>
        </div>
        <div class="row m-0" *ngIf="model.periodType?.type=='Date Range'">
            <div class="col-12 pb-1 pt-3 label-align Caption-M">
                Date Range
            </div>
            <div class="col-12 pl-3 pr-0" id="date-range-calendar">
                <p-calendar appendTo="body" #myCalendar
                    [style]="{'z-index': '999','left':'auto','width':'260px','border-radius':'4px 0px 0px 4px'}"
                    [styleClass]="'custom-kpi-calendar'" class="tablefilter-dropdrown-width kpi-prefence-filter"
                    [readonlyInput]="true" inputStyleClass="p-custom-calendar date-picker-input" name="startPeriod"
                    #startPeriod [(ngModel)]="model.startPeriod" (onSelect)="onDateSelect($event)" dateFormat="mm/yy"
                    [showIcon]="true" yearRange={{yearRange}} [yearNavigator]="true" view="month"
                    placeholder="Select Date" selectionMode="range" required="{{isdate}}">
                </p-calendar>
            </div>
        </div>
        <div class="row m-0" *ngIf="model.periodType?.type=='Custom'">
            <div class="col-12 pb-1 pt-3 label-align Caption-M">
                From Quarter
            </div>
            <div class="col-12 pl-3 pr-3">
                <kendo-combobox id="from-quarter-filter" [required]="iscustom" [clearButton]="false" [(ngModel)]="model.fromQuarter"
                    #fromQuarter="ngModel" [fillMode]="'solid'" name="fromQuarter"
                    class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                    [data]="quarterOptions" [valuePrimitive]="false" textField="text" placeholder="From Quarter"
                    (valueChange)="fromQuaterevent($event)" valueField="text">
                </kendo-combobox>
                <div *ngIf="fromQuarter.invalid && ( form.submitted)" class="text-danger">
                    <div *ngIf="form.submitted && !fromQuarter.valid" class="text-danger">
                        Select from quarter
                    </div>
                </div>
            </div>
        </div>
        <div class="row m-0" *ngIf="model.periodType?.type=='Custom'">
            <div class="col-12 pb-1 pt-3 label-align Caption-M">
                From Year
            </div>
            <div class="col-12 pl-3 pr-3">
                <kendo-combobox id="from-year-filter" [required]="iscustom" [clearButton]="false" [(ngModel)]="model.fromYear"
                    #fromYear="ngModel" [fillMode]="'solid'" name="fromYear"
                    class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                    [data]="yearOptions" [valuePrimitive]="false" textField="text" placeholder="From Year"
                    (valueChange)="fromYearevent($event)" valueField="text">
                </kendo-combobox>

                <div *ngIf="fromYear.invalid && (form.submitted)" class="text-danger">
                    <div *ngIf="form.submitted && !fromYear.valid" class="text-danger">
                        Select from year
                    </div>
                </div>
            </div>
        </div>
        <div class="row m-0" *ngIf="model.periodType?.type=='Custom'">
            <div class="col-12 pb-1 pt-3 label-align Caption-M">
                To Quarter
            </div>
            <div class="col-12 pl-3 pr-3">
                <kendo-combobox id="to-quarter-filter" [required]="iscustom" [clearButton]="false" [(ngModel)]="model.toQuarter"
                    #toQuarter="ngModel" [fillMode]="'solid'" name="toQuarter"
                    class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                    [data]="quarterOptions" [valuePrimitive]="false" textField="text" placeholder="To Quarter"
                    (valueChange)="toQuaterevent($event)" valueField="text">
                </kendo-combobox>
                <div *ngIf="toQuarter.invalid && (form.submitted)" class="text-danger">
                    <div *ngIf="form.submitted && !toQuarter.valid" class="text-danger">
                        Select to quarter
                    </div>
                </div>
            </div>
        </div>
        <div class="row pb-2 m-0" *ngIf="model.periodType?.type=='Custom'">
            <div class="col-12 pb-1 pt-3 label-align Caption-M">
                To Year
            </div>
            <div class="col-12 pl-3 pr-3">
                <kendo-combobox id="to-year-filter" [required]="iscustom" [clearButton]="false" [(ngModel)]="model.toYear" #toYear="ngModel"
                    [fillMode]="'solid'" name="toYear"
                    class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                    [data]="yearOptions" [valuePrimitive]="false" textField="text" placeholder="To Year"
                    (valueChange)="toYearevent($event)" valueField="text">
                </kendo-combobox>

                <div *ngIf="toYear.invalid && ( form.submitted)" class="text-danger">
                    <div *ngIf="form.submitted && !toYear.valid" class="text-danger">
                        Select to year
                    </div>
                </div>
            </div>
        </div>
        <div class="row mr-0 ml-0">
            <div class="col-12 pl-2 pr-2">
                <div *ngIf="periodErrorMessage!=''" class="display-inline-block text-danger error-msg">
                    {{periodErrorMessage}}</div>
            </div>
        </div>
        <div class="row m-0 " *ngIf="!isFundKpi">
            <div class="col-12 pb-1 pt-3 label-align Caption-M">
                Fx rates
            </div>
            <div class="col-12 pl-3 pr-3">
                <kendo-combobox id="fxrates-filter" [clearButton]="false" [(ngModel)]="model.fxRates" #fxRates="ngModel" [fillMode]="'solid'" name="fxRates"
                    class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="fxRatesList"
                    [valuePrimitive]="false" textField="type" placeholder="Select Fx" valueField="id">
                </kendo-combobox>
            </div>
        </div>
        <div class="row m-0 " *ngIf="!isFundKpi">
            <div class="col-12 pb-1 pt-3 label-align Caption-M">
                Currency
            </div>
            <div class="col-12 pl-3 pr-3">
                <kendo-combobox id="currency-filter" [clearButton]="false" [filterable]="true" [(ngModel)]="model.currencyCode" #currencyCode="ngModel"
                    [fillMode]="'solid'" name="currencyCode" class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32"
                    [size]="'medium'" [data]="filteredCurrencyList" [valuePrimitive]="false" textField="currencyCode"
                    placeholder="Select Currency" (valueChange)="onCurrencyChange($event);" valueField="currencyID"
                    (filterChange)="handleFilter($event)">
                </kendo-combobox>
            </div>
        </div>
        <div class="row m-0 ">
            <div class="col-12 pb-1 pt-3 label-align Caption-M">
                Currency Unit
            </div>
            <div class="col-12 pl-3 pr-3">
                <kendo-combobox id="currency-unit-filter" [clearButton]="false" [(ngModel)]="investmentKpiValueUnit" #Unit="ngModel"
                    [fillMode]="'solid'" name="Unit"
                    class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                    [data]="unitTypeList" [valuePrimitive]="false" textField="unitType" placeholder="Select Unit"
                    (valueChange)="convertUnitType()" valueField="typeId">
                </kendo-combobox>
            </div>
        </div>
        <div class="row m-0 " *ngIf="!isFundKpi">
            <div class="col-12 pb-1 pt-3 label-align pr-0">
                <div class="float-left Body-R spot-label Caption-M">Spot Rate Conversion</div>
                <div class="float-right"> <kendo-switch id="spotrate-switch" size="small" name="isSpotRate" (valueChange)="onSpotRateChange()"  #isSpotRate [(ngModel)]="model.isSpotRate"  [onLabel]="' '" [offLabel]="' '"></kendo-switch></div>
            </div>
        </div>
        <div class="row m-0" *ngIf="model.isSpotRate">
            <div class="col-12 pb-1 pt-3 label-align Caption-M">
                Spot Rate Date
            </div>
            <div class="col-12 pl-3 pr-3">
                <kendo-datepicker id="spotrate-datepicker" calendarType="classic" class="k-picker-custom-solid k-picker-custom-solid-pc k-datepicker-height-32 k-datepicker-width-260"  [format]="format" [fillMode]="'solid'"
                            placeholder="Select Spot Rate Date"
                            id="spotRateDate" name="spotRateDate" [disabledDates]="disabledDates" 
                            [(ngModel)]="model.spotRateDate" (valueChange)="getSpotRate()" [value]="model.isSpotRate ? getFormattedDate(model.spotRateDate == null ? defaultDate : model.spotRateDate) : null"
                              #spotRateDate></kendo-datepicker>
            </div>
            <div class="col-12 pb-1 pt-3 label-align Caption-M error-message" *ngIf="spotRateErrorMessage!=null">
                {{spotRateErrorMessage}}
            </div>
        </div>
    </div>
    <div class="filter-footer pr-3 pb-3 mr-2">
        <div class="d-inline ">
            <button id="preference-reset" type="submit" name="Reset" class="btn btn-reset ">Reset</button>
        </div>
        <div class="d-inline ">
            <button id="preference-apply" type="submit" name="Save" [disabled] ="model.periodType?.type=='Date Range' && (this.model.startPeriod == null || this.model.startPeriod == undefined) || spotRateErrorMessage!=null || !form.valid||periodErrorMessage!=''"
                class="btn btn-light btn-app  pt-0 pb-0">Apply</button>
        </div>
    </div>

</form>