import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GridModule } from '@progress/kendo-angular-grid';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FlatTableModule } from './flat-table.module';
import { FlatTableComponent } from './flat-table.component';
import { EditCellDialogComponent } from './edit-cell-dialog.component';
import { TestBed, ComponentFixture } from '@angular/core/testing';
import {  NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrModule, ToastrService } from 'ngx-toastr';

describe('FlatTableModule', () => {
    let fixture: ComponentFixture<EditCellDialogComponent>;
    let component: EditCellDialogComponent;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
        imports: [
            CommonModule,
            FormsModule,
            ReactiveFormsModule,
            GridModule,
            ToastrModule.forRoot(),
            NgbModule,
            FlatTableModule
        ],providers: [NgbActiveModal, ToastrService]
        }).compileComponents();

        fixture = TestBed.createComponent(EditCellDialogComponent);
        component = fixture.componentInstance;
    });

    it('should create the module', () => {
        const module = TestBed.inject(FlatTableModule);
        expect(module).toBeTruthy();
    });


    it('should declare EditCellDialogComponent', () => {
        expect(component).toBeTruthy();
    });


    it('should dismiss modal on cancel', () => {
        spyOn(component.modal, 'dismiss');
        component.OnCancel();
        expect(component.modal.dismiss).toHaveBeenCalled();
    });

    it('should close modal with new value on save', () => {
        spyOn(component.modal, 'close');
        component.newValue = 'test value';
        component.comments = '';
        component.uploadedFiles = [];
        component.onSave();
        expect(component.modal.close).toHaveBeenCalledWith({
            newValue: 'test value',
            supportingDocument: null,
            comments: ''
        });
    });
});