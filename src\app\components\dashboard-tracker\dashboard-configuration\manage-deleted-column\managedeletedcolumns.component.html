<app-loader-component *ngIf="isLoading"></app-loader-component>
<div class="kendo-container">
  <kendo-grid id="dashboardTrackerTable" class="dashboard-tracker-table" [data]="view | async"
    [scrollable]="'scrollable'" [sortable]="true" [style.min-width.px]="gridColumns.length * 200 + 400"
    [pageSize]="state.take" [skip]="state.skip" [pageable]="{
      buttonCount: 10,
      info: true,
      type: 'numeric',
      pageSizes: [100, 200, 300],
      previousNext: true
    }" (dataStateChange)="dataStateChange($event)">

    <ng-container *ngFor="let col of gridColumns; let i = index">
      <kendo-grid-column *ngIf="col.name === DashboardConfigurationConstants.SerialNo" [name]="col.name"
        [title]="col.name" [width]="200">
        <ng-template kendoGridHeaderTemplate>
          <input kendoCheckBox type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md custom-border">
          <span class="Body-R text-truncate header-title ml-3">S.No.</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          {{ (state.skip || 0) + rowIndex + 1 }}
        </ng-template>
      </kendo-grid-column>
      <!-- All other columns -->
      <kendo-grid-column *ngIf="col.name !== DashboardConfigurationConstants.SerialNo" [name]="col.name" [title]="col.name" [width]="200">
        <ng-template kendoGridHeaderTemplate>
          <input id="{{col.name}}-{{i}}" kendoCheckBox type="checkbox"
            class="k-checkbox k-checkbox-md k-rounded-md custom-border" [checked]="col.selected"
            (click)="onColumnSelectionClick(i)">
          <span class="Body-R text-truncate header-title ml-3">{{ col.name }}</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
        
            <ng-container *ngIf="!col.isDropDown && col.mapTo">
              {{ dataItem[col.name] || '' }}
            </ng-container>
            <!-- Dropdown for columns with isDropDown -->
            <ng-container *ngIf="col.isDropDown">
              <div class="dropdown-input">
                <kendo-combobox id="dropdown-input-{{ i }}" class="k-custom-solid-dropdown k-dropdown-height-32"
                  [data]="col.dropDownValues" valueField="value" textField="displayText" [clearButton]="false"
                  [rounded]="'medium'" [fillMode]="'solid'" placeholder="Select Here" [valuePrimitive]="true"
                  [ngModel]="dataItem[col.name]">
                </kendo-combobox>
              </div>
            </ng-container>

            <!-- Textbox for other columns -->
            <ng-container *ngIf="!col.isDropDown && !col.mapTo">
              <kendo-textbox fillMode="flat" [placeholder]="col.dataType === 3 ? defaultDateFormat : ''"
                [value]="dataItem[col.name] || ''" [showSuccessIcon]="false"
                [showErrorIcon]="hasValidationError(dataItem, col)" [valid]="!hasValidationError(dataItem, col)"
                [ngClass]="{'validation-error': hasValidationError(dataItem, col)}"
                (valueChange)="onTextboxValueChange($event, dataItem, col)">
                <ng-template kendoTextBoxSuffixTemplate>
                  <button kendoButton fillMode="clear" (click)="clearTextboxValue(dataItem, col)">
                    <img src="assets/dist/images/close-clear.svg" alt="Clear">
                  </button>
                </ng-template>
              </kendo-textbox>
            </ng-container>

        </ng-template>
      </kendo-grid-column>
    </ng-container>
    <!-- default screen if grid data is empty -->
    <ng-template kendoGridNoRecordsTemplate>
      <div class="text-center py-5 mt-5" *ngIf="totalRecords === 0">
        <img src="assets/dist/images/Illustrations.svg" alt="No data" class="mb-3" />
        <p class="mb-0 Body-R content-secondary">No Data Found</p>
      </div>
    </ng-template>
  </kendo-grid>
</div>