<div class="align-items-start">
    <div class="ui-widget-header ui-bb ui-wed-bb border-bottom">
        <div class="float-right plus-btn">
            <a title="Add">
                <img id="track-record-Model" class="tc-add" (click)="open(fundTrackRecordModel)" title="Add"
                    src="assets/dist/images/plus.svg" /></a>
        </div>
        <div class="row mr-0 ml-0">
            <div class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                <div class="float-left">
                    <div class="allvalues-kpis TextTruncate"
                        title="All values in: {{model?.currencyDetail?.currencyCode}}">All values in:
                        {{model?.currencyDetail?.currencyCode}}</div>
                </div>
                <div class="float-right">
                    <div class="d-inline-block search">
                        <span class="fa fa-search fasearchicon p-1"></span>
                        <input #gbTrackRecord (input)="searchGrid($event.target.value)" [(ngModel)]="globalFilter"
                            type="text" pInputText class="search-text-company companyListSearchHeight TextTruncate"
                            placeholder="Search" [(ngModel)]="globalFilter">
                    </div>
                    <div class="d-inline-block">
                        <img id="track-record-fund-holdings" class="p-action-padding download-excel" title="Export  (Excel file)"
                            src="assets/dist/images/Cloud-download.svg" (click)="exportFundHoldingValues()" />
                        <span class="excel-load" *ngIf="isExportLoading"><i aria-hidden="true"
                                class="fa fa-circle-o-notch fa-1x fa-fw"></i>
                        </span>
                    </div>
                    <div class="d-inline">
                        <span class="col-divider">
                        </span>
                    </div>
                    <div class="d-inline-block pl-2 pref-icon"><img id="dropdownMenuButton" [matMenuTriggerFor]="menu"
                            src="assets/dist/images/ConfigurationWhite.svg" class="cursor-filter" alt=""
                            #tRecordTrigger="matMenuTrigger" /> </div>
                </div>
            </div>
        </div>
    </div>
    <div class="datatable-container">
        <kendo-grid [kendoGridBinding]="fundTrackRecords"  scrollable="virtual" [rowHeight]="44"
            [resizable]="true"
            class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cab-table-grid kendo-fund-tr-grid">
            <kendo-grid-column [sticky]="true" [width]="300"  title="Quarter Year"
                *ngFor="let col of frozenFundTrackTableColumns;">
                <ng-template kendoGridHeaderTemplate>
                    <span class="TextTruncate S-M">{{col.header}}</span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-fundTrackRecord>
                    <a id="track-record-details" class="click-view" (click)="openFunHoldingDetailForQuarter(fundTrackRecord);globalFilterTrack = '';"
                        title="View Details">{{
                        fundTrackRecord.Quarter +' '+fundTrackRecord.Year }}</a>
                </ng-template>
            </kendo-grid-column>
            <kendo-grid-column title="{{col.displayName}}" *ngFor="let col of funddynamicCoulmns;" [width]="200">
                <ng-template kendoGridHeaderTemplate>
                    <span class="TextTruncate S-M">{{col.displayName}}</span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-fundTrackRecord>
                    <div class="TextTruncate" *ngIf="col.header!=FundTrackRecordStatic.Quarter &&!last"
                        [ngClass]="(col.name==FundTrackRecordStatic.Quarter) ? 'table-data-left' : 'table-data-right'">
                        {{
                        col.name==FundTrackRecordStatic.TotalNumberOfInvestments?(fundTrackRecord[col.displayName]!="NA"?(fundTrackRecord[col.displayName]|number
                        : NumberDecimalConst.noDecimal):fundTrackRecord[col.displayName]):
                        col.name==FundTrackRecordStatic.RealizedInvestments?(fundTrackRecord[col.displayName]
                        !="NA"?(fundTrackRecord[col.displayName]|number :
                        NumberDecimalConst.noDecimal):fundTrackRecord[col.displayName]):
                        col.name==FundTrackRecordStatic.UnRealizedInvestments?(fundTrackRecord[col.displayName]
                        !="NA"?(fundTrackRecord[col.displayName]|number :
                        NumberDecimalConst.noDecimal):fundTrackRecord[col.displayName]):
                        col.name==FundTrackRecordStatic.TotalInvestedCost?(fundTrackRecord[col.displayName]
                        !="NA"?(fundTrackRecord[col.displayName]|number :
                        NumberDecimalConst.currencyDecimal):fundTrackRecord[col.displayName]):
                        col.name==FundTrackRecordStatic.TotalRealizedValue?(fundTrackRecord[col.displayName]
                        !="NA"?(fundTrackRecord[col.displayName]|number :
                        NumberDecimalConst.currencyDecimal):fundTrackRecord[col.displayName]):
                        col.name==FundTrackRecordStatic.TotalUnRealizedValue?(fundTrackRecord[col.displayName]
                        !="NA"?(fundTrackRecord[col.displayName]|number : NumberDecimalConst.currencyDecimal
                        ):fundTrackRecord[col.displayName]):
                        col.name==FundTrackRecordStatic.TotalValue?(fundTrackRecord[col.displayName]
                        !="NA"?(fundTrackRecord[col.displayName]|number :
                        NumberDecimalConst.currencyDecimal):fundTrackRecord[col.displayName]):
                        col.name==FundTrackRecordStatic.Dpi?(fundTrackRecord[col.displayName]
                        !="NA"?(fundTrackRecord[col.displayName]|number : NumberDecimalConst.multipleDecimal
                        )+"x":fundTrackRecord[col.displayName]):
                        col.name==FundTrackRecordStatic.Rvpi?(fundTrackRecord[col.displayName]
                        !="NA"?(fundTrackRecord[col.displayName]|number : NumberDecimalConst.multipleDecimal
                        )+"x":fundTrackRecord[col.displayName]):
                        col.name==FundTrackRecordStatic.GrossMultiple?(fundTrackRecord[col.displayName]
                        !="NA"?(fundTrackRecord[col.displayName]|number :
                        NumberDecimalConst.multipleDecimal)+"x":fundTrackRecord[col.displayName]):
                        col.name==FundTrackRecordStatic.NetMultiple?(fundTrackRecord[col.displayName]
                        !="NA"?(fundTrackRecord[col.displayName]|number :
                        NumberDecimalConst.multipleDecimal)+"x":fundTrackRecord[col.displayName]):
                        col.name==FundTrackRecordStatic.GrossIRR?(fundTrackRecord[col.displayName]
                        !="NA"?(fundTrackRecord[col.displayName]|number :
                        NumberDecimalConst.percentDecimal)+"%":fundTrackRecord[col.displayName]):
                        col.name==FundTrackRecordStatic.NetIRR?(fundTrackRecord[col.displayName]
                        !="NA"?(fundTrackRecord[col.displayName]|number :
                        NumberDecimalConst.percentDecimal)+"%":fundTrackRecord[col.displayName]):
                        col.dataType != Mdatatypes.Date?(
                        col.dataType==Mdatatypes.Multiple?(fundTrackRecord[col.displayName]!="NA"?(
                        fundTrackRecord[col.displayName] | number :
                        NumberDecimalConst.multipleDecimal)+"x":fundTrackRecord[col.displayName]):
                        col.dataType==Mdatatypes.Percentage?(fundTrackRecord[col.displayName]!="NA"?(
                        fundTrackRecord[col.displayName] | number :
                        NumberDecimalConst.percentDecimal)+"%":fundTrackRecord[col.displayName]):
                        col.dataType==Mdatatypes.CurrencyValue?(fundTrackRecord[col.displayName]!="NA"?(
                        fundTrackRecord[col.displayName] | number :
                        NumberDecimalConst.currencyDecimal):fundTrackRecord[col.displayName]):
                        col.dataType==Mdatatypes.Number?(fundTrackRecord[col.displayName]!="NA"?(
                        fundTrackRecord[col.displayName] | number :
                        NumberDecimalConst.noDecimal):fundTrackRecord[col.displayName]):fundTrackRecord[col.displayName]
                        ):
                        fundTrackRecord[col.displayName]!="NA"? (fundTrackRecord[col.displayName]| date:'MM/dd/yyyy' ):
                        fundTrackRecord[col.displayName]
                        }}
                    </div>
                </ng-template>
            </kendo-grid-column>
            <kendo-grid-column [width]="200" field="action" title="Action">
                <ng-template kendoGridHeaderTemplate>
                    <span class="TextTruncate S-M">Action</span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-fundTrackRecord>
                    <div class="text-center">
                        <a id="track-record-funds" (click)="open(fundTrackRecord)" class="text-center">
                            <kendo-svg-icon [icon]="pencilIcon"></kendo-svg-icon>
                        </a>
                    </div>
                </ng-template>
            </kendo-grid-column>
            <ng-template kendoGridNoRecordsTemplate>
                <app-empty-state class="finacials-beta-empty-state"
                    [isGraphImage]="false"></app-empty-state>
            </ng-template>
        </kendo-grid>
    </div>
</div>

<div *ngIf="displayCompanyFundHoldingsDialog" class="nep-modal nep-modal-show custom-modal" style="display: block; background: rgba(0, 0, 0, 0.25);">
    <div class="nep-modal-mask"></div>
    <div [style.width]="fullViewWidth" class="nep-card nep-card-shadow nep-modal-panel nep-modal-default" style="position: relative; display: inline-flex; top: 30%;width: 80rem;">
        <div class="nep-card-header nep-modal-title">
            <div class="float-left TextTruncate">{{headerText}}</div>
            <div id="track-record-hide-modal" class="float-right" (click)="hideModal()">
                <div class="close-icon"><i  class="pi pi-times"></i></div>
            </div>
        </div>
        <div class="nep-card-body">    
                <div class="model-custom-padding">
                    <div class="card">
                        <div class="row mr-0 ml-0 header-tr-bg">
                            <div class="col-12 pl-3 pr-0">
                                <div class="float-left pt-2 pb-2">
                                    <span class="tr-popup-currency">All values in:
                                        {{model?.currencyDetail?.currencyCode}} ({{trackRecordValueUnit?.unitType}})</span>
                
                                </div>
                                <div class="float-right search">
                                    <span class="fa fa-search"></span> <input (input)="searchDealTr($event.target.value)"type="text" pInputText placeholder="Search"
                                        class="form-control search-box search-text-company companyListSearchHeight TextTruncate search-tr-popup mt-0"
                                        [(ngModel)]="globalFilterTrack">
                                </div>
                            </div>
                        </div>
                        <div class="card-body mb-0">
                            <div class="align-items-start">
                                <div class="ui-widget-header ui-track-bb ui-wed-bb border-bottom">
                                </div>
                                <kendo-grid [kendoGridBinding]="portfolioCompanyFundHolding" [sortable]="true" scrollable="virtual"
                                    [rowHeight]="44" [resizable]="true"
                                    class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cab-table-grid kendo-fund-tr-grid">
                                    <kendo-grid-column [sticky]="true" [width]="300" field="companyName" title="Portfolio Company">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="TextTruncate S-M">Portfolio Company</span>
                                        </ng-template>
                                        <ng-template kendoGridCellTemplate let-portfolioCompanyFundHolding>
                                            {{portfolioCompanyFundHolding['companyName']}}
                                        </ng-template>
                                    </kendo-grid-column>
                                    <kendo-grid-column *ngIf="frozenPeriodColumn!=null" [sticky]="true" [width]="200" [field]="frozenPeriodColumn?.name" title="frozenPeriodColumn?.displayName">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="TextTruncate S-M"> {{frozenPeriodColumn?.displayName}}</span>
                                        </ng-template>
                                        <ng-template kendoGridCellTemplate let-portfolioCompanyFundHolding>
                                            {{portfolioCompanyFundHolding[frozenPeriodColumn?.name]}}
                                        </ng-template>
                                    </kendo-grid-column>
                                    <kendo-grid-column  [width]="200" [field]="col?.displayName" title="col?.displayName" *ngFor="let col of portfolioCompanyFundHoldingColumns;">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="TextTruncate S-M"> {{col?.displayName}}</span>
                                        </ng-template>
                                        <ng-template kendoGridCellTemplate let-portfolioCompanyFundHolding>
                                            <div class="TextTruncate"
                                            [ngClass]="(col.name==DealTrackRecordInfo.TotalValue||col.name==DealTrackRecordInfo.Dpi||col.name==DealTrackRecordInfo.Rvpi||col.name==DealTrackRecordInfo.GrossMultiple||col.name==DealTrackRecordInfo.GrossIRR) ? ' table-data-right higlighted-cell' : 'table-data-right'"
                                            [ngClass]="(col.name==DealTrackRecordInfo.Quarter) ? '' : 'table-data-right'">                             
                                        {{
                                            (col.name==DealTrackRecordInfo.InvestmentCost)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":portfolioCompanyFundHolding[col.displayName]|
                                            number : NumberDecimalConst.currencyDecimal ):
                                            (col.name==DealTrackRecordInfo.RealizedValue)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":portfolioCompanyFundHolding[col.displayName]|
                                            number : NumberDecimalConst.currencyDecimal):
                                            (col.name==DealTrackRecordInfo.UnrealizedValue)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":portfolioCompanyFundHolding[col.displayName]|
                                            number : NumberDecimalConst.currencyDecimal):
                                            (col.name==DealTrackRecordInfo.TotalValue)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":portfolioCompanyFundHolding[col.displayName]|
                                            number : NumberDecimalConst.currencyDecimal):
                                            (col.name==DealTrackRecordInfo.Dpi)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":(portfolioCompanyFundHolding[col.displayName]|
                                            number : NumberDecimalConst.multipleDecimal )+"x"):
                                            (col.name==DealTrackRecordInfo.Rvpi)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":(portfolioCompanyFundHolding[col.displayName]|
                                            number : NumberDecimalConst.multipleDecimal )+"x"):
                                            (col.name==DealTrackRecordInfo.GrossMultiple)?((portfolioCompanyFundHolding[col.displayName]=="NA")?"NA":(portfolioCompanyFundHolding[col.displayName]|
                                            number:NumberDecimalConst.multipleDecimal)+"x"):
                                            (col.name==DealTrackRecordInfo.GrossIRR)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":(portfolioCompanyFundHolding[col.displayName]|
                                            number : NumberDecimalConst.currencyPercentMultiple)+"%"):
                                            col.dataType != Mdatatypes.Date?(
                                                col.dataType==Mdatatypes.Multiple?(portfolioCompanyFundHolding[col.displayName]!="NA"?( portfolioCompanyFundHolding[col.displayName] | number:NumberDecimalConst.multipleDecimal)+"x":portfolioCompanyFundHolding[col.displayName]):
                                                col.dataType==Mdatatypes.Percentage?(portfolioCompanyFundHolding[col.displayName]!="NA"?( portfolioCompanyFundHolding[col.displayName] | number:NumberDecimalConst.percentDecimal)+"%":portfolioCompanyFundHolding[col.displayName]):
                                                col.dataType==Mdatatypes.CurrencyValue?(portfolioCompanyFundHolding[col.displayName]!="NA"?( portfolioCompanyFundHolding[col.displayName] | number : NumberDecimalConst.currencyDecimal):portfolioCompanyFundHolding[col.displayName]):
                                                col.dataType==Mdatatypes.Number?(portfolioCompanyFundHolding[col.displayName]!="NA"?( portfolioCompanyFundHolding[col.displayName] | number : NumberDecimalConst.noDecimal):portfolioCompanyFundHolding[col.displayName]):portfolioCompanyFundHolding[col.displayName]
                                            ):
                                            portfolioCompanyFundHolding[col.displayName]!="NA"?
                                            (portfolioCompanyFundHolding[col.displayName]| date:'MM/dd/yyyy' ):
                                            portfolioCompanyFundHolding[col.displayName]
                                            }}  
                                                              
                                        </div>
                                        </ng-template>
                                    </kendo-grid-column>
                                    <ng-template kendoGridNoRecordsTemplate>
                                        <app-empty-state class="finacials-beta-empty-state"
                                            [isGraphImage]="false"></app-empty-state>
                                    </ng-template>
                                </kendo-grid>
                            </div>
                        </div>
                    </div>
                </div>
            
        </div>
    </div>
</div>

<mat-menu #menu="matMenu" [hasBackdrop]="true" class="fixed-menu">
    <div id="track-record-value-unit" class="filter-first" (click)="$event.stopPropagation()" (keydown)="$event.stopPropagation()">
        <div class="row m-0 ">
            <div class="col-12 pb-1 pt-3 label-align">
                Values in:
            </div>
            <div id="track-record-value-record-change" class="col-12 pl-3 pr-3">
                    <kendo-combobox id="track-record-value-change" [clearButton]="false" [(ngModel)]="trackRecordValueUnit" #unit="ngModel" [fillMode]="'solid'"
                        name="Unit" class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                        [data]="unitTypeList" [filterable]="true" [valuePrimitive]="false" textField="unitType" placeholder="Select Unit"
                        (valueChange)="convertTrackRecordValueUnits()" valueField="typeId">
                    </kendo-combobox>
            </div>
        </div>

    </div>
</mat-menu>