import { AddCloComponent } from './add-clo-list/add-clo-list.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { AngularResizeEventModule } from 'angular-resize-event';
import { QuillModule } from 'ngx-quill';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { CloReviewFormComponent } from './clo-review-form/clo-review-form.component';


@NgModule({
  declarations: [
    AddCloComponent,
    CloReviewFormComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MaterialModule,
    PrimeNgModule,
    AngularResizeEventModule,
    QuillModule,
    SharedDirectiveModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule.forChild([
      { path: '', component: AddCloComponent }
  ]),
  KendoModule
],

})
export class AddCloComponentModule { }