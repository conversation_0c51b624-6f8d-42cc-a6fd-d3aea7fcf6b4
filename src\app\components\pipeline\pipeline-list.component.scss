@import "../../../variables";
.tab-shadow {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000014;
    opacity: 1;
    margin-bottom: 20px;
    margin-top: -18.8px;
    margin-left: -20px;
    margin-right: -20px;
    padding-top: 12px;
    padding-left: 5px;
}

.search-text-company {
    font-size: 12px !important;
}

.add-user-component {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #DEDFE0;
    opacity: 1;
    border-radius: 4px 4px 0px 0px;
    box-shadow: 0px 0px 12px #00000014 !important;
}

.card-header {
    background: $nep-white 0% 0% no-repeat padding-box !important;
    border-bottom: 1px solid #DEDFE0;
}

.card-header-main {
    font-size: 14px !important;
}

.card-body {
    margin-bottom: 0px !important;
}

.sectorHeadQuarter {
    background: $nep-white 0% 0% no-repeat padding-box;
    border: 1px solid $nep-divider;
    border-radius: 4px;
    opacity: 1;
    padding: 4px 12px;
    &:hover {
        border: 1px solid $nep-divider;
        .company-buttons {
            display: block;
        }
    }
}

.download-excel {
    cursor: pointer;
}

.pipeline-header {
    padding: 0px 16px;
    .pipe-title {
        padding: 10px 0px;
        font-family: "Helvetica Neue LT W05_65 Medium",Arial, Verdana, Tahoma,sans-serif !important;
        font-weight: initial !important;
        font-size: 14px !important;
    }
    .p-action-padding {
        padding: 0px 8px 0px 12px;
    }
    .p-add-padding {
        padding: 0px 0px 0px 8px;
    }
    .col-divider {
        border-right: 1px solid #DEDFE0;
    }
    .search-text-company {
        height: 40px !important;
    }
}