﻿<div class="col-12 pl-0 pr-0 attribution-report">
    <div class="row attribution-report-header  mr-0 ml-0">
        <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 horizontal-parent">
            <div class="horizontal-child1 float-left menu-box-shadow" [ngClass]="enableLeft?'header-left-arrow':''">
                <a id="clientReportingArrowLeft" class="btn pt-2" (click)="moveLeft()"
                    (mouseleave)="stopLeftScrolling()">
                    <i class="fa fa-chevron-left arrow-style" aria-hidden="true"></i>
                </a>
            </div>
            <div class="horizontal-child2 float-right menu-box-shadow" [ngClass]="enableRight?'header-right-arrow':''">
                <a id="clientReportingArrowRight" class="btn pt-2" (click)="moveRight()"
                    (mouseleave)="stopRightScrolling()">
                    <i class="fa fa-chevron-right arrow-style" aria-hidden="true"></i>
                </a>
            </div>
            <div #panel class="horizontal-menu menu-box-shadow">
                <div class="client-report-tabs">
                    <ng-container *ngFor="let item of adhocDataScroll">
                        <div class="client-report-tab" *ngIf="selectedPeriodType.label === 'Monthly'"
                            [class.active]="item.MonthYear === defaultSelectedTab"
                            [ngStyle]="{'padding': '0px !important'}" id="{{item.MonthYear}}" (click)="changeTabType(item)">
                            {{item.MonthYear}}
                        </div>
                        <div class="client-report-tab" *ngIf="selectedPeriodType.label === 'Yearly'"
                            [class.active]="item.Year === defaultSelectedTab" [ngStyle]="{'padding': '0px !important'}"
                            id="{{item.Year}}" (click)="changeTabType(item)">
                            {{item.Year}}
                        </div>
                        <div class="client-report-tab" *ngIf="selectedPeriodType.label === 'Quarterly'"
                            [class.active]="item.Quarter === defaultSelectedTab"
                            [ngStyle]="{'padding': '0px !important'}" id="{{item.Quarter}}" (click)="changeTabType(item)">
                            {{item.Quarter}}
                        </div>
                    </ng-container>
                </div>
            </div>
        </div>
    </div>
</div>