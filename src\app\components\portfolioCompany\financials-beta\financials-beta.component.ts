import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, ViewChild } from '@angular/core';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import {ErrorMessage, FinancialValueUnitsEnum, FxRates, MiscellaneousService, OrderTypesEnum, PeriodTypeEnum, PeriodType } from 'src/app/services/miscellaneous.service';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { ActivatedRoute } from "@angular/router";
import { CurrencyService } from 'src/app/services/currency.service';
import { MatMenu,MatMenuTrigger } from '@angular/material/menu';
import { Subject, Observable, Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { KpiConfig, MSubFields, FinancialTypes } from 'src/app/components/portfolioCompany/models/Kpi-Configuartion.model';
import { KpiTypes, PeriodTypeFilterOptions, FinancialsValueTypes, GlobalConstants } from "src/app/common/constants";
import { DropDownFilterSettings} from '@progress/kendo-angular-dropdowns';
import { KPIModulesEnum } from "src/app/services/permission.service";
import { ToastrService } from 'ngx-toastr';
import { FormatSettings, PopupSettings } from "@progress/kendo-angular-dateinputs";
import { SpotRateModel } from './models/spot-rate.model';
import { DatePipe } from '@angular/common';
@Component({
  selector: "app-financials-beta",
  templateUrl: "./financials-beta.component.html",
  styleUrls: ["./financials-beta.component.scss"],
})
/**
 * Represents a component for displaying financials data for a portfolio company.
 * This component handles the display and interaction logic for financials data.
 */
export class FinancialsBetaComponent implements OnInit, AfterViewInit {
  currencyModel: any = {  
    defaultUnit:null,
    convertedUnit:null,
    currencyCode:null,
    spotRate:null
  };
  isApply:boolean = false;
  spotRate:number = null;
  isSpotRateApply:boolean = false;
  spotRateErrorMessage: string = null;
  originalCurrencyList:any[] = [];
  minDay: Date = new Date("2010-01-01");
  defaultDate: Date; // Set the default date to today
  public format: FormatSettings = {
    displayFormat: "MM/dd/yyyy",
    inputFormat: "MM/dd/yyyy",
  };
  filteredCurrencyList: any[] = [];
  selectedUnit = null;
  public virtual: any = {
    itemHeight: 32,
  };
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: "contains",
  };
  @ViewChild("menutabs") el: ElementRef;
  @Input() masterModel: any = {};
  @Input() pcCompanyId: any = 0;
  @Input() reportingCurrencyCode: any = "";
  @Input() companyName: any = "";
  @Input() subHeaderName: any = "";
  tabName: string = "";
  tabList: ITab[] = [];
  id: any;
  portfolioCompanyID: any;
  selectedCurrencyCode = "";
  model: any = {
    isAscending: false,
    portfolioCompany: {
      portfolioCompanyID: 0,
      companyName: null,
      companyCurrency: null,
    },
    subTabName: "Actual",
    selectionChange: null,
    spotRateDate: null,
    isSpotRate: false,
    spotRate:null
  };
  @ViewChild("menu") uiuxMenu!: MatMenu;
  @ViewChild("filterMenuTrigger") menuTrigger: MatMenuTrigger;
  @Input() selectedtab: string;
  selectedtabclone: string;
  @ViewChild("periodList") periodList;
  @ViewChild("myCalendar") datePicker;
  @ViewChild("currencyUnit") currencyUnittag;
  isSection: string = "financial";
  isChildtab: boolean = true;
  isLogs: boolean = false;
  isDownload: boolean = false;
  ErrorNotation: boolean = false;
  isReportingCurrency: boolean = true;
  periodTypes: any;
  orderType: any;
  decimalType: any;
  today: Date;
  yearRange: any;
  minDate: Date | null = null;
  dateRange: any[];
  en: any;
  isCustom: boolean = false;
  isdate: boolean = false;
  yearOptions: any = [];
  CurrencyList: any[] = [];
  periodErrorMessage: any = "";
  quarterOptions: any = [
    { value: "Q1", text: "Q1", number: 1 },
    { value: "Q2", text: "Q2", number: 2 },
    { value: "Q3", text: "Q3", number: 3 },
    { value: "Q4", text: "Q4", number: 4 },
  ];
  fxRatesList = [
    {
      id: FxRates.SystemAPI,
      type: FxRates[FxRates.SystemAPI],
    },
    {
      id: FxRates.BulkUpload,
      type: FxRates[FxRates.BulkUpload],
    },
  ];
  unitTypeList = [
    {
      typeId: FinancialValueUnitsEnum.Absolute,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Absolute],
    },
    {
      typeId: FinancialValueUnitsEnum.Thousands,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Thousands],
    },
    {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    },
    {
      typeId: FinancialValueUnitsEnum.Billions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Billions],
    },
  ];
  KpiValueUnit: { typeId: FinancialValueUnitsEnum; unitType: string };
  tabValueTypeList: FinancialTypes[];
  selectedPeriodType: number = PeriodType.Monthly;
  isMonthly: boolean = true;
  isQuarterly: boolean = false;
  isAnnually: boolean = false;
  isSubmit: boolean = false;
  @Input() pageConfigData: KpiConfig[];
  selectedPeriodTypeConfiguration: KpiConfig;
  subSectionFields: MSubFields[];
  hasMonthly: boolean = false;
  hasQuarterly: boolean = false;
  hasAnnually: boolean = false;
  isPageLoad: boolean = true;
  loadData: boolean = false;
  tabValueTypeListCopy: FinancialTypes[];
  isYtd: boolean = false;
  isLtm: boolean = false;
  hasYtd: boolean = false;
  hasLtm: boolean = false;
  valueTypeString: string;
  isYtdPageLoad: boolean = true;
  isLtmPageLoad: boolean = true;
  @Input() financialsPermission: any = [];
  profitLossPermission: any = [];
  balanceSheetPermission: any = [];
  cashFlowPermission: any = [];
  kpiModulesEnum: typeof KPIModulesEnum = KPIModulesEnum;
  plViewPermission: boolean = false;
  bsViewPermission: boolean = false;
  cfViewPermission: boolean = false;
  showDownloadIcon: boolean = false;
  isValueConverted: boolean = false;
  auditLogErrorForConvertedValue: string = GlobalConstants.AuditLogErrorForConvertedValue;
  auditLogTitle: string = GlobalConstants.AuditLogTitle;
  sectionId: number = 0;

  constructor(
    private datePipe: DatePipe,
    private miscService: MiscellaneousService,
    private currencyService: CurrencyService,
    private portfolioCompanyService: PortfolioCompanyService,
    protected changeDetectorRef: ChangeDetectorRef,
    private _avRoute: ActivatedRoute,
    public toasterService: ToastrService
  ) {
    if (this._avRoute.snapshot.params["id"]) {
      this.id = this._avRoute.snapshot.params["id"];
    }
    this.setDefaultModelValue();
    // Set the default date to one day before today
    this.defaultDate = new Date(
      new Date().getFullYear(),
      new Date().getMonth(),
      new Date().getDate() - 1
    );
    if (
      this.model.SpotRateDate == null ||
      this.model.SpotRateDate == undefined
    ) {
      this.model.SpotRateDate = this.defaultDate;
    }
  }
  getValueTypeTabList() {
    this.portfolioCompanyService.getfinancialsvalueTypes().subscribe((x) => {
      this.tabValueTypeListCopy = x.body?.financialTypesModelList;
      this.setValueTypesList(x.body?.financialTypesModelList);
    });
    delete this.model["currencyCode"];
    window.addEventListener("scroll", this.scrollEvent, true);
  }
  getFormattedDate(date: any) {
    return date == "NA" || date == undefined || date == null
      ? ""
      : new Date(date);
  }
  public disabledDates = (date: Date): boolean => {
    return date < this.minDay || date > new Date();
  };
  /**
   * Sets the value types list for the financials-beta component.
   *
   * @param valueTypesList The list of FinancialTypes to set.
   */
  setValueTypesList(valueTypesList: FinancialTypes[]) {
    const tabName = this.tabName;
    this.showDownloadIcon = this.financialsPermission.some(
      (x) => x.subFeature == tabName && x.canExport
    );
    const pageConfigs = this.pageConfigData;
    const kpiTypeMap = {
      [KpiTypes.CashFlow.name]: KpiTypes.CashFlow.type,
      [KpiTypes.BalanceSheet.name]: KpiTypes.BalanceSheet.type,
      default: KpiTypes.ProfitLoss.type,
    };
    let filteredValueTypes = valueTypesList.filter((tab: { name: string }) => {
      const kpiType = kpiTypeMap[tabName] || kpiTypeMap.default;
      const data = pageConfigs.find(
        (config: { kpiType: string }) => config.kpiType === kpiType
      )?.kpiConfigurationData;
      return data?.some((x: { aliasName: string }) =>
        x.aliasName.includes(tab.name)
      );
    });
    filteredValueTypes = this.tabListUpdatePageConfigAliasName(
      kpiTypeMap[tabName] || kpiTypeMap.default,
      filteredValueTypes
    );
    this.tabValueTypeList = filteredValueTypes;
    if (this.tabValueTypeList.length > 0) {
      this.tabValueTypeList[0].active = true;
      this.model.subTabName = this.tabValueTypeList[0].name;
    }
    this.setDataPeriodTypesPageConfigValues();
  }
  tabListUpdatePageConfigAliasName(kpiType: string, filteredValueTypes: any[]) {
    return filteredValueTypes.filter((tab) => {
      const config = this.pageConfigData.find(
        (config) => config.kpiType === kpiType
      )?.kpiConfigurationData;
      if (!config) return false;
      const matchingConfig = config.find((x) => x.aliasName === tab.name);
      if (matchingConfig) tab.alias = matchingConfig?.subFieldAliasName;
      return true;
    });
  }

  setDataPeriodTypesPageConfigValues(): void {
    const financialsConfig = this.getFinancialConfig();
    let periodType = this.tabValueTypeList.find((x) => x.active)?.name;
    periodType = this.setLtmYtdPeriodType(financialsConfig, periodType);
    const kpiConfigurationData = financialsConfig?.kpiConfigurationData ?? [];
    let selectedPeriodTypeConfiguration = kpiConfigurationData.find(
      (x) => x.aliasName === periodType
    );
    ({ selectedPeriodTypeConfiguration, periodType } = this.processPageLoadView(
      selectedPeriodTypeConfiguration,
      periodType,
      kpiConfigurationData
    ));
    const chartValue = selectedPeriodTypeConfiguration?.chartValue ?? [];
    const { Monthly, Quarterly, Annual } = PeriodTypeFilterOptions;
    const hasMonthly = chartValue.some((x) => x === Monthly);
    const hasQuarterly = chartValue.some((x) => x === Quarterly);
    const hasAnnually = chartValue.some((x) => x === Annual);
    this.subSectionFields = kpiConfigurationData;
    this.sectionId = this.subSectionFields.find((x) => x.aliasName === periodType)?.sectionID ?? 0;
    this.hasMonthly = hasMonthly;
    this.hasQuarterly = hasQuarterly;
    this.hasAnnually = hasAnnually;
    this.selectedPeriodTypeConfiguration = financialsConfig;
    this.loadData = hasMonthly || hasQuarterly || hasAnnually;
    if (
      this.isPageLoad ||
      !(
        (this.hasMonthly && this.isMonthly) ||
        (this.hasQuarterly && this.isQuarterly) ||
        (this.hasAnnually && this.isAnnually)
      )
    ) {
      this.setDataPeriodTypes();
    }
  }

  private setLtmYtdPeriodType(financialsConfig: KpiConfig, periodType: string) {
    let ltmType = periodType + " " + FinancialsValueTypes.LTM;
    let ytdType = periodType + " " + FinancialsValueTypes.YTD;
    let ltmRes = financialsConfig?.kpiConfigurationData?.filter(
      (x) => x.aliasName === ltmType
    );
    let ytdRes = financialsConfig?.kpiConfigurationData?.filter(
      (x) => x.aliasName === ytdType
    );
    this.hasLtm = ltmRes?.length > 0;
    this.hasYtd = ytdRes?.length > 0;
    if (this.hasLtm || this.hasYtd) periodType = this.GetPeriodType(periodType);
    return periodType;
  }

  private processPageLoadView(
    selectedPeriodTypeConfiguration: MSubFields,
    periodType: string,
    kpiConfigurationData: MSubFields[]
  ) {
    if (selectedPeriodTypeConfiguration == undefined) {
      if (this.hasYtd && !this.isYtd) {
        if (!this.hasLtm || (this.hasLtm && !this.isLtm)) {
          this.isYtd = true;
          this.isLtm = false;
        }
      } else if (this.hasLtm && !this.isLtm) {
        if (!this.hasYtd || (this.hasYtd && !this.isYtd)) {
          this.isLtm = true;
          this.isYtd = false;
        }
      }
      periodType = this.GetPeriodType(periodType);
      selectedPeriodTypeConfiguration = kpiConfigurationData.find(
        (x) => x.aliasName === periodType
      );
    }
    this.valueTypeString = periodType;
    return { selectedPeriodTypeConfiguration, periodType };
  }

  private GetPeriodType(periodType: string) {
    if (
      this.isLtm &&
      this.hasLtm &&
      !periodType.includes(FinancialsValueTypes.LTM)
    ) {
      periodType = periodType + " " + FinancialsValueTypes.LTM;
    } else if (
      this.isYtd &&
      this.hasYtd &&
      !periodType.includes(FinancialsValueTypes.YTD)
    ) {
      periodType = periodType + " " + FinancialsValueTypes.YTD;
    } else {
      this.isLtm = false;
      this.isYtd = false;
    }
    return periodType;
  }

  private getFinancialConfig() {
    const activeTab = this.tabList.find((x) => x.active)?.name;
    let currentTab: string = this.getCurrentTab(activeTab);
    const financialsConfig = this.pageConfigData.find(
      (x) => x.kpiType === currentTab
    );
    return financialsConfig;
  }

  private getCurrentTab(activeTab: string) {
    let currentTab: string;
    switch (activeTab) {
      case KpiTypes.CashFlow.name:
        currentTab = KpiTypes.CashFlow.type;
        break;
      case KpiTypes.BalanceSheet.name:
        currentTab = KpiTypes.BalanceSheet.type;
        break;
      default:
        currentTab = KpiTypes.ProfitLoss.type;
        break;
    }
    return currentTab;
  }

  setDataPeriodTypes(): void {
    this.isPageLoad = false;
    const isMonthlyDataAvailable = this.hasMonthly;
    const isQuarterlyDataAvailable = this.hasQuarterly;
    this.isAnnually = !isMonthlyDataAvailable && !isQuarterlyDataAvailable;
    this.isQuarterly = isQuarterlyDataAvailable;
    this.updateDataPeriodType();
  }

  ngOnInit() {
    this.isApply = false;
    this.getPermissions();
    this.selectedCurrencyCode = this.reportingCurrencyCode;
    this.getTabList();
    this.KpiValueUnit = {
      typeId: FinancialValueUnitsEnum.Thousands,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Thousands],
    };
    this.portfolioCompanyID = this.pcCompanyId;
    this.model.portfolioCompany.portfolioCompanyID = this.pcCompanyId;
    this.model.portfolioCompany.companyName = this.companyName;
    this.model.portfolioCompany.companyCurrency = this.reportingCurrencyCode;
    this.selectedCurrencyCode = this.reportingCurrencyCode;
    window.addEventListener("scroll", this.scrollEvent, true);
    this.setDefaultCurrencyModel();
  }
  setDefaultCurrencyModel() {
    this.currencyModel.defaultUnit = this.selectedUnit;
    this.currencyModel.defaultCurrencyCode = this.reportingCurrencyCode;
    this.currencyModel.spotRate = this.model.spotRate;
    this.currencyModel.isSpotRate = this.model.isSpotRate;
    this.currencyModel.convertedUnit = this.selectedUnit;
    this.currencyModel.convertedCurrencyCode = this.model.currencyCode?.currencyCode;
  }
  resetDefaultCurrencyModel() {
    this.currencyModel = {};
    this.currencyModel.defaultUnit = this.selectedUnit;
    this.currencyModel.defaultCurrencyCode = this.reportingCurrencyCode;
  }
  getPermissions() {
    this.profitLossPermission = this.financialsPermission?.filter(
      (x) => x.moduleId == this.kpiModulesEnum.ProfitAndLoss
    );
    this.balanceSheetPermission = this.financialsPermission?.filter(
      (x) => x.moduleId == this.kpiModulesEnum.BalanceSheet
    );
    this.cashFlowPermission = this.financialsPermission?.filter(
      (x) => x.moduleId == this.kpiModulesEnum.CashFlow
    );
    this.plViewPermission = this.financialsPermission?.some(
      (x) => x.moduleId == this.kpiModulesEnum.ProfitAndLoss && x.canView
    );
    this.bsViewPermission = this.financialsPermission?.some(
      (x) => x.moduleId == this.kpiModulesEnum.BalanceSheet && x.canView
    );
    this.cfViewPermission = this.financialsPermission?.some(
      (x) => x.moduleId == this.kpiModulesEnum.CashFlow && x.canView
    );
  }

  ngAfterViewInit() {
    if (this.uiuxMenu != undefined) {
      (this.uiuxMenu as any).closed = this.uiuxMenu.closed;
      this.configureMenuClose(this.uiuxMenu.closed);
    }
  }
  /**
   * Retrieves the tab list for the financials-beta component.
   * It makes an API call to get the master KPI tabs for "Financials",
   * filters the tabs based on the page configuration data,
   * and sets the tab list and active tab accordingly.
   * Finally, it calls the getValueTypeTabList method.
   */
  getTabList() {
    this.portfolioCompanyService
      .getMasterKPITabs("Financials")
      .subscribe((x) => {
        let financialTabs = x.body.kpiListModel;
        let pageConfigs = this.pageConfigData;
        for (const tab of financialTabs) {
          const tabName = tab.name;

          switch (tabName) {
            case KpiTypes.CashFlow.name:
              if (
                pageConfigs.find((x) => x.kpiType == KpiTypes.CashFlow.type)
                  .kpiConfigurationData.length == 0 ||
                !this.cfViewPermission
              ) {
                financialTabs = financialTabs.filter(
                  (x: { name: string }) => x.name != KpiTypes.CashFlow.name
                );
              }
              break;
            case KpiTypes.BalanceSheet.name:
              if (
                pageConfigs.find((x) => x.kpiType == KpiTypes.BalanceSheet.type)
                  .kpiConfigurationData.length == 0 ||
                !this.bsViewPermission
              ) {
                financialTabs = financialTabs.filter(
                  (x: { name: string }) => x.name != KpiTypes.BalanceSheet.name
                );
              }
              break;
            default:
              if (
                pageConfigs.find((x) => x.kpiType == KpiTypes.ProfitLoss.type)
                  .kpiConfigurationData.length == 0 ||
                !this.plViewPermission
              ) {
                financialTabs = financialTabs.filter(
                  (x: { name: string }) => x.name != KpiTypes.ProfitLoss.name
                );
              }
              break;
          }
        }
        this.tabList = financialTabs;
        if (this.tabList.length > 0) {
          this.tabName = this.tabList[0].name;
          this.tabList[0].active = true;
        }
        this.getValueTypeTabList();
      });
  }
  changesCurrency(event) {
    this.selectedCurrencyCode = event?.currencyCode;
  }
  selectValueTab(tab: any) {
    const currentTab = this.tabList.find((x) => x.active)?.name;
    if (currentTab == undefined) {
      tab = this.tabValueTypeList[0];
    }
    this.tabValueTypeList.forEach((tab) => (tab.active = false));
    tab.active = true;
    this.model.subTabName = tab.name;
    this.model.subAliasTabName = tab.alias;
    this.model.selectionChange = "tab";
    if (tab?.name == "IC") {
      this.isLtm = false;
      this.isYtd = false;
      this.setValueType({
        isMonthly: false,
        isQuarterly: false,
        isAnnually: true,
      });
    }
    let type = this.isLtm
      ? FinancialsValueTypes.LTM
      : this.isYtd
      ? FinancialsValueTypes.YTD
      : undefined;
    if (type != undefined) this.valueTypeString = tab.name + " " + type;
    else this.valueTypeString = undefined;
    this.setDataPeriodTypesPageConfigValues();
  }
  setValueType(valueType: any) {
    this.isMonthly = valueType.isMonthly;
    this.isQuarterly = valueType.isQuarterly;
    this.isAnnually = valueType.isAnnually;
    switch (true) {
      case valueType.isMonthly:
        this.selectedPeriodType = PeriodType.Monthly;
        break;
      case valueType.isQuarterly:
        this.selectedPeriodType = PeriodType.Quarterly;
        break;
      default:
        this.selectedPeriodType = PeriodType.Annually;
    }
  }
  private updateDataPeriodType(): void {
    const isMonthlyDataAvailable = this.hasMonthly;
    const isQuarterlyDataAvailable = this.hasQuarterly;
    const type = isMonthlyDataAvailable
      ? PeriodTypeFilterOptions.isMonthly
      : getType(isQuarterlyDataAvailable);
    this.setPeriodType(type);
  }

  setDefaultModelValue() {
    let year = new Date();
    this.today = new Date(year.setFullYear(year.getFullYear() + 10));
    this.yearRange = "2000:" + year.getFullYear();
    this.periodTypes = [
      { type: PeriodTypeEnum.Last3Month },
      { type: PeriodTypeEnum.Last6Month },
      { type: PeriodTypeEnum.YearToDate },
      { type: PeriodTypeEnum.Last1Year },
      { type: PeriodTypeEnum.DateRange },
    ];
    if (this.isSection.toLocaleLowerCase() != "financial") {
      this.periodTypes.push({ type: "Custom" });
    }
    this.orderType = [
      { type: OrderTypesEnum.LatestOnLeft },
      { type: OrderTypesEnum.LatestOnRight },
    ];
    this.model.orderType = [{ type: OrderTypesEnum.LatestOnLeft }];
    this.model.periodType = { type: PeriodTypeEnum.Last1Year };
    this.model.decimalType = 0;
    this.model.fxRates = {
      id: FxRates.SystemAPI,
      type: FxRates[FxRates.SystemAPI],
    };
    this.model.currecyUnit = {
      typeId: FinancialValueUnitsEnum.Thousands,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Thousands],
    };
    this.selectedUnit = this.model.currecyUnit;
    this.yearOptions = this.miscService.bindYearList();
    this.getCurrencyLists();
  }
  onCurrencyChange(e) {
    this.isReportingCurrency = true;
    if (this.reportingCurrencyCode != e?.currencyCode)
      this.isReportingCurrency = false;
    if(this.model.isSpotRate)
      this.getSpotRate();
  }
  scrollEvent = (event: any): void => {
    if (this.isdate) {
      this.datePicker.showOverlay();
      event.stopPropagation();
      let ui = this.datePicker?.nativeElement
        ?.querySelector(".p-datepicker-monthpicker ")
        .setStyle("top", "unset");
      if (ui) this.datePicker.setStyle(ui, "top", "unset");
    }
  };
  configureMenuClose(old: MatMenu["close"]): MatMenu["close"] {
    const upd = new EventEmitter();
    feed(
      upd.pipe(
        filter((event) => {
          if (event === "click") {
            return false;
          }
          return true;
        })
      ),
      old
    );
    return upd;
  }
  getCurrencyLists() {
    this.currencyService.getAllCurrencies().subscribe((result) => {
      let resp = result;
      if (resp != undefined && resp.code == "OK") {
        this.CurrencyList = resp.body;
        this.originalCurrencyList = resp.body;
        this.CurrencyList = this.CurrencyList.filter(
          (x) => x.currencyCode != this.reportingCurrencyCode?.trim() && x.currencyCode === GlobalConstants.USDCurrencyCode
        );
        this.filteredCurrencyList = this.CurrencyList;
      }
    });
  }
  onPeriodChange(e) {
    this.isCustom = false;
    this.isdate = false;
    if (e.type == "Date Range") {
      this.model.startPeriod = null;
      this.isdate = true;
    }
  }
  /**
   * Custom filter logic for the ComboBox.
   */
  public handleFilter(value: string): void {
    this.filteredCurrencyList = this.CurrencyList.filter(
      (s) =>
        s.currencyCode.toLowerCase().includes(value.toLowerCase()) ||
        s.currency.toLowerCase().includes(value.toLowerCase())
    );
  }
  /**
   * Handles the form submission event.
   */
  onSubmit() {
    this.model.selectionChange = "filter";
    this.isdate = false;
    this.menuTrigger.closeMenu();
    if (
      this.model.periodType?.type == "Date Range" &&
      (this.model.startPeriod == null || this.model.startPeriod == undefined)
    ) {
      return;
    }
    this.model.spotRate = this.spotRate;
    this.isSpotRateApply = this.model.isSpotRate && this.spotRate != null;
    this.isApply = true;
    this.isSubmit = this.isSubmit ? false : true;
    this.selectedUnit = this.model.currecyUnit;
    this.setDefaultCurrencyModel();
    if(this.reportingCurrencyCode != null && this.currencyModel?.convertedCurrencyCode != null && this.reportingCurrencyCode != this.currencyModel?.convertedCurrencyCode){
      this.isValueConverted = true;
    }
    else{
      this.isValueConverted = false;
    }
  }
  closeMenu() {
    this.menuTrigger.closeMenu();
  }
  showNoAccessError() {
    this.toasterService.error(ErrorMessage.NoAccess, "", {
      positionClass: "toast-center-center",
    });
  }
  export() {
    if (!this.showDownloadIcon) {
      this.showNoAccessError();
    } else {
      this.model.selectionChange = "export";
      this.isDownload = true;
    }
  }
  formReset(from: any) {
    this.isSubmit = false;
    this.spotRateErrorMessage = null;
    this.spotRate = null;
    this.model.spotRate = null;
    from.resetForm();
    this.resetInit();
    this.getCurrencyLists();
    this.model.currencyCode = null;
    this.resetDefaultCurrencyModel();
    this.isValueConverted = false;
  }
  resetInit() {
    this.model.orderType = [{ type: OrderTypesEnum.LatestOnLeft }];
    this.model.periodType = { type: PeriodTypeEnum.Last1Year };
    this.model.decimalType = 0;
    this.model.fxRates = {
      id: FxRates.SystemAPI,
      type: FxRates[FxRates.SystemAPI],
    };
    this.model.currecyUnit = {
      typeId: FinancialValueUnitsEnum.Thousands,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Thousands],
    };
    this.model.currencyCode = null;
  }
  validateKPIPeriod(form: any, model: any) {
    if (
      model.startPeriod != undefined &&
      model.startPeriod.length > 1 &&
      model.startPeriod[0] != null &&
      model.startPeriod[1] != null
    ) {
      this.datePicker.overlayVisible = false;
      this.datePicker.datepickerClick = true;
      if (model.startPeriod[0] > model.startPeriod[1]) {
        this.periodErrorMessage = ErrorMessage.StartDateLessThanEndDate;
      } else {
        this.periodErrorMessage = "";
      }
    }
  }
  onChangePeriodOption(type) {
    this.setPeriodType(type);
    this.model.selectionChange = "tab";
  }
  setPeriodType(type: string) {
    if (type == PeriodTypeFilterOptions.isMonthly) {
      this.selectedPeriodType = PeriodType.Monthly;
      this.isMonthly = true;
      this.isQuarterly = false;
      this.isAnnually = false;
    } else if (type == PeriodTypeFilterOptions.isQuarterly) {
      this.selectedPeriodType = PeriodType.Quarterly;
      this.isMonthly = false;
      this.isQuarterly = true;
      this.isAnnually = false;
    } else {
      this.selectedPeriodType = PeriodType.Annually;
      this.isMonthly = false;
      this.isQuarterly = false;
      this.isAnnually = true;
    }
  }
  selectTab(tab: ITab) {
    this.tabList.forEach((tab) => (tab.active = false));
    this.isYtd = false;
    this.isLtm = false;
    tab.active = true;
    this.onTabClick(tab);
    this.tabName = tab.name;
    this.model.selectionChange = "tab";
    this.resetTab();
  }
  resetTab() {
    this.setValueTypesList(this.tabValueTypeListCopy);
    this.selectValueTab(this.tabValueTypeList[0]);
    this.updateDataPeriodType();
    this.model.selectionChange = "tab";
  }
  onTabClick(tab: ITab) {
    if (tab != null || tab != undefined) {
      this.isDownload = false;
      this.tabName = tab.name;
      this.model = { ...this.model };
    }
  }
  isDownloading(exportProfitLoading) {
    this.isDownload = exportProfitLoading;
  }
  handleChange(e) {
    this.ErrorNotation = e;
  }
  onSpotRateChange() {
    this.spotRateErrorMessage = null;
    if (!this.model.isSpotRate) this.model.spotRateDate = null;
    if(this.model.isSpotRate) 
      this.getSpotRate();
    else
    {
      this.model.currencyCode = null;
    }
  }
  onChangeValueTypeOption(type) {
    const tab = this.tabValueTypeList.find((x) => x.active)?.name;
    if (tab != FinancialsValueTypes.IC) {
      this.setLtmAndYtdFlags(type);
      this.valueTypeString = tab + " " + type;
      let isPageLoad = this.setPageLoad();
      if (isPageLoad)
        this.setValueType({
          isMonthly: true,
          isQuarterly: false,
          isAnnually: false,
        });

      this.setDataPeriodTypesPageConfigValues();
      if (!this.isLtm && !this.isYtd) {
        this.valueTypeString = undefined;
      }
    }
  }

  private setPageLoad() {
    let isPagLoad = false;
    if (this.isLtm && this.isLtmPageLoad) {
      isPagLoad = true;
      this.isLtmPageLoad = false;
    } else if (this.isYtd && this.isYtdPageLoad) {
      isPagLoad = true;
      this.isYtdPageLoad = false;
    }
    return isPagLoad;
  }

  private setLtmAndYtdFlags(type: string) {
    if (type == FinancialsValueTypes.YTD) {
      this.isYtd = !this.isYtd;
      this.isLtm = false;
    } else {
      this.isLtm = !this.isLtm;
      this.isYtd = false;
    }
  }
  /**
   * Retrieves the spot rate based on the selected currency and reporting currency.
   *
   * This method constructs a SpotRateModel object using the current reporting currency code,
   * selected currency code, effective date, and conversion rate source. It then calls the
   * getSpotRate method from the PortfolioCompanyService to fetch the spot rate.
   *
   * @returns {void}
   */
  getSpotRate(): void {
    this.spotRateErrorMessage = null;
    if(!this.model.currencyCode || !this.model.fxRates)
    {
      this.spotRateErrorMessage = GlobalConstants.CurrencyRateError;
      return;
    }
    const spotRateModel: SpotRateModel = {
      FromCurrencyId: this.originalCurrencyList.find(x => x.currencyCode === this.reportingCurrencyCode)?.currencyID,
      ToCurrencyId: this.model?.currencyCode?.currencyID,
      EffectiveDate:this.datePipe.transform(this.model.spotRateDate, 'yyyy-MM-dd'),
      ConversionRateSource: this.model?.fxRates?.type,
    };
    this.portfolioCompanyService.getSpotRate(spotRateModel).subscribe({
      next: (result: number) => {
        if (result==null) {
          this.spotRate= null;
          this.model.spotRate = null;
          this.spotRateErrorMessage = GlobalConstants.SpotRateError;
        }
        else
        {
          this.spotRate= result;
        }
      },
      error: (error) => {
        console.error("Error retrieving spot rate:", error);
        this.spotRateErrorMessage = GlobalConstants.SpotRateRetrievalError;
      }
    });
  }
}
function getType(isQuarterlyDataAvailable: boolean) {
  return isQuarterlyDataAvailable ? PeriodTypeFilterOptions.isQuarterly : PeriodTypeFilterOptions.isAnnually;
}

function feed<T>(from: Observable<T>, to: Subject<T>): Subscription {
  return from.subscribe({
    next: data => to.next(data),
    error: err => to.error(err),
    complete: () => to.complete(),
  });

}