<div class="container-fluid pr-0 pl-0 data-ingestion-section">
  <div class="row mt-4 mb-2 mr-0 ml-0">
    <div class="col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
      <div class="card text-center ingestion-card mr-3 border-0">
        <div class="card-body mb-0">
          <div class="card-border  border-left-danger float-left mb-2 mt-2"></div>
          <div class="float-left">
            <div class="card-title text-left mb-0 mt-0">{{statusCounts?.inProgressCount}}</div>
            <p class="card-text">Ingestion In Progress</p>
          </div>
        </div>
      </div>
      <div class="card text-center  ingestion-card border-0">
        <div class="card-body mb-0">
          <div class="card-border border-left-success float-left mb-2 mt-2"></div>
          <div class="float-left">
            <div class="card-title text-left mb-0 mt-0">{{statusCounts?.completedCount}}</div>
            <p class="card-text">Ingestion Completed</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row mr-0 ml-0 mb-3">
    <div class="col-12 pr-0 pl-0">
      <div class="float-left v-align-middle">
        <div class="search-box-container">
          <div class="search-box-ingestion">
            <div class="search-input-container">
              <img src="assets/dist/images/search-icon-n.svg" alt="Search" class="search-icon">
              <input type="text" id="searchInput"  (input)="filterGrid($event.target.value)" placeholder="Search" class="data-ingestion-search" autocomplete="off" />
            </div>
            <button class="filter-btn" disabled>
              <img src="assets/dist/images/fi-filter-primary.svg" alt="filter-icon">
            </button>
          </div>
        </div>
      </div>
      <div class="float-right ingestion-btn">
        <button class="btn btn-primary float-right" id="id-ingestion" class="kendo-custom-button Body-R apply-btn"
          kendoButton themeColor="primary" (click)="showDigestion = true" 
          [disabled]="shouldDisableInitiateIngestion()"
          [title]="shouldDisableInitiateIngestion() ? 'Initiate Ingestion is disabled because both extraction types are hidden in page configuration' : 'Click to initiate data ingestion'">Initiate Ingestion</button>
      </div>
    </div>
  </div>
  <div class="row mr-0 ml-0 ">
    <div class="col-12 pr-0 pl-0">
      <!-- Conditional rendering based on data availability -->
      <ng-container *ngIf="spreadDetails && spreadDetails.length > 0; else emptyGridTemplate">
        <!-- Grid with virtual scrolling when data is present -->
        <kendo-grid [sortable]="sortSettings" 
          (cellClick)="onRowClick($event)" 
          class="k-grid-extraction-custom custom-ingestion-list-grid custom-ingest-grid" 
          [data]="spreadDetails" [sort]="sort" [skip]="skip"
          [scrollable]="'virtual'" [rowHeight]="40" [height]="450"
          (scrollBottom)="onScrollEnd()" [loading]="gridLoading" style="width: 100%;">
          <kendo-grid-column field="companyName" title="Data Ingestion Requests" width="500px">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              <div [ngSwitch]="dataItem.extractionType">
                <div *ngSwitchCase="'Specific KPI'" class="Body-R company-title text-truncate">
                    {{ dataItem.fundName }}
                </div>
                <div *ngSwitchDefault class="Body-R company-title text-truncate">
                    {{ dataItem.companyName }}
                </div>
            </div>
            <div class="Caption-R state-status text-truncate">{{dataItem.state}}</div>
            </ng-template>
          </kendo-grid-column>          
          <kendo-grid-column field="extractionType" title="Extraction Type"  width="150px">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              <div class="Body-R company-title text-truncate"> {{dataItem.extractionType}} </div>
            </ng-template>
          </kendo-grid-column>
         <kendo-grid-column field="statusName" title="Status" width="150px">
          <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
            <ng-container [ngSwitch]="dataItem?.status">
              <div class="inprogress-box" *ngSwitchCase="'In Progress'">In Progress</div>
              <div class="approved-box" *ngSwitchCase="'Completed'">Completed</div>
              <div class="error-box" *ngSwitchCase="'Failed'">Failed</div>
              <div class="approval-box" *ngSwitchCase="'Unknown'">Unknown</div>
            </ng-container>
          </ng-template>
         </kendo-grid-column>
          <kendo-grid-column field="startDate" title="Start Date" width="150px">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              <div class="Body-R company-title text-truncate"> {{dataItem.startDate | utcToLocalTime:'DD/MM/YYYY HH:mm:ss'}} </div>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="name" title="Initiated By" width="150px">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
              <div *ngIf="dataItem?.createdBy!=''" class="Body-R uploaded-box text-truncate" title="{{dataItem?.createdBy}}">{{dataItem?.userInitials}} </div>
            </ng-template>
          </kendo-grid-column>
        </kendo-grid>
      </ng-container>

      <ng-template #emptyGridTemplate>
        <!-- Grid without virtual scrolling for empty data state -->
        <kendo-grid class="k-grid-extraction-custom custom-ingestion-list-grid custom-ingest-grid"
          [data]="[]"
          [sortable]="sortSettings"
          [loading]="gridLoading"
          [height]="450">
          <kendo-grid-column field="companyName" title="Data Ingestion Requests" width="500px"></kendo-grid-column>
          <kendo-grid-column field="extractionType" title="ExtractionType"></kendo-grid-column>
          <kendo-grid-column field="statusName" title="Status" width="200px"></kendo-grid-column>
          <kendo-grid-column field="period" title="Period"></kendo-grid-column>
          <kendo-grid-column field="startDate" title="Start Date"></kendo-grid-column>
          <kendo-grid-column field="name" title="Initiated By"></kendo-grid-column>
          <!-- No Data Message -->
          <ng-template kendoGridNoRecordsTemplate>
            <ng-container *ngTemplateOutlet="noDataTemplate;"></ng-container>
          </ng-template>
        </kendo-grid>
      </ng-template>
    </div>
  </div>
</div>

<ng-template #noDataTemplate>
  <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 no-content-section no-content-section">
    <div class="text-center">
      <img src="assets/dist/images/no-content-lp-report.svg" alt="No Content" class="no-content-image">
    </div>
    <div class="text-center no-content-text pt-3 pb-2 Body-M">
      No requests available
    </div>
    <div class="text-center no-content-sub Caption-M template-text break-word">
      Click on “Initiate Ingestion” button to start data ingestion
    </div>
  </div>
</ng-template>

<div class="notification-sidebar data-ingestion-sidebar" *ngIf="showDigestion">
  <div class="side-pane">
    <div class="side-header">
      <div class="title-h float-left">Initiate Ingestion</div>
      <div class="float-right close-icon">
        <a (click)="showDigestion = false;resetAll();"> <img src="assets/dist/images/fix-close.svg" alt="" /></a>
      </div>
    </div>
    <div>
      <!-- {{ingestionForm.value | json}} -->
      <form [formGroup]="ingestionForm" (ngSubmit)="fetchFiles()">
        <div class="form-container">
          <div class="next-stepper">
          <div class="form-group">
            <label class="Body-R req-label">Extraction Type</label>
            <div class="radio-options-group d-inline-block">
              <div class="radio-option d-inline-block" *ngIf="showSpecificKpiRadio">
                <input type="radio" kendoRadioButton formControlName="extractionType" value="Specific KPI" 
                       (click)="onExtractionTypeChange('Specific KPI')" />
                <span class="extraction-radio-btn pr-4">{{ specificKpiDisplayName }} <span kendoTooltip position="left" [height]="'auto'" position="left"
                    class="custom-info" [title]="'Extract specific KPIs for analysis. KPIs marked for extraction during mapping will be automatically extracted.'">
                    <img src="assets/dist/images/file-info.svg" alt="info-icon" class="info-icon">
                  </span> </span>
              </div>
              <div class="radio-option d-inline-block padding-left-24" *ngIf="showAsIsExtractionRadio">
                <input type="radio" kendoRadioButton formControlName="extractionType" value="As Is Extraction" 
                       (click)="onExtractionTypeChange('As Is Extraction')" />
                <span class="extraction-radio-btn">{{ asIsExtractionDisplayName }} <span kendoTooltip position="left" [height]="'auto'" position="left"
                    class="custom-info" [title]="'Select As is Extraction to extract Financial Statements from documents'">
                    <img src="assets/dist/images/file-info.svg" alt="info-icon" class="info-icon">
                  </span></span>
              </div>
              <div *ngIf="asIsExtraction" class="file-size-warning Caption-R mt-2 p-2 mb-2">
                Only .pdf file can be uploaded with size not more than 500 MB
              </div>
            </div>
          </div>
          <!-- <div *ngIf="specificKpi" class="specific-kpi-note Caption-I mt-3 p-2 mb-3">
            <img src="assets/dist/images/ingetion-note.svg" alt="alert" class="mr-1">
            Please note that the KPIs “Marked for extraction” during KPI mapping will be automatically used for extraction.
          </div> -->
          <hr class="card-divider" >
          <div class="form-group" *ngIf="asIsExtraction">
            <label class="Body-R req-label" for="di_next_module">Module</label>
            <kendo-combobox id="di_next_module" [textField]="'name'" [valueField]="'id'" placeholder="Select Module" class="k-custom-solid-dropdown k-dropdown-height-32"
              [data]="modules" formControlName="module" [clearButton]="false"></kendo-combobox>
          </div>
          <div class="form-group pb-2" *ngIf="specificKpi">
            <label class="Body-R req-label" for="di_funds">Fund</label>
            <kendo-combobox id="di_funds" [showStickyHeader]="false"
              [popupSettings]="{ popupClass: 'k-custom-group-radio-parent-child' }" [clearButton]="false" formControlName="funds"
              [fillMode]="'solid'" [filterable]="true" [virtual]="virtual" class="k-custom-solid-dropdown k-dropdown-height-32"
              [size]="'medium'" [data]="investmentDetails" placeholder="Select Investment" textField="fundName" valueField="fundId"
              (valueChange)="onFundChange($event)"  (filterChange)="filterArrayObjects($event,'funds')">
              <ng-template kendoComboBoxItemTemplate let-fund>
                <span class="TextTruncate" title="{{fund.fundName}}">
                  {{fund.fundName}}
                </span>
              </ng-template>
            </kendo-combobox>
          </div>
          <div *ngIf="specificKpi">
            <label class="Body-R" for="selectKpiFor">Select KPI’s for :</label>
            <input [disabled]="!isFundSelected" type="checkbox" #selectKpiForFund (click)="onSelectKpiForClick($event, 'fund');" class="ml-2 mr-2 k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isFundKpis"  />
            <kendo-label class="k-checkbox-label dark-title" [for]="selectKpiForFund" text="Fund" ></kendo-label>
            <input [disabled]="!isFundSelected" type="checkbox" #selectKpiForPC (click)="onSelectKpiForClick($event, 'portfolioCompany');" class="ml-2 mr-2 k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isCompanyKpis"  />
            <kendo-label class="dark-title k-checkbox-label" [for]="selectKpiForPC" text="Portfolio Companies" ></kendo-label>
          </div>
          <hr class="card-divider" *ngIf="specificKpi">
          <div class="form-group" *ngIf="specificKpi && isCompanyKpis">
            <label class="Body-R req-label" for="di_company">Select portfolio companies</label>
            <kendo-multiselecttree [checkAll]="true"  [checkableSettings]="checkableSettings" class="k-dropdown-width-320 k-multiselect-custom  k-multiselect-custom-tree k-multiselect-chip-100 k-multiselect-grey-chip" 
            [clearButton]="false" [fillMode]="'solid'"  kendoMultiSelectTreeExpandable [kendoMultiSelectTreeHierarchyBinding]="issuerDetails" [textField]="'text'" [valueField]="'fieldId'"
              [checkboxes]="true" formControlName="companyIssuers"   [tagMapper]="tagMapper"
              placeholder="Select Companies" [filterable]="true" (valueChange)="onIssuerSelectionChange($event)" >
            </kendo-multiselecttree>
          </div>
          <div class="form-group" *ngIf="asIsExtraction">
            <label class="Body-R req-label" for="di_company">Portfolio Company Name</label>
            <kendo-combobox id="di_company" [loading]="companyLoading" [showStickyHeader]="false" [popupSettings]="{ popupClass: 'k-custom-group-parent-child' }" [clearButton]="false" formControlName="company" [fillMode]="'solid'"
            [filterable]="true" [virtual]="virtual" class="k-custom-solid-dropdown k-dropdown-height-32"
            [size]="'medium'" [data]="groupedCompanyList"  [valuePrimitive]="false" placeholder="Select Company" 
            [groupField]="'fundName'" textField="companyName"  valueField="companyId" (filterChange)="customFilter($event)"     (valueChange)="onCompanyChange($event)">
            
            <ng-template kendoComboBoxItemTemplate let-company>
              <span class="TextTruncate" title="{{company.companyName}}">
                {{company.companyName}}
              </span>
            </ng-template>
          </kendo-combobox>
          </div>
          <div *ngIf="repositoryStructure && repositoryStructure.length === 0 && asIsExtraction" class="error-message Caption-I mt-2 p-2 mb-2">
            <img src="assets/dist/images/FiAlertTriangle.svg" alt="alert" class="mr-1">
            Data Collection for this 'Portfolio Company' is incomplete. Please click 'Configure' to finish setup.
          </div>
          <div *ngIf="fundRepositoryStructure && fundRepositoryStructure.length === 0 && specificKpi" class="error-message Caption-I mt-2 p-2 mb-2">
            <img src="assets/dist/images/FiAlertTriangle.svg" alt="alert" class="mr-1">
            Data Collection for this 'Fund' is incomplete. Please click 'Configure' to finish setup.
          </div>
          <div *ngIf="(allMappedFundKpis.length == 0 && isFundKpis) && !(allMappedFundKpis.length == 0 && isFundKpis && (isCompanyKpis && selectedIssuers.length > 0 && companyWithUnmapperdKpi.length > 0))" class="kpi-error-message Caption-I mt-2 p-2 mb-2">
            <img src="assets/dist/images/FiAlertTriangle-red.svg" alt="alert" class="mr-1">
            No KPIs are marked for this 'Fund'. Please mark KPIs or choose a different Fund.
          </div>
          <div *ngIf="((isCompanyKpis && selectedIssuers.length > 0 && companyWithUnmapperdKpi.length > 0) || (isCompanyKpis && selectedIssuers.length > 0 && allMappedPcKpis?.length === 0 && companyWithUnmapperdKpi?.length === 0)) && !(allMappedFundKpis.length == 0 && isFundKpis && (isCompanyKpis && selectedIssuers.length > 0 && companyWithUnmapperdKpi.length > 0))" class="kpi-error-message Caption-I mt-2 p-2 mb-2">
            <img src="assets/dist/images/FiAlertTriangle-red.svg" alt="alert" class="mr-1">
             No KPIs are marked for all/few 'Portfolio Companies'. Please mark KPIs or choose a different Portfolio Company.
          </div>
          <div *ngIf="allMappedFundKpis.length == 0 && isFundKpis && (isCompanyKpis && selectedIssuers.length > 0 && companyWithUnmapperdKpi.length > 0)" class="kpi-error-message Caption-I mt-2 p-2 mb-2">
            <img src="assets/dist/images/FiAlertTriangle-red.svg" alt="alert" class="mr-1">
            No KPIs are marked for this  'Fund & Portfolio Company'. Please mark KPIs or choose a different Fund and Portfolio Company.
          </div>
          <div class="form-group" *ngIf="asIsExtraction">
            <label class="Body-R req-label" for="di_sourceType">Source Type</label>
            <kendo-combobox [textField]="'name'" [valueField]="'id'" id="di_sourceType" [clearButton]="false"
              placeholder="Select Source Type" class="k-custom-solid-dropdown k-dropdown-height-32" [data]="sourceTypes"
              formControlName="sourceType"></kendo-combobox>
          </div>
          <!-- <div class="Caption-I files-padding" *ngIf="specificKpi || asIsExtraction">
            <ng-container *ngIf="ingestionForm.get('extractionType')?.value=='Specific KPI'">
              Only .pdf, .xlsx and .xls files can be uploaded with size not more than 500 MB
            </ng-container>
            <ng-container *ngIf="ingestionForm.get('extractionType')?.value!='Specific KPI'">
              Only .pdf files can be uploaded with size not more than 500 MB
            </ng-container>
          </div> -->
          <div *ngIf="specificKpi" class="file-size-warning Caption-R mt-3 p-2 mr-4 mb-3">
            You can upload up to 10 Excel or PDF files, each up to 100 MB in size.
          </div>
        </div>
        
          <div class="button-group fixed-bottom custom-bottom">
            <div class="float-right">
              <button *ngIf="repositoryStructure && repositoryStructure.length === 0" type="button" class="kendo-custom-button Body-R apply-btn mr-2 custom-configbtn" fillMode="outline" kendoButton
              (click)="configure()" themeColor="primary">Configure</button>
              <button *ngIf="fundRepositoryStructure && fundRepositoryStructure.length === 0" type="button" class="kendo-custom-button Body-R apply-btn mr-2 custom-configbtn" fillMode="outline" kendoButton
              (click)="configure()" themeColor="primary">Configure</button>
              <button type="button" class="kendo-custom-button Body-R apply-btn mr-2" fillMode="outline" kendoButton
                (click)="reset()" themeColor="primary">Reset</button>
              <button [disabled]="!ingestionForm.valid || ((repositoryStructure && repositoryStructure.length === 0) || (fundRepositoryStructure && fundRepositoryStructure.length === 0)) || !(isFundKpisLoaded || isPcKpisLoaded)" class="kendo-custom-button Body-R apply-btn" kendoButton
                themeColor="primary">Fetch Files</button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
<input #fileInput type="file" multiple class="hidden-file-input" [accept]="ingestionForm.get('extractionType')?.value=='Specific KPI' ? '.pdf,.xlsx,.xls' : '.pdf'" (change)="onFileSelected($event)">
<app-loader-component *ngIf="isLoader"></app-loader-component>
