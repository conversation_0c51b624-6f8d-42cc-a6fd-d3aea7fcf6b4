import { Component, Input, OnInit } from "@angular/core";
import { FileExtension, KpiInfo } from "src/app/common/enums";
import { DocAuditModel, PcDataAuditModel } from "src/app/services/DataAuditModel";
import { AuditService } from "src/app/services/audit.service";
import { MiscellaneousService } from "src/app/services/miscellaneous.service";
import { DataAuditDto, DataAuditModel, PcAuditDocumentModel, PcAuditLogModel } from "../../models/kpi-audit-model";
import { NumberDecimalConst } from "src/app/common/constants";
import { isNumeric } from "@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric";
@Component({
  selector: "app-kpi-audit",
  templateUrl: "./kpi-audit.component.html",
  styleUrls: ["./kpi-audit.component.scss"],
})
export class KpiAuditComponent implements OnInit {
  NumberDecimalConst = NumberDecimalConst;
  @Input() kpiAuditModel: PcDataAuditModel = null;
  auditLogData: PcAuditLogModel[] = [];
  auditInfo: DataAuditModel = null;
  isOpenPopup: boolean = false;
  sideNavWidth: string = "";
  leftWidth: string = "";
  isDocument: boolean = false;
  isComments: boolean = true;
  commentText: string = "";
  documentData: any = [];
  isLoading: boolean = false;
  fileExtension = FileExtension;
  kpiInfo = KpiInfo;
  isDocLoading: boolean = false;
  isDocSupportLoading: boolean = false;
  kpiInfoCurrency: string = KpiInfo.Currency;
  @Input() currency:string;

  /**
   * Gets whether the current ModuleId is in the specific range (11-15)
   */
  get isModuleIdInCapTableRange(): boolean {
    return [11, 12, 13, 14, 15,31,32,33,34,35,36,37,38,39,40,41,42,42,44,45].includes(this.kpiAuditModel?.ModuleId);
  }

  /**
   * Constructs a new instance of the KpiAuditComponent.
   * @param auditService - The audit service.
   * @param miscService - The miscellaneous service.
   */
  constructor(
    private auditService: AuditService,
    private miscService: MiscellaneousService
  ) {}

  /**
   * Initializes the component and retrieves the audit logs.
   */
  ngOnInit() { 
    this.getAuditLogs();
  }
  /**
   * Retrieves the audit logs for the portfolio company.
   * Sets the auditLogData and auditInfo properties based on the retrieved data.
   * Sets the isLoading property to true while the data is being fetched.
   * Sets the isLoading property to false once the data is fetched or an error occurs.
   */
  getAuditLogs() {
    this.isLoading = true;
    this.auditService.getPcAuditLogData(this.kpiAuditModel).subscribe({
      next: (data: DataAuditDto) => {
        this.auditLogData = data.pcAudits;
        this.auditInfo = data.auditInfo;
        if (this.kpiAuditModel.ColumnKpiInfo != null && this.auditInfo!=null) {
          this.auditInfo.kpiInfo = this.kpiAuditModel.ColumnKpiInfo;
        }
        if(!this.currency){
          this.currency = this.auditInfo?.currencyCode;
        }
        this.isLoading = false;
      },
      error: (error: any) => {
        console.log(error);
        this.isLoading = false;
      },
    });
  }
  /**
   * Retrieves the width of the side navigation bar and calculates the width of the main content area.
   * @returns The calculated width of the main content area.
   */
  getSideNavWidth() {
    this.sideNavWidth = "50vw";
  }
  /**
   * Opens the popup and gets the side nav width.
   */
  openPopUp() {
    this.getSideNavWidth();
    this.isOpenPopup = true;
  }
  /**
   * Downloads a zip file containing audit log documents.
   * Sets the isDocLoading flag to true while the download is in progress.
   * The zip file is named using the current date and time, along with the module name from the auditInfo object.
   * @returns void
   */
  downloadZip() {
    this.isDocLoading = true;
    const currentDate = new Date();
    const formattedDate = currentDate
      .toLocaleTimeString()
      .replace(/:/g, "_")
      .replace(/\./g, "_");
    this.isDocLoading = true;
    this.auditService.exportZipFile(this.documentData).subscribe({
      next: (response: any) => {
        this.isDocLoading = false;
        this.miscService.downloadAllFormatFile(
          response,
          `AuditLogDocuments_${this.auditInfo.moduleName}_${formattedDate}.zip`
        );
      },
      error: (error: any) => {
        this.isDocLoading = false;
      },
    });
  }
  /**
   * Downloads a file from the server.
   * 
   * @param fileId - The ID of the file to download.
   * @param fileName - The name of the file to download.
   */
  downloadFile(fileId: string, fileName: string, isSourceFile: boolean = false) {
    this.isDocSupportLoading = true;
    let doc: DocAuditModel;
    if (isSourceFile) {
      doc = {
        DocumentId: fileId,
        DocumentName: fileName,
        Period: this.kpiAuditModel.Period,
        KpiId: this.kpiAuditModel.KpiId,
        ValueType: this.kpiAuditModel.ValueType,
        SubPageId: this.kpiAuditModel.ModuleId,
        IsSourceFile: true,
        SectionId: this.kpiAuditModel.SectionId
      };
    } else {
      doc = {
        DocumentId: fileId,
        DocumentName: fileName,
        Period: '',
        KpiId: 0,
        ValueType: '',
        SubPageId: 0,
        IsSourceFile: false,
        SectionId: this.kpiAuditModel.SectionId
      };
    }
    this.auditService.exportDocFile(doc).subscribe({
      next: (response: any) => {
        this.isDocSupportLoading = false;
        this.miscService.downloadAllFormatFile(response, fileName);
      },
      error: (error: any) => {
        console.log(error);
        this.isDocSupportLoading = false;
      },
    });
  }
  /**
   * Opens a document based on the provided document IDs.
   * @param documentIds - The IDs of the documents to be opened.
   */
  openDoc(documentIds: string) {
    this.miscService.closePopup();
    this.documentData = [];
    this.getSideNavWidth();
    this.isOpenPopup = true;
    this.isDocument = true;
    this.isComments = false;
    if (documentIds != null) {
      this.getSupportingDocuments(documentIds);
    }
  }
  /**
   * Opens the comments pop-up window.
   * @param commentId The ID of the comment to be displayed in the pop-up window.
   */
  openCommentsPopUp(commentId: number) {
    this.miscService.closePopup();
    this.commentText = "";
    this.getSideNavWidth();
    this.isOpenPopup = true;
    this.isDocument = false;
    this.isComments = true;
    if (commentId != null && commentId > 0) {
      this.getComment(commentId);
    }
  }
  /**
   * Retrieves the supporting documents for the given document IDs.
   * @param supportingDocIds - A comma-separated string of document IDs.
   */
  getSupportingDocuments(supportingDocIds: string) {
    this.isLoading = true;
    let documentIds = supportingDocIds.split(",").map(Number);
    this.auditService.getSupportingDocs(documentIds).subscribe({
      next: (data: PcAuditDocumentModel[]) => {
        this.documentData = data;
        this.isLoading = false;
      },
      error: (error: any) => {
        console.log(error);
        this.isLoading = false;
      },
    });
  }
  /**
   * Retrieves a comment by its ID.
   * @param commentId The ID of the comment to retrieve.
   */
  getComment(commentId: number) {
    this.isLoading = true;
    this.auditService.getComment(commentId).subscribe({
      next: (data: any) => {
        this.commentText = data.comments;
        this.isLoading = false;
      },
      error: (error: any) => {
        console.log(error);
        this.isLoading = false;
      },
    });
  }
  isNumberCheck(str: any) {
    return isNumeric(str);
  }
}
