export class  PageConfigFieldModel {
    name : string
    displayName: string;
    value : string;
    isMandatory : boolean = false;
    isActive : boolean;
    dataTypeId:number;
}
export class  PCCustomListFieldModel {
    groupId:number=0;
    groupName : string
    isActive : boolean;
    displayOrder:number;
    featureId:number;
    fieldId:number;
    isEditable:boolean=false;
}

export class  SubFeatureAccessPermissionsModel {
    public subFeature: string;
    public pageConfigName: string;
    public groupId: number=0;
    public companyId: number=0;
    public subFeatureId: number=0;
    public moduleId: number=0;
    public canAdd: boolean=false;
    public canEdit: boolean=false;
    public canView: boolean=false;
    public canExport: boolean=false;
    public canImport: boolean=false;
    public isActive: boolean=false;
    public dealId: number=0;
}
export class  Mapping_GroupFeature {
    public groupId: number=0;
    public companyId: number=0;
    public groupFeatureMappingId: number=0;
    public featureId: number=0;
    public canAdd: boolean=false;
    public canEdit: boolean=false;
    public canView: boolean=false;
    public canExport: boolean=false;
    public canImport: boolean=false;
    public isActive: boolean=false;
}
