﻿import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { ITab } from "projects/ng-neptune/src/lib/Tab/tab.model";
import { PortfolioCompanyService } from "src/app/services/portfolioCompany.service";
import { FeaturesEnum } from 'src/app/services/permission.service';
@Component({
  selector: "portfolio-company",
  templateUrl: "./portfolioCompany-new-list.component.html",
  styleUrls:['./portfolioCompany-new-list.component.scss'],
})
export class PortfolioCompanyNewListComponent implements OnInit{
  tabList: ITab[] = [];
  tabName: string;
  isWorkflowEnable: boolean;
  feature: typeof FeaturesEnum = FeaturesEnum;
  addPortfolioText: string = "Add Portfolio Company";
  constructor(
    protected changeDetectorRef: ChangeDetectorRef,
    private portfolioCompanyService: PortfolioCompanyService,
  ) {}

  ngOnInit(): void {  
     this.portfolioCompanyService.getWorkFlowPageConfiguration().subscribe((result) => {    
      this.isWorkflowEnable = result;   
      this.getTabList();   
      });
  }

  getTabList() {
    this.tabList.push({
      active: true,
      name: "Published"
    });
    if(this.isWorkflowEnable)
    {
      this.tabList.push({
        active: false,
        name: "Active Drafts"
      });
    }    
    this.tabName = this.tabList[0].name;
  }
  selectTab(tab: ITab){
    // deactivate all tabs
    this.tabList.forEach(tab => tab.active = false);
    tab.active = true;
    this.tabName=tab.name;
  }
  changesModelevent(isDraftOpen: any) {
    if (isDraftOpen) {
      this.selectTab(this.tabList[1]);
    }
  }
}
