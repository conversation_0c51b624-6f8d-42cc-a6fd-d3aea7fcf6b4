<div class="row ml-0 mr-0 portfolio-company-mapping" *ngIf="isEnableView" (resized)="onResized($event)"
    aria-autocomplete="none" id="kpi-mapping">
    <!-- Overlay to block clicks when extraction is active -->
    <div class="extraction-overlay" *ngIf="getExtractionSelectedCount() > 0"></div>
    <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0">
        <div class="row ml-0 mr-0">
            <div class="col-5 col-sm-5 col-md-5 col-xl-5 col-lg-5 pl-0 pr-0">
                <div class="col-12 col-xs-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pr-0 pl-0">
                    <div class="card">
                        <div class="card-header kpi-card-header">
                            <span class="custom-kpi-header-style pl-3 float-left">List of KPIs</span>
                            <span class="float-right pr-3 pt-1">
                                <kendo-dropdownlist id="kpiType-header-filter"
                                    class="kpi-list k-dropdown-width-220 k-custom-solid-dropdown k-dropdown-height-36 k-input-flat k-dropdown-no-border kpi-dropdown-background"
                                    [data]="groupedData" textField="pageConfigAliasName" valueField="field"
                                    [(ngModel)]="selectedKpiType" (valueChange)="GetSelectedKpiData($event)"
                                    [filterable]="true" [groupField]="groupField" [clearButton]="false"
                                    (filterChange)="handleFilter($event)">
                                    <ng-template kendoDropDownListGroupTemplate let-dataItem>
                                        <div title="{{ dataItem }}" class="flex align-items-center">
                                            <span class="TextTruncate" title="{{ dataItem }}">{{ dataItem }}</span>
                                        </div>
                                    </ng-template>
                                    <ng-template kendoDropDownListItemTemplate let-dataItem>
                                        <span id="kpi-type-name" class="TextTruncate"
                                            title="{{ dataItem.pageConfigAliasName }}">
                                            {{ dataItem.pageConfigAliasName }}
                                        </span>
                                    </ng-template>
                                </kendo-dropdownlist>
                            </span>
                        </div>
                        <div class="card-body mb-0">
                            <div
                                class="col-12 col-xs-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0 mapping-sec-search">
                                <div class="row mr-0 ml-0">
                                    <div class="col-12 col-xs-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pr-0 pl-0">
                                        <span class="fa fa-search fasearchicon p-1 kpi-mapping-cursor-pointer"
                                            (click)="onSelectAllListkpi()"></span>
                                        <div class="combobox">
                                            <input id="kpi-search" #searchKpiButton type="search" autocomplete="off"
                                                (click)="onSelectAllListkpi()" [(ngModel)]="kpiAllListItem"
                                                (ngModelChange)="getFilteredAllKpiList()" class="combobox-input"
                                                (keyup)="onKeyPress($event)" placeholder="Search KPI"
                                                [ngClass]="{'error': showError}">
                                            <span id="invalid-selection" *ngIf="showError" class="error-text"><i>Invalid
                                                    Selection.</i></span>
                                            <div #searchKpiModal class="combobox-options searchKpiModal"
                                                *ngIf="!allListHidden">
                                                <list-item id="kpi-list"
                                                    *ngFor="let item of filteredAllKpiList;let i = index"
                                                    class="search-unmapped-kpi-list"
                                                    [ngClass]="(item.isBoldKPI || item.isHeader) ? 'kpi-node-text':''">
                                                    <span id="kpi-name" title="{{item.name}}"
                                                        class="TextTruncate">{{item.name}}
                                                        <span id="header-kpi-icon" *ngIf="item.isHeader"
                                                            class="pl-3 pr-3" [pTooltip]="'Header KPI'"
                                                            tooltipPosition="top"
                                                            tooltipStyleClass="bg-grey-tooltip-color">
                                                            <img src="assets/dist/images/kpi-header.svg"
                                                                alt="kpi-header.svg" />
                                                        </span>
                                                    </span>
                                                    <span id="add-to-mapped-kpis"
                                                        class="float-right kpi-mapping-cursor-pointer pl-3">
                                                        <img class="pr-1" title="Column Kpi" *ngIf="item.kpiTypeId==2"
                                                            alt="column image"
                                                            [src]="'assets/dist/images/column-icon.svg'" />
                                                        <img class="pr-1" title="Row Kpi" *ngIf="item.kpiTypeId==1"
                                                            alt="row image" [src]="'assets/dist/images/row-icon.svg'" />
                                                        <img id="add-kpi-to-company" alt=""
                                                            (click)="addToMappedKPI(item,index,'Add')"
                                                            [src]="'assets/dist/images/kpi-plus-icon.svg'" />
                                                    </span>
                                                </list-item>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row  mr-0 ml-0">
                                <div class="col-12 col-xs-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0">
                                    <div class="row ml-0 mr-0 pl-0 protofilo-cmpny-o"
                                        [ngClass]="listKPIList.length>0?'unkpi-card-body-height':''"
                                        *ngIf="listKPIList.length > 0 && isEnableView">
                                        <div
                                            class="col-12 col-xs-12 col-sm-12 col-md-12 col-xl-12 col-lg-12  pr-0 pl-0 mat-custom-nodes">
                                            <ul>
                                                <li *ngFor="let data of listKPIList;let index = index"
                                                    title="{{data.name}}" class="custom-table-style">
                                                    <span class="float-left d-flex TextTruncate">
                                                        <!-- {{data.name}} -->
                                                        <span class="custom-node-class TextTruncate"
                                                            [ngClass]="(data.isBoldKPI || data.isHeader) ? 'kpi-node-text':''">
                                                            {{data.name}}</span>
                                                        <span *ngIf="data.isHeader" class="pl-3 pr-3"
                                                            [pTooltip]="'Header KPI'" tooltipPosition="top"
                                                            tooltipStyleClass="bg-grey-tooltip-color">
                                                            <img src="assets/dist/images/kpi-header.svg"
                                                                alt="kpi-header.svg" />
                                                        </span>
                                                        <span class="formula-image formula-image-cursor-pointer"
                                                            [ngClass]="[data.formula!=null && data.formula!='' && data!=undefined ?'d-block':'d-none',!data.isHeader?'pl-3':'']">
                                                            <img *ngIf="!data?.isHeader && data?.kpiInfo !='Text'"
                                                                (click)="openFormulaPopup(data)"
                                                                src="{{'assets/dist/images/formula.svg'}}"
                                                                alt="formula.svg" />
                                                        </span>

                                                    </span>
                                                    <span class="formula-image synonym-badge float-right mr-2" *ngIf="data?.synonym != null && data?.synonym != ''"
                                                    title="" [matTooltip]="data?.synonym?.split(',').join('\n')" matTooltipClass="mat-tooltip-multiline" >
                                                        Synonym(s)
                                                    </span>
                                                    <span
                                                        class="float-right kpi-mapping-cursor-pointer padding-right-add">
                                                        <img class="pr-1" title="Column Kpi" *ngIf="data.kpiTypeId==2"
                                                            alt="column image"
                                                            [src]="'assets/dist/images/column-icon.svg'" />
                                                        <img class="pr-1" title="Row Kpi" *ngIf="data.kpiTypeId==1"
                                                            alt="row image" [src]="'assets/dist/images/row-icon.svg'" />
                                                        <img id="add-kpi-comp" alt="plus icon"
                                                            (click)="addToMappedKPI(data,index,'Add')"
                                                            [src]="'assets/dist/images/kpi-plus-icon.svg'" /></span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="row ml-0 mr-0 procmpny-of"
                                        [ngClass]="listKPIList.length == 0?'unkpi-card-body-height':''"
                                        *ngIf="listKPIList.length==0">
                                        <div class="col-12 col-xs-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0">
                                            <zero-state-kpi [header]="'No KPIs to Select'"
                                                [message]="'Select a company to start KPI mapping'"
                                                id="mapping-notfound" [height]="kpilistheight"></zero-state-kpi>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="row mr-0 ml-0">
                                <div class="col-12 pl-0 col-sm-12 col-lg-12 col-xl-12 col-md-12 col-xs-12 pr-0">
                                    <div class="float-right" id="select-all-kpis">
                                        <button id="select-all-kpis" class="nep-button nep-button-secondary"
                                            [disabled]="listKPIList.length==0" type="button"
                                            (click)="getSelectAllListofkpi()">Select all</button>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-7 col-sm-7 col-md-7 col-xl-7 col-lg-7 pl-0 pr-0 custom-space-table">
                <!-- KPI action bar positioned above the mapping panel -->
                <div class="kpi-action-bar-container" *ngIf="getExtractionSelectedCount() > 0">
                    <div class="kpi-action-bar">
                        <div class="action-bar-content">
                            <span class="selected-count" >
                                {{getExtractionSelectedCount()}} KPI selected:
                                <span matTooltip="Select the KPIs you wish to only mark or unmark for extraction." class="no-matching-actions pl-2 pr-2" *ngIf="hasMixedExtractionStates()">No
                                    matching actions found</span>
                                <span *ngIf="hasMixedExtractionStates()" class="info-icon ml-1">
                                    <img src="assets/dist/images/info-icon.svg" alt="info" />
                                </span>
                            </span>
                            <button  class="extraction-action-btn"
                                [ngClass]="{'unmark-btn': areSelectedItemsMarkedForExtraction()}"
                                (click)="markOrUnmarkSelectedForExtraction()" *ngIf="!hasMixedExtractionStates()">
                                <mat-icon class="mr-2">{{areSelectedItemsMarkedForExtraction() ? 'link_off' :
                                    'link'}}</mat-icon>
                                {{areSelectedItemsMarkedForExtraction() ? 'Unmark for Specific KPI Extraction' : 'Mark
                                for Specific KPI Extraction'}}
                            </button>
                            <div class="flex-spacer"></div>
                            <div class="divider-vertical"></div>
                            <div class="close-action" (click)="clearExtractionSelection()">
                                <img src="assets/dist/images/close-icon.svg" alt="close" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mappedkpi-card-body-height">
                    <div
                        class="card-header card-header-main kpi-card-header d-flex align-items-center justify-content-between">
                        <div class="pl-3">
                            <mat-checkbox *ngIf="isKpiTypeMarkedForExtraction" [(ngModel)]="selectAllExtraction" (change)="toggleAllExtractionSelection()"
                                class="extraction-main-checkbox">
                                <span class="custom-kpi-header-style">Selected KPIs</span>
                            </mat-checkbox>
                            <span *ngIf="!isKpiTypeMarkedForExtraction" class="kpi-header-style">Selected KPIs</span>
                        </div>
                        <div class="pr-3">
                            <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                [(ngModel)]="selectedCompany" [fillMode]="'flat'" [filterable]="true" name="company"
                                [virtual]="virtual"
                                class="k-dropdown-width-240 k-select-medium k-select-flat-custom k-dropdown-height-32 no-bg"
                                [size]="'medium'" [data]="companyList" [filterable]="true" [value]="selectedCompany"
                                [valuePrimitive]="false" textField="companyName" placeholder="Select company"
                                valueField="portfolioCompanyID" (valueChange)="onSelectCompany($event)">
                                <ng-template kendoComboBoxItemTemplate let-company>
                                    <div title="{{company.companyName}}" class="TextTruncate">
                                        {{company.companyName}} </div>
                                </ng-template>
                            </kendo-combobox>
                        </div>
                    </div>
                    <div class="card-body mb-0">
                        <div class="row mr-0 ml-0">
                            <div
                                class="col-12 col-xs-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pr-0 pl-0 kpi-custom-border">
                                <span class="fa fa-search fasearchicon p-1"></span>
                                <input value="" [(ngModel)]="kpiName" (input)="FilterKPI(kpiName)" type="search"
                                    autocomplete="off" class="search-text-company kpi-Search-Height pl-3"
                                    placeholder="Search KPI" />
                            </div>
                        </div>
                        <div class="row  mr-0 ml-0">
                            <div class="col-12  pl-0 pr-0 col-xs-12 col-sm-12 col-md-12 col-xl-12 col-lg-12">
                                <div class="row  ml-0 mr-0 pl-0 protofilo-cmpny-o"
                                    *ngIf="kpiTreeList.length > 0&& Emptyischeck==true">
                                    <div
                                        class="col-12 col-xs-12 col-sm-12 col-md-12 col-xl-12 col-lg-12  pr-0 pl-0 mat-custom-nodes">
                                        <mat-tree class="unkpi-card-body-height kpi-mapping-truncate"
                                            [dataSource]="dataSource" [treeControl]="treeControl">
                                            <mat-tree-node
                                                [ngClass]="{'drop-above': dragNodeExpandOverArea === 'above' && dragNodeExpandOverNode === node,
                                                'drop-below': dragNodeExpandOverArea === 'below' && dragNodeExpandOverNode === node,
                                                'drop-center': dragNodeExpandOverArea === 'center' && dragNodeExpandOverNode === node,
                                                'child-node': node.level > 0}"
                                                draggable="true" (dragstart)="handleDragStart($event, node);"
                                                (dragover)="handleDragOver($event, node);"
                                                (drop)="handleDrop($event, node);" (dragend)="handleDragEnd($event);"
                                                *matTreeNodeDef="let node" matTreeNodeToggle
                                                class="mat-custom-treenode matcustomtreenode-padding TextTruncate"
                                                title="{{node.name}}">
                                                <button class="d-none" mat-icon-button disabled></button>
                                                <span class="pl-3 pr-3 drag-icon"><img
                                                        src="assets/dist/images/6dots.svg" alt=""></span>
                                                <img *ngIf="node.level > 0" class="corner-down-icon" src="assets/dist/images/corner-down-image.svg" alt="corner-down">
                                                <mat-checkbox *ngIf="isKpiTypeMarkedForExtraction" class="extraction-checkbox pr-3"
                                                    [ngClass]="{'child-extraction-checkbox': node.level > 0}"
                                                    [checked]="extractionSelection.isSelected(node)"
                                                    (click)="$event.stopPropagation()"
                                                    (change)="extractionSelection.toggle(node)">
                                                </mat-checkbox>
                                                <mat-checkbox matTreeNodePadding [matTreeNodePaddingIndent]="node.level > 0 ? 0 : 24"
                                                    [ngClass]="(node.isBoldKPI || node.isHeader) ? 'kpi-node-text':''"
                                                    class="mapped-kpi-custom-width checklist-leaf-node mat-custom-checkbox TextTruncate"
                                                    [checked]="checklistSelection.isSelected(node)"
                                                    (change)="checklistSelection.toggle(node);IsCheckParent($event,node)">
                                                    {{node.name}}
                                                    <span *ngIf="node.isHeader" class="pl-3 pr-3"
                                                        [pTooltip]="'Header KPI'" tooltipPosition="top"
                                                        tooltipStyleClass="bg-grey-tooltip-color">
                                                        <img src="assets/dist/images/kpi-header.svg"
                                                            alt="kpi-header.svg" />
                                                    </span>
                                                </mat-checkbox>
                                                <span class="formula-image custom-formula"
                                                    [ngClass]="[node.formula!=null && node.formula!='' && node!=undefined ?'d-block':'d-none',!node.isHeader?'pl-3':'',node.oldIsMapped ?'formula-image-cursor-pointer':'formula-image-cursor-none']">
                                                    <img *ngIf="!node.isHeader && node?.kpiInfo !='Text'"
                                                        (click)="node.oldIsMapped ? openFormulaPopup(node):''"
                                                        src="{{node.oldIsMapped ? 'assets/dist/images/formula.svg':'assets/dist/images/Edit-formula-Grey.svg'}}"
                                                        alt="formula.svg" />
                                                </span>
                                               
                                                <span class="float-right kpi-mapping-cursor-pointer remove-kpi">
                                                    <span class="synonym-container synonym-badge mr-1" *ngIf="isKpiTypeMarkedForExtraction && !(getExtractionSelectedCount() > 0)">
                                                        <span title="" [matTooltip]="node?.synonym?.split(',').join('\n')" matTooltipClass="mat-tooltip-multiline" >
                                                            Synonym(s)
                                                        </span>
                                                        <span title="" [matTooltip]="node.mappingKPIId > 0 ? 'Edit Synonym' : 'You can make changes once the KPI is mapped'"  
                                                        class="synonym-add-icon ml-1" *ngIf="node?.synonym != null && node?.synonym != ''" (click)="OnClickEditSynonym(node.name, node.mappingKPIId, node.synonym)">
                                                            <mat-icon class="custom-add-icon">edit</mat-icon>
                                                        </span>
                                                    
                                                        <span title="" [matTooltip]="node.mappingKPIId > 0 ? 'Add Synonym' : 'You can make changes once the KPI is mapped'"
                                                        *ngIf="node?.synonym == null || node?.synonym == ''" class="synonym-add-icon ml-1" (click)="OnClickAddSynonym(node.name, node.mappingKPIId)">
                                                            <mat-icon class="custom-add-icon">add</mat-icon>
                                                        </span>
                                                    </span>
                                                    <span>
                                                        <img src="assets/dist/images/duplicate-kpi.svg" id="duplicate-kpi" (click)="createDuplicate(node)" class="dup-icon pr-1"
                                                        [pTooltip]="'Duplicate'" tooltipPosition="top" tooltipStyleClass="bg-grey-tooltip-color"  alt="kpi-header.svg" />
                                                        <img [src]="'assets/dist/images/fi-unlink-icon.svg'" *ngIf="isKpiTypeMarkedForExtraction && node.isExtraction" class="unlink-extraction mr-2"
                                                        (click)="unlinkExtraction(node)" matTooltip="Unmark Extraction KPI"/>
                                                        <img class="pr-1" title="Column Kpi" *ngIf="node.kpiTypeId==2"
                                                            alt="column image"
                                                            [src]="'assets/dist/images/column-icon.svg'" />
                                                        <img class="pr-1" title="Row Kpi" *ngIf="node.kpiTypeId==1"
                                                            alt="row image" [src]="'assets/dist/images/row-icon.svg'" />
                                                        <img title="Remove kpi from this list" id="remove-kpi-from-comp"
                                                            (click)="showUnmappedKpiWarningModel(node)" alt=""
                                                            [src]="'assets/dist/images/minus-icon.svg'" />
                                                    </span>
                                                </span>
                                            </mat-tree-node>
                                            <mat-tree-node
                                                [ngClass]="{'drop-above': dragNodeExpandOverArea === 'above' && dragNodeExpandOverNode === node,
                                                'drop-below': dragNodeExpandOverArea === 'below' && dragNodeExpandOverNode === node,
                                                'drop-center': dragNodeExpandOverArea === 'center' && dragNodeExpandOverNode === node,
                                                'child-node': node.level > 0}"
                                                draggable="true" (dragend)="handleDragEnd($event);"
                                                (dragstart)="handleDragStart($event, node);"
                                                (dragover)="handleDragOver($event, node);"
                                                (drop)="handleDrop($event, node);"
                                                *matTreeNodeDef="let node; when: hasChild"
                                                class="mat-custom-treenode-child">
                                                <span class="pl-3 pr-3 drag-icon"><img
                                                        src="assets/dist/images/6dots.svg" alt=""></span>
                                                <button class="d-none" mat-icon-button matTreeNodeToggle
                                                    [attr.aria-label]="'toggle ' + node.name">
                                                    <mat-icon class="mat-icon-rtl-mirror">
                                                        {{treeControl.isExpanded(node) ? 'expand_more' :
                                                        'chevron_right'}}
                                                    </mat-icon>
                                                </button>
                                                <img *ngIf="node.level > 0" class="corner-down-icon" src="assets/dist/images/corner-down-image.svg" alt="corner-down">
                                                <mat-checkbox *ngIf="isKpiTypeMarkedForExtraction" class="extraction-checkbox pr-3"
                                                    [ngClass]="{'child-extraction-checkbox': node.level > 0}"
                                                    [checked]="extractionSelection.isSelected(node)"
                                                    (click)="$event.stopPropagation()"
                                                    (change)="extractionSelection.toggle(node)">
                                                </mat-checkbox>
                                                <mat-checkbox matTreeNodePadding [matTreeNodePaddingIndent]="node.level > 0 ? 0 : 24"
                                                    [ngClass]="(node.isBoldKPI || node.isHeader) ? 'kpi-node-text':''"
                                                    matTreeNodePadding class="mat-custom-checkbox"
                                                    [checked]="descendantsAllSelected(node)"
                                                    [indeterminate]="descendantsPartiallySelected(node)"
                                                    (change)="todoItemSelectionToggle(node);IsCheckParent($event,node)">
                                                    {{node.name}}
                                                    <span *ngIf="node.isHeader" class="pl-3 pr-3"
                                                        [pTooltip]="'Header KPI'" tooltipPosition="top"
                                                        tooltipStyleClass="bg-grey-tooltip-color">
                                                        <img src="assets/dist/images/kpi-header.svg"
                                                            alt="kpi-header.svg" />
                                                    </span>
                                                </mat-checkbox>
                                                <span class="formula-image"
                                                    [ngClass]="[node.formula!=null && node.formula!='' && node!=undefined ?'d-block':'d-none',!node.isHeader?'pl-3':'',node.oldIsMapped ?'formula-image-cursor-pointer':'formula-image-cursor-none']">
                                                    <img *ngIf="!node?.isHeader && node?.kpiInfo !='Text'"
                                                        (click)="node.oldIsMapped ? openFormulaPopup(node):''"
                                                        src="{{node.oldIsMapped ? 'assets/dist/images/formula.svg':'assets/dist/images/Edit-formula-Grey.svg'}}"
                                                        alt="formula.svg" />
                                                </span>                                     
                                                <span class="float-right kpi-mapping-cursor-pointer remove-kpi">
                                                    <span class="synonym-container synonym-badge mr-1" *ngIf="isKpiTypeMarkedForExtraction &&!(getExtractionSelectedCount() > 0)">
                                                        <span title="" [matTooltip]="node?.synonym?.split(',').join('\n')" matTooltipClass="mat-tooltip-multiline">
                                                            Synonym(s)
                                                        </span>
                                                        <span title="" [matTooltip]="node.mappingKPIId > 0 ? 'Edit Synonym' : 'You can make changes once the KPI is mapped'" 
                                                        class="synonym-add-icon ml-1" *ngIf="node?.synonym != null && node?.synonym != ''" (click)="OnClickEditSynonym(node.name, node.mappingKPIId, node.synonym)">
                                                            <mat-icon class="custom-add-icon">edit</mat-icon>
                                                        </span>
                                                    
                                                        <span title="" [matTooltip]="node.mappingKPIId > 0 ? 'Add Synonym' : 'You can make changes once the KPI is mapped'" 
                                                        *ngIf="node?.synonym == null || node?.synonym == ''" class="synonym-add-icon ml-1" (click)="OnClickAddSynonym(node.name, node.mappingKPIId)">
                                                            <mat-icon class="custom-add-icon">add</mat-icon>
                                                        </span>
                                                    </span>
                                                    <span>
                                                        <img src="assets/dist/images/duplicate-kpi.svg" id="duplicate-kpi" (click)="createDuplicate(node)" class="dup-icon pr-1"
                                                        [pTooltip]="'Duplicate'" tooltipPosition="top" tooltipStyleClass="bg-grey-tooltip-color"  alt="kpi-header.svg" />
                                                        <img [src]="'assets/dist/images/fi-unlink-icon.svg'" *ngIf="isKpiTypeMarkedForExtraction && node.isExtraction" class="unlink-extraction mr-2"
                                                        (click)="unlinkExtraction(node)" matTooltip="Unmark Extraction KPI"/>
                                                        <img class="pr-1" title="Column Kpi" *ngIf="node.kpiTypeId==2"
                                                            alt="column image"
                                                            [src]="'assets/dist/images/column-icon.svg'" />
                                                        <img class="pr-1" title="Row Kpi" *ngIf="node.kpiTypeId==1"
                                                            alt="row image" [src]="'assets/dist/images/row-icon.svg'" />
                                                        <img title="Remove kpi from this list" id="remove-kpi-from-comp"
                                                            (click)="showUnmappedKpiWarningModel(node)" alt=""
                                                            [src]="'assets/dist/images/minus-icon.svg'" />
                                                    </span>
                                                </span>
                                            </mat-tree-node>
                                        </mat-tree>
                                        <span #emptyItem></span>
                                    </div>
                                </div>
                                <div class="row ml-0 mr-0 procmpny-of"
                                    [ngClass]="kpiTreeList.length == 0?'unkpi-card-body-height':''"
                                    *ngIf="kpiTreeList.length==0">
                                    <div class="col-12 pl-0 pr-0 col-xs-12 col-sm-12 col-md-12 col-xl-12 col-lg-12">
                                        <zero-state-kpi [header]="'No KPIs Selected'"
                                            [message]="'Select a company to start KPI mapping'" id="mapping-notfound"
                                            [ngClass]="kpiTreeList.length !=0 ? 'unkpi-card-body-height':'kpi-list-zero-state'"
                                            [height]="kpilistheight"></zero-state-kpi>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row mr-0 ml-0">
                            <div class="col-12 pl-0 col-sm-12 col-lg-12 col-xl-12 col-md-12 col-xs-12 pr-0">
                                <div class="float-left kpi-footer-copy-all" id="copy-mapping-to">
                                    <kendo-multiselect #multiSelect [checkboxes]="true" [rounded]="'medium'" [popupSettings]="popupSettings"
                                        [fillMode]="'solid'"
                                        [ngClass]="{'k-multiselect-search':selectedCopyToCompanyList.length>0}"
                                        [kendoDropDownFilter]="filterSettings" name="copyToFeature"
                                        [virtual]="virtualMultiSelect" [clearButton]="false"
                                        class="k-multiselect-custom k-dropdown-width-280 high-z-index-popup" [disabled]="disableCopyTo"
                                        [tagMapper]="tagMapper" [data]="portfolioCompanyList"
                                        [(ngModel)]="selectedCopyToCompanyList" [textField]="'companyName'"
                                        (close)="clearSearch(multiSelect)" [valueField]="'portfolioCompanyID'"
                                        (valueChange)="onCopytoCompanyFunction($event);" [autoClose]="false"
                                        id="copymapping" placeholder="Copy Mapping To">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input id="copy-mapping-to-checkbox" type="checkbox"
                                                    class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"
                                                    kendoCheckBox [checked]="isCheckedCopyAll" [indeterminate]="isIndet"
                                                    (click)="onClick(multiSelect)" />
                                                <kendo-label for="chk">{{ toggleAllText }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.companyName">{{
                                                dataItem.companyName }}</span>
                                        </ng-template>
                                    </kendo-multiselect>

                                    <button id="copy-to-all"
                                        class="nep-button margin-copyall-btn nep-button-secondary d-none"
                                        disabled="true" type="button" (click)="getCopytoAllCompanies()">Copy to
                                        all</button>
                                </div>
                                <div class="float-right">
                                    <div class="float-right pl-3 pr-0">
                                        <button id="reset-mapped-kpis" class="nep-button nep-button-secondary"
                                            [disabled]="isDisabledCancelBtn" type="button"
                                            (click)="isCancelPopup=true">Reset</button>
                                        <button id="save-mapped-kpis" class="nep-button nep-button-primary"
                                            [disabled]="isDisabledSaveBtn" type="button" (click)="isSavePopup=true">Map
                                            KPIs</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<div>
    <confirm-modal *ngIf="isCancelPopup" customwidth="480px" modalTitle="Cancel Editing" primaryButtonName="Yes"
        secondaryButtonName="No" (primaryButtonEvent)="OnYes($event)" (secondaryButtonEvent)="OnNo($event)">
        <div class="row ml-0 mr-0">
            <div class="col-12 pl-0 pr-0 col-xs-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 p-3 custom-modal-message">All
                your
                changes will be lost. Are you sure you want to proceed?
            </div>
        </div>
    </confirm-modal>
    <confirm-modal *ngIf="isSavePopup" customwidth="480px" modalTitle="Save Changes" primaryButtonName="Confirm"
        secondaryButtonName="Cancel" (primaryButtonEvent)=" SaveMapping($event)"
        (secondaryButtonEvent)="OnCancelSavePopup($event)">
        <div class="row ml-0 mr-0">
            <div class="col-12 pl-0 pr-0 col-xs-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 p-3 custom-modal-message">
                <div>Please confirm you want to map the selected KPIs</div>
                <div class="pt-2">Note: Any unmapped KPIs will loose their parent-child relationship & Reordering</div>
            </div>
        </div>
    </confirm-modal>
</div>
<app-loader-component [ngStyle]="isLoader?{'display':'block'}:{'display':'none'}" *ngIf="isLoader">
</app-loader-component>
<app-kpi-formula-builder [moduleId]="this.moduleID" [isMapping]="true" [kpiModel]="selectedFormulaKPI" *ngIf="isOpenPopUp"
    (OnClose)="closeFormulaPopUp($event)" [formulaKPITypes]="groupedData"
    [selectedFormulaKPIType]="selectedKpiType" [companyId]="companyId"></app-kpi-formula-builder>

    <div *ngIf="showUpsertSynonymPopUp">
        <div id="kpi-add-dialog"
            class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12 AddOrUpdateKpi add-or-update-modal">
            <div class="nep-modal nep-modal-show kpi-add-edit-modal nep-kpi-bg">
                <div class="nep-modal-mask"></div>
                <div [style.width]="customwidth" [style.top]="customTop"
                    class="nep-card nep-card-shadow nep-modal-panel nep-modal-default nep-kpilist-pr">
                    <div class="nep-card-header" [ngClass]="hasHeaderStyle? 'nep-modal-title-custom':'nep-modal-title'">
                        {{popUpTitle}}
                        <div class="float-right close-icon cursor-filter">
                            <i class="pi pi-times" (click)="OnCloseUpsertSynonymPopUp()"></i>
                        </div>
                    </div>
                    <div class="nep-card-body">
                        <div class="row mr-0 ml-0 main-row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 pl-0 pr-0">
                                <div class="row mr-0 ml-0">
                                        <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 pl-0 pr-0">
                                            <div class="Caption-M">KPI Name</div>
                                            <div class="kpis-custom-select custom-nep-input default-txtcolor TextTruncate"> {{selectedKpiName}}</div>
                                            <div class="Caption-M mt-4">Synonyms</div>
                                            <div id="tagSynonymChips" class="opn-doc-mar-tp">
                                                <chip maxlen="200" [isReadonly]=false (removeItem)="removeTagItem($event)" (addItem)="addTagItem($event)"
                                                    [chipitems]="kpisynonym"
                                                    customclass="mat-chip mat-primary mat-standard-chip circularOutlined textColor fontStyle14">
                                                </chip>
                                            </div>    
                                            <div class="mt-2 info-style d-flex align-items-center">
                                                <mat-icon class="custom-info-icon">info</mat-icon>
                                                <span>Editing these synonym(s) will apply changes only at the company level. This will not impact the original synonym(s).</span>
                                            </div>                              
                                        </div>                                      
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nep-card-footer  nep-modal-footer add-or-update-modal-footer">
                        <div>
                            <nep-button  Type="Secondary" (click)="onResetSynonym()" [disabled]="disablePrimaryBtn">
                                Reset
                            </nep-button>
                            <nep-button Type="Primary" [disabled]="disablePrimaryBtn" (click)="upsertSynonyms()"
                                class="kpinep-butn-pl float-right">
                                {{primaryButtonName}}
                            </nep-button>
                            <nep-button class="float-right"  Type="Secondary" (click)="OnCloseUpsertSynonymPopUp()">
                                Cancel
                            </nep-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div *ngIf="showWarningModel">
        <div 
            class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12  add-or-update-modal">
            <div class="nep-modal nep-modal-show kpi-add-edit-modal nep-kpi-bg">
                <div class="nep-modal-mask"></div>
                <div [style.width]="'25.125rem '"
                    class="nep-card nep-card-shadow nep-modal-panel nep-modal-default nep-kpilist-pr unmappedKpiWarningModel">
                    <div class="nep-card-header" [ngClass]="hasHeaderStyle? 'nep-modal-title-custom':'nep-modal-title'">
                        Attention!
                        <div class="float-right close-icon cursor-filter">
                            <i class="pi pi-times" (click)="closeUnmappedKpiWarningModel()"></i>
                        </div>
                    </div>
                    <div class="unmappedKpiWarningModelBody">
					<span class="">
					    This action will remove your company-specific synonyms but retain the global synonyms.
					</span>                       
                    </div>
                    <div class="nep-card-footer nep-card-right nep-modal-footer add-or-update-modal-footer">
                        <div >
                            <nep-button  Type="Secondary" (click)="closeUnmappedKpiWarningModel()">
                                Cancel
                            </nep-button>
                            <nep-button  Type="Primary" (click)="addToMappedKPI(currentNode,0,'Remove')" class="kpinep-butn-pl">
                                Confirm
                            </nep-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>