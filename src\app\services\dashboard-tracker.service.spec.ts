
import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { DashboardTrackerService } from './dashboard-tracker.service';

const BASE_URL = 'http://localhost/';

describe('DashboardTrackerService', () => {
  let service: DashboardTrackerService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        { provide: 'BASE_URL', useValue: BASE_URL }
      ]
    });
    service = TestBed.inject(DashboardTrackerService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });


    it('should fetch dashboard data', () => {
      const mockData = [{ id: 1, name: 'Test' }];
      service.getDashboardData().subscribe(data => {
        expect(data).toEqual(mockData);
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/get');
      expect(req.request.method).toBe('GET');
      req.flush(mockData);
    });

    it('should handle empty dashboard data', () => {
      service.getDashboardData().subscribe(data => {
        expect(data).toEqual([]);
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/get');
      req.flush([]);
    });


  it('should handle error', () => {
    service.getDashboardData().subscribe({
      next: () => fail('should have errored'),
      error: (error) => {
        expect(error.status).toBe(500);
      }
    });
    const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/get');
    req.flush('Error', { status: 500, statusText: 'Server Error' });
  });
  

  it('should fetch dashboard tracker column data', () => {
    const mockColumnData = [{ columnId: 1, columnName: 'Column1' }];
    service.getDashboardTrackerColumnData().subscribe(data => {
      expect(data).toEqual(mockColumnData);
    });
    const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/get-column-data');
    expect(req.request.method).toBe('GET');
    req.flush(mockColumnData);
  });

  it('should handle empty dashboard tracker column data', () => {
    service.getDashboardTrackerColumnData().subscribe(data => {
      expect(data).toEqual([]);
    });
    const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/get-column-data');
    req.flush([]);
  });

  it('should handle error when fetching dashboard tracker column data', () => {
    service.getDashboardTrackerColumnData().subscribe({
      next: () => fail('should have errored'),
      error: (error) => {
        expect(error.status).toBe(404);
      }
    });
    const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/get-column-data');
    req.flush('Not Found', { status: 404, statusText: 'Not Found' });
  });

  // --- Additional tests for coverage ---

  describe('saveDashboardTrackerConfig', () => {
    it('should save dashboard tracker config', () => {
      const config = { id: 1, name: 'Config' } as any;
      const mockResponse = { success: true };
      service.saveDashboardTrackerConfig(config).subscribe(res => {
        expect(res).toEqual(mockResponse);
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/config/save');
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(config);
      req.flush(mockResponse);
    });

    it('should handle error when saving dashboard tracker config', () => {
      const config = { id: 1, name: 'Config' } as any;
      service.saveDashboardTrackerConfig(config).subscribe({
        next: () => fail('should have errored'),
        error: (error) => {
          expect(error.status).toBe(400);
        }
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/config/save');
      req.flush('Bad Request', { status: 400, statusText: 'Bad Request' });
    });
  });

  describe('getDashboardTableData', () => {
    it('should fetch dashboard table data with filter', () => {
      const filter = { page: 1 };
      const mockTableData = [{ row: 1 }];
      service.getDashboardTableData(filter).subscribe(data => {
        expect(data).toEqual(mockTableData);
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/table-data');
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(filter);
      req.flush(mockTableData);
    });

    it('should fetch dashboard table data with empty filter', () => {
      const mockTableData = [{ row: 1 }];
      service.getDashboardTableData().subscribe(data => {
        expect(data).toEqual(mockTableData);
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/table-data');
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({});
      req.flush(mockTableData);
    });

    it('should handle error when fetching dashboard table data', () => {
      service.getDashboardTableData().subscribe({
        next: () => fail('should have errored'),
        error: (error) => {
          expect(error.status).toBe(500);
        }
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/table-data');
      req.flush('Error', { status: 500, statusText: 'Server Error' });
    });
  });

  describe('saveTrackerDropdownValues', () => {
    it('should save tracker dropdown values', () => {
      const dto = { trackerFieldId: 1, dropdownValues: ['A', 'B'] };
      const mockResponse = { success: true };
      service.saveTrackerDropdownValues(dto).subscribe(res => {
        expect(res).toEqual(mockResponse);
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/save-dropdown-values');
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(dto);
      req.flush(mockResponse);
    });

    it('should save tracker dropdown values with empty array', () => {
      const dto = { trackerFieldId: 2, dropdownValues: [] };
      const mockResponse = { success: true };
      service.saveTrackerDropdownValues(dto).subscribe(res => {
        expect(res).toEqual(mockResponse);
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/save-dropdown-values');
      expect(req.request.body).toEqual(dto);
      req.flush(mockResponse);
    });

    it('should save tracker dropdown values with undefined dropdownValues', () => {
      const dto = { trackerFieldId: 3 };
      const mockResponse = { success: true };
      service.saveTrackerDropdownValues(dto).subscribe(res => {
        expect(res).toEqual(mockResponse);
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/save-dropdown-values');
      expect(req.request.body).toEqual(dto);
      req.flush(mockResponse);
    });

    it('should handle error when saving tracker dropdown values', () => {
      const dto = { trackerFieldId: 1, dropdownValues: ['A'] };
      service.saveTrackerDropdownValues(dto).subscribe({
        next: () => fail('should have errored'),
        error: (error) => {
          expect(error.status).toBe(403);
        }
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/save-dropdown-values');
      req.flush('Forbidden', { status: 403, statusText: 'Forbidden' });
    });
  });

  describe('errorHandler', () => {
    it('should return an observable that errors', (done) => {
      const error = new Error('Test error');
      service.errorHandler(error).subscribe({
        next: () => fail('should have errored'),
        error: (err) => {
          expect(err).toBe(error);
          done();
        }
      });
    });
  });
    describe('deleteDashboardTrackerColumn', () => {
    it('should delete dashboard tracker column by id', () => {
      const id = 123;
      const mockResponse = { success: true };
      service.deleteDashboardTrackerColumn(id).subscribe(res => {
        expect(res).toEqual(mockResponse);
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/config/delete/' + id);
      expect(req.request.method).toBe('DELETE');
      req.flush(mockResponse);
    });

    it('should handle error when deleting dashboard tracker column', () => {
      const id = 456;
      service.deleteDashboardTrackerColumn(id).subscribe({
        next: () => fail('should have errored'),
        error: (error) => {
          expect(error.status).toBe(404);
        }
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/config/delete/' + id);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('getAvailableCustomFieldsForMapTo', () => {
    it('should fetch available custom fields for map to', () => {
      const mockCustomFields = [
        { text: 'Custom Field 1', value: 1 },
        { text: 'Custom Field 2', value: 2 }
      ];
      service.getAvailableCustomFieldsForMapTo().subscribe(data => {
        expect(data).toEqual(mockCustomFields);
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/get-available-custom-fields');
      expect(req.request.method).toBe('GET');
      req.flush(mockCustomFields);
    });

    it('should handle empty custom fields response', () => {
      service.getAvailableCustomFieldsForMapTo().subscribe(data => {
        expect(data).toEqual([]);
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/get-available-custom-fields');
      req.flush([]);
    });

    it('should handle error when fetching custom fields', () => {
      service.getAvailableCustomFieldsForMapTo().subscribe({
        next: () => fail('should have errored'),
        error: (error) => {
          expect(error.status).toBe(500);
        }
      });
      const req = httpMock.expectOne(BASE_URL + 'api/dashboard-tracker/get-available-custom-fields');
      req.flush('Server Error', { status: 500, statusText: 'Server Error' });
    });
  });
});
