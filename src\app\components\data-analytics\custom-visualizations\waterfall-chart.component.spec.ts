import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { ChangeDetectorRef } from '@angular/core';
import { WaterfallChartComponent } from './waterfall-chart.component';
import * as Highcharts from 'highcharts';

describe('WaterfallChartComponent', () => {
  let component: WaterfallChartComponent;
  let fixture: ComponentFixture<WaterfallChartComponent>;
  let httpMock: HttpTestingController;
  let changeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;

  const mockWaterfallData = [
    { name: 'Start', y: 120000 },
    { name: 'Product Revenue', y: 569000 },
    { name: 'Service Revenue', y: 231000 },
    { name: 'Positive Balance', isIntermediateSum: true },
    { name: 'Fixed Costs', y: -342000 },
    { name: 'Variable Costs', y: -233000 },
    { name: 'Total', isSum: true }
  ];

  const mockRevealBridgeData = {
    metadata: {
      columns: [
        { name: 'Category', type: 0 },
        { name: 'Value', type: 1 }
      ]
    },
    data: [
      ['Start', 120000],
      ['Revenue', 569000],
      ['Costs', -342000],
      ['Total', 0]
    ]
  };

  beforeEach(async () => {
    const changeDetectorRefSpy = jasmine.createSpyObj('ChangeDetectorRef', ['detectChanges']);

    // Mock Highcharts
    spyOn(Highcharts, 'chart').and.returnValue({
      destroy: jasmine.createSpy('destroy')
    } as any);

    await TestBed.configureTestingModule({
      declarations: [WaterfallChartComponent],
      imports: [HttpClientTestingModule],
      providers: [
        { provide: ChangeDetectorRef, useValue: changeDetectorRefSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(WaterfallChartComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
    changeDetectorRef = TestBed.inject(ChangeDetectorRef) as jasmine.SpyObj<ChangeDetectorRef>;

    // Mock the chart container
    component.chartContainer = {
      nativeElement: document.createElement('div')
    };

    // Clean up window objects
    delete (window as any).revealBridge;
    delete (window as any).revealBridgeListener;
  });

  afterEach(() => {
    httpMock.verify();
    // Clean up window objects
    delete (window as any).revealBridge;
    delete (window as any).revealBridgeListener;
  });

  describe('Component Initialization', () => {
    it('should create the component', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with empty default data', () => {
      expect(component.defaultData).toEqual([]);
      expect(component.data).toEqual([]);
      expect(component.chartData).toEqual([]);
    });

    it('should have correct default input properties', () => {
      expect(component.apiUrl).toBe('');
      expect(component.chartTitle).toBe('');
      expect(component.chartData).toEqual([]);
    });
  });

  describe('ngOnInit - RevealBridge Setup', () => {
    it('should set up revealBridge on window object', () => {
      component.ngOnInit();
      
      expect((window as any).revealBridge).toBeDefined();
      expect(typeof (window as any).revealBridge.sendMessageToHost).toBe('function');
      expect(typeof (window as any).revealBridge.notifyExtensionIsReady).toBe('function');
      expect(typeof (window as any).revealBridge.runAction).toBe('function');
      expect(typeof (window as any).revealBridge.openUrl).toBe('function');
    });

    it('should set up revealBridgeListener on window object', () => {
      component.ngOnInit();
      
      expect((window as any).revealBridgeListener).toBeDefined();
      expect(typeof (window as any).revealBridgeListener.dataReady).toBe('function');
    });

    it('should add message event listener to window', () => {
      spyOn(window, 'addEventListener');
      component.ngOnInit();
      
      expect(window.addEventListener).toHaveBeenCalledWith('message', jasmine.any(Function), false);
    });

    it('should call fetchChartDataFromApi when apiUrl is provided', () => {
      component.apiUrl = 'https://test-api.com/data';
      spyOn(component, 'fetchChartDataFromApi');
      
      component.ngOnInit();
      
      expect(component.fetchChartDataFromApi).toHaveBeenCalledWith('https://test-api.com/data');
    });

    it('should call createChart when chartData is provided', () => {
      component.chartData = mockWaterfallData;
      spyOn(component, 'createChart');
      
      component.ngOnInit();
      
      expect(component.createChart).toHaveBeenCalled();
    });
  });

  describe('Data Conversion Methods', () => {
    describe('dataToJson', () => {
      it('should convert revealBridge data format to JSON objects', () => {
        const result = component.dataToJson(mockRevealBridgeData);
        
        expect(result.length).toBe(4);
        expect(result[0]).toEqual(jasmine.objectContaining({ name: 'Start', y: 120000 }));
        expect(result[1]).toEqual(jasmine.objectContaining({ name: 'Revenue', y: 569000 }));
        expect(result[2]).toEqual(jasmine.objectContaining({ name: 'Costs', y: -342000 }));
        expect(result[3]).toEqual(jasmine.objectContaining({ name: 'Total', y: 0 }));
      });

      it('should return empty array when metadata.columns is missing', () => {
        const invalidData = { metadata: {}, data: [[1, 2]] };
        const result = component.dataToJson(invalidData);
        
        expect(result).toEqual([]);
      });

      it('should handle empty data array gracefully', () => {
        const emptyData = {
          metadata: { columns: [{ name: 'Category' }, { name: 'Value' }] },
          data: []
        };
        const result = component.dataToJson(emptyData);
        
        expect(result).toEqual([]);
      });
    });

    describe('convertToWaterfallFormat', () => {
      it('should convert data objects to waterfall chart format', () => {
        const dataObjects = [
          { Category: 'Start', Value: 120000 },
          { Category: 'Revenue', Value: 569000 },
          { Category: 'Subtotal Test', Value: 0 },
          { Category: 'Total Sum', Value: 0 }
        ];
        const propertyNames = ['Category', 'Value'];
        
        const result = component['convertToWaterfallFormat'](dataObjects, propertyNames);
        
        expect(result[0]).toEqual({ name: 'Start', y: 120000 });
        expect(result[1]).toEqual({ name: 'Revenue', y: 569000 });
        expect(result[2]).toEqual(jasmine.objectContaining({ 
          name: 'Subtotal Test', 
          y: 0, 
          isIntermediateSum: true 
        }));
        expect(result[3]).toEqual(jasmine.objectContaining({ 
          name: 'Total Sum', 
          y: 0, 
          isSum: true 
        }));
      });

      it('should return empty array for insufficient data', () => {
        const result1 = component['convertToWaterfallFormat']([], ['Category', 'Value']);
        const result2 = component['convertToWaterfallFormat']([{ test: 1 }], ['Category']);
        
        expect(result1).toEqual([]);
        expect(result2).toEqual([]);
      });
    });
  });

  describe('API Data Fetching', () => {
    it('should fetch and process valid API data successfully', () => {
      spyOn(component, 'createChart');
      
      component.fetchChartDataFromApi('https://test-api.com/waterfall');
      
      const req = httpMock.expectOne('https://test-api.com/waterfall');
      expect(req.request.method).toBe('GET');
      
      req.flush(mockWaterfallData);
      
      expect(component.defaultData).toEqual(mockWaterfallData);
      expect(component.createChart).toHaveBeenCalled();
    });

    it('should handle API errors gracefully', () => {
      spyOn(console, 'error');
      
      component.fetchChartDataFromApi('https://test-api.com/waterfall');
      
      const req = httpMock.expectOne('https://test-api.com/waterfall');
      req.error(new ErrorEvent('Network Error', { message: 'Connection failed' }));
      
      expect(console.error).toHaveBeenCalled();
      // Verify chart is created (even with error state)
      expect(Highcharts.chart).toHaveBeenCalled();
    });

    it('should handle empty API response', () => {
      spyOn(console, 'error');
      
      component.fetchChartDataFromApi('https://test-api.com/waterfall');
      
      const req = httpMock.expectOne('https://test-api.com/waterfall');
      req.flush([]);
      
      expect(console.error).toHaveBeenCalled();
      expect(Highcharts.chart).toHaveBeenCalled();
    });

    it('should validate API data format', () => {
      spyOn(console, 'error');
      const invalidData = [{ wrongField: 'test', missingName: true }];
      
      component.fetchChartDataFromApi('https://test-api.com/waterfall');
      
      const req = httpMock.expectOne('https://test-api.com/waterfall');
      req.flush(invalidData);
      
      expect(console.error).toHaveBeenCalled();
      expect(Highcharts.chart).toHaveBeenCalled();
    });
  });

  describe('Chart Creation', () => {
    it('should prioritize revealBridge data over other sources', () => {
      component.data = [{ name: 'RevealBridge', y: 1000 }];
      component.chartData = [{ name: 'Input', y: 2000 }];
      component.defaultData = [{ name: 'API', y: 3000 }];
      
      spyOn(console, 'log');
      component.createChart();
      
      expect(console.log).toHaveBeenCalledWith(
        'Using revealBridge data:', 
        jasmine.arrayContaining([jasmine.objectContaining({ name: 'RevealBridge' })])
      );
    });

    it('should use chartData when no revealBridge data available', () => {
      component.data = [];
      component.chartData = [{ name: 'Input', y: 2000 }];
      component.defaultData = [{ name: 'API', y: 3000 }];
      
      spyOn(console, 'log');
      component.createChart();
      
      expect(console.log).toHaveBeenCalledWith(
        'Using input chartData:', 
        jasmine.arrayContaining([jasmine.objectContaining({ name: 'Input' })])
      );
    });

    it('should use defaultData when no other data sources available', () => {
      component.data = [];
      component.chartData = [];
      component.defaultData = [{ name: 'API', y: 3000 }];
      
      spyOn(console, 'log');
      component.createChart();
      
      expect(console.log).toHaveBeenCalledWith(
        'Using API defaultData:', 
        jasmine.arrayContaining([jasmine.objectContaining({ name: 'API' })])
      );
    });

    it('should create empty chart when no data is available', () => {
      component.data = [];
      component.chartData = [];
      component.defaultData = [];
      
      spyOn(console, 'warn');
      component.createChart();
      
      expect(console.warn).toHaveBeenCalledWith('No data available for chart creation');
      expect(Highcharts.chart).toHaveBeenCalled();
    });
  });

  describe('Utility Methods', () => {
    it('should update chart data via updateChartData method', () => {
      const newData = [{ name: 'Updated', y: 5000 }];
      spyOn(component, 'createChart');
      
      component.updateChartData(newData);
      
      expect(component.defaultData).toEqual(newData);
      expect(component.createChart).toHaveBeenCalled();
    });

    it('should refresh data from API when URL is available', () => {
      component.apiUrl = 'https://test-api.com/refresh';
      spyOn(component, 'fetchChartDataFromApi');
      
      component.refreshFromApi();
      
      expect(component.fetchChartDataFromApi).toHaveBeenCalledWith('https://test-api.com/refresh');
    });

    it('should warn when trying to refresh without API URL', () => {
      component.apiUrl = '';
      spyOn(console, 'warn');
      
      component.refreshFromApi();
      
      expect(console.warn).toHaveBeenCalledWith('No API URL provided for refresh');
    });



 describe('Component Lifecycle', () => {
    it('should destroy chart on component destruction', () => {
      const mockChart = jasmine.createSpyObj('Chart', ['destroy']);
      component.chart = mockChart;
      
      component.ngOnDestroy();
      
      expect(mockChart.destroy).toHaveBeenCalled();
    });

    it('should handle destruction when chart is undefined', () => {
      component.chart = undefined;
      
      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });

  describe('Input Property Handling', () => {
    it('should use custom chart title when provided', () => {
      component.chartTitle = 'Custom Waterfall Chart';
      component.chartData = mockWaterfallData;
      
      // Reset the spy to capture arguments
      (Highcharts.chart as jasmine.Spy).calls.reset();
      
      component.createChart();
      
      expect(Highcharts.chart).toHaveBeenCalled();
      const chartCall = (Highcharts.chart as jasmine.Spy).calls.mostRecent();
      const chartOptions = chartCall.args[1];
      expect(chartOptions.title.text).toBe('Custom Waterfall Chart');
    });

    it('should use default title when no custom title provided', () => {
      component.chartTitle = '';
      component.chartData = mockWaterfallData;
      
      (Highcharts.chart as jasmine.Spy).calls.reset();
      
      component.createChart();
      
      expect(Highcharts.chart).toHaveBeenCalled();
      const chartCall = (Highcharts.chart as jasmine.Spy).calls.mostRecent();
      const chartOptions = chartCall.args[1];
      expect(chartOptions.title.text).toBe('Waterfall Chart');
    });
  });
});
});