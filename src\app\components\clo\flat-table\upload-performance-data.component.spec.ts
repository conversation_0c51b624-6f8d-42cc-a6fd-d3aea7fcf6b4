import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UploadPerformanceData } from './upload-performance-data.component';
import { InvestCompanyService } from '../investmentcompany/investmentcompany.service';
import { ToastrService } from 'ngx-toastr';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ToastrModule } from 'ngx-toastr';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

describe('UploadPerformanceData', () => {
  let component: UploadPerformanceData;
  let fixture: ComponentFixture<UploadPerformanceData>;
  let investCompanyService: InvestCompanyService;
  let toastrService: ToastrService;
  let modalService: NgbModal;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [UploadPerformanceData],
      imports: [
        HttpClientTestingModule,
        ToastrModule.forRoot(),
        BrowserAnimationsModule
      ],
      providers: [
        InvestCompanyService,
        ToastrService,
        NgbModal,
        { provide: 'BASE_URL', useValue: 'http://localhost/' }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UploadPerformanceData);
    component = fixture.componentInstance;
    investCompanyService = TestBed.inject(InvestCompanyService);
    toastrService = TestBed.inject(ToastrService);
    modalService = TestBed.inject(NgbModal);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should download template', () => {
    const spy = spyOn(investCompanyService, 'downloadTemplate').and.returnValue(of(new Blob()));
    const linkSpy = spyOn(document, 'createElement').and.callThrough();
    component.tableName = 'testTable';
    component.downloadTemplate();
    expect(spy).toHaveBeenCalledWith('testTable');
    expect(linkSpy).toHaveBeenCalledWith('a');
  });

  it('should show error if tableName is empty when downloading template', () => {
    const spy = spyOn(toastrService, 'error');
    component.tableName = '';
    component.downloadTemplate();
    expect(spy).toHaveBeenCalledWith('Table name is required');
  });

  it('should handle file selection', () => {
    const file = new File([''], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    component.uploadedFiles = [file];
    const spy = spyOn(modalService, 'dismissAll');
    const emitSpy = spyOn(component.fileUploaded, 'emit');
    component.tableName = 'testTable';
    component.onFileSelected();
    expect(spy).toHaveBeenCalled();
    expect(emitSpy).toHaveBeenCalledWith(file);
  });

  it('should process file correctly', () => {
    const file = new File([''], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    component.processFile(file);
    expect(component.uploadedFiles).toEqual([file]);
    expect(component.fileSize).toBeCloseTo(0);
  });

  it('should show warning for invalid file type', () => {
    const spy = spyOn(toastrService, 'warning');
    const file = new File([''], 'test.txt', { type: 'text/plain' });
    component.processFile(file);
    expect(spy).toHaveBeenCalledWith('Please select only Excel files');
  });

  it('should show warning for file size exceeding limit', () => {
    const file = new File([new ArrayBuffer(21 * 1024 * 1024)], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    component.processFile(file);
    expect(component.invalidFile).toBeTrue();
  });

  it('should remove file', () => {
    component.uploadedFiles = [new File([''], 'test.xlsx')];
    component.removeFile();
    expect(component.uploadedFiles.length).toBe(0);
  });

  it('should open upload dialog', () => {
    component.openUpload();
    expect(component.openUploadDialog).toBeTrue();
  });

  it('should close modal and reset upload dialog', () => {
    const spy = spyOn(modalService, 'dismissAll');
    component.closeModal();
    expect(spy).toHaveBeenCalled();
    expect(component.openUploadDialog).toBeFalse();
    expect(component.uploadedFiles.length).toBe(0);
  });
});