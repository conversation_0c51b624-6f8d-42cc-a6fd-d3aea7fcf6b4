import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Chips } from './chip.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';

@NgModule({
  declarations: [Chips],
  imports: [CommonModule, FormsModule, ReactiveFormsModule, MatChipsModule, MatAutocompleteModule, MatFormFieldModule, MatIconModule, MatInputModule],
  exports: [Chips]
})
export class ChipModule {} 