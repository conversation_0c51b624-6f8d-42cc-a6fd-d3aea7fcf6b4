.loading-spinner {
  text-align: center;
  margin: 20px;
}

.no-data {
  text-align: center;
  margin: 20px;
  color: gray;
}

.datatable-container {
  overflow-x: auto;
}

.table-data-right {
  text-align: right;
}

.table-data-left {
  text-align: left;
}

.ui-bb {
  padding: 10px 15px;
}

.filter-first {
  min-width: 250px;
  padding: 5px;
  margin-bottom: 10px;
}

.fixed-menu {
  max-height: 400px !important;
  overflow-y: auto !important;
}

.click-view:hover  {
  cursor: pointer;
  color: #007bff;
  text-decoration: none;
  background-color: transparent;
}

.click-view:hover {
  color: #0056b3;
  text-decoration: underline;
}

.excel-load {
  margin-left: 5px;
}

.search-box {
  height: 32px;
  width: 200px;
}

.model-custom-padding {
  padding: 15px;
}

.header-tr-bg {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.tr-popup-currency {
  font-weight: 500;
  color: #495057;
}

.TextTruncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.S-M {
  font-weight: 600;
}

.cursor-filter {
  cursor: pointer;
}

.label-align {
  font-weight: 500;
}