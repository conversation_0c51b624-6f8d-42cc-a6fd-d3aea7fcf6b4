import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { DecimalDigitEnum, MiscellaneousService, FinancialValueUnitsEnum, OrderTypesEnum, PeriodTypeQuarterEnum } from 'src/app/services/miscellaneous.service';
import { ActionsEnum, FeaturesEnum, KPIModulesEnum, UserSubFeaturesEnum } from 'src/app/services/permission.service';
import { ReportCategory, ReportService, ReportType } from 'src/app/services/report.service';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { KpiInfo } from 'src/app/common/constants';
import { DropDownFilterSettings } from '@progress/kendo-angular-dropdowns';
import { GroupResult, groupBy,filterBy } from "@progress/kendo-data-query";
import { Observable, of } from 'rxjs';
@Component({
  selector: 'app-company-kpi-graph',
  templateUrl: './company-kpi-graph.component.html',
  styleUrls: ['./company-kpi-graph.component.scss']
})
export class CompanyKpiGraphComponent implements OnInit {
  public virtual: any = {
    itemHeight: 30,
    pageSize: 20
  };
  filteredKpiItems: [];
  expandedKpiLineItems: any = [];
  kpiItems: [];
  groupedData: GroupResult[] = [];
  isLoaded: boolean = false;
  id: any;
  feature: typeof FeaturesEnum = FeaturesEnum;
  subFeature: typeof UserSubFeaturesEnum = UserSubFeaturesEnum;
  actions: typeof ActionsEnum = ActionsEnum;
  companyKpiModuleCurrency: string;
  reportType: typeof ReportType = ReportType;
  reportCategory: typeof ReportCategory = ReportCategory;
  reportData: any = [];
  width: number = 0;
  reportModel: any = {
    sectorwiseOperationalKPIs: [],
    portfolioCompany: null,
    selectedReportTypes: [
      this.reportType.CompanyFinancialKPIReport,
      this.reportType.CompanyOperationalKPIGrowth,
    ],
    chartMetadetaList: [
      {
        chartName: "Financial KPI",
        chartType: "LineMarkers",
        colNameX: "Quarter",
        colNameY: "% Change In Revenue",
      },
      {
        chartName: "Financial KPI",
        chartType: "ColumnClustered",
        colNameX: "Quarter",
        colNameY: "Revenue",
      },
    ],
  };
  kpiInfo = "%";
  modelCompanyKpi: any = {};
  CompanyKPIOrginalData: any[] = [];
  CompanyKPIChartData: any[] = [];
  CompanyKPIChartCol: any = [];
  public filteredData: any[] = [...this.groupedData];
  @Input() modelList: any;
  @Input() searchFilter: any;
  @Input() isValueUpdated: boolean = false;
  @Input() typeField: string;
  searchFilterCopy: any = null;
  companyKPIs: any;
  companyKPIList: any;
  selectedCompanyKPI: any;
  companyKpiChartData: any[];
  companyKpiValueUnit: any;
  yBarFields = [];
  yLineFields = [];
  chartData = [];
  xField = null;
  yShades=[];
  isNoData:boolean=false;
  public showPopup = false;
  public selectedItem: any;
  defaultOrFavKpiExists:boolean = false;
  constructor(
    private portfolioCompanyService: PortfolioCompanyService,
    private miscService: MiscellaneousService,
    private _avRoute: ActivatedRoute,private cdr: ChangeDetectorRef) {
    if (this._avRoute.snapshot.params["id"]) {
      this.id = this._avRoute.snapshot.params["id"];
    }
  }

	public filterSettings: DropDownFilterSettings = { caseSensitive: false, operator: 'contains', };

  ngOnInit() {
    if(this.companyKPIList?.length > 0){
      this.setKpiGroup(this.companyKPIList);
    }
    this.modelCompanyKpi.periodType = PeriodTypeQuarterEnum.Last1Year;
    this.modelCompanyKpi.orderType = { type: OrderTypesEnum.LatestOnRight };
    this.modelCompanyKpi.decimalPlaces = {
      type: DecimalDigitEnum.Zero,
      value: "1.0-0",
    };
    this.companyKpiValueUnit = {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    };
  }

  ngOnChanges(changes: any): void {
    if (this.searchFilter != this.searchFilterCopy || this.isValueUpdated || this.typeField) {
      this.companyKpiModuleCurrency = this.modelList?.reportingCurrencyDetail != null ? this.modelList?.reportingCurrencyDetail?.currencyCode : 'NA';
      this.searchFilterCopy = this.searchFilter;
      this.isValueUpdated = false;
      this.modelCompanyKpi.periodType = this.searchFilter.periodType;
      this.companyKpiValueUnit = this.searchFilter.UnitType;
      this.getCompanyKPIs();
      this.isLoaded = true;
    }
  }
  onResized(event: any) {
    this.width = event?.newRect?.width;
  }

  truncateTabName(name: string, maxLength: number): any {
    name = name.trim();
    if (name.length <= maxLength) {
      return name;
    } else {
      const shortFilename = name.slice(0, maxLength); // Leave space for "...."
      return `${shortFilename}...`;
    }
  }

  getKpiItemList(result: any,isPreferenceUpdated:boolean) {
    const expandedKpiLineItems = [];
    let expandedId = 0;
    this.kpiItems = [];
      let filteredItems = result.filter(item => item?.parentId === null);
      let filteredChieldItems = result.filter(item => item?.parentId !== null);
      filteredItems.forEach((element) => {
        element.items = filteredChieldItems.filter((item) => item.parentId === element.kpiid);
      });
      if (filteredItems.length > 0) {
        expandedKpiLineItems.push(expandedId++);
        this.kpiItems = filteredItems;
        this.filteredKpiItems = [...this.kpiItems];
        if(!isPreferenceUpdated && !this.selectedCompanyKPI){
          let defaultLineItem = result.find(x => x.isDefault);
          if(!defaultLineItem){
            defaultLineItem = result.find(x => x.isFavourite);
          }
          let defaultKpi = defaultLineItem ? defaultLineItem : filteredItems[0];
          this.selectedCompanyKPI = defaultKpi;
        }
      };
    this.expandedKpiLineItems = expandedKpiLineItems.join(",");
  }

    /**
    * Handles the close event of the dropdown tree
    * @param {MouseEvent} event - The mouse event that triggered the dropdown close
    */
    onDropDownClose(event: MouseEvent) {
      this.showPopup = false;
    }
     /**
    * Handles the click event on the three dots menu icon
    * @param {MouseEvent} event - The mouse click event
    * @param {any} dataItem - The KPI item data associated with the clicked row
    */
    public handleDotsClick(event: MouseEvent, dataItem: any) {
      event.stopPropagation();
      this.showPopup = this.selectedItem !== dataItem || !this.showPopup;
      this.selectedItem = dataItem;
    }

    /**
   * Sets the selected KPI as default
   * @param {MouseEvent} event - The mouse click event
   * @param {any} item - The KPI item to be set as default
   */
    public onSetDefaultKpi(event: MouseEvent, item: any): void {
      if(this.selectedCompanyKPI.isDefault){
        this.selectedCompanyKPI.isDefault = false;
      }
      if(this.selectedCompanyKPI.kpiid == item.kpiid){
        this.selectedCompanyKPI.isDefault = true;
      }
      this.setShowPopUp();
      this.getUpdatedKPIs(item.kpiid,true,item.isFavourite,event);
    }

    /**
   * Removes the default status from selected KPI
   * @param {MouseEvent} event - The mouse click event
   * @param {any} item - The KPI item to remove default status
   */
    public onUnSetDefaultKpi(event: MouseEvent, item: any): void {
      if(item.kpiid == this.selectedCompanyKPI.kpiid){
        this.selectedCompanyKPI.isDefault = false;
      }
      this.setShowPopUp();
      this.getUpdatedKPIs(item.kpiid,false,item.isFavourite,event);

    }
    /**
   * Updates the popup state and marks KPI preference as updated
   * @private
   */
    private setShowPopUp() {
      this.showPopup = false;
    }

    /**
   * Sets the selected KPI as favorite
   * @param {MouseEvent} event - The mouse click event
   * @param {any} item - The KPI item to be marked as favorite
   */
    public onSetFavKpi(event: MouseEvent, item: any): void {
      if(this.selectedCompanyKPI.kpiid == item.kpiid){
        this.selectedCompanyKPI.isFavourite = true;
      }
      this.setShowPopUp();
      this.getUpdatedKPIs(item.kpiid,item.isDefault,true,event);
    }
    /**
   * Removes favorite status from selected KPI
   * @param {MouseEvent} event - The mouse click event
   * @param {any} item - The KPI item to remove favorite status
   */
    public onUnSetFavKpi(event: MouseEvent, item: any): void {
      if(item.kpiid == this.selectedCompanyKPI.kpiid){
        this.selectedCompanyKPI.isFavourite = false;
      }
      this.setShowPopUp();
      this.getUpdatedKPIs(item.kpiid,item.isDefault,false,event);
    }

    public fetchChildren(node: any): Observable<any[]> {
      // returns the items collection of the parent node as children
      return of(node?.items);
    }
  
    public hasChildren(node: any): boolean {
      // checks if the parent node has children
      return node?.items && node?.items.length > 0;
    }
  
    /**
   * Updates KPI preferences and refreshes KPI list
   * @param {number} kpiId - The ID of the KPI to update
   * @param {boolean} isDefault - Flag indicating if KPI should be set as default
   * @param {boolean} isFavourite - Flag indicating if KPI should be set as favourite
   * @param {MouseEvent} event - Mouse event that triggered the update
   * @description
   * Sends update request to server with new KPI preferences.
   * On successful response, processes updated KPI list and reloads graph.
   */
    getUpdatedKPIs(kpiId: number, isDefault: boolean, isFavourite: boolean,event: MouseEvent) {
      let KPIQueryModel = {
        portfolioCompanyIds: this.modelList.portfolioCompanyID.toString(),
        moduleId: this.modelList.moduleId,
        kpiType: "Company",
        CompanyId : this.modelList.portfolioCompanyID,
        KpiId:kpiId,
        IsDefault:isDefault,
        IsFavourite:isFavourite
      };
      this.miscService.UpdateKpiPreferenceAndGetKPIList(KPIQueryModel).subscribe({
        next: (result) => {
          let resp = result;
          if (resp != null) {
            this.processKpisAndLoadGraph(resp,true);
          }
        },
        error: (_error) => {},
      });
    }
    onFilterChange(filterString: string) {
      if (filterString?.length === 0) {
          this.filteredKpiItems = [...this.kpiItems];
          this.cdr.detectChanges(); // Manually trigger change detection
          return this.filteredKpiItems;
      }
  
      const searchTerm = filterString?.toLowerCase();
      const filteredRes = (this.kpiItems as any[]).map(group => ({
          ...group,
          items: group?.items?.filter((item) =>
              item?.itemName?.toLowerCase().includes(searchTerm)
          )
      })).filter(group =>
          group?.items?.length > 0 ||
          group?.itemName?.toLowerCase().includes(searchTerm)
      );
  
      this.filteredKpiItems = filteredRes as [];
      this.cdr.detectChanges(); // Manually trigger change detection
      return this.filteredKpiItems;
  }

    private showNoDataGraph() {
      this.isNoData = true;
      this.chartData = [];
    }

  getCompanyKPIs() {
    let KPIQueryModel = {
      portfolioCompanyIds: this.modelList.portfolioCompanyID?.toString(),
      moduleId:this.modelList.moduleId,
      kpiType: "Company"
    };
    this.miscService.GetCompanyOrInvestmentKPIList(KPIQueryModel).subscribe({
      next:(result) => {
        let resp = result["body"];
        if (resp != null && result.code == "OK") {
          this.processKpisAndLoadGraph(resp, false);
        }
      },
      error:(_error) => {
      }
  });
  }
  /**
 * Processes KPI response data and initializes the graph visualization
 * @param {any} resp - Response containing KPI list data from the server
 * @param {boolean} isPreferenceUpdated - Flag indicating if KPI preferences were updated
 * @private
 * @description
 * This method performs several key operations:
 * 1. Updates the company KPI list with new response data
 * 2. Generates KPI item list with parent-child relationships
 * 3. Sets KPI grouping for display
 * 4. Handles KPI selection logic:
 *    - On initial load or reset (isPreferenceUpdated = false):
 *      - Uses first KPI if none selected
 *      - Finds matching KPI by ID if previously selected
 *    - Maintains current selection when preferences are updated
 * 5. Initializes graph visualization:
 *    - Sets appropriate symbol/unit based on KPI type
 *    - Loads graph data for non-text KPIs
 *    - Shows "No Data" state for text KPIs or headers
 */
  private processKpisAndLoadGraph(resp: any, isPreferenceUpdated:boolean) {
    this.companyKPIList = resp;
    this.getKpiItemList(resp, false);
    this.setKpiGroup(this.companyKPIList);
    if (this.selectedCompanyKPI == undefined || this.selectedCompanyKPI == null) {
      this.selectedCompanyKPI = resp[0];
    } else {
      this.selectedCompanyKPI = resp.filter((x) => x["kpiid"] == this.selectedCompanyKPI?.kpiid)[0];
    }
    if(!isPreferenceUpdated){
      if (!this.selectedCompanyKPI) {
        this.selectedCompanyKPI = resp[0];
      } else {
        this.selectedCompanyKPI = resp.find(x => x["kpiid"] == this.selectedCompanyKPI?.kpiid);
      }
    }
    if (this.selectedCompanyKPI.kpiInfo != KpiInfo.Text && !this.selectedCompanyKPI.isHeader) {
      this.setSymbol(this.selectedCompanyKPI);
      this.LoadGraph();
    } else {
      this.showNoDataGraph();
    }
  }

  setSymbol(kpi:any)
  {
    switch(kpi?.kpiInfo){
      case  KpiInfo.Currency:
        this.companyKpiModuleCurrency = this.modelList?.reportingCurrencyDetail != null ? this.modelList?.reportingCurrencyDetail?.currencyCode : 'NA';
        break;
      case  KpiInfo.Number:
        this.companyKpiModuleCurrency = KpiInfo.Number;
        break;
      case  KpiInfo.Percentage:
        this.companyKpiModuleCurrency = KpiInfo.Percentage;
        break;
      case  KpiInfo.Multiple:
        this.companyKpiModuleCurrency = KpiInfo.Multiple;
        break;
    }
  }
  OnCompanyKPIChange(event: any) {
    if(event?.kpiInfo != KpiInfo.Text && !event?.isHeader){
      this.selectedCompanyKPI = event;
      this.updateWidth(false);
      this.setSymbol(this.selectedCompanyKPI);
      this.LoadGraph();
    }else{
      this.showNoDataGraph();
    }
  }
  LoadGraph() {
    this.isNoData=false;
    this.clearAll();
    let filter = {
      companyId: this.modelList.portfolioCompanyID.toString(),
      portfolioCompanyID: this.modelList.portfolioCompanyID.toString(),
      searchFilter: this.searchFilter,
      moduleId: KPIModulesEnum.Company,
      unit: this.companyKpiValueUnit == undefined ? FinancialValueUnitsEnum.Millions : this.companyKpiValueUnit.typeId,
      chartType: this.typeField,
      kpiId: this.selectedCompanyKPI?.kpiid
    };
    this.portfolioCompanyService
      .getChartsKpiData(filter)
      .subscribe({
        next: (result) => {
          if (result != undefined) {
            this.chartData = result?.data || [];
            this.yLineFields = result?.yLineFields || [];
            this.yBarFields = result?.yBarFields || [];
            this.xField = result?.xField;
            this.yShades=result?.yShades||[];
            this.isNoData=this.chartData.length>0?false:true;
          } else {
            this.isNoData=true;
            this.clearAll();
          }
        },
        error: (error) => {
        this.clearAll();
        }
      });
  }
  clearAll(): void {
    this.chartData = [];
    this.yLineFields = [];
    this.yBarFields = [];
    this.yShades = [];
  }
  dynamicWidth: string = "240px";
  onFocus(): void {
    this.updateWidth(false);
  }
  onBlur(): void {
    this.updateWidth(true);
  }
  open(): void {
    this.updateWidth();
  }
  close(): void {
    this.updateWidth();
  }
  opened(): void {
    this.updateWidth();
  }
  closed(): void {
    this.updateWidth();
  }
  updateWidth(defaultWidth:boolean=true): void {
    if (defaultWidth) {
      this.dynamicWidth = "240px";
    } else {
      this.dynamicWidth = Math.round(this.width - 10) + "px";
    }
    this.cdr.detectChanges();
  }
  onDoubleClick(){
    this.updateWidth(false);
  }
  /**
   * Filters the company KPI list based on the provided value and updates the KPI group.
   * If the value is empty, it resets the KPI group to the original company KPI list.
   * 
   * @param value - The string value to filter the company KPI list by. 
   *                The filter checks if the value is contained in either the 'parentkpi' or 'displayName' fields, ignoring case.
   */
  public customFilter(value: string): void {
    if (!value) {
      this.setKpiGroup(this.companyKPIList);
      return;
    }
    const filterConfig:any = {
      logic: 'or',
      filters: [
        { field: "parentkpi", operator: "contains", value: value, ignoreCase: true },
        { field: "displayName", operator: "contains", value: value, ignoreCase: true },
      ]
    };
  
    const filteredData = filterBy(this.companyKPIList, filterConfig);
    this.setKpiGroup(filteredData);
  }
  /**
   * Groups the provided KPI data by the "parentkpi" field and assigns the result to the `filteredData` property.
   *
   * @param filteredData - The KPI data to be grouped. It is expected to be an array of objects.
   */
  setKpiGroup(filteredData:any){
    this.filteredData = groupBy(filteredData, [
      { field: "parentkpi" },
    ]) as GroupResult[];
  }
}