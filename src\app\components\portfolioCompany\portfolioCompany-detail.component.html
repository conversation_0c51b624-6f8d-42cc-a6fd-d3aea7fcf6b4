<div class="row portfolio-company-detail-section">
    <div class="col-lg-12">
        <div class="detail-portfolio-component">
            <div class="card-body portafolio-table" *ngIf="!loading">
                <ng-container *ngFor="let item of subPageList">
                    <ng-container [ngSwitch]="item.name">
                        <ng-container *ngSwitchCase="pcConstants.STATIC_INFORMATION">
                            <ng-container *ngTemplateOutlet="staticInformationTemplate"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="pcConstants.KEY_PERFORMANCE_INDICATOR">
                            <ng-container *ngTemplateOutlet="keyPerformanceIndicatorTemplate"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="pcConstants.COMPANY_FINANCIALS">
                            <ng-container *ngTemplateOutlet="companyFinancialsTemplate"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="pcConstants.COMMENTARY">
                            <ng-container *ngTemplateOutlet="commentaryTemplate"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="pcConstants.CAP_TABLE">
                            <ng-container *ngTemplateOutlet="capTableTemplate"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="pcConstants.OTHER_KPIS">
                            <ng-container *ngTemplateOutlet="otherKpisTemplate"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="pcConstants.SUSTAINABLE_DEVELOPMENT_GOALS_IMAGES">
                            <ng-container *ngTemplateOutlet="sustainableDevelopmentGoalsImagesTemplate"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="pcConstants.VALUATION_SUMMARY">
                            <ng-container *ngTemplateOutlet="valuationSummaryTemplate"></ng-container>
                        </ng-container>
                    </ng-container>
                </ng-container>
                <ng-template #staticInformationTemplate>
                    <div class="row pt-3" *ngIf="canView">
                        <div class="col-lg-12">
                            <div class="portfolio-detail-component">
                                <div class="card card-main static-card">
                                    <div class="row mr-0 ml-0">
                                        <div class="col-sm-12 pr-0 pl-0">
                                            <div
                                                class="static-pc-section fund-detail section1-height chart-area row mr-0 ml-0 mb-0">
                                                <div class="col-12 col-md-12 col-lg-12 col-xl-12 col-sm-12 pr-0 pl-0">
                                                    <div class="row mr-0 ml-0 pb-1 static-bg">
                                                        <div
                                                            class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 pr-0 pl-0 chart-title pc-section-header">
                                                            <div class="float-right pr-2 edit-icon-padding">
                                                                <a (click)="editRedirect()" title="Edit"
                                                                    id="pc-edit-anchor"><img alt=""
                                                                        src="assets/dist/images/EditIcon.svg"
                                                                        id="pc-edit-img"></a>
                                                            </div>
                                                            <div class="float-right pr-2 edit-icon-padding">
                                                                <kendo-splitbutton
                                                                    *ngIf="mappedLpReportTemplates?.length>0"
                                                                    (buttonClick)="openSplitButton($event)" #splitButton
                                                                    (itemClick)="downloadReport($event)"
                                                                    class="split-button-custom-primary-bg split-button-custom"
                                                                    [ngClass]="exportLoading ? 'split-button-width-164' : 'split-button-width-140'"
                                                                    buttonClass="split-button-custom-bg"
                                                                    [data]="mappedLpReportTemplates"
                                                                    [popupSettings]="{ popupClass:'popup-split-button-custom-primary-bg', animate: true,popupAlign: { vertical: 'bottom', horizontal: 'left' } }"
                                                                    id="pc-download-btn">
                                                                    Download <img *ngIf="exportLoading" alt=""
                                                                        src="assets/dist/images/Loader.svg"
                                                                        class=" spin float-right">
                                                                    <ng-template kendoSplitButtonItemTemplate
                                                                        let-dataItem>
                                                                        <div
                                                                            class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 pr-0 pl-0 ">
                                                                            <div class="float-left pr-1">
                                                                                <kendo-svg-icon [icon]="pdfIcon"
                                                                                    class="pdf-icon "></kendo-svg-icon>
                                                                            </div>
                                                                            <div class="">
                                                                                <span class="truncate Body-R"
                                                                                    [fillMode]="'outline'"
                                                                                    [size]="'medium'"
                                                                                    [themeColor]="'primary'"
                                                                                    title="{{dataItem.templateName}}"
                                                                                    id="{{ dataItem.templateName }}">
                                                                                    {{ dataItem.templateName }}
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    </ng-template>
                                                                </kendo-splitbutton>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <ng-container *ngFor="let subPage of subPageList">
                                                        <ng-container [ngSwitch]="subPage.name">
                                                            <ng-container *ngSwitchCase="pcConstants.GEOGRAPHIC_LOCATIONS">
                                                                <div class="row mr-0 ml-0 pt-3 pb-2"
                                                                    *ngIf="subPageList | arrayFilter: isFieldActive :companyPageSectionConstants.Locations">
                                                                    <div class="col-sm-12 pr-3 pl-3">
                                                                        <div class="line-wrapper">
                                                                            <span class="mr-2 TextTruncate"
                                                                                title="{{subPageList | arrayFilter: getStaticFieldDisplayName :companyPageSectionConstants.Locations}}">{{subPageList
                                                                                | arrayFilter: getStaticFieldDisplayName
                                                                                :companyPageSectionConstants.Locations}}</span>
                                                                            <div class="line"></div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="pt-2 pb-3"
                                                                    [ngClass]="investmentProfessionFieldList?.length ==0 ?'pb-3':'pb-1'"
                                                                    *ngIf="subPageList | arrayFilter: isFieldActive :companyPageSectionConstants.Locations"
                                                                    [hideIfUserUnAuthorized]='{subFeatureId:subFeature.StaticDataBusinessDesciptionInvestmentProfessional,action:actions[actions.canView],id:id}'>
                                                                    <div class="card">
                                                                        <div class="card-body mb-0">
                                                                            <div
                                                                                class="table-responsive card-border static-table-border">
                                                                                <table
                                                                                    class='table mb-0 static-info-table'>
                                                                                    <thead>
                                                                                        <tr>
                                                                                            <ng-container
                                                                                                *ngFor="let geo of geographicLocationFieldList">
                                                                                                <th scope="col"
                                                                                                    class="text-align-left TextTruncate"
                                                                                                    title="{{geo.displayName}}"
                                                                                                    *ngIf="geo.isActive">
                                                                                                    {{geo.displayName}}
                                                                                                </th>
                                                                                            </ng-container>
                                                                                        </tr>
                                                                                    </thead>
                                                                                    <tbody>
                                                                                        <tr
                                                                                            *ngFor="let location of model.geographicLocations">
                                                                                            <ng-container
                                                                                                *ngFor="let geo of geographicLocationFieldList">
                                                                                                <td class="text-align-left"
                                                                                                    *ngIf="geo.isActive">
                                                                                                    <span
                                                                                                        class="loc{{geo.fieldID}}"
                                                                                                        *ngIf="geo.name ==companyInformationConstants.Region"
                                                                                                        title="{{ location?.region!=null
                                                                                                                        ?location?.region?.region:'NA' }}">{{
                                                                                                        location?.region!=null
                                                                                                        ?location?.region?.region:"NA"
                                                                                                        }}</span>
                                                                                                    <span
                                                                                                        class="loc{{geo.fieldID}}"
                                                                                                        *ngIf="geo.name ==companyInformationConstants.Country"
                                                                                                        title="{{
                                                                                                                        location?.country?.country!=null?location?.country?.country:'NA'
                                                                                                                        }}">{{
                                                                                                        location?.country?.country!=null?location?.country?.country:"NA"
                                                                                                        }}</span>
                                                                                                    <span
                                                                                                        class="loc{{geo.fieldID}}"
                                                                                                        *ngIf="geo.name ==companyInformationConstants.State"
                                                                                                        title="{{
                                                                                                                        location?.state?.state!=null?location?.state?.state:'NA' }}">{{
                                                                                                        location?.state?.state!=null?location?.state?.state:"NA"
                                                                                                        }}</span>
                                                                                                    <span
                                                                                                        class="loc{{geo.fieldID}}"
                                                                                                        *ngIf="geo.name ==companyInformationConstants.City"
                                                                                                        title="{{
                                                                                                                        location?.city?.city!=null?location?.city?.city:'NA' }}">
                                                                                                        {{
                                                                                                        location?.city?.city!=null?location?.city?.city:"NA"
                                                                                                        }} </span>
                                                                                                </td>
                                                                                            </ng-container>
                                                                                        </tr>
                                                                                    </tbody>
                                                                                </table>
                                                                                <app-empty-state [isGraphImage]="false"
                                                                                    *ngIf="model.geographicLocations==null || model.geographicLocations?.length==0"></app-empty-state>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="pcConstants.INVESTMENT_PROFESSIONALS">
                                                                <div class="row mr-0 ml-0 pt-3 pb-2"
                                                                    *ngIf="subPageList | arrayFilter: isFieldActive :companyPageSectionConstants.InvestmentProfessionals">
                                                                    <div class="col-sm-12 pr-3 pl-3">
                                                                        <div class="line-wrapper">
                                                                            <span class="mr-2 TextTruncate"
                                                                                title="{{subPageList | arrayFilter: getStaticFieldDisplayName :companyPageSectionConstants.InvestmentProfessionals}}">
                                                                                {{subPageList | arrayFilter:
                                                                                getStaticFieldDisplayName
                                                                                :companyPageSectionConstants.InvestmentProfessionals}}</span>
                                                                            <div class="line"></div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="invest-section-pb pt-2 pb-1"
                                                                    *ngIf="subPageList | arrayFilter: isFieldActive :companyPageSectionConstants.InvestmentProfessionals"
                                                                    [hideIfUserUnAuthorized]='{subFeatureId:subFeature.StaticDataBusinessDesciptionInvestmentProfessional,action:actions[actions.canView],id:id}'>
                                                                    <div class="card">
                                                                        <div class="card-body mb-0">
                                                                            <div
                                                                                class="table-responsive card-border static-table-border">
                                                                                <table
                                                                                    class='table mb-0 static-info-table'>
                                                                                    <thead>
                                                                                        <tr>
                                                                                            <ng-container
                                                                                                *ngFor="let investpro of investmentProfessionFieldList">
                                                                                                <th scope="col"
                                                                                                    class="text-align-left "
                                                                                                    *ngIf="investpro.isActive"
                                                                                                    title="{{investpro.displayName}}">
                                                                                                    {{investpro.displayName}}
                                                                                                </th>
                                                                                            </ng-container>
                                                                                        </tr>
                                                                                    </thead>
                                                                                    <tbody>
                                                                                        <tr
                                                                                            *ngFor="let employee of model.pcEmployees">
                                                                                            <ng-container
                                                                                                *ngFor="let investpro of investmentProfessionFieldList">
                                                                                                <td class="text-align-left"
                                                                                                    *ngIf="investpro.isActive">
                                                                                                    <span
                                                                                                        class="inv{{investpro?.fieldID}}"
                                                                                                        *ngIf="investpro.name ==companyInformationConstants.EmployeeName"
                                                                                                        title="{{ employee?.employeeName || 'NA'  }}">
                                                                                                        {{
                                                                                                        employee?.employeeName
                                                                                                        ||
                                                                                                        "NA" }}</span>
                                                                                                    <span
                                                                                                        class="inv{{investpro?.fieldID}}"
                                                                                                        *ngIf="investpro.name ==companyInformationConstants.Designation"
                                                                                                        title="{{ employee?.designation?.designation || 'NA' }}">{{
                                                                                                        employee?.designation?.designation
                                                                                                        || "NA" }}
                                                                                                    </span>
                                                                                                    <span
                                                                                                        class="inv{{investpro?.fieldID}}"
                                                                                                        *ngIf="investpro.name ==companyInformationConstants.Email"
                                                                                                        title="{{ employee?.emailId || 'NA' }}">
                                                                                                        {{
                                                                                                        employee?.emailId
                                                                                                        || "NA"
                                                                                                        }}</span>
                                                                                                    <span
                                                                                                        class="inv{{investpro?.fieldID}}"
                                                                                                        *ngIf="investpro.name ==companyInformationConstants.Education"
                                                                                                        title="{{ employee?.education || 'NA' }}">
                                                                                                        {{
                                                                                                        employee?.education
                                                                                                        ||
                                                                                                        "NA"
                                                                                                        }} </span>
                                                                                                    <span
                                                                                                        class="inv{{investpro?.fieldID}}"
                                                                                                        *ngIf="investpro.name ==companyInformationConstants.PastExperience"
                                                                                                        title="{{ employee?.pastExperience || 'NA'  }}">
                                                                                                        {{
                                                                                                        employee?.pastExperience
                                                                                                        ||
                                                                                                        "NA" }} </span>
                                                                                                </td>
                                                                                            </ng-container>
                                                                                        </tr>
                                                                                    </tbody>
                                                                                </table>
                                                                                <app-empty-state [isGraphImage]="false"
                                                                                    *ngIf="model?.pcEmployees==null || model.pcEmployees?.length==0"></app-empty-state>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="pcConstants.STATIC_INFORMATION">
                                                                <div class=" row mr-0 ml-0 pt-3 pb-2">
                                                                    <div
                                                                        class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 pr-3 pl-3 pt-0 pb-2">
                                                                        <div class="line-wrapper">
                                                                            <span class="mr-2 TextTruncate"
                                                                                title="{{subPageList | arrayFilter: getStaticFieldDisplayName :companyPageSectionConstants.CompanyInformation}}">{{subPageList
                                                                                | arrayFilter: getStaticFieldDisplayName
                                                                                :companyPageSectionConstants.CompanyInformation}}</span>
                                                                            <div class="line"></div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-12 col-md-12 col-lg-12 col-xl-12 col-sm-12 pr-3 Fund-section row mr-0 ml-0"
                                                                        [ngClass]="companyLogoModel.IsActive && !['NA', 'NA'].includes(companyLogoModel.Value) ? 'pl-3':''"
                                                                        [hideIfUserUnAuthorized]='{subFeatureId:subFeature.StaticDataBusinessDesciptionInvestmentProfessional,action:actions[actions.canView],id:id}'>
                                                                        <div class="col-md-3 col-lg-3 col-xl-3 col-sm-3 col-3 pl-0 pr-3"
                                                                            *ngIf="companyLogoModel.IsActive && !['NA', 'NA'].includes(companyLogoModel.Value)">
                                                                            <img class="companyLogoStyle"
                                                                                src={{companyLogoModel.Value}} alt="" />
                                                                        </div>
                                                                        <div class="pl-0 pr-0"
                                                                            [ngClass]="[companyLogoModel.IsActive && !['NA', 'NA'].includes(companyLogoModel.Value) ? 'static-section-pl col-md-9 col-lg-9 col-xl-9 col-sm-9 col-9':'col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12']">
                                                                            <div class="row mr-0 ml-0">
                                                                                <ng-container
                                                                                    *ngFor="let field of fieldValueList">
                                                                                    <div class="col-6 pl-0 pr-0 pt-2 mb-1 custom-static-label-padding"
                                                                                        *ngIf="field.isActive">
                                                                                        <div class="row mr-0 ml-0">
                                                                                            <div
                                                                                                class="pr-0 pl-0 col-4">
                                                                                                <div class="TextTruncate field-w"
                                                                                                    title="{{field.displayName}}">
                                                                                                    <span
                                                                                                        [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                        class="hColor static-label">
                                                                                                        {{field.displayName}}</span>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div
                                                                                                class="pr-0 pl-0 col-8 custom-static-value-padding">
                                                                                                <div
                                                                                                    class="TextTruncate field-w">
                                                                                                    <span
                                                                                                        title="{{field?.value}}"
                                                                                                        [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                        class=""
                                                                                                        *ngIf="field.name == companyInformationConstants.Website">
                                                                                                        <a [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                            id="{{field.fieldID}}"
                                                                                                            *ngIf="field?.value!=''"
                                                                                                            class="linkStyle"
                                                                                                            href="//{{field.value}}"
                                                                                                            target="_blank">{{field.value||
                                                                                                            "NA"}}</a>
                                                                                                        <span
                                                                                                            [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                            id="{{field.fieldID}}"
                                                                                                            *ngIf="field?.value==''">NA</span>
                                                                                                    </span>
                                                                                                    <span
                                                                                                        [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                        title="{{fundDetail?.fundName}}"
                                                                                                        *ngIf="field.name == companyInformationConstants.FundId">
                                                                                                        <a [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                            id="{{field.fieldID}}"
                                                                                                            class="linkStyle"
                                                                                                            *ngIf="fundDetail!=null"
                                                                                                            [routerLink]="['/fund-details', fundDetail?.encryptedFundId]">{{fundDetail?.fundName}}</a>
                                                                                                        <span
                                                                                                            [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                            id="{{field.fieldID}}"
                                                                                                            title="NA"
                                                                                                            *ngIf="fundDetail==null"
                                                                                                            class="vColor static-field-value">
                                                                                                            NA
                                                                                                        </span>
                                                                                                    </span>
                                                                                                    <span
                                                                                                        [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                        title="{{dealDetail?.dealCustomID}}"
                                                                                                        class=""
                                                                                                        *ngIf="field.name == companyInformationConstants.DealId">
                                                                                                        <a [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                            id="{{field.fieldID}}"
                                                                                                            class="linkStyle"
                                                                                                            *ngIf="dealDetail!=null"
                                                                                                            [routerLink]="['/deal-details', dealDetail?.encryptedDealID]">{{dealDetail?.dealCustomID}}</a>
                                                                                                        <span
                                                                                                            [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                            id="{{field.fieldID}}"
                                                                                                            title="NA"
                                                                                                            *ngIf="dealDetail==null"
                                                                                                            class="vColor static-field-value">
                                                                                                            NA
                                                                                                        </span>
                                                                                                    </span>
                                                                                                    <span
                                                                                                        id="{{field.fieldID}}"
                                                                                                        [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                        title="{{transform(field.value)||'NA'}}"
                                                                                                        class="vColor static-field-value"
                                                                                                        *ngIf="field.name == companyInformationConstants.CompanyStatus && field.subPageID !=6;else other_content; ">{{transform(field.value)||
                                                                                                        "NA"}}
                                                                                                    </span>
                                                                                                   
                                                                                                    <ng-container *ngIf="field.subPageID ==6">
                                                                                                        <span
                                                                                                        id="{{field.fieldID}}"
                                                                                                        [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                        title="{{transform(field.value)||'NA'}}"
                                                                                                        class="vColor static-field-value">
                                                                                                        <a class="static-highlight" *ngIf="field.name != companyInformationConstants.DealId" [routerLink]="['/deal-details', dealDetail?.encryptedDealID]">{{field.value | formatFieldValue: (field.isCustom ? field.displayName : field.name) :field.dataTypeId}}</a>
                                                                                                    </span>
                                                                                                    </ng-container>
                                                                                                    <ng-template
                                                                                                        #other_content>
                                                                                                        <span
                                                                                                            [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                            *ngIf="field.dataTypeId!=mDataTypes.List && field.subPageID !=6">
                                                                                                            <span
                                                                                                                [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                                id="{{field.fieldID}}"
                                                                                                                title="{{field.value|| 'NA'}}"
                                                                                                                class="vColor static-field-value"
                                                                                                                *ngIf="field.name !== companyInformationConstants.Website && field.name !== companyInformationConstants.DealId && field.name !== companyInformationConstants.FundId && (field.dataTypeId==mDataTypes.FreeText||field.dataTypeId==0||field.dataTypeId==mDataTypes.Date)">
                                                                                                                {{field.value||"NA"}}
                                                                                                            </span>
                                                                                                            <span
                                                                                                                [ngClass]="[field.isHighLight ? 'static-highlight' : '', 'default-text-color']"
                                                                                                                id="{{field.fieldID}}"
                                                                                                                *ngIf="field.dataTypeId==mDataTypes.CurrencyValue"
                                                                                                                [innerHtml]="isNumberCheck(field.value) ? (field.value | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : field.value||'NA'">
                                                                                                            </span>
                                                                                                            <span
                                                                                                                [ngClass]="[field.isHighLight ? 'static-highlight' : '', 'default-text-color']"
                                                                                                                id="{{field.fieldID}}"
                                                                                                                *ngIf="field.dataTypeId==mDataTypes.Number"
                                                                                                                [innerHtml]="isNumberCheck(field.value) ? (field.value | number:'1.0-0' | minusSignToBrackets: '') : field.value||'NA'">
                                                                                                            </span>
                                                                                                            <span
                                                                                                                [ngClass]="[field.isHighLight ? 'static-highlight' : '', 'default-text-color']"
                                                                                                                id="{{field.fieldID}}"
                                                                                                                *ngIf="field.dataTypeId==mDataTypes.Multiple">
                                                                                                                {{isNumberCheck(field.value)
                                                                                                                ?
                                                                                                                (field.value
                                                                                                                |
                                                                                                                number:
                                                                                                                NumberDecimalConst.multipleDecimal)+'x'
                                                                                                                :
                                                                                                                field.value||"NA"}}
                                                                                                            </span>
                                                                                                            <span
                                                                                                                [ngClass]="[field.isHighLight ? 'static-highlight' : '', 'default-text-color']"
                                                                                                                id="{{field.fieldID}}"
                                                                                                                *ngIf="field.dataTypeId==mDataTypes.Percentage"
                                                                                                                [innerHtml]="isNumberCheck(field.value) ? (field.value | number: NumberDecimalConst.percentDecimal | minusToBracketsWithPercentage: '%') : field.value||'NA'">
                                                                                                            </span>
                                                                                                        </span>
                                                                                                        <span
                                                                                                            [ngClass]="[field.isHighLight ? 'static-highlight' : '', 'default-text-color']"
                                                                                                            *ngIf="field.dataTypeId===mDataTypes.List && field.subPageID !=6">
                                                                                                            <div [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                                id="{{field.fieldID}}"
                                                                                                                *ngIf="(customPortfolioGroupList|pcArrayFilter:field).length==0">
                                                                                                                NA</div>
                                                                                                            <div [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                                id="{{field.fieldID}}"
                                                                                                                *ngIf="(customPortfolioGroupList|pcArrayFilter:field).length>0">
                                                                                                                <li [ngClass]="field.isHighLight ? 'static-highlight' :''"
                                                                                                                    class="TextTruncate list-padding"
                                                                                                                    title="{{custom.groupName|| 'NA'}}"
                                                                                                                    *ngFor="let custom of customPortfolioGroupList|pcArrayFilter:field">
                                                                                                                    {{custom.groupName||"NA"}}
                                                                                                                </li>
                                                                                                            </div>
                                                                                                        </span>
                                                                                                    </ng-template>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </ng-container>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="row mr-0 ml-0 pt-3 pb-3"
                                                                    *ngIf="businessModel.IsActive">
                                                                    <div class="col-sm-12 pr-3 pl-3">
                                                                        <div class="line-wrapper">
                                                                            <span class="mr-2 TextTruncate"
                                                                                title="{{businessModel.DisplayName}}">{{businessModel.DisplayName}}</span>
                                                                            <div class="line"></div>
                                                                        </div>
                                                                        <div class="chart-content">
                                                                            <!-- The stripHtmlTags function is used to remove any HTML tags from businessModel.Value before displaying it. -->
                                                                            <div id="businessDescId"
                                                                                [hideIfUserUnAuthorized]='{subFeatureId:subFeature.StaticDataBusinessDesciptionInvestmentProfessional,action:actions[actions.canView],id:id}'
                                                                                class="statit-desc pr-0 pl-0 pt-3 TextTruncate"
                                                                                title="{{stripHtmlTags(businessModel.Value) || 'NA'}}">
                                                                                {{stripHtmlTags(businessModel.Value) ||
                                                                                "NA"}}</div>
                                                                            <div class="row view-auth"
                                                                                *ngIf="!isCompanyInfo">
                                                                                <div class="col-12 text-center mt-5">
                                                                                    <app-unauthorizedacces class="mt-2"
                                                                                        [isHideAuthImage]=false>
                                                                                    </app-unauthorizedacces>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </ng-container>
                                                        </ng-container>
                                                    </ng-container>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </ng-template>
                <ng-template #keyPerformanceIndicatorTemplate>
                    <div class="row performance-section pt-3 mr-0 ml-0" *ngIf="tabList.length>0">
                        <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0">
                            <div class="performance-header pt-0 pb-2 TextTruncate"
                                title="{{subPageList | arrayFilter: getStaticFieldDisplayName :companyPageSectionConstants.KPI}}">
                                {{subPageList | arrayFilter: getStaticFieldDisplayName
                                :companyPageSectionConstants.KPI}}
                            </div>
                        </div>
                        <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 outer-section pl-0 pr-0">
                            <div
                                class="tab-section-div panel-heading pr-0 pl-0 custom-tab-panel-heading pb-0 border-bottom-0 panel-title custom-tabs custom-mat-tab">
                                <nav mat-tab-nav-bar [tabPanel]="tabPanel" *ngIf="tabList?.length > 0"
                                    id="kpi-tab-container">
                                    <a id="{{truncate(tab?.name, 26)}}" mat-tab-link [disableRipple]="true"
                                        *ngFor="let tab of tabList;" (click)="selectTab(tab)" [active]="tab.active"
                                        class="TextTruncate" title="{{tab?.tabAliasName}}">
                                        {{truncate(tab?.tabAliasName, 26)}} </a>
                                </nav>
                            </div>
                            <div class="content-bg">
                                <mat-tab-nav-panel #tabPanel>
                                    <div *ngIf="tabName=='Operational'">
                                        <app-operational-kpi-beta *ngIf="isOperationalKPI" [modelList]="model"
                                            id="pc-operational-kpi" [pageConfigData]="subSectionFields"
                                            [operationalKPIPermissions]="operationalKPIPermission"></app-operational-kpi-beta>
                                        <div class="row view-auth" *ngIf="!isOperationalKPI">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="tabName=='Company'">
                                        <portfolio-company-kpi *ngIf="isCompanyKPI"
                                            [companyKPIPermissions]="companyKPIPermission"
                                            [events]="eventsSubject.asObservable()" [model]=model
                                            [pageConfigData]="subSectionFields">
                                        </portfolio-company-kpi>
                                        <div class="row view-auth" *ngIf="!isCompanyKPI">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="tabName=='Impact'">
                                        <portfolio-impact-kpi *ngIf="isImpactKPI"
                                            [impactKPIPermissions]="impactKPIPermission" [modelList]="model"
                                            [pageConfigData]="subSectionFields">
                                        </portfolio-impact-kpi>
                                        <div class="row view-auth" *ngIf="!isImpactKPI">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="tabName=='Trading Records'">
                                        <app-master-kpi-beta [kpiName]="'TradingRecords'" *ngIf="isTradingRecord"
                                            [tradingRecordPermissions]="tradingRecordPermission" [modelList]="model"
                                            [pageConfigData]="subSectionFields"></app-master-kpi-beta>
                                        <div class="row view-auth" *ngIf="!isTradingRecord">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="tabName=='Credit'">
                                        <app-master-kpi-beta [kpiName]="'CreditKpi'" *ngIf="isCreditKPI"
                                            [modelList]="model" [pageConfigData]="subSectionFields"
                                            [creditKPIPermissions]="creditKPIPermission"></app-master-kpi-beta>
                                        <div class="row view-auth" *ngIf="!isCreditKPI">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="tabName=='Custom Table1'">
                                        <app-master-kpi-beta [kpiName]="'CustomTable1'" *ngIf="isCustomTable1"
                                            [modelList]="model" [pageConfigData]="subSectionFields"
                                            [customKPI1Permission]="customKPI1Permission"></app-master-kpi-beta>
                                        <div class="row view-auth" *ngIf="!isCustomTable1">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="tabName=='Custom Table2'">
                                        <app-master-kpi-beta [kpiName]="'CustomTable2'" *ngIf="isCustomTable2"
                                            [modelList]="model" [pageConfigData]="subSectionFields"
                                            [customKPI2Permission]="customKPI2Permission"></app-master-kpi-beta>
                                        <div class="row view-auth" *ngIf="!isCustomTable2">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="tabName=='Custom Table3'">
                                        <app-master-kpi-beta [kpiName]="'CustomTable3'" *ngIf="isCustomTable3"
                                            [modelList]="model" [pageConfigData]="subSectionFields"
                                            [customKPI3Permission]="customKPI3Permission"></app-master-kpi-beta>
                                        <div class="row view-auth" *ngIf="!isCustomTable3">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="tabName=='Custom Table4'">
                                        <app-master-kpi-beta [kpiName]="'CustomTable4'" *ngIf="isCustomTable4"
                                            [modelList]="model" [pageConfigData]="subSectionFields"
                                            [customKPI4Permission]="customKPI4Permission"></app-master-kpi-beta>
                                        <div class="row view-auth" *ngIf="!isCustomTable4">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="tabName=='Investment'">
                                        <app-investment-kpi-beta *ngIf="isInvestmentKPI" [modelList]="model"
                                            [investmentKPIPermissions]="investmentKPIPermission"
                                            [pageConfigData]="subSectionFields">
                                        </app-investment-kpi-beta>
                                        <div class="row view-auth" *ngIf="!isInvestmentKPI">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                </mat-tab-nav-panel>
                            </div>
                        </div>

                    </div>

                    <div class="row mr-0 ml-0" *ngIf="tabList.length>0">
                        <div class="col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                            <div class="p-rcalcLegend mb-0">
                                <span class="rcalcLegend"></span>
                                <span class="pl-2 pr-3">Converted Value</span>
                            </div>
                        </div>
                    </div>
                </ng-template>
                <ng-template #capTableTemplate>
                    <app-cap-table *ngIf="capTableConfig!=null && capTableConfig?.isCapTable && capTableViewPermission"
                        [modelList]="model" [config]="capTableConfig"
                        [capTablePermission]="capTablePermission"></app-cap-table>
                    <app-cap-table *ngIf="otherCapTableConfig!=null && otherCapTableConfig?.isCapTable && otherCapTableViewPermission"
                        [modelList]="model" [config]="otherCapTableConfig"
                        [capTablePermission]="otherCapTablePermission"></app-cap-table>
                </ng-template>

                <ng-template #companyFinancialsTemplate>
                    <app-financials-beta *ngIf="isFinancials && financialsViewPermission"
                        [pcCompanyId]="portfolioInfoSectionModel.portfolioCompanyID" [companyName]="model.companyName"
                        [subHeaderName]="subPageList | arrayFilter: getStaticFieldDisplayName :companyPageSectionConstants.Financials"
                        [reportingCurrencyCode]="model.reportingCurrencyDetail?.currencyCode"
                        [pageConfigData]="subSectionFields"
                        [financialsPermission]="financialsPermission"></app-financials-beta>
                </ng-template>
                <ng-template #commentaryTemplate>
                    <div *ngIf="isCommentarySection && commentaryViewPermission && commentFieldList?.length > 1"
                        [hideIfUserUnAuthorized]='{subFeatureId:subFeature.Commentary,action:actions[actions.canView],id:id}'>
                        <div class="row pt-3">
                            <div class="col-sm-12">
                                <div id="Commentary-section" class="commentary-section">
                                    <div class="row c-header align-items-center">
                                        <div class="col">
                                            <span class="Heading2-M text-truncate-width TextTruncate"
                                                title="{{subPageList | arrayFilter: getStaticFieldDisplayName :companyPageSectionConstants.Commentary}}">
                                                {{subPageList | arrayFilter: getStaticFieldDisplayName
                                                :companyPageSectionConstants.Commentary}}
                                            </span>
                                        </div>
                                        <div class="col-auto d-flex align-items-center comm-per-cel">
                                            <div class="mr-2">
                                                <kendo-combobox id="commentary-period-month" [clearButton]="false"
                                                    [(ngModel)]="selectedCommentaryPeriod" #Period="ngModel"
                                                    [fillMode]="'flat'" name="Period"
                                                    class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32"
                                                    [size]="'medium'" [data]="commentaryPeriodList"
                                                    [valuePrimitive]="false" textField="name" valueField="value"
                                                    (valueChange)="onChangeCommentaryPeriod()">
                                                </kendo-combobox>
                                            </div>
                                            <div class="comm-cel-Q" *ngIf="showQuarterYearControl">
                                                <quarter-year-control class="quarter-year-control"
                                                    [isFutureYearRequired]="true" [ControlName]="'pcYear'"
                                                    [QuarterYear]="YearQuarter"
                                                    (onCalendarYearPicked)="QuarterYear($event)">
                                                </quarter-year-control>
                                            </div>
                                            <div class="comm-cel" *ngIf="!showQuarterYearControl">
                                                <kendo-datepicker id="commentary-period-year" #shortDate
                                                    [fillMode]="'flat'" format="{{commentaryCalendarFormat}}"
                                                    [value]="commentaryCalendarValue"
                                                    activeView="{{commentaryCalendarActiveView}}"
                                                    bottomView="{{commentaryCalendarActiveView}}" calendarType="classic"
                                                    [max]="today" (valueChange)="onDateChange($event)"
                                                    [popupSettings]="{ popupClass: 'commentary-period-year-pc' }">
                                                </kendo-datepicker>
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pr-0 pl-0 content-body-r-section panel-section">
                                        <div class="row mr-0 ml-0 panel-row" *ngIf="commentFieldList?.length > 1">
                                            <div appScrollBorder
                                                class="col-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 g-panel-header pr-0 pl-0"
                                                [ngClass]="commentField.isExpanded ? 'panel-h-auto' :'panel-h'"
                                                *ngFor="let commentField of commentFieldList; let i = index;">
                                                <div class="c-p-header d-inline-block TextTruncate custom-text S-M header-block"
                                                    [ngClass]="{
                                                                'panel-h-margin': commentField.isExpanded,
                                                                'no-boarder': i === commentFieldList?.length - 1 && !commentField.isExpanded
                                                              }" [style.width.%]="100"
                                                    *ngIf="commentField.displayName !== commentaryPeriodConst">
                                                    <div class="d-inline">
                                                        <a (click)="expandCommentartPanel(commentField)"> <img
                                                                src="assets/dist/images/{{commentField.isExpanded ? 'arrow-down.svg' :'arrow-left.svg'}}"
                                                                alt="Sort left" /> </a>
                                                    </div>
                                                    <div title="{{commentField.displayName}}" class="d-inline pl-2 S-M">
                                                        {{commentField.displayName}}</div>
                                                    <div class="float-right" *ngIf="!commentField.isEdit && !isWorkflow">
                                                        <a (click)="editCommentry($event, i)" title="Edit"
                                                            id="comment-edit-anchor-{{i}}"><img alt=""
                                                                src="assets/dist/images/EditIcon.svg"
                                                                id="comment-edit-img-{{i}}"></a>
                                                    </div>
                                                </div>
                                                <div class="comment-editor-h mr-0 ml-0"
                                                    [ngClass]="!commentField.isEdit ? 'only-text' : ''"
                                                    *ngIf="commentField.isExpanded && commentField.displayName !== commentaryPeriodConst">
                                                    <p [ngClass]="'previewEditorWrapper ql-editor pl-0'"
                                                        class="clickable-area"
														[innerHTML]="commentField.value | sanitizeHtml"
                                                        [attr.style]="getCommentaryStyles(commentField.value)"
                                                        *ngIf="!commentField.isEdit && !(commentField.value === '' || commentField.value === undefined || commentField.value == null)">
                                                    </p>
                                                    <app-custom-quill-editor
                                                        (onKeydownChange)="onKeydownChangeEvent($event)"
                                                        *ngIf="commentField.isEdit" class="comment-editor pc-comment"
                                                        [editorPlaceholder]="editorPlaceholder" [commentIndex]="i"
                                                        [(ngModel)]="commentField.value"
                                                        (commentIndextrigger)="commentIndextriggerEvent($event)"
                                                        [useDivAsBlockTag]="true"></app-custom-quill-editor>
                                                    <div class="row mr-0 ml-0 no-data-container"
                                                        *ngIf="commentField?.noData">
                                                        <ng-container
                                                            *ngTemplateOutlet="noDataTemplate; context: { message: 'No commentary available for this section', subMessage: 'Click on the edit icon at top to start adding commentary for this period' ,icon:'no-content-lp-report', commentIndex: i }"></ng-container>
                                                    </div>
                                                </div>
                                                <div [ngClass]="{'button-footer': true, 'conditional-button-footer': commentFieldList?.length - 1 == i}"
                                                    *ngIf="commentField.displayName !== commentaryPeriodConst && commentField.isExpanded && !commentField?.noData && !isWorkflow">
                                                    <div class="float-right btn-comment-section">
                                                        <button (click)="cancelCommentry($event,i)" kendoButton
                                                            [disabled]="commentField.saveDisabled"
                                                            class="kendo-custom-button Body-R button-action apply-btn mr-2 btn-width-102"
                                                            fillMode="outline" themeColor="primary">Cancel</button>
                                                        <button (click)="saveCommentry($event,i)"
                                                            [disabled]="commentField.saveDisabled" kendoButton
                                                            class="kendo-custom-button button-action Body-R apply-btn btn-width-102"
                                                            themeColor="primary">Save</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <app-foot-note *ngIf="isCommentaryFootnote" moduleId="1001"
                                            [companyId]="model?.portfolioCompanyID"
                                            class="comm-footnote custom-quill-editor"></app-foot-note>
                                    </div>
                                    <confirm-modal *ngIf="showConfirModel" primaryButtonName="Yes"
                                        secondaryButtonName="No" modalTitle="Confirm"
                                        (primaryButtonEvent)="YesOn($event)"
                                        (secondaryButtonEvent)="NoOnCancel($event)">Are you sure, do you want to
                                        cancel?</confirm-modal>
                                </div>
                            </div>
                        </div>
                    </div>
                </ng-template>
                <ng-template #otherKpisTemplate>
                    <div class="row performance-section pt-3 mr-0 ml-0" *ngIf="otherKpisTabList.length>0">
                        <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0">
                            <div class="performance-header pt-0 pb-2 TextTruncate"
                                title="{{subPageList | arrayFilter: getStaticFieldDisplayName :companyPageSectionConstants.OtherKpi}}">
                                {{subPageList | arrayFilter: getStaticFieldDisplayName
                                :companyPageSectionConstants.OtherKpi}}
                            </div>
                        </div>
                        <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 outer-section pl-0 pr-0">
                            <div
                                class="tab-section-div panel-heading pr-0 pl-0 custom-tab-panel-heading pb-0 border-bottom-0 panel-title custom-tabs custom-mat-tab">
                                <nav mat-tab-nav-bar [tabPanel]="tabPanel" *ngIf="otherKpisTabList?.length > 0"
                                    id="kpi-tab-container">
                                    <a id="{{truncate(tab?.tabAliasName, 26)}}" mat-tab-link [disableRipple]="true"
                                        *ngFor="let tab of otherKpisTabList;" (click)="selectOtherKPIsTab(tab)"
                                        [active]="tab.active" class="TextTruncate" title="{{tab?.tabAliasName}}">
                                        {{truncate(tab?.tabAliasName, 26)}} </a>
                                </nav>
                            </div>
                            <div class="content-bg">
                                <mat-tab-nav-panel #tabPanel>
                                    <div *ngIf="otherKPIsTabName=='Other KPI1'">
                                        <app-other-kpi [kpiName]="'OtherKPI1'" *ngIf="isOtherKPI1"
                                            [modelList]="otherKPIsModel" [pageConfigData]="subSectionFields"
                                            [otherKPI1Permission]="otherKPI1Permission"></app-other-kpi>
                                        <div class="row view-auth" *ngIf="!isOtherKPI1">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="otherKPIsTabName=='Other KPI2'">
                                        <app-other-kpi [kpiName]="'OtherKPI2'" *ngIf="isOtherKPI2"
                                            [modelList]="otherKPIsModel" [pageConfigData]="subSectionFields"
                                            [otherKPI2Permission]="otherKPI2Permission"></app-other-kpi>
                                        <div class="row view-auth" *ngIf="!isOtherKPI2">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="otherKPIsTabName=='Other KPI3'">
                                        <app-other-kpi [kpiName]="'OtherKPI3'" *ngIf="isOtherKPI3"
                                            [modelList]="otherKPIsModel" [pageConfigData]="subSectionFields"
                                            [otherKPI3Permission]="otherKPI3Permission"></app-other-kpi>
                                        <div class="row view-auth" *ngIf="!isOtherKPI3">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="otherKPIsTabName=='Other KPI4'">
                                        <app-other-kpi [kpiName]="'OtherKPI4'" *ngIf="isOtherKPI4"
                                            [modelList]="otherKPIsModel" [pageConfigData]="subSectionFields"
                                            [otherKPI4Permission]="otherKPI4Permission"></app-other-kpi>
                                        <div class="row view-auth" *ngIf="!isOtherKPI4">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="otherKPIsTabName=='Other KPI5'">
                                        <app-other-kpi [kpiName]="'OtherKPI5'" *ngIf="isOtherKPI5"
                                            [modelList]="otherKPIsModel" [pageConfigData]="subSectionFields"
                                            [otherKPI5Permission]="otherKPI5Permission"></app-other-kpi>
                                        <div class="row view-auth" *ngIf="!isOtherKPI5">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="otherKPIsTabName=='Other KPI6'">
                                        <app-other-kpi [kpiName]="'OtherKPI6'" *ngIf="isOtherKPI6"
                                            [modelList]="otherKPIsModel" [pageConfigData]="subSectionFields"
                                            [otherKPI6Permission]="otherKPI6Permission"></app-other-kpi>
                                        <div class="row view-auth" *ngIf="!isOtherKPI6">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="otherKPIsTabName=='Other KPI7'">
                                        <app-other-kpi [kpiName]="'OtherKPI7'" *ngIf="isOtherKPI7"
                                            [modelList]="otherKPIsModel" [pageConfigData]="subSectionFields"
                                            [otherKPI7Permission]="otherKPI7Permission"></app-other-kpi>
                                        <div class="row view-auth" *ngIf="!isOtherKPI7">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="otherKPIsTabName=='Other KPI8'">
                                        <app-other-kpi [kpiName]="'OtherKPI8'" *ngIf="isOtherKPI8"
                                            [modelList]="otherKPIsModel" [pageConfigData]="subSectionFields"
                                            [otherKPI8Permission]="otherKPI8Permission"></app-other-kpi>
                                        <div class="row view-auth" *ngIf="!isOtherKPI8">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="otherKPIsTabName=='Other KPI9'">
                                        <app-other-kpi [kpiName]="'OtherKPI9'" *ngIf="isOtherKPI9"
                                            [modelList]="otherKPIsModel" [pageConfigData]="subSectionFields"
                                            [otherKPI9Permission]="otherKPI9Permission"></app-other-kpi>
                                        <div class="row view-auth" *ngIf="!isOtherKPI9">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="otherKPIsTabName=='Other KPI10'">
                                        <app-other-kpi [kpiName]="'OtherKPI10'" *ngIf="isOtherKPI10"
                                            [modelList]="otherKPIsModel" [pageConfigData]="subSectionFields"
                                            [otherKPI10Permission]="otherKPI10Permission"></app-other-kpi>
                                        <div class="row view-auth" *ngIf="!isOtherKPI10">
                                            <div class="col-12 text-center mt-5">
                                                <app-unauthorizedacces class="mt-2" [isHideAuthImage]=false>
                                                </app-unauthorizedacces>
                                            </div>
                                        </div>
                                    </div>
                                </mat-tab-nav-panel>
                            </div>
                        </div>
                    </div>
                </ng-template>

                <ng-template #sustainableDevelopmentGoalsImagesTemplate>
                    <app-sdg-images-upload *ngIf="isSDGImages" [sdgTitle]="sdgTitle"
                        [pcId]="model.portfolioCompanyID"></app-sdg-images-upload>
                </ng-template>

                <ng-template #valuationSummaryTemplate>
                    <app-valuation-summary [valuationSummary]="valuationSummary" [reportingCurrencyCode]="model.reportingCurrencyDetail?.currencyCode" [pcId]="model.portfolioCompanyID" [fundId]="this.fundDetail?.encryptedFundId"></app-valuation-summary>
                </ng-template>
            </div>
        </div>
    </div>
</div>
<ng-template #noDataTemplate let-message="message" let-subMessage="subMessage" let-commentIndex="commentIndex"
    let-icon="icon">
    <div
        class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 no-content-section">
        <div class="text-center">
            <img src="assets/dist/images/{{icon}}.svg" alt="No Content" class="no-content-image">
        </div>
        <div class="text-center no-content-text pt-3 pb-2 Body-R no-comment-font">
            {{ message }}
        </div>
        <div class="text-center no-content-sub template-text break-word Body-R no-comment-font">
            {{ subMessage }}
        </div>
    </div>
</ng-template>
<ng-template #noImageTemplate let-message="message" let-icon="icon">
    <div class="col-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 no-content-section">
        <div class="text-center">
            <img src="assets/dist/images/{{icon}}.svg" alt="No Content" class="no-content-image">
        </div>
        <div class="text-center no-content-text pt-3 pb-2 Body-M no-comment-font">
            {{ message }}
        </div>
    </div>
</ng-template>
