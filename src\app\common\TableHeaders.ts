export function getKpiHeaders () {
    let headers = [
        { field: 'kpi', header: 'KPI Name', sort: true, valueCustomClass: 'textEllipsis',headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
        { field: 'kpiInfoType', header: 'Information', sort: true, valueCustomClass: '', headerWidthClass:'KpiInfo KpiHeaders', valueWidthClass:'KpiValues KpiInfo' },
        { field: 'methodologyName', header: 'FX Methodology', sort: true, valueCustomClass: '', headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' }
      ];
      return headers;
}
export function getInvestmentKpiHeaders () {
  let headers = [
      { field: 'kpi', header: 'KPI Name', sort: true, valueCustomClass: 'textEllipsis',headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
      { field: 'kpiInfoType', header: 'Information', sort: true, valueCustomClass: '', headerWidthClass:'KpiInfo KpiHeaders', valueWidthClass:'KpiValues KpiInfo' },
      { field: 'methodologyName', header: 'FX Methodology', sort: true, valueCustomClass: '', headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' }
    ];
    return headers;
}

export function getOperationalKpiHeaders () {
  let headers = [
      { field: 'kpi', header: 'KPI Name', sort: true, valueCustomClass: 'textEllipsis',headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
      { field: 'kpiInfoType', header: 'Information', sort: true, valueCustomClass: '', headerWidthClass:'KpiInfo KpiHeaders', valueWidthClass:'KpiValues KpiInfo' },
      { field: 'methodologyName', header: 'FX Methodology', sort: true, valueCustomClass: '', headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' }
    ];
    return headers;
}

export function getBalanceSheetKpiHeaders () {
  let headers = [
      { field: 'balanceSheetLineItem', header: 'KPI Name', sort: true, valueCustomClass: 'textEllipsis',headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
      { field: 'kpiInfoType', header: 'Information', sort: true, valueCustomClass: '', headerWidthClass:'KpiInfo KpiHeaders', valueWidthClass:'KpiValues KpiInfo' },
      { field: 'methodologyName', header: 'FX Methodology', sort: true, valueCustomClass: '', headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
    ];
    return headers;
}

export function getProfitAndLossKpiHeaders () {
  let headers = [
      { field: 'profitAndLossLineItem', header: 'KPI Name', sort: true, valueCustomClass: 'textEllipsis',headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
      { field: 'kpiInfoType', header: 'Information', sort: true, valueCustomClass: '', headerWidthClass:'KpiInfo KpiHeaders', valueWidthClass:'KpiValues KpiInfo' },
      { field: 'methodologyName', header: 'FX Methodology', sort: true, valueCustomClass: '', headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
    ];
    return headers;
}

export function getCashflowKpiHeaders () {
  let headers = [
      { field: 'cashFlowLineItem', header: 'KPI Name', sort: true, valueCustomClass: 'textEllipsis',headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
      { field: 'kpiInfoType', header: 'Information', sort: true, valueCustomClass: '', headerWidthClass:'KpiInfo KpiHeaders', valueWidthClass:'KpiValues KpiInfo' },
      { field: 'methodologyName', header: 'FX Methodology', sort: true, valueCustomClass: '', headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
    ];
    return headers;
}
export function getMasterKpiHeaders () {
  let headers = [
      { field: 'kpi', header: 'KPI Name', sort: true, valueCustomClass: 'textEllipsis', headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
      { field: 'kpiInfoType', header: 'Information', sort: true, valueCustomClass: '', headerWidthClass:'KpiInfo KpiHeaders', valueWidthClass:'KpiValues KpiInfo' },
      { field: 'methodologyName', header: 'FX Methodology', sort: true, valueCustomClass: '', headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
    ];
    return headers;
}

export function getESGKpiHeaders () {
  let headers = [
      { field: 'kpi', header: 'KPI Name', sort: true, valueCustomClass: 'textEllipsis',headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
      { field: 'kpiInfoType', header: 'Information', sort: true, valueCustomClass: '', headerWidthClass:'KpiInfo KpiHeaders', valueWidthClass:'KpiValues KpiInfo' },
      { field: 'methodologyName', header: 'FX Methodology', sort: true, valueCustomClass: '', headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
    ];
    return headers;
}
export function getCapTableHeaders () {
  let headers = [
      { field: 'kpi', header: 'KPI Name', sort: true, valueCustomClass: 'textEllipsis', headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
      { field: 'kpiInfoType', header: 'Information', sort: true, valueCustomClass: '', headerWidthClass:'KpiInfo KpiHeaders', valueWidthClass:'KpiValues KpiInfo' },
      { field: 'methodologyName', header: 'FX Methodology', sort: true, valueCustomClass: '', headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
      { field: 'kpiType', header: 'Grid Type', sort: true, valueCustomClass: '', headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' }
    ];
    return headers;
}
export function getMonthlyReportHeaders () {
  let headers = [
      { field: 'kpi', header: 'KPI Name', sort: true, valueCustomClass: 'textEllipsis', headerWidthClass:'KpiHeaders', valueWidthClass:'KpiValues' },
      { field: 'kpiInfoType', header: 'Information', sort: true, valueCustomClass: '', headerWidthClass:'KpiInfo KpiHeaders', valueWidthClass:'KpiValues KpiInfo' }
    ];
    return headers;
}