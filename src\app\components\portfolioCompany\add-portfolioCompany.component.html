﻿<div  class="row main-row ">
    <div class="col-md-12 pl-0 pr-0 mb-5">
        <div class="card-body">
            <form name="form" class="mt-0 pt-0 pb-0 pl-0 pr-0" (ngSubmit)="savePortfolioCompany(f)" #f="ngForm">
                <div class="row mr-0 ml-0">
                    <div class="col-12 BusinessDescriptionlabel pl-0 pr-0 pb-2 TextTruncate"
                        title="Company Information">
                        <strong>Company Information</strong>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 bottom-spacing ">

                        <div class="row flexmoduleContainer pr-0 d-flex ">
                            <ng-container *ngFor="let staticField of pageFieldLists; let i=index">
                                <div class="col-sm-6 col-md-3  padding-card-rem"
                                    *ngIf="staticField.name == companyInformationConstants.CompanyName">
                                    <div class="TextTruncate Caption-M"
                                        [ngClass]="staticField.isMandatory ?  'required-field' : ''" for="CompanyName"
                                        title="{{staticField.displayName}}">
                                        {{staticField.displayName}}</div>
                                    <input type="text" class="form-control eachlabel-padding default-txt TextTruncate"
                                        name="CompanyName" [(ngModel)]="model.companyName" #companyName="ngModel"
                                        validateRequired required [placeholder]="'Enter '+staticField.displayName"
                                        validateBusinessName autocomplete="off" maxlength="500"
                                        (mouseout)="setMasterCompanyName()" (change)="setMasterCompanyName()" />

                                    <div *ngIf="(f.submitted || companyName?.dirty)" class="text-danger">
                                        <p *ngIf="companyName.errors?.pattern">
                                            Please enter valid company name</p>
                                    </div>
                                    <div *ngIf="(f.submitted || companyName?.dirty)" class="text-danger">
                                        <p *ngIf="companyName.errors?.required">
                                            Please enter company name</p>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-md-3  padding-card-rem"
                                    *ngIf="staticField.name == companyInformationConstants.MasterCompanyName">
                                    <div class="TextTruncate Caption-M"
                                        [ngClass]="staticField.isMandatory ?  'required-field' : ''"
                                        title="{{staticField.displayName}}">
                                        {{staticField.displayName}}</div>
                                    <input type="text" class="form-control eachlabel-padding default-txt TextTruncate"
                                        name="masterCompanyName" [(ngModel)]="model.masterCompanyName"
                                        #masterCompanyName="ngModel" [placeholder]="'Enter '+staticField.displayName"
                                        autocomplete="off" />
                                </div>
                                <div class="col-sm-6 col-md-3  padding-card-rem"
                                *ngIf="staticField.name == companyInformationConstants.CompanyLegalName">
                                <div class="TextTruncate Caption-M"
                                    [ngClass]="staticField.isMandatory ?  'required-field' : ''"
                                    title="{{staticField.displayName}}">
                                    {{staticField.displayName}}</div>
                                <input type="text" class="form-control eachlabel-padding default-txt TextTruncate"
                                    name="companyLegalName" [(ngModel)]="model.companyLegalName"
                                    #companyLegalName="ngModel" [placeholder]="'Enter '+staticField.displayName"
                                    autocomplete="off" />
                                </div>
                                <div class="col-sm-6 col-md-3  padding-card-rem"
                                *ngIf="staticField.name == dealDetailsConstants.PCInvestmentDate">
                                <div class="TextTruncate Caption-M"
                                    [ngClass]="staticField.isMandatory ?  'required-field' : ''"
                                    title="{{staticField.displayName}}">
                                    {{staticField.displayName}}</div>
                                    <kendo-datepicker calendarType="classic" class="k-picker-custom-flat k-datepicker-height-34" [format]="format"
                                        [fillMode]="'flat'" [placeholder]="'Select '+staticField.displayName" id="investmentDate" name="investmentDate"
                                        [(ngModel)]="model.investmentDate" [value]="getFormattedDate(model.investmentDate)"
                                        (valueChange)="onSelectDealInvestmentDate($event)" #investmentDate></kendo-datepicker>
                                </div>
                                <div class="col-sm-6 col-md-3 padding-card-rem"
                                    *ngIf="staticField.name == companyInformationConstants.Website">
                                    <div class="TextTruncate Caption-M"
                                        [ngClass]="staticField.isMandatory ?  'required-field' : ''" for="Website"
                                        title="{{staticField.displayName}}">
                                        {{staticField.displayName}}</div>
                                    <input type="text" class="form-control eachlabel-padding default-txt TextTruncate"
                                        name="Website" [(ngModel)]="model.website" #Website="ngModel" validateURL
                                        autocomplete="off" [placeholder]="'Enter '+staticField.displayName"
                                        maxlength="100" />
                                    <div *ngIf="Website.invalid && (Website.dirty ||f.submitted)" class="text-danger">
                                        <p *ngIf="Website.errors.validateURL">Please provide a valid website url
                                        </p>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-md-3 padding-card-rem"
                                    *ngIf="staticField.name == companyInformationConstants.CompanyStatus">
                                    <div class="TextTruncate Caption-M"
                                        [ngClass]="staticField.isMandatory ?  'required-field' : ''" for="status"
                                        title="{{staticField.displayName}}">
                                        {{staticField.displayName}}</div>
                                    <kendo-combobox [clearButton]="false" [valuePrimitive]="true"
                                        [kendoDropDownFilter]="filterSettings" [(ngModel)]="model.status"
                                        [fillMode]="'flat'" [filterable]="true" name="status" #status="ngModel"
                                        [virtual]="virtual"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                        [size]="'medium'" [data]="statusOptions" [filterable]="true" textField="text"
                                        valueField="value" [placeholder]="'Select '+staticField.displayName">
                                    </kendo-combobox>
                                    <div *ngIf="status.invalid && (status.dirty ||f.submitted)" class="text-danger">
                                        <div *ngIf="f.submitted && !status.valid" class="text-danger">Status is required
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-md-3 padding-card-rem"
                                    *ngIf="staticField.name ==companyInformationConstants.StockExchange_Ticker">
                                    <div class="TextTruncate Caption-M"
                                        [ngClass]="staticField.isMandatory ?  'required-field' : ''"
                                        for="StockExchangeTicker" title="{{staticField.displayName}}">
                                        {{staticField.displayName}}</div>
                                    <input type="text" class="form-control eachlabel-padding TextTruncate default-txt"
                                        [placeholder]="'Enter '+staticField.displayName" name="StockExchangeTicker"
                                        [(ngModel)]="model.stockExchange_Ticker" #StockExchangeTicker="ngModel"
                                        autocomplete="off" maxlength="1000" />
                                </div>

                                <div class="col-sm-6 col-md-3 padding-card-rem"
                                    *ngIf="staticField.name ==companyInformationConstants.Sector">
                                    <div class="TextTruncate Caption-M"
                                        [ngClass]="staticField.isMandatory ?  'required-field' : ''" for="sector"
                                        title="{{staticField.displayName}}">
                                        {{staticField.displayName}}</div>
                                    <div class="loading" *ngIf="masterModel==undefined"> <img alt=""
                                            src="assets/dist/images/loading.gif" class="loading-img" />
                                    </div>
                                    <div *ngIf="masterModel!=undefined">
                                        <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                            [(ngModel)]="model.sectorDetail" #sector="ngModel" [fillMode]="'flat'"
                                            [filterable]="true" name="sector" [virtual]="virtual"
                                            class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                            [size]="'medium'" [data]="masterModel.sectorList" [filterable]="true"
                                            textField="sector" valueField="sectorID"
                                            [placeholder]="'Select '+staticField.displayName"
                                            (valueChange)="onSectorChange()">
                                        </kendo-combobox>
                                        <div *ngIf="sector.invalid && (sector.dirty ||f.submitted)" class="text-danger">
                                            <div *ngIf="f.submitted && !sector.valid" class="text-danger">Sector is
                                                required
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-md-3 padding-card-rem"
                                    *ngIf="staticField.name ==companyInformationConstants.SubSector">
                                    <div class="TextTruncate Caption-M"
                                        [ngClass]="staticField.isMandatory ?  'required-field' : ''" for="subsector"
                                        title="{{staticField.displayName}}">
                                        {{staticField.displayName}}</div>
                                    <kendo-combobox [disabled]="model.sectorDetail==null || subSectorList.length ==0" [clearButton]="false"
                                        [kendoDropDownFilter]="filterSettings" [(ngModel)]="model.subSectorDetail"
                                        #subsector="ngModel" [fillMode]="'flat'" [filterable]="true" name="subsector"
                                        [virtual]="virtual"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                        [size]="'medium'" [data]="subSectorList" [filterable]="true"
                                        textField="subSector" valueField="subSectorID"
                                        [placeholder]="'Select '+staticField.displayName">
                                    </kendo-combobox>
                                    <div class="loading-input-controls" *ngIf="subSectorLoading"><i aria-hidden="true"
                                            class="fa fa-spinner fa-pulse fa-1x fa-fw"></i></div>
                                </div>
                                <div class="col-sm-6 col-md-3 padding-card-rem"
                                    *ngIf="staticField.name ==companyInformationConstants.Currency">
                                    <div class="required-field TextTruncate Caption-M"
                                        [ngClass]="staticField.isMandatory ?  'required-field' : ''" for="currency"
                                        title="{{staticField.displayName}}">
                                        {{staticField.displayName}}</div>
                                    <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                        [(ngModel)]="model.reportingCurrencyDetail" [required]="true"
                                        #reportingCurrency="ngModel" [fillMode]="'flat'" [filterable]="true"
                                        name="reportingCurrency" [virtual]="virtual"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                        [size]="'medium'" [data]="masterModel?.reportingCurrencyList"
                                        [filterable]="true" textField="currency" valueField="currencyID"
                                        [placeholder]="'Select '+staticField.displayName">
                                    </kendo-combobox>
                                    <div *ngIf="(f.submitted || reportingCurrency.dirty) && !reportingCurrency.valid"
                                        class="text-danger">Reporting
                                        Currency is required</div>
                                </div>
                                <div class="col-sm-6 col-md-3 padding-card-rem"
                                    *ngIf="staticField.name ==companyInformationConstants.FinancialYearEnd">
                                    <div class="TextTruncate Caption-M"
                                        [ngClass]="staticField.isMandatory ?  'required-field' : ''"
                                        for="fincancialyearend" title="{{staticField.displayName}}">
                                        {{staticField.displayName}}</div>
                                    <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                        [(ngModel)]="model.financialYearEnd" #financialYearEnd="ngModel"
                                        [fillMode]="'flat'" [filterable]="true" name="financialYearEnd"
                                        [virtual]="virtual"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                        [size]="'medium'" [data]="months" [filterable]="true" textField="name"
                                        valueField="value" [placeholder]="'Select '+staticField.displayName">
                                    </kendo-combobox>
                                    <div *ngIf="(f.submitted || financialYearEnd.dirty) && !financialYearEnd.valid"
                                        class="text-danger">Financial Year End is required</div>
                                </div>
                                <div class="col-sm-6 col-md-3 padding-card-rem"
                                    [ngClass]="tRecordTrigger.menuOpen?  'company-group-modal-open' : ''"
                                    *ngIf="staticField.name ==companyInformationConstants.CompanyGroupId">
                                    <div class="TextTruncate Caption-M"
                                        [ngClass]="staticField.isMandatory ?  'required-field' : ''"
                                        for="fincancialyearend" title="{{staticField.displayName}}">
                                        {{staticField.displayName}}</div>
                                    <span [matMenuTriggerFor]="menuGroup" #tRecordTrigger="matMenuTrigger">
                                        <input  type="text"
                                            class="form-control eachlabel-padding default-txt TextTruncate"
                                            placeholder="Add/Select Group" value="{{selectedGroup}}" name="groupName"
                                            autocomplete="off" maxlength="500" />
                                        <span class="k-i-chevron-down k-icon k-font-icon  company-group-icon"
                                            aria-hidden="true"></span>
                                    </span>
                                </div>
                                <div class="col-sm-6 col-md-3 padding-card-rem" *ngIf="false">
                                    <div class="col-12">
                                        <div class="desktop-m-3"></div>
                                        <label class="d-inline-block TextTruncate" for="IsActive"
                                            title="Active">Active</label>&nbsp;&nbsp;
                                        <input class="d-inline-block TextTruncate" type="checkbox" value=""
                                            name="IsActive" [(ngModel)]="model.isActive" #IsActive="ngModel">
                                    </div>
                                </div>
                                <div class="col-sm-6 col-md-3 padding-card-rem"
                                    *ngIf="staticField.name ==companyInformationConstants.Customfield">
                                    <div class="Caption-M TextTruncate"
                                        [ngClass]="staticField.isMandatory ?  'required-field' : ''" for="Customfield"
                                        title="{{staticField.displayName}}">
                                        {{staticField.displayName}} </div>
                                    <input placeholder="Enter {{staticField.displayName}}"
                                        *ngIf="(staticField.dataTypeId==mDataTypes.Number)&&(staticField.dataTypeId!=mDataTypes.FreeText)"
                                        autocomplete="off" type="text"
                                        class="form-control eachlabel-padding default-txt TextTruncate"
                                        [(ngModel)]="staticField.value == 'NA' ? '' : staticField.value"
                                        id="{{staticField.displayName}}" name="{{staticField.displayName}}"
                                        (input)="numbersOnlyValidator($event)"
                                        (blur)="AddOrUpdateCustomFieldValue($event,staticField)" />
                                    <input placeholder="Enter {{staticField.displayName}}"
                                        *ngIf="(staticField.dataTypeId==mDataTypes.CurrencyValue||staticField.dataTypeId==mDataTypes.Percentage||staticField.dataTypeId==mDataTypes.Multiple)&&(staticField.dataTypeId!=mDataTypes.FreeText)"
                                        autocomplete="off" type="text"
                                        class="form-control eachlabel-padding default-txt TextTruncate"
                                        [(ngModel)]="staticField.value == 'NA' ? '' : staticField.value"
                                        id="{{staticField.displayName}}" name="{{staticField.displayName}}"
                                        (input)="decimalNumbersOnlyValidator($event)" appTwoDigitDecimaNumber
                                        (blur)="AddOrUpdateCustomFieldValue($event,staticField)" />
                                  
                                    <kendo-datepicker calendarType="classic" class="k-picker-custom-flat k-datepicker-height-35"  [format]="format" [fillMode]="'flat'"
                                        placeholder="Enter {{staticField.displayName}}"
                                        *ngIf="staticField.dataTypeId===mDataTypes.Date&&(staticField.dataTypeId!==mDataTypes.FreeText)"
                                        id="{{staticField.displayName}}" name="{{staticField.displayName}}"
                                        [(ngModel)]="staticField.value == 'NA' ? '' : staticField.value" [value]="getFormattedDate(staticField.value)"
                                        (valueChange)="onChangeDate($event,staticField)" #{{staticField.name}}></kendo-datepicker>
                  
                                    <input *ngIf="staticField.dataTypeId===mDataTypes.FreeText" type="text"
                                        class="form-control eachlabel-padding default-txt TextTruncate"
                                        name="Customfield" [value]="staticField.value == 'NA' ? '' : staticField.value"
                                        (blur)="AddOrUpdateCustomFieldValue($event,staticField)"
                                        [placeholder]="'Enter '+staticField.displayName" autocomplete="off"
                                        maxlength="700" />
                                    <input *ngIf="staticField.dataTypeId===0" type="text"
                                        class="form-control eachlabel-padding default-txt TextTruncate"
                                        name="Customfield" [value]="staticField.value == 'NA' ? '' : staticField.value"
                                        (blur)="AddOrUpdateCustomFieldValue($event,staticField)"
                                        [placeholder]="'Enter '+staticField.displayName" autocomplete="off"
                                        maxlength="100" />
                                    <div *ngIf="staticField.dataTypeId===mDataTypes.List">
                                        <span [matMenuTriggerFor]="i">
                                            <input  type="text"
                                                class="form-control eachlabel-padding default-txt TextTruncate"
                                                placeholder="Add/Select List" name="pcCustomList" autocomplete="off"
                                                maxlength="500" />
                                            <span class="k-i-chevron-down k-icon k-font-icon  company-group-icon"
                                                aria-hidden="true"></span>
                                        </span>
                                        <mat-menu #i="matMenu" [hasBackdrop]="true"
                                            class="internal-report-preference-pop-up pc-group-pop-up"
                                            xPosition="before">
                                            <add-portfolioCustomFields [menuTrigger]="i"
                                                (updateList)="CustomListAddOrUpdate($event)"
                                                [indexFieldId]="staticField.fieldID"
                                                [customListDetails]="pcCustomListDetails"
                                                [fieldId]="staticField.fieldID" [companyId]="model.portfolioCompanyID"
                                                [displayName]="staticField.displayName"></add-portfolioCustomFields>
                                        </mat-menu>
                                    </div>
                                </div>
                            </ng-container>
                            <div class="row col-lg-12 col-md-12 col-sm-12 ">
                                <div class="col-sm-6 col-md-3 padding-card-rem pb-0" *ngIf="companyLogoModel.isActive">
                                    <div class="" for="currency"></div>
                                    <div class="border uploadCompanyLogo d-flex">
                                        <div id="add-pc-logo" class="textEllipsis uploadLogoIcon" (click)="file.click()"
                                            title={{browseText}}>
                                            <input class="hidefile TextTruncate"  id="add-pc-logo-input"
                                                accept=".png,.jpg,.jpeg,.gif,.tiff,.bpg" #file
                                                (click)="file.value = null" value=""
                                                (change)="fileBrowseHandler($event.target.files)" type="file">
                                            <img *ngIf="browseicon" class="pull-left broseicon"  id="add-pc-browse"
                                                [src]="'assets/dist/images/Iconmaterial-computer.svg'" alt="">
                                            <span class="browsetext TextTruncate"
                                                title="{{browseText}}">{{browseText}}</span>
                                        </div>
                                        <div class="d-inline-block">
                                            <div *ngIf="isFileUpload"
                                                class="loading-input-controls-manual pl-2 pull-left loading-file">
                                                <i aria-hidden="true"
                                                    class="fa fa-circle-o-notch fa-pulse fa-1x fa-fw"></i>
                                            </div>

                                            <div *ngIf="deleticon"
                                                class="loading-input-controls-manual pl-2 pull-left loading-file">
                                                <i aria-hidden="true" class="fa fa-check fa-1x fa-fw"></i>
                                            </div>

                                            <div id="add-pc-remove-logo" class="pl-2 pull-left" *ngIf="deleticon">
                                                <img (click)="deleteIconClick(filename)"  id="add-pc-logo-remove"
                                                    [src]="'assets/dist/images/ClearIcon.svg'" alt="">

                                            </div>


                                        </div>
                                    </div>

                                </div>
                            </div>

                        </div>


                        <div class="row second-spacing pb-2 pl-3" *ngIf="false">
                            <div class="col-12 BusinessDescriptionlabel pl-0 pr-0 TextTruncate"
                                title="{{businessModel.displayName}}">
                                {{businessModel.displayName}}
                            </div>
                        </div>
                        <div class="row" *ngIf="false">
                            <div class="col-12 d-flex  flexmoduleContainer BusinessDescription-css pt-3 pb-3 pr-3 pl-3">
                                <textarea type="text"
                                    class="BusinessDescriptionlabel BusinessDescription-css form-control" rows="4"
                                    name="BusinessDescription" [(ngModel)]="model.businessDescription"
                                    #BusinessDescription="ngModel" autocomplete="off"
                                    [placeholder]="'Enter '+businessModel.displayName+' here...'"></textarea>
                            </div>
                        </div>


                    </div>
                </div>

                <div class="row mr-0 ml-0" *ngIf="businessModel.isActive">
                    <div class="col-12 BusinessDescriptionlabel    pl-0 pr-0 pb-2 TextTruncate"
                        title="{{businessModel.displayName}}">
                        <strong>{{businessModel.displayName}}</strong>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 bottom-spacing">
                        <div class="row">
                            <div class="col-12 d-flex  businessContainer BusinessDescription-css pl- pb-3 ">
                                <textarea type="text"
                                    class="BusinessDescriptionlabel BusinessDescription-css form-control" rows="4"
                                    name="BusinessDescription" [(ngModel)]="model.businessDescription"
                                    #BusinessDescription="ngModel" autocomplete="off" maxlength="1500"
                                    [placeholder]="'Enter '+businessModel.displayName+' here...'"></textarea>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row mr-0 ml-0">
                    <div class="col-lg-12 col-md-12 col-sm-12 outer-section business-tab" *ngIf="tabList.length>0">
                        <div class="row  ">
                            <div class="panel panel-default pt-2 col-12 pl-0 pr-0 mr-0 tab-bg bg-color-none">
                                <div class="panel-heading ">
                                    <div class="panel-title">
                                        <ul class="nav nav-tabs ">
                                            <ng-container *ngFor="let tab of tabList">
                                                <li class="nav-item " role="presentation" *ngIf="tab.isActive">
                                                    <button (click)="selectTab(tab.name)"
                                                        class="nav-link  nav-custom nav-pc TextTruncate"
                                                        [ngClass]="tab.isSelected == true ? 'nav-active-tabcolor active' : 'nav-inactive-tabcolor'"
                                                        id="{{tab.displayName}}-tab" type="button" role="tab1"
                                                        aria-controls="home2" aria-selected="true"
                                                        title="{{tab.displayName}}">{{tab.displayName}}</button>
                                                </li>
                                            </ng-container>
                                        </ul>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="col-12 border tabradius tabs-shadow mb-4" *ngIf="isGeographic">
                        <form class="formcss" name="geographyForm" #geographyForm="ngForm">
                            <div class="" [ngClass]="{ 'has-error': f.submitted }">
                                <div *ngIf="locationModel==undefined"> <img src="assets/dist/images/loading.gif"
                                        class="loading-img" />
                                </div>
                                    <div class="row pl-3 pr-0 pt-3 location-form">
                                        <ng-container *ngFor="let locationConfig of locationFieldList">
                                            <div class="col-sm-6 col-md-6  col-lg-3 pl-0 pr-3 firm-custom-padding"
                                                *ngIf="locationConfig.name == 'Region' && locationConfig.isActive">
                                                <div class="TextTruncate Caption-M" for="region" title="{{locationConfig.displayName}}"> {{locationConfig.displayName}}</div>
                                                <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="geographicLocation.region" #region="ngModel"
                                                    [fillMode]="'flat'" [filterable]="true" name="region" [virtual]="virtual"
                                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                                    [data]="locationModel.regionList" [filterable]="true" textField="region" valueField="regionId"
                                                    [placeholder]="'Select '+locationConfig.displayName"  (valueChange)="handleRegionChange()">
                                                </kendo-combobox>
                                            </div>
                                            <div class="col-sm-6 col-md-6  col-lg-3 pl-0 pr-3 firm-custom-padding"
                                                *ngIf="locationConfig.name == 'Country' && locationConfig.isActive">
                                                <div class="TextTruncate Caption-M" for="country" title="{{locationConfig.displayName}}"> {{locationConfig.displayName}}</div>
                                                <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="geographicLocation.country" #country="ngModel"
                                                    [fillMode]="'flat'" [filterable]="true" name="country" [virtual]="virtual"
                                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                                    [data]="locationModel.countryList" [filterable]="true" textField="country" valueField="countryId"
                                                    [placeholder]="'Select '+locationConfig.displayName"  (valueChange)="handleCountryChange()">
                                                </kendo-combobox>
                                            </div>
                                            <div class="col-sm-6 col-md-6  col-lg-3 pl-0 pr-3 firm-custom-padding"
                                                *ngIf="locationConfig.name == 'State' && locationConfig.isActive">
                                                <div class="TextTruncate Caption-M" for="state" title="{{locationConfig.displayName}}"> {{locationConfig.displayName}}</div>
                                                <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="geographicLocation.state" #sate="ngModel"
                                                    [fillMode]="'flat'" [filterable]="true" name="state" [virtual]="virtual"
                                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                                    [data]="locationModel.stateList" [filterable]="true" textField="state" valueField="stateId"
                                                    [placeholder]="'Select '+locationConfig.displayName"   (valueChange)="handleStateChange()">
                                                </kendo-combobox>
                                            </div>
                                            <div class="col-sm-6 col-md-6  col-lg-3 pl-0 pr-3 firm-custom-padding"
                                                *ngIf="locationConfig.name == 'City' && locationConfig.isActive">
                                                <div class="TextTruncate Caption-M" for="city" title="{{locationConfig.displayName}}"> {{locationConfig.displayName}}</div>
                                                <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="geographicLocation.city" #city="ngModel"
                                                    [fillMode]="'flat'" [filterable]="true" name="city" [virtual]="virtual"
                                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                                    [data]="locationModel.cityList" [filterable]="true" textField="city" valueField="cityId"
                                                    [placeholder]="'Select '+locationConfig.displayName">
                                                </kendo-combobox>
                                            </div>
                                        </ng-container>
                                    </div>
                            </div>
                            <div class="row">
                                <div class="col-12 pl-3 top-spacing bottom-spacing pr-3">
                                    <div class="pull-right"> <a class="geography-clear pr-3 TextTruncate"
                                            (click)="clearGeographicLocation(geographyForm)" title="Clear" id="clear-pc-location">Clear
                                            all</a>
                                        <a class="nep-button nep-button-secondary TextTruncate" id="add-pc-location"
                                            (click)="addGeographicLocation(geographyForm)" title="Add Location">Add
                                            Location</a>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="row pb-0 border-top" *ngIf="model.geographicLocations?.length>0">
                            <kendo-grid [kendoGridBinding]="model.geographicLocations" scrollable="virtual"
                                [rowHeight]="44" [resizable]="true"
                                class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cab-table-grid">
                                <kendo-grid-column *ngFor="let geo of locationFieldList" [field]="geo.displayName"
                                    title="{{geo.displayName}}">
                                    <ng-template kendoGridHeaderTemplate>
                                        <span class="TextTruncate S-M">
                                            {{geo.displayName}}</span>
                                    </ng-template>
                                    <ng-template kendoGridCellTemplate let-location>
                                        <div class="text-align-left TextTruncate">
                                            <span *ngIf="geo.name ==companyInformationConstants.Region" title="{{
                                                                            location?.region!=null
                                                                            ?location?.region?.region:'N/A' }}">{{
                                                location?.region!=null
                                                ?location?.region?.region:"N/A" }}</span>
                                            <span *ngIf="geo.name ==companyInformationConstants.Country" title="{{
                                                                            location?.country?.country!=null?location?.country?.country:'N/A'
                                                                            }}">{{
                                                location?.country?.country!=null?location?.country?.country:"N/A"
                                                }}</span>
                                            <span *ngIf="geo.name ==companyInformationConstants.State"
                                                title="{{
                                                                            location?.state?.state!=null?location?.state?.state:'N/A' }}">{{
                                                location?.state?.state!=null?location?.state?.state:"N/A" }}</span>
                                            <span *ngIf="geo.name ==companyInformationConstants.City"
                                                title="{{
                                                                            location?.city?.city!=null?location?.city?.city:'N/A' }}"> {{
                                                location?.city?.city!=null?location?.city?.city:"N/A" }} </span>
                                        </div>
                                    </ng-template>
                                </kendo-grid-column>
                                <kendo-grid-column title="Remove">
                                    <ng-template kendoGridHeaderTemplate>
                                        <span class="TextTruncate S-M">
                                            Remove</span>
                                    </ng-template>
                                    <ng-template kendoGridCellTemplate let-location>
                                        <div class="text-center"> <a
                                                (click)="removeLocation(location.uniquelocationID)">
                                                <img title="Remove Location" [src]="'assets/dist/images/Trash-icon.svg'"
                                                    alt="">
                                            </a>
                                            <input type="hidden" value="{{location.uniquelocationID}}" />
                                        </div>
                                    </ng-template>
                                </kendo-grid-column>
                            </kendo-grid>
                        </div>
                    </div>
                </div>
                <div class="row mr-0 ml-0">
                    <div class="col-12 border tabradius tabs-shadow mb-4" *ngIf="isInvestment">
                        <form class="card-body-form-form" name="employeeForm" #employeeForm="ngForm">
                            <div class="row pt-3 pr-0 pl-3">
                                <ng-container *ngFor="let investProf of investmentProfessionFieldList">
                                    <div class="col-sm-6 col-md-3 padding-card-rem"
                                        *ngIf="investProf.name ==companyInformationConstants.EmployeeName">
                                        <div class="required-field TextTruncate Caption-M"
                                            title="{{investProf.displayName}}" for="EmployeeName">
                                            {{investProf.displayName}}</div>
                                        <input type="text"
                                            class="form-control  eachlabel-padding default-txt TextTruncate"
                                            name="EmployeeName" [(ngModel)]="pcEmployee.employeeName"
                                            #EmployeeName="ngModel" inputValidator="validatePersonName"
                                            autocomplete="off" maxlength="100"
                                            pattern="^(?!\s)(?!.*\s$)(?=.*[a-zA-Z0-9])[a-zA-Z0-9 '&()-\/,_ ]{2,}$"
                                            [placeholder]="'Enter '+investProf.displayName" validateRequired required />
                                        <div *ngIf="EmployeeName.invalid && (EmployeeName.dirty|| employeeForm.submitted)"
                                            class="text-danger">
                                            <p *ngIf="EmployeeName.errors.validateRequired">
                                                Employee name is required</p>
                                        </div>
                                        <div *ngIf="(f.submitted || EmployeeName?.dirty)" class="text-danger">
                                            <p *ngIf="EmployeeName.errors?.pattern">
                                                Please enter valid Employee name</p>
                                        </div>
                                    </div>

                                    <div class="col-sm-6 col-md-3 padding-card-rem"
                                        *ngIf="investProf.name ==companyInformationConstants.Designation">
                                        <div class="TextTruncate Caption-M" for="Designation"
                                            title="{{investProf.displayName}}">{{investProf.displayName}}</div>
                                        <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                            [(ngModel)]="pcEmployee.designation" #Designation="ngModel"
                                            [fillMode]="'flat'" [filterable]="true" name="Designation"
                                            [virtual]="virtual"
                                            class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                            [size]="'medium'" [data]="designationList" [filterable]="true"
                                            textField="designation" valueField="designationId"
                                            [placeholder]="'Select '+investProf.displayName">
                                        </kendo-combobox>
                                    </div>
                                    <div class="col-sm-6 col-md-3 padding-card-rem"
                                        *ngIf="investProf.name ==companyInformationConstants.Email">
                                        <div class="required-field TextTruncate Caption-M" for="EmployeeEmail"
                                            title="{{investProf.displayName}}">
                                            {{investProf.displayName}}</div>
                                        <input type="email"
                                            class="form-control eachlabel-padding default-txt TextTruncate"
                                            name="EmployeeEmail" [(ngModel)]="pcEmployee.emailId"
                                            #EmployeeEmail="ngModel"
                                            pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}" required
                                            [placeholder]="'Enter '+investProf.displayName" validateEmail
                                            autocomplete="off" maxlength="100" validateRequired />
                                        <div *ngIf="EmployeeEmail.invalid && (EmployeeEmail.dirty ||employeeForm.submitted)"
                                            class="text-danger">
                                            <p
                                                *ngIf="!EmployeeEmail.errors.validateRequired && EmployeeEmail.errors.validateEmail">
                                                Please enter valid email address</p>
                                            <p *ngIf="EmployeeEmail.errors.validateRequired">
                                                Employee email is required</p>
                                        </div>
                                        <div *ngIf="(f.submitted || EmployeeEmail?.dirty)" class="text-danger">

                                            <p *ngIf="EmployeeEmail.errors?.pattern">
                                                Please enter valid email address</p>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-md-3 padding-card-rem"
                                        *ngIf="investProf.name ==companyInformationConstants.Education">
                                        <div class="TextTruncate Caption-M" for="Education"
                                            title="{{investProf.displayName}}">{{investProf.displayName}}</div>
                                        <input type="text"
                                            class="form-control TextTruncate eachlabel-padding default-txt"
                                            name="Education" [(ngModel)]="pcEmployee.education" #Education="ngModel"
                                            [placeholder]="'Enter '+investProf.displayName"
                                            inputValidator="noSpecialChars" autocomplete="off" maxlength="100" />
                                    </div>
                                    <div class="col-sm-6 col-md-3 padding-card-rem"
                                        *ngIf="investProf.name ==companyInformationConstants.PastExperience">
                                        <div class="TextTruncate Caption-M" for="PastExperience"
                                            title="{{investProf.displayName}}">{{investProf.displayName}}
                                        </div>
                                        <input type="text"
                                            class="form-control TextTruncate eachlabel-padding default-txt"
                                            name="PastExperience" [(ngModel)]="pcEmployee.pastExperience"
                                            [placeholder]="'Enter '+investProf.displayName" #PastExperience="ngModel"
                                            inputValidator="noSpecialChars" autocomplete="off" maxlength="500" />

                                    </div>
                                </ng-container>
                            </div>

                            <div class="row">
                                <div class="col-12 pt-0 bottom-spacing pr-3">
                                    <div class="pull-right">
                                        <a class="geography-clear pr-3 TextTruncate"
                                            (click)="clearEmployees(employeeForm)" title="Clear">Clear all</a>
                                        <a class="nep-button nep-button-secondary TextTruncate"
                                            (click)="addEmployees(employeeForm)"
                                            title="{{employeeEditMode?'Update':'Add'}} Professional">{{employeeEditMode?'Update':'Add'}}
                                            Professional</a>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="row pt-0 pb-0 border-top" *ngIf="model.pcEmployees?.length>0">
                            <div class="col-12 col-sm-12 col-lg-12 col-xl-12 col-lg-12 pr-0 pl-0">
                                <kendo-grid
                                    class="k-grid-border-right-width  k-grid-outline-none custom-kendo-cab-table-grid"
                                    [data]="model.pcEmployees ?? []">
                                    <kendo-grid-column title="{{investpro.displayName}}"
                                        *ngFor="let investpro of investmentProfessionFieldList">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="TextTruncate S-M">{{investpro.displayName}}</span>
                                        </ng-template>
                                        <ng-template kendoGridCellTemplate let-employee>
                                            <div class="text-align-left TextTruncate">

                                                <span *ngIf="investpro.name ==companyInformationConstants.EmployeeName"
                                                    title="{{
                                                employee?.employeeName || 'N/A' }}">
                                                    {{
                                                    employee?.employeeName || "N/A" }}</span>
                                                <span *ngIf="investpro.name ==companyInformationConstants.Designation"
                                                    title="{{
                                                    employee?.designation?.designation || 'N/A' }} ">{{
                                                    employee?.designation?.designation || "N/A" }} </span>
                                                <span *ngIf="investpro.name ==companyInformationConstants.Email" title="{{
                                                employee?.emailId || 'N/A' }}"> {{
                                                    employee?.emailId || "N/A" }}</span>
                                                <span *ngIf="investpro.name ==companyInformationConstants.Education"
                                                    title="{{
                                                employee?.education || 'N/A' }}"> {{
                                                    employee?.education || "N/A" }} </span>
                                                <span
                                                    *ngIf="investpro.name ==companyInformationConstants.PastExperience"
                                                    title="{{ employee?.pastExperience || 'N/A' }}">
                                                    {{ employee?.pastExperience || "N/A" }} </span>
                                            </div>
                                        </ng-template>
                                    </kendo-grid-column>
                                    <kendo-grid-column title="Action">
                                        <ng-template kendoGridHeaderTemplate>
                                            <span class="TextTruncate S-M">Action</span>
                                        </ng-template>
                                        <ng-template kendoGridCellTemplate let-employee>
                                            <div class="add-control-btn">
                                                <a class="btn btn-edit" title="Edit" (click)="editPCEmployee(employee)">
                                                    <i class="fa fa-edit"></i>
                                                </a>
                                            </div> <a *ngIf="false" (click)="removeEmployee(employee.emailId)"><a
                                                    class="btn btn-remove">
                                                    <i class="fa fa-trash"></i>
                                                </a> </a>
                                        </ng-template>
                                    </kendo-grid-column>

                                </kendo-grid>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row row-ad-d">
                    <div class="col-12 border">
                        <div class="pull-right pt-2 pb-2 pr-3">
                            <div class="loading-input-controls-manual" *ngIf="loading"><i aria-hidden="true"
                                    class="fa fa-spinner fa-pulse fa-1x fa-fw"></i></div>
                            <input id="hiddenreloadButton" type="button" value="{{resetText}}" title="{{resetText}}"
                                (click)="formReset(f);"
                                class="btn btn-warning reset-update-portfolio-css TextTruncate" />
                            <button id="hiddenSaveButton" [disabled]="loading"
                                class="btn btn-primary reset-update-portfolio-css TextTruncate"
                                title="{{title}}">{{title}}</button>
                        </div>
                    </div>
                </div>
                <div class="row mr-0 ml-0">
                    <div class="col-lg-12 col-md-12 col-sm-12 fixed-footer pr-0 pl-0"
                        [ngStyle]="{'width': sideNavWidth}">
                        <app-static-info-modification-message *ngIf="id !== undefined"></app-static-info-modification-message>
                        <div class="pull-right pr-3 pt-2 pb-2">
                            <div class="d-inline pr-2">
                                <div class="loading-input-controls-manual" *ngIf="loading"><i aria-hidden="true"
                                        class="fa fa-spinner fa-pulse fa-1x fa-fw"></i></div>
                                <input type="button" value="{{resetText}}" title="{{resetText}}"
                                    onclick="document.getElementById('hiddenreloadButton').click()" id="pc-reset-text"
                                    class="nep-button nep-button-secondary TextTruncate reset-update-portfolio-css" />
                            </div>
                            <div class="d-inline"> <button [disabled]="loading || !f.form.valid"
                                    onclick="document.getElementById('hiddenSaveButton').click()" id="pc-title"
                                    class=" width-120 nep-button nep-button-primary width-135 reset-update-portfolio-css ml-2 TextTruncate"
                                    title="{{title}}">{{title}}</button></div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div  *ngIf="confirmLocDelete" class="nep-modal nep-modal-show custom-modal">
    <div class="nep-modal-mask"></div>
    <div  class="nep-card nep-card-shadow nep-modal-panel nep-modal-default" style="position: relative; display: inline-flex; top: 40%;width:24rem; height:10.5rem">
        <div class="nep-card-header nep-modal-title">
            <div class="float-left TextTruncate">Confirmation</div>
            <div class="float-right" (click)="closeDelLocModel()">
                <div class="close-icon"><i  class="pi pi-times"></i></div>
            </div>
        </div>
        <div class="nep-card-body">
            <strong>you sure that you want to delete this record?</strong><br/><br/>
                <div class="float-right">
                    <nep-button Type="Primary" (click)="delLocation()" class="mr-2">
                        Yes
                    </nep-button>
                    <nep-button Type="Secondary" (click)="closeDelLocModel()">
                        No
                    </nep-button>
                </div>        
        </div>
    </div>
</div>

<mat-menu #menuGroup="matMenu" [hasBackdrop]="true" class="internal-report-preference-pop-up pc-group-pop-up"
    xPosition="before">
    <div class="row mr-0 ml-0 pc-group-menu" (click)="$event.stopPropagation();">
        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 preference-header ">
            <div class="float-left menu-header">
                {{companyGroupPageField?.displayName}}
            </div>
            <div class="float-right close-icon cursor-filter" (click)="closePanel()">
                <i class="pi pi-times"></i>
            </div>
        </div>
        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 preference-content pr-0 pl-0 border-bottom-0">
            <div class="row mr-0 ml-0">
                <div class="col-12 col-sm-12 col-lg-12 col-md-12 col-xl-12 pr-0 pl-0" *ngIf="groupList?.length > 0">
                    <div class="kpi-sec-search">
                        <div class="search"><span class="fa fa-search fasearchicon p-1"></span><input
                                (click)="$event.stopPropagation();" type="text" placeholder="Search"
                                [(ngModel)]="searchGroup" class="search-text-company companyListSearchHeight"></div>
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-lg-12 col-md-12 col-xl-12 company-group-boundary"
                    *ngIf="groupList?.length > 0">
                    <div class="row mr-0 ml-0  company-group-list" cdkDropList (cdkDropListDropped)="drop($event)">
                        <div class="col-12 col-sm-12 col-lg-12 col-md-12 col-xl-12 company-group-list-padding drag-item pl-0 pr-0 company-group-box"
                            cdkDragBoundary=".company-group-boundary"
                            *ngFor="let group of groupList | groupFilter:searchGroup; let i = index" cdkDrag>
                            <div class="company-group-custom-placeholder" *cdkDragPlaceholder></div>
                            <div class="float-left drag-item-content">
                                <div class="dot-img">
                                    <img cdkDragHandle src="assets/dist/images/6dots.svg" alt="">
                                </div>
                            </div>
                            <div class="d-inline-block group-content"> 
                                <label class="container" *ngIf="!group.isEditable"> {{group?.groupName}}
                                        <input id="grp_radio" type="radio" name="radio" (click)="$event.stopPropagation();selectedGroupId = group.groupId" 
                                        (mouseout)="removeRadioerror(selectionradio)" name="{{group.groupName}}" value="{{group.groupName}}"name="radio" value="New" [(ngModel)]="selectedGroup" >
                                        <span class="checkmark"></span>
                                </label>
                                <input *ngIf="group.isEditable" [name]="'group' + i" [(ngModel)]="group.groupName"
                                    type="text"
                                    class="form-control eachlabel-padding default-txt TextTruncate edit-mode-input"
                                    name="groupName" validateRequired required value="{{group.groupName}}"
                                    autocomplete="off" maxlength="500" />
                            </div>
                            <div class="float-right">
                                <div class="float-left  icon-edit">
                                    <a *ngIf="!group.isEditable" target="_self" (click)="editGroup(group)"><img
                                            src="assets/dist/images/EditIcon.svg" alt="edit"></a>
                                    <a *ngIf="group.isEditable" target="_self"
                                        (click)="group.isEditable = false;cancelSelection(group)"><img
                                            src="assets/dist/images/red-tick.svg" alt="delete"></a>
                                </div>
                                <div class="float-right  icon-delete">
                                    <a *ngIf="!group.isEditable" target="_self" (click)="deleteGroup(group)"><img
                                            src="assets/dist/images/delete.svg" alt="delete"></a>
                                    <a [ngStyle]="{'pointer-events': group.groupName == null || group.groupName == '' ? 'none' : '' }"
                                        *ngIf="group.isEditable" target="_self" (click)="updateGroup(group)"><img
                                            src="assets/dist/images/green-tick.svg" alt="edit"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-lg-12 col-md-12 col-xl-12 content-p"
                    [ngClass]="groupList?.length > 0 ?'company-grp-add-btn-b' :''">
                    <div class="d-inline-block add-text">
                        <input (focus)="myFunc($event)" (click)="$event.stopPropagation();" [(ngModel)]="groupName"
                            type="text" placeholder="Enter group name here…" />
                    </div>
                    <div class="d-inline-block add-btn">
                        <button (click)="$event.stopPropagation();addCompanyGroup()"
                            [class.isdisabled]="groupName==null || groupName ==''"
                            [disabled]="groupName==null || groupName ==''">Add</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</mat-menu>