﻿@import "../../../variables";
.bulkMessage {
    font-weight: bold;
    color: green
}

.errorMessage {
    font-weight: 400;
    color: #6c757d
}

.tab-bg {
    background-color: $nep-white !important;
}

.outer-section {
    background: $nep-base-grey 0% 0% no-repeat padding-box;
    border: 1px solid $nep-divider;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    box-shadow: 0px 0px 12px $nep-shadow-color;
}

.nav-link {
    background-color: transparent !important;
    padding-top: 9px;
    padding-bottom: 9px;
    &.active {
        background-color: $nep-white !important;
    }
}

.content-bg {
    background-color: $nep-white !important;
}

.custom-tabs>.nav-tabs .nav-link.active,
.custom-tabs>.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
    background-color: $nep-white;
    border-color: $nep-nav-tab-border-color $nep-nav-tab-border-color $nep-white-secondary;
    border-bottom: none !important;
    top: 2px !important;
    position: relative !important;
}

.fund-currency-text {
    height: 35px !important;
}

.performance-header {
    text-align: left;
    font-size: 14px;
    font-family: "Helvetica Neue LT W05_65 Medium",Arial, Verdana, Tahoma,sans-serif;
    letter-spacing: 0px;
    color: $nep-dark-black;
    opacity: 1;
}

.realized {
    background: #DCFAE4 0% 0% no-repeat padding-box !important;
}
.totalRealized {
    background: #DCFAE4 0% 0% no-repeat padding-box !important;
}

.unrealized {
    background: #FFE8BF 0% 0% no-repeat padding-box !important;
}

.fundCashflowTypes {
    right: 1rem;
    position: absolute;
    width: 15%;
}

.rLegend {
    background: #DCFAE4 0% 0% no-repeat padding-box;
    border: 1px solid #55565A;
    border-radius: 4px;
    height: 16px;
    width: 16px;
    padding-left: 16px;
}

.urLegend {
    background: #FFE8BF 0% 0% no-repeat padding-box;
    border: 1px solid #55565A;
    border-radius: 4px;
    height: 16px;
    width: 16px;
    padding-left: 16px;
}

.cloud_download {
    padding: 5px 12px !important;
}

.searchicon {
    position: absolute !important;
    top: 10px !important;
    right: 15px !important;
    font-size: 15px !important;
    color: $nep-icon-grey !important;
    width: 16px !important;
    height: 16px !important;
}

.download-fund-excel {
    background: $nep-primary 0% 0% no-repeat padding-box;
    border-radius: 4px;
    opacity: 1;
    cursor: pointer;
    padding: 5px 16px;
    padding-bottom: 7px !important;
    color: $nep-white;
}

.fund-header-tbl {
    th {
        letter-spacing: 0.14px !important;
        color: #666666 !important;
        opacity: 1;
        padding: 12px 16px !important;
    }
    tbody {
        td {
            letter-spacing: 0.17px;
            color: $nep-black !important;
            text-shadow: 0px 1px 11px #00000017;
            opacity: 1;
        }
    }
}

.bordered {
    border: solid $nep-divider 1px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    box-shadow: 0px 0px 12px #DEDFE0 !important;
    -webkit-box-shadow: 0px 0px 12px #DEDFE0 !important;
    -moz-box-shadow: 0px 0px 12px #DEDFE0 !important;
    border-radius: 4px #DEDFE0 !important;
    margin-bottom: 0px !important;
}

.bordered td,
.bordered th {
    border-left: 1px solid $nep-divider;
    border-top: 1px solid $nep-divider;
    padding: 10px;
    text-align: left;
}

.bordered th {
    background: #FAFAFB 0% 0% no-repeat padding-box;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#F8F8F8), to(#ECECEC));
    background-image: -webkit-linear-gradient(top, #F8F8F8, #ECECEC);
    background-image: -moz-linear-gradient(top, #F8F8F8, #ECECEC);
    background-image: linear-gradient(top, #F8F8F8, #ECECEC);
    -webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, .8) inset;
    -moz-box-shadow: 0 1px 0 rgba(255, 255, 255, .8) inset;
    box-shadow: 0 1px 0 rgba(255, 255, 255, .8) inset;
    border-top: none;
    text-shadow: 0 1px 0 rgba(255, 255, 255, .5);
}

.bordered td:first-child,
.bordered th:first-child {
    border-left: none;
}

.bordered th:first-child {
    -moz-border-radius: 4px 0 0 0;
    -webkit-border-radius: 6px 0 0 0;
}

.bordered th:last-child {
    -moz-border-radius: 0 4px 0 0;
    -webkit-border-radius: 0 6px 0 0;
}

.bordered th:only-child {
    -moz-border-radius: 6px 6px 0 0;
    -webkit-border-radius: 6px 6px 0 0;
}

.bordered tr:last-child td:first-child {
    -moz-border-radius: 0 0 0 4px;
    -webkit-border-radius: 0 0 0 4px;
}

.bordered tr:last-child td:last-child {
    -moz-border-radius: 0 0 4px 0;
    -webkit-border-radius: 0 0 4px 0;
}

.allvalues {
    font-size: 12px;
    margin-right: 12px;
    color: #75787B;
    padding: 9px 16px;
}

.expenses,
.others {
    background-color: #FFFFFF !important;
}

.shadow{
    box-shadow: 0px 0px 12px #DEDFE0 !important;
    border-radius: 4px !important;
}
.cashflow-total-header{
    border-collapse: collapse !important;
}