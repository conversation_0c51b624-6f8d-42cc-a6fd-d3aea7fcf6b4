<div class="row mr-0 ml-0 clo-table-content">
  <div
    class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pl-0 pr-3 pt-3 pb-3 table-border"
  >
    <div class="float-left pl-3 Heading2-M pt-2 d-flex align-items-center">
      {{ tableTitle }}
      <ng-container *ngIf="TAB_NAMES.Aggregate_CLO_Metrics.includes(tableId)">
        <div>
          <span class="ml-3 domicile-label">Domicile</span>
          <kendo-dropdownlist
            id="domile-select-aggregate-clo"
            [data]="domicileOptions"
            [(ngModel)]="selectedDomicile"
            (valueChange)="onDomicileChange($event)"
            class="domicile-dropdown"
          >
          </kendo-dropdownlist>
        </div>
      </ng-container>
    </div>
    <div class="float-right">
      <input
        #fileInput
        type="file"
        style="display: none"
        accept=".xlsx,.xls"
        (change)="onFileSelected($event)"
      />
      <button
        id="btn-export-excel"
        (click)="exportToExcel()"
        [disabled]="!data?.length"
        class="btn TextTruncate btn-warning-export mr-2 export-btn"
      >
        <img
          src="assets/dist/images/Vector.svg"
          class="align-middle me-2"
          alt="Export Icon"
        />
        Export
      </button>

      <button
        id="btn-import"
        class="btn btn-primary btn-import-template"
        (click)="openUpload()"
      >
        Import
      </button>

      <button
        kendoButton
        id="btn-download-template"
        (click)="downloadTemplate()"
        (keypress)="downloadTemplate()"
        class="btn btn-link pr-2 pl-2 btn-primary mr-2 btn-download-template"
        title="Click here to Download Template"
      >
        <img
          src="assets/dist/images/FiDownload.svg"
          alt="Icon"
          class="align-middle"
        />
      </button>

<button [disabled]="!data?.length" class="btn TextTruncate threeDots-btn kebab-button p-0" #anchor (click)="openMenu($event)">
  &#x22EE;
  <kendo-popup [anchor]="anchor" [anchorAlign]="{ vertical: 'bottom', horizontal: 'right' }"
    [popupAlign]="{ vertical: 'top', horizontal: 'right' }" *ngIf="isMenuOpen">
  <kendo-menu [items]="menuItems" (select)="onSelect($event)">
  <ng-template kendoMenuItemTemplate let-item>
  <span>
  <img src="assets/dist/images/delete-Table.svg" alt="" width="16" height="16" style="margin-right: 8px;">
            {{ menuItems[0].text }}
  </span>
  </ng-template>
  </kendo-menu>
  </kendo-popup>
  </button>
    </div>
  </div>

  <div class="row mr-0 ml-0 w-100">
    <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pl-0 pr-0">
      <!-- Dynamic Grid View -->
      <ng-container *ngIf="!isStaticTable">
        <ng-container *ngIf="data && data.length > 0; else noDataTemplate">
          <ng-container [ngSwitch]="tableType">
            <!-- Pivot Table View -->
            <kendo-grid
              *ngSwitchCase="'pivot'"
              [data]="data"
              [resizable]="true"
              [sortable]="true"
              class="k-grid k-grid-custom custom-pivot-grid"
              [ngStyle]="{'overflow-y': data.length >= 30 ? 'scroll' : 'hidden', 'max-height': data.length > 30 && tableId !==CLO_Distributions_To_Date ? '907px' : tableId ==CLO_Distributions_To_Date?'2817px':'907px'}">
              <!-- Process all columns in their original order -->
              <ng-container *ngFor="let col of columns; let i = index">
                <!-- If column has a parent, create or add to group -->
                <ng-container *ngIf="col.parent">
                  <kendo-grid-column-group
                    *ngIf="isFirstInGroup(col)"
                    [title]="col.parent"
                    [headerStyle]="{ 'justify-content': 'center' }"
                  >
                    <kendo-grid-column
                      *ngFor="let groupCol of getColumnsForParent(col.parent)"
                      [field]="groupCol.field"
                      [title]="groupCol.header"
                      [width]="columns.length > 5 ? groupCol.width : 200"
                      [headerClass]="'headerCustomClass'"
                      [headerTitle]="groupCol.header"
                      [locked]="isCompositeRowFilterRequired ? i < 2 : i === 0"
                    >
                      <ng-template
                        kendoGridCellTemplate
                        let-dataItem
                        let-rowIndex="rowIndex"
                        let-columnIndex="columnIndex"
                      >
                        <div
                          class="cell-content d-flex justify-content-between align-items-center"
                          [ngStyle]="{'height': calculateRowHeight(dataItem) + 'px'}">
                          <div class="cell-value">
                            <ng-container
                              *ngIf="groupCol.enableLink; else plainText"
                            >
                              <a
                                href="javascript:void(0)"
                                [ngStyle]="{ color: '#4061C7' }"
                                class="text-link"
                                (click)="onLinkClick(dataItem[groupCol.field])"
                                >{{ dataItem[groupCol.field] }}</a
                              >
                            </ng-container>
                            <ng-template #plainText>
                              <div
                              (click)="onCellClick(dataItem, groupCol, false, true)" 
                              title="Click here to view logs"
                              class="edit-icon"
                              >
                                {{ dataItem[groupCol.field] }}
                              </div>
                            </ng-template>
                          </div>
                          <div
                            class="edit-icon"
                            *ngIf="
                              isCompositeRowFilterRequired
                                ? columnIndex > 1
                                : columnIndex > 0
                            "
                            (click)="onCellClick(dataItem, groupCol)"
                            title="Click here to view logs"
                          >
                            <img
                              src="assets/dist/images/clo-pencil-icon.svg"
                              class="edit-icon-img"
                              alt="Edit Icon"
                              title="Click here to update cell value"
                            />
                          </div>
                        </div>
                      </ng-template>
                    </kendo-grid-column>
                  </kendo-grid-column-group>
                </ng-container>

                <!-- If column has no parent, render as normal column -->
                <ng-container *ngIf="!col.parent">
                  <kendo-grid-column
                    [field]="col.field"
                    [title]="col.header"
                    [width]="columns.length > 5 ? col.width : 320"
                    [headerClass]="'headerCustomClass'"
                    [headerTitle]="col.header"
                    [locked]="isCompositeRowFilterRequired ? i < 2 : i === 0"
                  >
                    <ng-template
                      kendoGridCellTemplate
                      let-dataItem
                      let-rowIndex="rowIndex"
                      let-columnIndex="columnIndex"
                    >
                      <div
                        class="cell-content d-flex justify-content-between align-items-center"
                        [ngStyle]="{'height': calculateRowHeight(dataItem) + 'px'}">
                        <div class="cell-value">
                          <ng-container *ngIf="col.enableLink; else plainText">
                            <div
                              href="javascript:void(0)"
                              [ngStyle]="{ color: '#4061C7' }"
                              class="text-link"
                              (click)="onLinkClick(dataItem[col.field])"
                              >{{ dataItem[col.field] }}</div
                            >
                          </ng-container>
                          <ng-template #plainText>
                            <div
                              *ngIf="
                                col.field === 'Transaction' &&
                                tableId === GLI_Portfolio_Composition
                              "
                              (click)="onGLIPortfolioClick(dataItem[col.field])"
                              [ngStyle]="{ color: '#4061C7' }"
                              class="transaction-hover text-link"
                            >
                              {{ dataItem[col.field] }}
                            </div>
                            <div
                              *ngIf="
                                (col.field !== 'Transaction' ||
                                tableId !== GLI_Portfolio_Composition) &&
                                isCompositeRowFilterRequired
                                ? columnIndex > 1
                                : columnIndex > 0
                              "
                              class="edit-icon"
                              (click)="onCellClick(dataItem, col, false, true,true)"
                              title="Click here to view logs"
                            >
                              {{ dataItem[col.field] }}
                            </div>
                            <div
                            *ngIf="
                              (col.field !== 'Transaction' ||
                              tableId !== GLI_Portfolio_Composition) &&
                              !(isCompositeRowFilterRequired
                              ? columnIndex > 1
                              : columnIndex > 0)
                            "
                          >
                            {{ dataItem[col.field] }}
                          </div>
                          </ng-template>
                        </div>
                        <div
                        id="edit-cell"
                          *ngIf="
                            isCompositeRowFilterRequired
                              ? columnIndex > 1
                              : columnIndex > 0
                          "
                          class="edit-icon"
                          (click)="onCellClick(dataItem, col)"
                          title="Click here to view logs"
                        >
                          <img
                            src="assets/dist/images/clo-pencil-icon.svg"
                            class="edit-icon-img"
                            alt="Edit Icon"
                            title="Click here to edit cell value"
                          />
                        </div>
                      </div>
                    </ng-template>
                  </kendo-grid-column>
                </ng-container>
              </ng-container>
            </kendo-grid>

            <!-- Regular Grid View with Column Groups -->
            <div>
              <kendo-grid *ngSwitchDefault
              [resizable]="true"
              [data]="data"
              [ngStyle]="{'overflow-y': data.length >= 30 ? 'scroll' : 'hidden', 'max-height': data.length > 30 && tableId !==CLO_Distributions_To_Date ? '907px' : tableId ==CLO_Distributions_To_Date?'2817px':'907px'}">
                <kendo-grid-column
                *ngFor="let col of columns; let i = index"
                [field]="col.field"
                [title]="col.header"
                [width]="columns.length > 5 ? col.width : 320"
                [headerClass]="'headerCustomClass'"
                [headerTitle]="col.title"
                [headerStyle]="col.header === '__' ? { display: 'none' } : {}"
                [locked]="isCompositeRowFilterRequired ? i < 2 : i === 0"
              >
                <ng-template
                  kendoGridCellTemplate
                  let-dataItem
                  let-rowIndex="rowIndex"
                  let-columnIndex="columnIndex"
                >
                  <div
                    class="cell-content d-flex justify-content-between align-items-center"
                    [ngStyle]="{'height': calculateRowHeight(dataItem) + 'px'}">
                    <div class="cell-value">
                      <ng-container *ngIf="col.enableLink; else plainText">
                        <div
                          href="javascript:void(0)"
                          [ngStyle]="{ color: '#4061C7' }"
                          class="text-link"
                          (click)="onLinkClick(dataItem[col.field])"
                          >{{ dataItem[col.field] }}</div
                        >
                      </ng-container>
                      <ng-template #plainText>
                        <div
                          *ngIf="
                            col.field === 'Transaction' &&
                            tableId === GLI_Portfolio_Composition
                          "
                          (click)="onGLIPortfolioClick(dataItem[col.field])"
                          [ngStyle]="{ color: '#4061C7' }"
                          class="transaction-hover text-link"
                        >
                          {{ dataItem[col.field] }}
                        </div>
                        <div                          
                          *ngIf="
                            (col.field !== 'Transaction' ||
                            tableId !== GLI_Portfolio_Composition) &&
                            isCompositeRowFilterRequired
                            ? columnIndex > 1
                            : columnIndex > 0
                          "
                          class="edit-icon"
                          (click)="onCellClick(dataItem, col, false, true)" 
                          title="Click here to view logs"
                        >
                          {{ dataItem[col.field] }}
                        </div>
                        <div
                            *ngIf="
                              (col.field !== 'Transaction' ||
                              tableId !== GLI_Portfolio_Composition) &&
                              !(
                              isCompositeRowFilterRequired
                              ? columnIndex > 1
                              : columnIndex > 0)
                            "
                          >
                            {{ dataItem[col.field] }}
                          </div>
                      </ng-template>
                    </div>
                    <div
                      class="edit-icon"
                      *ngIf="
                        isCompositeRowFilterRequired
                          ? columnIndex > 1
                          : columnIndex > 0
                      "
                      (click)="onCellClick(dataItem, col)"
                      title="Click here to view logs"
                    >
                      <img
                        src="assets/dist/images/clo-pencil-icon.svg"
                        class="edit-icon-img"
                        alt="Edit Icon"
                        title="Click here to edit cell value"
                      />
                    </div>
                  </div>
                </ng-template>
              </kendo-grid-column>
              </kendo-grid>
            </div>
          </ng-container>
        </ng-container>
      </ng-container>

      <!-- Static Form View -->
      <div *ngIf="isStaticTable" class="static-form-container">
        <!-- Added Header Section -->
        <div class="row g-0 mb-3 static-table-header" *ngIf="staticTableHeader">
          <div class="col-3 content-padding bottom-border">
            <div class="Body-R">{{ staticTableHeader.header }}</div>
          </div>
          <div class="col-9 content-padding border-primary bottom-border">
            <div
              class="form-value d-flex justify-content-between align-items-center content-height"
            >
              <div class="Body-B">
                
                {{data[0]?.[staticTableHeader.field] || '-'}}
              </div>
              <div
                class="edit-icon d-none"
                id="edit-icon"
                (click)="onCellClick(data[0], staticTableHeader, true)"
                title="Click here to view logs"
              >
                <img
                  class="edit-icon-img"
                  src="assets/dist/images/clo-pencil-icon.svg"
                  alt="Edit Icon"
                  title="Click here to Update Cell Value"
                />
              </div>
            </div>
          </div>
        </div>

        <div
          class="row g-0"
          *ngIf="data && data.length > 0 && columns.length > 0; else noDataTemplate"
        >
        <ng-container *ngIf="tableId !== NAV_DISTRIBUTION; else navDistribution">
          <ng-container *ngFor="let item of columns; let i = index">
          <ng-container *ngIf="columns.length < 4; else doubleColumn">
            <div class="col-3 content-padding bottom-border">
              <div class="form-label Body-R content-height">
                {{ columns[i].header }}
              </div>
            </div>
            <div
              class="col-3 content-padding border-primary bottom-border"               
              
            >
              <div
                class="form-value d-flex justify-content-between align-items-center content-height"
              >
                <div class="Body-B">
                  <span
                  *ngIf="
                    columns[i].field !== 'Transaction' ||
                    tableId !== GLI_Portfolio_Composition
                  "
                  (click)="onCellClick(data[0], columns[i], false, true)"
                  title="Click here to view logs"
                >
                {{data[0]?.[columns[i].field] || '-'}}
                </span>
                  
                </div>
                <div
                  class="edit-icon d-none"
                  id="edit-icon"
                  (click)="onCellClick(data[0], item, true)"
                  title="Click here to view logs"
                >
                  <img
                    class="edit-icon-img"
                    src="assets/dist/images/clo-pencil-icon.svg"
                    alt="Edit Icon"
                    title="Click here to Update Cell Value"
                  />
                </div>
              </div>
            </div>
            <div class="col-6"></div>
          </ng-container>
          <ng-template #doubleColumn>
            <ng-container>
              <div
                class="col-3 col-lg-3 col-xl-3 col-sm-3 col-lg-3 col-md-3 content-padding bottom-border"
              >
                <div class="form-label Body-R content-height">
                  {{ columns[i].header }}
                </div>
              </div>
              <div
                class="col-3 col-lg-3 col-xl-3 col-sm-3 col-lg-3 col-md-3 content-padding border-primary bottom-border"
              >
                <div
                  class="form-value d-flex justify-content-between align-items-center content-height"
                >
                  <div class="Body-B">
                      <span
                      *ngIf="
                        columns[i].field === 'Transaction' &&
                        tableId === GLI_Portfolio_Composition
                      "
                      (click)="onGLIPortfolioClick(data[0]?.[columns[i].field])"
                      [ngStyle]="{ color: '#4061C7' }"
                      class="transaction-hover"
                    >
                      {{data[0]?.[columns[i].field] || '-'}}
                      </span>
                    <span
                      *ngIf="
                          columns[i].field !== 'Transaction' ||
                        tableId !== GLI_Portfolio_Composition
                      "
                      (click)="onCellClick(data[0], columns[i], false, true)"
                      title="Click here to view logs"
                    >
                      {{data[0]?.[columns[i].field] || '-'}}
                    </span>
                    <span                          
                        *ngIf="
                          (columns[i].field !== 'Transaction' ||
                          tableId !== GLI_Portfolio_Composition) &&
                          isCompositeRowFilterRequired
                          ? columnIndex > 1
                          : columnIndex > 0
                        "
                        class="edit-icon"
                        (click)="onCellClick(data[0], columns[i], false, true)" 
                        title="Click here to view logs"
                      >
                      {{data[0]?.[columns[i].field] || '-'}}
                  </span>
                  </div>
                  <div
                    class="edit-icon d-none"
                    id="edit-icon"
                    (click)="onCellClick(data[0], item, true)"
                    title="Click here to view logs"
                  >
                    <img
                      class="edit-icon-img"
                      src="assets/dist/images/clo-pencil-icon.svg"
                      alt="Edit Icon"
                      title="Click here to update cell value"
                    />
                  </div>
                </div>
              </div>
            </ng-container>
          </ng-template>
        </ng-container>
        </ng-container>
        <ng-template #navDistribution>
          <ng-container>
            <div class="row col-12 content-padding bottom-border pt-0">
              <!-- Left Section: NAV Columns -->
              <div class="col-6 pl-0 pr-0">
                <ng-container *ngFor="let navColumn of navColumns">
                  <div class="row align-items-center content-padding bottom-border">
                    <div class="col-6">
                      <div class="form-label Body-R content-height">
                        {{ navColumn.header }}
                      </div>
                    </div>
                    <div class="col-6 border-primary bottom-border">
                      <div class="form-value d-flex justify-content-between align-items-center content-height">
                        <div class="Body-B">
                          <span
                            
                            (click)="onCellClick(data[0], navColumn, false, true)"
                            title="Click here to view logs"
                          >
                          {{ data[0]?.[navColumn.field] || '-' }}
                          </span>
                        </div>
                        <div
                          class="edit-icon d-none"
                          id="edit-icon"
                          (click)="onCellClick(data[0], navColumn, true)"
                          title="Click here to view logs"
                        >
                          <img
                            class="edit-icon-img"
                            src="assets/dist/images/clo-pencil-icon.svg"
                            alt="Edit Icon"
                            title="Click here to Update Cell Value"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            
              <!-- Right Section: Distribution Columns -->
              <div class="col-6 pl-0 pr-0">
                <ng-container *ngFor="let distColumn of distColumns">
                  <div class="row align-items-center content-padding bottom-border">
                    <div class="col-6">
                      <div class="form-label Body-R content-height">
                        {{ distColumn.header }}
                      </div>
                    </div>
                    <div class="col-6 border-primary bottom-border">
                      <div class="form-value d-flex justify-content-between align-items-center content-height">
                        <div class="Body-B">
                          <span
                           
                            (click)="onCellClick(data[0], distColumn, false, true)"
                            title="Click here to view logs"
                          >
                          {{ data[0]?.[distColumn.field] || '-' }}
                          </span>
                        </div>
                        <div
                          class="edit-icon d-none"
                          id="edit-icon"
                          (click)="onCellClick(data[0], distColumn, true)"
                          title="Click here to view logs"
                        >
                          <img
                            class="edit-icon-img"
                            src="assets/dist/images/clo-pencil-icon.svg"
                            alt="Edit Icon"
                            title="Click here to Update Cell Value"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
          </ng-container>
        </ng-template>
        </div>
      </div>

      <ng-template #noDataTemplate>
        <div class="nodata-container">
          <img src="assets/dist/images/clonodata.svg" alt="No Data" />
        </div>
      </ng-template>
    </div>
  </div>
  <div
    class="clo-table-body col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0 Heading2-M"
  >
    <app-clo-commentries
      [footnotes]="footnotes"
      (save)="handleSave($event)"
      (cancel)="handleCancel($event)"
      (reset)="handleReset($event)"
    >
    </app-clo-commentries>

    <div *ngIf="showPopup">
      <app-delete-confirmation-modal [confirmBtn]="'Yes, Delete'" [cancelBtn]="'No, keep it'"  [modalTitle]="'Delete Confirmation'" [deletedCloName]="tableName" [isTableLevel]="true" [deleteNoteType]="'Company'" (PrimaryButtonEventHandler)="deleteItem()" (SecondaryButtonEventHandler)="hideDeletePopup()"></app-delete-confirmation-modal>
    </div>
  </div>
</div>