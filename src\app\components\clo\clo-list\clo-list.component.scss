@import '../../../../assets/dist/css/font.scss';
@import '../../../../variables';
$color-black: #000000;
$color-white: #FFFFFF;
$color-light-blue: #EBF3FF;
$color-active-blue: #D9EDFF;
$color-border: #F2F2F2;
$color-border-active: #93B0ED;
$padding-small: 0.5rem 1rem;
$padding-medium: 10px 1rem;
$border-radius-small: 4px;

.clo-container {
    .clo-title {
        color: $color-black;
        padding: $padding-small;
        padding-top: 24px;
        padding-bottom: 24px;
        align-items: center;
         display: flex;
         font-size: 20px;
         font-weight: 500;
         letter-spacing: normal; 
    }

    .clo-subtitle {
        color: $color-black;
        margin-left: 5px;
        font-size: 16px;
    }

    .card-table {
        .c-table-header {
            background: $color-light-blue;
            padding: $padding-small;
            color: $color-black;
            border-top-right-radius: $border-radius-small;
            border-top-left-radius: $border-radius-small;
        }

        .clo-table-body {
            .clo-table-content {
                border: 1px solid $color-border;
                border-bottom-left-radius: $border-radius-small;
                border-bottom-right-radius: $border-radius-small;
            }

            .clo-item{
                padding: $padding-medium;
                border-bottom: 0.5px solid $color-border-active;
                cursor: pointer;
                .title {
                    color: $color-black;
                    width: calc(100% - 100px) !important;
                }
            }

            .child-item {
                border-bottom: 1px solid $color-border !important;
                background: $color-white !important;
                padding: $padding-medium;
                color: $color-black;

                &:last-child {
                    border-bottom: none !important;
                    border-radius: $space-4;
                }
            }

            .child-add-item {
                @extend .child-item;
                padding-top: 0 !important;
                padding-bottom: 0 !important;

                .add-clo-item {
                    padding-top: 10px;
                    padding-bottom: 10px;
                    color: $color-black;
                }

                .add-clo-btn {
                    padding: 6px 1rem;
                }

                .custom-padding {
                    padding-right: 8px;
                }
            }
        
            .no-data-container {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
            }

            .no-content-section {
                .no-content-text {
                    border-bottom: 1px solid $color-border;   
                    border-radius: $space-4;                 
                    color: #666666;
                    @extend .Body-R;
                }
            }
        }
    }
}
                
.clo-content{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0.25rem 0px;
    cursor: pointer;
}

.no-companylistdata{
    display: flex;
    align-items: center; 
    justify-content: center;
    height: 100vh;
}
.no-content-text {
    color: #666666;
    @extend .Body-R;
}

.delete-icon{
    height: $icon-size;
    width: $icon-size;
    border-radius: 0.25rem;   
    display: flex;
    justify-content: center;
    align-items: center;       
}
.delete-icon:hover{
    background-color: $delete-on-hover-color;
}

.delete-icon:active{
    background-color: $delete-on-click-color
}

.clo-name{
    color: $primary-color-78;
}

.company-active{     
    background: $color-white !important; 
    border-radius: $space-4;      
    box-shadow: $border-shadow-black;
    border: $border-primary;    
}

.company-inactive{
    border: none;
}

.previous-active{
    .clo-item{
        border-bottom: none !important;
    }    
}
