<div class="main-container pr-2" id="investment-company-review-form">

    <div class="company-facts-conatiner">
      
      <div class="d-flex align-items-center mt-3 mb-2" id="company-facts">
        <div class="header-field">
          Company Facts            
        </div>
        <div class="edit-Button" (click)="triggerGoToStep(1)">
          <img src="assets/dist/images/k-edit.svg" alt="Edit" class="ml-4">
        </div>
      </div>

      <div class="row mr-0 ml-0 card-body">
        <div class="col-md-4 padding-card-rem" id="company-name">                      
          <label class="mb-0" for="companyName">Company Name</label>
          <input type="text" 
            class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
            name="input-text" 
            autocomplete="off" 
            maxlength="100"
            [value]="InvestmentCompanyModel.companyName"/>         
        </div>   
        <div class="col-md-4 padding-card-rem" id="domicile">                      
          <label class="mb-0" for="domicile">Domicile</label>
          <input type="text" 
            class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
            name="input-text" 
            autocomplete="off" 
            maxlength="100"
            [value]="InvestmentCompanyModel.domicile"/>         
          </div>
        <div class="col-md-4 padding-card-rem" id="incorporation-date">                      
          <label class="mb-0" for="incorporationDate">Incorporation Date</label>
          <input type="text" 
            class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
            name="input-text" 
            autocomplete="off" 
            maxlength="100" 
            [value]="formattedInvestmentCompanyModel.incorporationDate"/>         
        </div>
        <div class="col-md-4 padding-card-rem" id="final-close">                      
          <label class="mb-0" for="firstClose">First Close</label>
          <input type="text" 
            class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
            name="input-text" 
            autocomplete="off" 
            maxlength="100" 
            [value]="formattedInvestmentCompanyModel.firstClose"/>         
        </div>   
        <div class="col-md-4 padding-card-rem" id="final-close">                      
          <label class="mb-0" for="finalClose">Final Close</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100" 
          [value]="formattedInvestmentCompanyModel.finalClose"/>         
        </div>
        <div class="col-md-4 padding-card-rem" id="investment-period-endDate">                      
          <label class="mb-0" for="investmentPeriodEndDate">Investment Period End Date</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100" 
          [value]="formattedInvestmentCompanyModel.investmentPeriodEndDate"/>         
        </div>
        <div class="col-md-4 padding-card-rem" id="maturity-date">                      
          <label class="mb-0" for="maturityDate">Maturity Date</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100"
          [value]="formattedInvestmentCompanyModel.maturityDate" />         
        </div>   
        <div class="col-md-4 padding-card-rem" id="commitments">                      
          <label class="mb-0" for="commitments">Commitments</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100" 
          [value]="InvestmentCompanyModel.commitments"/>         
        </div>
        <div class="col-md-4 padding-card-rem" id="base-currency">                      
          <label class="mb-0" for="baseCurrency">Base Currency</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100"     
          [value]="InvestmentCompanyModel.baseCurrency"/>         
        </div>    
        <div class="col-md-4 padding-card-rem" id="custodian">                      
          <label class="mb-0" for="custodian">Custodian</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100" 
          [value]="InvestmentCompanyModel.custodian"/>         
        </div>  
        <div class="col-md-4 padding-card-rem" id="administrator">                      
          <label class="mb-0" for="administrator">Administrator</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100" 
          [value]="InvestmentCompanyModel.administrator"/>         
        </div>  
        <div class="col-md-4 padding-card-rem" id="listing-agent">                      
          <label class="mb-0" for="listingAgent">Listing Agent</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100"
          [value]="InvestmentCompanyModel.listingAgent" />         
        </div>  
        <div class="col-md-4 padding-card-rem" id="legal-counsel">                      
          <label class="mb-0" for="legalCounsel">Legal Counsel</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100" 
          [value]="InvestmentCompanyModel.legalCounsel"/>         
        </div>  
        <div class="col-md-4 padding-card-rem" id="portfolio-advisor">                      
          <label class="mb-0" for="portfolioAdvisor">Portfolio Advisor</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100" 
          [value]="InvestmentCompanyModel.portfolioAdvisor"/>         
        </div>                                                    
      </div>  
    </div>  

    <div class="investment-summery-container">

      <div class="d-flex align-items-center mb-2">
        <div class="header-field" id="investment-summery-heading">
          Investment Summary           
        </div>
        <div class="edit-Button" (click)="triggerGoToStep(2)">
          <img src="assets/dist/images/k-edit.svg" alt="Edit" class="ml-4">
        </div>
      </div>
      
      <div class="row mr-0 ml-0 card-body padding-card-rem" id="investment-summary">
        <label for="investmentSummary">Summary</label>        
        <div id="investment-summary" class="investment-summary-content pb-2">
          {{InvestmentCompanyModel.investmentSummary}} 
        </div>
      </div>
    </div>
   
  </div>