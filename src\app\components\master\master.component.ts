import { Router } from '@angular/router';
import { After<PERSON>iew<PERSON>nit, Component, ElementRef, HostListener, OnDestroy, OnInit, ViewChild } from "@angular/core";
import { IdleService, IdleWarningStates } from "ngx-idle-timeout";
import { OidcConfig } from "src/app/configuration/oidcConfig";
import { AccountService } from "src/app/services/account.service";
import { CryptoService } from "src/app/services/crypto.service";
import { OidcAuthService } from "src/app/services/oidc-auth.service";
import { PermissionService } from "src/app/services/permission.service";
import { FeaturesImageEnum } from "../../services/permission.service";
import { DataAnalyticsConstants } from 'src/app/common/constants';
import { MiscellaneousService } from "../../services/miscellaneous.service";
import { debug } from 'console';
import { feature } from 'topojson';
import { sequence } from '@angular/animations';

interface MenuItem {
  label: string;
  children?: MenuItem[];
 }
 
import { AppSettingService } from '../../services/appsettings.service';
import {AppConfig} from "src/app/common/models";
import { MatMenuTrigger } from '@angular/material/menu';
import { OverlayPanel } from 'primeng/overlaypanel';
import {
  Input,
} from "@angular/core";
import { ToastrService } from 'ngx-toastr';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';
@Component({
  selector: "master",
  templateUrl: "./master.component.html",
  styleUrls: ["./master.component.scss"],
})
export class MasterComponent implements OnInit,AfterViewInit, OnDestroy {
    featureUpdatedList: any[] = [];
    @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger;
    resetPasswordUrl: any;
  isIdsEnabled: boolean;
  isPermissionAvailable:boolean = true;
  userName: any = "A";
  userId: any;
  idleTimer = true;
  isLoader:boolean = true;
  navbarString="nep-sidebar-expanded";
  panelOpenState = true  
  isClientUser = false;
  userEmail:string="";
  featureList:any = [];
  featureImage: typeof FeaturesImageEnum = FeaturesImageEnum;
  sidenavWidth = 4.68;
  displayOpenOption = false;

@ViewChild('navContainer', { static: false }) navContainer!: ElementRef;
 @ViewChild('moreButton', { static: false }) moreButton!: ElementRef;
 isMobile: boolean = false;
 visibleMenus: MenuItem[] = [];
 overflowMenus: MenuItem[] = [];
 resizeObserver!: ResizeObserver;
 menus: MenuItem[] = [
  { label: 'Home' },
  { label: 'Repository' },
  { label: 'Reports' },
  { label: 'Data Ingestion', children: [{ label: 'Fetch File' }] },
  { label: 'KPI Mapping' },
  { label: 'Configure' }
];

    appConfig: AppConfig;
    selectedTab: string = '';
  constructor(private ids:OidcAuthService,
    private accountService:AccountService,
    private cryptoService:CryptoService,
    private permissionService: PermissionService,
    private _idleService: IdleService,
    private miscService: MiscellaneousService,
    private appSettingService: AppSettingService,
      private router:Router,
    private breadcrumbService: BreadcrumbService,){
        this.appConfig = this.appSettingService.getConfig();
        console.log(this.appConfig.IsClo);
    }
    ngOnDestroy() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
      }
      window.removeEventListener('resize', this.checkScreenSize.bind(this));
    }
    checkScreenSize() {
      this.createMenus();
    }
    ngAfterViewInit() {
     
    }
  ngOnInit() {
    const currentUrl = this.router.url;
    switch (true) {
      case currentUrl.includes('clo-list'):
      case currentUrl.includes('add-clo'):
      case currentUrl.includes('view-clo-summary'):
      case currentUrl.includes('clo-kpi-history'):
      this.selectedTab = 'CLO Page';
      break;
      case currentUrl.includes('investment-company'):
      case currentUrl.includes('view-company-details'):
      this.selectedTab = 'Investment Company';
      break;
      case currentUrl.includes('page-configuration'):
      case currentUrl.includes('workflow/group-access'):
        this.selectedTab = 'Admin';
      break;    
      default:
      break;
    }
    this.timerSubscribe();
    let config = this.ids.getEnvironmentConfig();
    this.resetPasswordUrl = config.authority + "Manage/ChangePassword";
    let authValue = localStorage.getItem("authStatus") != null ? this.cryptoService.getDecryptedValue(localStorage.getItem("authStatus")?.trim()) : null;
    if (authValue === 'false')
      this.isPermissionAvailable = false;
    else
      this.isPermissionAvailable = true;
    this.isIdsEnabled = OidcConfig.isIdsEnabled;
    this.getFeatureList();
    this.getFeatureData();

    if (localStorage.getItem("TypeOfUser") == null || localStorage.getItem("TypeOfUser") == undefined) {
      this.getMetaData();
    }
    this.checkScreenSize();
    window.addEventListener('resize', this.checkScreenSize.bind(this));
    
  }
  createMenus()
  {
    const investmentFeatureIds = [42, 9, 13, 14, 15];
    let investment = this.featureList.filter((x: { featureID: number }) => investmentFeatureIds.some(id => id === x.featureID));
    investment.sort((a, b) => {
      const indexA = investmentFeatureIds.indexOf(a.featureID);
      const indexB = investmentFeatureIds.indexOf(b.featureID);
      return indexA - indexB;
    });
    this.featureUpdatedList = this.featureList.filter((x: { featureID: number }) => !investmentFeatureIds.some(id => id === x.featureID));
    if(investment.length > 0){
      let parent = {
        aliasName: 'Investment',
        children: investment,
        featureID: 0,
        feature: 'Investment',
        path: null,
        parentID: null,
        sequence: 2
      };
      if(this.featureUpdatedList[0].feature != DataAnalyticsConstants.Dashboard){
      this.featureUpdatedList.splice(0, 0, parent);
      }
      else{
        this.featureUpdatedList.splice(1, 0, parent);
      }
    }
    this.updateMenuVisibility();
  }
  updateMenuVisibility() {
    let maxVisibleMenus;
    if (window.innerWidth <= 720) {
      maxVisibleMenus = 2;
    } else if (window.innerWidth <= 980) {
      maxVisibleMenus = 3;
    } else if (window.innerWidth <= 1024) {
      maxVisibleMenus = 4;
    } else if (window.innerWidth <= 1100) {
      maxVisibleMenus = 5;
    } else {
      maxVisibleMenus = Math.floor(window.innerWidth / 170); // Adjust the divisor as needed
    }
  
    // Separate admin menus and other menus
    const adminMenus = this.featureUpdatedList.filter(menu => menu.feature === 'Admin');
    const otherMenus = this.featureUpdatedList.filter(menu => menu.feature !== 'Admin');
  
    // Determine visible and overflow menus
    const visibleMenus = otherMenus.slice(0, maxVisibleMenus - adminMenus.length);
    const overflowMenus = otherMenus.slice(maxVisibleMenus - adminMenus.length);
    // Add admin menus to visible menus
    const finalVisibleMenus = [...visibleMenus, ...adminMenus];
  
    if (overflowMenus.length > 0) {
      let moreMenu = {
        aliasName: 'More',
        children: overflowMenus,
        featureID: 0,
        feature: 'More',
        path: null,
        parentID: null,
        sequence: this.featureUpdatedList[this.featureUpdatedList.length - 1].sequence + 1
      };
      this.featureUpdatedList = [...finalVisibleMenus, moreMenu];
    } else {
      this.featureUpdatedList = finalVisibleMenus;
    }
  }
  
    /**
 * Checks if the current user is an internal user based on their email domain.
 * @returns {boolean} - Returns true if the user's email domain is "acuitykp.com", otherwise false.
 */
  private checkInternalUser() {
    const currentUserEmail = localStorage.getItem("currentUser");
    if (typeof currentUserEmail === 'string' && currentUserEmail) {
        const splitEmail = currentUserEmail.split('@');
        if (splitEmail.length > 1) {
            const domain = splitEmail[splitEmail.length - 1].trim().toLowerCase();
            if (domain === "acuitykp.com") {
                return true;
            }
        }
    }
    return false;
}

  
  private getMetaData() {
    this.accountService.getMetaData().subscribe({
      next: (result: any) => {
        localStorage.setItem("CreatedOn", result.visitorData.createdOn);
        localStorage.setItem("Roles", result.visitorData.roles);
        localStorage.setItem("ActiveUsers", result.accountData.activeUsers);
        localStorage.setItem("InActiveUsers", result.accountData.inActiveUsers);
        localStorage.setItem("TotalUsers", result.accountData.totalUsers);
        const IsInternal=this.checkInternalUser();
        localStorage.setItem("TypeOfUser",IsInternal ? "Internal" : "External");
      },
      error: (error) => {
      }
    });
  }

  getFeatureList() {
    if (!this.isIdsEnabled && localStorage.getItem("currentUser") != null &&
      localStorage.getItem("currentUser") != undefined) {
      let userData = JSON.parse(localStorage.getItem("currentUser") + "");
      this.userName = userData.firstName[0];
      this.userId = userData.encryptedUserId;
      this.isPermissionAvailable = true;
      this.isLoader = false;
    }
    else if (this.isIdsEnabled && this.ids.getToken() != null) {
      this.getUserPermissionByEmail()
    }
    else if (this.isIdsEnabled && localStorage.getItem("currentUser") != undefined) {
      this.isLoader = false;
      this.userName = localStorage.getItem("currentUser")[0];
      this.userId = "";//userData.encryptedUserId;//Need to be changed//Get encrypted user id from session or cookie
    }
  }
  getFeatureData() {
    if(this.featureList.length == 0){
      let  featureData = this.permissionService.getFeatureData();
      this.featureList = featureData != null && featureData!=undefined ? this.getFeatureJson(JSON.parse(featureData)) : [];
      this.createMenus();
      this.resetParntActive('init')
    }
  }
  getFeatureJson(featureData:any) {
    if(featureData?.length > 0){
        featureData?.forEach(element => {
          if(element?.feature ==DataAnalyticsConstants.Dashboard){
            element.children = [];
          }
          else if(element?.feature ==DataAnalyticsConstants.Admin){
           element.children = element?.children.filter((child) => child.feature !== "ExcelPlugin");
          }
        });  
    return featureData;
    }
  }
  getUserPermissionByEmail(){
    let claims = this.ids.getClaimsFromToken();
    if (claims != null) {
      this.accountService.getUserPermissionByEmail()
        .subscribe({next:(result) => {
          if (result != null) {
            let permissions = JSON.stringify(result.permissions);
            let features = JSON.stringify(result.features);
            localStorage.setItem(`${this.ids.environment}_userPermissions`, permissions);
            localStorage.setItem(`${this.ids.environment}_featurePermissions`, features);
            this.featureList = result.features != null ? this.getFeatureJson(result?.features) : [];
            
            this.resetParntActive('init');
          }
          this.isPermissionAvailable = true;
          this.isLoader = false;
        },
          error:(error) => {
            this.accountService.redirectToUnauthorized();
            this.isLoader = false;
          }});

      this.userName = localStorage.getItem("currentUser")!=null ?  localStorage.getItem("currentUser")[0] : '';
      this.userId = "";//userData.encryptedUserId;//Need to be changed//Get encrypted user id from session or cookie
    }
  }
  getCurrentUser(){
    this.accountService.getCurrentUserDeatils().subscribe({next:
      (result) => {
        this.userEmail = result.emailID?.toLocaleLowerCase();
        //For test purpose
        if(!this.userEmail?.toLocaleLowerCase()?.includes('acuitykp.com')){
          this.isClientUser= true;
        } else{
          this.isClientUser= false;
        }
      },
      error:(error) => {
      }
  });
  }

  resubscribe(): void {
    this.idleTimer = true;
    this.timerSubscribe();
  }
  setContentWidth(event:string)
  {
    this.navbarString = event;
  }

  private timerSubscribe(): void {
    this._idleService
      .idleStateChanged()
      .subscribe(
        val => {
          if (val === IdleWarningStates.SecondaryTimerExpired) {
            this._idleService.stopTimer();
            this.idleTimer = false;
            this.router.navigate(['/out'], { queryParams: { action: 'logout' }, queryParamsHandling: 'merge' });
          }
        }
      );
  }

  resizeSideNav(size) {
    if(size == 'incress'){
      this.sidenavWidth =17.3;
      this.displayOpenOption = true
    }else{
      this.sidenavWidth = 4.68;
      this.displayOpenOption = false
    }
    this.miscService.emitMatPopupClosedEvent();
  }

  parentClickStatus(menu){
    let index = this.featureList.findIndex(x=> x.featureID == menu.featureID);
    this.featureList[index].isActiveParent = true
    localStorage.setItem(`${this.ids.environment}_isActiveParentID`, menu.featureID);
  }

  resetParntActive(val){
    let  self = this
    if(val == 'reset'){
      localStorage.setItem(`${this.ids.environment}_isActiveParentID`, '');
    }
      this.featureList?.forEach(function (val: any, key: any) {
        if (
          self.featureList[key].featureID ==
          parseInt(localStorage.getItem(`${self.ids.environment}_isActiveParentID`))
        ) {
          self.featureList[key].isActiveParent = true;
        } else {
          self.featureList[key].isActiveParent = false;
          self.featureList[key].isTabExpand = false;
        }
      });
  }



  activateSubMenu(data){
    let  self = this

    this.featureList.forEach(function (val: any, key: any) {
      if(val.featureID == data.featureID){
        self.featureList[key].isTabExpand = true
      }else{
        self.featureList[key].isTabExpand = false

      }
    })
  }
  highlightTab(tab: string, path: string) {
    this.selectedTab = tab;

    if(!this.isNullOrEmpty(path)){
      this.router.navigate([path]);
    }
  }

  isNullOrEmpty(value: any): boolean {
    return value === null || value === undefined || value === '';
  }

  resetPassword(){
    let redirectUrl = encodeURIComponent(window.location.href);
    window.location.href = `${this.resetPasswordUrl}?returnUrl=${redirectUrl}`
  }

  closeOverlay() {
    this.miscService.closeOverlayPanel();
  }

  sortMenuItems(menu: any[]): any[] {
    if (!menu) return [];
    menu =menu.filter((child) => !(child.feature =="Admin" && child.children.length == 0));
    return menu.sort((a, b) => {
      if (a.feature === 'Admin') return 1;
      if (b.feature === 'Admin') return -1;
      return 0;
    });
  }
}
