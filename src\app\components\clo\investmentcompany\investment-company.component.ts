import { Component, Inject } from '@angular/core';
import { Router } from "@angular/router";
import { CloService } from '../../../services/clo.service';
import { InvestCompanyService } from './investmentcompany.service';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { CommonSubFeaturePermissionService } from 'src/app/services/subPermission.service';
import { FeaturesEnum } from 'src/app/services/permission.service';
import { ErrorMessage } from 'src/app/services/miscellaneous.service';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';

@Component({
  selector: 'app-investment-company',
  templateUrl: './investment-company.component.html',
  styleUrls: ['./investment-company.component.scss']
})
export class InvestmentCompanyComponent {
  public gridData: any[] = [];
  showNoData: boolean = false;
  showForm: boolean = false;
  showPopup: boolean = false;
  canAddInv: boolean = true;
  public companyName: any[] = [];
  id: number = -1;
  cols: any[] = [
    { field: 'CompanyName', title: 'Company Name' }
  ];
  public companyFilter: string = '';
  deleteMessage = "You have successfully Deleted ";
  errorMessage = "Failed to delete investment company";
  investmentCompanyName: string = '';
  canAddInvestmentCompany: boolean = false;
  canDeleteInvestmentCompany:boolean=false;
  isLoading:boolean = false;
  constructor(private readonly router: Router, private readonly investCompanyService: InvestCompanyService, private readonly toastrService: ToastrService,    private readonly subPermissionService: CommonSubFeaturePermissionService
,private breadcrumbService: BreadcrumbService) { 
  this.updateBreadcrumbs();
}
  ngOnInit(): void {
    this.getSubFeatureAccessPermissions();
    this.loadInvestCompanyList();
  }

  loadInvestCompanyList(): void {
    const filter = {}; // Define your filter criteria here
    this.isLoading = true;
    this.investCompanyService.getInvestCompanyList(filter).subscribe({
      next: (data) => {
        this.isLoading = false;
        this.investCompanyService.setGridData(data);
        this.gridData = data;
        this.showNoData = !this.gridData || this.gridData.length === 0;
      },
      error: (error) => {
        this.isLoading = false;
        this.showNoData = true;
      }
    });
  }

  addRedirect() {
    if (this.canAddInvestmentCompany) {
      this.router.navigate(['/add-investment-company', 0]);
    } else {
      this.showNoAccessError();
    }
  }

  redirectToCompData(id: number): void {
    this.router.navigate(['/view-company-details', id]);
  }

  deleteCompany(index: number): void {
    this.gridData.splice(index, 1);
  }

  get CompanyCount(): number {
    return this.gridData.length;
  }

  formatCompanyName(companyName: string): string {
    if (!this.companyFilter) {
      return companyName;
    }
    const regex = new RegExp(`(${this.companyFilter})`, 'gi');
    return companyName.replace(regex, '<strong>$1</strong>');
  }

  clearSearch() {
    this.companyFilter = '';
  }

  showDeletePopup(id: number, companyName: string) {
    if (!this.canDeleteInvestmentCompany) {
      this.showNoAccessError();
      return;
    }
    this.showPopup = true;
    this.investmentCompanyName = companyName;
    this.id = id;
  }

  deleteInvestmentCompany() {
   
    this.showPopup = false;
    this.investCompanyService
      .deleteInvestmentCompany(this.id)
      .subscribe({
        next: (_response) => {
          if (_response) {
            this.loadInvestCompanyList();
            this.toastrService.success(`${this.deleteMessage} “<b>${this.investmentCompanyName}</b>”`, '', { enableHtml: true, positionClass: "toast-center-center" });
          }
        },
        error: (_error) => {
          this.toastrService.error(this.errorMessage, "", { positionClass: "toast-center-center" });
        },
      });
  }

  hideDeletePopup() {
    this.showPopup = false;
  }
  
  canAddInvestmentSummary: boolean = false;
  getSubFeatureAccessPermissions() {
    this.subPermissionService
      .getCommonFeatureAccessPermissions(FeaturesEnum.InvestmentCompany)
      .subscribe({
        next: (result) => {
          if (result.length > 0) {
            this.canAddInvestmentCompany = result?.map((x) => x.canAdd).includes(true);
            this.canDeleteInvestmentCompany = result?.map((x) => x.canEdit).includes(true);

          }
        },
        error: (_error) => {},
      });
  }

      checkPermissionAccess(permission:any[], permissionType): boolean {

        return permission.map(x => x[permissionType]).includes(true);
      }
      showNoAccessError() {
        this.toastrService.error(ErrorMessage.NoAccess, "", { positionClass: "toast-center-center" });
      }
      updateBreadcrumbs() {
        let newBreadcrumbs: any[] = [];
      newBreadcrumbs.push( { label: 'Investment Company', url: '/investment-company' });
    this.breadcrumbService.setBreadcrumbs(newBreadcrumbs);
      }
}