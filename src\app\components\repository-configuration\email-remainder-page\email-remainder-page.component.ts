import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { UpdateEmailRemainderDto, EmailRecipientsDto} from '../model/config-model';



@Component({
  selector: 'app-email-remainder-page',
  templateUrl: './email-remainder-page.component.html',
  styleUrls: ['./email-remainder-page.component.scss']
})
export class EmailRemainderPageComponent implements OnInit {
  reminderForm: FormGroup;
  id: string;
  isLoading: boolean = false;

  // Display data (read-only) - can be multiple items
  selectedPortfolioCompanies: any[] = [];
  selectedDocumentTypes: any[] = [];
  MILLISECONDS_PER_MINUTE : number = 60000;
  // Email Arrays for Chips
  toEmailArray: string[] = [];
  ccRecipientsArray: Array<{email?: string, name?: string, internalID?: number}> = [];
  // Other configurations
  reminderDays: number[] = [2, 3, 4, 5];
  minDate = new Date(new Date().setHours(0,0,0,0));
  defaultItem = { text: "Select days", value: null };
  reminderDayOptionsMap: { [key: number]: Array<{ text: string, value: string }> } = {};
  
  editorPlaceholder: string = '';

  // Quill editor configuration
  quillConfig = {
    toolbar: [
      [{ 'font': [] }],
      [{ 'size': ['small', false, 'large'] }],
      ['bold', 'italic', 'underline'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'align': [] }],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],
    ],
    theme: 'snow'
  };

  message: string = '';
  isEdit : boolean = false;
  editableData : UpdateEmailRemainderDto;
// Map frequency to number
  frequencyMap: { [key: string | number ]: number | string } = {
        'monthly': 1,
        'quarterly': 2,
        'annual': 3,
         1 :'monthly',
         2 : 'quarterly',
         3 : 'annual'
      };
  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private toastrService: ToastrService,
    private repositoryConfigService: RepositoryConfigService
  ) {
    this.initializeForm();
    this.updateEditorPlaceholder();
  }  
  
  private updateEditorPlaceholder(): void {
    const companies = this.selectedPortfolioCompanies.map(company => company.name).join(', ');
    const docTypes = this.selectedDocumentTypes.map(docType => docType.name).join(', ');
    const defaultMessage = `Hi Team,<br><br>This is a gentle reminder to please send the documents for the items mentioned below:<br><br>Company Name: ${companies}<br>Document Type: ${docTypes}<br><br>Your prompt attention to this matter is greatly appreciated.<br><br>Thank you.`;    this.message = defaultMessage;
    this.reminderForm?.get('message')?.setValue(defaultMessage);
    this.reminderForm?.get('message')?.markAsTouched();
  }

  ngOnInit() {
    this.route.params.subscribe(params => {
      this.id = params['id'];
      this.isEdit = this.router.url.includes('edit');
      this.loadReminderData();
    });
  }
  private toEmailArrayValidator = (control: any) => {
    // 'this' is not available in static validators, so we use a closure in the constructor
    if (!this || !this.toEmailArray) return null;
    return this.toEmailArray.length > 0 ? null : { required: true };
  };

  private initializeForm(): void {
    this.reminderForm = this.formBuilder.group({
      to: ['', [this.toEmailArrayValidator]], // Use custom validator
      cc: ['', [Validators.email]],
      subject: ['Document Request', [Validators.required]],
      message: ['', Validators.required],
      reminderFrequency: ['monthly', Validators.required],
      totalReminders: [1, [Validators.required, Validators.min(1), Validators.max(5)]],
      firstReminder: [new Date(new Date().setHours(0,0,0,0)), [Validators.required]],
      reminder2: [''],
      reminder3: [''],
      reminder4: [''],
      reminder5: ['']
    });
    const toControl = this.reminderForm.get('to');
     toControl?.updateValueAndValidity();
  }

  private loadReminderData(): void {
    if (this.id === 'new' || !this.id) {
      return;
    }
    if(!this.isEdit) {
      this.loadEmailReminderDefaults();
    } else {
      this.loadEmailReminderEdit();
    }
}

private loadEmailReminderEdit() {
    this.repositoryConfigService.getEmailReminderEdit(this.id).subscribe({
      next: (response: UpdateEmailRemainderDto) => {
        this.loadReminderSuccess(response);
      },
      error: (error) => {
        this.loadRemiderError(error);
      }
    });
}
private loadEmailReminderDefaults() {
    this.repositoryConfigService.getEmailReminderDefaults(this.id).subscribe({
      next: (response: UpdateEmailRemainderDto) => {
        this.loadReminderSuccess(response);
      },
      error: (error) => {
        this.loadRemiderError(error);
      }
    });
}

  private loadRemiderError(error: any) {
    this.toastrService.error(error.message || 'Failed to load reminder data');
  }

  private loadReminderSuccess(response: UpdateEmailRemainderDto) {
    if (response) {
      this.editableData = response;
      this.selectedPortfolioCompanies = response.portfolioCompanys || [];
      this.selectedDocumentTypes = response.documentTypes || [];

      this.toEmailArray = (response.toRecipients || [])
        .filter(recipient => recipient?.email)
        .map(recipient => recipient.email);
      // Ensure validator is re-evaluated after loading data
      const toControl = this.reminderForm.get('to');
      toControl?.updateValueAndValidity();
      if (this.toEmailArray.length > 0) {
        toControl?.markAsUntouched();
      } else {
        toControl?.markAsTouched();
      }

      this.ccRecipientsArray = (response.ccReciepients || [])
        .map(recipient => ({
          email: recipient.email || undefined,
          name: recipient.email ? undefined : recipient.name,
          internalID: recipient.internalID
        }));
    if(!this.isEdit) {
      this.updateEditorPlaceholder();
    } else {
      this.populateDataforEditFlow();
    }
    }
  }

onSubmit(): void {
    if (this.reminderForm.valid) {
      this.isLoading = true;

      const formValues = this.reminderForm.value;

      // Create recipients from arrays
      const toRecipients: EmailRecipientsDto[] = this.toEmailArray.map(email => ({
        internalID: 0,
        name: '',
        email: email
      }));

      const ccRecipients: EmailRecipientsDto[] = this.ccRecipientsArray.map(recipient => ({
        internalID :recipient.internalID,
        name: recipient.name || '',
        email: recipient.email || ''
      }));      

      // Create the DTO
      const updateDto: UpdateEmailRemainderDto = {
        featureID: 14,
        reminderID: this.id,
        portfolioCompanys: this.selectedPortfolioCompanies.map(company => ({
          id: company.id || company.companyId,
          name: company.name
        })),
        documentTypes: this.selectedDocumentTypes.map(docType => ({
          id: docType.id || docType.documentTypeID,
          name: docType.name || docType.documentName
        })),
        toRecipients: toRecipients,
        ccReciepients: ccRecipients,
        subject: formValues.subject,
        emailBody: formValues.message,
        frequencyType: Number(this.frequencyMap[formValues.reminderFrequency] || 1),
        totalRemindersPerCycle: formValues.totalReminders,
        remainder1Date: formValues.firstReminder ? new Date(this.adjustForTimezone(formValues.firstReminder)) : new Date(this.adjustForTimezone(new Date())),
        remainder2: formValues.reminder2 ? formValues.reminder2.toString() : undefined,
        remainder3: formValues.reminder3 ? formValues.reminder3.toString() : undefined,
        remainder4: formValues.reminder4 ? formValues.reminder4.toString() : undefined,
        remainder5: formValues.reminder5 ? formValues.reminder5.toString() : undefined
      };

      // Make API call
      this.repositoryConfigService.updateEmailReminder(updateDto).subscribe({
        next: (response) => {          this.isLoading = false;
          this.toastrService.success('Email reminder saved successfully!', '', {
            positionClass: 'toast-center-center'
          });          this.router.navigate(['/repository-configuration'], { 
            queryParams: { tab: 'Email Notification' },
            queryParamsHandling: 'merge'
          });
        },
        error: (error) => {
          this.isLoading = false;
          this.toastrService.error(error.message || 'Failed to save email reminder', '', {
            positionClass: 'toast-center-center'
          });
        }
      });
    } else {
      this.markFormGroupTouched();
      this.toastrService.error('Please fill in all required fields', '', {
        positionClass: 'toast-center-center'
      });
    }
  }
  private adjustForTimezone(firstReminderDate: Date): number {
    return firstReminderDate.getTime() - (firstReminderDate.getTimezoneOffset() * this.MILLISECONDS_PER_MINUTE);
  }

  onCancel(): void {
    this.router.navigate(['/repository-configuration'], {
      queryParams: { tab: 'Email Notification' },
      queryParamsHandling: 'merge'
    });
  }

  onReset(): void {
    this.reminderForm.reset();
    this.initializeForm();
  }
  onAttachFile(): void {
    // To be implemented in future sprint
  }

  private markFormGroupTouched(): void {
    Object.keys(this.reminderForm.controls).forEach(key => {
      const control = this.reminderForm.get(key);
      control?.markAsTouched();
    });
  }

  private updateReminderValidators(): void {
    const totalReminders = this.reminderForm.get('totalReminders')?.value || 1;

    // Reset all reminders first
    this.reminderDays.forEach(day => {
      const control = this.reminderForm.get('reminder' + day);
      if (control) {
        // Convert value to string if it's a number
        const val = control.value;
        if (typeof val === 'number') {
          control.setValue(val.toString(), { emitEvent: false });
        }
        control.clearValidators();
        control.updateValueAndValidity();
      }
    });

    // Then set validators for active reminders
    this.reminderDays.forEach(day => {
      const control = this.reminderForm.get('reminder' + day);
      if (control) {
        if (day <= totalReminders) {
          control.setValidators([
            Validators.required,
            Validators.min(1),
            Validators.max(this.getMaxDays(day))
          ]);
        } else {
          control.clearValidators();
        }
        control.updateValueAndValidity();
      }
    });
  }
  private getTotalPeriod(): number {
    const frequency = this.reminderForm.get('reminderFrequency')?.value;
    switch (frequency) {
      case 'quarterly':
        return 90;
      case 'annual':
        return 365;
      case 'monthly':
      default:
        return 30;
    }
  }
  getMaxDays(reminderNumber: number): number {
    const totalPeriod = this.getTotalPeriod();
    let usedDays = 0;

    // Calculate used days from previous reminders
    for (let i = 2; i < reminderNumber; i++) {
      const prevValue = this.reminderForm.get('reminder' + i)?.value;
      if (prevValue && !isNaN(Number(prevValue))) {
        usedDays += Number(prevValue);
      }
    }

    const maxDays = totalPeriod - usedDays;
    return maxDays > 0 ? maxDays : 1;
  }

  shouldShowReminder(reminderNumber: number): boolean {
    const totalReminders = this.reminderForm.get('totalReminders')?.value || 1;
    return reminderNumber <= totalReminders;
  }
  onTotalRemindersChange(): void {
    // Reset higher numbered reminders when total is decreased
    const totalReminders = this.reminderForm.get('totalReminders')?.value || 1;
    this.reminderDays.forEach(day => {
      const control = this.reminderForm.get('reminder' + day);
      if (control) {
        if (day > totalReminders) {
          control.reset();
          control.clearValidators();
        } else {
          control.markAsTouched();
        }
        control.updateValueAndValidity();
      }
    });
    this.updateReminderValidators();
  }

  getReminderLabel(reminderNumber: number): string {
    const suffixes = ['st', 'nd', 'rd', 'th'];
    const suffix = reminderNumber <= 3 ? suffixes[reminderNumber - 1] : suffixes[3];
    return `${reminderNumber}${suffix} Reminder`;
  }
  onReminderValueChange(reminderNumber: number): void {
    // Clear the cached options for all subsequent reminders
    for (let i = reminderNumber + 1; i <= 5; i++) {
      delete this.reminderDayOptionsMap[i];
    }
    this.updateReminderValidators();
  }

  getReminderDayOptions(reminderNumber: number) {
    if (!this.reminderDayOptionsMap[reminderNumber]) {
      const maxDays = this.getMaxDays(reminderNumber);
      const options = [];
      for (let i = 1; i <= maxDays; i++) {
        options.push({
          text: `${i} Day${i > 1 ? 's' : ''}`,
          value: i.toString()
        });
      }
      this.reminderDayOptionsMap[reminderNumber] = options;
    }
    return this.reminderDayOptionsMap[reminderNumber];
  }

  // Get current value for a reminder control
  getReminderValue(reminderNumber: number): number | null {
    return this.reminderForm.get('reminder' + reminderNumber)?.value || null;
  }

  // Helper methods for template
  isFieldInvalid(fieldName: string): boolean {
    const field = this.reminderForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }  getFieldError(fieldName: string): string {
    const field = this.reminderForm.get(fieldName);
    if (field?.errors) {
      if (fieldName === 'to' && field.errors['required']) {
        return this.toEmailArray.length === 0 ? 'At least one recipient is required' : '';
      }
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email address';
    }
    return '';
  }   
  addToEmail(event: any): void {
    event.preventDefault();
    const control = this.reminderForm.get('to');
    const email = control?.value?.trim();
    if (email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      if (!this.toEmailArray.includes(email)) {
        this.toEmailArray.push(email);
        control?.setValue('');
        control?.markAsUntouched();
        control?.setErrors(null);
        control?.updateValueAndValidity();
      }
    } else if (email) {
      control?.setErrors({ 'email': true });
    }
    // Always update validity after change
    control?.updateValueAndValidity();
  }
  removeToEmail(email: string): void {
    const index = this.toEmailArray.indexOf(email);
    if (index !== -1) {
      this.toEmailArray.splice(index, 1);
      const control = this.reminderForm.get('to');
      control?.updateValueAndValidity();
      if (this.toEmailArray.length === 0) {
        control?.markAsTouched();
      }
    }
  }
  addCcRecipient(event: any): void {
    event.preventDefault();
    const control = this.reminderForm.get('cc');
    const value = control?.value?.trim();

    if (value && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      if (!this.ccRecipientsArray.some(r => r.email === value)) {
        this.ccRecipientsArray.push({ email: value });
        control?.setValue('');
        control?.markAsUntouched();
      }
    } else if (value) {
      control?.setErrors({ 'email': true });
    }
  }

  removeCcRecipient(recipient: {email?: string, name?: string}): void {
    const index = this.ccRecipientsArray.findIndex(r =>
      (r.email && r.email === recipient.email) || (r.name && r.name === recipient.name)
    );
    if (index !== -1) {
      this.ccRecipientsArray.splice(index, 1);
    }
  }

  onReminderFrequencyChange(): void {
    // Reset total reminders to 1
    this.reminderForm.get('totalReminders')?.setValue(1);
    
    // Clear all reminder day selections
    this.reminderDays.forEach(day => {
      const control = this.reminderForm.get('reminder' + day);
      if (control) {
        control.reset();
      }
    });

    // Clear the cached options
    this.reminderDayOptionsMap = {};
    
    // Update validators
    this.updateReminderValidators();
  }
  onMessageChange(content: any) {
    let htmlContent = '';
    if (typeof content === 'string') {
      htmlContent = content;
    } else if (content && typeof content === 'object' && window && (window as any).Quill) {
      const tempQuill = new (window as any).Quill(document.createElement('div'));
      tempQuill.setContents(content);
      htmlContent = tempQuill.root.innerHTML;
    } else {
      htmlContent = '';
    }
    this.message = htmlContent;
    // Always set the form control as a string (HTML)
    this.reminderForm.get('message')?.setValue(htmlContent);
    this.reminderForm.get('message')?.markAsTouched();
  }

  populateDataforEditFlow() {
    this.reminderForm.get('subject')?.setValue(this.editableData.subject);
    this.reminderForm.get('subject')?.markAsTouched();
    this.message = this.editableData.emailBody || '';
    // Always set the form control as a string (HTML)
    this.reminderForm.get('message')?.setValue(this.message);
    this.reminderForm.get('message')?.markAsTouched();
    // Set frequency fields
    this.reminderForm.get('frequencyType')?.setValue(this.frequencyMap[this.editableData.frequencyType]);
    this.reminderForm.get('reminderFrequency')?.setValue(this.frequencyMap[this.editableData.frequencyType]);
    this.reminderForm.get('reminderFrequency')?.markAsTouched();
    // Set total reminders
    this.reminderForm.get('totalReminders')?.setValue(this.editableData.totalRemindersPerCycle);
    // Set first reminder date
    if (this.editableData.remainder1Date) {
      this.reminderForm.get('firstReminder')?.setValue(new Date (new Date(this.editableData.remainder1Date).setHours(0,0,0,0)));
    }
    // Set follow-up reminders
    if (this.editableData.remainder2) {
      this.reminderForm.get('reminder2')?.setValue(this.editableData.remainder2);
    }
    if (this.editableData.remainder3) {
      this.reminderForm.get('reminder3')?.setValue(this.editableData.remainder3);
    }
    if (this.editableData.remainder4) {
      this.reminderForm.get('reminder4')?.setValue(this.editableData.remainder4);
    }
    if (this.editableData.remainder5) {
      this.reminderForm.get('reminder5')?.setValue(this.editableData.remainder5);
    }
    // Mark all as touched for validation
    this.reminderForm.get('reminder2')?.markAsTouched();
    this.reminderForm.get('reminder3')?.markAsTouched();
    this.reminderForm.get('reminder4')?.markAsTouched();
    this.reminderForm.get('reminder5')?.markAsTouched();
  }


}