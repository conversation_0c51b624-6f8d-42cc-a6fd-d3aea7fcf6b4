import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { BehaviorSubject, catchError, map, Observable, throwError } from 'rxjs';
import { FeatureTableMapping, PermissionActions } from 'src/app/common/constants';

@Injectable({
  providedIn: 'root'
})
export class PanelbarItemService {
  selectedCompany:BehaviorSubject<any>=new BehaviorSubject<any>({});
  myAppUrl: string = "";
  constructor( private readonly http: HttpClient, @Inject("BASE_URL") baseUrl: string) {
    this.myAppUrl = baseUrl;
   }
  setCompany(company){
    this.selectedCompany.next(company);
  }
  getCompany(){
    return this.selectedCompany.asObservable();
  }
  
  getCLOPageDetails(): Observable<any> {
    return this.http
      .get<any>(this.myAppUrl + "api/v1/PageConfig/page-details/get")
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }
  
  getInvestCompanyList(): Observable<any> {
      return this.http
        .get<any>(this.myAppUrl + "api/v1/PageConfig/investment-company/get")
        .pipe(
          map((response: any) => response),
          catchError(this.errorHandler)
        );
    }
getCLOTableDetails(): Observable<any> {
    return this.http
      .get<any>(this.myAppUrl + "api/v1/KPIConfig/kpi-details/get")
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }
    getTabList(pageId: number, companyId: number, cloId: number = 0): Observable<any> {
      return this.http
        .get<any>(`${this.myAppUrl}api/v1/PageConfig/get/${pageId}/${companyId}/${cloId}`)
        .pipe(
          map((response: any) => response),
          catchError(this.errorHandler)
        );
    }

    saveTabDetails(tabModel: any): Observable<any> {    
      return this.http
      .post(this.myAppUrl + "api/v1/PageConfig/SaveSettings", tabModel)
        .pipe(
          map((response: any) => response),
          catchError(this.errorHandler)
        );
    }

    errorHandler(error: any) {
        return throwError(error);
      }
      updateTableVisibility(tableList,permissions?:any) {
        const ToBeChangeTableIds = Object.values(FeatureTableMapping.TOBE_CHANGE_DOMICILE_TABLES_NAME).flat();
        const investmentIndex=tableList.findIndex(x=>x.tableId==FeatureTableMapping.TABLES_NAME.Investment_Summary);
        const companyFactsIndex=tableList.findIndex(x=>x.tableId==FeatureTableMapping.TABLES_NAME.Company_Facts);
        if(investmentIndex >=0 && companyFactsIndex>=0){
          tableList[investmentIndex].sequenceNo = tableList[companyFactsIndex].sequenceNo + 1;
        }
        tableList = tableList?.map((x) =>
          Object.assign(x, {
            isShow:permissions?this.checkTablePermissions(x.tableId,permissions):true,
          })
        );
        tableList.forEach(item => {
            if (ToBeChangeTableIds.includes(item.tableId)) {
                item.isShow = false;
            }
        });
      }

      checkTablePermissions(tableId:number,permissions:any,permissionType:string=null):boolean{
        permissionType=permissionType==null?permissionType=PermissionActions.CAN_VIEW:permissionType;
        let result=false;
        let mappedFeature=0;
        for (const key in FeatureTableMapping.FEATURE_TABLE_MAP) {
          if (FeatureTableMapping.FEATURE_TABLE_MAP[key].TableId.includes(tableId)) {
            mappedFeature = FeatureTableMapping.FEATURE_TABLE_MAP[key].FeatureId;
            break;
          }
        }
        if(mappedFeature!=0)
          result = permissions.filter(x=>x.subFeatureId==mappedFeature).map(x => x[permissionType]).includes(true);
        return result;
      }
      getInvestCompanyListForKPI(): Observable<any> {
        return this.http
          .get<any>(this.myAppUrl + "api/v1/KPIConfig/investment-company/get")
          .pipe(
            map((response: any) => response),
            catchError(this.errorHandler)
          );
      }
      
}
