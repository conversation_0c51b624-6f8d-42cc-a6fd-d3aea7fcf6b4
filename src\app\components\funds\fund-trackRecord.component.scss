.nep-modal {
    .close-icon {
        cursor: pointer;
    }
    .nep-card {
        width: 600px !important;
        top: 15% !important;
        .nep-card-header {
            .user-header {
                .TextTruncate{
                    color: #1A1A1A !important; 
                }
                .close-icon {
                   line-height: 1rem;
                }
            }
        }
        .nep-card-body {
            padding: 20px;
            padding-bottom: 0px !important;
            max-height: 400px;
            overflow-y: auto;
            .user-upload {
                letter-spacing: 0px;
                color: #212121;
                a {
                    color: #4061C7 !important;
                    text-decoration: underline !important;
                }
                .desc-header {
                    font-weight: bold;
                }
            }
        }
        .right-border {
            border-right: 1px solid #DEDFE0;
        }
        .click-here {
            font-family: "Helvetica Neue LT W05_65 Medium",Arial, Verdana, Tahoma,sans-serif;
        }
        .detail-sec
        {
            color:#212121 !important;
            padding-left: 12px;
        }
        label[readonly]
        {
            color:#AAAAAC !important;
        }
 .form-control[readonly] {
     color: #CDCDD2 !important;
     border-bottom: 1px solid #D9D9DD !important;
 }
        .form-control {
            letter-spacing: 0px !important;
            color: #212121 !important;
            opacity: 1 !important;
        }
        .errorColor {
            color: #c62828 !important;
        }
        .successColor {
            color: #388e3c !important;
        }
        .form-control.is-invalid {
            border-bottom: 1px solid #c62828 !important;
        }
        .form-control.is-invalid:focus {
            box-shadow: none !important;
        }
        .upload-error-section {
            height: 160px;
            overflow-y: auto;
        }
        .form-group.required .control-label:after {
            content: "*";
            color: red;
        }
    }
    .custom-controls
    {
        .col-md-6:nth-of-type(odd)
        {
            padding-bottom: 20px;
            padding-right: 10px !important;
        }
        .col-md-6:nth-of-type(even)
        {
            padding-bottom: 20px;
            padding-left: 10px !important;
        }
    }
}
.sec-btn
{
    padding-right: 12px;
}
form{
    .form-group
    {
        margin: 0px !important;
    }
}
