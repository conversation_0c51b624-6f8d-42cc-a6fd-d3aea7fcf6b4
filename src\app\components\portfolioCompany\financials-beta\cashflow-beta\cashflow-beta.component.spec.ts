/* tslint:disable:no-unused-variable */
import { CashflowBetaComponent } from './cashflow-beta.component';
import {ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MiscellaneousService, PeriodType} from 'src/app/services/miscellaneous.service';
import { ActivatedRoute } from '@angular/router';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { CashflowBetaService } from 'src/app/services/cashflow-beta.service';
import { KPIModulesEnum } from 'src/app/services/permission.service';
import { OidcAuthService } from 'src/app/services/oidc-auth.service';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { AuditService } from 'src/app/services/audit.service';
import { DatePipe } from '@angular/common';

describe('CashflowBetaComponent', () => {
  let component: CashflowBetaComponent;
  let fixture: ComponentFixture<CashflowBetaComponent>;
  let miscService: MiscellaneousService;
  let oidcAuthService:jasmine.SpyObj<OidcAuthService>;
  const spyOidcAuthService = jasmine.createSpyObj('OidcAuthService', ['getEnvironmentConfig']);
  let auditService: jasmine.SpyObj<AuditService>;
  const auditServiceSpy = jasmine.createSpyObj('AuditService', ['getPortfolioEditSupportingCommentsData']);
  beforeEach(async() => {
    const CashflowBetaServiceStub = () => ({ getPCCashFlowValues: () => of({}),
    exportCompanyBalanceSheet: () => of({})});
    const miscServiceStub = () => ({ downloadExcelFile: () => of({}),
    getMonthNumber: () => 1});
    await TestBed.configureTestingModule({
        imports: [HttpClientTestingModule, FormsModule,ToastrModule.forRoot()],
      declarations: [ CashflowBetaComponent ],
      providers: [
        ToastrService,DatePipe,
        { provide: 'ToastConfig', useValue: {} },
        { provide: OidcAuthService, useValue: spyOidcAuthService },
        { provide: AuditService, useValue: auditServiceSpy },
        { provide: CashflowBetaService, useFactory: CashflowBetaServiceStub },
        { provide: MiscellaneousService, useFactory: miscServiceStub },
        { provide: 'BASE_URL', useValue: 'http://localhost:5000/' },
        { 
            provide: ActivatedRoute, 
            useValue: {
              snapshot: {
                params: {
                  id: '123' // replace with your mock value
                },
              },
            } 
          }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    })
    .compileComponents();
    fixture = TestBed.createComponent(CashflowBetaComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('getFilterAuditValue should return an empty array if calccolumn is defined and truthy in rowdata', () => {
    const rowdata = { 'Calc testField': true };
    const column = { field: 'testField' };

    const result = component.getFilterAuditValue(rowdata, column);

    expect(result).toEqual([]);
  });

  it('getFilterAuditValue should call filterAuditValue with the correct arguments if calccolumn is not defined or falsy in rowdata', () => {
    const rowdata = { 'Calc testField': false };
    const column = { field: 'testField1 testField2' };
    component.auditLogList = ['testAuditLog'];
    const miscService = TestBed.inject(MiscellaneousService);
    const getMonthNumberSpy = spyOn(miscService, 'getMonthNumber').and.returnValue(1);
    const filterAuditValueSpy = spyOn(component, 'filterAuditValue').and.returnValue(['testResult']);

    const result = component.getFilterAuditValue(rowdata, column);

    expect(getMonthNumberSpy).toHaveBeenCalledWith('testField1');
    expect(filterAuditValueSpy).toHaveBeenCalledWith(parseInt('testField2'), 1, ['testAuditLog'], 'testField1', rowdata);
    expect(result).toEqual(['testResult']);
  });

  it('filterAuditValue should return an empty array if no items in auditList match the provided parameters', () => {
    const yearHeader = 2022;
    const monthValue = 1;
    const auditList = [
      { quarter: 'Q2', year: 2022, mappingId: 'testMappingId', kpiValueId: 1, month: 2 },
      { quarter: '', year: 2022, mappingId: 'testMappingId', kpiValueId: 1, month: 0 },
    ];
    const periodHeader = 'Q1';
    const rowdata = { MappingId: 'testMappingId' };

    const result = component.filterAuditValue(yearHeader, monthValue, auditList, periodHeader, rowdata);

    expect(result).toEqual([]);
  });

  it('should return the first acutalAuditLog if the result of getFilterAuditValue has length greater than 0', () => {
    const rowData = {};
    const column = {};
    const getFilterAuditValueSpy = spyOn(component, 'getFilterAuditValue').and.returnValue([{ acutalAuditLog: 'testAcutalAuditLog' }]);
  
    const result = component.printColumn(rowData, column);
  
    expect(getFilterAuditValueSpy).toHaveBeenCalledWith(rowData, column);
    expect(result).toBe('testAcutalAuditLog');
  });
  
  it('should return false if the result of getFilterAuditValue has length 0', () => {
    const rowData = {};
    const column = {};
    const getFilterAuditValueSpy = spyOn(component, 'getFilterAuditValue').and.returnValue([]);
  
    const result = component.printColumn(rowData, column);
  
    expect(getFilterAuditValueSpy).toHaveBeenCalledWith(rowData, column);
    expect(result).toBe(false);
  });

  it('should return true if calccolumn is defined and truthy in rowData', () => {
    const rowData = { 'Calc testField': true };
    const column = { field: 'testField' };
  
    const result = component.printCalcColumn(rowData, column);
  
    expect(result).toBe(true);
  });
  
  it('should return false if calccolumn is not defined or falsy in rowData', () => {
    const rowData = { 'Calc testField': false };
    const column = { field: 'testField' };
  
    const result = component.printCalcColumn(rowData, column);
  
    expect(result).toBe(false);
  });
  it('should return the first item of the result of getFilterAuditValue if it has length greater than 0', () => {
    const rowdata = {};
    const column = {};
    const getFilterAuditValueSpy = spyOn(component, 'getFilterAuditValue').and.returnValue(['testItem']);
  
    const result = component.getValues(rowdata, column);
  
    expect(getFilterAuditValueSpy).toHaveBeenCalledWith(rowdata, column);
    expect(result).toBe('testItem');
  });

  it('should return null if the result of getFilterAuditValue has length 0', () => {
    const rowdata = {};
    const column = {};
    const getFilterAuditValueSpy = spyOn(component, 'getFilterAuditValue').and.returnValue([]);
  
    const result = component.getValues(rowdata, column);
  
    expect(getFilterAuditValueSpy).toHaveBeenCalledWith(rowdata, column);
    expect(result).toBeNull();
  });

  it('resetTable should reset properties to their initial values', () => {
    // Set up initial state
    component.isLoader = true;
    component.tableResult = [1, 2, 3];
    component.tableResultClone = [4, 5, 6];
    component.tableFrozenColumns = [7, 8, 9];
    component.auditLogList = [10, 11, 12];

    // Call the function under test
    component.resetTable();

    // Check the results
    expect(component.isLoader).toBe(false);
    expect(component.tableResult).toEqual([]);
    expect(component.tableResultClone).toEqual([]);
    expect(component.tableFrozenColumns).toEqual([]);
    expect(component.auditLogList).toEqual([]);
  });

  it('should emit an object with the provided boolean values', () => {
    spyOn(component.onChangeValueType, 'emit');

    component.setValueType(true, false, true);

    expect(component.onChangeValueType.emit).toHaveBeenCalledWith({
      isMonthly: true,
      isQuarterly: false,
      isAnnually: true
    });
  });
  it('should return true for numeric values', () => {
    expect(component.isNumberCheck('123')).toBe(true);
    expect(component.isNumberCheck(123)).toBe(true);
    expect(component.isNumberCheck('123.45')).toBe(true);
    expect(component.isNumberCheck(123.45)).toBe(true);
  });

  it('should return false for non-numeric values', () => {
    expect(component.isNumberCheck('abc')).toBe(false);
    expect(component.isNumberCheck({})).toBe(false);
    expect(component.isNumberCheck([])).toBe(false);
    expect(component.isNumberCheck(null)).toBe(false);
    expect(component.isNumberCheck(undefined)).toBe(false);
  });

  describe('setCurrentPeriodType', () => {
    it('should set currentPeriodType to Monthly when isMonthly is true', () => {
      component.setCurrentPeriodType(true, false, false);
      expect(component.currentPeriodType).toEqual(PeriodType.Monthly);
    });
  
    it('should set currentPeriodType to Quarterly when isQuarterly is true', () => {
      component.setCurrentPeriodType(false, true, false);
      expect(component.currentPeriodType).toEqual(PeriodType.Quarterly);
    });
  
    it('should set currentPeriodType to Annually when isAnnually is true', () => {
      component.setCurrentPeriodType(false, false, true);
      expect(component.currentPeriodType).toEqual(PeriodType.Annually);
    });
  
    it('should prioritize Monthly over Quarterly and Annually when multiple flags are true', () => {
      component.setCurrentPeriodType(true, true, true);
      expect(component.currentPeriodType).toEqual(PeriodType.Monthly);
    });
  
    it('should prioritize Quarterly over Annually when both flags are true', () => {
      component.setCurrentPeriodType(false, true, true);
      expect(component.currentPeriodType).toEqual(PeriodType.Quarterly);
    });
  });
  it('should call getPCCashFlowValues with correct parameters on getBalanceSheetValues', () => {
    const balanceSheetService = TestBed.inject(CashflowBetaService);
    spyOn(balanceSheetService, 'getPCCashFlowValues').and.returnValue(of({}));
  
    component.model = {
      periodType: { type: 'periodType' },
      startPeriod: new Date(),
      endPeriod: new Date(),
    };
    component.cashflowValuesMultiSortMeta = ['multiSortMeta'];
    const dataModel = {
      CompanyId: 'companyId',
      paginationFilter: {},
      searchFilter: {},
      segmentType: {},
      currencyCode: 'currencyCode',
      reportingCurrencyCode: {},
      currencyRateSource: 'source',
      valueType: 'valueType',
      isMonthly: true,
      isQuarterly: false,
      isAnnually: false,
      PeriodType: 23,
      ModuleId: 14,
      // ... other properties ...
      kpiConfigurationData: [],
      Kpis: null, // Add the missing property Kpis
      isPageLoad: null,
      isYtdPageLoad:true,
      isLtmPageLoad:false,
      isYtd : true,
      isLtm : false, // Add the missing property isPageLoad
      IsSpotRate:null,
      SpotRateDate:null,
      SpotRate:null,
    };
    component.getDataModel = () => dataModel;
  
    component.getCashflowValues(null, null, 'companyId', 'currencyCode');
  
    expect(balanceSheetService.getPCCashFlowValues).toHaveBeenCalledWith(dataModel);
  });
});
