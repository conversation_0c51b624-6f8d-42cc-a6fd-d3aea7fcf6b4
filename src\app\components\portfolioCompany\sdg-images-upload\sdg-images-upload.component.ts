import { AfterViewInit, Component, Input, OnInit, ViewChild, ElementRef } from '@angular/core';
import { ImageExtensions, CellEditConstants, GlobalConstants } from "../../../common/constants";
import { SDGImagesModel } from './interface/sdg-image-inteface';
import { ToastrService } from "ngx-toastr";
import { PortfolioCompanyService } from "../../../services/portfolioCompany.service";

const EMPTY_FILE_ID = 0;
const FILE_NAME_SPLIT_CHAR = ".";
const FILE_EXTENSION_PREFIX = '.';
const TOASTER_POSITION = CellEditConstants.ToasterMessagePosition;
const SUCCESS_MESSAGE = GlobalConstants.SDGImageUploadSuccess;
const FAILURE_MESSAGE = GlobalConstants.SDGImageUploadFailure;
const MAX_FILE_SIZE_MB = 2;

const INCORRECT_FILE_FORMAT = 'is incorrect file format';
const BYTE_SIZE: number = 1024;
const INCORRECT_FILE_FORMATS = 'are incorrect file formats';
const ALLOWED_FILE_FORMATS_MESSAGE = 'Please refer to allowed file formats list.';
const UPLOAD_IMAGES_UP_TO = 'Please upload images up to';
const MB_ONLY = 'MB only.';
const INCORRECT_FILE_FORMAT_AND_SIZE = 'Incorrect file format and size.';

@Component({
  selector: 'app-sdg-images-upload',
  templateUrl: './sdg-images-upload.component.html',
  styleUrls: ['./sdg-images-upload.component.scss']
})
export class SDGImagesUploadComponent implements OnInit, AfterViewInit {

  @ViewChild("sdgUpload") fileDropEl: ElementRef;
  @Input() pcId: number = 0;
  @Input() sdgTitle: string = '';
  sdgImages: any = [];
  existingSDGImages: any = [];
  imagesTobeDeleted: number[] = [];
  uploadAndSavaEnabled: boolean = true;
  isLoading: boolean = false;

  constructor(
    private _portfolioCompanyService: PortfolioCompanyService,
    private _toasterService: ToastrService
  ) {}

  ngAfterViewInit() {}

  ngOnInit() {
    this.getSDGImages();
  }

  /**
   * Fetches the existing SDG images for the portfolio company.
   */
  getSDGImages() {
    this.isLoading = true;
    this._portfolioCompanyService.getSDGImages(this.pcId).subscribe({
      next: (data: any) => {
        if (data?.sdgImages?.length > 0) {
          this.sdgImages = data.sdgImages.map((image: any) => this.createImageObject(image));
          this.existingSDGImages = data.sdgImages.map((image: any) => this.createImageObject(image));
        }
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      },
    });
  }

  /**
   * Creates an image object from the given image data.
   * @param image The image data.
   * @returns The image object.
   */
  createImageObject(image: any) {
    return {
      file: new File([], image.name, { type: image.type }),
      url: image.value,
      isExisting: true,
      id: image.id
    };
  }

  /**
   * Handles the change event when new images are selected.
   * @param files The selected files.
   */
  onBrowseImageChange(files: FileList) {
    const largeFiles: File[] = [];
    const invalidExtensionFiles: string[] = [];

    Array.from(files).forEach(file => {
      const fileExtension = this.getFileExtension(file.name);
      const fileSizeMB = file.size / (BYTE_SIZE * BYTE_SIZE);

      if (!ImageExtensions.includes(fileExtension)) {
        invalidExtensionFiles.push(fileExtension);
      } else if (fileSizeMB > MAX_FILE_SIZE_MB) {
        largeFiles.push(file);
      } else {
        this.readFile(file);
        this.uploadAndSavaEnabled = false;
      }
    });

    this.handleFileErrors(invalidExtensionFiles, largeFiles);
    this.fileDropEl.nativeElement.value = '';
  }

  /**
   * Handles file errors and displays appropriate messages.
   * @param invalidExtensionFiles The list of files with invalid extensions.
   * @param largeFiles The list of files that are too large.
   */
  handleFileErrors(invalidExtensionFiles: string[], largeFiles: File[]) {
    if (invalidExtensionFiles !== null || largeFiles !== null) {
      if (invalidExtensionFiles.length > 0 && largeFiles.length === 0) {
        const invalidExtensionFilesMessage = invalidExtensionFiles.length > 1 ? INCORRECT_FILE_FORMATS : INCORRECT_FILE_FORMAT;
        this.showErrorMessage(`${invalidExtensionFiles.join(', ')} ${invalidExtensionFilesMessage}. ${ALLOWED_FILE_FORMATS_MESSAGE}`);
      } else if (largeFiles.length > 0 && invalidExtensionFiles.length === 0) {
        const largerFileErrorMessage = largeFiles.length > 1 ? 'images' : 'image';
        this.showErrorMessage(`${largeFiles.length} ${largerFileErrorMessage} could not be uploaded. ${UPLOAD_IMAGES_UP_TO} ${MAX_FILE_SIZE_MB} ${MB_ONLY}`);
      } else if (largeFiles.length > 0 && invalidExtensionFiles.length > 0) {
        this.showErrorMessage(`${INCORRECT_FILE_FORMAT_AND_SIZE} ${UPLOAD_IMAGES_UP_TO} ${MAX_FILE_SIZE_MB} ${MB_ONLY}`);
      }
    }
  }

  /**
   * Extracts the file extension from the given file name.
   * @param fileName The file name.
   * @returns The file extension.
   */
  getFileExtension(fileName: string) {
    return fileName !== null ? FILE_EXTENSION_PREFIX + fileName.split(FILE_NAME_SPLIT_CHAR).pop().toLowerCase() : '';
  }

  /**
   * Displays an error message using the toaster service.
   * @param message The error message.
   */
  showErrorMessage(message: string) {
    this._toasterService.error(message, '', {
      positionClass: TOASTER_POSITION,
    });
  }

  /**
   * Reads the content of the given file and adds it to the sdgImages array.
   * @param file The file to read.
   */
  readFile(file: File) {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      this.sdgImages.push({ file, url: e.target.result, isExisting: false, id: EMPTY_FILE_ID });
    };
    reader.readAsDataURL(file);
  }

  /**
   * Removes an image from the sdgImages array.
   * @param index The index of the image to remove.
   */
  removeImage(index: number) {
    const image = this.sdgImages[index];
    const isExisting = image.isExisting;

    if (isExisting) {
        this.imagesTobeDeleted.push(image.id);
    }

    this.sdgImages.splice(index, 1);

    const hasNewImages = this.sdgImages.some(img => !img.isExisting);
    const hasExistingImages = this.sdgImages.some(img => img.isExisting);
    const noImagesLeft = this.sdgImages.length === 0 && this.existingSDGImages.length > 0;

    this.uploadAndSavaEnabled = !(hasNewImages || noImagesLeft || (isExisting && !hasExistingImages));
}

  /**
   * Uploads and saves the selected images.
   */
  uploadAndSave() {
    this.isLoading = true;
    const formData = this.createFormData();
    this._portfolioCompanyService.uploadSDGImages(formData).subscribe({
      next: () => {
        this.showSuccessMessage(SUCCESS_MESSAGE);
        this.uploadAndSavaEnabled = true;
        this.getSDGImages();
      },
      error: () => {
        this.showErrorMessage(FAILURE_MESSAGE);
        this.isLoading = false;
      },
    });
  }

  /**
   * Creates a FormData object containing the selected images and images to be deleted.
   * @returns The FormData object.
   */
  createFormData() {
    const formData = new FormData();
    formData.append('PortfolioCompanyId', this.pcId.toString());
    this.sdgImages.filter(x => !x.isExisting).forEach(image => {
      formData.append('Files', image.file, image.file.name);
    });
    this.imagesTobeDeleted.forEach((imageId, index) => {
      formData.append(`ImagesTobeDeleted[${index}]`, imageId.toString());
    });
    return formData;
  }

  /**
   * Displays a success message using the toaster service.
   * @param message The success message.
   */
  showSuccessMessage(message: string) {
    this._toasterService.success(message, '', {
      positionClass: TOASTER_POSITION,
    });
  }
}

