import { CommonModule } from "@angular/common";
import { HTTP_INTERCEPTORS, HttpClientModule } from "@angular/common/http";
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import { AngularResizeEventModule } from "angular-resize-event";
import { KendoModule } from "src/app/custom-modules/kendo.module";
import { SharedComponentModule } from "src/app/custom-modules/shared-component.module";
import { SharedDirectiveModule } from "src/app/directives/shared-directive.module";
import { DataIngestionComponent } from "./data-ingestion.component";
import { HttpServiceInterceptor } from "src/app/interceptors/http-service-interceptor";
import { DataIngestionService } from "src/app/services/data-ingestion.service";
import { ExtractionSharedService } from "src/app/services/extraction-shared.service";
import { ExtractionIngestionService } from "src/app/services/extraction-ingestion.service";
import { UtcToLocalTimePipe } from "src/app/pipes/utc-to-local-dateTime.pipe";
import { KPIDataService } from 'src/app/services/kpi-data.service';

@NgModule({
    imports: [
        CommonModule,
        HttpClientModule,
        SharedComponentModule,
        KendoModule,
        SharedDirectiveModule,
        ReactiveFormsModule, FormsModule,AngularResizeEventModule,
        RouterModule.forChild([
            { path: '', component: DataIngestionComponent }
        ])
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    declarations: [DataIngestionComponent,UtcToLocalTimePipe],
    providers: [KPIDataService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpServiceInterceptor,
      multi: true,
    },
    DataIngestionService, {
        provide: HTTP_INTERCEPTORS,
        useClass: HttpServiceInterceptor,
        multi: true,
    },ExtractionSharedService,ExtractionIngestionService]
})
export class DataIngestionModule {

}