@import '../../../../../variables';


/* Styles for manage-deleted-columns component */
.deleted-dashboard-column-table {
    border: none !important;
}

/* Ensure the component is visible */
:host {
  display: block;
  width: 100%;
}

.kendo-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

$space-24: 24px;

.dashboard-tracker-table{
  height: 75vh;
  border-radius: $Radius-4;
  overflow: auto;
}

.configuration-dashboard {
  border-radius: 0 !important;
  border-left: 0 !important;
  border-right: 0 !important;
}

.text-logo{
  border-radius: $Radius-4;
  height: $space-24;
  width: $space-24;
  display: flex;
  justify-content: center;
  align-items: center;
  color: $Neutral-Gray-00;
  background: linear-gradient(180deg, #021155 0%, #9C27B0 100%);
}

.company-logo {
  vertical-align: middle;
  border-radius: $Radius-4;
  height: $space-24;
  width: $space-24;  
  background: $Neutral-Gray-00 !important;
  border: 1px solid $Neutral-Gray-10;
  object-fit: contain;
}

.header-title{
  color: $Neutral-Gray-90;
}

.dashboard-tracker-table input[type="checkbox"] {
  border: 1px solid $Neutral-Gray-60 !important;
}

.dropdown-input{
  overflow: visible;
  text-overflow: unset;
  white-space: normal;
  padding-right: 0;
}

.kendo-container{
  overflow-x: auto;
  width: 100%;
}

.dashboard-controls {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: $Radius-4;

  .pending-changes-info {
    .badge {
      font-size: 12px;
      padding: 4px 8px;
      border-radius: $Radius-4;

      &.badge-warning {
        background-color: #ffc107;
        color: #212529;
      }
    }
  }

  .action-buttons {
    .btn {
      font-size: 14px;
      padding: 6px 12px;
      border-radius: $Radius-4;

      &.btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;

        &:hover:not(:disabled) {
          background-color: #5a6268;
          border-color: #545b62;
        }
      }

      &.btn-primary {
        background-color: #007bff;
        border-color: #007bff;
        color: white;

        &:hover:not(:disabled) {
          background-color: #0056b3;
          border-color: #004085;
        }
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}
// Alternative approach using CSS classes
.validation-error {
  border-color: $Red-80;
}

