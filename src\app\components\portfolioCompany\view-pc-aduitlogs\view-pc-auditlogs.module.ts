import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ViewPCAduitlogsComponent } from './view-pc-aduitlogs.component';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { RouterModule } from '@angular/router';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpServiceInterceptor } from 'src/app/interceptors/http-service-interceptor';
import { SharedPipeModule } from 'src/app/custom-modules/shared-pipe.module';
import { FormsModule } from '@angular/forms';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { KpiAuditComponent } from './pc-audit/kpi-audit/kpi-audit.component';
import { KendoModule } from 'src/app/custom-modules/kendo.module';

@NgModule({
  imports: [
    SharedPipeModule,
    CommonModule,
    FormsModule,
    PrimeNgModule,
    SharedComponentModule,
    SharedDirectiveModule,
    KendoModule,
    RouterModule.forChild([
      { path: '', component: ViewPCAduitlogsComponent }
    ])
  ],
  providers: [    {
    provide: HTTP_INTERCEPTORS,
    useClass: HttpServiceInterceptor,
    multi: true,
  }],
  schemas:[CUSTOM_ELEMENTS_SCHEMA],
  declarations: [ViewPCAduitlogsComponent,KpiAuditComponent]
})
export class ViewPcAuditLogModule { }
