import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { of } from 'rxjs';
import { EmailGroupComponent } from './email-group.component';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { EmailGroupDto, EmailMemberDto } from '../../../repository-configuration/model/config-model';
import { GridModule } from '@progress/kendo-angular-grid';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';

// Mock ToastrService
class MockToastrService {
  success(message?: string, title?: string, override?: any) {}
  error(message?: string, title?: string, override?: any) {}
}

// Mock Router
class MockRouter {
  navigate(commands: any[], extras?: any) {}
}

describe('EmailGroupComponent', () => {
  let component: EmailGroupComponent;
  let fixture: ComponentFixture<EmailGroupComponent>;
  let mockRepositoryConfigService: jasmine.SpyObj<RepositoryConfigService>;

  // Patch EmailGroupDto for test to allow companyNames
  interface EmailGroupDtoWithCompanies extends EmailGroupDto {
    companyNames: string[];
  }

  const mockEmailGroups: EmailGroupDtoWithCompanies[] = [
    {
      groupId: 1,
      groupName: 'Test Group 1',
      createdBy: 'Test User',
      createdOn: new Date(),
      emailCount: 2,
      UploadedBy: 'Test User',
      companyNames: ['Company A', 'Company B', 'Company C', 'Company D']
    },
    {
      groupId: 2,
      groupName: 'Test Group 2',
      createdBy: 'Test User',
      createdOn: new Date(),
      emailCount: 3,
      UploadedBy: 'Test User',
      companyNames: ['Company X', 'Company Y']
    }
  ];

  const mockEmailMembers: EmailMemberDto[] = [
    {
      memberId: 1,
      name: 'Member 1',
      email: '<EMAIL>',
      isActive: true
    },
    {
      memberId: 2,
      name: 'Member 2',
      email: '<EMAIL>',
      isActive: true
    }
  ];

  beforeEach(() => {
    mockRepositoryConfigService = jasmine.createSpyObj('RepositoryConfigService', [
      'getEmailGroups',
      'getEmailListByGroupId',
      'deleteEmailGroup' // <-- Add this line
    ]);
    mockRepositoryConfigService.getEmailGroups.and.returnValue(of(mockEmailGroups));
    mockRepositoryConfigService.getEmailListByGroupId.and.returnValue(of(mockEmailMembers));
    TestBed.configureTestingModule({
      declarations: [EmailGroupComponent],
      imports: [
        GridModule,
        BrowserAnimationsModule
      ],
      providers: [
        { provide: RepositoryConfigService, useValue: mockRepositoryConfigService },
        { provide: ToastrService, useClass: MockToastrService },
        { provide: Router, useClass: MockRouter } // <-- Add this line
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
    });
    
    fixture = TestBed.createComponent(EmailGroupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load email groups on init', fakeAsync(() => {
    component.ngOnInit();
    tick();
    
    expect(mockRepositoryConfigService.getEmailGroups).toHaveBeenCalled();
    expect(component.emailGroups).toEqual(mockEmailGroups);
  }));

  it('should load email members when group is clicked', fakeAsync(() => {
    const groupId = 1;
    component.onGroupClick(groupId);
    tick();
    expect(mockRepositoryConfigService.getEmailListByGroupId).toHaveBeenCalledWith(groupId);
    // Check that selectedGroupEmails has the correct structure
    expect(component.selectedGroupEmails.length).toBe(mockEmailMembers.length);
    component.selectedGroupEmails.forEach((member, idx) => {
      expect(member.memberId).toBe(mockEmailMembers[idx].memberId);
      expect(member.isSelected).toBe(false);
    });
    expect(component.expandedGroupId).toBe(groupId);
  }));

  it('should collapse group and clear members when clicking same group', fakeAsync(() => {
    // First click to expand
    component.onGroupClick(1);
    tick();
    
    // Second click to collapse
    component.onGroupClick(1);
    tick();

    expect(component.expandedGroupId).toBeUndefined();
    expect(component.selectedGroupEmails).toEqual([]);
  }));

  it('should switch selected group when clicking different group', fakeAsync(() => {
    // Click first group
    component.onGroupClick(1);
    tick();
    
    expect(component.expandedGroupId).toBe(1);
    expect(mockRepositoryConfigService.getEmailListByGroupId).toHaveBeenCalledWith(1);

    // Click second group
    component.onGroupClick(2);
    tick();
    
    expect(component.expandedGroupId).toBe(2);
    expect(mockRepositoryConfigService.getEmailListByGroupId).toHaveBeenCalledWith(2);
  }));

  it('should toggle select all emails', fakeAsync(() => {
    component.onGroupClick(1);
    tick();
    // Initially, all emails are unselected
    expect(component.isAllSelected).toBe(false);
    expect(component.selectedGroupEmails.every(e => !e.isSelected)).toBe(true);
    // Select all
    component.toggleSelectAll(true);
    expect(component.isAllSelected).toBe(true);
    expect(component.selectedGroupEmails.every(e => e.isSelected)).toBe(true);
    // Deselect all
    component.toggleSelectAll(false);
    expect(component.isAllSelected).toBe(false);
    expect(component.selectedGroupEmails.every(e => !e.isSelected)).toBe(true);
  }));

  it('should toggle individual email selection and update isAllSelected', fakeAsync(() => {
    component.onGroupClick(1);
    tick();
    const email = component.selectedGroupEmails[0];
    component.toggleEmailItem(email);
    expect(email.isSelected).toBe(true);
    expect(component.isAllSelected).toBe(false);
    // Manually select all
    component.selectedGroupEmails.forEach(e => e.isSelected = true);
    // Now toggleEmailItem on the last one to false, then back to true
    const last = component.selectedGroupEmails[component.selectedGroupEmails.length - 1];
    last.isSelected = false;
    component.toggleEmailItem(last);
    expect(component.isAllSelected).toBe(true);
  }));

  it('CanShowDeletePopup should return true if any email is selected', fakeAsync(() => {
    component.onGroupClick(1);
    tick();
    expect(component.CanShowDeletePopup()).toBe(false);
    component.selectedGroupEmails[0].isSelected = true;
    expect(component.CanShowDeletePopup()).toBe(true);
  }));

  it('getSelectedContacts should return only selected emails', fakeAsync(() => {
    component.onGroupClick(1);
    tick();
    component.selectedGroupEmails[0].isSelected = true;
    expect(component.getSelectedContacts().length).toBe(1);
    expect(component.getSelectedContacts()[0].memberId).toBe(1);
  }));

  it('should close selection popup', () => {
    component.showDeletePopup = true;
    component.closeSelectionPopup();
    expect(component.showDeletePopup).toBe(false);
  });

  it('should handle delete selected click', () => {
    component.showDeletePopup = true;
    component.handleDeleteSelectedClick();
    expect(component.showDeletePopup).toBe(false);
    expect(component.showEmailGroupDeletePopup).toBe(true);
    expect(component.deleteEmail).toBe(true);
    expect(component.confirmationDialogContent).toContain('delete the selected User Details');
  });

  it('should set up groupToDelete and show popup on deleteEmailGroup', () => {
    const group = mockEmailGroups[0];
    component.deleteEmailGroup(group);
    expect(component.groupToDelete).toBe(group);
    expect(component.showEmailGroupDeletePopup).toBe(true);
    expect(component.deleteEmail).toBe(false);
    expect(component.confirmationDialogContent).toContain('also delete all associated Emails');
  });

  it('should call deleteSelectedEmails when triggerDeleteAction and deleteEmail is true', () => {
    spyOn<any>(component, 'deleteSelectedEmails');
    component.deleteEmail = true;
    component.triggerDeleteAction();
    expect((component as any).deleteSelectedEmails).toHaveBeenCalled();
  });

  it('should call deleteGroup when triggerDeleteAction and groupToDelete is set', () => {
    spyOn<any>(component, 'deleteGroup');
    component.deleteEmail = false;
    component.groupToDelete = mockEmailGroups[0];
    component.triggerDeleteAction();
    expect((component as any).deleteGroup).toHaveBeenCalled();
  });

  it('should not delete selected emails if less than 2 remain', fakeAsync(() => {
    // Only 2 members, select both
    component.onGroupClick(1);
    tick();
    component.selectedGroupEmails.forEach(e => e.isSelected = true);
    const toastr = TestBed.inject(ToastrService);
    spyOn(toastr, 'error');
    (component as any).deleteSelectedEmails();
    expect(toastr.error).toHaveBeenCalledWith(
      'Cannot delete selected members. At least 2 email members must remain in the group.',
      '',
      { positionClass: 'toast-center-center' }
    );
    expect(component.showEmailGroupDeletePopup).toBe(false);
  }));

  it('should call service to delete group and reload on success', fakeAsync(() => {
    const group = mockEmailGroups[0];
    component.groupToDelete = group;
    // Use the already-created spy
    const deleteSpy = mockRepositoryConfigService.deleteEmailGroup.and.returnValue(of({}));
    const loadSpy = spyOn(component, 'loadEmailGroups');
    const toastr = TestBed.inject(ToastrService);
    spyOn(toastr, 'success');
    (component as any).deleteGroup();
    tick();
    expect(deleteSpy).toHaveBeenCalledWith(group.groupId);
    expect(toastr.success).toHaveBeenCalledWith('Email group deleted successfully!', '', { positionClass: 'toast-center-center' });
    expect(component.showEmailGroupDeletePopup).toBe(false);
    expect(component.groupToDelete).toBeUndefined();
    expect(loadSpy).toHaveBeenCalled();
    expect(component.selectedGroupEmails).toEqual([]);
  }));

  it('should call service to delete group and show error on failure', fakeAsync(() => {
    const group = mockEmailGroups[0];
    component.groupToDelete = group;
    // Use throwError from rxjs for error observable
    const { throwError } = require('rxjs');
    const deleteSpy = mockRepositoryConfigService.deleteEmailGroup.and.returnValue(throwError(() => 'fail'));
    const toastr = TestBed.inject(ToastrService);
    spyOn(toastr, 'error');
    (component as any).deleteGroup();
    tick();
    expect(deleteSpy).toHaveBeenCalledWith(group.groupId);
    expect(toastr.error).toHaveBeenCalledWith('Failed to delete email group', '', { positionClass: 'toast-center-center' });
    expect(component.showEmailGroupDeletePopup).toBe(false);
    expect(component.groupToDelete).toBeUndefined();
  }));

  it('should navigate to edit email group', () => {
    // Use the injected router from the component
    const router = (component as any).router;
    spyOn(router, 'navigate');
    const group = mockEmailGroups[0];
    component.editEmailGroup(group);
    expect(router.navigate).toHaveBeenCalledWith(['/edit-email-group/' + group.groupId]);
  });

  it('should do nothing in triggerDeleteAction if no deleteEmail and no groupToDelete', () => {
    component.deleteEmail = false;
    component.groupToDelete = undefined;
    // Should not throw or call anything
    expect(() => component.triggerDeleteAction()).not.toThrow();
  });

  it('should reset state on cancelDelete', () => {
    component.showEmailGroupDeletePopup = true;
    component.groupToDelete = { groupId: 1 } as any;
    component.cancelDelete();
    expect(component.showEmailGroupDeletePopup).toBe(false);
    expect(component.groupToDelete).toBeUndefined();
  });

  it('should handle toggleSelectAll when no emails', () => {
    component.selectedGroupEmails = [];
    component.isAllSelected = false;
    component.toggleSelectAll(true);
    expect(component.isAllSelected).toBe(true);
    // Should not throw
    component.toggleSelectAll(false);
    expect(component.isAllSelected).toBe(false);
  });

  it('should not fail if deleteGroup called with undefined groupToDelete', () => {
    component.groupToDelete = undefined;
    expect(() => (component as any).deleteGroup()).not.toThrow();
  });

  it('should not fail if deleteSelectedEmails called with no expandedGroupId', () => {
    component.expandedGroupId = undefined;
    component.selectedGroupEmails = [{ memberId: 1, isSelected: true } as any];
    expect(() => (component as any).deleteSelectedEmails()).not.toThrow();
  });

  it('should handle onGroupClick with invalid groupId', () => {
    // Should not throw
    expect(() => component.onGroupClick(999)).not.toThrow();
  });
});
