import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, Input, OnInit, Output, EventEmitter, ViewChild, OnChanges, SimpleChanges } from "@angular/core";
import { MatMenu, MatMenuTrigger } from "@angular/material/menu";
import { ActivatedRoute } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { Subject, Observable, Subscription } from 'rxjs';
import { filter } from "rxjs/operators";
import { NumberDecimalConst, M_Datatypes } from "src/app/common/constants";
import { FundService } from "../../../services/funds.service";
import { pencilIcon } from "@progress/kendo-svg-icons";
import { ToastrService } from 'ngx-toastr';
import { ErrorMessage, FinancialValueUnitsEnum, MiscellaneousService } from "../../../services/miscellaneous.service";
import { CompositeFilterDescriptor, filterBy, SortDescriptor, orderBy } from "@progress/kendo-data-query";

@Component({
  selector: 'app-fund-ingestion',
  templateUrl: './fund-ingestion.component.html',
  styleUrls: ['./fund-ingestion.component.scss']
})
export class FundIngestionComponent implements OnInit, AfterViewInit, OnChanges {
  public sort: SortDescriptor[] = [
    {
      field: "sortKey",
      dir: "asc",
    },
  ];
  pencilIcon = pencilIcon;
  frozenPeriodColumn: any = null;
  
  @Input() fundId: number;
  @Input() canEditFundIngestion: boolean = false;
  @Input() canExportFundIngestion: boolean = false;
  
  financialValueUnits: typeof FinancialValueUnitsEnum = FinancialValueUnitsEnum;
  NumberDecimalConst = NumberDecimalConst;
  Mdatatypes = M_Datatypes;
  
  @Output() onPickedHeaderTextFundIngestion = new EventEmitter<any>();
  id: any;
  
  fundIngestionData: any[] = [];
  fundIngestionDataClone: any[] = [];
  fundIngestionDetailsData: any[] = [];
  fundIngestionDetailsDataClone: any[] = [];
  
  model: any = {};
  fundIngestionModel: any = {};
  loading: boolean = false;
  isExportLoading: boolean = false;
  
  blockedFundIngestionTable: boolean = false;
  blockedFundIngestionDetailsTable: boolean = false;
  
  totalFundIngestionRecords: number;
  totalFundIngestionDetailsRecords: number;
  
  msgTimeSpan: any;
  globalFilter: string = "";
  globalFilterDetails: string = "";
  
  currentQuarter: string;
  currentYear: number;
  displayFundIngestionDetailsDialog: boolean = false;
  
  fundIngestionValueUnit: any;
  showFundIngestionValueDecimals: boolean = true;
  
  fundIngestionColumns = [];
  frozenFundIngestionTableColumns = [];
  
  fundIngestionMultiSortMeta: any[] = [
    { field: "year", order: -1 },
    { field: "quarter", order: -1 },
  ];
  
  noRecords: boolean = false;
  
  @ViewChild('menu') uiuxMenu!: MatMenu;
  @ViewChild('ingestionTrigger') menuTrigger: MatMenuTrigger;
  @ViewChild("gbFundIngestion") gbFundIngestion: ElementRef;
  
  @Output() onClosePopUpClick: EventEmitter<any> = new EventEmitter();
  
  unitTypeList = [
    {
      typeId: FinancialValueUnitsEnum.Absolute,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Absolute],
    },
    {
      typeId: FinancialValueUnitsEnum.Thousands,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Thousands],
    },
    {
      typeId: FinancialValueUnitsEnum.Millions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Millions],
    },
    {
      typeId: FinancialValueUnitsEnum.Billions,
      unitType: FinancialValueUnitsEnum[FinancialValueUnitsEnum.Billions],
    },
  ];
  
  isLazyLoad: boolean = false;
  sideNavWidth: any = "";
  fullViewWidth: any = "";  // Added this property for the template
  headerText: string = "Fund Ingestion";
  
  constructor(
    private miscService: MiscellaneousService,
    private fundService: FundService,
    protected changeDetectorRef: ChangeDetectorRef,
    private _avRoute: ActivatedRoute,
    private modalService: NgbModal,
    private toastrService: ToastrService
  ) {
    if (this._avRoute.snapshot.params["id"]) {
      this.id = this._avRoute.snapshot.params["id"];
    }
    this.msgTimeSpan = this.miscService.getMessageTimeSpan();
  }
  
  sourceURL: any;
  
  ngOnInit() {
    this.fundIngestionValueUnit = this.unitTypeList[2]; // Default to Millions
    this.sourceURL = this.miscService.GetPriviousPageUrl();
    this.initializeComponent();
    this.noRecords = false;
  }

  ngOnChanges(changes: SimpleChanges) {
    // Check if fundId input has changed and is not the first change
    if (changes.fundId && !changes.fundId.firstChange) {
      this.initializeComponent();
    }
  }

  private initializeComponent() {
    if (this.fundId) {
      this.model.fundID = this.fundId;
      this.fundIngestionModel.fundDetailsID = this.fundId;
      this.getFundIngestionList(null);
    }
  }

  ngAfterViewInit() {
    if (this.uiuxMenu != undefined) {
      (this.uiuxMenu as any).closed = this.uiuxMenu.closed
      this.configureMenuClose(this.uiuxMenu.closed);
    }
  }

  configureMenuClose(old: MatMenu['closed']): MatMenu['closed'] {
    const upd = new EventEmitter();
    feed(upd.pipe(
      filter(event => {
        if (event === 'click') {
          return false;
        }
        return true;
      }),
    ), old);
    return upd;
  }


  getFundIngestionList(event: any) {
    if (event == null) {
      event = {
        first: 0,
        rows: 10,
        globalFilter: null,
        FilterWithoutPaging: true,
        multiSortMeta: this.fundIngestionMultiSortMeta,
      };
    }

    this.blockedFundIngestionTable = true;
    const filter = {
      fundDetailsId: this.model.fundID || this.fundId,
    };

    this.fundService
      .getFundIngestionList(filter)
      .subscribe({
        next: (result) => {
          if (result != null) {
            // Handle the API response property name (dynamicCoulmns instead of dynamicColumns)
            this.fundIngestionColumns = result.dynamicCoulmns || result.dynamicColumns || [];
            this.fundIngestionData = result.result || [];
            
            console.log('Original Columns:', this.fundIngestionColumns);
            console.log('Original Data sample:', this.fundIngestionData[0]);
            
            // Store reference to Year, Quarter, and Month columns if they exist but don't include them in display columns
            let yearColumn = null;
            let quarterColumn = null;
            let monthColumn = null;
            let otherColumns = [];
            
            this.fundIngestionColumns.forEach(col => {
              const lowerName = col.name.toLowerCase();
              if (lowerName === 'year') {
                yearColumn = col;
              } else if (lowerName === 'quarter') {
                quarterColumn = col;
              } else if (lowerName === 'month') {
                monthColumn = col;
              } else {
                otherColumns.push(col);
              }
            });
            
            // We're only keeping non-date columns in the displayed grid
            this.fundIngestionColumns = otherColumns;
            
            // If we're missing date information, create temp columns for data processing
            // but don't add them to this.fundIngestionColumns
            if (!yearColumn) {
              yearColumn = {
                name: "Year",
                displayName: "Year",
                dataType: this.Mdatatypes.Number,
                sequence: -3,
              };
            }
            
            if (!quarterColumn) {
              quarterColumn = {
                name: "Quarter",
                displayName: "Quarter",
                dataType: this.Mdatatypes.FreeText,
                sequence: -2,
              };
            }
            
            if (!monthColumn) {
              monthColumn = {
                name: "Month",
                displayName: "Month",
                dataType: this.Mdatatypes.Number,
                sequence: -1,
              };
            }
            
            // Set up frozen column for period
            this.frozenFundIngestionTableColumns = [
              {
                field: "quarterAndYear",
                header: "Period"
              }
            ];
            
            // Create a richer data structure with properly accessible properties
            this.fundIngestionData = this.fundIngestionData.map(item => {
              // Create a copy of the original data
              const mappedItem = { ...item };
              
              // Add displayable period information
              mappedItem.Quarter = item.quarter || '';
              mappedItem.Year = item.year || '';
              mappedItem.Month = item.month || '';
              
              // Add a sortKey property that will be used for custom sorting
              // Format: YYYY-T-MM where T is the type (1=Month, 2=Quarter, 3=Year-only)
              // This ensures months come first, then quarters, then year-only entries
              const year = item.year || 0;
              
              if (item.month && item.year) {
                // For months, use type 1
                const month = parseInt(item.month) || 0;
                mappedItem.sortKey = `${year.toString().padStart(4, '0')}-1-${month.toString().padStart(2, '0')}`;
                mappedItem.quarterAndYear = item.valueTypeId === 12 ? 
                  `YTD ${this.getMonthName(item.month)} ${item.year}` : 
                  `${this.getMonthName(item.month)} ${item.year}`;
              } else if (item.quarter && item.year) {
                // For quarters, use type 2
                const quarter = parseInt(String(item.quarter).replace('Q', '')) || 0;
                mappedItem.sortKey = `${year.toString().padStart(4, '0')}-2-${quarter.toString().padStart(2, '0')}`;
                
                // Check if quarter value already starts with 'Q' to avoid double Q prefix
                const quarterValue = String(item.quarter);
                const quarterDisplay = quarterValue.startsWith('Q') ? quarterValue : `Q${quarterValue}`;
                
                mappedItem.quarterAndYear = item.valueTypeId === 12 ? 
                  `YTD ${quarterDisplay} ${item.year}` : 
                  `${quarterDisplay} ${item.year}`;
              } else {
                // For year-only entries, use type 3
                mappedItem.sortKey = `${year.toString().padStart(4, '0')}-3-00`;
                mappedItem.quarterAndYear = item.valueTypeId === 12 ? 
                  `YTD ${item.year}` : 
                  `${item.year}`;
              }
              
              // Add properly accessible properties for each column
              this.fundIngestionColumns.forEach(col => {
                const propertyName = col.name.charAt(0).toLowerCase() + col.name.slice(1);
                
                // Add both original property and camelCase version for access in template
                // First check if the original property exists directly in the data
                if (col.name in item) {
                  mappedItem[col.name] = item[col.name];
                  mappedItem[propertyName] = item[col.name]; // Also add camelCase access
                } 
                // Then check if the camelCase version exists in data
                else if (propertyName in item) {
                  mappedItem[col.name] = item[propertyName]; // Add proper cased version
                  mappedItem[propertyName] = item[propertyName]; // Ensure camelCase access
                }
                
                console.log(`Added column ${col.name}:`, mappedItem[col.name], 
                            `camelCase ${propertyName}:`, mappedItem[propertyName]);
              });
              
              return mappedItem;
            });
            
            console.log('Processed Columns:', this.fundIngestionColumns);
            console.log('Processed Data sample:', this.fundIngestionData[0]);
            
            this.fundIngestionDataClone = JSON.parse(
              JSON.stringify(this.fundIngestionData)
            );
            
            this.onPickedHeaderTextFundIngestion.emit(result.headerText);
            this.convertFundIngestionValueUnits();
            this.totalFundIngestionRecords = result.totalRecords || this.fundIngestionData.length || 0;
          } else {
            this.fundIngestionData = [];
            this.totalFundIngestionRecords = 0;
          }
          this.blockedFundIngestionTable = false;
        },
        error: (error) => {
          this.blockedFundIngestionTable = false;
          console.error('Error loading fund ingestion data:', error);
        }
      });
  }

  convertFundIngestionValueUnits() {
    setTimeout(
      function (local: any) {
        local.fundIngestionData = [];
        local.fundIngestionDataClone.forEach(function (value: any) {
          let valueClone = JSON.parse(JSON.stringify(value));
          local.fundIngestionColumns.forEach((element: any) => {
            // Convert column name to camelCase for property access
            const propertyName = element.name.charAt(0).toLowerCase() + element.name.slice(1);
            
            // Only convert currency fields like FundSize, InvestorCommitments, etc.
            if (element.dataType === M_Datatypes.CurrencyValue) {
              switch (+local?.fundIngestionValueUnit.typeId) {
                case FinancialValueUnitsEnum.Absolute:
                  // No conversion needed
                  break;
                case FinancialValueUnitsEnum.Thousands:
                  valueClone[propertyName] = valueClone[propertyName] != "NA" ? 
                    (valueClone[propertyName] / 1000).toFixed(2) : 
                    valueClone[propertyName];
                  break;
                case FinancialValueUnitsEnum.Millions:
                  valueClone[propertyName] = valueClone[propertyName] != "NA" ? 
                    (valueClone[propertyName] / 1000000).toFixed(2) : 
                    valueClone[propertyName];
                  break;
                case FinancialValueUnitsEnum.Billions:
                  valueClone[propertyName] = valueClone[propertyName] != "NA" ? 
                    (valueClone[propertyName] / 1000000000).toFixed(2) : 
                    valueClone[propertyName];
                  break;
              }
            }
          });
          local.fundIngestionData.push(valueClone);
        });
      },
      10,
      this
    );
  }


  searchGrid(event) {
    let allHeaders = this.fundIngestionColumns.map((column) => ({
      field: column.displayName,
      operator: "contains",
      value: event
    }));
    
    allHeaders.push({
      "field": "quarterAndYear",
      "operator": "contains",
      "value": event
    });
    
    let filterValue: CompositeFilterDescriptor = {
      "filters": allHeaders,
      "logic": "or"
    }
    
    this.fundIngestionData = filterBy(this.fundIngestionDataClone, filterValue);
  }

  searchDetailsGrid(event: any) {
    let allHeaders = this.fundIngestionColumns.map((column) => ({
      field: column.displayName,
      operator: "contains",
      value: event
    }));
    
    allHeaders.push({
      "field": "Quarter",
      "operator": "contains",
      "value": event
    });
    
    let filterValue: CompositeFilterDescriptor = {
      "filters": allHeaders,
      "logic": "or"
    }
    
    this.fundIngestionDetailsData = filterBy(this.fundIngestionDetailsDataClone, filterValue);
  }

  hideModal() {
    this.displayFundIngestionDetailsDialog = false;
  }

  public sortChange(sort: SortDescriptor[]): void {
    this.sort = sort;
    this.loadFundIngestionData();
  }

  private loadFundIngestionData(): void {
    this.fundIngestionData = orderBy(this.fundIngestionDataClone, this.sort);
  }

  showNoAccessError() {
    this.toastrService.error(ErrorMessage.NoAccessFund, "", { positionClass: "toast-center-center" });
  }

  // Method to export fund ingestion data to Excel
  exportFundIngestionData() {
    if (!this.canExportFundIngestion) {
      this.showNoAccessError();
      return;
    }

    this.isExportLoading = true;
    const filter = {
      fundDetailsId: this.model.fundID || this.fundId,
      fundIngestionValueUnitId: this.fundIngestionValueUnit?.typeId || FinancialValueUnitsEnum.Millions
    };

    this.fundService.exportFundIngestionData(filter)
      .subscribe({
        next: (response) => {
          if (response) {
            // Create a Blob from the response and create a download link
            const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            document.body.appendChild(a);
            a.setAttribute('style', 'display: none');
            a.href = url;
            a.download = `FundIngestion_${new Date().toISOString().split('T')[0]}.xlsx`;
            a.click();
            window.URL.revokeObjectURL(url);
            a.remove();
          }
          this.isExportLoading = false;
        },
        error: (error) => {
          this.isExportLoading = false;
          console.error('Error exporting fund ingestion data:', error);
          this.toastrService.error('Failed to export fund ingestion data. Please try again later.', '', { positionClass: "toast-center-center" });
        }
      });
  }

  // Method to open fund ingestion details for a specific quarter
  openFundIngestionDetailForQuarter(fundIngestion: any) {
    if (!fundIngestion || !fundIngestion.encryptedFundIngestionId) {
      this.toastrService.error('Unable to retrieve fund ingestion details.', '', { positionClass: "toast-center-center" });
      return;
    }

    this.currentQuarter = fundIngestion.Quarter;
    this.currentYear = fundIngestion.Year;
    
    // Update header text to use month names if available
    if (fundIngestion.Month && fundIngestion.Year) {
      this.headerText = `Fund Ingestion Details - ${this.getMonthName(fundIngestion.Month)} ${this.currentYear}`;
    } else if (fundIngestion.Quarter && fundIngestion.Year) {
      this.headerText = `Fund Ingestion Details - Q${fundIngestion.Quarter} ${this.currentYear}`;
    } else {
      this.headerText = `Fund Ingestion Details - ${this.currentYear}`;
    }
    
    this.blockedFundIngestionDetailsTable = true;
    this.displayFundIngestionDetailsDialog = true;
    
    // Prepare the data for the details grid
    this.fundIngestionDetailsData = [];
    this.fundIngestionDetailsDataClone = [];
    
    // Format the details data from the columns and values in the selected row
    this.fundIngestionColumns.forEach(column => {
      // Only include columns with actual values (not NA)
      const value = fundIngestion[column.name] || fundIngestion[column.name.charAt(0).toLowerCase() + column.name.slice(1)];
      
      if (value != null) {
        this.fundIngestionDetailsData.push({
          fieldName: column.displayName,
          value: value,
          dataType: column.dataType
        });
      }
    });
    
    this.fundIngestionDetailsDataClone = JSON.parse(JSON.stringify(this.fundIngestionDetailsData));
    this.totalFundIngestionDetailsRecords = this.fundIngestionDetailsData.length;
    this.blockedFundIngestionDetailsTable = false;
  }

  // Add this method to the class to convert month numbers to month names
  getMonthName(monthNumber: number): string {
    const monthNames = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    // Check if the month number is valid (1-12)
    if (monthNumber >= 1 && monthNumber <= 12) {
      return monthNames[monthNumber - 1];
    }
    
    return monthNumber.toString();
  }

  /**
   * Helper method to reliably get cell values from the data object
   * This handles different property naming conventions and null checks
   */
  getCellValue(item: any, column: any): any {
    if (!item || !column || !column.name) {
      return 'NA';
    }

    // Try different property access patterns
    const camelCaseName = column.name.charAt(0).toLowerCase() + column.name.slice(1);
    const exactName = column.name;
    
    // Debug logging - comment out in production
    console.log(`Accessing column: ${exactName}, camelCase: ${camelCaseName}`);
    console.log(`Item keys:`, Object.keys(item));
    
    // Try multiple ways to access the data
    let value = undefined;
    
    // Priority order for value lookup:
    // 1. Try direct property access using exact case
    if (exactName in item) {
      value = item[exactName];
      console.log(`Found exact match for ${exactName}:`, value);
    }
    // 2. Try direct property access using camelCase 
    else if (camelCaseName in item) {
      value = item[camelCaseName];
      console.log(`Found camelCase match for ${camelCaseName}:`, value);
    }
    // 3. Try displayName if it exists
    else if (column.displayName && column.displayName in item) {
      value = item[column.displayName];
      console.log(`Found displayName match for ${column.displayName}:`, value);
    } 
    // 4. Try case-insensitive match (slower, but more thorough)
    else {
      const itemKeys = Object.keys(item);
      const matchingKey = itemKeys.find(key => 
        key.toLowerCase() === exactName.toLowerCase() || 
        key.toLowerCase() === camelCaseName.toLowerCase()
      );
      
      if (matchingKey) {
        value = item[matchingKey];
        console.log(`Found case-insensitive match for ${matchingKey}:`, value);
      }
    }
    
    // Handle special value cases
    if (value === undefined || value === null || value === 'NA' || value === '-') {
      return 'NA';
    }
    
    return value;
  }
}

function feed<T>(from: Observable<T>, to: Subject<T>): Subscription {
  return from.subscribe({
    next: data => to.next(data),
    error: err => to.error(err),
    complete: () => to.complete(),
  });
}