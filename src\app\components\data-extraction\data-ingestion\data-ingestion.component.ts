import { Compo<PERSON>, ElementRef, Inject, OnInit, ViewChild } from "@angular/core";
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from "@angular/forms";
import { DataIngestionUtility } from "./data-Ingestion-utility";
import {
  DealPCFund,
  FileConfig,
  FileValidationResult,
  IngestionFormData,
  SelectedFile,
  FileDetails,
  SourceType,
  StatusResponse,
  CompanyKpiTreeItem,
  StatusCount,
} from "./data-ingestion.model";
import { FundService } from "src/app/services/funds.service";
import {
  CompositeFilterDescriptor,
  GroupResult,
  SortDescriptor,
  filterBy,
  groupBy,
} from "@progress/kendo-data-query";
import { DropDownFilterSettings,  MultiSelectTreeCheckableSettings } from "@progress/kendo-angular-dropdowns";
import { ToastContainerDirective, ToastrService } from "ngx-toastr";
import { Router } from "@angular/router";
import { FileSharingService } from "src/app/services/file-sharing.service";
import { DataIngestionConstants, KpiModuleAlias } from "src/app/common/constants";
import { DataIngestionService } from "src/app/services/data-ingestion.service";
import { MultipleSortSettings } from "@progress/kendo-angular-grid";
import { ExtractionSharedService } from "src/app/services/extraction-shared.service";
import { ExtractionUiConstants, StateDescriptions } from "../extraction-constants";
import { DocumentDetailDto, StateAndStatus } from "../extraction.model";
import { ExtractionIngestionService } from "src/app/services/extraction-ingestion.service";
import { CommonFileModel, S3UrlExtractedModel, UploadResponse, SpecificKPIsJsonModel } from "../extraction.model";
import { Subscription, interval } from 'rxjs';
import { OidcAuthService } from "src/app/services/oidc-auth.service";
import { RepositoryConfigService } from "src/app/services/repository.config.service";
import * as signalR from '@microsoft/signalr';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { KPIDataService } from 'src/app/services/kpi-data.service';
import { FeaturesEnum, KPIModulesEnum } from "src/app/services/permission.service";
import { SUBPAGE_FIELD_NAMES,DATA_INGESTION_SUBPAGE_ID } from 'src/app/common/constants';
import { PageConfigurationService } from 'src/app/services/page-configuration.service';
import { DataIngestion } from 'src/app/common/enums';

import { firstValueFrom } from 'rxjs';
/**
 * Component for handling data ingestion operations
 * Manages file uploads, form validations, and data processing for ingestion
 */
@Component({
  selector: "app-data-ingestion",
  templateUrl: "./data-ingestion.component.html",
  styleUrls: ["./data-ingestion.component.scss"],
})
export class DataIngestionComponent implements OnInit {
  showDigestion: boolean = false;
  @ViewChild("fileInput") fileInput!: ElementRef;
  groupedCompanyList: GroupResult[] = [];
  ingestionForm: FormGroup;
  fileConfig: FileConfig = {
    maxSize: 500 * 1024 * 1024, // 500MB
    maxFiles: 1,
    allowedTypes: ["xlsx", "xls", "csv", "pdf"],
  };
  isAllSelected: boolean = false;
  virtual: any = {
    itemHeight: 32,
    pageSize: 20,
    horizontalScroll: true,
  };
  filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: "contains",
  };
  companyLoading: boolean = false;
  selectedFiles: FileDetails[] = [];
  selectedFilesList: SelectedFile[] = [];
  fileErrors: string[] = [];
  modules: SourceType[] = [];
  periods = ["Month", "Quarter", "Year"];
  timeSeriesClone: string[] = [];
  years: number[] = [];
  yearsClone: number[] = [];
  months: string[] = [];
  quarters: string[] = [];
  periodOptions: string[] = [];
  periodOptionsClone: string[] = [];
  searchText: string = "";
  ingestionData: any[] = [];
  pageSize = 10;
  skip = 0;
  companyList: DealPCFund[] = [];
  companyListClone: DealPCFund[] = [];
  spreadDetails: DocumentDetailDto[] = [];
  @ViewChild(ToastContainerDirective, {})
  toastContainer: ToastContainerDirective;
  sourceTypes: SourceType[] = [];
  public sort: SortDescriptor[] = [
    { field: "request.companyName", dir: "asc" },
  ];
  public sortSettings: MultipleSortSettings = {
    mode: "multiple",
    initialDirection: "asc",
    allowUnsort: true,
    showIndexes: true,
  };
  stateAndStatus: StateAndStatus[] = [];
  isLoader: boolean = false;
  s3UploadResponse: UploadResponse[] = [];
  s3UrlExtractedModel: S3UrlExtractedModel[] = [];
  fileFormatModel: CommonFileModel[] = [];
  companyEncryptedID: string = null;
  private dataRefreshSubscription: Subscription;
  private readonly REFRESH_INTERVAL = 10000; // 10 seconds in milliseconds
  gridFilter: CompositeFilterDescriptor;
  spreadDetailsClone: DocumentDetailDto[] = [];
  currentOffset = 0;
  hasMoreData = true;
  gridLoading = false;
  pageLimit = 30; // Number of records to fetch per request
  config: any;
  public ingestionTypeForm: FormGroup;
  nextStepper: boolean = false;
  asIsExtraction: boolean = false;
  specificKpi: boolean = false;
  investmentDetails: any[] = [];
  investmentDetailsClone: any[] = [];
  issuerDetails: any[] = [];
  issuerFullDetails: any[] = [];
  selectedFund: any = null;
  fundKpiList: any[] = [];
  fundKpiListClone: any[] = [];
  finalIssuerDetails: any[] = [];
  public checkableSettings: MultiSelectTreeCheckableSettings = {
    checkChildren: true,
    checkOnClick: false,
  };
  statusCounts: StatusCount;
  ingestionHubUrl: string = "";
  private hubConnection: signalR.HubConnection;
  repositoryStructure: any;
  fundRepositoryStructure: any;
  isFundKpis: boolean = false;
  isCompanyKpis: boolean = false;
  isFundKpisLoaded: boolean = false;
  isPcKpisLoaded: boolean = false;
  isFundSelected: boolean = false;
  allMappedPcKpis: any[] = [];
  companyWithUnmapperdKpi: number[] = [];
  allMappedFundKpis: any[] = [];
  kpi_json: SpecificKPIsJsonModel = {
    module_name: "fund",
    investment_name: "",
    kpis_list: [],
    issuer_kpis_list: [],
  };
  selectedIssuers: any[] = [];
  kpiModuleAlias: typeof KpiModuleAlias = KpiModuleAlias;
  subPageFields: any[] = [];
  showSpecificKpiRadio: boolean = true;
  showAsIsExtractionRadio: boolean = true;
  DataIngestion=DataIngestion;
  public specificKpiDisplayName: string = '';
  public asIsExtractionDisplayName: string = '';

  payloadKpiCount: number = 1;
  constructor(
    private readonly fb: FormBuilder,
    private readonly fundService: FundService,
    private readonly toasterService: ToastrService,
    private readonly router: Router,
    private readonly fileSharingService: FileSharingService,
    private readonly dataIngestion: DataIngestionService,
    private readonly extractionSharedService: ExtractionSharedService,
    private readonly extractionIngestionService: ExtractionIngestionService,
    private readonly oidcService: OidcAuthService,
    private readonly portfolioCompanyService: PortfolioCompanyService,
    private readonly repositoryConfigService: RepositoryConfigService, 
    private readonly kpiDataService: KPIDataService, 
    private pageConfigurationService: PageConfigurationService,
    @Inject("apiIngestionBaseUrl") baseUrl: string, 
  ) {
    this.ingestionHubUrl = baseUrl;
    this.timeSeriesClone = this.periods;
    this.createForm();
    this.modules = [
      { id: 14, name: "Portfolio Company" },
      { id: 13, name: "Fund" },
    ];
    this.getStateAndStatus();
  }

  /**
   * Creates the ingestion form with required validations
   */
  createForm(): void {
    this.ingestionForm = this.fb.group({
      extractionType: [null, Validators.required],
      module: [null, Validators.required],
      company: [null, Validators.required],
      sourceType: [null, Validators.required],
      funds: [null],
      companyIssuers: [[]],
    });
  }
  ngOnInit(): void {
    this.config = this.oidcService.getEnvironmentConfig();
    this.toasterService.overlayContainer = this.toastContainer;
    this.loadSourceTypes();
    this.loadStatusCounts();
    this.getFundWiseCompanies();
    this.loadInvestmentDetails();
    this.getIngestedData();
    this.initializeSignalRConnection();
    this.loadDataIngestionModules();
    this.getPageConfigSetting();

  }
  initializeSignalRConnection() {
    this.hubConnection = new signalR.HubConnectionBuilder()
      .withUrl(this.ingestionHubUrl + 'notify', {
        accessTokenFactory: () => this.oidcService.getToken(),
      })
      .configureLogging(signalR.LogLevel.Information)
      .build();
    this.hubConnection
      .start()
      .then(() => {
        this.registerNotificationHandler();
      })
      .catch((err) => {
        console.error("Error while starting SignalR connection:", err);
      });
  }
  registerNotificationHandler(): void {
    this.hubConnection.on("ReceiveNotification", (notification: any) => {
     this.loadStatusCounts();
     this.getIngestedData();
    });
  }
  loadSourceTypes(): void {
    this.dataIngestion.getDataSourceTypes().subscribe({
      next: (data: SourceType[]) => {
        this.sourceTypes = data;
      },
      error: (error) => {
        this.sourceTypes = [];
      },
    });
  }
  /**
   * Fetches and processes fund-wise company data
   */
  getFundWiseCompanies() {
    this.companyLoading = true;
    this.fundService.getFundsAndPcs().subscribe({
      next: (result: DealPCFund[]) => {
        this.companyList = result;
        this.companyListClone = [...this.companyList];
        this.processFundCompanies(result);
        this.companyLoading = false;
      },
      error: (error) => {
        this.groupedCompanyList = [];
        this.companyListClone = [];
        this.companyLoading = false;
      },
    });
  }

  /**
   * Processes company data and groups it by fund name
   * @param result - Array of DealPCFund objects to process
   */
  processFundCompanies(result: DealPCFund[]): void {
    if (result && result.length > 0) {
      this.groupedCompanyList = groupBy(result, [
        { field: "fundName" },
      ]) as GroupResult[];
    } else {
      this.groupedCompanyList = [];
    }
  }

  /**
   * Filters company data based on search value
   * @param value - Search string to filter companies
   */
  customFilter(value: string): void {
    if (value) {
      const filteredData = filterBy(this.companyListClone, {
        logic: "or",
        filters: [
          {
            field: "fundName",
            operator: "contains",
            value: value,
            ignoreCase: true,
          },
          {
            field: "companyName",
            operator: "contains",
            value: value,
            ignoreCase: true,
          },
        ],
      });
      this.processFundCompanies(filteredData);
    } else {
      this.processFundCompanies(this.companyListClone);
    }
  }
  filterTable() {
    console.log("Filtering with:", this.searchText);
  }
  initiateIngestion() {
    alert("Initiate Ingestion Clicked");
  }
  pageChange(event: any) {
    this.skip = event.skip;
  }
  pageSizeChange(event: any) {
    this.pageSize = event.target.value;
  }

  reset() {
    this.ingestionForm.reset();
    this.clearFiles();
    this.groupedCompanyList = [];
    this.processFundCompanies(this.companyList);
    this.isFundKpis = false;
    this.isCompanyKpis = false;
    this.allMappedFundKpis = [];
    this.allMappedPcKpis = [];
    this.companyWithUnmapperdKpi = [];
    this.repositoryStructure = undefined;
  }

loadDataIngestionModules() {
  this.dataIngestion.getSubPageFieldsBySubPageId(DATA_INGESTION_SUBPAGE_ID).subscribe({
    next: (result: any) => {
      if (Array.isArray(result)) {
        this.subPageFields = result;
        this.showSpecificKpiRadio = this.getFieldIsActive(SUBPAGE_FIELD_NAMES.SPECIFIC_KPI_EXTRACTION);
        this.showAsIsExtractionRadio = this.getFieldIsActive(SUBPAGE_FIELD_NAMES.ASIS_EXTRACTION);
      } else {
        this.resetSubPageFields();
      }
    },
    error: () => {
      this.resetSubPageFields();
    },
  });
}
private getFieldIsActive(fieldName: string): boolean {
  return this.subPageFields?.find((f: any) => f.name === fieldName)?.isActive ?? true;
}

private resetSubPageFields(): void {
  this.subPageFields = [];
  this.showSpecificKpiRadio = true;
  this.showAsIsExtractionRadio = true;
}
  /**
   * Clears all selected files and resets related states
   */
  clearFiles(): void {
    this.fileInput.nativeElement.value = DataIngestionConstants.EMPTY_TITLE;
    this.selectedFiles = [];
    this.selectedFilesList = [];
    this.fileErrors = [];
  }
  fetchFiles() {
    if (this.ingestionForm.valid) {
      this.fileInput.nativeElement.click();
    }
  }

  /**
   * Clears the file input element and resets selected files
   */
  clearFileInput(): void {
    if (this.fileInput?.nativeElement) {
      this.fileInput.nativeElement.value = DataIngestionConstants.EMPTY_TITLE;
      this.selectedFiles = [];
      this.selectedFilesList = [];
    }
  }

  /**
   * Validates a file against configured constraints
   * @param file - File to validate
   * @returns FileValidationResult object
   */
  validateFile(file: File): FileValidationResult {
    const existingNames = new Set(this.selectedFiles.map((f) => f.name));
    return DataIngestionUtility.validateFile(
      file,
      this.fileConfig,
      existingNames
    );
  }

  /**
   * Handles file selection event and processes selected files
   * @param event - File input change event
   */
  onFileSelected(event: any): void {
    const ingestionData = this.ingestionForm.value as IngestionFormData;
    if (ingestionData.extractionType == DataIngestionConstants.SpecificKPI) {
      this.fileConfig.maxFiles = 10;
    }
    this.fileErrors = [];
    const files = Array.from(event.target.files || []) as File[]; //NOSONAR
    if (files.length === 0) return;

    const existingNames = new Set(this.selectedFiles.map((f) => f.name));
    const { newFiles, totalFiles } = DataIngestionUtility.processFiles(
      files,
      this.fileConfig,
      existingNames,
      ingestionData.extractionType
    );

    if (totalFiles > this.fileConfig.maxFiles) {
      this.toasterService.warning(
        DataIngestionConstants.MAX_FILES_MESSAGE(this.fileConfig.maxFiles),
        DataIngestionConstants.EMPTY_TITLE,
        { positionClass: DataIngestionConstants.ToasterMessagePosition }
      );
      this.clearFileInput();
      return;
    }

    this.selectedFilesList = [...this.selectedFilesList, ...newFiles];
    this.selectedFiles = [
      ...this.selectedFiles,
      ...newFiles.map((f) => ({ id: f.id, name: f.name })),
    ];

    this.fileSharingService.shareFiles(
      this.selectedFilesList,
      this.selectedFiles,
      ingestionData,
      this.stateAndStatus
    );
    if (this.finalIssuerDetails.length > 0)
      this.ingestionForm.value.companyIssuers = this.finalIssuerDetails;
    this.uploadFiles(
      this.ingestionForm.value as IngestionFormData,
      this.selectedFilesList
    );
  }
  getIngestedData(append: boolean = false) {
    this.gridLoading = true;
    const uri = `${ExtractionUiConstants.EXTRACT_SPREAD_DETAILS}?limit=${this.pageLimit}&offset=${this.currentOffset}`;

    this.extractionSharedService.get(uri).subscribe({
      next: (result: DocumentDetailDto[]) => {
        if (result.length < this.pageLimit) {
          this.hasMoreData = false;
        }
        if (append) {
          this.spreadDetails = [...this.spreadDetails, ...result];
          this.spreadDetailsClone = [...this.spreadDetailsClone, ...result];
        } else {
          this.spreadDetails = result;
          this.spreadDetailsClone = result;
        }
        this.gridLoading = false;
      },
      error: (error) => {
        this.gridLoading = false;
        console.error("Error fetching spread details:", error);
        if (!append) {
          this.spreadDetails = [];
          this.spreadDetailsClone = [];
        }
      },
    });
  }
  getStateAndStatus() {
    this.extractionSharedService
      .get(ExtractionUiConstants.StateAndStatus)
      .subscribe({
        next: (result: StateAndStatus[]) => {
          this.stateAndStatus = result;
        },
      });
  }
  onRowClick(event: any): void {
    const selectedRow = event.dataItem as DocumentDetailDto;
    if (selectedRow.state == StateDescriptions.FILE_DRAFT_BEFORE_EXTRACTION) {
      this.router.navigate(["/fetch-file", event.dataItem.processId]);
    } else if (
      selectedRow.state == StateDescriptions.EXTRACTION_COMPLETED &&
      selectedRow.extractionType == DataIngestionConstants.SpecificKPI
    ) {
      this.router.navigate(["/extraction"], {
        state: { rowData: event.dataItem, isSpecificKPI: true },
      });
    } else if (
      selectedRow.state == StateDescriptions.EXTRACTION_COMPLETED &&
      selectedRow.extractionType !== DataIngestionConstants.SpecificKPI
    ) {
      this.router.navigate(["/extraction"], {
        state: { rowData: event.dataItem, isSpecificKPI: false },
      });
    }
  }

  uploadFiles(
    ingestionFormData: IngestionFormData,
    selectedFilesList: SelectedFile[]
  ) {
    const formArray = this.fb.array([]);
    this.isLoader = true;
    this.extractionIngestionService
      .uploadFiles(
        ingestionFormData,
        selectedFilesList,
        formArray,
        this.stateAndStatus
      )
      .subscribe({
        next: (result) => {
          if (result.isSuccess) {
            this.s3UploadResponse = result.s3UploadResponse;
            this.s3UrlExtractedModel = result.s3UrlExtractedModel;
            this.fileFormatModel = result.fileFormatModel;
            if (
              ingestionFormData.extractionType ==
              DataIngestionConstants.AsIsExtraction
            ) {
              this.processSuccessfulUploadAsIsExtraction(ingestionFormData);
            } else if (
              ingestionFormData.extractionType ==
              DataIngestionConstants.SpecificKPI
            ) {
              this.processSuccessfulUploadSpecificKPIExtraction(
                ingestionFormData
              );
            }
          }
        },
        error: (error) => {
          this.isLoader = false;
          console.error("Upload error:", error);
        },
      });
  }
  processSuccessfulUploadAsIsExtraction(ingestionFormData: IngestionFormData) {
    this.extractionIngestionService
      .createDocIngestion(
        ingestionFormData.extractionType ==
          DataIngestionConstants.AsIsExtraction
          ? ingestionFormData.company.encryptedPortfolioCompanyID
          : ingestionFormData.funds.fundId.toString(),
        ingestionFormData.extractionType ==
          DataIngestionConstants.AsIsExtraction
          ? ingestionFormData.company.companyName
          : ingestionFormData.funds.fundName,
        this.fileFormatModel,
        this.s3UrlExtractedModel,
        this.config.client_env
      )
      .subscribe({
        next: (result) => {
          if (result.isSuccess) {
            this.addOrUpdateJobs();
          }
        },
      });
  }
  processSuccessfulUploadSpecificKPIExtraction(
    ingestionFormData: IngestionFormData
  ) {
    this.extractionIngestionService
      .createSpecificKpiDocIngestion(
        ingestionFormData,
        this.fileFormatModel,
        this.config.client_env,
        this.kpi_json
      )
      .subscribe({
        next: (result) => {
          if (result.isSuccess) {
            this.s3UrlExtractedModel.forEach((item) => {
              item.parentJobId = ExtractionUiConstants.EMPTY_GUID;
              item.jobId = result.ingestionResponse.job_id;
            });
            this.addOrUpdateJobs();
          }
        },
      });
  }
  addOrUpdateJobs() {
    this.extractionIngestionService
      .createUpdateJob(this.s3UrlExtractedModel)
      .subscribe({
        next: (result: StatusResponse) => {
          this.isLoader = false;
          if (result != null && result.isSuccess) {
            this.clearFileInput();
            this.router.navigate([
              "/fetch-file",
              this.s3UrlExtractedModel[0].processId,
            ]);
          }
        },
      });
  }
  ngOnDestroy(): void {
    if (this.dataRefreshSubscription) {
      this.dataRefreshSubscription.unsubscribe();
    }
  }
  setupAutoRefresh(): void {
    this.dataRefreshSubscription = interval(this.REFRESH_INTERVAL).subscribe(
      () => {
        this.currentOffset = 0; // Reset pagination on refresh
        this.hasMoreData = true;
        this.getIngestedData(false);
      }
    );
  }
  filterGrid(value: string) {
    // Reset pagination when filtering
    this.currentOffset = 0;
    this.hasMoreData = true;

    if (value) {
      this.gridFilter = {
        logic: "or",
        filters: [
          { field: "companyName", operator: "contains", value: value },
          { field: "state", operator: "contains", value: value },
          { field: "status", operator: "contains", value: value },
          { field: "period", operator: "contains", value: value },
          { field: "name", operator: "contains", value: value },
          { field: "extractionType", operator: "contains", value: value },
        ],
      };
      this.spreadDetails = filterBy(this.spreadDetailsClone, this.gridFilter);
    } else {
      this.spreadDetails = [...this.spreadDetailsClone];
    }
  }

  /**
   * Method triggered when user scrolls to the bottom of the grid
   */
  onScrollEnd() {
    if (!this.gridLoading && this.hasMoreData) {
      this.currentOffset += this.pageLimit;
      this.getIngestedData(true);
    }
  }
  onExtractionTypeChange(value: string): void {
    this.ingestionForm.get("extractionType").setValue(value);
    this.modules = [];
    this.clearFileInput();
    if (value === DataIngestionConstants.SpecificKPI) {
      this.clearAsExtractionValuesValidators();
      this.modules = [{ id: 13, name: "Fund" }];
      this.ingestionForm.get("module").setValue(this.modules[0]);
      this.ingestionForm.get("sourceType").setValue(this.sourceTypes[0]);
      this.setupSpecificKPIMode();
      this.specificKpiSetValidation();
     this.isFundKpisLoaded=false;
      this.isPcKpisLoaded=false;
    }
    if (value === DataIngestionConstants.AsIsExtraction) {
      this.clearSpecificKpiValuesValidation();
      this.modules = [{ id: 14, name: "Portfolio Company" }];
      this.ingestionForm.get("module").setValue(this.modules[0]);
      this.setupAsIsExtractionMode();
      this.specificKpiRemoveValidation();
      this.isFundKpis = false;
      this.isCompanyKpis = false;
      this.isFundKpisLoaded=true;
      this.isPcKpisLoaded=true;
      this.allMappedPcKpis = [];
      this.companyWithUnmapperdKpi = [];
      this.allMappedFundKpis = [];
      this.fundRepositoryStructure = undefined;
    }
  }
  setupSpecificKPIMode(): void {
    this.clearRequiredValidators();
    this.specificKpi = true;
    this.asIsExtraction = false;
  }
  setupAsIsExtractionMode(): void {
    this.setRequiredValidators();
    this.asIsExtraction = true;
    this.specificKpi = false;
  }
  clearRequiredValidators(): void {
    this.ingestionForm.get("company").clearValidators();
    this.ingestionForm.get("sourceType").clearValidators();
    this.ingestionForm.get("companyIssuers").clearValidators();
    this.updateFormControlsValidity();
  }
  setRequiredValidators(): void {
    this.ingestionForm.get("company").setValidators([Validators.required]);
    this.ingestionForm.get("sourceType").setValidators([Validators.required]);
    this.updateFormControlsValidity();
  }
  updateFormControlsValidity(): void {
    this.ingestionForm.get("company").updateValueAndValidity();
    this.ingestionForm.get("sourceType").updateValueAndValidity();
  }
  specificKpiSetValidation() {
    // this.ingestionForm.get("sourceType").setValidators([Validators.required]);
    this.ingestionForm.get("funds").setValidators([Validators.required]);
    this.specificKpiUpdateValidation();
  }
  specificKpiRemoveValidation() {
    this.ingestionForm.get("funds").clearValidators();
    this.ingestionForm.get("companyIssuers").clearValidators();
    this.specificKpiUpdateValidation();
  }
  specificKpiUpdateValidation() {
    this.ingestionForm.get("funds").updateValueAndValidity();
  }
  clearAsExtractionValuesValidators(): void {
    const controlsToReset = [
      "company",
      "sourceType",
    ];
    this.resetFormControls(
      controlsToReset,
      this.updateFormControlsValidity.bind(this)
    );
  }
  clearSpecificKpiValuesValidation() {
    const controlsToUpdate = [
      "funds",
      "companyIssuers",
      "sourceType",
     
    ];
    this.resetFormControls(
      controlsToUpdate,
      this.specificKpiUpdateValidation.bind(this)
    );
  }
  resetFormControls(controlNames: string[], updateCallback: Function): void {
    controlNames.forEach((controlName) => {
      const control = this.ingestionForm.get(controlName);
      if (control) {
        control.clearValidators();
        if (controlName === "companyIssuers")
          control.setValue([]);
        else control.setValue(null);
        control.markAsUntouched();
        control.markAsPristine();
      }
    });
    if (updateCallback) {
      updateCallback();
    }
  }
  tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags];
  }
  resetBeforeNext() {
    this.ingestionTypeForm.reset();
    this.nextStepper = false;
    this.asIsExtraction = false;
    this.specificKpi = false;
    this.modules = [];
  }
  resetAll() {
    this.reset();
    this.specificKpi = false;
    this.asIsExtraction = false;
  }
  loadInvestmentDetails(): void {
    this.dataIngestion.getInvestmentDetails().subscribe({
      next: (data: any) => {
        this.investmentDetails = data;
        this.investmentDetailsClone = [...this.investmentDetails];
      },
      error: (error) => {
        this.investmentDetails = [];
      },
    });
  }
  getFundKpiDetails(): void {
    this.dataIngestion.getFundKpiDetails().subscribe({
      next: (data: any) => {
        this.fundKpiList = data;
        this.fundKpiListClone = [...this.fundKpiList];
      },
      error: (error) => {
        this.fundKpiList = [];
      },
    });
  }
  filterArrayObjects(event: string, type: string): void {
    if (type === "funds") {
      DataIngestionUtility.filterArrayObjects(
        event,
        this.investmentDetailsClone,
        (filtered) => {
          this.investmentDetails = filtered;
        },
        ["fundName"]
      );
    } else if (type === "KPIs") {
      DataIngestionUtility.filterArrayObjects(
        event,
        this.fundKpiListClone,
        (filtered) => {
          this.fundKpiList = filtered;
        },
        ["aliasName", "name"]
      );
    }
  }
  onFundChange(event: any): void {
    if (event == undefined) {
      this.issuerDetails = [];
      this.selectedFund = null;
    }
    this.isFundSelected = false;
    this.isFundKpis = false;
    this.isCompanyKpis = false;
    this.isFundKpisLoaded = false;
    this.isPcKpisLoaded = false;
    this.allMappedPcKpis = [];
    this.companyWithUnmapperdKpi = [];
    this.kpi_json = {
      module_name: "fund",
      investment_name: "",
      kpis_list: [],
      issuer_kpis_list: []
    };
    this.removeIssuerValidation();
    this.selectedFund = event;
    this.kpi_json.investment_name = this.selectedFund?.fundName;
    this.dataIngestion.getCompaniesKpiDetails(event.fundId).subscribe({
      next: (data: CompanyKpiTreeItem[]) => {
        this.issuerFullDetails = data;
        this.issuerDetails = data.map(({ items, ...rest }) => rest);
        this.isFundSelected = true;
        if (event.encryptedFundId) {
          this.repositoryConfigService.getRepositoryStructureData(event?.encryptedFundId, FeaturesEnum.Fund).subscribe({
            next: (response) => {
              this.fundRepositoryStructure = response?.data || [];
              this.isFundSelected = this.fundRepositoryStructure.length !== 0;
            },
            error: (error) => {
              this.fundRepositoryStructure = [];
              this.toasterService.error('Failed to load repository structure', 'Error');
            }
          });
        }
      },
      error: (error) => {
        this.issuerDetails = [];
        this.isFundSelected = false;
      },
    });
  }
  groupByPortfolioCompanyId(kpis: any[]): { [key: string]: any[] } {
    return kpis.reduce((acc, item) => {
      const key = item.portfolioCompanyId;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(item);
      return acc;
    }, {} as { [key: string]: typeof this.allMappedPcKpis });
  }
  onIssuerSelectionChange(selection: any[]): void {
    this.kpi_json.issuer_kpis_list = [];
    if (!selection || selection.length === 0) {
      this.finalIssuerDetails = [];
      this.isPcKpisLoaded = false;
      this.selectedIssuers = [];
      return;
    }
    const selectedIds = selection.map(item =>
      typeof item === "object" ? item.fieldId : item
    );
    const companyIds = selectedIds.join(",");
    this.finalIssuerDetails = DataIngestionUtility.createSelectedIssuerDetailsHierarchy(
      this.issuerDetails,
      selectedIds
    );

    this.kpiDataService.getAllMappedKpis(companyIds).subscribe({
      next: (data) => {
        if (data?.body?.length > 0) {
          const kpiByCompany = this.groupByPortfolioCompanyId(data.body);
          const groupedKeys = Object.keys(kpiByCompany); // array of keys present in grouped object

          // companyIds is a comma-separated string, so split it into an array
          const companyIdArray = companyIds.split(',');

          // Find missing companyIds
          this.companyWithUnmapperdKpi = companyIdArray.filter(id => !groupedKeys.includes(id)).map(x => Number(x));

          this.allMappedPcKpis = data.body;

          const issuer_kpis_list = selection.filter(x => !this.companyWithUnmapperdKpi.includes(x.fieldId))
            .filter(item => typeof item === "object")
            .map(item => {
              const companyData: any = {
                metadata: {
                  name: item.text,
                  id: item.fieldId?.toString(),
                  sections: []
                }
              };

              const mappedKpis = this.allMappedPcKpis.filter(
                kpi => kpi.portfolioCompanyId === item.fieldId
              );
              const companyDetailById = this.issuerFullDetails.find(x => x.fieldId === item.fieldId);
              if(companyDetailById){
                const masterData = companyDetailById?.items?.find(x => x.name === "MasterData");
                if(masterData){
                  const staticData = masterData?.items;
                  if(staticData && staticData.length > 0){
                    companyData["staticInformation"] = (staticData as any[]).map(kpi => ({
                      name: kpi.text,
                      synonyms: [],
                      kpiId: kpi.kpiId,
                      id: this.payloadKpiCount++,
                      mappingKPIId: 0,
                      definition: "",
                      type: "",
                      columns: [],
                      source: "s3://"
                    }));
                  }
                }
              }
              if (mappedKpis.length > 0) {
                // Group KPIs by moduleId
                const groupedByModuleId = mappedKpis.reduce((acc, kpi) => {
                  const key = kpi.moduleId;
                  if (!acc[key]) acc[key] = [];
                  acc[key].push(kpi);
                  return acc;
                }, {} as { [key: number]: typeof mappedKpis });

                // Add grouped KPIs to companyData
                Object.entries(groupedByModuleId).forEach(([moduleId, kpis]) => {
                  const moduleName = this.getModuleName(Number(moduleId));
                  if (moduleName) {
                    companyData[moduleName] = (kpis as any[]).map(kpi => ({
                      name: kpi.kpiName,
                      synonyms: kpi.synonym ? kpi.synonym.split(",") : [],
                      kpiId: kpi.kpiId,
                      id: this.payloadKpiCount++,
                      mappingKPIId: kpi.mappingKPIId,
                      definition: kpi.definition,
                      type: this.getKpiType(kpi.kpiInfo),
                      columns: [],
                      source: "s3://"
                    }));
                  }
                });
              }
              return companyData;
            });

          if (issuer_kpis_list.length > 0) {
            this.kpi_json.issuer_kpis_list = issuer_kpis_list;
            console.log(this.kpi_json);
          }
          this.isPcKpisLoaded = this.companyWithUnmapperdKpi?.length == 0;
          if (this.isFundKpis && this.allMappedFundKpis?.length == 0) {
            this.isPcKpisLoaded = false;
          }
          if (this.isFundKpis && this.allMappedFundKpis?.length > 0 && this.companyWithUnmapperdKpi?.length > 0) {
            this.isFundKpisLoaded = false;
          }
        } else {
          this.allMappedPcKpis = [];
          this.companyWithUnmapperdKpi = [];
          this.isPcKpisLoaded = false;
          this.isFundKpisLoaded = false;
          this.kpi_json.issuer_kpis_list = [];
        }
        this.selectedIssuers = selection;
      },
      error: (error) => {
        console.error("Error fetching mapped KPIs:", error);
        this.selectedIssuers = selection;
      }
    });
  }
  getKpiType(kpiInfo: string){
    return DataIngestionUtility.getKpiType(kpiInfo);
  }
  getModuleName(moduleId: number) {
    return DataIngestionUtility.getModuleNameFromMap(moduleId, this.kpiModuleAlias);
  }
  getFundModuleName(moduleId: number) {
    return DataIngestionUtility.getFundModuleNameFromMap(moduleId, this.kpiModuleAlias);
  }
  loadStatusCounts(): void {
    this.extractionIngestionService
      .getStatusCount()
      .subscribe((result) => (this.statusCounts = result));
  }
  isIndeterminate(selectedList: any[], totalList: any[]): boolean {
    return (
      selectedList.length !== 0 && selectedList.length !== totalList.length
    );
  }
  onCompanyChange(event: any): void {
    if (!event) {
      return;
    }
    this.companyEncryptedID = event.encryptedPortfolioCompanyID;
    if (!this.companyEncryptedID) {
      this.toasterService.warning('Portfolio ID not available');
      return;
    }

    this.repositoryConfigService.getRepositoryStructureData(this.companyEncryptedID, FeaturesEnum.PortfolioCompany).subscribe({
      next: (response) => {
        this.repositoryStructure = response?.data || [];  // Assign the response data or empty array

      },
      error: (error) => {
        this.repositoryStructure = [];
        this.toasterService.error('Failed to load repository structure', 'Error');
      }
    });
  }
  removeIssuerValidation() {
    this.ingestionForm.get("companyIssuers").setValue([]);
    this.ingestionForm.get("companyIssuers").clearValidators();
    this.ingestionForm.get("companyIssuers").updateValueAndValidity();
  }
  async onSelectKpiForClick(event: any, type: string): Promise<void> {
    if (type === 'fund') {
      const isChecked = event?.srcElement?.checked;
      if (isChecked && this.selectedFund?.encryptedFundId) {
        try {
          const result = await firstValueFrom(
            this.portfolioCompanyService.getKPIMappingListForExtraction(this.selectedFund.encryptedFundId)
          );
          const resp = result;
          if (resp?.code === "OK" && Array.isArray(resp.body) && resp.body.length > 0) {
            this.allMappedFundKpis = resp.body;
            const groupedByModuleId = resp.body.reduce((acc, kpi) => {
              const key = kpi.moduleId;
              if (!acc[key]) acc[key] = [];
              acc[key].push(kpi);
              return acc;
            }, {} as { [key: number]: typeof resp.body });
            const kpiList = {};

            // Add grouped KPIs to companyData
            Object.entries(groupedByModuleId).forEach(([moduleId, kpis]) => {
              const moduleName = this.getFundModuleName(Number(moduleId));
              if (moduleName) {
                kpiList[moduleName] = (kpis as any[]).map(kpi => ({
                  name: kpi?.name,
                synonyms: kpi?.synonym ? kpi.synonym.split(",") : [],
                kpiId: kpi?.id,
                id: this.payloadKpiCount++,
                mappingKPIId: kpi?.mappingKPIId,
                definition: kpi?.definition,
                type: this.getKpiType(kpi?.kpiInfo),
                columns: [],
                source: "s3://"
                }));
              }
            });
            this.kpi_json.kpis_list = kpiList ? [kpiList] : [];
            console.log(this.kpi_json);
            this.isFundKpisLoaded = true;
            if ((this.isCompanyKpis && this.companyWithUnmapperdKpi?.length > 0) || (this.isCompanyKpis && this.allMappedPcKpis?.length === 0 && this.companyWithUnmapperdKpi?.length === 0)) {
              this.isFundKpisLoaded = false;
            }
          } else {
            this.isFundKpisLoaded = false;
            this.isPcKpisLoaded = false;
            this.allMappedFundKpis = [];
            this.kpi_json.kpis_list = [];
          }
        } catch (error) {
          this.isFundKpisLoaded = false;
          this.isPcKpisLoaded = false;
          this.allMappedFundKpis = [];
          this.kpi_json.kpis_list = [];
        }
        this.isFundKpis = true;
      } else {
        this.isFundKpis = false;
        this.isFundKpisLoaded = false;
        this.kpi_json.kpis_list = [];
        if (this.isCompanyKpis && this.allMappedPcKpis?.length > 0 && this.companyWithUnmapperdKpi?.length == 0) {
          this.isPcKpisLoaded = true;
        }
        else if ((this.isCompanyKpis && this.companyWithUnmapperdKpi?.length > 0) || (this.isCompanyKpis && this.allMappedPcKpis.length === 0 && this.companyWithUnmapperdKpi?.length === 0)) {
          this.isPcKpisLoaded = false;
        }
      }
    } else if (type === 'portfolioCompany') {
      if (event?.srcElement?.checked) {
        this.ingestionForm.get("companyIssuers").setValidators([Validators.required]);
        this.ingestionForm.get("companyIssuers").updateValueAndValidity();
        this.isCompanyKpis = true;
        this.ingestionForm.get("companyIssuers").setValue(this.issuerDetails);
        this.onIssuerSelectionChange(this.issuerDetails);
      }
      else {
        this.isPcKpisLoaded = false;
        this.selectedIssuers = [];
        this.removeIssuerValidation();
        if (this.isFundKpis && this.allMappedFundKpis?.length > 0) {
          this.isFundKpisLoaded = true;
        }
        this.isCompanyKpis = false;
      }
      this.allMappedPcKpis = [];
      this.companyWithUnmapperdKpi = [];
      this.kpi_json.issuer_kpis_list = [];
    }
  }
  configure(): void {
    this.router.navigate(['/repository-configuration']);
  }
   getPageConfigSetting = () => {
        this.pageConfigurationService.getPageConfigSettingById(11).subscribe(
            (result: any) => {
                if (result) {
var documentsPageConfig = result.fieldValueList?.find((x: any) => x.name == this.DataIngestion.SpecificKpiExtraction);
this.specificKpiDisplayName = documentsPageConfig ? documentsPageConfig.displayName : '';
var asIsExtractionConfig = result.fieldValueList?.find((x: any) => x.name == this.DataIngestion.AsIsExtraction);
this.asIsExtractionDisplayName = asIsExtractionConfig ? asIsExtractionConfig.displayName : '';
                }
            },
        );
    };

  /**
   * Checks if both extraction types are currently disabled.
   * @returns true if both Specific KPI Extraction and AsIs Extraction are disabled, false otherwise.
   */
  areBothExtractionTypesDisabled(): boolean {
    return !this.showSpecificKpiRadio && !this.showAsIsExtractionRadio;
  }

  /**
   * Checks if Initiate Ingestion should be disabled.
   * It should be disabled when both extraction types are disabled.
   * @returns true if Initiate Ingestion should be disabled, false otherwise.
   */
  shouldDisableInitiateIngestion(): boolean {
    return this.areBothExtractionTypesDisabled();
  }
}