/* tslint:disable:no-unused-variable */
import {ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { FinancialsBetaComponent } from './financials-beta.component';
import { PeriodType } from 'src/app/services/miscellaneous.service';
describe('FinancialsBetaComponent', () => {
  let component: FinancialsBetaComponent;
  let fixture: ComponentFixture<FinancialsBetaComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ FinancialsBetaComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FinancialsBetaComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  
  it('should set period type to Monthly', () => {
    component.setPeriodType('Monthly');
    expect(component.selectedPeriodType).toBe(PeriodType.Monthly);
    expect(component.isMonthly).toBe(true);
    expect(component.isQuarterly).toBe(false);
    expect(component.isAnnually).toBe(false);
  });

  it('should set period type to Quarterly', () => {
    component.setPeriodType('Quarterly');
    expect(component.selectedPeriodType).toBe(PeriodType.Quarterly);
    expect(component.isMonthly).toBe(false);
    expect(component.isQuarterly).toBe(true);
    expect(component.isAnnually).toBe(false);
  });

  it('should set period type to Annually', () => {
    component.setPeriodType('Annually');
    expect(component.selectedPeriodType).toBe(PeriodType.Annually);
    expect(component.isMonthly).toBe(false);
    expect(component.isQuarterly).toBe(false);
    expect(component.isAnnually).toBe(true);
  });
  it('should filter the currency list based on the provided value', () => {
    // Arrange
    const value = 'usd';
    const expectedFilteredCurrencyList = [
      { currencyCode: 'USD', currency: 'US Dollar' },
      { currencyCode: 'AUD', currency: 'Australian Dollar' },
    ];
    
    // Act
    component.handleFilter(value);
    
    // Assert
    expect(component.filteredCurrencyList).toEqual(expectedFilteredCurrencyList);
  });
});
