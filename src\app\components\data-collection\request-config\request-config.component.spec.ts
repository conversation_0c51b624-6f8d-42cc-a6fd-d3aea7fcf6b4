import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientModule } from '@angular/common/http';
import { RequestConfigComponent } from './request-config.component';
import { InputsModule } from '@progress/kendo-angular-inputs';
import { GridModule } from '@progress/kendo-angular-grid';
import { MatIconModule } from '@angular/material/icon';
import { NO_ERRORS_SCHEMA } from "@angular/core";

describe('RequestConfigComponent', () => {
  let component: RequestConfigComponent;
  let fixture: ComponentFixture<RequestConfigComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientModule,InputsModule,GridModule,MatIconModule],
      declarations: [RequestConfigComponent],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        { provide: 'BASE_URL', useValue: 'http://example.com' }
      ],
    });
    fixture = TestBed.createComponent(RequestConfigComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  
  it('should filter grid when value is provided', async () => {
    component.configRequestClone = [{ test: 'testValue' }];
    component.requestConfigColumns = [{ field: 'test' }];
    await component.filterGrid('testValue');
    expect(component.configRequest).toEqual([{ test: 'testValue' }]);
});

  it('should not filter grid when value is not provided', () => {
    component.filterGrid('');
    expect(component.configRequest).toEqual(component.configRequestClone);
  });
});
