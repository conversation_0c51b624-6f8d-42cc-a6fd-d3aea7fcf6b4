@import "../../../../variables";
.portfolio-detail-component {
    .chart-bg {
        padding: 0px !important;
        margin: 0px !important;
    }
}

.allvalues{
  float:left;
  font-size:12px;
  margin-right:12px;
  color: $nep-icon-grey;
  padding-left: 16px !important;
  padding-top: 8px !important;
}
.master-kpi-beta-panel{
  ::ng-deep  .panel{
    border:0px solid transparent !important;
    border-radius:0px
  }
}
.QMY_Container{
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border: 1px solid #DEDFE0;
  border-radius: 4px;
  opacity: 1;
  cursor: pointer;
}
.QMY_Text{
  text-align: left;
  font-size: 14px;
  letter-spacing: 0px;
  color: #55565A;
  opacity: 1;
}
.QMYStyle{
  position: relative;
  top: -0.25rem;
  padding: 0.2rem 0.75rem 0.2rem 0.75rem;
}
.YStyle{
  margin-right: 0.25rem !important;
}

.activeQMY{
  background: #F7F8FC 0% 0% no-repeat padding-box !important;
  color: #4061C7 !important;
  border-radius: 4px;
}
.headerSize {
  font-size: 1.5rem;
  padding-top:4px;
  padding-right: 12px;
}
.backgroundColor{
  background-color: white !important;
}
.topBorder{
  border-top: 1px solid #dee2e6 !important; 
}
.custom-padding{
  padding-left: 2px !important;
  padding-right: 2px !important;
}
.custom-filter{
  padding-right: 8px !important;
  padding-top: 0px !important;margin-top: 3px!important;
}
.tr-all-values
{
  letter-spacing: 0.14px;
  color: #55565A;
  opacity: 1;
  padding-top: 2px !important;
}
.tr-all-values-l{
  padding: 6px 15px;
}
.tr-all-values-l-right{
  padding-right: 0.8125rem;
}
.tr-all-values-right
{
  padding-right: 12px;
  margin-right:0px !important;
}
::ng-deep .attribute-table-con thead tr th:last-child {
  border-right: none !important;
}
::ng-deep .attribute-table-con tbody tr td:last-child {
  border-right: none !important;
}
.value-type-switch-style {
  background: #EDEDF2 0% 0% no-repeat padding-box;
  border-radius: 1.25rem;
  opacity: 1;
  padding: 0.4rem 0.75rem;
  color: #2b2b33;
  margin-left: 0.75rem !important;
  position: relative;
  top: -0.2rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.value-type-text-style{
  position: relative;
  left: 1px;
}

.active-value-type-style{
  background: #4061C7 0% 0% no-repeat padding-box !important;
  color: #ffffff !important;
}

.disable-value-type-style{
  background: #FAFAFC 0% 0% no-repeat padding-box !important;
  color: #ABABB3 !important;
  cursor: not-allowed !important;
}
.comm-footnote{
  position: relative !important;
  z-index: 1;
}
