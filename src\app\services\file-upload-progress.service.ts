import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, Subscription } from 'rxjs';
import { HttpClient, HttpEventType, HttpRequest, HttpResponse } from '@angular/common/http';
import { map, catchError, tap, finalize } from 'rxjs/operators';
import { FileUploadService } from './file-upload.service';

export interface FileUploadProgress {
  id: number;
  fileName: string;
  fileSize?: string;
  progress: number;
  status: 'uploaded' | 'progress' | 'failed';
  message?: string;
  organization: string;
  moduleType?: string; // Added to track which module the file belongs to
  timestamp: Date; // Added timestamp for sorting
}

@Injectable({
  providedIn: 'root'
})
export class FileUploadProgressService {
  private _uploadProgressMap = new Map<number, FileUploadProgress>();
  private _uploadProgress = new BehaviorSubject<FileUploadProgress[]>([]);
  public uploadProgress$ = this._uploadProgress.asObservable();

  private _newUploadCount = new BehaviorSubject<number>(0);
  public newUploadCount$ = this._newUploadCount.asObservable();

  private subscription: Subscription;
  private nextId = 1; // For generating unique IDs

  constructor(private http: HttpClient, private fileUploadService: FileUploadService) {
    // Subscribe to existing file upload service events
    this.subscription = this.fileUploadService.events$.subscribe(event => {
      if (event) {
        this.handleFileUploadEvent(event);
      }
    });
  }

  private handleFileUploadEvent(event: any): void {
    // Check if this is a file upload related event
    if (event.fileId || event.fileName) {
      const fileId = event.fileId || this.generateFileId();
      const { progress, status, message, fileName, fileSize, organization, moduleType } = event;
      
      // If this is a new file being tracked
      if (!this._uploadProgressMap.has(fileId)) {
        this.trackUpload(
          fileId, 
          fileName || 'Document', 
          fileSize || this.formatFileSize(0), 
          organization || 'Unknown',
          moduleType
        );
      }
      
      // Update progress
      if (progress !== undefined) {
        this.updateUploadProgress(fileId, progress);
      }
      
      // Handle status updates
      if (status === 'success' || status === 'complete' || status === 'uploaded') {
        this.completeUpload(fileId, true, message || 'File uploaded successfully');
      } else if (status === 'error' || status === 'failed') {
        this.completeUpload(fileId, false, message || 'Upload failed');
      }
    }
  }

  getProgress(): FileUploadProgress[] {
    return Array.from(this._uploadProgressMap.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()); // Sort by timestamp, newest first
  }

  private updateProgress() {
    this._uploadProgress.next(this.getProgress());
  }

  // Format file size to human-readable format
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Generate a unique file ID
  private generateFileId(): number {
    return this.nextId++;
  }

  trackUpload(id: number, fileName: string, fileSize: string, organization: string, moduleType?: string): Observable<FileUploadProgress> {
    // Initial status
    const progress: FileUploadProgress = {
      id,
      fileName,
      fileSize,
      progress: 0,
      status: 'progress',
      message: 'Please wait, document is getting upload',
      organization,
      moduleType,
      timestamp: new Date()
    };
    
    this._uploadProgressMap.set(id, progress);
    this.updateProgress();
    this.incrementNewUploadCount();
    
    return of(progress);
  }

  updateUploadProgress(id: number, progress: number): void {
    const currentProgress = this._uploadProgressMap.get(id);
    if (currentProgress) {
      this._uploadProgressMap.set(id, {
        ...currentProgress,
        progress,
        timestamp: new Date() // Update timestamp
      });
      this.updateProgress();
    }
  }

  completeUpload(id: number, success: boolean, message?: string): void {
    const currentProgress = this._uploadProgressMap.get(id);
    if (currentProgress) {
      this._uploadProgressMap.set(id, {
        ...currentProgress,
        status: success ? 'uploaded' : 'failed',
        progress: success ? 100 : currentProgress.progress,
        message: message || (success ? 'Now you can see the information on table' : 'Upload failed'),
        timestamp: new Date() // Update timestamp
      });
      this.updateProgress();
    }
  }

  retryUpload(id: number): void {
    const currentProgress = this._uploadProgressMap.get(id);
    if (currentProgress) {
      // Update the progress status to "in progress"
      this._uploadProgressMap.set(id, {
        ...currentProgress,
        status: 'progress',
        progress: 0,
        message: 'Please wait, retrying upload...',
        timestamp: new Date() // Update timestamp
      });
      this.updateProgress();
      
      // In a real implementation, we would need to retry the actual upload
      // based on the module type and other parameters stored in the progress object
      if (currentProgress.moduleType) {
        // Actual implementation would retry the specific upload based on module type
        this.simulateUploadByModuleType(id, currentProgress.moduleType);
      } else {
        this.simulateUpload(id);
      }
    }
  }

  private simulateUploadByModuleType(id: number, moduleType: string): void {
    // In a real implementation, this would call different upload methods based on the module type
    console.log(`Simulating upload for module type: ${moduleType}`);
    this.simulateUpload(id);
  }

  private simulateUpload(id: number): void {
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      this.updateUploadProgress(id, progress);
      
      if (progress >= 100) {
        clearInterval(interval);
        this.completeUpload(id, true);
      }
    }, 500);
  }

  removeUpload(id: number): void {
    this._uploadProgressMap.delete(id);
    this.updateProgress();
  }

  getUploadsByOrganization(organization: string): FileUploadProgress[] {
    return this.getProgress().filter(progress => progress.organization === organization);
  }

  incrementNewUploadCount() {
    this._newUploadCount.next(this._newUploadCount.value + 1);
  }

  resetNewUploadCount() {
    this._newUploadCount.next(0);
  }

  // Method to track an actual file upload using HttpClient
  trackHttpUpload(
    file: File, 
    url: string, 
    organization: string, 
    moduleType?: string, 
    additionalData?: any
  ): Observable<any> {
    const fileId = this.generateFileId();
    
    // Create FormData for the request
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });
    }
    
    // Track the upload in our progress service
    this.trackUpload(
      fileId,
      file.name,
      this.formatFileSize(file.size),
      organization,
      moduleType
    );
    
    // Create the request with progress reporting
    const req = new HttpRequest('POST', url, formData, {
      reportProgress: true
    });
    
    return this.http.request(req).pipe(
      map(event => {
        if (event.type === HttpEventType.UploadProgress && event.total) {
          const progress = Math.round(100 * event.loaded / event.total);
          this.updateUploadProgress(fileId, progress);
          return { progress, fileId };        } else if (event instanceof HttpResponse) {
          this.completeUpload(fileId, true, 'File uploaded successfully');
          return { response: event.body, fileId };
        }
        return null;
      }),
      catchError(error => {
        this.completeUpload(fileId, false, error.message || 'Upload failed');
        return of({ error, fileId });
      }),
      finalize(() => {
        // Ensure the upload is marked as complete even if there's an error
        const progress = this._uploadProgressMap.get(fileId);
        if (progress && progress.status === 'progress') {
          this.completeUpload(fileId, false, 'Upload interrupted');
        }
      })
    );
  }
}
