import { CdkDragDrop, transferArrayItem, moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, EventEmitter, Input, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { NgForm } from '@angular/forms';
import { ComponentCanDeactivate } from 'src/app/unsaved-changes/can-deactivate/component-can-deactivate';
import { PanelbarItemService } from './panelbar-item.service';
import { FeatureTableMapping } from 'src/app/common/constants';
interface PreviousValue {
  id: string;
  value: string;
}
@Component({
  selector: 'app-panelbar-item',
  templateUrl: './panelbar-item.component.html',
  styleUrls: ['./panelbar-item.component.scss'],
  encapsulation: ViewEncapsulation.None
})

export class PanelbarItemComponent1  extends ComponentCanDeactivate
  implements OnInit {
     @ViewChild("f", { static: true }) ngform: NgForm;
     @ViewChild("f")
     form: NgForm;
     @Input() subPageList:any[];
     @Input() subPageListClone:any[];
     @Output() checkAnyDataChangeEmmiter=new EventEmitter<any>();



     cloPageList: any = [];
     clonodataPath = 'assets/dist/images/clonodata.svg'; 
     arrowIconPath = 'assets/dist/images/arrow-icon.svg';
     featureTableMapping=FeatureTableMapping;
     constructor(
       private panelbarItemService:PanelbarItemService,
     ) {
         super();
     }
     ngOnInit() {       
       this.ngform.valueChanges.subscribe(() => {});
     }
      canDeactivate(): boolean {
       return !this.form.form.valid ;
     }
   
     localCanDeactivate(): boolean {
       return !this.form.form.valid ;
     }
     
     previousValues: PreviousValue[] = [];
     onTabToggle = (currentItem: any) => {
       this.subPageList
         .filter((x) => x.tabId != currentItem.tabId)
         .forEach((x) => (x.isTabExpanded = false));
       currentItem.isTabExpanded = !currentItem.isTabExpanded;
     };
     onTabToggle1 = (currentItem: any,page:any) => {
      page.subTabList
        .filter((x) => x.tabId != currentItem.tabId)
        .forEach((x) => (x.isTabExpanded = false));
      currentItem.isTabExpanded = !currentItem.isTabExpanded;
    };
    dropForTable(event: CdkDragDrop<any[]>, page: any) {
      this.handleDrop(event, page);
    }
    
    dropForSubTable(event: CdkDragDrop<any[]>, page: any, subPage: any) {
      this.handleDrop(event, page, subPage);
    }
    
    private handleDrop(event: CdkDragDrop<any[]>, page: any, subPage?: any) {
      if(((event.container.data[event.previousIndex].tableId!=this.featureTableMapping.TABLES_NAME.Company_Facts && event.container.data[event.previousIndex].tableId!=this.featureTableMapping.TABLES_NAME.Investment_Summary)
        && ( event.container.data[event.currentIndex].tableId!=this.featureTableMapping.TABLES_NAME.Company_Facts && event.container.data[event.currentIndex].tableId!=this.featureTableMapping.TABLES_NAME.Investment_Summary) )
       && event.container.data.length > 1){
        if (event.previousContainer !== event.container) {
          transferArrayItem(
            event.previousContainer.data,
            event.container.data,
            event.previousIndex,
            event.currentIndex
          );
        } else if (event.previousIndex !== event.currentIndex) {
          moveItemInArray(
            event.container.data,
            event.previousIndex,
            event.currentIndex
          );
      
          const temp = event.previousIndex;
          event.previousIndex = event.currentIndex;
          event.currentIndex = temp;
      
          const subPageID = event.container.data[event.currentIndex]["tabId"] || event.container.data[event.currentIndex]["parentId"];
          let subSectionIndex = this.subPageList?.findIndex((x) => x.tabId == (subPageID || page?.tabId));
          let elements;
      
          if (subPage) {
            let subSectionIndex = this.subPageList?.findIndex((x) => x.tabId == (page?.tabId));
            const sublistSectionIndex = this.subPageList[subSectionIndex]?.subTabList.findIndex((x) => x.tabId == subPage.tabId);
            elements = this.subPageList[subSectionIndex].subTabList[sublistSectionIndex].tableList;
          } else {
            elements = this.subPageList[subSectionIndex].tableList;
          }
      
          elements.forEach((element, index) => {
            element.sequenceNo = index + 1;
          });
          this.checkAnyDataChange();
        }
      }
     
    }

    drop(event: CdkDragDrop<any[]>) {
      this.handlePageDrop(event, false);
    }
    
    dropForPage(event: CdkDragDrop<any[]>) {
      this.handlePageDrop(event, true);
    }
    
    private handlePageDrop(event: CdkDragDrop<any[]>, isPageDrop: boolean) {
      if(((event.container.data[event.previousIndex].tabId!=this.featureTableMapping.TABS_NAME.Key_KPI_History && event.container.data[event.previousIndex].tableId!=this.featureTableMapping.TABLES_NAME.Company_Facts && event.container.data[event.previousIndex].tableId!=this.featureTableMapping.TABLES_NAME.Investment_Summary)
         && (event.container.data[event.currentIndex].tabId!=this.featureTableMapping.TABS_NAME.Key_KPI_History && event.container.data[event.currentIndex].tableId!=this.featureTableMapping.TABLES_NAME.Company_Facts && event.container.data[event.currentIndex].tableId!=this.featureTableMapping.TABLES_NAME.Investment_Summary) )
        && event.container.data.length > 1){
        if (event.previousContainer !== event.container) {
          transferArrayItem(
            event.previousContainer.data,
            event.container.data,
            event.previousIndex,
            event.currentIndex
          );
        } else if (event.previousIndex !== event.currentIndex) {
          moveItemInArray(
            event.container.data,
            event.previousIndex,
            event.currentIndex
          );
      
          const temp = event.previousIndex;
          event.previousIndex = event.currentIndex;
          event.currentIndex = temp;
          let elements;
  
  
          if (isPageDrop) {
            elements = this.subPageList;
          } else {
            const subPageID = event.container.data[event.currentIndex]["parentId"];
            const subSectionIndex = this.subPageList?.findIndex((x) => x.tabId == subPageID);
            elements = this.subPageList[subSectionIndex].subTabList;
          }
      
          elements.forEach((element, index) => {
            element.sequenceNo = index + 1;
          });
          this.checkAnyDataChange();
        }
        
      }
      
    }
    
    
    
    checkAnyDataChange() {
      if(this.form.valid){
        if (JSON.stringify(this.subPageListClone) !== JSON.stringify(this.subPageList)) {
          this.checkAnyDataChangeEmmiter.emit({isDisabledBtn:false,subPageList:this.subPageList});
        } 
      }
      else {
        this.checkAnyDataChangeEmmiter.emit({isDisabledBtn:true,subPageList:this.subPageList});
      }
    }
     editMode: boolean[] = [];
     duplicateEmptyMode: boolean[] = [];
     /**
      * Handles the click event when the edit button is clicked.
      * Sets the edit mode for the specified index to true and all other edit modes to false.
      *
      * @param index - The index of the item to be edited.
      */
     onEditClick(index: number) {
       this.editMode.fill(false);
       this.editMode[index] = true;
     }
     /**
      * Handles the keypress event.
      * Prevents input of characters that do not match the specified pattern.
      *
      * @param event - The keypress event object.
      */
     onKeypress(event: any) {
       const pattern = /^[a-zA-Z0-9[\]{}_\@.\/#&+\-\\ ]*$/;
       let inputChar = String.fromCharCode(event.charCode);
       if (!pattern.test(inputChar)) {
         event.preventDefault();
       }
     }
  
     /**
    * Replaces all instances of a search string with a replacement string in the provided text.
    * @param text The original text.
    * @param search The string to search for.
    * @param replacement The string to replace the search string with.
    * @returns The text with all instances of the search string replaced with the replacement string.
    */
     replaceItem(text: string, search: string, replacement: string): string {
       return text.replace(new RegExp(search, 'g'), replacement);
     }
     /**
      * Checks if the given alias name is duplicated within the specified field.
      * @param aliasName - The alias name to check for duplication.
      * @param field - The field object to search for duplicate alias names.
      * @returns A boolean value indicating whether the alias name is duplicated within the field.
      */
     isDuplicateAliasName(aliasName: string, field: any): boolean {
       return (
         field.mSubFields.filter(
           (subField) =>
             subField.aliasName.toLowerCase().trim() === aliasName.toLowerCase().trim()
         ).length > 1
       );
     }
     
     /**
    * Checks if the subField name includes any of the specified statuses.
    * @param subFieldName The name of the subField to check.
    * @returns true if subFieldName includes any of the statuses, false otherwise.
    */
   isStatusIncluded(subFieldName: string | undefined): boolean {
     const statuses = ['Actual', 'LTM', 'YTD'];
     return statuses.some(status => subFieldName?.includes(status));
   }
   getDisable(tableList):boolean{
    return (tableList.length==2 && tableList.findIndex(x=>x.tableId==this.featureTableMapping.TOBE_CHANGE_DOMICILE_TABLES_NAME.Key_KPIs[0])>0)
    || tableList.length <= 1;
   }
}
