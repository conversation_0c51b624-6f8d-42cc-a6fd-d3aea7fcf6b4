import { SelectionModel } from '@angular/cdk/collections';
import { FlatTreeControl } from '@angular/cdk/tree';
import { Component, Injectable, ElementRef, ViewChild, OnInit, Input, AfterViewInit, Output, EventEmitter, ViewEncapsulation,Renderer2 } from '@angular/core';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { BehaviorSubject } from 'rxjs';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { ToastContainerDirective, ToastrService } from "ngx-toastr";
import { KpiTypesConstants,KpiMappingConstants, isFundFinancialsType } from "src/app/common/constants";
import { DropDownFilterSettings, PopupSettings } from '@progress/kendo-angular-dropdowns';
import { GroupResult, groupBy } from "@progress/kendo-data-query";
import { MatChipInputEvent } from "@angular/material/chips";
import { Chips } from "projects/ng-neptune/src/lib/chip/chip.component";
/**
 * Node for to-do item
 */
export class KPIItemNode {
    children: KPIItemNode[];
    id: string;
    name: string;
    isMapped: boolean;
    isBoldKPI: boolean;
    isHeader: boolean;
    parentKPIID: number;
    displayOrder: number;
    oldDisplayOrder: number;
    oldIsMapped: boolean;
    mappingKPIId: number;
    formula: string;
    formulaKPIId: string;
    kpiInfo: string;
    kpiTypeId:number;
    isExtraction: boolean = false;
    oldIsExtraction: boolean = false;
    synonym:string;
}

/** Flat to-do item node with expandable and level information */
export class KPIItemFlatNode {
    id: string;
    name: string;
    isMapped: boolean;
    isBoldKPI: boolean;
    isHeader: boolean;
    parentKPIID: number;
    displayOrder: number;
    oldDisplayOrder: number;
    level: number;
    expandable: boolean;
    mappingKPIId: number;
    oldIsMapped: boolean;
    children: KPIItemNode[];
    formula: string;
    formulaKPIId: string;
    kpiInfo: string;
    kpiTypeId:number;
    isExtraction: boolean = false;
    oldIsExtraction: boolean = false;
    synonym:string;
}
@Injectable()
export class ChecklistDatabase {
    dataChange = new BehaviorSubject<KPIItemNode[]>([]);

    get data(): KPIItemNode[] { return this.dataChange.value; }
    constructor() {
        // constructor
    }

    initialize(treeitem) {
        const data = this.buildFileTree(treeitem, 0);
        this.dataChange.next(data);
    }
    buildFileTree(obj: { [key: string]: any }, level: number, parentId: string = '0'): KPIItemNode[] {
        return Object.keys(obj).reduce<KPIItemNode[]>((accumulator, key, idx) => {
            const value = obj[key];
            const node = new KPIItemNode();
            node.name = value.name;
            node.id = value.id;
            node.isMapped = value.isMapped;
            node.isBoldKPI = value?.isBoldKPI;
            node.isHeader = value?.isHeader;
            node.parentKPIID = value.parentKPIID;
            node.displayOrder = value.displayOrder;
            node.mappingKPIId = value.mappingKPIId;
            node.oldIsMapped = value.oldIsMapped;
            node.oldDisplayOrder = value.oldDisplayOrder;
            node.formula = value.formula;
            node.formulaKPIId = value.formulaKPIId;
            node.kpiInfo = value.kpiInfo;
            node.kpiTypeId = value.kpiTypeId == undefined ? 0 : value.kpiTypeId;
            node.isExtraction = value.isExtraction || false;
            node.oldIsExtraction = value.oldIsExtraction || false;
            node.synonym = value.synonym;
            if (value.children != null) {
                if (typeof value === 'object') {
                    node.children = this.buildFileTree(value.children, level + 1);
                } else {
                    node.name = value;
                }
            }

            return accumulator.concat(node);
        }, []);
    }

    /** Add an item to to-do list */
    insertItem(parent: KPIItemNode, node: any): KPIItemNode {
        if (!parent.children) {
            parent.children = [];
        }
        const newItem = node as KPIItemNode;
        parent.children.push(newItem);
        this.dataChange.next(this.data);
        return newItem;
    }
    insertNewItem(node: any): KPIItemNode {
        const newItem = node as KPIItemNode;
        this.data.splice(this.data.length + 1, 0, newItem);
        this.dataChange.next(this.data);
        return newItem;
    }

    insertItemAbove(node: KPIItemNode, nodenew: any): KPIItemNode {
        const parentNode = this.getParentFromNodes(node);
        const newItem = nodenew as KPIItemNode;
        if (parentNode != null) {
            parentNode.children.splice(parentNode.children.indexOf(node), 0, newItem);
        } else {
            this.data.splice(this.data.indexOf(node), 0, newItem);
        }
        this.dataChange.next(this.data);
        return newItem;
    }

    insertItemBelow(node: KPIItemNode, nodeNew: any): KPIItemNode {
        const parentNode = this.getParentFromNodes(node);
        const newItem = nodeNew as KPIItemNode;
        if (parentNode != null) {
            parentNode.children.splice(parentNode.children.indexOf(node) + 1, 0, newItem);
        } else {
            this.data.splice(this.data.indexOf(node) + 1, 0, newItem);
        }
        this.dataChange.next(this.data);
        return newItem;
    }

    getParentFromNodes(node: KPIItemNode): KPIItemNode {
        const parent = this.data.find(currentRoot => this.getParent(currentRoot, node) !== null);
        return parent || null;
      }

      getParent(currentRoot: KPIItemNode, node: KPIItemNode): KPIItemNode {
        if (currentRoot.children?.length > 0) {
          for (const child of currentRoot.children) {
            if (child === node) {
              return currentRoot;
            } else if (child.children?.length > 0) {
              const parent = this.getParent(child, node);
              if (parent) {
                return parent;
              }
            }
          }
        }
        return null;
      }

    updateItem(node: KPIItemNode, newNode: any) {
        this.dataChange.next(this.data);
    }

    deleteItem(node: KPIItemNode) {
        this.deleteNode(this.data, node);
        this.dataChange.next(this.data);
    }

    copyPasteItem(from: KPIItemNode, to: KPIItemNode): KPIItemNode {
        this.insertItem(to, from);
        return to;
    }

    copyPasteItemAbove(from: KPIItemNode, to: KPIItemNode): KPIItemNode {
        this.insertItemAbove(to, from);
        return to;
    }

    copyPasteItemBelow(from: KPIItemNode, to: KPIItemNode): KPIItemNode {
        this.insertItemBelow(to, from);
        return to;
    }

    deleteNode(nodes: KPIItemNode[], nodeToDelete: KPIItemNode) {
        const index = nodes.indexOf(nodeToDelete, 0);
        if (index > -1) {
            nodes.splice(index, 1);
        } else {
            nodes.forEach(node => {
                if (node.children && node.children.length > 0) {
                    this.deleteNode(node.children, nodeToDelete);
                }
            });
        }
    }
}
@Component({
    selector: 'app-portfolio-company-mapping',
    templateUrl: './portfolio-company-mapping.component.html',
    styleUrls: ['./portfolio-company-mapping.component.scss', '../kpi-list.component.scss'],
    encapsulation: ViewEncapsulation.None,
    providers: [ChecklistDatabase]
})
export class PortfolioCompanyMappingComponent implements OnInit, AfterViewInit {
    popupSettings: PopupSettings = {
        appendTo: "component"
    };
    public isCheckedCopyAll:boolean = false;
    subPageId: number;
    virtualMultiSelect: any = {
        itemHeight: 32,
        pageSize: 3100
      };
    public virtual: any = {
        itemHeight: 32,
        pageSize: 20
      };
      public filterSettings: DropDownFilterSettings = {
        caseSensitive: false,
        operator: 'contains',
      };
    selectedKpiType = { name: KpiTypesConstants.TRADING_RECORDS, field: 'TradingRecords', moduleID: 1, subPageId: 2 };
    kpiType: string = KpiTypesConstants.TRADING_RECORDS;
    @Input() kpiTypes = null;
    companyList: any = [];
    @Input() moduleID: number = null;
    @Input() reload: any;
    selectedCopyToCompanyList: any = [];
    @Output() OnCopyCompanyEmit: EventEmitter<any> = new EventEmitter();
    kpiList = [];
    listKPIList:any = [];
    companyId: string;
    kpiTreeList = [];
    kpiName: string;
    allKpiName:string;
    isHierarchy: boolean = true;
    isCheckedAll: boolean = true;
    isCancelPopup: boolean = false;
    isSavePopup: boolean = false;
    isDisabledCancelBtn = true;
    disableCopyTo = false;
    isDisabledSaveBtn = true;
    isLoader: boolean = false;
    isMapAlertPopup: boolean = false;
    isUnMapped: boolean = false;
    isMappingContinue = false;
    treeHeight: any;
    kpilistheight: any;
    filteredTreeData = [];
    currentselectedthread: KPIItemNode[] = [];
    SearchSelectedTreeData: any[];
    clonekpiTreeList = [];
    isCheck = "N";
    PreviousSelectedTreedata: any;
    searchDataList = [];
    selectedFormulaKPI:any = null;
    flatNodeMap = new Map<KPIItemFlatNode, KPIItemNode>();
    nestedNodeMap = new Map<KPIItemNode, KPIItemFlatNode>();
    selectedParent: KPIItemFlatNode | null = null;
    Emptyischeck: boolean = true;
    /** The new item's name */
    newItemName = '';

    treeControl: FlatTreeControl<KPIItemFlatNode>;

    treeFlattener: MatTreeFlattener<KPIItemNode, KPIItemFlatNode>;

    dataSource: MatTreeFlatDataSource<KPIItemNode, KPIItemFlatNode>;

    /** The selection for checklist */
    checklistSelection = new SelectionModel<KPIItemFlatNode>(true /* multiple */);

    /* Drag and drop */
    dragNode: any;
    allListHidden = true;
    kpiAllListItem:any;
    filteredAllKpiList: string[] = [];
    dragNodeExpandOverWaitTimeMs = 50;
    dragNodeExpandOverNode: any;
    dragNodeExpandOverTime: number;
    dragNodeExpandOverArea: string;
    @ViewChild(KpiMappingConstants.emptyItem) emptyItem: ElementRef;
    @ViewChild(ToastContainerDirective, {})
    toastContainer: ToastContainerDirective;
    isOpenPopUp: boolean = false;
    isEnableView: boolean;
    selectedCompany: any = null;
    deletedMappedkpi: any = [];
    portfolioCompanyList:any=[];
    @ViewChild(KpiMappingConstants.searchKpiButton) searchKpiButton: ElementRef;
    @ViewChild(KpiMappingConstants.searchKpiModal) searchKpiModal: ElementRef;
    OriginalKpiList: any;
    OrginalCompanyList: any =[];
    selectAllExtraction: boolean = false;
    anyExtractionSelected: boolean = false;

    // Add a new selection model specifically for extraction
    extractionSelection = new SelectionModel<KPIItemFlatNode>(true /* multiple */);

    showUpsertSynonymPopUp: boolean = false;
    popUpTitle: string = "";
    primaryButtonName: string = "";
    selectedKpiName: string = "";
    synonym: string = "";
    kpisynonym : string[] = [];
    selectedKpiMappingId: number = 0;
    previousSynonym: string = "";
    disablePrimaryBtn: boolean = true;
    currentNode:any;
    showWarningModel:boolean = false;
    isKpiTypeMarkedForExtraction: boolean = false;
    constructor(private renderer: Renderer2,private database: ChecklistDatabase, private portfolioCompanyService: PortfolioCompanyService, private toastrService: ToastrService) {
        this.treeFlattener = new MatTreeFlattener(this.transformer, this.getLevel, this.isExpandable, this.getChildren);
        this.treeControl = new FlatTreeControl<KPIItemFlatNode>(this.getLevel, this.isExpandable);
        this.dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);
        // cdk tree that mat tree is based on has a bug causing children to not be rendered in the UI without first setting the data to null
        database.dataChange.subscribe(data => {
            this.dataSource.data = data;
        });
        this.renderer.listen('window', 'click',(e:Event)=>{
           if(e.target !== this.searchKpiButton?.nativeElement){
               this.allListHidden=true;
           }
       });
    }
    public tagMapper(tags: any[]): any[] {
        return tags.length < 2 ? tags : [tags];
    }
    clearSearch($event: any) {
        $event.clearFilter();
        $event.filterChange.emit('');
    }
    public get isIndet() {
        return (
            this.selectedCopyToCompanyList.length !== 0 && this.selectedCopyToCompanyList.length !== this.portfolioCompanyList.length
        );
    }
    public get toggleAllText() {
        return this.isCheckedCopyAll ? "Deselect All" : "Select All";
    }
    public onClick(multiSelect: any) {
        this.isCheckedCopyAll = !this.isCheckedCopyAll;
        this.selectedCopyToCompanyList = this.isCheckedCopyAll ? this.portfolioCompanyList.slice() : [];
        this.isDisabledSaveBtn=false;
      }
    getLevel = (node: KPIItemFlatNode) => node.level;

    isExpandable = (node: KPIItemFlatNode) => node.expandable;

    getChildren = (node: KPIItemNode): KPIItemNode[] => node.children;

    hasChild = (_: number, _nodeData: KPIItemFlatNode) => _nodeData.expandable;

    hasNoContent = (_: number, _nodeData: KPIItemFlatNode) => _nodeData.name === '';

    transformer = (node: KPIItemNode, level: number) => {
        const existingNode = this.nestedNodeMap.get(node);
        const flatNode = existingNode && existingNode.name === node.name
            ? existingNode
            : new KPIItemFlatNode();
        flatNode.name = node.name;
        flatNode.id = node.id;
        flatNode.level = level;
        flatNode.isMapped = node.isMapped;
        flatNode.isHeader = node?.isHeader;
        flatNode.isBoldKPI = node?.isBoldKPI;
        flatNode.parentKPIID = node.parentKPIID;
        flatNode.displayOrder = node.displayOrder;
        flatNode.level = level;
        flatNode.mappingKPIId = node.mappingKPIId;
        flatNode.oldIsMapped = node.oldIsMapped;
        flatNode.oldDisplayOrder = node.oldDisplayOrder;
        flatNode.formula = node.formula;
        flatNode.formulaKPIId = node.formulaKPIId;
        flatNode.kpiInfo = node.kpiInfo;
        flatNode.kpiTypeId = node?.kpiTypeId == undefined ? 0 : node.kpiTypeId;
        flatNode.synonym = node.synonym;
        
        // Explicitly convert isExtraction to boolean (treat null/undefined as false)
        flatNode.isExtraction = node.isExtraction === true;
        
        // Ensure oldIsExtraction is properly set to match isExtraction for newly created nodes
        if (node.oldIsExtraction === undefined) {
            flatNode.oldIsExtraction = flatNode.isExtraction;
        } else {
            flatNode.oldIsExtraction = node.oldIsExtraction === true;
        }
        
        if (node.children && node.children.length > 0) {
            flatNode.children = node.children;
        }
        flatNode.expandable = (node.children && node.children.length > 0);
        this.flatNodeMap.set(flatNode, node);
        this.nestedNodeMap.set(node, flatNode);
        return flatNode;
    }

    /** Whether all the descendants of the node are selected */
    descendantsAllSelected(node: KPIItemFlatNode): boolean {
        const descendants = this.treeControl.getDescendants(node);
        return descendants.every(child => this.checklistSelection.isSelected(child));
    }

    /** Whether part of the descendants are selected */
    descendantsPartiallySelected(node: KPIItemFlatNode): boolean {
        const descendants = this.treeControl.getDescendants(node);
        const result = descendants.some(child => this.checklistSelection.isSelected(child));
        return result && !this.descendantsAllSelected(node);
    }

    /** Toggle the to-do item selection. Select/deselect all the descendants node */
    todoItemSelectionToggle(node: KPIItemFlatNode): void {
        this.checklistSelection.toggle(node);
        const descendants = this.treeControl.getDescendants(node);
        this.checklistSelection.isSelected(node)
            ? this.checklistSelection.select(...descendants)
            : this.checklistSelection.deselect(...descendants);
    }

    /** Select the category so we can insert the new item. */
    addNewItem(node: KPIItemFlatNode) {
        const parentNode = this.flatNodeMap.get(node);
        this.database.insertItem(parentNode, null);
        this.treeControl.expand(node);
    }

    /** Save the node to database */
    saveNode(node: KPIItemFlatNode, itemValue: any) {
        const nestedNode = this.flatNodeMap.get(node);
        this.database.updateItem(nestedNode, itemValue);
    }

    handleDragStart(event, node) {
        event.dataTransfer.setData('foo', 'bar');
        event.dataTransfer.setDragImage(this.emptyItem.nativeElement, 0, 0);
        this.dragNode = node;
    }

    handleDragOver(event, node) {
        event.preventDefault();
        // Handle node expand
        if (node === this.dragNodeExpandOverNode) {
            if (this.dragNode !== node && !this.treeControl.isExpanded(node)) {
                if ((new Date().getTime() - this.dragNodeExpandOverTime) > this.dragNodeExpandOverWaitTimeMs) {
                    this.treeControl.expand(node);
                }
            }
        } else {
            this.dragNodeExpandOverNode = node;
            this.dragNodeExpandOverTime = new Date().getTime();
        }

        // Handle drag area
        const percentageY = event.offsetY / event.target.clientHeight;
        if (percentageY < 0.25) {
            this.dragNodeExpandOverArea = KpiMappingConstants.above;
        } else if (percentageY > 0.75) {
            this.dragNodeExpandOverArea = KpiMappingConstants.below;
        } else {
            this.dragNodeExpandOverArea = KpiMappingConstants.center;
        }
    }
    dragAboveFunction(event:any, node:any,dropNode:any,newItem:any) {
        if (node.level == 1 && this.dragNode?.children?.length > 0) {
            this.toastrService.error(KpiMappingConstants.OnlyOneLevelOfHierarchyIsAllowed, "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
            event.preventDefault();
        } else {
            let isLastdropNode = this.getParent(node);
            if (this.isDuplicateCheck(this.flatNodeMap.get(isLastdropNode), this.flatNodeMap.get(dropNode))) {
                this.database.deleteItem(this.flatNodeMap.get(dropNode));
                newItem = this.database.copyPasteItemAbove(dropNode, this.flatNodeMap.get(node));
            }else {
                this.toastrService.error(KpiMappingConstants.MultipleDuplicateNotAllowed, "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
                event.preventDefault();
            }
        }
        return newItem;
    }
    dragBelowFunction(event:any, node:any,dropNode:any,newItem:any) {
        let isLastdropNode = this.getParent(node);
        if(this.dataSource.data[this.dataSource.data.length - 1].id == node.id || this.dataSource.data[this.dataSource.data.length - 1].id == isLastdropNode?.id){
            if(isLastdropNode == null){
                this.database.deleteItem(this.flatNodeMap.get(dropNode));
                newItem = this.database.copyPasteItemBelow(dropNode, this.flatNodeMap.get(node));
            }else{
                this.database.deleteItem(this.flatNodeMap.get(dropNode));
                newItem = this.database.copyPasteItemBelow(dropNode, this.flatNodeMap.get(isLastdropNode));
            }
           
        }
        else if (node.level == 1 && this.dragNode?.children?.length > 0) {
            this.toastrService.error(KpiMappingConstants.OnlyOneLevelOfHierarchyIsAllowed, "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
            event.preventDefault();
        } else {
            if (this.isDuplicateCheck(this.flatNodeMap.get(isLastdropNode), this.flatNodeMap.get(dropNode))) {
                this.database.deleteItem(this.flatNodeMap.get(dropNode));
                newItem = this.database.copyPasteItemBelow(dropNode, this.flatNodeMap.get(node));
            }else {
                this.toastrService.error(KpiMappingConstants.MultipleDuplicateNotAllowed, "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
                event.preventDefault();
            }
        }
        return newItem;
    }
    dragCenterFunction(event:any, node:any,dropNode:any,newItem:any) {
        if (node.level == 0 && (dropNode.children == undefined || dropNode?.children?.length ==0)) {
            if (this.isDuplicateCheck(this.flatNodeMap.get(node), this.flatNodeMap.get(dropNode))) {
                this.database.deleteItem(this.flatNodeMap.get(dropNode));
                newItem = this.database.copyPasteItem(dropNode, this.flatNodeMap.get(node));
            }
            else {
                this.toastrService.error(KpiMappingConstants.MultipleDuplicateNotAllowed, "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
                event.preventDefault();
            }
        } else {
            this.toastrService.error(KpiMappingConstants.OnlyOneLevelOfHierarchyIsAllowed, "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
            event.preventDefault();
        }
        return newItem;
    }
    handleDrop(event, node) {
        event.preventDefault();
        let newItem: KPIItemNode;
        let dropNode = this.dragNode;
        dropNode.parentKPIID = node.id;
        if (node !== this.dragNode) {
            if (node.level < 2) {
               
                dropNode.isMapped = true;
                if (this.dragNodeExpandOverArea === KpiMappingConstants.above) {
                    newItem = this.dragAboveFunction(event, node,dropNode,newItem);

                } else if (this.dragNodeExpandOverArea === KpiMappingConstants.below) {
                    newItem = this.dragBelowFunction(event, node,dropNode,newItem);
                } else {
                    newItem = this.dragCenterFunction(event, node,dropNode,newItem);
                }
            }
            else {
                this.toastrService.error(KpiMappingConstants.OnlyOneLevelOfHierarchyIsAllowed, "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
                event.preventDefault();
            }
            if(newItem){
                this.treeControl.expandDescendants(this.nestedNodeMap.get(newItem));
                this.checklistSelection.select(dropNode);
                this.SetKPIChecked(dropNode, true);
            }
        }
        this.dragNode = null;
        this.dragNodeExpandOverNode = null;
        this.dragNodeExpandOverTime = 0;
        this.isDisabledCancelBtn = false;
        this.isDisabledSaveBtn = false;
        this.ValidateAllChecked();
        this.treeControl.expandAll();
    }
    isDuplicateCheck(node: any, dropNode: any) {
        let isExist = node?.children?.find(
            (x) => x.name.toLowerCase() == dropNode.name.toLowerCase() && x.id != dropNode.id
        );
        if (isExist == undefined) return true;
        else return false;
    }
    handleDragEnd(event) {
        this.dragNode = null;
        this.dragNodeExpandOverNode = null;
        this.dragNodeExpandOverTime = 0;
    }
    groupedData: GroupResult[] = [];
    originalGroupedData: any[] = [];
    groupField: string = 'aliasName';
    ngOnInit() {    
        this.originalGroupedData = this.flattenAndGroupData(this.kpiTypes, this.groupField);
        this.groupedData = groupBy(this.originalGroupedData, [{ field: this.groupField }]);
        this.selectedKpiType = (this.kpiTypes || []).length > 0 ? this.kpiTypes[0].items[0] : null;
        this.subPageId = this.selectedKpiType.subPageId;
        this.toastrService.overlayContainer = this.toastContainer;
        if (this.selectedKpiType != null && isFundFinancialsType(this.selectedKpiType.name)) {
            this.getFundList(true);
        }
        else {
            this.getCompanyList(true);
        }
        
        // Initialize extraction badge visibility
        this.updateExtractionBadgeVisibility();
    }

    getCompanyList(isInitialLoad: boolean = false) {
        this.isLoader = true;
        this.portfolioCompanyService.getPortfolioCompany().subscribe({
            next: (result) => {
                if (result != null && result.length > 0) {
                    this.setCompanyItems(result,isInitialLoad);
                }
                this.isLoader = false;
            }, error: (error) => {
                this.isLoader = false;
            }
        });
    }
    private setCompanyItems(result: any,isInitialLoad: boolean = false) {   
        this.companyList = result;
        this.selectedCompany = result[0];
        this.portfolioCompanyList = result.filter(item => item !== this.selectedCompany);
        this.OrginalCompanyList = JSON.stringify(result);
        this.companyId = this.selectedCompany.encryptedPortfolioCompanyId;
        if(isInitialLoad)
            this.GetSelectedKpiData(this.selectedKpiType);
        this.isEnableView = true;
    }

    getFundList(isInitialLoad: boolean = false) {
        this.isLoader = true;
        this.portfolioCompanyService.getFunds().subscribe({
            next: (result) => {
                if (result != null && result.length > 0) {
                    let fundsList =[];
                    result.forEach((item: any) => {
                            fundsList.push({
                                portfolioCompanyID: item.fundID,
                                companyName: item.fundName,
                                encryptedPortfolioCompanyId: item.encryptedFundId
                            });
                    });
                    this.setCompanyItems(fundsList,isInitialLoad);
                }
                this.isLoader = false;
            }, error: (error) => {
                this.isLoader = false;
            }
        });
    }
        onSelectCompany(company: any) {
        this.deletedMappedkpi = [];
        this.selectedCopyToCompanyList = [];
        this.selectedCompany = company;
        this.companyId = company.encryptedPortfolioCompanyId;
        this.portfolioCompanyList =this.companyList.filter(item => item !== this.selectedCompany);
        this.kpiAllListItem = "";
        this.getAll();
        this.getKPIUnMappingList();
    }
    getAll() {
        if (
          this.kpiType != undefined &&
          this.companyId != undefined &&
          this.companyId != null &&
          this.kpiType != null
        )
          this.getKPIMapping();
        this.PreviousSelectedTreedata = undefined;
        this.isCheckedAll = true;
        this.isDisabledCancelBtn = true;
        this.isCheckedCopyAll=false;
        this.isDisabledSaveBtn = true;
        if (this.reload != null) {
          this.getKPIMapping();
          this.PreviousSelectedTreedata = undefined;
          this.isCheckedAll = true;
          this.isDisabledCancelBtn = true;
          this.isCheckedCopyAll=false;
          this.isDisabledSaveBtn = true;
        }
    }
    async LoadTree() {
        this.database.initialize(this.filteredTreeData);
        this.database.dataChange.subscribe(data => {
            this.dataSource.data = data;
            
            // Initialize oldIsExtraction from isExtraction for all nodes
            this.treeControl.dataNodes.forEach(node => {
                node.oldIsExtraction = node.isExtraction;
            });
        });
    }
    isChecked(event: any) {
        this.CheckAndUnCheckAll();
        this.SetMapAllNodes(event.checked);
        this.isEnableSaveButton();
    }
    IsCheckParent(event, node) {
        if (event.checked) {
            node.isMapped = true;
            this.SetKPIChecked(node, true);
        }
        this.isEnableSaveButton();
        this.ValidateAllChecked();
    }
    OnYes(event) {
        this.deletedMappedkpi = [];
        this.isCancelPopup = false;
        this.isCheckedAll = false;
        this.getKPIMapping();
        this.getKPIUnMappingList();
    }
    OnNo(event) {
        this.isCancelPopup = false;
    }
    OnCancelSavePopup(event) {
        this.isSavePopup = false;
        // Reset extraction changes to original state
        this.resetExtractionChanges();
    }
    SaveMapping() {
        this.GetDataSourceData();
        
        // Include extraction state in mapping data
        const mappingData = this.dataSource.data;
        mappingData.forEach(kpi => {
            // Ensure extraction state is properly set (null/undefined should be false)
            kpi.isExtraction = kpi.isExtraction === true;
            
            // After successful save, update the oldIsExtraction value to match current isExtraction
            kpi.oldIsExtraction = kpi.isExtraction;
            
            // Update for children too
            if (kpi.children?.length > 0) {
                kpi.children.forEach(childKpi => {
                    // Ensure extraction state is properly set (null/undefined should be false)
                    childKpi.isExtraction = childKpi.isExtraction === true;
                    childKpi.oldIsExtraction = childKpi.isExtraction;
                });
            }
        });
        
        this.updateMapping(mappingData);
    }
    mergeDeleteKPI(){
        if(this.deletedMappedkpi.length > 0){
            this.deletedMappedkpi.forEach(element => {
                    this.dataSource.data.push(element);
            });
        }
        this.deletedMappedkpi = [];
    }
    GetDataSourceData() {
        for (const data of this.dataSource.data) {
          if (data.children?.length > 0) {
            for (const child of data.children) {
              if (child.isMapped) {
                data.isMapped = true;
              }
              if (!child.isMapped && child.mappingKPIId > 0) {
                this.isUnMapped = true;
              }
            }
          }
        }
      }
    public filters(array, text) {
        const getChildren = (result, object) => {
            if (object.name.toLowerCase().includes(text.toLowerCase())) {
                result.push(object);
                return result;
            }
            if (Array.isArray(object.children)) {
                const children = object.children.reduce(getChildren, []);
                if (children.length) result.push({ ...object, children });
            }
            return result;
        };
        return array.reduce(getChildren, []);
    }
    public filter(filterText: string): any {
        let filteredTreeData;
        if (filterText) {
            filteredTreeData = this.filters(this.dataSource.data, filterText);
        } else {
            filteredTreeData = this.dataSource.data;
        }
        return filteredTreeData;
    }
    private uniqByKeepFirst(a, key) {
        let seen = new Set();
        return a.filter(item => {
            let k = key(item);
            return seen.has(k) ? false : seen.add(k);
        });
    }
    FilterKPI(filterText: string) {
        
        if (filterText) {
            this.filteredTreeData = this.filterobject(filterText);
            this.SearchSelectedTreeData = [...this.filteredTreeData];
            this.isCheck = "Y";
        }
        else {
            this.filteredTreeData = this.searchDataList;
            this.SearchSelectedTreeData = [];
            this.isCheck = "N";
        }
        if ((this.filteredTreeData.length == 0 || this.filteredTreeData.length > 0) && this.isCheck == "N") {
            this.Emptyischeck = true;       
            this.checkPreviousandCurrentselcted();
            this.LoadTree();
            this.SearchClearSelectionUnselect();
            this.filteredTreeData = this.dataSource.data;
            this.isCheckedAll = false;
        }
        if (this.filteredTreeData.length == 0 && this.isCheck == "Y" && this.SearchSelectedTreeData.length == 0) {
            this.filteredTreeData = this.dataSource.data;
            this.Emptyischeck = false
        }
        if (this.filteredTreeData.length > 0 && this.isCheck == "Y" && this.SearchSelectedTreeData.length > 0) {
            this.Emptyischeck = true;
            this.checkPreviousandCurrentselcted();
            this.exitsSearchClearSelectionUnselect();
        }
        if (this.filteredTreeData.length == 0 && this.isCheck == "Y") {
            this.Emptyischeck = false;
        }
        this.database.initialize(this.filteredTreeData);
        this.database.dataChange.subscribe(data => {
            this.dataSource.data = data;
        });
        this.SetCheckListSelection();
        this.ValidateAllChecked();
        this.SetCheckListSelection();
    }
    checkPreviousandCurrentselcted() {
        let CurrentThreaddata = this.SearchSelectedTreeData;
        this.PreviousSelectedTreedata = [...CurrentThreaddata];
        let curretPreviousTreedata = [];
        if (this.currentselectedthread.length > 0) {
            const filteredThreads = this.currentselectedthread.filter(thread => thread !== undefined);
            curretPreviousTreedata.push(...filteredThreads);
          }
        this.SearchSelectedTreeData = [];
        if (curretPreviousTreedata != null && curretPreviousTreedata != undefined && curretPreviousTreedata.length > 0) {
            this.SearchSelectedTreeData = [...this.currentselectedthread];
        }
        else {
            this.SearchSelectedTreeData = this.PreviousSelectedTreedata;
        }
    }
    toObject(arr) {
        let rv = {};
        for (let i = 0; i < arr.length; ++i)
            if (arr[i] !== undefined) rv[i] = arr[i];
        return rv;
    }
    filterobject(filterText) {
        let matchingArray = [];
        this.dataSource.data.forEach(key => {
            const parent = (key.name.toLocaleLowerCase().indexOf(filterText.toLocaleLowerCase()) > -1);
            let Con = key.children?.filter(b => b.name.toLocaleLowerCase().indexOf(filterText.toLocaleLowerCase()) > -1);
            if (key.children?.length > 0 && Con.length > 0) {
                const ch = key.children?.filter(b => b.name.toLocaleLowerCase().indexOf(filterText.toLocaleLowerCase()) > -1);
                matchingArray.push({ ...key, children: ch })
            }
            if (parent && ( Con?.length == 0 || Con == undefined)) {
                const child = key.children;
                matchingArray.push({ ...key, children: child });
            }
        });
        return matchingArray;
    }
    SearchClearSelectionUnselect() {
        let AlreadySelected: any = [];
        let ChildAlreadySelected: any = [];
        let parentunchecked: boolean = true;
        for (let i = 0; i < this.dataSource.data.length; i++) {
            AlreadySelected = this.SearchSelectedTreeData.filter(F => +F.id === +this.dataSource.data[i].id);
            for (let C = 0; C < AlreadySelected.length; C++) {
                this.dataSource.data[i].isMapped = AlreadySelected[C].isMapped;
                parentunchecked = AlreadySelected[C].isMapped;
                if (AlreadySelected[C].children.length > 0)
                    this.dataSource.data[i].children = AlreadySelected[C].children;
                if (this.dataSource.data[i].children.length == 0) {
                    if (AlreadySelected[C].children.length > 0) {
                        this.dataSource.data[i].children = AlreadySelected[C].children;
                        AlreadySelected[C].children.forEach(element => {
                            this.dataSource.data = this.dataSource.data.filter((X: any) => X.id != element.id);
                            this.dataSource.data.filter(function (ele) { return ele.id != element.id })
                        });
                    }
                }
                if (!parentunchecked) {
                    if (this.dataSource.data[i].children != null && this.dataSource.data[i].children != undefined && this.dataSource.data[i].children.length > 0) {
                        for (let j = 0; j < this.dataSource.data[i].children.length; j++) {
                            for (let j = 0; j < this.dataSource.data[i].children.length; j++) {
                                this.dataSource.data[i].children[j].isMapped = parentunchecked;
                            }
                        }
                    }
                }
                //}
            }
        }
    }
    exitsSearchClearSelectionUnselect() {
        let AlreadySelected: any = [];
        let parentunchecked: boolean = true;
        for (let i = 0; i < this.filteredTreeData.length; i++) {
            AlreadySelected = this.SearchSelectedTreeData.filter(F => +F.id === +this.filteredTreeData[i].id);
            for (let C = 0; C < AlreadySelected.length; C++) {
                this.filteredTreeData[i].isMapped = AlreadySelected[C].isMapped;
                parentunchecked = AlreadySelected[C].isMapped;
                    if (AlreadySelected[C]?.children?.length > 0)
                    {
                        this.filteredTreeData[i].children = AlreadySelected[C].children;
                    }
                    if (this.filteredTreeData[i]?.children?.length == 0) {
                        if (AlreadySelected[C].children.length > 0) {
                            this.filteredTreeData[i].children = AlreadySelected[C].children;
                            AlreadySelected[C].children.forEach(element => {
                                this.filteredTreeData = this.filteredTreeData.filter((X: any) => X.id != element.id);
                                this.filteredTreeData.filter(function (ele) { return ele.id != element.id })
                            });
                        }
                    }
                    if (!parentunchecked) {
                        if (this.filteredTreeData[i]?.children != null && this.filteredTreeData[i].children != undefined && this.filteredTreeData[i].children.length > 0) {
                            for (let j = 0; j < this.filteredTreeData[i].children.length; j++) {
                                for (let j = 0; j < this.filteredTreeData[i].children.length; j++) {
                                    this.filteredTreeData[i].children[j].isMapped = parentunchecked;
                                }
                            }
                        }
                    }
                //}
            }
        }

    }
    findDuplicate(kpiList:any[]) {
        let mappedList = (kpiList || [])?.filter((x) => x.isMapped);
        let kpiNameList = mappedList?.map(function (item) {
          return item.name;
        });
        return kpiNameList.some(function (item, idx) {
          return kpiNameList.indexOf(item) != idx;
        });
    }
    updateMapping(kpiList) {
        if (this.findDuplicate(kpiList)){
            this.isSavePopup = false;
            this.toastrService.error(
                KpiMappingConstants.DuplicateKpiMappedSameCompany,
                "",
                { positionClass: KpiMappingConstants.ToasterMessagePossition }
              );
        }
        else {
          this.mergeDeleteKPI();
           this.isLoader = true;
          const companyIds = [];
          this.selectedCopyToCompanyList.forEach((company) => companyIds.push(company.portfolioCompanyID.toString()));
          let kPIMappingQueryModel = {
            KPIMappings: kpiList,
            CompanyIds: companyIds?.toString()
           };
        
            this.portfolioCompanyService
                .updateKPIMappingList(
                 kPIMappingQueryModel,
                 this.companyId,
                this.kpiType,
                this.moduleID,
                this.subPageId,
                isFundFinancialsType(this.selectedKpiType.name)
                )
                .subscribe({
                next:(result) => {
                    let resp = result;
                    if (resp != null && resp.code == "OK") {
                    this.isSavePopup = false;
                    this.deletedMappedkpi = [];
                    this.PreviousSelectedTreedata = [];
                    this.SearchSelectedTreeData = [];
                    this.kpiList = [];
                    this.listKPIList = [];
                    this.toastrService.success(
                        KpiMappingConstants.MappedKpiSuccessfullywithcompany,
                        "",
                        { positionClass: KpiMappingConstants.ToasterMessagePossition }
                    );
                    this.selectedCopyToCompanyList =[];
                    this.OnCopyCompanyEmit.emit();
                    this.getKPIUnMappingList();
                    this.getAll();
                    } else {
                    this.toastrService.error(KpiMappingConstants.ErrorOccured, "", {
                        positionClass: KpiMappingConstants.ToasterMessagePossition,
                    });
                     this.isSavePopup = false;
                    }
                    this.isMappingContinue = false;
                    this.isUnMapped = false;
                    this.isLoader = false;
                 },
               error: (error) => {
                     this.isLoader = false;
                 }
         });
          
        }
     }

    getKPIUnMappingList() {
        this.isLoader = true;
        this.portfolioCompanyService.getKPIUnMappingList(this.companyId, this.kpiType, this.moduleID, this.subPageId, isFundFinancialsType(this.selectedKpiType.name)).subscribe({
            next: (result) => {
                this.listKPIList = result;
                this.filteredAllKpiList = result;
                this.isLoader = false;
            },
            error: (_error) => {
                this.isLoader = false;
            }
        });
    }
    getKPIMapping() {
        this.currentselectedthread = [];
         this.isLoader = true;    
        this.portfolioCompanyService.getKPIMappingList(this.companyId, this.kpiType, this.moduleID, this.subPageId, isFundFinancialsType(this.selectedKpiType.name)).subscribe((result) => {
            let resp = result;
            if (resp != null && resp.code == "OK") {
                this.Emptyischeck = true;
                this.kpiList = resp.body;
                this.kpiTreeList = this.kpiList;
                this.filteredTreeData = this.kpiTreeList;
                 this.LoadTree();
                this.kpiName = "";
                this.PreviousSelectedTreedata = undefined;
                 this.SetCheckListSelection();
                this.ValidateAllChecked();
                this.treeControl.expandAll();
                this.OriginalKpiList = JSON.parse(JSON.stringify(this.dataSource.data));
                this.SetDisableCopyTo(resp);
                
                // Update extraction status visibility
                 this.updateExtractionBadgeVisibility();
             }
             this.isLoader = false;
            this.disableButtons();
            this.ValidateAllChecked();
            this.treeControl.expandAll();
         });
     }

    private SetDisableCopyTo(resp: any) {
        if (resp?.body?.length == 0) {
            this.disableCopyTo = true;
            this.isDisabledSaveBtn = true;
            this.isDisabledCancelBtn = true;
        } else {
            this.disableCopyTo = false;
            this.isDisabledSaveBtn = false;
            this.isDisabledCancelBtn = false;
        }
    }

    SetKPIChecked(checkednode: any, checked: boolean) {
        let index = this.dataSource.data.findIndex(x => x.id == checkednode.id);

        if (index > -1) {
            this.dataSource.data[index].isMapped = checked;
            if (this.dataSource.data[index].children != null && this.dataSource.data[index].children != undefined && this.dataSource.data[index].children.length > 0) {
                for (let i = 0; i < this.dataSource.data[index].children.length; i++) {
                    this.dataSource.data[index].children[i].isMapped = checked;
                }
            }
        }
        else {
            for (let i = 0; i < this.dataSource.data.length; i++) {
                if (this.dataSource.data[i].children != null && this.dataSource.data[i].children != undefined && this.dataSource.data[i].children.length > 0) {
                    for (let j = 0; j < this.dataSource.data[i].children.length; j++) {
                        if (this.dataSource.data[i].children[j].id === checkednode.id) {
                            this.dataSource.data[i].children[j].isMapped = checked;
                        }
                    }
                }
            }
        }
        let arrayobj: any;
        let finddata: any = this.dataSource.data[index]
        if (finddata != undefined) {
            this.clonekpiTreeList = this.dataSource.data.filter(x => x.id == finddata.id);
            this.clonekpiTreeList.forEach(element => {
                let obj = <KPIItemNode>{}
                obj.children = element.children;
                obj.id = element.id;
                obj.displayOrder = element.displayOrder;
                obj.isMapped = element.isMapped;
                obj.isBoldKPI = element?.isBoldKPI;
                obj.isHeader = element?.isHeader;
                obj.mappingKPIId = element.mappingKPIId;
                obj.name = element.name;
                obj.oldDisplayOrder = element.oldDisplayOrder;
                obj.oldIsMapped = element.oldIsMapped;
                obj.parentKPIID = element.parentKPIID;
                obj.formula =element.formula;
                obj.formulaKPIId =element.formulaKPIId;
                obj.kpiInfo = element.kpiInfo;
                obj.kpiTypeId = element?.kpiTypeId == undefined ? 0 : element.kpiTypeId;
                this.currentselectedthread.push(obj);
            });
        }
        if (finddata == undefined) {
            let query = this.dataSource.data.filter(
                x => x.children?.some(y => y.id == checkednode.id));
            query.forEach(element => {
                let obj = <KPIItemNode>{}
                obj.children = element.children;
                obj.id = element.id;
                obj.displayOrder = element.displayOrder;
                obj.isMapped = element.isMapped;
                obj.isHeader = element.isHeader;
                obj.isBoldKPI = element.isBoldKPI;
                obj.mappingKPIId = element.mappingKPIId;
                obj.name = element.name;
                obj.oldDisplayOrder = element.oldDisplayOrder;
                obj.oldIsMapped = element.oldIsMapped;
                obj.parentKPIID = element.parentKPIID;
                obj.formula = element.formula;
                obj.formulaKPIId = element.formulaKPIId;
                obj.kpiInfo = element.kpiInfo;
                obj.kpiTypeId = element?.kpiTypeId == undefined ? 0 : element.kpiTypeId;
                this.currentselectedthread.push(obj);
            });
        }
    }
    SetCheckListSelection() {
        for (const node of this.treeControl.dataNodes) {
          if (node.isMapped) {
            this.checklistSelection.select(node);
            this.treeControl.expand(node);
          }
        }
      }
    CheckAndUnCheckAll() {
        this.treeControl.dataNodes.forEach((r, index) => {
            this.treeControl.expand(this.treeControl.dataNodes[index]);
            this.isCheckedAll
                ? this.checklistSelection.select(this.treeControl.dataNodes[index])
                : this.checklistSelection.deselect(this.treeControl.dataNodes[index]);
        });
    }
    disableButtons() {
        this.isDisabledCancelBtn = true;
        this.isDisabledSaveBtn = true;
    }
    OnCancelMapAlertPopup() {
        this.isUnMapped = false;
        this.isMapAlertPopup = false;
    }
    IsSaveMapping(event) {
        this.isMapAlertPopup = false;
        this.updateMapping(this.dataSource.data);
    }
    isEnableSaveButton() {
        this.isDisabledCancelBtn = true;
        this.isDisabledSaveBtn = true;
        for (const item of this.dataSource.data) {
          // Check for changes in mapping status
          if (item.isMapped !== item.oldIsMapped) {
            this.isDisabledCancelBtn = false;
            this.isDisabledSaveBtn = false;
          }
          
          // Check for changes in extraction status
          if (item.isExtraction !== item.oldIsExtraction) {
            this.isDisabledCancelBtn = false;
            this.isDisabledSaveBtn = false;
          }
          
          if (item.children?.length > 0) {
            for (const child of item.children) {
              // Check for changes in mapping status
              if (child.isMapped !== child.oldIsMapped) {
                this.isDisabledCancelBtn = false;
                this.isDisabledSaveBtn = false;
              }
              
              // Check for changes in extraction status
              if (child.isExtraction !== child.oldIsExtraction) {
                this.isDisabledCancelBtn = false;
                this.isDisabledSaveBtn = false;
              }
            }
          }
        }
      }
      SetMapAllNodes(isChecked: boolean) {
        this.searchDataList = [];
        for (const item of this.dataSource.data) {
          item.isMapped = isChecked;
      
          if (item.children?.length > 0) {
            for (const child of item.children) {
              child.isMapped = isChecked;
            }
          }
        }
        this.currentselectedthread = isChecked ? [...this.dataSource.data] : [];
        this.isDisabledCancelBtn = isChecked?false: true;
        this.isDisabledSaveBtn = isChecked?false: true;
      
        this.searchDataList = this.database.data;
      }
    ValidateAllChecked() {
        this.isCheckedAll = true;
        for (let i = 0; i < this.dataSource.data.length; i++) {
            if (!this.dataSource.data[i].isMapped) {
                this.isCheckedAll = false;
                break;
            }
            if (this.dataSource.data[i].children != null && this.dataSource.data[i].children != undefined && this.dataSource.data[i].children.length > 0) {
                for (let j = 0; j < this.dataSource.data[i].children.length; j++) {
                    if (!this.dataSource.data[i].children[j].isMapped) {
                        this.isCheckedAll = false;
                        break;
                    }
                }
            }
        }

    }
   
    ngAfterViewInit() {
        this.treeHeight = (window.innerHeight - 300) + KpiMappingConstants.PX;
        this.kpilistheight =  (window.innerHeight - 300) + + KpiMappingConstants.PX;
    }
    onResized(event: any) {
        this.treeHeight = (window.innerHeight - 300) + KpiMappingConstants.PX;
        this.kpilistheight =  (window.innerHeight - 300) + KpiMappingConstants.PX;
    }
    createDuplicate(node:any)
    {
        this.isLoader = true;
       
            let kpiModel = {
                KPIType: this.kpiType == KpiMappingConstants.ProfitLossKPI ? KpiMappingConstants.ProfitAndLossKPI : this.kpiType,
                id: node.id,
                moduleId:this.moduleID,
                subPageId: this.subPageId
            };
            this.portfolioCompanyService.createDuplicateKPI(kpiModel,this.selectedKpiType != null && isFundFinancialsType(this.selectedKpiType.name)).subscribe({
                next:(result) => {
                    this.isLoader = false;
                    let kpiId = result;
                    if (kpiId != null && kpiId > 0) {
                        this.toastrService.success(KpiMappingConstants.DuplicateKPICreatedSuccessfully, "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
                        this.createDuplicateKPIPosition(node, kpiId);
                        this.getMapButtonStatus();
                    }
                    else
                        this.toastrService.error(KpiMappingConstants.ErrorOccurredCreatedDuplicateKPI, "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
                },
                error:_error => {
                    this.toastrService.error(KpiMappingConstants.ErrorOccurredCreatedDuplicateKPI, "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
                    this.isLoader = false;
                }
    });
        
    }
    createDuplicateKPIPosition(node: any, kpiId: number) {
        let duplicateNode = JSON.parse(JSON.stringify(node));
        duplicateNode.id = kpiId;
        duplicateNode.displayOrder = null;
        duplicateNode.expandable = false;
        duplicateNode.isMapped = true;
        duplicateNode.mappingKPIId = 0;
        duplicateNode.oldIsMapped = false;
        duplicateNode.oldDisplayOrder = null;
        duplicateNode.parentKPIID = 0;
        duplicateNode.level = 0;
        duplicateNode.formula = null;
        duplicateNode.formulaKPIId = null;
        duplicateNode.kpiTypeId = duplicateNode.kpiTypeId == undefined ? 0 : duplicateNode.kpiTypeId;
        duplicateNode.children = [];
        this.database.insertItemAbove(node, duplicateNode);
        this.database.deleteItem(duplicateNode);
        let from = this.flatNodeMap.get(node);
        let dropItemNode: KPIItemNode = duplicateNode;
        if (node.level == 0)
            this.database.copyPasteItemBelow(dropItemNode, from);
        else if (node.level == 1) {
            let dropNode = this.getParent(node);
            this.database.copyPasteItemBelow(duplicateNode, this.flatNodeMap.get(dropNode));
        }
        else {
            let dropNodes = this.getAncestors(this.treeControl.dataNodes, node.id);
            if (dropNodes?.length > 0)
                this.database.copyPasteItemBelow(duplicateNode, this.flatNodeMap.get(dropNodes[0]));
        }
        this.treeControl.expandAll();
    }
    getParent(node: any) {
        const { treeControl } = this;
        const currentLevel = treeControl.getLevel(node);
        if (currentLevel < 1) {
            return null;
        }
        const startIndex = treeControl.dataNodes.indexOf(node) - 1;

        for (let i = startIndex; i >= 0; i--) {
            const currentNode = treeControl.dataNodes[i];

            if (treeControl.getLevel(currentNode) < currentLevel) {
                return currentNode;
            }
        }
    }
    getAncestors(array, id) {
        if (!array) {
          return null;
        }
        for (const item of array) {
          if (item.id === id) {
            return [item];
          }
          const ancestors = this.getAncestors(item.children, id);
          if (ancestors) {
            return [item, ...ancestors];
          }
        }
        return null;
      }
    openFormulaPopup(node: any) {
        let kpiModel = {
            kpi: node.name,
            kpiId: node.id,
            mappingKpiId: node.mappingKPIId,
            formula: node.formula,
            formulaKPIId: node.formulaKPIId,
        };
        this.selectedFormulaKPI = kpiModel;
        this.isOpenPopUp = true;
    }
    partialCloseFormulaPopUp(data: any) {
        this.listKPIList.forEach(element => {
            if (data?.KpiId == element?.id) {
                element.formula = data?.Formula;
            }
            else {
                if (element?.children?.length > 0) {
                    element?.children?.forEach(element => {
                        if (data?.KpiId == element?.id) {
                            element.formula = data?.Formula;
                        }
                    });
                }
            }
           });
    }
    closeFormulaPopUp(data: any) {
        this.isOpenPopUp = false;
        if (data != null && data != undefined) {
            if(data.MappingId > 0){
               this.getKPIMapping();
            }else{
               this.partialCloseFormulaPopUp(data); 
            }
            this.kpiTreeList = this.kpiList;
            this.filteredTreeData = this.kpiTreeList;
            this.LoadTree();
            this.kpiName = "";
            this.PreviousSelectedTreedata = undefined;
            this.SetCheckListSelection();
            this.ValidateAllChecked();
            this.treeControl.expandAll();
            this.OriginalKpiList = JSON.parse(JSON.stringify(this.dataSource.data));
        }
    }    
    GetSelectedKpiData(kpiType) {
        this.deletedMappedkpi=[];
        this.kpiType =kpiType.name;
        this.selectedKpiType = kpiType;
        this.moduleID =kpiType?.moduleID;
        this.subPageId = kpiType?.subPageId;  
        
        // Use Promises to ensure the proper execution order
        const processData = async () => {
            // First get the fund list or company list
            if(this.selectedKpiType != null && isFundFinancialsType(this.selectedKpiType.name)) {
                await new Promise<void>((resolve) => {
                    this.isLoader = true;
                    this.portfolioCompanyService.getFunds().subscribe({
                        next: (result) => {
                            if (result != null && result.length > 0) {
                                let fundsList = [];
                                result.forEach((item: any) => {
                                    fundsList.push({
                                        portfolioCompanyID: item.fundID,
                                        companyName: item.fundName,
                                        encryptedPortfolioCompanyId: item.encryptedFundId
                                    });
                                });
                                this.setCompanyItems(fundsList);
                            }
                            this.isLoader = false;
                            resolve();
                        },
                        error: () => {
                            this.isLoader = false;
                            resolve();
                        }
                    });
                });
            }
            else {
                await new Promise<void>((resolve) => {
                    this.isLoader = true;
                    this.portfolioCompanyService.getPortfolioCompany().subscribe({
                        next: (result) => {
                            if (result != null && result.length > 0) {
                                this.setCompanyItems(result);
                            }
                            this.isLoader = false;
                            resolve();
                        }, 
                        error: () => {
                            this.isLoader = false;
                            resolve();
                        }
                    });
                });
            }
            // Await checkIsKPITypeMarkedForExtraction
            await new Promise<void>((resolve) => {
                this.portfolioCompanyService.checkIsKPITypeMarkedForExtraction(kpiType?.subPageId, kpiType?.pageConfigAliasName).subscribe({
                    next: (result) => {
                        this.isKpiTypeMarkedForExtraction = result;
                        resolve();
                    },
                    error: () => {
                        resolve();
                    }
                });
            });
            // Then execute getAll and getKPIUnMappingList
            this.getAll();
            this.getKPIUnMappingList();
        };
        
        // Execute the async function
        processData();
        
        this.kpiAllListItem = "";
    }
    getSelectAllListofkpi(){
        this.isLoader = true;
        this.kpilistheight = 0 + KpiMappingConstants.PX;
        this.isDisabledSaveBtn = false;
        this.isDisabledCancelBtn = false;
        setTimeout(() => {
        this.listKPIList.forEach(element => {
            element.isMapped = true;
            this.kpiList.push(element);
        });
        this.kpiTreeList = this.kpiList;
        this.filteredTreeData = this.kpiTreeList;
        this.listKPIList = [];
        this.filteredAllKpiList = [];
        this.LoadTree();
        this.kpiName = "";
        this.PreviousSelectedTreedata = undefined;
        this.SetCheckListSelection();
        this.ValidateAllChecked();
        this.treeControl.expandAll();
        this.isLoader = false;
        }, 100);
    }

    showUnmappedKpiWarningModel(data:any){
        this.currentNode = data;
        this.showWarningModel = true;
    }

    closeUnmappedKpiWarningModel(){
        this.showWarningModel = false;
    }

    async addToMappedKPI(data:any,index:any,action:any){
        this.showWarningModel = false;
        this.isLoader = true;
        this.kpilistheight = (window.innerHeight - 300) + KpiMappingConstants.PX;
        this.kpiAllListItem = "";
        this.kpiName = "";
        let dataStringify = JSON.parse(JSON.stringify(data));
       if(action == KpiMappingConstants.Add){
        data.isMapped = true;
        
        // Set both isExtraction and oldIsExtraction to the same value
        // This ensures that newly added KPIs are not automatically
        // marked as having changed extraction state
        data.isExtraction = false;
        data.oldIsExtraction = false;
        
        let currentIndex = this.listKPIList.findIndex(std=> std.id === data.id);
        this.listKPIList.splice(currentIndex,1);
        this.filteredAllKpiList = this.listKPIList;
        this.database.insertNewItem(data); 
        this.kpiList = this.dataSource.data;
        this.kpiTreeList = this.kpiList;
       }else{
        // When removing a node, preserve its extraction state
        const extractionState = data.isExtraction;
        if(data?.children && data?.children?.length>0){
            data?.children.forEach(element => {
                element.children = null;
                element.mappingKPIId = 0;
                // Preserve child extraction state and oldIsExtraction
                const childExtractionState = element.isExtraction;
                this.database.insertItemBelow(this.flatNodeMap.get(data),element);
                element.isExtraction = childExtractionState;
                element.oldIsExtraction = childExtractionState;
            });
        }
        this.database.deleteItem(this.flatNodeMap.get(data));
        if(data?.mappingKPIId > 0 && data?.mappingKPIId != null){
            data.isMapped = false;
            data.children = null;
            if(this.deletedMappedkpi.length > 0){
                let checkDuplicateKPI = this.deletedMappedkpi.filter(kpi => kpi.id == data.id);
                if(checkDuplicateKPI.length == 0) {
                    this.deletedMappedkpi[this.deletedMappedkpi.length] = data;
                }
            }else{
                this.deletedMappedkpi[this.deletedMappedkpi.length] = data;
            }
            
        }
        this.kpiList = this.dataSource.data;
        this.kpiTreeList = this.kpiList;
        dataStringify.isMapped = false;
        if(dataStringify?.children && dataStringify?.children?.length>0){
            dataStringify?.children.forEach(element => {
                element.isMapped = false;
                element.children = null;
                if(this.deletedMappedkpi.length > 0){
                    let checkdeletekpi = this.deletedMappedkpi.filter(kpi => kpi.id == element.id && kpi.mappingKPIId == element.mappingKPIId);
                    if(checkdeletekpi.length == 0) {
                        this.deletedMappedkpi[this.deletedMappedkpi.length] = element;
                    }
                }else{
                    this.deletedMappedkpi[this.deletedMappedkpi.length] = element;
                }  
            });
        }
        dataStringify.children = null;
        dataStringify.mappingKPIId = 0;
        this.listKPIList.push(JSON.parse(JSON.stringify(dataStringify)));
        this.filteredAllKpiList = this.listKPIList;
       
        this.kpiList = this.dataSource.data;
        this.kpiTreeList = this.kpiList;
        if(this.kpiList?.length == 0){
            this.kpilistheight = 0 + KpiMappingConstants.PX;
        }
       }
        setTimeout(() => {
            this.isLoader = false;
            this.getMapButtonStatus();
        }, 200);
    }
    deleteNodeFromkpiList(nodes: any, nodeToDelete: any) {
        let val = nodeToDelete.id;
        let index = nodes.findIndex(function(item, i){
          return item.id === val
        });
        if (index > -1) {
            nodes.splice(index, 1);
        } else {
            nodes.forEach(node => {
                if (node.children && node.children.length > 0) {
                    this.deleteNodeFromkpiList(node.children, nodeToDelete);
                }
            });
        }
    }
    onSelectAllListkpi(){
        if(this.allListHidden){
            this.allListHidden = false;
        }else{
            this.allListHidden = true;
        }
    }
    toggleListDisplay(sender: number) {
        if (sender === 1) {
          this.allListHidden = false;
        } else {
          setTimeout(() => {
            this.allListHidden = true;
            if (!this.listKPIList.includes(this.kpiAllListItem)) {
              this.filteredAllKpiList = this.listKPIList;
            }
          }, 500);
        }
      }
      onKeyPress(event) {
        if (!this.allListHidden) {
          if (event.key === KpiMappingConstants.Escape ) {
            this.toggleListDisplay(1);
          }
          if (event.key === KpiMappingConstants.Enter) {
    
            this.toggleListDisplay(1);
          }
        } 
      }
      getCopytoAllCompanies(){
        // copy to all function will do
      }
      getFilteredAllKpiList() {  
 
        this.allListHidden = false;
        if (!this.allListHidden && this.kpiAllListItem !== undefined) { 
            this.filteredAllKpiList = this.listKPIList.filter((item) => item.name.toLowerCase().includes(this.kpiAllListItem.toLowerCase()));
        }
        return this.filteredAllKpiList;
      }
      onCopytoCompanyFunction(event:any){
        this.getMapButtonStatus();
        this.isCheckedCopyAll = this.selectedCopyToCompanyList.length === this.portfolioCompanyList.length;
      }
      getMapButtonStatus(){
        let dataSourseData =  JSON.parse(JSON.stringify(this.dataSource.data));
        let areEqual = this.compareArrays(dataSourseData, this.OriginalKpiList);
        if(this.selectedCopyToCompanyList.length >0 || !areEqual){
            this.isDisabledSaveBtn = false;
            this.isDisabledCancelBtn = false;
        }else{
            this.isDisabledSaveBtn = true;
            this.isDisabledCancelBtn = true;
        }
      }
      compareArrays(array1, array2) {
        if (array1.length !== array2.length) {
          return false;
        }
        for (let i = 0; i < array1.length; i++) {
          if (JSON.stringify(array1[i]) !== JSON.stringify(array2[i])) {
            return false;
          }
        }
        return true;
      }
      handleFilter(value: string): void {
        const filteredData = this.filterData(this.originalGroupedData, value, 'pageConfigAliasName');
        this.groupedData = groupBy(filteredData, [{ field: this.groupField }]);
      }
    
      flattenAndGroupData(data: any[], groupField: string): any[] {
        return data.reduce((acc, group) => {
          group.items.forEach(item => acc.push({ ...item, [groupField]: group[groupField] }));
          return acc;
        }, []);
      }
    
      filterData(data: any[], filterValue: string, textField: string): any[] {
        return data.filter(
          item => item[textField].toLowerCase().indexOf(filterValue.toLowerCase()) !== -1
        );
      }

    toggleAllExtraction(event: any) {
        const isChecked = event.checked;
        this.selectAllExtraction = isChecked;
        let extractionStateChanged = false;
        
        // Apply to all tree nodes
        this.treeControl.dataNodes.forEach(node => {
            // Only update if there's an actual change to avoid unnecessary updates
            if (node.isExtraction !== isChecked) {
                extractionStateChanged = true;
                node.isExtraction = isChecked;
                
                // Update the corresponding nested node
                const nestedNode = this.flatNodeMap.get(node);
                if (nestedNode) {
                    nestedNode.isExtraction = isChecked;
                }
            }
        });
        
        // Update data source to reflect changes
        this.updateDataSourceWithExtractionChanges();
        
        // Update badge visibility
        this.updateExtractionBadgeVisibility();
        
        // Force enable save button if extraction state changed
        if (extractionStateChanged) {
            this.isDisabledCancelBtn = false;
            this.isDisabledSaveBtn = false;
        } else {
            // Regular check if no changes detected
            this.isEnableSaveButton();
        }
    }
    
    // Handle individual extraction checkbox changes
    onExtractionChange(node: KPIItemFlatNode, isParent: boolean = false) {
        let extractionStateChanged = false;
        
        // Check if there's an actual change compared to the original state
        if (node.isExtraction !== node.oldIsExtraction) {
            extractionStateChanged = true;
        }
        
        // Update the corresponding nested node
        const nestedNode = this.flatNodeMap.get(node);
        if (nestedNode) {
            nestedNode.isExtraction = node.isExtraction;
        }
        
        // Update data source to reflect changes
        this.updateDataSourceWithExtractionChanges();
        
        // Don't propagate to children - each node's extraction state is independent
        
        // Check if all nodes have the same extraction state to update the "Select All" checkbox
        this.updateSelectAllExtractionState();
        
        // Update badge visibility
        this.updateExtractionBadgeVisibility();
        
        // Force enable save button if extraction state changed
        if (extractionStateChanged) {
            this.isDisabledCancelBtn = false;
            this.isDisabledSaveBtn = false;
        } else {
            // Regular check if no changes detected
            this.isEnableSaveButton();
        }
    }
    
    // Set extraction state for all descendants of a node
    setDescendantsExtractionState(node: KPIItemFlatNode, isExtraction: boolean) {
        let extractionStateChanged = false;
        
        const descendants = this.treeControl.getDescendants(node);
        descendants.forEach(child => {
            // Only update if there's an actual change
            if (child.isExtraction !== isExtraction) {
                extractionStateChanged = true;
                child.isExtraction = isExtraction;
                
                // Update the corresponding nested node
                const nestedNode = this.flatNodeMap.get(child);
                if (nestedNode) {
                    nestedNode.isExtraction = isExtraction;
                }
            }
        });
        
        // Update data source to reflect changes
        this.updateDataSourceWithExtractionChanges();
        
        // Force enable save button if extraction state changed
        if (extractionStateChanged) {
            this.isDisabledCancelBtn = false;
            this.isDisabledSaveBtn = false;
        } else {
            // Regular check if no changes detected
            this.isEnableSaveButton();
        }
    }
    
    // Update the state of the "Select All" checkbox based on individual node states
    updateSelectAllExtractionState() {
        const nodes = this.treeControl.dataNodes;
        if (nodes.length === 0) {
            this.selectAllExtraction = false;
            return;
        }
        
        const allChecked = nodes.every(node => node.isExtraction);
        const anyChecked = nodes.some(node => node.isExtraction);
        
        this.selectAllExtraction = allChecked;
    }
    
    // Update the extraction badge visibility
    updateExtractionBadgeVisibility() {
        const nodes = this.treeControl.dataNodes;
        this.anyExtractionSelected = nodes.some(node => node.isExtraction);
    }
    
    // Get the count of extraction-enabled KPIs
    getExtractionCount(): number {
        return this.treeControl.dataNodes.filter(node => node.isExtraction).length;
    }

    // Get count of selected KPIs for extraction
    getExtractionSelectedCount(): number {
        return this.extractionSelection.selected.length;
    }
    
    // Check if all nodes are selected for extraction
    isAllExtractionSelected(): boolean {
        const numSelected = this.extractionSelection.selected.length;
        const numTotal = this.treeControl.dataNodes.length;
        return numSelected === numTotal;
    }
    
    // Toggle all extraction selections
    toggleAllExtractionSelection(): void {
        // No need to toggle here as [(ngModel)] already updated the value
        // this.selectAllExtraction = !this.selectAllExtraction;
        
        if (this.selectAllExtraction) {
            this.treeControl.dataNodes.forEach(node => {
                this.extractionSelection.select(node);
            });
        } else {
            this.extractionSelection.clear();
        }
    }
    
    // Mark selected KPIs for extraction
    markSelectedForExtraction(): void {
        let extractionStateChanged = false;
        
        // Apply extraction to all selected KPIs
        this.extractionSelection.selected.forEach(node => {
            // Only update if there's an actual change
            if (node.isExtraction !== true) {
                extractionStateChanged = true;
                node.isExtraction = true;
                
                // Update the corresponding nested node
                const nestedNode = this.flatNodeMap.get(node);
                if (nestedNode) {
                    nestedNode.isExtraction = true;
                }
            }
        });
        
        // Update data source to reflect changes
        this.updateDataSourceWithExtractionChanges();
        
        // Clear selection after marking
        this.extractionSelection.clear();
        
        // Update badge visibility
        this.updateExtractionBadgeVisibility();
        
        // Force enable save button if extraction state changed
        if (extractionStateChanged) {
            this.isDisabledCancelBtn = false;
            this.isDisabledSaveBtn = false;
        } else {
            // Regular check if no changes detected
            this.isEnableSaveButton();
        }
        
        // Show confirmation message
        this.toastrService.success(
            `KPIs marked for extraction successfully`,
            '',
            { 
                positionClass: KpiMappingConstants.ToasterMessagePossition
            }
        );
    }
    
    // Clear extraction selection
    clearExtractionSelection(): void {
        this.extractionSelection.clear();
        this.selectAllExtraction = false;
    }
    
    // Remove extraction from a KPI
    unlinkExtraction(node: KPIItemFlatNode): void {
        let extractionStateChanged = false;
        
        // Check if there's an actual change
        if (node.isExtraction !== false) {
            extractionStateChanged = true;
            node.isExtraction = false;
            
            // Update the corresponding nested node
            const nestedNode = this.flatNodeMap.get(node);
            if (nestedNode) {
                nestedNode.isExtraction = false;
            }
        }
        
        // Update data source to reflect changes
        this.updateDataSourceWithExtractionChanges();
        
        // Update extraction badge
        this.updateExtractionBadgeVisibility();
        
        // Force enable save button if extraction state changed
        if (extractionStateChanged) {
            this.isDisabledCancelBtn = false;
            this.isDisabledSaveBtn = false;
        } else {
            // Regular check if no changes detected
            this.isEnableSaveButton();
        }
        
        // Show confirmation
        this.toastrService.success(
            'Selected KPI is removed from Specific KPIs Extraction',
            '',
            { 
                positionClass: KpiMappingConstants.ToasterMessagePossition
            }
        );
    }

    // Check if selected items are already marked for extraction
    areSelectedItemsMarkedForExtraction(): boolean {
        if (this.extractionSelection.selected.length === 0) return false;
        return this.extractionSelection.selected.every(node => node.isExtraction);
    }

    // Check if some selected items are marked for extraction
    areSomeSelectedItemsMarkedForExtraction(): boolean {
        if (this.extractionSelection.selected.length === 0) return false;
        return this.extractionSelection.selected.some(node => node.isExtraction);
    }

    // Check if selected items have mixed extraction states
    hasMixedExtractionStates(): boolean {
        if (this.extractionSelection.selected.length <= 1) return false;
        
        const firstState = this.extractionSelection.selected[0].isExtraction;
        return this.extractionSelection.selected.some(node => node.isExtraction !== firstState);
    }
    
    // Mark or unmark selected KPIs for extraction based on current state
    markOrUnmarkSelectedForExtraction(): void {
        // Check for mixed state
        if (this.hasMixedExtractionStates()) {
            // Do nothing for mixed state
            return;
        }
        
        const shouldUnmark = this.areSelectedItemsMarkedForExtraction();
        let extractionStateChanged = false;
        
        // Apply extraction change to all selected KPIs
        this.extractionSelection.selected.forEach(node => {
            // Only update if there's an actual change to avoid unnecessary updates
            if (node.isExtraction !== !shouldUnmark) {
                extractionStateChanged = true;
                node.isExtraction = !shouldUnmark;
                
                // Update the corresponding nested node
                const nestedNode = this.flatNodeMap.get(node);
                if (nestedNode) {
                    nestedNode.isExtraction = !shouldUnmark;
                }
            }
        });
        
        // Update data source to reflect changes
        this.updateDataSourceWithExtractionChanges();
        
        // Clear selection after marking
        this.extractionSelection.clear();
        
        // Update badge visibility
        this.updateExtractionBadgeVisibility();
        
        // Force enable save button if extraction state changed
        if (extractionStateChanged) {
            this.isDisabledCancelBtn = false;
            this.isDisabledSaveBtn = false;
        } else {
            // Regular check if no changes detected
            this.isEnableSaveButton();
        }
        
        // Show confirmation message
        if (shouldUnmark) {
            this.toastrService.success("Selected KPIs are removed from Specific KPIs Extraction", "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
        } else {
            this.toastrService.success("Selected KPIs are added to the Specific KPIs Extraction", "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
        }
        this.selectAllExtraction = false;
    }
    
    // Method to reset all extraction changes and revert to original state
    resetExtractionChanges(): void {
        this.treeControl.dataNodes.forEach(node => {
            node.isExtraction = node.oldIsExtraction;
        });
        
        // Update data source to reflect changes
        this.updateDataSourceWithExtractionChanges();
        
        // Update badge visibility
        this.updateExtractionBadgeVisibility();
        
        // Update save button state
        this.isEnableSaveButton();
    }

    // Update the data source to reflect changes in extraction states
    updateDataSourceWithExtractionChanges(): void {
        // First ensure all flat nodes have their extraction states synced to nested nodes
        this.treeControl.dataNodes.forEach(flatNode => {
            const nestedNode = this.flatNodeMap.get(flatNode);
            if (nestedNode) {
                // Ensure nested node has the same extraction state as the flat node
                nestedNode.isExtraction = flatNode.isExtraction;
            }
        });
        
        // Create a copy of the current data to trigger change detection
        const currentData = [...this.dataSource.data];
        
        // Update the data source
        this.database.dataChange.next(currentData);
    }
      removeTagItem(item: any): void {
        const index = this.kpisynonym.indexOf(item);
    
        if (index >= 0) {
          this.kpisynonym.splice(index, 1);
        }
        this.synonym = this.kpisynonym.toString();
        this.setdisablePrimaryBtn();
      }
      setdisablePrimaryBtn(){
        if(this.synonym == this.previousSynonym){
            this.disablePrimaryBtn = true;
        }else{
            this.disablePrimaryBtn = false;
        }
      }
      addTagItem(event: MatChipInputEvent): void {
        const input = event.input;
        const value = event.value;
        if ((value || '').trim()) {
          this.kpisynonym.push(value.trim());
        }
        if (input) {
          input.value = '';
        }
        this.synonym = this.kpisynonym.toString();
        this.setdisablePrimaryBtn();
      }
      OnClickAddSynonym(kpiName: string,kpiMappingId: number){
        if(kpiMappingId == null || kpiMappingId == undefined || kpiMappingId == 0){
            return;
        }
        this.selectedKpiMappingId = kpiMappingId;
        this.selectedKpiName = kpiName;
        this.synonym = "";
        this.kpisynonym = [];
        this.previousSynonym = "";
        this.popUpTitle = KpiMappingConstants.AddSynonym;
        this.primaryButtonName = KpiMappingConstants.Add;
        this.showUpsertSynonymPopUp = true;
    }
    OnClickEditSynonym(kpiName: string,kpiMappingId: number,synonym: string){
        if(kpiMappingId == null || kpiMappingId == undefined || kpiMappingId == 0){
            return;
        }
        this.selectedKpiMappingId = kpiMappingId;
        this.selectedKpiName = kpiName;
        this.synonym = synonym;
        this.previousSynonym = synonym;
        this.kpisynonym = synonym?.split(',');
        this.popUpTitle = KpiMappingConstants.EditSynonym;
        this.primaryButtonName = KpiMappingConstants.Update;
        this.showUpsertSynonymPopUp = true;
    }
    OnCloseUpsertSynonymPopUp(){
        this.synonym = "";
        this.kpisynonym = [];
        this.showUpsertSynonymPopUp = false;
    }
    onResetSynonym(){
        this.synonym = this.previousSynonym;
        this.kpisynonym = this.previousSynonym?.split(',');
        this.setdisablePrimaryBtn();
    }
    upsertSynonyms(){
        this.portfolioCompanyService
        .upsertSynonyms({moduleId: this.moduleID,mappingId: this.selectedKpiMappingId,synonym: this.synonym,isFundKpi: isFundFinancialsType(this.selectedKpiType.name)})
        .subscribe({next:(result) => {
            if(result.value == 1){
                this.getKPIMapping();
                if(this.primaryButtonName == KpiMappingConstants.Add){
                    this.toastrService.success(KpiMappingConstants.SynonymAddedSuccessfully,"",{ positionClass: KpiMappingConstants.ToasterMessagePossition });
                }else{
                    this.toastrService.success(KpiMappingConstants.SynonymUpdatedSuccessfully,"",{ positionClass: KpiMappingConstants.ToasterMessagePossition });
                }
            }else{
                this.toastrService.error("Failed to add/update synonym", "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
            }
             this.OnCloseUpsertSynonymPopUp();
         },
       error: (error) => {
            this.toastrService.error("Failed to add/update synonym", "", { positionClass: KpiMappingConstants.ToasterMessagePossition });
            this.OnCloseUpsertSynonymPopUp();
         }
         });
     }
}
