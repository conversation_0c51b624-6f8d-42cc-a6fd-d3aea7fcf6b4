@import "../../../../variables.scss";

.page-config-container{
    .page-config-header{        

        .header-title{
            margin-bottom: $space-4;
        }

        .header-info{
            margin-bottom: $space-4;
            color: $gray-color-66;
        }
    } 
    
    .config-content-wrapper{
        border: $border-color-dark;
        border-radius: $space-4;
        margin-top: $space-12;        

        .config-bar{
            border-bottom: $border-color-dark;
            display: flex;
            justify-content: space-between;

            .config-options{
                margin-top: $space-12;
                margin-bottom: $space-12;
                margin-left: $space-20;

                .dropdown-info{
                    background-color: $light-blue-color;
                    color: $primary-color-78;                                 
                    border-radius: $space-4 0px 0px $space-4;  
                    width: 120px !important;                  
                }

                .dropdown-options{
                    border: $border-color-dark;
                    color: $dark-gray-color;   
                    border-radius: 0px $space-4 $space-4 0px;          
                    width: $dropdown-width;   
                    text-align: left;  
                    position: relative                                     
                }

                .dropdown-company-options{
                    color: $dark-gray-color;
                    border: $border-color-dark;
                    border-radius: $space-4;  
                    width: $dropdown-width;                     
                    text-align: left;  
                    position: relative;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }     
                
                .arrow-icon{
                    height: $space-12;
                    width: $space-12;
                    margin-left: $space-8;
                }    
                
                .down-arrow{
                    position: absolute;
                    right: 0;
                }
            }

            .config-actions{
                margin-top: $space-12;
                margin-bottom: $space-12;
                margin-right: $space-20;

                button{
                    border-radius: $space-4;
                }

                .btn-reset{
                    border: 1px solid $primary-color-78 !important;
                    background-color: $white-color;
                    color: $primary-color-78;
                    margin-right: $space-20;
                }

                .btn-save{
                    background-color: $primary-color-78;      
                    color: $white-color;              
                }

                .btn-save:disabled{
                    background-color: $pastel-blue-color;
                }
            }
        }

        .content{
            .nodata-container {
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                background: #fff;
                margin: $space-20 0;
                text-align: center;
              
                img {
                  display: block;
                  max-width: 100%;
                  height: auto;
                }
            }
        }
    }        
}
.panel-bar-container{
    overflow-x: hidden;
}
.k-dropdown-width-240{
    width: 240px;
}
.form-group {
    display: flex;
    flex-wrap: wrap;
    
  }
  
  .form-item {
    display: flex;
    align-items: center;
    color: $primary-color-78; 
    padding-right: 20px;
  }
  #page-config-filter{
    .form-group {
        margin: 0 !important;
    }
  }

  @media (min-width: 768px)  and (max-width: 768px) { 
    .form-group {
    gap: 0 !important;
    }
    .form-item {
        padding-right: 0 !important;
      }
}
#page-config-filter .k-rounded-md {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
}