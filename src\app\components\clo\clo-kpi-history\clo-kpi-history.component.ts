import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonSubFeaturePermissionService } from 'src/app/services/subPermission.service';
import { FeatureTableMapping, PermissionActions } from 'src/app/common/constants';
import { FeaturesEnum } from 'src/app/services/permission.service';
import { ToastrService } from 'ngx-toastr';
import { CloService } from '../../../services/clo.service';
import { PanelbarItemService } from '../shared/panelbar-item/panelbar-item.service';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';
@Component({
  selector: 'app-clo-kpi-history',
  templateUrl: './clo-kpi-history.component.html',
  styleUrls: ['./clo-kpi-history.component.scss']
})
export class CloKpiHistoryComponent implements OnInit {
  cloName: string;
  cloId: string;
  companyId: string;
  isusdomicile: boolean = true;
  pageId:number=2;
  kpiTab:any=[];
  clO_ID:number=0;
  permissions:any=[];
  kPISummary:number=FeatureTableMapping.TABLES_NAME.KPI_Summary[0];
  CAN_IMPORT=PermissionActions.CAN_IMPORT;
  CAN_EXPORT = PermissionActions.CAN_EXPORT;
  CAN_EDIT= PermissionActions.CAN_EDIT;

  constructor(private readonly route: ActivatedRoute, private readonly router: Router,private readonly subPermissionService: CommonSubFeaturePermissionService,    private readonly toastrService: ToastrService,    private readonly cloService: CloService,
    private pagePanelService: PanelbarItemService,
    private breadcrumbService: BreadcrumbService

  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.cloName = params['kpiname'];
      this.cloId = params['cloid'];
      this.companyId = params['companyid'];
      this.isusdomicile = (params['dmcl'] === 'US')      
    });
    if (this.cloId) {
      this.cloService.getCloById(this.cloId).subscribe({
        next: (cloDetails) => {
          this.companyId = cloDetails.companyID;
          this.clO_ID=cloDetails.clO_ID;
          this.updateBreadcrumbs(this.cloName, cloDetails.issuer);
          if (this.companyId) {
            this.getSubFeatureAccessPermissions();
          }
        },
      });
    }  }

    updateBreadcrumbs(cloname:string,issuer:string) {
      let newBreadcrumbs: any[] = [];
      newBreadcrumbs.push( { label: 'Collateral Loan Obligation(CLO)', url: '/clo-list' });
    newBreadcrumbs.push( { label: issuer, url: '/view-clo-summary/'+this.cloId });
    newBreadcrumbs.push( { label: cloname });
    this.breadcrumbService.setBreadcrumbs(newBreadcrumbs);
    }
  
  redirectToCloPage() {
    this.router.navigate(['/clo-list']);
  }

  redirecttoCloSummary(){
    this.router.navigate(['/view-clo-summary',this.cloId]);
  }
  canViewKPIHistory: boolean = false;
  canViewSummary: boolean = false;
  
    getSubFeatureAccessPermissions() {
     
      this.subPermissionService.getCommonSubFeatureAccessPermissions(this.companyId, FeaturesEnum.CLOPage).subscribe({
          next: (result) => {
            if (result.length > 0) {
              this.permissions=result;
              this.getConfiguration()
              this.canViewKPIHistory = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.KPIHistory.FeatureId), PermissionActions.CAN_VIEW);
              this.canViewSummary = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.Summary.FeatureId), PermissionActions.CAN_VIEW);
             }
          },
          
      });
  } 
  
      checkPermissionAccess(permission:any[], permissionType): boolean {
        return permission.map(x => x[permissionType]).includes(true);
      }
      getConfiguration() {
        this.cloService.getTabList(this.pageId, +(this.companyId), this.clO_ID).subscribe(data=>{
          const tempTabs = data;
      
              const index = data.findIndex(tab => tab.tabId == FeatureTableMapping.TABS_NAME.PE_Performance_Indicators);
              if (index >= 0) {
                let kpiTab = data[index].subTabList.filter(subTab=>subTab.tabId == FeatureTableMapping.TABS_NAME.Key_KPI_History);
                
                kpiTab.forEach(page => {
                  this.pagePanelService.updateTableVisibility(page.tableList,this.permissions);
                });
                this.kpiTab=kpiTab[0];
                let usTable = this.kpiTab.tableList?.find(x=>x.tableId==FeatureTableMapping.EDIT_DOMICILE_TABLES_NAME.KPI_History);
                let euTable = this.kpiTab.tableList?.find(x=>x.tableId==FeatureTableMapping.TOBE_CHANGE_DOMICILE_TABLES_NAME.KPI_History);
                usTable.tableName=this.isusdomicile?usTable.tableName:euTable.tableName;
              }
        })
          
      }

      checkTablePermissions(tableId:number,permissionType:string=null):boolean{
        return this.pagePanelService.checkTablePermissions(tableId,this.permissions,permissionType);
       } 
     
}
