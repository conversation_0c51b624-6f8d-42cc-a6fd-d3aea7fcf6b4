import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmLeaveComponent } from './confirm-leave.component';

describe('ConfirmLeaveComponent', () => {
  let component: ConfirmLeaveComponent;
  let fixture: ComponentFixture<ConfirmLeaveComponent>;
  let mockActiveModal;

  beforeEach(() => {
    mockActiveModal = jasmine.createSpyObj(['close']);

    TestBed.configureTestingModule({
      declarations: [ConfirmLeaveComponent],
      providers: [
        { provide: NgbActiveModal, useValue: mockActiveModal }
      ]
    });

    fixture = TestBed.createComponent(ConfirmLeaveComponent);
    component = fixture.componentInstance;
  });

  it('should emit true and close the modal when CloseModal is called with "Yes"', () => {
    spyOn(component.onSave, 'emit');

    component.CloseModal('Yes');

    expect(component.onSave.emit).toHaveBeenCalledWith(true);
    expect(mockActiveModal.close).toHaveBeenCalled();
  });

  it('should emit false and close the modal when CloseModal is called with any value other than "Yes"', () => {
    spyOn(component.onSave, 'emit');

    component.CloseModal('No');

    expect(component.onSave.emit).toHaveBeenCalledWith(false);
    expect(mockActiveModal.close).toHaveBeenCalled();
  });
});