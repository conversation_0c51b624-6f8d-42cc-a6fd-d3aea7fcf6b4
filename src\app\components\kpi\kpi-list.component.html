<div class="row kpi-list-section">
    <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12  pl-0 pr-0">
        <div class="card-body kpi-list-table mb-0">
            <div class="row performance-section mr-0 ml-0">
                <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0">
                    <div class="panel panel-default tab-bg border-left-0 border-right-0">
                        <div class="panel-heading">
                            <div class="panel-title custom-tabs pc-tabs">
                                <ul class="nav nav-tabs">
                                    <li class="nav-item" role="presentation" *ngFor="let tab of tabList;"
                                        (click)="onTabClick(tab)">
                                        <button class="nav-link nav-custom" [ngClass]="tab.active?'active Body-R':'Body-R'"
                                            id="home-tab" data-bs-toggle="tab" type="button" role="tab"
                                            aria-controls="home" aria-selected="true">
                                            {{tab.name}}
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="content-bg ">
                        <div class="custom-outer-margin" *ngIf="tabName=='KPI List'">
                            <div class="row border-bottom mr-0 ml-0 margin-top-header filter-bg">
                                <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0">
                                    <div
                                        class="float-left kpi-list-searchbox-width search-kpi-left col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12">
                                        <div class="d-inline-block search-box-width search">
                                            <span class="fa fa-search p-1  kpi-search-icon"></span>
                                            <input #gb pInputText type="text"
                                                class="search-text-company companyListSearchHeight"
                                                placeholder="Search KPI" [appApplyFilter]="{ data: kpiListClone, columns: kpiHeaders,IsFreezeColumn:false,freezeColumns:'Kpi,text'}"
                                                (filtered)="kpiList = $event" [(ngModel)]="searchText">
                                        </div>
                                    </div>
                                    <div class="float-right custom-right-header" id="create-kpi">
                                        <div class="d-inline-block btn-kpi-left">
                                            <nep-button Name="create-kpi" Type="Primary" (click)="ShowAddKPI=true;Openpopup()">
                                                <em class="fa fa-plus"></em> KPI
                                            </nep-button>
                                        </div>
                                    </div>

                                    <div class="float-right dropdown-kpi-right custom-right-header">
                                        <div class="d-inline-block custom-right-select">
                                
                                            <kendo-dropdownlist id="kpiType-header-filter"
                                            class="kpi-list k-dropdown-width-220 k-custom-solid-dropdown k-dropdown-height-36 k-input-flat k-dropdown-no-border"
                                            [data]="groupedData" textField="pageConfigAliasName" valueField="field"
                                                [(ngModel)]="selectedKpiType" (valueChange)="GetSelectedKpiData($event)" [filterable]="true"
                                                [groupField]="groupField" [clearButton]="false" (filterChange)="handleFilter($event)">
                                                <ng-template kendoDropDownListGroupTemplate let-dataItem>
                                                    <div title="{{ dataItem }}" class="flex align-items-center">
                                                        <span class="TextTruncate" title="{{ dataItem }}">{{ dataItem }}</span>
                                                    </div>
                                                </ng-template>
                                                <ng-template kendoDropDownListItemTemplate let-dataItem>
                                                    <span id="kpi-type-name" class="TextTruncate" title="{{ dataItem.pageConfigAliasName }}">
                                                        {{ dataItem.pageConfigAliasName }}
                                                    </span>
                                                </ng-template>
                                            </kendo-dropdownlist>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="row border-bottom mr-0 ml-0 margin-top-header filter-bg">
                                <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 pl-0 pr-0">
                                    <kendo-grid [kendoGridBinding]="kpiList" [resizable]="true" [sortable]="true" scrollable="virtual" [rowHeight]="42" 
                                        class="kendo-grid-kpi-list k-grid-border-right-width k-grid-outline-none">
                                            <kendo-grid-column  *ngFor="let col of kpiHeaders;let i=index;let last=last" class="TextTruncate"  [field]="col.field" [title]="col.header" [width]="i === 0 ? 300 : 150">
                                                <ng-template kendoGridHeaderTemplate>
                                                    <div class="header-icon-wrapper wd-98">
                                                        <span class="TextTruncate S-M">
                                                            {{col.header}}
                                                        </span>
                                                    </div>
                                                </ng-template>
                                                <ng-template kendoGridCellTemplate let-rowData>
                                                    <div class="kpi-name-cell" style="position: relative;">
                                                        <span class="text-truncate d-inline-block kpi-title"
                                                            [ngClass]="(rowData.isBoldKPI || rowData.isHeader || rowData.isBoldKpi) && col.header == 'KPI Name' ? 'font-weight-bold':''">{{rowData[col.field]}}</span>
                                                        <span
                                                            *ngIf="col.header == 'KPI Name' && rowData.synonym?.length"
                                                            class="synonym-container synonym-badge float-right company-buttons" 
                                                            [matTooltip]="rowData?.synonym?.split(',').join('\n')" matTooltipClass="mat-tooltip-multiline" >
                                                            Synonym(s)
                                                        </span>
                                                        <div class="float-right company-buttons" *ngIf="last" title="Delete kpi">
                                                            <div (click)="deletePopUpFun(rowData)">
                                                                <img src='assets/dist/images/delete.svg' alt="" />
                                                            </div>
                                                        </div>
                                                        <div class="float-right company-buttons margin12" *ngIf="last" id="edit-kpi" title="Edit kpi">
                                                            <div (click)="OnEdit(rowData)">
                                                                <img src='assets/dist/images/EditIcon.svg' alt="" />
                                                            </div>
                                                        </div>
                                                        <span *ngIf="col.header == 'KPI Name'"
                                                            class="pl-3 pr-3 kpi-header">
                                                            <img *ngIf="rowData.isHeader" class="img-header"
                                                                src="assets/dist/images/kpi-header.svg"
                                                                alt="kpi-header.svg" [pTooltip]="'Header KPI'"
                                                                tooltipPosition="top"
                                                                tooltipStyleClass="bg-darkgrey-tooltip-color" />
                                                            <img class="pl-2 formula-image"
                                                                *ngIf="!rowData?.isHeader && rowData?.kpiInfo !='Text'"
                                                                [ngClass]="rowData.formula!=null && rowData.formula!='' ?'d-inline-block':'d-none'"
                                                                (click)="openFormulaPopup(rowData)"
                                                                src="assets/dist/images/formula.svg"
                                                                alt="formula.svg" />
                                                        </span>
                                                    </div>
                                                </ng-template>
                                            </kendo-grid-column>
                                            <ng-template kendoGridNoRecordsTemplate>
                                                <app-empty-state class="finacials-beta-empty-state"
                                                    [imageHeight]="'calc(100vh - 245px)'" [isGraphImage]="false"></app-empty-state>
                                            </ng-template>
                                    </kendo-grid>
                                </div>
                            </div>
                        </div>
                        <div class="custom-outer-margin-right" *ngIf="tabName=='Mapping'">
                            <app-kpi-mapping [kpiTypeList]="kpiTypes" [kpiTypeSelected]="selectedKpiType"
                                [reload]="isReload" [moduleID]="selectedKpiType?.moduleID"
                                [kpiType]=selectedKpiType?.name *ngIf="tabName=='Mapping'">
                            </app-kpi-mapping>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Popup div -->
    <div *ngIf="ShowAddKPI">
        <div id="kpi-add-dialog"
            class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12 AddOrUpdateKpi add-or-update-modal">
            <div class="nep-modal nep-modal-show kpi-add-edit-modal nep-kpi-bg">
                <div class="nep-modal-mask"></div>
                <div [style.width]="customwidth" [style.top]="customTop"
                    class="nep-card nep-card-shadow nep-modal-panel nep-modal-default nep-kpilist-pr">
                    <div class="nep-card-header" [ngClass]="hasHeaderStyle? 'nep-modal-title-custom':'nep-modal-title'">
                        {{modalTitle}}
                        <div class="float-right close-icon cursor-filter">
                            <i class="pi pi-times" (click)="onCloseAddKpiModal()"></i>
                        </div>
                    </div>
                    <div class="nep-card-body">
                        <div class="row mr-0 ml-0 main-row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 pl-0 pr-0">
                                <div class="row mr-0 ml-0">
                                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12 pl-0 pr-0 ">
                                        <div class="Caption-M kpi-mandatory-label">KPI Name</div>
                                        <nep-input [ngClass]="{'custom-error-kpi-input':isError}"
                                            (onChange)="onKpiNameChange($event)" (onBlur)="onKpiNameUpdate(); "
                                            (mouseout)="onKpiNameChange($event);onKpiNameUpdate();" [value]="addKpiName"
                                            [placeholder]=placeholderKPIName [title]="addKpiName"
                                            class="kpis-custom-select custom-nep-input default-txtcolor TextTruncate"></nep-input>
                                        <div *ngIf="isError" class="nep-error">{{ErrorMessage}}</div>
                                    </div>
                                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12">
                                        <div class="row kpi-label-padding pl-0 pr-0">
                                            <div class="col-6 col-md-6 col-sm-6 col-lg-6 col-xs-6 col-xl-6 kpi-label-value pl-0 pr-0"
                                                *ngIf="!UpdateKpi">
                                                <div class="Caption-M kpi-mandatory-label">KPI Type</div>
                                                <div class="flex justify-content-center">
                                                  
                                                    <kendo-dropdownlist id="add-update-kpiType"
                                                        class="kpi-list k-dropdown-width-100 k-custom-solid-dropdown k-dropdown-height-36 k-input-flat k-dropdown-no-border"
                                                        [data]="groupedData" textField="pageConfigAliasName" valueField="field" [(ngModel)]="selectedAddKpiType"
                                                        (valueChange)="onSelectKpiGroup($event)" [filterable]="true" [groupField]="groupField" [clearButton]="false"
                                                        (filterChange)="handleFilter($event)">
                                                        <ng-template kendoDropDownListGroupTemplate let-dataItem>
                                                            <div title="{{ dataItem }}" class="flex align-items-center">
                                                                <span class="TextTruncate" title="{{ dataItem }}">{{ dataItem }}</span>
                                                            </div>
                                                        </ng-template>
                                                        <ng-template kendoDropDownListItemTemplate let-dataItem>
                                                            <span id="kpi-type-name" class="TextTruncate" title="{{ dataItem.pageConfigAliasName }}">
                                                                {{ dataItem.pageConfigAliasName }}
                                                            </span>
                                                        </ng-template>
                                                    </kendo-dropdownlist>
                                                </div>
                                            </div>
                                            <div
                                                class="col-6 col-md-6 col-sm-6 col-lg-6 col-xs-6 col-xl-6 kpi-label-value pl-0 pr-0 kpi-padding-top">
                                                <div class="Caption-M kpi-mandatory-label">KPI Info</div>
                                                <kendo-combobox [clearButton]="false" [(ngModel)]="selectedKpiInfoType"
                                                     [fillMode]="'flat'" [filterable]="true" name="kpiInfoModule"
                                                    class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32" [size]="'medium'"
                                                    [data]="kpiInfoTypes" [filterable]="true" [value]="selectedKpiInfoType" [valuePrimitive]="false"
                                                    textField="name" placeholder="Select KPI Info" (valueChange)="onSelectKpiInfo($event)"
                                                    valueField="name">
                                                </kendo-combobox>

                                            </div>
                                            <div class="col-6 col-md-6 col-sm-6 col-lg-6 col-xs-6 col-xl-6 kpi-label-value pl-0 pr-0 kpi-padding-top"
                                                *ngIf="selectedKpiInfoType.name !='Text' && selectedAddKpiType.moduleID!=16">
                                                <div class="Caption-M kpi-mandatory-label">FX Conversion
                                                    Methodology</div>
                                                <kendo-combobox [clearButton]="false" [(ngModel)]="selectedMethodology" [fillMode]="'flat'" [filterable]="true"
                                                    name="MethodologyModule" class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32"
                                                    [size]="'medium'" [data]="methodologyList" [filterable]="true" [value]="selectedMethodology"
                                                    [valuePrimitive]="false" textField="name" placeholder="Select Methodology"
                                                    (valueChange)="onSelectMethodology($event)" valueField="id">
                                                </kendo-combobox>
                                            </div>
                                            <div class="col-6 col-md-6 col-sm-6 col-lg-6 col-xs-6 col-xl-6 kpi-label-value pl-0 pr-0 kpi-padding-top"
                                                *ngIf="kpiModule.CapTable1 == selectedAddKpiType.moduleID || kpiModule.CapTable2 == selectedAddKpiType.moduleID || kpiModule.CapTable3 == selectedAddKpiType.moduleID || kpiModule.CapTable4 == selectedAddKpiType.moduleID || kpiModule.CapTable5 == selectedAddKpiType.moduleID || kpiModule.CapTable6 == selectedAddKpiType.moduleID || kpiModule.CapTable7 == selectedAddKpiType.moduleID || kpiModule.CapTable8 == selectedAddKpiType.moduleID || kpiModule.CapTable9 == selectedAddKpiType.moduleID || kpiModule.CapTable10 == selectedAddKpiType.moduleID    
                                                || kpiModule.OtherCapTable1 == selectedAddKpiType.moduleID || kpiModule.OtherCapTable2 == selectedAddKpiType.moduleID || kpiModule.OtherCapTable3 == selectedAddKpiType.moduleID || kpiModule.OtherCapTable4 == selectedAddKpiType.moduleID || kpiModule.OtherCapTable5 == selectedAddKpiType.moduleID || kpiModule.OtherCapTable6 == selectedAddKpiType.moduleID || kpiModule.OtherCapTable7 == selectedAddKpiType.moduleID || kpiModule.OtherCapTable8 == selectedAddKpiType.moduleID || kpiModule.OtherCapTable9 == selectedAddKpiType.moduleID || kpiModule.OtherCapTable10 == selectedAddKpiType.moduleID">
                                                <div class="Caption-M kpi-mandatory-label">Grid Type
                                                </div>
                                                <kendo-combobox [clearButton]="false" [(ngModel)]="selectedGridType" [fillMode]="'flat'" [filterable]="true"
                                                    name="kpiTypeModule" class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32"
                                                    [size]="'medium'" [data]="gridTypeList" [filterable]="true" [value]="selectedGridType"
                                                    [valuePrimitive]="false" textField="kpiType" placeholder="Select Grid Type"
                                                    (valueChange)="onKpiType($event)" valueField="kpiTypeId">
                                                </kendo-combobox>
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12 pl-0 pr-2 kpi-padding-bottom">
                                        <div class="row mr-0 ml-0">
                                            <div class="col-3 col-md-3 col-sm-3 col-lg-3 col-xs-3 col-xl-3 pl-0 pr-0">
                                                <mat-checkbox (mouseout)="onKpiNameUpdate();" label
                                                    class=" mat-custom-checkbox d-inline-block "
                                                    [disabled]="(isBoldKPI === true || selectedGridType?.kpiType ==='Column') ? 'disabled' : null"
                                                    name="IsHeader" [(ngModel)]="isHeader" #IsHeader="ngModel">
                                                </mat-checkbox>
                                                <div class="d-inline-block pl-2 info Caption-M"
                                                    [ngClass]="(isBoldKPI === true || selectedGridType?.kpiType ==='Column')?'checkbox-diasbledcolor':''">
                                                    <span class="Caption-M">KPI Header</span>
                                                </div>
                                                <div class="d-inline-block pl-1 info"> 
                                                        <kendo-icon class="font-bg" name="info" [size]="'medium'" [pTooltip]="'This will enable the KPI as a header on the table'" tooltipStyleClass="bg-grey-tooltip-color" placement="right"></kendo-icon>                        
                                                    </div>
                                            </div>
                                            <div class="col-3 col-md-3 col-sm-3 col-lg-3 col-xs-3 col-xl-3 pl-3 pr-0">
                                                <mat-checkbox (mouseout)="onKpiNameUpdate();" label
                                                    class="d-inline-block mat-custom-checkbox " name="boldKPI"
                                                    [(ngModel)]="isBoldKPI" #boldKPI="ngModel"
                                                    [disabled]="isHeader === true ? 'disabled' : null">
                                                </mat-checkbox>
                                                <div class="d-inline-block pl-2 info Caption-M"
                                                    [ngClass]="isHeader === true?'checkbox-diasbledcolor':''">
                                                    <span class="Caption-M"> Bold KPI </span>
                                                </div>
                                                <div class="d-inline-block pl-1 info">
                                                        <kendo-icon class="font-bg" name="info" [size]="'medium'" pTooltip="This will make the KPI bold on the table" tooltipStyleClass="bg-grey-tooltip-color" placement="right"></kendo-icon>
                                                </div>
                                            </div>
                                            <div class="col-6 col-md-6 col-sm-6 col-lg-6 col-xs-6 col-xl-6 pl-1 pr-0"
                                                *ngIf="selectedGridType?.kpiType === 'Row' && [kpiModule.CapTable1, kpiModule.CapTable2, kpiModule.CapTable3, kpiModule.CapTable4, kpiModule.CapTable5,kpiModule.CapTable6,kpiModule.CapTable7,kpiModule.CapTable8,kpiModule.CapTable9,kpiModule.CapTable10,kpiModule.OtherCapTable1,kpiModule.OtherCapTable2,kpiModule.OtherCapTable3,kpiModule.OtherCapTable4,kpiModule.OtherCapTable5,kpiModule.OtherCapTable6,kpiModule.OtherCapTable7,kpiModule.OtherCapTable8,kpiModule.OtherCapTable9,kpiModule.OtherCapTable10].includes(selectedAddKpiType.moduleID)">
                                                <mat-checkbox (mouseout)="onKpiNameUpdate();" label
                                                    class="d-inline-block mat-custom-checkbox " name="overrule"
                                                    [(ngModel)]="isOverrule" #overrule="ngModel">
                                                </mat-checkbox>
                                                <div class="d-inline-block pl-2 info Caption-M">
                                                    <span class="Caption-M"> Overrule Column Type </span>
                                                </div>
                                                <div class="d-inline-block pl-1 info">
                                                        <kendo-icon class="font-bg" name="info" [size]="'medium'" pTooltip="This option will retain data type for row grid type" tooltipStyleClass="bg-grey-tooltip-color" placement="right"></kendo-icon>
                                                </div>
                                            </div>
                                        </div>
                                        </div>
                                        <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 pl-0 pr-0">
                                            <div class="Caption-M">Synonyms</div>
                                            <div id="tagChips" class="opn-doc-mar-tp">
                                                <chip maxlen="200" [isReadonly]=false (removeItem)="removeTagItem($event)" (addItem)="addTagItem($event)"
                                                    [chipitems]="kpisynonym"
                                                    customclass="mat-chip mat-primary mat-standard-chip circularOutlined textColor fontStyle14">
                                                </chip>
                                            </div>                                  
                                        </div>                                      
                                        <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 pl-0 pr-0">
                                        <div class="Caption-M">Definition</div>
                                        <div class="custom-nep-input kpi-modal-textarea">
                                            <label class="nep-input nep-input-textarea kpitext-label">
                                                <textarea rows="4" placeholder="input something" spellcheck="false"
                                                    data-ms-editor="true" placeholder="Enter Definition"
                                                    class="list-kpi-h-o" [(ngModel)]="addKpiDesc"
                                                    (mouseout)="onDescChange()" class="pl-0"
                                                    (change)="onDescChange()"></textarea>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nep-card-footer nep-card-right nep-modal-footer add-or-update-modal-footer">
                        <div *ngIf="!IsInfoPopup">
                            <nep-button Name="reset-create-kpi" *ngIf="!UpdateKpi" [disabled]="disableAdd" Type="Secondary" (click)="reset()">
                                Reset
                            </nep-button>
                            <nep-button Name="reset-update-kpi" *ngIf="UpdateKpi" [disabled]="disableAdd" Type="Secondary"
                                (click)="updateReset()">
                                Reset
                            </nep-button>
                            <nep-button Name="reset-add-or-update-kpi" Type="Primary" [disabled]="disableAdd" (click)="AddOrUpdateKpi()"
                                class="kpinep-butn-pl">
                                {{primaryButtonName}}
                            </nep-button>
                        </div>
                        <div *ngIf="IsInfoPopup">
                            <nep-button Name="reset-add-or-update-kpi" Type="Primary" [disabled]="disableAdd" (click)="AddOrUpdateKpi()"
                                class="kpinep-butn-pl">
                                {{primaryButtonName}}
                            </nep-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div *ngIf="deletePopUp">
        <confirm-modal [primaryButtonName]="primaryDeleteButtonName" [secondaryButtonName]="secondaryDeleteButtonName"
            [modalTitle]="deleteModalTitle" (primaryButtonEvent)="performDelete()"
            (secondaryButtonEvent)="performCancel()">
            <div class="modalBodyTextStyle">
                <div>
                    {{deleteModalBody1}}
                </div>
            </div>
        </confirm-modal>
    </div>
</div>
<app-kpi-formula-builder [moduleId]="selectedKpiType?.moduleID" [kpiModel]="selectedFormulaKPI" *ngIf="isOpenPopUp"
    (OnClose)="closeFormulaPopUp($event)" [formulaKPITypes]="groupedData" [selectedFormulaKPIType]="selectedKpiType"
    [companyId]="0"></app-kpi-formula-builder>
<app-loader-component [ngStyle]="blockedTable?{'display':'block'}:{'display':'none'}" *ngIf="blockedTable">
</app-loader-component>