import { Component } from "@angular/core";
import { LazyLoadEvent } from "primeng/api";
import { NumberDecimalConst } from "src/app/common/constants";
import { InvestorService } from "src/app/services/investor.service";
import {
  FeaturesEnum,
  PermissionService,
} from "src/app/services/permission.service";
import { Router } from "@angular/router";
import { SortDescriptor, State } from "@progress/kendo-data-query";
import { GridDataResult } from "@progress/kendo-angular-grid";
import { Observable, of } from "rxjs";
import { KendoService } from "src/app/services/kendo.service";
import { CommonSubFeaturePermissionService } from "src/app/services/subPermission.service";
import { ErrorMessage } from "src/app/services/miscellaneous.service";
import { ToastrService } from "ngx-toastr";

@Component({
  selector: "app-investorlist",
  templateUrl: "./investorlist.component.html",
  styleUrls: ["./investorlist.component.scss"],
})
export class InvestorlistComponent {
  feature: typeof FeaturesEnum = FeaturesEnum;
  NumberDecimalConst = NumberDecimalConst;
  investorList = [];
  globalFilter: string = "";
  isLoading: boolean = false;
  paginationFilterClone: any = {};
  public view: Observable<GridDataResult>;
  public state: State = {
    skip: 0,
    take: 100,
  };
  sort: SortDescriptor[] = [];
  cols = [
    {
      field: "investorName",
      sortFieldName: "investorName",
      header: "InvestorName",
    },
    { field: "website", sortFieldName: "website", header: "Website" },
    {
      field: "totalCommitment",
      sortFieldName: "totalCommitment",
      header: "TotalCommitment",
    },
  ];
  investorListClone: any = [];
  userPermissions: any = [];
  userId: number = 0;
  canViewInv: boolean = false;
  canAddInv: boolean = false;
  canExportInv: boolean = false;
  constructor(
    private _investorService: InvestorService,
    private kendoService: KendoService,
    private router: Router,
    private subPermissionService: CommonSubFeaturePermissionService,
    private toastrService: ToastrService,
  ) {
    this.getSubFeatureAccessPermissions();
    this.getConfigurationDetails(null);
  }
  getSubFeatureAccessPermissions() {
    this.subPermissionService
      .getCommonFeatureAccessPermissions(FeaturesEnum.Investors)
      .subscribe({
        next: (result) => {
          if (result.length > 0) {
            this.canViewInv = result?.map((x) => x.canView).includes(true);
            this.canAddInv = result?.map((x) => x.canAdd).includes(true);
            this.canExportInv = result?.map((x) => x.canExport).includes(true);
          }
        },
        error: (_error) => {},
      });
  }
  showNoAccessError() {
    this.toastrService.error(ErrorMessage.NoAccessInvestor, "", { positionClass: "toast-center-center" });
  }
  addRedirect(){
    if (this.canAddInv) {
      this.router.navigate(['/addinvestor']);
    }
    else {
      this.showNoAccessError();
    }
  }
  investorListExport(){
    if(!this.canExportInv){
      this.showNoAccessError();
    }
  }
  getConfigurationDetails(event: any) {
    this.isLoading = true;
    if (event == null) {
      event = {
        first: 0,
        rows: 100,
        globalFilter: null,
        sortField: null,
        sortOrder: 1,
        FilterWithoutPaging: true,
      };
    }
    if (event.multiSortMeta == undefined) {
      event.multiSortMeta = [{ field: "investorName", order: 1 }];
      event.sortField = "investorName";
    }
    this._investorService
      .getInvestorList({ paginationFilter: event })
      .subscribe((result: any) => {
        this.isLoading = false;
        if (result != null) {
          this.investorList = result;
          this.investorListClone = result || [];
          this.view = of<GridDataResult>({
            data: this.investorList,
            total: this.investorList.length,
          });
        } 
        else 
          this.investorList = [];
      });
  }

  redirectToInvestor(investor) {
    localStorage.setItem("headerName", investor.investorName);
    this.router.navigate(["/investor-details", investor.encryptedInvestorId]);
  }

  dataStateChange($event) {
    this.state.skip = $event.skip;
    this.state.take = $event.take;
    let params: any = {
      first: $event.skip,
      rows: $event.take,
      globalFilter: this.globalFilter != "" ? this.globalFilter : null,
      sortField: null,
      sortOrder: 1,
    };
    let result = this.kendoService.getHeaderValue(
      params,
      $event,
      this.cols,
      this.sort
    );
    params = result.params;
    this.sort = result.parentSort;
    this.getConfigurationDetails(params);
  }
}