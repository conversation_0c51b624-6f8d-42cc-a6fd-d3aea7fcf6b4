import { Component, ElementRef, EventEmitter, Inject, Input, Output, ViewChild } from "@angular/core";
import { HelperService } from "src/app/services/helper.service";
import { FileUploadService } from "src/app/services/file-upload.service";
import { ToastrService } from "ngx-toastr";
import { BulkuploadConstants, ModuleList } from "src/app/common/constants";
import { FileExtension } from "../../../common/enums";
@Component({
  selector: "all-popup-bulk-upload",
  templateUrl: "./all-popup-bulk-upload.component.html",
  styleUrls: ["./all-popup-bulk-upload.component.scss"],
})
export class AllPopupBulkUploadComponent {
  @ViewChild("fileDropRef") fileDropEl: ElementRef;
  @ViewChild("singleFileDropRef") singleFileDropRef: ElementRef;
  files: any = [];
  singleUploadFiles: any = [];
  FileExtension = FileExtension;
  comments: string;
  @Output() cancelButtonEvent: EventEmitter<any> = new EventEmitter();
  @Output() confirmButtonEvent: EventEmitter<any> = new EventEmitter();
  @Output() toasterMessageEvent: EventEmitter<any> = new EventEmitter();
  @Input() companyId:number = 0;
  @Input() fundId: number = 0;
  @Input() moduleName : string = "";
  isLoader: boolean = false;
  constructor(
    private helperService: HelperService,
    private fileUploadService: FileUploadService,
    public toastrService: ToastrService
  ) {
  }

  /**
   * Closes the popup and emits the cancelButtonEvent.
   */
  onClose(): void {
    this.cancelButtonEvent.emit(); 
  }

  /**
   * Retrieves the static icon path for the given name.
   * 
   * @param name - The name of the icon.
   * @returns The static icon path.
   */
  getIcons(name: string) {
    return this.helperService.getstaticIconPath(name);
  }
  /**
   * Handles the file change event.
   * @param value - The selected file(s).
   */
  onFileChange(value: any) {
    for (let file of value) {
      const fileExtension = file?.name?.split(".").pop();
      file.extension = fileExtension.toLowerCase();
      if(fileExtension == BulkuploadConstants.exe){
        this.toasterMessageEvent.emit({message: BulkuploadConstants.validateSupportingFileFormat, type: BulkuploadConstants.error});
      }else{
        this.files.push(file);
      }
     
    }
    this.fileDropEl.nativeElement.value = '';
  }
  /**
   * Handles the change event when a single file is selected.
   * @param file - The selected file.
   */
  onSingleFileChange(file: File[]) {
    if(!BulkuploadConstants.fileExtensions.some(extension => file[0]?.name.endsWith(extension))){
     this.toasterMessageEvent.emit({message: BulkuploadConstants.ValidateExcelFormat, type: BulkuploadConstants.error});
      return;
    }
    this.singleUploadFiles = file;
  }

  /**
   * Removes a support document file from the files array at the specified index.
   * @param index - The index of the file to be removed.
   */
  removeSupportDocumentFile(index) {
    this.files?.splice(index, 1);
  }
  /**
   * Removes the single document file and clears the file input value.
   */
  removeSingleDocumentFile(){
    this.singleUploadFiles = [];
    this.singleFileDropRef.nativeElement.value = '';
  }

  /**
   * Handles the form submission for bulk file upload.
   * If there are no files selected, it emits an error message.
   * Otherwise, it creates a FormData object and appends the selected files and other form data.
   * Finally, it calls the fileUploadService to submit the files and handles the response.
   */
  onSubmit() {
    if (this.singleUploadFiles?.length == 0) {
      this.toasterMessageEvent.emit({message: BulkuploadConstants.ValidateEmptyFile, type: BulkuploadConstants.error});
      return;
    }
    const formData = new FormData();
    if (this.files?.length > 0) {
      for (const file of this.files) {
        formData.append(BulkuploadConstants.supportingDocuments, file);
      }
    } 
    formData.append(BulkuploadConstants.CompanyId, String(this.companyId));
    formData.append(BulkuploadConstants.Comments, this.comments ? this.comments : '');
    formData.append(BulkuploadConstants.FundId, String(this.fundId == null ? 0 : this.fundId));
    switch (this.moduleName?.trim()?.toLowerCase()) {
      case ModuleList.ESG:
        this.onSubmitESG(formData);
        break;
      default:
        this.onSubmitFinancials(formData);
        break;
    }
  }

  /**
   * Handles the submission of the ESG upload form.
   * If no files are selected, it emits an error message.
   * Otherwise, it creates a FormData object and appends the selected files and other form data.
   * Finally, it calls the UploadESG method of the fileUploadService and emits the result or error.
   */
  onSubmitESG(formData: FormData){
    formData.append(BulkuploadConstants.FileSize, this.singleUploadFiles[0].size);
    formData.append(BulkuploadConstants.FileName, this.singleUploadFiles[0].name);
    formData.append(BulkuploadConstants.FormFile, this.singleUploadFiles[0]);
    this.isLoader = true;
    this.fileUploadService.UploadESG(formData).subscribe({
      next: (result: any) => {
        this.isLoader = false;
          this.confirmButtonEvent.emit(result);
      },
      error: (error) => {
        this.confirmButtonEvent.emit(error);
        this.isLoader = false;
      },
    });
  }

  /**
   * Submits the financials for bulk upload.
   * If no files are selected, it emits an error message.
   * Otherwise, it creates a FormData object and appends the selected files and other necessary data.
   * Finally, it calls the fileUploadService to submit the files for upload.
   */
  onSubmitFinancials(formData: FormData){
    let subPageId: number = 0;
    formData.append(BulkuploadConstants.Template, this.singleUploadFiles[0]);
    formData.append(BulkuploadConstants.ModuleName, this.moduleName);
    formData.append(BulkuploadConstants.SubPageId, String(subPageId));
    this.isLoader = true;
    this.fileUploadService.OnSubmitAllFilesUpload(formData).subscribe({
      next: (result) => {
        this.isLoader = false;
        if (result?.length > 0) {
          this.confirmButtonEvent.emit(result);
        }
      },
      error: (error) => {
        this.confirmButtonEvent.emit(error);
        this.isLoader = false;
      },
    });
  }
}
