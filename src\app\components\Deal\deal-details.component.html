<div class="row mr-0 ml-0 deal-detail-section">
    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xs-12 col-xl-12 pr-0 pl-0">
        <div class="row mr-0 ml-0 card card-main static-card pb-4" *ngIf="dealPermissions?.canView ">
            <div class="col-md-12 col-sm-12 col-12 col-lg-12 col-xl-12 col-xs-12 pr-0 pl-0">
                <div class="static-pc-section fund-detail section1-height chart-area row mr-0 ml-0 mb-0">
                    <div class="col-12 pr-0 pl-0">
                        <div class="row mr-0 ml-0 pb-1 static-bg deal-detail-header-h">
                            <div class="col-12 pr-0 pl-0 chart-title pc-section-header">
                                <div class="float-right margin-right fund-static-header">
                                    <a href="javascript:void(0)"  title="Edit ">
                                        <img
                    id="btn-static-edit"
alt=""
                                            src="assets/dist/images/EditIcon.svg"
                                            (click)="EditDeal($event)"></a>
                                </div>
                            </div>
                        </div>
                        <div class="row mr-0 ml-0">
                            <div class="col-sm-12 pr-3 pt-3 pb-2">
                                <div class="line-wrapper">
                                    <span>{{pageDetails.displayName}} </span>
                                    <div class="line ml-2"></div>
                                </div>
                            </div>
                            <div class="pl-3 pr-3 Fund-section col-12">
                                <div class="row mr-0 ml-0"
                                    *ngIf="dealData !== undefined && dealData !== null && dealData.length > 0">
                                    <ng-container *ngFor="let deal of dealData">
                                        <div class="col-4 pr-0 pt-2 mb-1 custom-deal-label-padding"
                                            *ngIf="deal.hasLink">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-5">
                                                    <div class="truncate-text ">
                                                        <span title="{{deal.displayName}}">{{deal.displayName}}</span>
                                                    </div>
                                                </div>
                                                <div class="pr-0 pl-2 col-7  custom-deal-value-padding">
                                                    <div class="truncate-value">
                                                        <a class="deal-link"
                                                            *ngIf="model.fundDetails!=null;else empty_Text"
                                                            title="{{deal.displayName}}"
                                                            [routerLink]="[deal.link, deal.linkEncryptedId]">{{deal.value}}</a>
                                                        <ng-template #empty_Text class="detail-sec deal-link">NA
                                                        </ng-template>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4 pt-2 mb-1 pr-0 custom-deal-label-padding"
                                            *ngIf="!deal.hasLink">
                                            <div class="row mr-0 ml-0">
                                                <div class="pr-0 pl-0 col-5">
                                                    <div class="truncate-text">
                                                        <span title="{{deal.displayName}}">{{deal.displayName}}</span>
                                                    </div>
                                                </div>
                                                <div class="pr-0 pl-2 col-7 custom-deal-value-padding">
                                                    <div class="truncate-value">
                                                        <span title="{{deal.value}}"
                                                            *ngIf="deal.name == 'EnterpriseValue' && deal.displayName != 'Vintage' && deal.displayName != 'Holding Period (in days)'">
                                                            <ng-container
                                                                *ngIf="deal.value != 'NA' && deal.value != ''">
                                                                {{deal.value | number:
                                                                NumberDecimalConst.currencyDecimal}}
                                                            </ng-container>
                                                            <ng-container
                                                                *ngIf="deal.value == 'NA' || deal.value == ''">
                                                                NA
                                                            </ng-container>
                                                        </span>
                                                        <span title="{{deal.value}}"
                                                            *ngIf="deal.name != 'EnterpriseValue' && deal.name != 'InvestmentDate' && deal.displayName != 'Vintage' && deal.displayName != 'Holding Period (in days)'">{{isNumberCheck(deal.value)
                                                            ? (deal.value | number:
                                                            NumberDecimalConst.percentDecimal):
                                                            (deal.value == null || deal.value == "" || deal.value ==
                                                            "NA" )?'NA'
                                                            : deal.value}}</span>
                                                        <span title="{{deal.value}}"
                                                            *ngIf="deal.value !== 'NA' && (deal.name === 'EntryOwnershipPercent' || deal.name === 'CurrentExitOwnershipPercent')">%</span>
                                                        <span title="{{deal.value}}"
                                                            *ngIf="deal.displayName == 'Vintage'">{{deal.value}}</span>
                                                        <span title="{{deal.value}}"
                                                            *ngIf="deal.displayName == 'Holding Period (in days)'">{{deal.value}}</span>
                                                        <span title="{{deal.value}}"
                                                            *ngIf="deal.name == 'InvestmentDate'">{{(model.investmentDate
                                                            |
                                                            date:'MM/dd/yyyy')|| "NA"}}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </ng-container>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mr-0 ml-0 header-section header-performance">
            <div class="header-section col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                <div class="fund-header pb-2">
                    {{headerText}}
                </div>
            </div>
            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 trackrecord-section" *ngIf="dealTrackRecordPermissions.canView">
                <div class="financial-page">
                    <div class="panel panel-default border-0 tab-bg">
                        <div class="panel-heading">
                            <div class="panel-title custom-tabs">
                                <ul class="nav nav-tabs ">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link nav-custom active" id="home-tab" data-bs-toggle="tab"
                                            type="button" role="tab" aria-controls="home" aria-selected="true">
                                            Fund currency
                                        </button>
                                    </li>
                                </ul>
                            </div>
                            <div class="d-inline-block pref-icon float-right">
                                <a title="Add">
                                    <img alt="" tabindex="0" class="tc-add" (click)="open(fundHoldingModel)" title="Add"
                                        src="assets/dist/images/plus.svg" /></a>
                            </div>
                        </div>
                    </div>
                    <div class="filter-bg">
                            <div
                                class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 fund-performance-content chart-area deal-detail-header-b">
                                <div class="align-items-start">
                                    <div class="ui-widget-header">
                                        <div class="row mr-0 ml-0 deal-detail-header-bg border-bottom">
                                            <div
                                                class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                                                <div class="float-left">
                                                    <div class="allvalues-kpis">All values in:
                                                        {{model?.currencyDetail?.currencyCode}}</div>
                                                </div>
                                                <div class="float-right margin-right">
                                                    <div class="d-inline-block search">
                                                        <span class="fa fa-search fasearchicon"></span>

                                                        <input #tblportfolioCompanyFundHoldingColumns type="text" (input)="SearchHolding($event.target.value)"
                                                            pInputText
                                                            class="search-text-company TextTruncate companyListSearchHeight"
                                                            placeholder="Search">
                                                        
                                                    </div>
                                                    <div class="d-inline-block pl-2" id="div-download-tc">
                                                            <img
                                                            id="btn-download-tc"
                                                            tabindex="0"
                                                            src="assets/dist/images/Cloud-download.svg"
                                                            class="cursor-filter" title="Export"
                                                            (click)="exportFundHoldingValues()" alt="" /><span
                                                            class="excel-load deal-detail-header-m"
                                                            *ngIf="isExportLoading">
                                                            <i aria-hidden="true"
                                                                class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                                        </span></div>
                                                    <div class="d-inline float-left-right custom-border-right">
                                                        <span class="col-divider">
                                                        </span>
                                                    </div>
                                                    <div class="d-inline-block  pref-icon">
                                                        <img id="dropdownMenuButton btn-preference"
#dropdownMenuButton
                                                            [matMenuTriggerFor]="menu"
                                                            src="assets/dist/images/ConfigurationWhite.svg"
                                                            class="cursor-filter" alt=""
                                                            #tRecordTrigger="matMenuTrigger" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="datatable-container">
                                            <kendo-grid  [kendoGridBinding]="portfolioCompanyFundHolding"  scrollable="virtual" [rowHeight]="44"
                                                [resizable]="true"
                                                class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cab-table-grid kendo-deal-tr-grid">
                                                <kendo-grid-column *ngIf="frozenDealTableColumns.length > 0" [sticky]="true" [width]="200"
                                                     title="frozenDealTableColumns[0]?.header">
                                                    <ng-template kendoGridHeaderTemplate>
                                                        <span class="TextTruncate S-M">
                                                            {{frozenDealTableColumns[0]?.header}}</span>
                                                    </ng-template>
                                                    <ng-template kendoGridCellTemplate let-portfolioCompanyFundHolding>
                                                      <div class="TextTruncate k-cell-padding">{{portfolioCompanyFundHolding?.Quarter!=null?portfolioCompanyFundHolding?.Quarter:'NA'}}</div>  
                                                    </ng-template>
                                                </kendo-grid-column>
                                                <kendo-grid-column [width]="200"  title="col?.displayName"
                                                    *ngFor="let col of portfolioCompanyFundHoldingColumns;">
                                                    <ng-template kendoGridHeaderTemplate>
                                                        <span class="TextTruncate S-M"> {{col?.displayName}}</span>
                                                    </ng-template>
                                                    <ng-template kendoGridCellTemplate let-portfolioCompanyFundHolding>
                                                        <div class="TextTruncate k-cell-padding"
                                                            [ngClass]="(col.name==DealTrackRecordInfo.TotalValue||col.name==DealTrackRecordInfo.Dpi||col.name==DealTrackRecordInfo.Rvpi||col.name==DealTrackRecordInfo.GrossMultiple) ? ' table-data-right higlighted-cell' : 'table-data-right'">
                                                            {{
                                                            (col.name==DealTrackRecordInfo.ValuationDate)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":portfolioCompanyFundHolding[col.displayName]):
                                                            (col.name==DealTrackRecordInfo.InvestmentDate)?(model.investmentDate==null?"NA":model.investmentDate
                                                            | date:'MM/dd/yyyy'):
                                                            (col.name==DealTrackRecordInfo.InvestmentCost)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":portfolioCompanyFundHolding[col.displayName]|
                                                            number : NumberDecimalConst.currencyDecimal):
                                                            (col.name==DealTrackRecordInfo.RealizedValue)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":portfolioCompanyFundHolding[col.displayName]|
                                                            number : NumberDecimalConst.currencyDecimal):
                                                            (col.name==DealTrackRecordInfo.UnrealizedValue)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":portfolioCompanyFundHolding[col.displayName]|
                                                            number : NumberDecimalConst.currencyDecimal):
                                                            (col.name==DealTrackRecordInfo.TotalValue)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":portfolioCompanyFundHolding[col.displayName]|
                                                            number : NumberDecimalConst.currencyDecimal):
                                                            (col.name==DealTrackRecordInfo.Dpi)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":(portfolioCompanyFundHolding[col.displayName]|
                                                            number : NumberDecimalConst.multipleDecimal)+"x"):
                                                            (col.name==DealTrackRecordInfo.Rvpi)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":(portfolioCompanyFundHolding[col.displayName]|
                                                            number : NumberDecimalConst.multipleDecimal)+"x"):
                                                            (col.name==DealTrackRecordInfo.GrossMultiple)?((portfolioCompanyFundHolding[col.displayName]=="NA")?"NA":(portfolioCompanyFundHolding[col.displayName]|
                                                            number:NumberDecimalConst.multipleDecimal)+"x"):
                                                            (col.name==DealTrackRecordInfo.GrossIRR)?(portfolioCompanyFundHolding[col.displayName]=="NA"?"NA":(portfolioCompanyFundHolding[col.displayName]|
                                                            number : NumberDecimalConst.percentDecimal)+"%"):
                                                            col.dataType != 6?(
                                                            col.dataType==5?(portfolioCompanyFundHolding[col.displayName]!="NA"?(
                                                            portfolioCompanyFundHolding[col.displayName] |
                                                            number:NumberDecimalConst.multipleDecimal)+"x":portfolioCompanyFundHolding[col.displayName]):
                                                            col.dataType==4?(portfolioCompanyFundHolding[col.displayName]!="NA"?(
                                                            portfolioCompanyFundHolding[col.displayName] |
                                                            number:NumberDecimalConst.percentDecimal)+"%":portfolioCompanyFundHolding[col.displayName]):
                                                            col.dataType==3?(portfolioCompanyFundHolding[col.displayName]!="NA"?(
                                                            portfolioCompanyFundHolding[col.displayName] |
                                                            number :
                                                            NumberDecimalConst.currencyDecimal):portfolioCompanyFundHolding[col.displayName]):
                                                            col.dataType==2?(portfolioCompanyFundHolding[col.displayName]!="NA"?(
                                                            portfolioCompanyFundHolding[col.displayName] |
                                                            number : (showHoldingValueDecimals ?
                                                            NumberDecimalConst.singleDecimal:NumberDecimalConst.noDecimal)):portfolioCompanyFundHolding[col.displayName]):portfolioCompanyFundHolding[col.displayName]
                                                            ):
                                                            portfolioCompanyFundHolding[col.displayName]!="NA"?
                                                            (portfolioCompanyFundHolding[col.displayName]|
                                                            date:'MM/dd/yyyy' ):
                                                            portfolioCompanyFundHolding[col.displayName]
                                                            }}
                                                        </div>
                                                    </ng-template>
                                                </kendo-grid-column>
                                                <kendo-grid-column [width]="200" field="Action" title="Action" [cellStyle]="{'background-color': 'red'}">
                                                    <ng-template kendoGridHeaderTemplate>
                                                        <span class="TextTruncate S-M">
                                                            Action</span>
                                                    </ng-template>
                                                    <ng-template kendoGridCellTemplate let-portfolioCompanyFundHolding>
                                                        <div class="text-center k-cell-padding">
                                                            <a (click)="open(portfolioCompanyFundHolding)" class="text-center">
                                                                <i class="fa fa-pencil-square-o pr-2" title="Edit" aria-hidden="true"></i>
                                                            </a>
                                                            <a (click)="openConfirmPopUp(portfolioCompanyFundHolding)" class="text-center track-record-delete-icon">
                                                                <img title="Remove track record" alt="" src="assets/dist/images/Trash-icon.svg">
                                                            </a>
                                                        </div>
                                                    </ng-template>
                                                </kendo-grid-column>
                                                <ng-template kendoGridNoRecordsTemplate>
                                                    <app-empty-state class="finacials-beta-empty-state"
                                                        [isGraphImage]="false"></app-empty-state>
                                                </ng-template>
                                            </kendo-grid>
                                            <div class="col-info p-2" *ngIf="portfolioCompanyFundHolding.length>0">
                                                <span><i aria-hidden="true" class="fa fa-square calculated-value"
                                                        aria-hidden="true"></i><i>Calculated Values</i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>                     
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<mat-menu #menu="matMenu" [hasBackdrop]="true" class="fixed-menu">
    <div class="filter-first" (click)="$event.stopPropagation()" (keydown)="$event.stopPropagation()">
        <div class="row m-0 ">
            <div class="col-12 pb-1 pt-3 label-align">
                Values in:
            </div>
            <div class="pl-3 pr-3 ">
                <kendo-combobox [clearButton]="false" [(ngModel)]="trackRecordValueUnit" #unit="ngModel" [fillMode]="'solid'"
                    [filterable]="true" name="Unit" 
                    class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="unitTypeList"
                    [filterable]="true"  [valuePrimitive]="false" textField="unitType"
                    placeholder="Select Unit" (valueChange)="convertFundHoldingValueUnits()" valueField="typeId">
                </kendo-combobox>
            </div>
        </div>

    </div>
</mat-menu>
<div *ngIf="isConfirmPopUp">
    <modal customwidth="489px" modalTitle="Confirmation" primaryButtonName="Confirm" secondaryButtonName="Cancel"
        (primaryButtonEvent)="OnDeleteTrackRecord($event)" (secondaryButtonEvent)="OnCancelDeleteTrackRecord($event)">
        <div class="row">
            <div class="col-lg-12 col-md-12 col-xs-12">
                Are you sure you want to delete this deal track record for Quarter {{selectedFundHolding?.Quarter}}?
            </div>
        </div>
    </modal>
</div>