<div #advancedFilters id="filtersContainer"class="advancefilters-w-h">
  <div id="containerHeader" class="advancefilters-hw-p">
    <span class="advancefilters-fs-f">
      Apply filters
    </span>
    <span class="advancefilters-fs-fc" (click)="clearAll()">
          <!-- [ngClass]="disableApplyFilters ? 'disableClearAll' : 'enableClearAll'" -->
      Clear all
    </span>
  </div>
  <div id="containerBody" class="advancefilters-bb-bt">
    <div class="advancefilters-d">
      <div class="advancefilters-w-bg">
        <div
          [ngClass]="activeFilterCategory === filterCategory ? 'filterHeadingStyle activeCategory' : 'filterHeadingStyle inactiveCategory'"
          *ngFor="let filterCategory of filterCategories" (click)="selectFilterCategory(filterCategory)">
          {{filterCategory}}
        </div>
      </div>
      <div class="advancefilters-height">

        <div
          *ngIf="activeFilterCategory === 'File Format' || activeFilterCategory === 'Type' || activeFilterCategory === 'Sub-Type'">
          <div *ngFor="let activerFilter of activeFilterList ; let indexOfelement=index;">
            <div class="advancefilters-pt-pl">
              <checkbox height="20px" width="20" [isChecked]="activerFilter.isChecked"
                (change)="handleCheckBox($event,indexOfelement)" [value]="activerFilter.name"></checkbox>
            </div>
          </div>
        </div>

        <div *ngIf="activeFilterCategory === 'Document Date'">
          <div class="advancefilters-w-mt-ml">
            <span  class="contentStyle">From Date</span>
              <kendo-datepicker calendarType="classic" class="k-picker-custom-flat k-datepicker-height-32"  [format]="format" [fillMode]="'flat'"
                                        placeholder="Select from date"
                                        id="fromDate" name="fromDate" [popupSettings]="popupSettings"
                                        [(ngModel)]="fromDate" [value]="getFormattedDate(fromDate)"
                                        (valueChange)="onChangeFromDate(fromDate)"  #fromDateCal></kendo-datepicker>
            <span *ngIf="hasInvalidDateInput"
              class="advancefilters-c-pl">{{fromDateValidationMsg}}</span>
          </div>
          <div class="advancefilters-margintop">
            <span  class="contentStyle">To Date</span>
            <kendo-datepicker  calendarType="classic"   [popupSettings]="popupSettings" class="custom-to-date k-picker-custom-flat k-datepicker-height-32"  [format]="format" [fillMode]="'flat'"
            placeholder="Select to date"
            id="toDate" name="toDate"
            [(ngModel)]="toDate" [value]="getFormattedDate(toDate)"
            (valueChange)="onChangeToDate(toDate)"  #toDateCal></kendo-datepicker>
            <span *ngIf="hasInvalidDateInput"
             class="advancefilters-cp">{{toDateValidationMsg}}</span>
          </div>
        </div>

        <div class="advancefilters-w-mt">
          <div *ngIf="activeFilterCategory === 'Firm Name'">
            <div  class="contentStyle contentstyle-mb">Firm Name</div>
            <kendo-multiselect #multiSelect [disabled]="firms==undefined" [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'"
            [ngClass]="{'k-multiselect-search':selectedFirms?.length>0}" [kendoDropDownFilter]="filterSettings"
            name="SelectedFirms" [virtual]="virtualMultiSelect" [clearButton]="false"
            class="custom-multiselect-dr k-select-flat-custom multiselect-revealbi-custom" [tagMapper]="tagMapper"
            [data]="firms" [(ngModel)]="selectedFirms" [textField]="'firmName'" [valueField]="'firmID'"
            (valueChange)="onFirmChanged($event)" [autoClose]="false" placeholder="Select Firm(s)" [checkAll] ="true"
            >
            <ng-template kendoMultiSelectHeaderTemplate *ngIf="firms?.length>0">
                <div class="inline-container">
                    <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isFirmCheckAll" [indeterminate]="isIndet('firm')" (click)="onSelectAllClick('firm');$event.stopPropagation();" />
                    <kendo-label for="chk">{{ toggleAllText }}</kendo-label>
                    <kendo-label for="chk" > Select All</kendo-label>
                </div>
            </ng-template>
            <ng-template kendoMultiSelectItemTemplate let-dataItem>
                <span class="TextTruncate pl-1 Body-R" [title]="dataItem.fundName">{{ dataItem.firmName }}</span>
            </ng-template>
          </kendo-multiselect>

          </div>

          <div *ngIf="activeFilterCategory === 'Fund Name'">
            <div  class="contentStyle contentstyle-marginbottom">Fund Name</div>
            <kendo-multiselect #multiSelect [disabled]="funds==undefined" [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'"
            [ngClass]="{'k-multiselect-search':selectedFunds?.length>0}" [kendoDropDownFilter]="filterSettings"
            name="Funds" [virtual]="virtualMultiSelect" [clearButton]="false"
            class="custom-multiselect-dr k-select-flat-custom multiselect-revealbi-custom" [tagMapper]="tagMapper"
            [data]="funds" [(ngModel)]="selectedFunds" [textField]="'fundName'" [valueField]="'fundID'"
            (valueChange)="OnFundChanged($event)" [autoClose]="false" placeholder="Select"
            >
            <ng-template kendoMultiSelectHeaderTemplate *ngIf="funds?.length>0">
                <div class="inline-container">
                    <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isFundCheckAll" [indeterminate]="isIndet('fund')" (click)="onSelectAllClick('fund');$event.stopPropagation();" />
                    <kendo-label for="chk">{{ toggleAllText }}</kendo-label>
                    <kendo-label for="chk" > Select All</kendo-label>
                </div>
            </ng-template>
            <ng-template kendoMultiSelectItemTemplate let-dataItem>
                <span class="TextTruncate pl-1 Body-R" [title]="dataItem.fundName">{{ dataItem.fundName }}</span>
            </ng-template>
          </kendo-multiselect>
          </div>

          <div *ngIf="activeFilterCategory === 'Portfolio Company'">
            <div  class="contentStyle contentstyle-bottom-margin">Portfolio Company Name</div>
            <kendo-multiselect #multiSelect [disabled]="portfolioComapanies==undefined" [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'"
            [ngClass]="{'k-multiselect-search':selectedPortfolioComapanies?.length>0}" [kendoDropDownFilter]="filterSettings"
            name="companyName" [virtual]="virtualMultiSelect" [clearButton]="false"
            class="custom-multiselect-dr k-select-flat-custom multiselect-revealbi-custom" [tagMapper]="tagMapper"
            [data]="portfolioComapanies" [(ngModel)]="selectedPortfolioComapanies" [textField]="'companyName'" [valueField]="'portfolioCompanyID'"
            (valueChange)="onCompanyChanged($event)" [autoClose]="false" placeholder="Select Portfolio Company(s)"
            >
            <ng-template kendoMultiSelectHeaderTemplate *ngIf="portfolioComapanies?.length>0">
                <div class="inline-container">
                    <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isCompanyCheckAll" [indeterminate]="isIndet('company')" (click)="onSelectAllClick('company');$event.stopPropagation();" />
                    <kendo-label for="chk">{{ toggleAllText }}</kendo-label>
                    <kendo-label for="chk" > Select All</kendo-label>
                </div>
            </ng-template>
            <ng-template kendoMultiSelectItemTemplate let-dataItem>
                <span class="TextTruncate pl-1 Body-R" [title]="dataItem.fundName">{{ dataItem.companyName }}</span>
            </ng-template>
          </kendo-multiselect>
          </div>

          <div *ngIf="activeFilterCategory === 'Deal ID'">
            <div  class="contentStyle contentstyle-bm" >Deal Name</div>
            <kendo-multiselect #multiSelect [disabled]="deals==undefined" [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'"
            [ngClass]="{'k-multiselect-search':selectedDeals?.length>0}" [kendoDropDownFilter]="filterSettings"
            name="companyName" [virtual]="virtualMultiSelect" [clearButton]="false"
            class="custom-multiselect-dr k-select-flat-custom multiselect-revealbi-custom" [tagMapper]="tagMapper"
            [data]="deals" [(ngModel)]="selectedDeals" [textField]="'dealCustomID'" [valueField]="'dealID'"
            (valueChange)="onDealChanged($event)" [autoClose]="false" placeholder="Select Deal(s)"
            >
            <ng-template kendoMultiSelectHeaderTemplate *ngIf="deals?.length>0">
                <div class="inline-container">
                    <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isDealCheckAll" [indeterminate]="isIndet('deal')" (click)="onSelectAllClick('deal');$event.stopPropagation();" />
                    <kendo-label for="chk">{{ toggleAllText }}</kendo-label>
                    <kendo-label for="chk" > Select All</kendo-label>
                </div>
            </ng-template>
            <ng-template kendoMultiSelectItemTemplate let-dataItem>
                <span class="TextTruncate pl-1 Body-R" [title]="dataItem.fundName">{{ dataItem.dealCustomID }}</span>
            </ng-template>
          </kendo-multiselect>
          </div>

        </div>

      </div>
    </div>
  </div>
  <div id="containerFooter" class="containerfooter-hw" >
    <div class="advancefilters-fr-p" >
      <nep-button Type="Secondary" (click)="onCancelFilters()">
        Cancel
      </nep-button>
      <nep-button Type="Primary" [disabled]="disableApplyFilters" class="advnc-btn-pl"  (click)="onApplyFilters()">
        Apply
      </nep-button>
    </div>
  </div>
</div>
