import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { StaticInfoModificationComponent } from './static-info-modification-message.component';
import { GlobalConstants } from "src/app/common/constants";

describe('StaticInfoModificationComponent', () => {
  let component: StaticInfoModificationComponent;
  let fixture: ComponentFixture<StaticInfoModificationComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [StaticInfoModificationComponent]
    });
    fixture = TestBed.createComponent(StaticInfoModificationComponent);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  it(`staticInfoSaveWarningMessage has default value`, () => {
    expect(component.staticInfoSaveWarningMessage).toEqual(GlobalConstants.StaticInfoSaveWarningMessage);
  });
});
