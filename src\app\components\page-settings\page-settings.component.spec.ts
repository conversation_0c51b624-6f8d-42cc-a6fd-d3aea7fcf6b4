import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA, QueryList, ElementRef } from '@angular/core';
import { Router } from '@angular/router';
import { PageConfigurationService } from 'src/app/services/page-configuration.service';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { FormBuilder } from '@angular/forms';
import { ToastrService, ToastrModule } from 'ngx-toastr';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule } from '@angular/forms';
import { PageSettingsComponent } from './page-settings.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of } from 'rxjs/internal/observable/of';
import { throwError } from 'rxjs/internal/observable/throwError';
import { PageConfigurationPageDetails } from 'src/app/common/enums';
import { PageSettingsModule } from './page-settings.module';
xdescribe('PageSettingsComponent', () => {
  let component: PageSettingsComponent;
  let fixture: ComponentFixture<PageSettingsComponent>;
  let pageConfigurationService: jasmine.SpyObj<PageConfigurationService>;
  let mockToastrService = jasmine.createSpyObj('ToastrService', ['success', 'error']);
  let mockRouter = jasmine.createSpyObj('Router', ['navigate']);
  let mockModalService: jasmine.SpyObj<NgbModal>;
  let mockFormBuilder: FormBuilder;
  let pageConfigResponse = [];
  let mockElement;
  // Mock ElementRef
  const mockElementRef = new ElementRef({ nativeElement: document.createElement('div') });
  // Mock QueryList
  class MockQueryList extends QueryList<ElementRef> {
    first: ElementRef = mockElementRef;
    last: ElementRef = mockElementRef;
  }

  beforeEach(() => {
    mockModalService = jasmine.createSpyObj('NgbModal', ['open']);
    mockFormBuilder = new FormBuilder();
    const spyPageConfigService = jasmine.createSpyObj('PageConfigurationService', ['pageConfigurationSaveData', 'getTrackrecordDataTypes', 'getPageConfiguration']);
    TestBed.configureTestingModule({
      imports: [FormsModule, HttpClientTestingModule, ToastrModule.forRoot()],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [PageSettingsComponent],
      providers: [
        {
          provide: PageConfigurationService,
          useValue: spyPageConfigService
        },
        { provide: ToastrService, },
        { provide: 'BASE_URL', useValue: 'http://localhost:5000/' },
        { provide: NgbModal, useValue: mockModalService },
        { provide: FormBuilder, useValue: mockFormBuilder },
        { provide: Router, useValue: { navigate: jasmine.createSpy('navigate') } },
      ]
    }).compileComponents();

  });
  beforeEach(() => {
    fixture = TestBed.createComponent(PageSettingsComponent);
    pageConfigurationService = TestBed.inject(PageConfigurationService) as jasmine.SpyObj<PageConfigurationService>;
    const response =
    {
      "pageConfigurations": [
        {
          "trackRecordId": 1,
          "dataType": "Free Text"
        },
        {
          "trackRecordId": 2,
          "dataType": "Number"
        },
        {
          "trackRecordId": 3,
          "dataType": "Currency Value"
        },
        {
          "trackRecordId": 4,
          "dataType": "Percentage"
        },
        {
          "trackRecordId": 5,
          "dataType": "Multiple"
        },
        {
          "trackRecordId": 6,
          "dataType": "Date"
        },
        {
          "trackRecordId": 7,
          "dataType": "List"
        }
      ],
      "pageConfigurationsDatalist": [
        {
          "pageID": 1,
          "subPageID": 1,
          "fieldID": 1,
          "value": "258.366"
        },
        {
          "pageID": 1,
          "subPageID": 1,
          "fieldID": 2,
          "value": "*********.356"
        }
      ]
    };
    pageConfigurationService.getTrackRecordDataTypes.and.returnValue(of(response));
    const configResponse = [
      {
        "id": 1,
        "displayName": "Portfolio Company",
        "parentId": null,
        "name": "Portfolio Company",
        "description": null,
        "isActive": true,
        "isDeleted": false,
        "encryptedID": null,
        "sequenceNo": null,
        "pagePath": "/portfolio-company",
        "isCustom": false,
        "subPageDetailList": [
          {
            "id": 1,
            "displayName": "Static Informations",
            "parentId": 1,
            "name": "Static Information",
            "description": null,
            "isActive": true,
            "isDeleted": false,
            "encryptedID": null,
            "sequenceNo": 1,
            "pagePath": null,
            "isCustom": false,
            "isExpanded": false,
            "isDynamicFieldSupported": true,
            "subPageFieldList": [
              {
                "id": 2,
                "displayName": "Company Name",
                "subPageID": 1,
                "name": "CompanyName",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 1,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": true,
                "dataTypeId": 0,
                "isListData": true,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2022-03-16T16:19:05.61",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 1,
                "displayName": "Company Image",
                "subPageID": 1,
                "name": "CompanyLogo",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 2,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": true,
                "dataTypeId": 0,
                "isListData": false,
                "showOnList": false,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2022-03-16T16:19:05.61",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 881,
                "displayName": "Deal ID",
                "subPageID": 1,
                "name": "DealId",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 3,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": false,
                "dataTypeId": 0,
                "isListData": true,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2022-06-10T09:17:36.17",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 880,
                "displayName": "Fund ID",
                "subPageID": 1,
                "name": "FundId",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 4,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": false,
                "dataTypeId": 0,
                "isListData": true,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2022-06-10T09:17:36.15",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 4,
                "displayName": "SectorOne",
                "subPageID": 1,
                "name": "Sector",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 5,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": false,
                "dataTypeId": 0,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2022-03-16T16:19:05.61",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 5,
                "displayName": "Sub-Sector",
                "subPageID": 1,
                "name": "SubSector",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 6,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": false,
                "dataTypeId": 0,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2022-03-16T16:19:05.61",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 11,
                "displayName": "Status",
                "subPageID": 1,
                "name": "Status",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 7,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": false,
                "dataTypeId": 0,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2022-03-16T16:19:05.61",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 9,
                "displayName": "CurrencyOne",
                "subPageID": 1,
                "name": "Currency",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 8,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": false,
                "dataTypeId": 0,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2022-03-16T16:19:05.61",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 10,
                "displayName": "Website",
                "subPageID": 1,
                "name": "Website",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 9,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": false,
                "dataTypeId": 0,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2022-03-16T16:19:05.61",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 1339,
                "displayName": "Company Group",
                "subPageID": 1,
                "name": "CompanyGroupId",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 10,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": false,
                "dataTypeId": 0,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2023-01-19T17:34:27.763",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 8,
                "displayName": "Stock Exchange / Ticker",
                "subPageID": 1,
                "name": "StockExchange_Ticker",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 11,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": false,
                "dataTypeId": 0,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2022-03-16T16:19:05.61",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 107,
                "displayName": "Business description",
                "subPageID": 1,
                "name": "BussinessDescription",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 12,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": false,
                "dataTypeId": 0,
                "isListData": false,
                "showOnList": false,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2022-03-31T13:43:35.243",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 6,
                "displayName": "Financial Year End",
                "subPageID": 1,
                "name": "FinancialYearEnd",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 13,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": false,
                "dataTypeId": 0,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2022-03-16T16:19:05.61",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 882,
                "displayName": "Master Company Name",
                "subPageID": 1,
                "name": "MasterCompanyName",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 14,
                "pagePath": null,
                "isCustom": false,
                "isMandatory": false,
                "dataTypeId": 0,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2022-06-10T09:17:36.183",
                "createdBy": 1,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 1628,
                "displayName": "Custom Curr",
                "subPageID": 1,
                "name": "Customfield",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 15,
                "pagePath": null,
                "isCustom": true,
                "isMandatory": false,
                "dataTypeId": 3,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2023-11-15T07:28:44.32",
                "createdBy": 1090,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 1629,
                "displayName": "Custom Num",
                "subPageID": 1,
                "name": "Customfield",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 16,
                "pagePath": null,
                "isCustom": true,
                "isMandatory": false,
                "dataTypeId": 2,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2023-11-15T07:28:44.32",
                "createdBy": 1090,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 1630,
                "displayName": "Custom X",
                "subPageID": 1,
                "name": "Customfield",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 17,
                "pagePath": null,
                "isCustom": true,
                "isMandatory": false,
                "dataTypeId": 5,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2023-11-15T07:28:44.32",
                "createdBy": 1090,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 1631,
                "displayName": "Custom Date",
                "subPageID": 1,
                "name": "Customfield",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 18,
                "pagePath": null,
                "isCustom": true,
                "isMandatory": false,
                "dataTypeId": 6,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2023-11-15T07:28:44.32",
                "createdBy": 1090,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 1632,
                "displayName": "Custom Text",
                "subPageID": 1,
                "name": "Customfield",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 19,
                "pagePath": null,
                "isCustom": true,
                "isMandatory": false,
                "dataTypeId": 1,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2023-11-15T07:28:44.32",
                "createdBy": 1090,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 1633,
                "displayName": "Custom List",
                "subPageID": 1,
                "name": "Customfield",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 20,
                "pagePath": null,
                "isCustom": true,
                "isMandatory": false,
                "dataTypeId": 7,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2023-11-15T07:28:44.32",
                "createdBy": 1090,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              },
              {
                "id": 1636,
                "displayName": "Custom %",
                "subPageID": 1,
                "name": "Customfield",
                "description": null,
                "isActive": true,
                "isDeleted": false,
                "encryptedID": null,
                "sequenceNo": 21,
                "pagePath": null,
                "isCustom": true,
                "isMandatory": false,
                "dataTypeId": 4,
                "isListData": false,
                "showOnList": true,
                "isChart": false,
                "mSubFields": [],
                "createdOn": "2023-11-15T07:40:41.157",
                "createdBy": 1090,
                "modifiedOn": "2024-03-01T12:52:59.85",
                "modifiedBy": 1060
              }
            ],
            "isMandatory": false,
            "isDataType": true,
            "isDragDrop": true,
            "isListData": false,
            "showOnList": false,
            "createdOn": "2022-03-16T16:19:05.61",
            "createdBy": 1,
            "modifiedOn": "2024-03-01T12:52:59.85",
            "modifiedBy": 1060
          }
        ],
        "subPageFieldList": null,
        "createdOn": "2022-03-16T16:19:05.61",
        "createdBy": 1,
        "modifiedOn": null,
        "modifiedBy": null
      }

    ];
    pageConfigResponse = configResponse;
    if (component) {
      component.sectionContent = new MockQueryList();
    }
    pageConfigurationService.getPageConfiguration.and.returnValue(of(configResponse));
    component = fixture.componentInstance;
  })
  it('can load instance', () => {
    expect(component).toBeTruthy();
  });
  it('should allow local deactivation if form is invalid or button is disabled', () => {
    component.form = { form: { get valid() { return false; } } } as any; // Mock form
    component.isDisabledBtn = true;
    expect(component.localCanDeactivate()).toBeTrue();

    component.isDisabledBtn = false;
    expect(component.localCanDeactivate()).toBeTrue();
  });

  it('should not allow local deactivation if form is valid and button is not disabled', () => {
    component.form = { form: { get valid() { return true; } } } as any; // Mock form
    component.isDisabledBtn = false;
    expect(component.localCanDeactivate()).toBeFalse();
  });
  it('should set isPopup to false on cancel', () => {
    component.isPopup = true;
    component.OnCancel(null);
    expect(component.isPopup).toBeFalse();
  });
  it('should set isDisabledBtn to false if subPageListClone and subPageList are not equal', () => {
    component.subPageListClone = [{ id: 1 }];
    component.subPageList = [{ id: 2 }];
    component.checkAnyDataChange();
    expect(component.isDisabledBtn).toBeFalse();
  });

  it('should set isDisabledBtn to true if subPageListClone and subPageList are equal', () => {
    component.subPageListClone = [{ id: 1 }];
    component.subPageList = [{ id: 1 }];
    component.checkAnyDataChange();
    expect(component.isDisabledBtn).toBeTrue();
  });
  it('should toggle isActive and isTabExpanded on onMainCardToggleChange call', () => {
    const i = 0;
    component.subPageList = [{ isActive: false, isTabExpanded: false }];
    component.subPageListClone = [{ isActive: false, isTabExpanded: false }];
    component.onMainCardToggleChange(true, true, i);
    expect(component.subPageList[i].isActive).toBe(false);
    expect(component.subPageListClone[i].isTabExpanded).toBe(true);
  });
  it('should set isTabExpanded and check any data change on onChangeSubPageFields call', () => {
    const i = 0;
    component.subPageListClone = [{ isTabExpanded: false }];
    component.onChangeSubPageFields(null, null, i, true);
    expect(component.subPageListClone[i].isTabExpanded).toBe(true);
    expect(component.isDisabledBtn).toBe(false);
  });
  it('should toggle isChart, set isTabExpanded and check any data change on onToggleChartChange call', () => {
    const i = 0, j = 0;
    component.subPageList = [{ subPageFieldList: [{ isChart: false }] }];
    component.subPageListClone = [{ isTabExpanded: false }];
    component.onToggleChartChange(true, j, i, true);
    expect(component.subPageList[i].subPageFieldList[j].isChart).toBe(false);
    expect(component.subPageListClone[i].isTabExpanded).toBe(true);
    expect(component.isDisabledBtn).toBe(false);
  });
  it('should create a deep copy of the object', () => {
    const original = { a: 1, b: { c: 2 } };
    const copy = component.createCopy(original);
    expect(copy).toEqual(original);
    expect(copy).not.toBe(original);
    expect(copy.b).not.toBe(original.b);
  });
  it('should call getConfiguration on reset', () => {
    spyOn(component, 'getConfiguration');
    component.reset();
    expect(component.getConfiguration).toHaveBeenCalled();
  });
  it('should call getPageConfiguration and handle result', () => {
    component.getConfiguration();

    expect(pageConfigurationService.getPageConfiguration).toHaveBeenCalled();
  });
  it('should getPageConfiguration handle error', () => {
    pageConfigurationService.getPageConfiguration.and.returnValue(throwError('error'));
    component.getConfiguration();
    expect(pageConfigurationService.getPageConfiguration).toHaveBeenCalled();
    expect(component.isLoader).toBeFalse();
  });
  it('should add a custom field', () => {
    const item = {
      id: 1,
      isDataType: false,
      isTabExpanded: false,
      isListData: true,
      subPageFieldList: [{ sequenceNo: 1 }]
    };
    component.subPageList = [item];
    component.isSaveChagesOldCount = 0;
    spyOn(component, 'addTags');
    spyOn(component, 'onTabToggle');
    component.addCustomField(item);
    expect(component.isAddFieldButtonClicked).toBeTrue();
    expect(component.subPageDetailmodel.parentId).toEqual(item.id);
    expect(component.subPageDetailmodel.name).toEqual('Customfield');
    expect(component.subPageDetailmodel.isActive).toBeTrue();
    expect(component.subPageDetailmodel.isDeleted).toBeFalse();
    expect(component.subPageDetailmodel.isCustom).toBeTrue();
    expect(component.subPageDetailmodel.dataTypeId).toEqual(item.isDataType ? null : 0);
    expect(component.subPageDetailmodel.showOnList).toEqual((item.id != 1 && item.id !== 7) ? false : true);
    expect(component.subPageDetailmodel.isListData).toEqual((item.id != 1 && item.id !== 7) ? false : item.isListData);
    expect(component.subPageList[0].subPageFieldList.length).toEqual(2);
    expect(component.isSaveChagesOldCount).toEqual(1);
    expect(component.addTags).toHaveBeenCalled();
    expect(component.onTabToggle).toHaveBeenCalledWith(item);
  });
  it('should remove a custom field', () => {
    const currentItem = { id: 1 };
    const removeFieldItem = { displayName: null, sequenceNo: 1 };

    component.subPageList = [{ id: currentItem.id, subPageFieldList: [removeFieldItem] }];
    component.subPageListClone = [{ id: currentItem.id, subPageFieldList: [removeFieldItem] }];
    component.isSaveChagesOldCount = 1;

    spyOn(component, 'checkAnyDataChange');

    component.removeCustomField(currentItem, removeFieldItem);

    expect(component.subPageList[0].subPageFieldList).toEqual([]);
    expect(component.subPageListClone[0].subPageFieldList).toEqual([]);
    expect(component.isSaveChagesOldCount).toEqual(0);
    expect(component.checkAnyDataChange).toHaveBeenCalled();
  });

  it('should mark a custom field as deleted', () => {
    const currentItem = { id: 1 };
    const removeFieldItem = { displayName: 'Customfield', sequenceNo: 1 };

    component.subPageList = [{ id: currentItem.id, subPageFieldList: [removeFieldItem] }];
    component.subPageListClone = [{ id: currentItem.id, subPageFieldList: [removeFieldItem] }];
    component.isSaveChagesOldCount = 1;

    spyOn(component, 'checkAnyDataChange');

    component.removeCustomField(currentItem, removeFieldItem);

    expect(component.subPageList[0].subPageFieldList[0].isDeleted).toBeTrue();
    expect(component.subPageListClone[0].subPageFieldList[0].isDeleted).toBeTrue();
    expect(component.isSaveChagesOldCount).toEqual(0);
    expect(component.checkAnyDataChange).toHaveBeenCalled();
  });
  it('should handle keyup event', () => {
    const item = { id: 1 };
    const subPageFieldList = [{}];

    component.subPageList = [{ id: item.id, subPageFieldList, isTabExpanded: false }];
    component.subPageListClone = [{ id: item.id, isTabExpanded: true }];

    spyOn(component, 'addTags');

    component.onKeyup(item);

    expect(component.addTags).toHaveBeenCalledWith(subPageFieldList, false);
    expect(component.subPageListClone[0].isTabExpanded).toBeFalse();
  });
  it('should update isTabExpanded in subPageListClone', () => {
    const item = { id: 1 };
    const subSection = { isTabExpanded: true };

    component.subPageListClone = [{ id: item.id, isTabExpanded: false }];

    component.hotFixForRecentChangesButtonEnabledDisable(item, subSection);

    expect(component.subPageListClone[0].isTabExpanded).toBeTrue();
  });
  it('should toggle tab', () => {
    const currentItem = { id: 1, isTabExpanded: false };

    component.subPageList = [currentItem, { id: 2, isTabExpanded: true }];

    spyOn(component, 'addTags');

    component.onTabToggle(currentItem);

    expect(component.subPageList[0].isTabExpanded).toBeTrue();
    expect(component.subPageList[1].isTabExpanded).toBeFalse();
    expect(component.addTags).toHaveBeenCalledWith([], false);
  });

  it('should check if collection has item', () => {
    const a = [1, 2, 3];
    const b = 2;

    const result = component.collectionHas(a, b);

    expect(result).toBeTrue();
  });

  it('should find parent by selector', () => {
    const elm = document.createElement('div');
    const parent = document.createElement('div');
    parent.className = 'parent';
    parent.appendChild(elm);
    document.body.appendChild(parent);

    const result = component.findParentBySelector(elm, '.parent');

    expect(result).toBe(parent);

    document.body.removeChild(parent);
  });
  it('should add tags for page', () => {
    const subPageList = [
      { displayName: 'Page1', subPageFieldList: [{ displayName: 'Field1' }, { displayName: 'Field2' }] },
      { displayName: 'Page2', subPageFieldList: [{ displayName: 'Field3' }, { displayName: null }] }
    ];
    component.addTags(subPageList, true);
    expect(component.existingTags).toEqual(['page1', 'field1', 'field2', 'page2', 'field3']);
  });

  it('should add tags for subPageFieldList', () => {
    const subPageList = [{ displayName: 'Field1' }, { displayName: 'Field2' }, { displayName: null }]; // replace with your mock subPageList
    component.addTags(subPageList, false);
    expect(component.existingTags).toEqual(['field1', 'field2']);
  });
  it('should check if display name exists', () => {
    const i = 1;
    const value = 'Field1';
    component.existingTags = ['field1', 'field2'];
    component.exitsCheckDisplayName(i, value);
    expect(component.isExits).toEqual(i);
  });

  it('should reset isexits if display name does not exist', () => {
    const i = 1;
    const value = 'Field3';

    component.existingTags = ['field1', 'field2'];
    component.exitsCheckDisplayName(i, value);
    expect(component.isExits).toEqual(0);
  });

  it('should reset isexits if value is empty', () => {
    const i = 1;
    const value = '';
    component.exitsCheckDisplayName(i, value);
    expect(component.isExits).toEqual(0);
  });
  it('should save page configuration', () => {
    pageConfigurationService.pageConfigurationSaveData.and.returnValue(of({ message: 'Saved', code: 'success' }));
    component.subPageList = [
      {
        subPageFieldList: [{ isActive: true }, { isActive: false }],
        parentId: 'notESG',
        isActive: true
      }
    ];
    component.pageConfigurationPageDetails = PageConfigurationPageDetails;
    const saveDataResponse = { message: 'Saved', code: 'success' };
    pageConfigurationService.pageConfigurationSaveData.and.returnValue(of(saveDataResponse));
    component.save();
    expect(component.isPopup).toBeFalse();
    //  expect(pageConfigurationService.pageConfigurationSaveData).toHaveBeenCalledWith(component.subPageList);
    //expect(mockToastrService.success).toHaveBeenCalledWith(' Page Configuration saved successfully', '', { positionClass: 'toast-center-center' });
  });

  it('should handle error when saving page configuration', () => {
    component.subPageList = [
      {
        subPageFieldList: [{ isActive: true }, { isActive: false }],
        parentId: 'notESG',
        isActive: true
      }
    ];
    component.pageConfigurationPageDetails = PageConfigurationPageDetails;
    pageConfigurationService.pageConfigurationSaveData.and.returnValue(throwError({ status: 500 }));

    component.save();
    expect(pageConfigurationService.pageConfigurationSaveData).toHaveBeenCalledWith(component.subPageList);
  });
  it('should save and handle error response', () => {
    const response = { code: 'error', message: 'Error' };
    pageConfigurationService.pageConfigurationSaveData.and.returnValue(of(response));
    component.save();
    expect(pageConfigurationService.pageConfigurationSaveData).toHaveBeenCalled();
    expect(component.isPopup).toBeFalse();
  });

  it('should handle server error', () => {
    const error = { status: 500 };
    pageConfigurationService.pageConfigurationSaveData.and.returnValue(throwError(error));
    component.save();
    expect(pageConfigurationService.pageConfigurationSaveData).toHaveBeenCalled();
  });
  it('should handle drop event', () => {
    const event = {
      previousContainer: { data: ['item1', 'item2', 'item3'] },
      container: { data: ['item4', 'item5', 'item6'] },
      previousIndex: 1,
      currentIndex: 2
    } as CdkDragDrop<string[]>;
    spyOn(component, 'checkAnyDataChange');
    component.drop(event);
    expect(component.checkAnyDataChange).toHaveBeenCalled();
  });
  it('should transfer item when previousContainer is not the same as container', () => {
    const event = {
      previousContainer: { data: ['item1', 'item2', 'item3'] },
      container: { data: ['item4', 'item5', 'item6'] },
      previousIndex: 1,
      currentIndex: 2
    } as CdkDragDrop<string[]>;

    component.drop(event);

    expect(event.container.data).toContain('item2');
    expect(event.previousContainer.data).not.toContain('item2');
  });

  it('should move item in array when previousIndex is not the same as currentIndex', () => {
    const event = {
      previousContainer: { data: ['item1', 'item2', 'item3'] },
      container: { data: ['item2', 'item1', 'item3'] },
      previousIndex: 0,
      currentIndex: 2
    } as CdkDragDrop<string[]>;

    component.drop(event);

    expect(event.container.data[2]).toBe('item1');
    expect(event.container.data[0]).toBe('item2');
  });

  it('should update sequenceNo when currentID and previousID are not zero', () => {
    const event = {
      previousContainer: { data: [{ id: 1, subPageID: 1, sequenceNo: 1 }, { id: 2, subPageID: 1, sequenceNo: 2 }] },
      container: { data: [{ id: 1, subPageID: 1, sequenceNo: 1 }, { id: 2, subPageID: 1, sequenceNo: 2 }] },
      previousIndex: 0,
      currentIndex: 1
    } as CdkDragDrop<any[]>;

    component.subPageList = [{ id: 1, subPageFieldList: [{ id: 1, sequenceNo: 1 }, { id: 2, sequenceNo: 2 }] }];
    component.subPageListClone = [{ id: 1, subPageFieldList: [{ id: 1, sequenceNo: 1 }, { id: 2, sequenceNo: 2 }] }];

    component.drop(event);

    expect(component.subPageList[0].subPageFieldList[0].sequenceNo).toBe(1);
    expect(component.subPageList[0].subPageFieldList[1].sequenceNo).toBe(2);
  });
  it('should refresh sub page detail list', () => {
    component.pageList = pageConfigResponse;
    component.selectedPageItem = { id: 1, name: 'Portfolio Company' };
    spyOn(component, 'checkListPageToggle');
    component.refreshSubPageDetailList();
    expect(component.subPageList.length).toBe(1);
    expect(component.subPageListClone.length).toBe(1);
    expect(component.isSaveChagesOldCount).toBe(0);
    expect(component.checkListPageToggle).toHaveBeenCalled();
  });

  it('should not refresh sub page detail list when pageList is empty', () => {
    component.pageList = [];
    component.selectedPageItem = { id: 1, name: 'Funds' };

    component.refreshSubPageDetailList();

    expect(component.subPageList).toEqual([]);
    expect(component.subPageListClone).toEqual([]);
    expect(component.isSaveChagesOldCount).toEqual(0);
  });

  it('should set disablePageList to false when selectedPageItem name is not Funds or Portfolio Company', () => {
    component.pageList = [
      { id: 1, subPageDetailList: [{ id: 1, name: 'SubPage1' }, { id: 2, name: 'SubPage2' }] }
    ];
    component.selectedPageItem = { id: 1, name: 'Other' };

    component.refreshSubPageDetailList();

    expect(component.disablePageList).toBe(false);
  });
  it('should have initial properties set', () => {
    expect(component.pageList).toEqual([]);
    expect(component.pageListClone).toEqual([]);
    expect(component.subPageList).toEqual([]);
    expect(component.subPageListClone).toEqual([]);
    expect(component.selectedPageItem).toEqual({});
    expect(component.pageDropdownOptions).toEqual([]);
    expect(component.isAddFieldButtonClicked).toBe(false);
    expect(component.subPageDetailmodel).toBeUndefined();
    expect(component.isLoader).toBe(false);
    expect(component.lastSelectedPageItem).toEqual({});
    expect(component.filterDelete).toEqual({ isDeleted: false });
    expect(component.existingTags).toEqual([]);
    expect(component.isPopup).toBe(false);
    expect(component.modalTitle).toBe("Confirmation");
    expect(component.isSaveChagesOldCount).toBe(0);
    expect(component.onChagelastSelectedPageItem).toEqual({});
    expect(component.isDisabledBtn).toBe(true);
    expect(component.trackRecordDataTypes).toEqual([]);
    expect(component.pcDataTypes).toEqual([]);
    expect(component.pageConfigurationsDatalist).toEqual([]);
    expect(component.temp).toBeUndefined();
    expect(component.disablePageList).toBe(false);
    expect(component.currentModelRef).toBeUndefined();
    expect(component.status).toBe(false);
    expect(component.kpiSubPageId).toBe(2);
    expect(component.financial).toBe(3);
    expect(component.pageConfigurationPageDetails).toBe(PageConfigurationPageDetails);
    expect(component.disabled).toBeUndefined();
    expect(component.inputSwitchElement).toBeUndefined();
    expect(component.onChange).toBeUndefined();
  });
  it('should toggle isHighLight and set isTabExpanded on onHighLightChange call', () => {
    const i = 0, j = 0;
    component.subPageList = [{ subPageFieldList: [{ isHighLight: false }] }];
    component.subPageListClone = [{ isTabExpanded: false }];
    component.onHighLightChange(true, j, i, true);
    expect(component.subPageList[i].subPageFieldList[j].isHighLight).toBe(true);
    expect(component.subPageListClone[i].isTabExpanded).toBe(true);
    expect(component.isDisabledBtn).toBe(false);
  });
});
describe('PageSettingsModule', () => {
  let pageSettingsModule: PageSettingsModule;

  beforeEach(() => {
    pageSettingsModule = new PageSettingsModule();
  });

  it('should create an instance pageSettingsModule', () => {
    expect(pageSettingsModule).toBeTruthy();
  });
});
