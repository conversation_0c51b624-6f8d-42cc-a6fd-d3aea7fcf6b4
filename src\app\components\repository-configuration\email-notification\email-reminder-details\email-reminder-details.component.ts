import { Component, Input, OnInit, OnChanges } from '@angular/core';
import { UpdateEmailRemainderDto, EmailRecipientsDto, EmailRemainderDetailsDto, SkipReminderCycleDto } from '../../model/config-model';
import { RepositoryConfigService } from '../../../../services/repository.config.service';
import { ToastrService } from 'ngx-toastr';
import * as moment from 'moment';

@Component({
    selector: 'app-email-reminder-details',
    templateUrl: './email-reminder-details.component.html',
    styleUrls: ['./email-reminder-details.component.scss']
})
export class EmailReminderDetailsComponent implements OnInit, OnChanges {
    @Input() reminderDetails: EmailRemainderDetailsDto | null = null;
    @Input() reminderId: string = '';
    @Input() isActive: boolean = true;

    public toRecipients: EmailRecipientsDto[] = [];
    public ccRecipients: EmailRecipientsDto[] = [];
    public portfolioCompanies: any[] = [];
    public documentTypes: any[] = [];
    public subject: string = '';
    public emailBody: string = '';
    public totalReminders: number = 0;
    public remindersSent: number = 0;
    public lastReminderDate: string = '';
    public nextReminderDate: string = '';
    public totalErrorandSkips: number = 0;
    public isSkipCycleEnabled: boolean = false;

    constructor(
        private repositoryConfigService: RepositoryConfigService,
        private toastrService: ToastrService
    ) { }

    ngOnInit(): void {
        if (this.reminderDetails) {
            this.loadReminderData();
        }
    }

    ngOnChanges(): void {
        if (this.reminderDetails) {
            this.loadReminderData();
        }
    }

    private loadReminderData(): void {
        if (!this.reminderDetails) return;

        this.toRecipients = this.reminderDetails.toRecipients || [];
        this.ccRecipients = this.reminderDetails.ccRecipients || [];       
        this.subject = this.reminderDetails.subject || '';
        this.emailBody = this.reminderDetails.messageBody || '';      
        this.totalReminders = this.reminderDetails.totalNumberOfReminders || 0;
        this.remindersSent = this.reminderDetails.totalRemindersSent || 0;
        this.totalErrorandSkips = this.reminderDetails.totalErrorAndSkippedReminders || 0;
        this.lastReminderDate = this.reminderDetails.lastReminderSentDate ? moment.utc(this.reminderDetails.lastReminderSentDate).local().format('DD MMM YYYY hh:mm A') : 'NA';
        this.nextReminderDate = this.reminderDetails.nextReminderScheduledDate ? moment.utc(this.reminderDetails.nextReminderScheduledDate).local().format('DD MMM YYYY') : 'NA';
    }    

    // Get display text for recipient (email or name for groups)
    public getRecipientDisplayText(recipient: any): string {
        return recipient.emailAddress || recipient.name || '';
    }

    // Check if recipient is a group (no email)
    public isGroupRecipient(recipient: any): boolean {
        return !recipient.name && !!recipient.emailAddress;
    }

    // Handle skip cycle switch change
    public onSkipCycleChange(isChecked: boolean): void {
        if (isChecked) {
            // When switch is turned ON (skip cycle)
            this.skipReminderCycle();
        }
        // Note: When switch is turned OFF, we don't need to do anything
        // as the user is just disabling the skip option
    }

    // Skip the current reminder cycle
    private skipReminderCycle(): void {
        if (!this.reminderId) {
            this.toastrService.error('Reminder ID is required to skip cycle','', {
                    positionClass: 'toast-center-center'
                });
            this.isSkipCycleEnabled = false; // Reset switch state
            return;
        }

        const skipData: SkipReminderCycleDto = {
            reminderId: this.reminderId
        };

        this.repositoryConfigService.skipReminderCycle(skipData).subscribe({
            next: (response) => {
                this.toastrService.success(response.message || 'Reminder cycle skipped successfully', '', {
                    positionClass: 'toast-center-center'
                });
                // Keep the switch in the ON state to indicate cycle has been skipped
                this.isSkipCycleEnabled = true;
                this.updateNextCycleRemainderStats(this.reminderId);
            },
            error: (error) => {
                this.toastrService.error(error.message || 'Failed to skip reminder cycle','', {
                    positionClass: 'toast-center-center'
                });
                // Reset switch state on error
                this.isSkipCycleEnabled = false;
            }
        });
    }

    private updateNextCycleRemainderStats(reminderId : string): void {
        this.repositoryConfigService.getEmailReminderDetails(reminderId).subscribe({
            next: (details : EmailRemainderDetailsDto) => {
                this.totalReminders = details.totalNumberOfReminders;
                this.remindersSent = details.totalRemindersSent;
                this.totalErrorandSkips = details.totalErrorAndSkippedReminders;
                this.lastReminderDate = details.lastReminderSentDate ? moment.utc(details.lastReminderSentDate).local().format('DD MMM YYYY hh:mm A') : 'NA';
                this.nextReminderDate = details.nextReminderScheduledDate ? moment.utc(details.nextReminderScheduledDate).local().format('DD MMM YYYY') : 'NA';
            },
            error: (error) => {
                this.toastrService.error('Failed to load next cycle reminder details','', {
                    positionClass: 'toast-center-center'
                });
            }
        });
    }
        

}


