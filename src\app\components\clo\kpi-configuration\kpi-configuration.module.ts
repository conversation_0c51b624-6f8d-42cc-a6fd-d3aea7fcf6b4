import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { KpiConfigurationComponent } from './kpi-configuration.component';
import { RouterModule } from '@angular/router';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { MatMenuModule } from '@angular/material/menu';
import { SharedCloModule } from '../shared-clo.module';



@NgModule({
  declarations: [
    KpiConfigurationComponent
  ],
  imports: [
    CommonModule,
    KendoModule,
    MatMenuModule,
    RouterModule.forChild([
          { path: '', component: KpiConfigurationComponent}
      ]),
           SharedCloModule
  ]
})
export class KpiConfigurationModule { }
