import { Component, OnInit, ViewChild, ChangeDetectorRef,<PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { DashboardConfigurationConstants } from 'src/app/common/constants';
import { DashboardTrackerComponent } from '../dashboard-tracker.component';
import { FeaturesEnum } from 'src/app/services/permission.service';
import { CommonSubFeaturePermissionService } from 'src/app/services/subPermission.service';

@Component({
  selector: 'app-dashboard-configuration',
  templateUrl: './dashboard-configuration.component.html',
  styleUrls: ['./dashboard-configuration.component.scss']
})
export class DashboardConfigurationComponent implements OnInit, OnDestroy {
  @ViewChild('dashboardTracker') dashboardTrackerComponent!: DashboardTrackerComponent;

  // Expose constants to template
  DashboardConfigurationConstants = DashboardConfigurationConstants;

  isDashboardConfigurationTab: boolean = true;

  tabList: ITab[] = [
    { name: DashboardConfigurationConstants.DashboardConfigurationTab, active: true },
    { name: DashboardConfigurationConstants.ManageTrackerFieldsTab, active: false },
    { name: DashboardConfigurationConstants.DeletedColumnTab, active: false },
    { name: DashboardConfigurationConstants.StatusFilterTab, active: false }
  ];
  selectedTab: ITab = this.tabList[0];
  pendingChangesCount: number = 0;
  canDeleteColumnRecord: any;
  private dashboardPermissionSub: any;
    constructor(private cdr: ChangeDetectorRef, private readonly router: Router, private readonly subPermissionService: CommonSubFeaturePermissionService,) {}

    ngOnInit(): void {    
        this.getDashboardTrackerPermissions();    

  }

  ngOnDestroy(): void {
    if (this.dashboardPermissionSub) {
      this.dashboardPermissionSub.unsubscribe();
    }
  }
  
  onTabClick(tab: ITab) {
    this.tabList.forEach(t => t.active = false);
    tab.active = true;
    this.selectedTab = tab;
  }

  navigateToDashboardConfig() {
    this.tabList.forEach(t => t.active = false);
    this.tabList[1].active = true;
    this.selectedTab = this.tabList[1];
  }

  // Handle cell changes from dashboard tracker
  onCellChangesUpdated(count: number): void {
    this.pendingChangesCount = count;
    this.cdr.detectChanges();
  }

  // Check if save button should be disabled
  isSaveDisabled(): boolean {
    if (this.pendingChangesCount === 0) {
      return true;
    }

    // Return true if dashboardTrackerComponent is not available
    if (!this.dashboardTrackerComponent) {
      return true;
    }

    // Also check if there are validation errors
    if (this.dashboardTrackerComponent.hasErrors()) {
      return true;
    }

    return false;
  }

  // Handle save button click
  onSaveClick(): void {
    if (this.dashboardTrackerComponent && this.dashboardTrackerComponent.getPendingChangesCount() > 0) {
      this.dashboardTrackerComponent.saveCellValues();
    }
  }

  // Handle cancel button click
  onCancelClick(): void {
    if (this.dashboardTrackerComponent) {
      this.dashboardTrackerComponent.clearPendingChanges();
    }
    this.router.navigate(["/home"], { queryParams: { tab: 'dashboard-tracker' } });
  }
  getDashboardTrackerPermissions() {
    this.dashboardPermissionSub = this.subPermissionService
      .getCommonFeatureAccessPermissions(FeaturesEnum.DashboardTracker)
      .subscribe({
        next: (result) => {
          if (result.length > 0) {
            this.canDeleteColumnRecord = result?.map((x) => x.canEdit).includes(true);
            if (!this.canDeleteColumnRecord) {
              this.router.navigate(["/401"]);
            }
          } 
        },
      });
  }
}


