<div class="portfolio-detail-component" (resized)="onResized($event)">
    <div class="row mr-0 ml-0">
        <div class="col-lg-12 col-sm-12 pl-0 pr-0">
            <div class="chart-area section1-height chart-section-height">
                <div class="row mr-0 ml-0" *ngIf="companyKPIList?.length>0">
                    <div class="col-12 col-lg-12 col-sm-12 col-md-12 col-xl-12 p-3">
                        <kendo-dropdowntree
                        #cpmpanykpiiddropdown
                        kendoDropDownTreeExpandable
                        [data]="filteredKpiItems"
                        textField="itemName"
                        valueField="kpiid"
                        [hasChildren]="hasChildren"
                        [fetchChildren]="fetchChildren"
                        (valueChange)="OnCompanyKPIChange($event)"
                        [clearButton]="false"
                        [value]="selectedCompanyKPI"
                        [filterable]="{mode: 'contains'}"
                        (filterChange)="onFilterChange($event)"
                        class="k-dropdown-width-500 k-custom-solid-dropdown k-dropdown-height-32"
                        (close)="onDropDownClose($event)"
                        >
                        <ng-template kendoDropDownTreeValueTemplate let-dataItem>
                            <div class="selected-item-wrapper">
                                <div class="line-item selected-kpi">
                                    <img *ngIf="dataItem?.isDefault" src="assets/dist/images/FaStar.svg" >
                                    <img *ngIf="dataItem?.isFavourite" src="assets/dist/images/FIBookmark.svg" >
                                    <span>{{dataItem?.itemName}}</span>
                                </div>
                            </div>
                        </ng-template>
                        <ng-template kendoDropDownTreeNodeTemplate let-dataItem>
                            <div>
                                <div class="line-item" *ngIf="dataItem.isHeader || dataItem.kpiInfo === 'Text'">
                                    <span  title="{{dataItem.itemName}}"
                                    [ngClass]="{'child-lineItem-width': dataItem.parentId > 0,'lineItem-width': !dataItem.parentId,'ml-kpi': !dataItem.isDefault && !dataItem.isFavourite && defaultOrFavKpiExists}" > 
                                        {{ truncateTabName(dataItem.itemName,35) }}
                                    </span> 
                                </div>
                                <div class="line-item" *ngIf="!dataItem.isHeader && dataItem.kpiInfo !== 'Text'">
                                    <img class="selected-preference" src="assets/dist/images/FaStar.svg" *ngIf="dataItem.isDefault"/> 
                                    <img class="selected-preference" src="assets/dist/images/FIBookmark.svg" *ngIf="dataItem.isFavourite"/> 
                                    <span  title="{{dataItem.itemName}}"
                                    [ngClass]="{'child-lineItem-width': dataItem.parentId > 0,'lineItem-width': !dataItem.parentId,'ml-kpi': !dataItem.isDefault && !dataItem.isFavourite && defaultOrFavKpiExists}" > 
                                        {{ truncateTabName(dataItem.itemName,35) }}
                                    </span>                                         
                                    <img #anchor class="Threedots" [ngClass]="dataItem.parentId > 0 ? 'child-kpi' : 'parent-kpi'"
                                    src="assets/dist/images/ThreeDotsVertical.svg" 
                                    (click)="handleDotsClick($event,dataItem)" /> 
                                    <kendo-popup class="preference-popup"  
                                    [ngClass]="" [anchor]="anchor"  
                                    *ngIf="selectedItem === dataItem && showPopup">
                                        <div *ngIf="!dataItem.isDefault && !dataItem.isFavourite"  
                                        class="preference-lineitem" (click)="onSetDefaultKpi($event,dataItem)">
                                          <img class="pr-1" src="assets/dist/images/Star.svg" /> 
                                          <span> Mark as default</span>
                                        </div>
                                        <div *ngIf="!dataItem.isDefault && !dataItem.isFavourite" class="preference-lineitem"  (click)="onSetFavKpi($event,dataItem)">
                                          <img class="pr-1" src="assets/dist/images/Bookmark.svg" /> 
                                          <span>Mark as favourite</span>
                                        </div>
                                        <div *ngIf="dataItem.isDefault" class="preference-lineitem" (click)="onUnSetDefaultKpi($event,dataItem)">
                                            <img class="pr-1" src="assets/dist/images/FaStar.svg" /> 
                                            <span> Remove as default</span>
                                        </div>
                                        <div *ngIf="dataItem.isFavourite" class="preference-lineitem"  (click)="onUnSetFavKpi($event,dataItem)">
                                            <img class="pr-1" src="assets/dist/images/FIBookmark.svg" /> 
                                            <span>Remove as favourite</span>
                                        </div>
                                    </kendo-popup>
                                </div> 
                            </div>
                        </ng-template>
                    </kendo-dropdowntree>
                    </div>
                    <div class="col-8"></div>
                </div>
                <div class="chart-bg">
                    <div>
                        <div class="row mr-0 ml-0">
                            <div class="col-sm-12 pl-0 pr-0">
                                <div class="text-info mt-3">
                                    <app-empty-state *ngIf="isNoData" [isGraphImage]="true"></app-empty-state>
                                </div>
                                <br>
                            </div>
                        </div>
                    </div>
                    <!----Chart space---->
                    <div class="row mr-0 ml-0 pt-3" *ngIf="chartData.length>0">
                        <div class="col-sm-12 pl-0 pr-0">
                            <app-lineBar-chart [isDisplay]="width" [data]="chartData" [xField]="xField" 
                                [yBarFields]="yBarFields" [yLineFieldText]="selectedCompanyKPI['kpiInfo']!= '%' && selectedCompanyKPI['kpiInfo']!= 'x'?'% Change of Value':''" [isScrollBar]="true" [yLineFields]="this.selectedCompanyKPI['kpiInfo']!= '%' && this.selectedCompanyKPI['kpiInfo']!= 'x'?yLineFields:[]" [unit]="companyKpiModuleCurrency" [barColors]="yShades">
                            </app-lineBar-chart>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
