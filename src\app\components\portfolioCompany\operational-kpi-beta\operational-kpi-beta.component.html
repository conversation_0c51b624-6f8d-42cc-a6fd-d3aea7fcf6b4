<div class="row mr-0 ml-0" id="operational-kpi">
    <div class="col-lg-12 col-xl-12 col-12 col-sm-12 col-md-12 col-xl-12 pl-0 pr-0 backgroundColor">
        <div class="panel panel-default border-0 pt-0 tab-bg custom-panel">
            <div class="pull-right headerSize">
                <div class="d-inline  QMY_Container" id="operational-kpi-period-options">
                    <div class="d-inline custom-padding">
                        <div class="d-inline QMY_Text MStyle QMYStyle" *ngFor="let opt of filterOptions"
                            (click)="onChangePeriodOption(opt)" [ngClass]="opt.key ?'activeQMY':''" id="operational-kpi-{{opt.field}}">
                            {{opt.field}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-title custom-tabs">
                <div class="float-left">
                    <div class="pl-3">
                        <div class="nep-tabs nep-tabs-line">
                            <div class="nep-tabs-header">
                                <div class="nep-tabs-header-tabs">
                                    <div class="nep-tabs-inner">
                                        <div class="nep-tabs-scroll nep-tab-alignment-subtab financial-section" id="operational-kpi-value-types">
                                            <div class="nep-tabs-tab" *ngFor="let tab of tabValueTypeList"
                                                (click)="selectValueTab(tab)" [class.nep-tabs-active]="tab.active"
                                                [ngStyle]="{'padding': '0px !important'}" id="{{tab.name == 'IC' ? '' : tab.name}}">
                                                {{tab.name == 'IC' ? '' : tab.name}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mr-0 ml-0 topBorder">
    <div class="col-lg-12 col-12 col-sm-12 col-md-12 col-xl-12 pl-0 pr-3 pl-3" id="operational-kpi-graph-conatiner">
        <app-master-kpi-graph-beta id="operational-kpi-graph" [searchFilter]="operationalKpiSearchFilter" [isValueUpdated]="isValueUpdated" [modelList]="modelList" [typeField]="defaultType" *ngIf="pageConfigResponse?.hasChart">
        </app-master-kpi-graph-beta>
    </div>
</div>
<div class="border-top filter-bg border-bottom" [ngClass]="tableResult != null && tableResult.length == 0 ?'pc-border-bottom':''">
    <div class="row mr-0 ml-0">
        <div class=" col-lg-12 col-md-12 col-sm-12 pl-0 pr-0">
                <div [ngClass]="isBristol ? 'd-none':'d-block'" class="allvalues-kpis" id="operational-kpi-allvalues">
                    <span id="operational-kpi-default-value">Default: {{modelList?.reportingCurrencyDetail?.currencyCode}} ({{operationalKpiValueUnit?.unitType}})</span> 
                    <span id="operational-kpi-converted-value" *ngIf="kpiCurrencyFilterModel?.currencyCode!=null && kpiCurrencyFilterModel?.isApply"> | Converted: {{kpiCurrencyFilterModel?.currencyCode}} ({{operationalKpiValueUnit?.unitType}})</span> 
                    <span id="operational-kpi-spotrate-value" *ngIf="kpiCurrencyFilterModel?.spotRate!=null && kpiCurrencyFilterModel?.isSpotRate && kpiCurrencyFilterModel?.isApply"> | Spot Rate: {{kpiCurrencyFilterModel?.currencyCode}} {{kpiCurrencyFilterModel?.spotRate}}</span>
                </div>
            <div class="pull-right headerfontsize">
                <div class="d-inline-block search input-with-icon">
                    <span class="fa fa-search fasearchicon search-icon"></span>
                    <input #gb pInputText [appApplyFilter]="{ data: tableResultClone, columns: tableColumns,IsFreezeColumn:true,freezeColumns:'KPI'}"
                    (filtered)="tableResult = $event" type="text"
                        class="search-text-company k-pl" placeholder="Search" [(ngModel)]="globalFilter" id="operational-kpi-search">
                </div>
                <div class="d-inline-block pr-1"
                    [hideIfUserUnAuthorized]='{subFeatureId:subFeature.OperationalKPIs,action:actions[actions.canEdit],id:id}'>
                    <div class="d-inline-block table-pref">Logs</div>
                    <div class="d-inline-block pr-2 pl-1" [title]="auditLogTitle">
                        <kendo-switch id="operational-kpi-log" size="small" [(ngModel)]="ErrorNotation" (valueChange)="handleChange($event)" [onLabel]="' '" [offLabel]="' '"></kendo-switch>
                    </div>
                </div>
                <div class="d-inline textsplit"
                    [hideIfUserUnAuthorized]='{subFeatureId:subFeature.OperationalKPIs,action:actions[actions.canEdit],id:id}'>
                </div>
                <div class="d-inline-block cloud_download">
                    <div class="d-inline-block pr-2"
                        [hideIfUserUnAuthorized]='{subFeatureId:subFeature.OperationalKPIs,action:actions[actions.canExport],id:id}'
                        *ngIf="tableResult != null && tableResult.length > 0">
                        <span title="Export KPI (Excel file)">
                            <a (click)="exportOperationalKpiValues()" id="op-kpi-download">
                                <img src="assets/dist/images/Cloud-download.svg" class="cursor-filter" alt="" id="operational-kpi-download"/>
                            </a>
                        </span>
                        <span
                            class="excel-load" *ngIf="exportOperationalKPILoading">
                            <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                        </span></div>
                    <div class="d-inline textsplit"
                        [hideIfUserUnAuthorized]='{subFeatureId:subFeature.OperationalKPIs,action:actions[actions.canExport],id:id}'
                        *ngIf="tableResult != null && tableResult.length > 0"></div>

                    <div class="d-inline-block pl-2 pr-1"><img id="dropdownMenuButton" [matMenuTriggerFor]="menu"
                            src="assets/dist/images/ConfigurationWhite.svg" class="cursor-filter" alt=""
                            #menuTrigger="matMenuTrigger" /> 
                            <span id="dropdownMenuButton-op-kpi" [matMenuTriggerFor]="menu" #filterMenuTrigger="matMenuTrigger" class="api-spot-rate-circle Caption-R" title="Spot Rate applied" *ngIf="kpiCurrencyFilterModel != null && kpiCurrencyFilterModel?.spotRate!=null && kpiCurrencyFilterModel?.isSpotRate && kpiCurrencyFilterModel?.isApply">S</span>
                        </div>
                </div>
            </div>

        </div>
    </div>
</div>
<div class="portfolio-company-table master-kpi-table">
    <div class="align-items-start" id="operational-kpi-table">
        <kendo-grid id="kpi-grid" class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-cab-table-grid" 
            [kendoGridBinding]="tableResult" scrollable="virtual" [rowHeight]="44" [resizable]="true">
            <ng-container *ngIf="tableResult.length > 0">
                <kendo-grid-column [sticky]="true" [minResizableWidth]="200" [maxResizableWidth]="800"  [width]="300" 
                *ngFor="let col of tableFrozenColumns;" [field]="col.field">
                    <ng-template kendoGridHeaderTemplate>
                        <div class="header-icon-wrapper wd-100 header-left-padding" >
                          <span class="TextTruncate S-M">
                           {{col.header}}
                          </span>
                        </div>
                      </ng-template>
                    <ng-template kendoGridCellTemplate let-rowData>
                        <div class="content header-left-padding" >
                            <span *ngIf="col.header =='KPI'" title={{rowData[col.field]}}
                            [ngClass]="[(rowData.IsHeader||rowData.IsBoldKPI) ? 'showToolTip TextTruncate bold-text' :'showToolTip TextTruncate',rowData.IsHeader ? 'headerKpi bold-text' : rowData['IsBoldKPI'] ? 'bold-text': '',((rowData.ParentId !==0||rowData.ParentId ==0)&&!rowData.IsHeader)?'pl-3':'']">
                            <span *ngIf="rowData.ParentId !== 0">- </span>{{rowData[col.field]}}
                            <span *ngIf="rowData['KPI Info'] =='#'">{{'('+rowData['KPI Info'] +')'}}</span>
                        </span>
                        </div>
                    </ng-template>
                </kendo-grid-column>
            </ng-container>
            <ng-container *ngIf="tableResult.length > 0">
                <kendo-grid-column [minResizableWidth]="200" *ngFor="let col of tableColumns; index as i" [maxResizableWidth]="800"
                [width]="200" title="{{col.header}}">
                <ng-template kendoGridHeaderTemplate>
                    <div class="header-icon-wrapper wd-100">
                      <span class="TextTruncate S-M">{{col.header}}</span>
                    </div>
                  </ng-template>
                <ng-template kendoGridCellTemplate let-rowData> 
                    <div tabindex="0" class="prtcmny-det-o cell-padding" 
                    [attr.title]="col.header !=='KPI' && ErrorNotation ? 'Click to view this cell logs' : ''"                                           
                    [class.table-data-right]="col.field !='KPI'" (click)="onAuditLog(rowData,col) " (dblclick)="onEditInit(rowData,col)"
                    [ngClass]="[(isValueConverted && !rowData.IsHeader && rowData['KPI Info'] === '$') ? 'kpi-set-calc-bgcolor' : '']" id="operational-kpi-value">
                    <div class="content">
                        <div *ngIf="col.header !='KPI'" [ngClass]="rowData.IsBoldKPI ? 'bold-text': ''"
                            class="showToolTip TextTruncate">
                            <input autofocus
                            *ngIf="rowData[col.field + ' editable']  && rowData['KPI Info'] !='Text' && !rowData.IsHeader"
                            class="InputText companyText " pattern="/^-?\d+\.?\d*$/ "
                            (keypress)="validateMaxLength($event)" type="number" 
                            [(ngModel)]="rowData[col.field] " required
                            (blur)="onColumnEditComplete(i, col, rowData,$event) " (keyup)="validateNumber($event,rowData['KPI Info']);$event.preventDefault()"
                            (keyup.enter)="onColumnEdit($event) ">
                        <input autofocus
                            *ngIf="rowData[col.field + ' editable'] && rowData['KPI Info'] =='Text' && !rowData.IsHeader"
                            class="InputText companyText" type="text"
                            [value]="rowData[col.field]" [(ngModel)]="rowData[col.field]" required
                            (blur)="onColumnEditComplete(i, col, rowData)" (keyup.enter)="onColumnEdit($event)">
                            <div [title]="rowData['KPI Info']=='Text'? rowData[col.field]:''"
                                *ngIf="rowData[col.field]!= undefined && rowData[col.field]!=null && rowData[col.field]!=''&& !rowData.IsHeader;else empty_Text">
                                <container-element [ngSwitch]="rowData['KPI Info']">
                                    <container *ngSwitchCase="'#'">
                                        <span [title]="rowData[col.field]" *ngIf="!rowData[col.field + ' editable']"
                                            [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number:'1.0-0' | minusSignToBrackets) : rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchCase="'Text'">
                                        <span class="float-left left-align TextTruncate drop-above"  [title]="rowData[col.field]" *ngIf="!rowData[col.field + ' editable']"
                                            [innerHtml]="rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchCase="'%'"> 
                                        <span [title]="rowData[col.field]" *ngIf="!rowData[col.field + ' editable']"
                                            [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.percentDecimal | minusToBracketsWithPercentage: '%'): rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchCase="'x'">
                                        <span [title]="rowData[col.field]"
                                            *ngIf="rowData[col.field] != 'NA' && rowData[col.field] != '' && !rowData[col.field + ' editable']"
                                            [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.multipleDecimal)+'x': rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchCase="'$'">
                                        <span [title]="rowData[col.field]"
                                            *ngIf="rowData[col.field] != 'NA' && rowData[col.field] != '' && !rowData[col.field + ' editable']"
                                            [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: NumberDecimalConst.currencyDecimal | minusSignToBrackets: '') : rowData[col.field]"></span>
                                    </container>
                                    <container *ngSwitchDefault>
                                    </container>
                                </container-element>
                            </div>
                            <ng-template #empty_Text class="detail-sec">
                                <span [ngClass]="rowData['KPI Info']=='Text'? 'float-left':'float-right'" *ngIf="!rowData[col.field + ' editable'] && !rowData.IsHeader">NA</span>
                                <div class="cell-padding" *ngIf="rowData.IsHeader">
                                    <div></div>
                                </div>
                            </ng-template>
                        </div>
                    </div>
                </div>
                </ng-template>
            </kendo-grid-column> 
            </ng-container>
            <ng-template kendoGridNoRecordsTemplate>
                <app-empty-state class="finacials-beta-empty-state" [imageHeight]="'41vh'" [isGraphImage]="false"
                *ngIf="tableResult.length == 0"></app-empty-state>
            </ng-template>
            </kendo-grid>
    </div>
    <div>
        <app-loader-component *ngIf="isLoader"></app-loader-component>
    </div>
    <mat-menu #menu="matMenu" [hasBackdrop]="true" class="fixed-menu">
        <app-kpitablefilters [tabname]="'Operational'" [currencyCode]="modelList?.reportingCurrencyDetail?.currencyCode" isDefaultMillion="true"
            (Kpifilter)="kpiTable_GlobalFilter($event)" [typeField]="defaultType" id="operational-kpi-table-filters"></app-kpitablefilters>
    </mat-menu>
</div>

<app-foot-note [moduleId]="modelList.moduleId" [companyId]="modelList?.portfolioCompanyID"
    *ngIf="tableResult.length > 0" class="comm-footnote custom-quill-editor"></app-foot-note>
<div *ngIf="infoUpdate">
    <confirm-modal IsInfoPopup="true" customwidth="400px" modalTitle="Change Values in Selection" primaryButtonName="OK"
        (primaryButtonEvent)="CloseInfo()">
        <div>
            <div class="oprtnkpi-lh">
                To edit cell data please select numbers in <b><i>'Absolute'</i></b> under <b><i>Values in</i></b>
                dropdown
            </div>
        </div>
    </confirm-modal>
</div>

<div *ngIf="isUploadPopupVisible" class="nep-modal nep-modal-show kpi-add-edit-modal nep-kpi-bg">
    <div class="nep-modal-mask"></div>
    <div class="nep-card nep-card-shadow nep-modal-panel nep-modal-default nep-all-upload-file">
        <app-kpi-cell-edit id="operational-kpi-cell-edit" [kpiType]="pageConfigResponse.kpiType" [dataRow]="dataRow" [tableColumns]="dataColumns" [moduleCompanyModel]="uniqueModuleCompany" (cancelButtonEvent)="cancelButtonEvent()" (confirmButtonEvent)="onSubmitButtonEvent($event)"></app-kpi-cell-edit>
      </div>
</div>
