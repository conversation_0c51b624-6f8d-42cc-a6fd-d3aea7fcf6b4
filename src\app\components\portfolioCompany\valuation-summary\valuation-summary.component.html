<div id="valuation-Summary-table" *ngIf="isValuationSummary" class="row valuation-Summary-section pt-3 mr-0 ml-0">
    <div class="col-12 pl-0">
        <div class="cap-header pt-1 pb-2 TextTruncate Heading2-M" title="{{vsTitle}}">{{vsTitle}}</div>
    </div>
    <div class="col-12 valuation-Summary-section-body pl-0 pr-0">
        <div class="panel panel-default tab-bg">
            <div class="panel-heading pl-0 pr-0 border-0 pb-0">
                <div class="panel-title custom-tabs custom-mat-tab cab-table-tab">
                    <nav mat-tab-nav-bar [tabPanel]="tabPanel">
                        <a mat-tab-link [disableRipple]="true" *ngFor="let tab of tabList" [active]="tab.isActive"
                            (click)="changeTabType(tab)" id="valuation-summary-tab-{{tab.id}}" class="TextTruncate">
                            {{tab.displayName}}
                        </a>
                    </nav>
                    <div class="float-right filter-icon">
                        <div class="allvalues-valuation pr-2 S-R">
                            <span>
                                All values in: {{reportingCurrencyCode}} (Thousands)
                            </span>
                        </div>
                        <div class="d-inline pr-0 headerfontsize">
                            <img id="dropdownMenuButton" [matMenuTriggerFor]="menu" #filterMenuTrigger="matMenuTrigger"
                                src="assets/dist/images/ConfigurationWhite.svg" class="cursor-filter" alt="" />
                        </div>
                    </div>
                    <mat-tab-nav-panel #tabPanel>
                        <div class="row mr-0 ml-0">
                            <div class="col-12 pr-0 pl-0" *ngIf="!selectedTab?.isChart">
                                <div class="row mr-0 ml-0 no-data-container" *ngIf="true">
                                    <ng-container *ngTemplateOutlet="noDataTemplate; context: { message: 'No data found' ,icon:'no-content-lp-report' }"></ng-container>
                                </div>
                            </div>
                            <div *ngIf="valuationSummaryModel?.length == 0" class="col-12 pr-0 tab-middle">
                                <span class="S-M">KPIs</span>
                            </div>
                            <div class="col-12 pr-0 pl-0">
                                <div class="shadow-sm chart-area w-100" [ngClass]="valuationSummaryModel?.length > 0 ? 'border-top' : ''">
                                    <div class="tbl-sec">
                                        <kendo-grid id="kpi-grid" class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none valuation-summary-table" 
                                            [kendoGridBinding]="valuationSummaryModel" scrollable="virtual" [rowHeight]="44" [resizable]="true">
                                            <ng-container *ngIf="valuationSummaryModel.length > 0">
                                                <kendo-grid-column [sticky]="true" [minResizableWidth]="200" [maxResizableWidth]="800" [width]="300" 
                                                    *ngFor="let col of tableFrozenColumns;" [field]="col.field">
                                                    <ng-template kendoGridHeaderTemplate>
                                                        <div class="header-icon-wrapper wd-100">
                                                            <span class="TextTruncate S-M">{{col.header}}</span>
                                                        </div>
                                                    </ng-template>
                                                    <ng-template kendoGridCellTemplate let-rowData>
                                                        <div class="content header-left-padding">
                                                            <span *ngIf="col.header == 'KPIs'" kendoTooltip position="right" title={{rowData[col.field]}}
                                                                class="showToolTip TextTruncate" [ngClass]="rowData[col.field] === 'Valuation Methodology' ? 'bold-text' : ''">
                                                                {{rowData[col.field]}}
                                                            </span>
                                                        </div>
                                                    </ng-template>
                                                </kendo-grid-column>
                                            </ng-container>
                                            <ng-container *ngIf="tableColumnsHeaders.length > 0">
                                                <kendo-grid-column kendoTooltip position="right" [minResizableWidth]="200" *ngFor="let col of tableColumnsHeaders; index as i" [maxResizableWidth]="800"
                                                    [width]="200" title="{{col.header}}">
                                                    <ng-template kendoGridHeaderTemplate>
                                                        <div class="header-icon-wrapper wd-100">
                                                            <span class="TextTruncate table-data-right S-M">{{col.header}}</span>
                                                        </div>
                                                    </ng-template>
                                                    <ng-template kendoGridCellTemplate let-rowData>
                                                        <div class="prtcmny-det-o cell-padding table-data-right" [ngClass]="rowData['Kpi'] === 'Valuation Methodology' ? 'metho-bg-color' : ''">
                                                            <div class="content">
                                                                <div *ngIf="col.header != 'KPIs'" [ngClass]="rowData['Kpi'] === 'Valuation Methodology' ? 'bold-text' : ''" class="showToolTip TextTruncate">
                                                                    <div [title]="rowData[col.field]" *ngIf="rowData[col.field] != undefined && rowData[col.field] != null && rowData[col.field] != ''; else empty_Text">
                                                                        <span *ngIf="rowData['Kpi'] === 'Valuation Methodology'" kendoTooltip position="right" class="float-left" [title]="rowData[col.field]" [innerHtml]="rowData[col.field]"></span>
                                                                        <span *ngIf="rowData['Kpi'] === 'Fund Ownership (%)'" kendoTooltip position="right" class="TextTruncate tr-width-v1 text-align-right" [title]="rowData[col.field]" 
                                                                            [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: numberDecimalConst.percentDecimal | minusToBracketsWithPercentage: '%') : rowData[col.field]"></span>
                                                                        <span *ngIf="rowData['Kpi'] === 'Discount/Premium/Par'">
                                                                            <span class="float-left">({{ getPrefix(rowData, col.field) }})</span>
                                                                            <span kendoTooltip position="right" class="TextTruncate tr-width-v1 text-align-right" [title]="rowData[col.field]" 
                                                                                [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: numberDecimalConst.percentDecimal)+'%' : rowData[col.field]">
                                                                            </span>
                                                                        </span>
                                                                        <span *ngIf="rowData['Kpi'] === 'Target Multiple Mean/Median'">
                                                                            <span class="float-left">({{ getPrefix(rowData, col.field) }})</span>
                                                                            <span kendoTooltip position="right" class="TextTruncate tr-width-v1 text-align-right" [title]="rowData[col.field]" 
                                                                                [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: numberDecimalConst.currencyDecimal | minusSignToBrackets: '') : rowData[col.field]">
                                                                            </span>
                                                                        </span>
                                                                        <span *ngIf="rowData['Kpi'] !== 'Valuation Methodology' && rowData['Kpi'] !== 'Fund Ownership (%)' && rowData['Kpi'] !== 'Discount/Premium/Par' && rowData['Kpi'] !== 'Target Multiple Mean/Median'" kendoTooltip position="right" class="float-left TextTruncate tr-width-v1 text-align-right" [title]="rowData[col.field]" 
                                                                            [innerHtml]="isNumberCheck(rowData[col.field]) ? (rowData[col.field] | number: numberDecimalConst.currencyDecimal | minusSignToBrackets: '') : rowData[col.field]"></span>
                                                                    </div>
                                                                    <ng-template #empty_Text class="detail-sec">
                                                                        <span *ngIf="rowData['Kpi'] === 'Discount/Premium/Par'" class="float-left">({{ getPrefix(rowData, col.field) }})</span>
                                                                        <span *ngIf="rowData['Kpi'] !== 'Discount/Premium/Par'"class="float-right">NA</span>
                                                                        <span *ngIf="rowData['Kpi'] === 'Discount/Premium/Par'"class="float-right">-</span>
                                                                        
                                                                    </ng-template>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </ng-template>
                                                </kendo-grid-column>
                                            </ng-container>
                                            <ng-template kendoGridNoRecordsTemplate>
                                                <div class="row mr-0 ml-0 no-data-container" *ngIf="valuationSummaryModel.length == 0">
                                                    <ng-container *ngTemplateOutlet="noDataTemplate; context: { message: 'No data found' ,icon:'no-content-lp-report' }"></ng-container>
                                                </div>
                                            </ng-template>
                                        </kendo-grid>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </mat-tab-nav-panel>
                </div>
            </div>
        </div>
    </div>
</div>
<ng-template #noDataTemplate let-message="message" let-icon="icon">
    <div class="col-12 pr-0 pl-0 no-content-section">
        <div class="text-center">
            <img src="assets/dist/images/{{icon}}.svg" alt="No Content" class="no-content-image">
        </div>
        <div class="text-center pt-2 empty-state-text">
            {{ message }}
        </div>
    </div>
</ng-template>
<div class="d-none" #OutsideClick>Out click</div>
<mat-menu #menu="matMenu" [hasBackdrop]="true" class="fixed-menu vs-filter-menu">
    <form [formGroup]="filterForm" (ngSubmit)="onSubmitFilter()" (click)="$event.stopPropagation()">
        <div class="filter-first">
            <div class="row m-0 p-3">
                <div class="float-left">
                    <img src="assets/dist/images/new-filter-icon.svg" alt="" />
                    <span class="pl-2">Filter</span>
                </div>
            </div>
            <div class="row m-0 pb-3">
                <div class="col-12 pl-3 pr-3">
                    <kendo-label text="Period">
                        <kendo-combobox [clearButton]="false"
                            formControlName="periodType" [fillMode]="'flat'" name="Period"
                            [virtual]="virtual" class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32"
                            [size]="'medium'" [data]="periodType" [valuePrimitive]="false" textField="name"
                            placeholder="Select Period" valueField="periodTypeId" id="period-type-filter"
                            [filterable]="true" (filterChange)="handleFilter($event, periodType, 'period')"
                            (valueChange)="onPeriodChange($event)">
                        </kendo-combobox>
                    </kendo-label>
                </div>
            </div>
            <div *ngIf="filterForm.get('periodType')?.value?.periodTypeId === 2">
                <div class="row m-0 pb-3">
                    <div class="col-12 pl-3 pr-3">
                        <kendo-label class="label-text" text="From Quarter">
                            <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                formControlName="fromQuarter" [fillMode]="'flat'" name="fromQuarter"
                                [virtual]="virtual" class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32"
                                [size]="'medium'" [data]="quarterOptions" [valuePrimitive]="false"
                                [filterable]="true" (filterChange)="handleFilter($event, quarterOptions, 'quarter')"
                                textField="text" placeholder="Select From Quarter" valueField="value" id="from-quarter-filter">
                            </kendo-combobox>
                        </kendo-label>
                    </div>
                </div>
                <div class="row m-0 pb-3">
                    <div class="col-12 pl-3 pr-3">
                        <kendo-label class="label-text" text="From Year">
                            <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                formControlName="fromYear" [fillMode]="'flat'" name="fromYear"
                                [virtual]="virtual" class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32"
                                [size]="'medium'" [data]="yearOptions" [valuePrimitive]="false"
                                [filterable]="true" (filterChange)="handleFilter($event, yearOptions, 'year')"
                                textField="text" placeholder="Select From Year" valueField="value" id="from-year-filter">
                            </kendo-combobox>
                        </kendo-label>
                    </div>
                </div>
                <div class="row m-0 pb-3">
                    <div class="col-12 pl-3 pr-3">
                        <kendo-label class="label-text" text="To Quarter">
                            <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                formControlName="toQuarter" [fillMode]="'flat'" name="toQuarter"
                                [virtual]="virtual" class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32"
                                [size]="'medium'" [data]="quarterOptions" [valuePrimitive]="false"
                                [filterable]="true" (filterChange)="handleFilter($event, quarterOptions, 'quarter')"
                                textField="text" placeholder="Select To Quarter" valueField="value" id="to-quarter-filter">
                            </kendo-combobox>
                        </kendo-label>
                    </div>
                </div>
                <div class="row pb-3 m-0">
                    <div class="col-12 pl-3 pr-3">
                        <kendo-label class="label-text" text="To Year">
                            <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                formControlName="toYear" [fillMode]="'flat'" name="toYear"
                                [virtual]="virtual" class="k-dropdown-width-260 k-custom-solid-dropdown k-dropdown-height-32"
                                [size]="'medium'" [data]="yearOptions" [valuePrimitive]="false"
                                [filterable]="true" (filterChange)="handleFilter($event, yearOptions, 'year')"
                                textField="text" placeholder="Select To Year" valueField="value" id="to-year-filter">
                            </kendo-combobox>
                        </kendo-label>
                    </div>
                </div>
            </div>
        </div>
        <div class="filter-footer pr-3 pb-3">
            <div class="d-inline">
                <button type="reset" name="Reset" class="btn btn-reset"
                    (click)="$event.stopPropagation();$event.preventDefault();resetForm()"
                    [disabled]="!filterForm.dirty">Reset</button>
            </div>
            <div class="d-inline">
                <button type="submit" name="Save" class="btn btn-light btn-app pt-0 pb-0"
                    [disabled]="!filterForm.valid">Set</button>
            </div>
        </div>
    </form>
</mat-menu>
<app-loader-component *ngIf="isLoading"></app-loader-component>