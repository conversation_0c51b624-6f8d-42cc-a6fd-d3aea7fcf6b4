@import "../../../../variables.scss";
.valuation-Summary-section-body{
    background: #FAFAFB 0% 0% no-repeat padding-box;
    border: 1px solid #DEDFE0;
    border-radius: 2px 2px 2px 2px;
    opacity: 1;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.0784313725);
}
.tab-middle{
    height: 32px;
    border-top: 1px solid #E6E6E6;
    padding: 4px 12px;
    background: #FAFAFA 0% 0% no-repeat padding-box;
}
.no-content-section{
    padding: 5rem;
}
.filter-icon{
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 100;
    display: flex;
}
.allvalues-valuation{
    font-weight: 400;
    font-style: italic;
    span{
        vertical-align: middle;
    }
}
.filter-first {
    background: $nep-white 0% 0% no-repeat padding-box;
    overflow-y: auto;
    height: 256px;
}

.filter-footer {
    padding-top: 32px;
    float: right;
}

.btn-app {
    color: $nep-white;
    background-color: $nep-button-primary;
    border-color: $nep-white !important;
    border-radius: 4px;
    width: 76px;
    height: 24px;
    text-align: center;
    font-size: 0.75rem;
    border: 1px solid #4061C7;
    padding-bottom: 0.063rem !important;
}
.btn-reset {
    color: $nep-button-primary;
    background-color: $nep-white;
    border-color: $nep-button-primary !important;
    border-radius: 4px;
    width: 76px;
    height: 24px;
    text-align: center;
    font-size: 0.75rem;
    border: 1px solid $nep-primary;
    padding-bottom: 0.063rem !important;
}
.metho-bg-color{
    background: #F5F9FF !important;
}