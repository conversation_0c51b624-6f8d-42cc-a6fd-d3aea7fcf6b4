<div class="row">
    <div class="col-lg-12">
        <div class="add-user-component">
            <div class="card card-main">
                <div class="card-header card-header-main p-0">
                    <div class="row mr-0 ml-0 fundlist-header">
                        <div class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                            <div class="float-right">
                                <div class="d-inline-block search">
                                    <span class="fa fa-search fasearchicon p-1"></span>
                                    <input #gb pInputText type="text"
                                    (input)="searchLoadPCLazy()"
                                        class="search-text-company companyListSearchHeight TextTruncate" placeholder="Search"
                                        [(ngModel)]="globalFilter">
                                </div>
                                <div class="d-inline-block"
                                    [hideIfUnauthorized]='{featureId:feature.Firm,action:"export"}'>
                                    <img
                    id="btn-download-firm"
class="p-action-padding download-excel" title="Export Firm (Excel file)"
                                        (click)="exportFirmList()" src="assets/dist/images/Cloud-download.svg" />
                                </div>
                                <div class="d-inline" [hideIfUnauthorized]='{featureId:feature.Fund,action:"export"}'>
                                    <span class="col-divider">
                                    </span>
                                </div>
                                <div class="d-inline-block"
                                    [hideIfUnauthorized]='{featureId:feature.Firm,action:"add"}'>
                                    <div class="add-icon p-add-padding">
                                        <a [routerLink]="['/add-firm']" title="Add Firm"
                      id="btn-add-firm"
>
                                            <img class="" title="Add Firm" src="assets/dist/images/plus.svg" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <kendo-grid  [data]="view | async" [pageSize]="state.take" [skip]="state.skip" 
                        [sortable]="true" [sort]="sort" [pageable]="{
                                buttonCount: 10,
                                info: true,
                                type: 'numeric',
                                pageSizes: [100,200,300,400,500],
                                previousNext: true  }" (dataStateChange)="dataStateChange($event)"
                        class="custom-kendo-list-grid k-grid-border-right-width k-grid-outline-none">
                        <ng-container>
                            <kendo-grid-column class="TextTruncate" field="FirmName">
                                <ng-template kendoGridHeaderTemplate>
                                    <div class="header-icon-wrapper wd-98">
                                        <span class="TextTruncate S-M">
                                            Firm Name
                                        </span>
                                    </div>
                                </ng-template>
                                <ng-template kendoGridCellTemplate let-firm>
                                    <a
                    id="btn-view-firm-details"
class="click-view" title="View Details" (click)="setHeaderName(firm.firmName)" href="javascript:void(0);" [routerLink]="['/firm-details', firm.encryptedFirmID]" [hideIfUnauthorized]='{featureId:feature.Firm,action:"view"}'>{{firm.firmName}}</a>
                                </ng-template>
                            </kendo-grid-column>
                            <kendo-grid-column class="TextTruncate" field="Website">
                                <ng-template kendoGridHeaderTemplate>
                                    <span class="TextTruncate S-M">
                                        Website
                                    </span>
                                </ng-template>
                                <ng-template kendoGridCellTemplate let-firm>
                                    <a title="View Website" href="//{{firm.website}}" target="_blank">{{firm.website}}</a>
                                </ng-template>
                            </kendo-grid-column>
                            <kendo-grid-column class="TextTruncate" field="headQuarter">
                                <ng-template kendoGridHeaderTemplate>
                                    <div class="header-icon-wrapper wd-98">
                                        <span class="TextTruncate S-M">
                                            Headquarter
                                        </span>
                                    </div>
                                </ng-template>
                                <ng-template kendoGridCellTemplate let-firm>
                                    <span *ngIf="firm.geographicLocations"><span *ngIf="firm.geographicLocations[0]?.region !=null"> {{ firm.geographicLocations[0].region?.region }}, </span>{{ firm.geographicLocations[0]?.country?.country }}<span *ngIf="firm.geographicLocations[0]?.state !=null">, {{ firm.geographicLocations[0].state?.state }}</span>
                                    <span *ngIf="firm.geographicLocations[0]?.city!=null">, {{ firm.geographicLocations[0].city?.city }}</span>
                                    </span>
                                </ng-template>
                            </kendo-grid-column>  
                            <ng-template kendoGridNoRecordsTemplate>
                                <app-empty-state class="finacials-beta-empty-state" [imageHeight]="'76vh'"
                                    [isGraphImage]="false"></app-empty-state>
                            </ng-template>
                        </ng-container>
                    </kendo-grid>
                  
                </div>
            </div>
        </div>
    </div>
</div>

<app-loader-component *ngIf="isLoader"></app-loader-component>