import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ToastrModule } from 'ngx-toastr';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ManageTrackerRecordsComponent } from './manage-tracker-records.component';
import { DashboardTrackerService } from 'src/app/services/dashboard-tracker.service';

const mockData = [
  {
    fieldType: 'Time Series',
    isEnable: true,
    namePattern: 'Pattern1',
    mapTo: null,
    dataType: 'String',
    dropdownList: ['Option1', 'Option2', 'Option3', 'Option4'],
    createdDate: '2025-07-11',
  },
  {
    fieldType: 'Static',
    isEnable: false,
    namePattern: 'Pattern2',
    mapTo: null,
    dataType: 'Number',
    dropdownList: [],
    createdDate: '2025-07-10',
  },
];

describe('ManageTrackerRecordsComponent', () => {
  let component: ManageTrackerRecordsComponent;
  let fixture: ComponentFixture<ManageTrackerRecordsComponent>;
  let dashboardTrackerServiceSpy: jasmine.SpyObj<DashboardTrackerService>;

  beforeEach(async () => {
    dashboardTrackerServiceSpy = jasmine.createSpyObj('DashboardTrackerService', ['getDashboardTrackerColumnData']);
    // Provide a default observable for all tests to prevent undefined.subscribe errors
    dashboardTrackerServiceSpy.getDashboardTrackerColumnData.and.returnValue({
      subscribe: (handlers: any) => {
        handlers.next([]);
      }
    } as any);

    await TestBed.configureTestingModule({
      declarations: [ManageTrackerRecordsComponent],
      imports: [
        ToastrModule.forRoot(),
        BrowserAnimationsModule
      ],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        { provide: DashboardTrackerService, useValue: dashboardTrackerServiceSpy }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageTrackerRecordsComponent);
    component = fixture.componentInstance;
    component.data = [...mockData];
    component.showDropdownValues = 2;
    component.dropdownValueMaxLength = 6;
    fixture.detectChanges();
  });


  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call getDashboardTrackerColumnData on ngOnInit and map response correctly', () => {
    const apiResponse = [
      {
        fieldTypeName: 'Time Series',
        dataTypeName: 'String',
        name: 'Pattern1',
        startPeriod: '2025-07-11',
        mapTo: null,
        dropdownList: ['Option1', 'Option2'],
        isActive: true
      },
      {
        fieldTypeName: 'Static',
        dataTypeName: 'Number',
        name: 'Pattern2',
        startPeriod: '',
        mapTo: null,
        dropdownList: [],
        isActive: false
      }
    ];
    dashboardTrackerServiceSpy.getDashboardTrackerColumnData.and.returnValue({
      subscribe: (handlers: any) => {
        handlers.next(apiResponse);
      }
    } as any);

    component.ngOnInit();

    expect(dashboardTrackerServiceSpy.getDashboardTrackerColumnData).toHaveBeenCalled();
    expect(component.data.length).toBe(2);
    expect(component.data[0].fieldType).toBe('Time Series');
    expect(component.data[0].dataType).toBe('String');
    expect(component.data[0].namePattern).toBe('Pattern1');
    expect(component.data[0].createdDate).toBe('');
    expect(component.data[0].mapTo).toBe('');
    expect(component.data[0].dropdownList).toEqual(['Option1', 'Option2']);
    expect(component.data[0].isEnable).toBeTrue();
  });

  it('should set createdDate to empty string if startPeriod is falsy', () => {
    const apiResponse = [
      {
        fieldTypeName: 'Static',
        dataTypeName: 'Number',
        name: 'Pattern2',
        startPeriod: null,
        mapTo: null,
        dropdownList: [],
        isActive: false
      }
    ];
    dashboardTrackerServiceSpy.getDashboardTrackerColumnData.and.returnValue({
      subscribe: (handlers: any) => {
        handlers.next(apiResponse);
      }
    } as any);

    component.ngOnInit();

    expect(component.data[0].createdDate).toBe('');
  });

  it('should set dropdownList to undefined if not present in API response', () => {
    const apiResponse = [
      {
        fieldTypeName: 'Static',
        dataTypeName: 'Number',
        name: 'Pattern2',
        startPeriod: null,
        mapTo: null,
        isActive: false
      }
    ];
    dashboardTrackerServiceSpy.getDashboardTrackerColumnData.and.returnValue({
      subscribe: (handlers: any) => {
        handlers.next(apiResponse);
      }
    } as any);

    component.ngOnInit();

    expect(component.data[0].dropdownList).toBeUndefined();
  });

  it('should log error if getDashboardTrackerColumnData fails', () => {
    const consoleSpy = spyOn(console, 'error');
    dashboardTrackerServiceSpy.getDashboardTrackerColumnData.and.returnValue({
      subscribe: (handlers: any) => {
        handlers.error('Some error');
      }
    } as any);

    component.ngOnInit();

    expect(consoleSpy).toHaveBeenCalledWith('Error fetching column data', 'Some error');
  });

  it('should call onToggleChange and update isEnable', () => {
    const item = { isEnable: false };
    component.onToggleChange(true, item);
    expect(item.isEnable).toBeTrue();
  });

  it('should call onToggleClick and stop event propagation', () => {
    const event = { stopPropagation: jasmine.createSpy('stopPropagation') };
    component.onToggleClick(event, {});
    expect(event.stopPropagation).toHaveBeenCalled();
  });

it('should not render dropdown section if dropdownList is empty', () => {
  const apiResponse = [
    {
      fieldTypeName: 'Time Series',
      dataTypeName: 'String',
      name: 'Pattern1',
      startPeriod: '2025-07-11',
      mapTo: null,
      dropdownList: ['Option1'],
      isActive: true
    },
    {
      fieldTypeName: 'Static',
      dataTypeName: 'Number',
      name: 'Pattern2',
      startPeriod: '',
      mapTo: null,
      dropdownList: [],
      isActive: false
    }
  ];
  dashboardTrackerServiceSpy.getDashboardTrackerColumnData.and.returnValue({
    subscribe: (handlers: any) => {
      handlers.next(apiResponse);
    }
  } as any);

  component.ngOnInit();

  expect(
    component.data[1] &&
    (component.data[1].dropdownList === undefined ||
      (Array.isArray(component.data[1].dropdownList) && component.data[1].dropdownList.length === 0))
  ).toBeTrue();
});

  it('should display truncated dropdown values if longer than max length', () => {
    // Simulate template logic for truncation
    const value = 'LongDropdownValue';
    const truncated = value.length > component.dropdownValueMaxLength
      ? value.substring(0, component.dropdownValueMaxLength) + '...'
      : value;
    expect(truncated.endsWith('...') || truncated.length <= component.dropdownValueMaxLength + 3).toBeTrue();
  });


  it('should handle empty API response gracefully', () => {
    dashboardTrackerServiceSpy.getDashboardTrackerColumnData.and.returnValue({
      subscribe: (handlers: any) => {
        handlers.next([]);
      }
    } as any);

    component.ngOnInit();

    expect(component.data.length).toBe(0);
  });
  
  it('should set confirmDelete true and set deleteIndex on onDeleteClick', () => {
    component.confirmDelete = false;
    component.deleteIndex = null;
    component.onDeleteClick(1);
    expect(component.confirmDelete).toBeTrue();
    expect(component.deleteIndex).toBe(1);
  });

  it('should call deleteDashboardTrackerColumn and remove item on successful deleteColumnRecord', () => {
    // Add the deleteDashboardTrackerColumn spy for this test
    (dashboardTrackerServiceSpy as any).deleteDashboardTrackerColumn = jasmine.createSpy('deleteDashboardTrackerColumn');
    const record = { id: 123 };
    component.data = [record];
    component.deleteIndex = 0;
    component.confirmDelete = true;
    dashboardTrackerServiceSpy.deleteDashboardTrackerColumn.and.returnValue({
      subscribe: (handlers: any) => {
        handlers.next();
      }
    } as any);

    component.deleteColumnRecord();

    expect(dashboardTrackerServiceSpy.deleteDashboardTrackerColumn).toHaveBeenCalledWith(123);
    expect(component.data.length).toBe(0);
    expect(component.deleteIndex).toBeNull();
    expect(component.confirmDelete).toBeFalse();
  });

  it('should handle error in deleteColumnRecord and reset confirmDelete/deleteIndex', () => {
    (dashboardTrackerServiceSpy as any).deleteDashboardTrackerColumn = jasmine.createSpy('deleteDashboardTrackerColumn');
    const record = { id: 123 };
    component.data = [record];
    component.deleteIndex = 0;
    component.confirmDelete = true;
    const consoleSpy = spyOn(console, 'error');
    dashboardTrackerServiceSpy.deleteDashboardTrackerColumn.and.returnValue({
      subscribe: (handlers: any) => {
        handlers.error('delete error');
      }
    } as any);

    component.deleteColumnRecord();

    expect(consoleSpy).toHaveBeenCalledWith('Error deleting column record', 'delete error');
    expect(component.confirmDelete).toBeFalse();
    expect(component.deleteIndex).toBeNull();
  });

  it('should do nothing in deleteColumnRecord if record id is missing', () => {
    (dashboardTrackerServiceSpy as any).deleteDashboardTrackerColumn = jasmine.createSpy('deleteDashboardTrackerColumn');
    component.data = [{}];
    component.deleteIndex = 0;
    component.confirmDelete = true;
    component.deleteColumnRecord();
    expect(dashboardTrackerServiceSpy.deleteDashboardTrackerColumn).not.toHaveBeenCalled();
  });

  it('should reset confirmDelete and deleteIndex on cancelDelete', () => {
    component.confirmDelete = true;
    component.deleteIndex = 2;
    component.cancelDelete();
    expect(component.confirmDelete).toBeFalse();
    expect(component.deleteIndex).toBeNull();
  });
});
