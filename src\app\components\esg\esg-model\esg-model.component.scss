@import "../../../../variables";

.card {
    margin-top: -4px !important;
}

mat-tab-nav-panel.mat-mdc-tab-nav-panel {
    display: block;
    width: calc(100% + 15px) !important;
}

.esg-section .col-lg-12 {
    padding-right: 0;
    padding-left: 0;
}

.row.esg-model-page {
    margin-right: 0px;
    margin-left: 0px;
}

.p-action-padding {
    padding: 0px 8px 0px 12px;
}

.p-label-padding {
    padding-left: 12px;
}

.col-divider {
    border-right: 1px solid #DEDFE0 !important;
}

.pref-icon {
    padding-right: 12px !important;
}

.label-content-padding {
    padding-top: 20px !important;
}

.position-label.label-content-padding {
    display: inline-table;
    padding-top: 15px !important;
    padding-left: 15px !important;
}

.filter-label {
    letter-spacing: 0px;
    color: #55565A;
    opacity: 1;
    white-space: nowrap !important;
}

.custom-padding {
    padding: 0.5rem 0 1rem 0;
}

.custom-padding-no-data {
    padding-top: 1%;
    padding-bottom: 1%;
    background: #FAFAFB;
}

.disable-pointer-events {
    pointer-events: none;
    opacity: 0.65;
}

.custom-background {
    background-color: antiquewhite;
}

.empty-state-1 {
    padding-top: 10%;
    padding-bottom: 10%;
}

.apply-button {
    margin-top: 30px !important;
}

.custom-padding-apply {
    margin-left: -44px;
}

.moduleContainer {
    box-shadow: 0px 3px 6px #00000014;
    border: 1px solid #DEDFE0;
    border-radius: 4px;
    opacity: 1;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    margin-bottom: 15px;
    position: relative;
    width: calc(100% - 30px);
    left: 15px;
    padding-left: 15px;
}

.button {
    background-color: $nep-primary;
    color: white;
}

.pc-lable {
    padding-top: 10px;
    padding-bottom: 5px;
    margin-right: 50px;
    padding-left: 5px;
}

.esg-model-page {
    border: 1px solid #DEDFE0;
    border-radius: 4px;
    opacity: 1;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    position: relative;
    width: calc(100% - 30px);
    left: 15px;
    padding-left: 15px;
    box-shadow: 0px 3px 6px #00000014;
}

.esg-model-page.no-data{
    height: calc(100vh - 200px)!important;
}

::ng-deep .esg-model-page .mat-mdc-tab-link-container {
    margin-left: 44px !important;
}

.esg-model-page .row {
    display: block !important;
}

.image-padding {
    padding-top: 18%;
    padding-bottom: 18%;
}

.section-width {
    left: -15px;
}

.tab-section-div {
    width: calc(100% + 15px);
}

::ng-deep .esg-pc-dropdown {
    .p-dropdown {
        width: 240px;
        height: 32px;
        background-color: #FFFFFF !important;

        .p-dropdown-label {
            color: #000000;
            opacity: 1 !important;
            font-size: 14px;
            padding: 5px 0px 0px 0px;
        }

        .p-dropdown-panel {
            margin-top: 4px;
            box-shadow: 0 0 1px 1px rgb(0 0 0 / 2%), 0 6px 12px rgb(0 0 0 / 18%);
            transform-origin: 0 0;

            .p-dropdown-items {
                padding: 0px !important;

                .p-dropdown-item {
                    padding: 12px 16px;
                    padding-right: 8px !important;
                    text-align: left;
                    letter-spacing: 0px;
                    color: #55565A !important;
                    opacity: 1;

                    &:hover {
                        background-color: #F0F0F1 !important;
                        cursor: pointer;
                    }
                }

                .p-highlight {
                    background-color: #F0F0F1 !important;
                }
            }
        }
    }

    .p-multiselect {
        width: 240px !important;
        height: 32px;
        background-color: #FFFFFF !important;
        border: 1px solid #CAC9C7;
        border-radius: 4px !important;
        padding-left: 8px;

        .p-multiselect-trigger {
            padding: 8px 8px 8px 0px;
        }
    }
}

.allvalues {
    float: left;
    font-size: 12px;
    margin-right: 12px;
    color: $nep-icon-grey;
    padding-left: 16px !important;
    padding-top: 8px !important;
}

::ng-deep .panel {
    border: 0px solid transparent !important;
    border-radius: 0px
}

.QMY_Container {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #DEDFE0;
    border-radius: 4px;
    opacity: 1;
    cursor: pointer;
}

.QMY_Text {
    text-align: left;
    font-size: 14px;
    letter-spacing: 0px;
    color: #55565A;
    opacity: 1;
}

.QMYStyle {
    position: relative;
    top: -0.25rem;
    padding: 0.2rem 0.75rem 0.2rem 0.75rem;
}

.YStyle {
    margin-right: 0.25rem !important;
}

.activeQMY {
    background: #F7F8FC 0% 0% no-repeat padding-box !important;
    color: #4061C7 !important;
    border-radius: 4px;
}

.headerSize {
    font-size: 1.5rem;
    padding: 10px;
}

.backgroundColor {
    background-color: #FAFAFB !important;
}

.topBorder {
    border-top: 1px solid #dee2e6 !important;
}

.custom-padding {
    padding-left: 2px !important;
    padding-right: 2px !important;
}

.custom-filter {
    padding-right: 8px !important;
    padding-top: 0px !important;
    margin-top: 3px !important;
}

.tr-all-values {
    letter-spacing: 0.14px;
    color: #55565A;
    opacity: 1;
    padding-top: 2px !important;
}

.tr-all-values-l {
    padding: 6px 15px;
}

.tr-all-values-l-right {
    padding-right: 8px;
}

.tr-all-values-right {
    padding-right: 12px;
    margin-right: 0px !important;
}

mat-tab-nav-panel#mat-tab-nav-panel-13 {
    display: block;
    width: calc(100% + 15px) !important;
}

.esg-foot-note-section {
    width: calc(100% + 15px);

    .foot-note {
        border-bottom: 1px solid #DEDFE0;
        letter-spacing: 0px;
        color: #212121;
        opacity: 1;
        font-family: "Helvetica Neue LT W05_65 Medium", Arial, Verdana, Tahoma, sans-serif;
        background: $nep-white 0% 0% no-repeat padding-box;
        box-shadow: 0px 3px 6px #00000014;
        opacity: 1;
        padding: 9px 16px;
        z-index: 1040;
    }

    .foot-editor-section {
        z-index: 1000;
    }

    .footnote-save {
        height: 56px;
        margin-left: 0px !important;
        margin-top: -5px;
        padding: 12px 16px 12px 16px;
        width: 100%;
        flex-shrink: 0;
        border-radius: 0px 0px 4px 4px;
        border-top: 1px solid #DEDFE0;
        border-right: var(--spacing-0, 0px) solid #DEDFE0;
        border-bottom: var(--spacing-0, 0px) solid #DEDFE0;
        border-left: var(--spacing-0, 0px) solid #DEDFE0;
        background: #FFF;
        box-shadow: 0px 2px 12px 0px rgba(113, 113, 113, 0.29);

        .footnote-save-buttons {
            float: right;
            display: flex;
        }
    }
}

.apply-opacity{
    opacity: 0.7;
}

.save-button {
    display: grid;
    width: 120px;
}

@media (max-width: 1660px) {
    .pc-lable {
        margin-right: 165px;
    }
}

@media (max-width: 1600px) {
    .pc-lable {
        margin-right: 100px;
    }
}

@media (max-width: 1440px) {
    .pc-lable {
        margin-right: 100px;
    }
}

@media (max-width: 1360px) {
    .pc-lable {
        margin-right: 100px;
    }
}

@media (max-width: 1280px) {
    .pc-lable {
        margin-right: 100px;
    }
}

@media (max-width: 1248px) {
    .pc-lable {
        margin-right: 100px;
    }
}

@media (max-width: 1152px) {
    .pc-lable {
        margin-right: 120px;
    }
}

@media (max-width: 1024px) {
    .pc-lable {
        margin-right: 140px;
    }
}

@media (max-width: 800px) {
    .pc-lable {
        margin-right: 165px;
    }
}

@media (max-height: 600px) {
    .image-padding {
        padding-top: 14%;
        padding-bottom: 14%;
    }
}

@media (max-height: 800px) {
    .image-padding {
        padding-top: 10%;
        padding-bottom: 10%;
    }
}
.req-label {
    color:#666666 !important;
    &:after {
        padding-left:0.25rem;
        content: "*";
        color: red;
    }
}