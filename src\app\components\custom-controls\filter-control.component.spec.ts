import { ComponentFixture, TestBed } from "@angular/core/testing";
import { EventEmitter, NO_ERRORS_SCHEMA ,SimpleChange,SimpleChanges} from "@angular/core";
import { AccountService } from "src/app/services/account.service";
import { FilterService } from "src/app/services/filter.services";
import { FormsModule } from "@angular/forms";
import { FilterControlComponent } from "./filter-control.component";
import { of, throwError } from "rxjs";

describe("FilterControlComponent", () => {
  let component: FilterControlComponent;
  let fixture: ComponentFixture<FilterControlComponent>;

  beforeEach( async () => {
    const accountServiceStub = () => ({ redirectToLogin: error => ({}) });
    const filterServiceStub = () => ({
      SaveFilter: filters => ({ subscribe: f => f({}) }),
      DeleteFilter: userReportId => ({ subscribe: f => f({}) }),
      getFilter: userReportId => ({ subscribe: f => f({}) }),
      UpdateFilter: response => ({ subscribe: f => f({}) }),
      getFilters: () => ({ subscribe: f => of({}) })
    });
   await TestBed.configureTestingModule({
      imports: [FormsModule],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [FilterControlComponent],
      providers: [
        { provide: AccountService, useFactory: accountServiceStub },
        { provide: FilterService, useFactory: filterServiceStub }
      ]
    }).compileComponents();
    fixture = TestBed.createComponent(FilterControlComponent);
    component = fixture.componentInstance;
    component.onApply = new EventEmitter();
    component.onReset = new EventEmitter();
    spyOn(component.onApply, 'emit');
    spyOn(component.onReset, 'emit');
    spyOn(component, 'LoadFilters');
  });

  it("can load instance", () => {
    expect(component).toBeTruthy();
  });

  it(`IsEnabled has default value`, () => {
    expect(component.IsEnabled).toEqual(false);
  });

  it(`EditMode has default value`, () => {
    expect(component.EditMode).toEqual(false);
  });

  it(`disablePrimaryButton has default value`, () => {
    expect(component.disablePrimaryButton).toEqual(true);
  });

  it(`confirmSave has default value`, () => {
    expect(component.confirmSave).toEqual(false);
  });

  it(`confirmDelete has default value`, () => {
    expect(component.confirmDelete).toEqual(false);
  });

  it(`DeleteDisabled has default value`, () => {
    expect(component.DeleteDisabled).toEqual(true);
  });

  it(`IsItemSelected has default value`, () => {
    expect(component.IsItemSelected).toEqual(false);
  });

  it(`ShowFilterUpdated has default value`, () => {
    expect(component.ShowFilterUpdated).toEqual(false);
  });

  it(`DuplicateRecord has default value`, () => {
    expect(component.DuplicateRecord).toEqual(false);
  });

  it(`EditDuplicateRecord has default value`, () => {
    expect(component.EditDuplicateRecord).toEqual(false);
  });

  it(`NewlyAddedId has default value`, () => {
    expect(component.NewlyAddedId).toEqual(0);
  });

  it('#ngOnChanges should call ResetFilters if ReportId changes', () => {
    const changes: SimpleChanges = {
      ReportId: new SimpleChange('oldValue', 'newValue', false)
    };
    spyOn(component, 'ResetFilters');

    component.ngOnChanges(changes);

    expect(component.ResetFilters).toHaveBeenCalled();
  });

  it('#ngOnChanges should not call ResetFilters if ReportId does not change', () => {
    const changes: SimpleChanges = {
      ReportId: new SimpleChange('oldValue', 'oldValue', false)
    };
    spyOn(component, 'ResetFilters');

    component.ngOnChanges(changes);

    expect(component.ResetFilters).not.toHaveBeenCalled();
  });

  it('#OnFilterNamedChanged should set disablePrimaryButton to true if the trimmed length of the event target\'s value is 0 or less', () => {
    const event = {
      target: {
        value: ' '
      }
    };

    component.OnFilterNamedChanged(event);

    expect(component.disablePrimaryButton).toBe(true);
  });

  it('#OnFilterNamedChanged should set userReportName to the event target\'s value and DuplicateRecord to false', () => {
    const event = {
      target: {
        value: 'Test'
      }
    };

    component.OnFilterNamedChanged(event);

    expect(component.userReportName).toBe('Test');
    expect(component.DuplicateRecord).toBe(false);
  });

  it('#ApplyFilters should emit onApply event', () => {
    component.ApplyFilters();

    expect(component.onApply.emit).toHaveBeenCalled();
  });

  it('#ResetFilters should reset properties and emit onReset event', () => {
    component.ResetFilters();

    expect(component.selectReport).toBeNull();
    expect(component.EditDuplicateRecord).toBe(false);
    expect(component.IsItemSelected).toBe(false);
    expect(component.LoadFilters).toHaveBeenCalled();
    expect(component.onReset.emit).toHaveBeenCalled();
  });

  it('#SaveFilters should set properties', () => {
    component.SaveFilters();

    expect(component.userReportName).toBe("");
    expect(component.confirmSave).toBe(true);
    expect(component.disablePrimaryButton).toBe(true);
  });

  it('#onCancel should reset properties', () => {
    component.onCancel();

    expect(component.confirmSave).toBe(false);
    expect(component.DuplicateRecord).toBe(false);
    expect(component.EditDuplicateRecord).toBe(false);
  });

  it('#OnDeleteFilter should set confirmDelete to true', () => {
    component.OnDeleteFilter();

    expect(component.confirmDelete).toBe(true);
  });

  it('#CancelDelete should set confirmDelete to false', () => {
    component.CancelDelete();

    expect(component.confirmDelete).toBe(false);
  });

  it('#search should call LoadFilters', () => {
    component.search();

    expect(component.LoadFilters).toHaveBeenCalled();
  });

  it('#Delete should call DeleteFilter and set properties', () => {
    const response = 'response';
    const filterServices = TestBed.inject(FilterService);
    spyOn(filterServices, 'DeleteFilter').and.returnValue(of(response));

    component.Delete();

    expect(filterServices.DeleteFilter).toHaveBeenCalled();
    expect(component.LoadFilters).toHaveBeenCalled();
    expect(component.onReset.emit).toHaveBeenCalled();
    expect(component.IsItemSelected).toBe(false);
    expect(component.selectReport).toBeNull();
  });

  it('#Delete should call redirectToLogin on error', () => {
    const error = 'error';
    const accountServices = TestBed.inject(AccountService);
    const filterServices = TestBed.inject(FilterService);
    spyOn(accountServices, 'redirectToLogin');
    spyOn(filterServices, 'DeleteFilter').and.returnValue(throwError(() =>error));

    component.Delete();

    expect(accountServices.redirectToLogin).toHaveBeenCalledWith(error);
  });

  it('#Update should call getFilter and UpdateFilter if temp is undefined', () => {
    component.selectReport = {};
    const response = { UserReportName: 'name', userReportId: 'id' };
    const filterServices = TestBed.inject(FilterService);
    spyOn(filterServices, 'getFilter').and.returnValue(of(response));
    spyOn(filterServices, 'UpdateFilter').and.returnValue(of(0));

    component.Update();

    expect(filterServices.getFilter).toHaveBeenCalled();
    expect(filterServices.UpdateFilter).toHaveBeenCalled();
    expect(component.IsItemSelected).toBe(false);
    expect(component.ShowFilterUpdated).toBe(true);
    expect(component.EditDuplicateRecord).toBe(false);
  });

  it('#Update should call redirectToLogin on error', () => {
    const error = 'error';
    const filterServices = TestBed.inject(FilterService);
    const accountServices = TestBed.inject(AccountService);
    spyOn(accountServices, 'redirectToLogin');
    spyOn(filterServices, 'getFilter').and.returnValue(throwError(() =>error));

    component.Update();

    expect(accountServices.redirectToLogin).toHaveBeenCalledWith(error);
  });

  it('#OnFiltersSelected should call getFilter and set properties', () => {
    component.selectReport = { userReportId: 'id' };
    const response = 'response';
    const filterServices = TestBed.inject(FilterService);
    spyOn(component.OnSavedFilter, 'emit');
    spyOn(filterServices, 'getFilter').and.returnValue(of(response));

    component.OnFiltersSelected();

    expect(filterServices.getFilter).toHaveBeenCalled();
    expect(component.DeleteDisabled).toBe(false);
    expect(component.SelectedReport).toBe(component.selectReport);
    expect(component.OnSavedFilter.emit).toHaveBeenCalledWith(response);
  });

  it('#OnFiltersSelected should call redirectToLogin on error', () => {
    const error = 'error';
    const filterServices = TestBed.inject(FilterService);
    const accountServices = TestBed.inject(AccountService);
    spyOn(accountServices, 'redirectToLogin');
    spyOn(filterServices, 'getFilter').and.returnValue(throwError(() =>error));

    component.OnFiltersSelected();

    expect(accountServices.redirectToLogin).toHaveBeenCalledWith(error);
  });

});
