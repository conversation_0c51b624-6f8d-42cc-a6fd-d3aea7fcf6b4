<div class="row">
    <div class="col-lg-12">
        <div class="add-user-component">
            <div class="card card-main">
                <div class="card-header card-header-main p-0 border-bottom-0 ">
                    <div class="row mr-0 ml-0 fundlist-header border-bottom">
                        <div class="col-12 col-xs-12 col-sm-12 col-lg-12 col-xl-12 col-md-12 pr-0 pl-0">
                            <div class="float-right">
                                <div class="d-inline-block search">
                                    <span class="fa fa-search  fasearchicon p-1"></span>
                                    <input #gb type="text" (input)="searchLoadPCLazy()" pInputText placeholder="Search"
                                        class="search-text-company companyListSearchHeight TextTruncate"
                                        [(ngModel)]="globalFilter">
                                </div>
                                <div id="downlaodDeals" class="d-inline-block"
                                    [ngClass]="{disabledNoOfCasesDiv: isdownloadfilter}"
                                    [hideIfUnauthorized]='{featureId:feature.Deal,action:"export"}'>
                                    <img class="p-action-padding download-excel cursor-filter"
                                        title="Export Deal (Excel file)" id="dropdownMenuButton" #dropdownMenuButton
                                        [matMenuTriggerFor]="menu" alt="" src="assets/dist/images/Cloud-download.svg"
                                        #iMenuTrigger="matMenuTrigger" />
                                </div>
                                <popover predecessorId="downlaodDeals" [show]="isExportLoading" marginLeft="-260px"
                                    marginTop="35px">
                                </popover>
                                <div class="fund-splitButton d-inline-block" id="div-download-deals-list">
                                    <a class="loading-input-controls2" *ngIf="isExportLoading">
                                        <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                    </a>
                                    <mat-menu #menu="matMenu">
                                        <div class="download-dropdown-content TextTruncate" id="download-deal-list" title="Deal List"
                                            (click)="exportDealList()">
                                            Deal List
                                        </div>
                                        <div class="download-dropdown-content TextTruncate" title="New Investments"
                                            (click)="openQuarterYearSelectionPopup()">
                                            New Investments
                                        </div>
                                    </mat-menu>
                                </div>


                                <div class="d-inline" [hideIfUnauthorized]='{featureId:feature.Deal,action:"export"}'>
                                    <span class="col-divider">
                                    </span>
                                </div>
                                <div class="d-inline-block"
                                    [hideIfUnauthorized]='{featureId:feature.Deal,action:"add"}'>
                                    <div class="add-icon p-add-padding">
                                        <a href="javascript:void(0)" title="Add Deal">
                                            <img
                        id="btn-add-deal"
class="" title="Add Deal" src="assets/dist/images/plus.svg"
                                                (click)="addDeal($event)" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <kendo-grid [data]="canViewInv ? (view | async):[]" [pageSize]="state.take" [skip]="state.skip" [sortable]="true"
                        [sort]="sort" [pageable]="{
            buttonCount: 10,
            info: true,
            type: 'numeric',
            pageSizes: [100,200,300,400,500],
            previousNext: true  }" (dataStateChange)="dataStateChange($event)"
                        class="custom-kendo-list-grid k-grid-border-right-width k-grid-outline-none">
                        <kendo-grid-column *ngFor="let dealDetails of dealData" field="{{dealDetails.name}}">
                            <ng-template kendoGridHeaderTemplate>
                                <div class="header-icon-wrapper wd-98">
                                    <span class="TextTruncate S-M">
                                        {{dealDetails.displayName}}
                                    </span>
                                </div>
                            </ng-template>
                            <ng-template kendoGridCellTemplate let-rowData>
                                <div class="content">
                                    <span *ngIf="dealDetails.name == dealDetailsConstants.DealCustomID"> <a
id="view-deal-details"
                                            class="click-view company-name"
                                            (click)="setHeaderName(rowData.dealCustomID)" href="javascript:void(0);"
                                            [routerLink]="['/deal-details', rowData.encryptedDealID]"
                                            title="View Details">{{rowData.dealCustomID}}</a></span>
                                    <span *ngIf="dealDetails.name == dealDetailsConstants.FundName"
                                        title="{{rowData.fundName}}">{{rowData.fundName}}</span>
                                    <span *ngIf="dealDetails.name == dealDetailsConstants.CompanyName"
                                        title="{{rowData.companyName}}">{{rowData.companyName}}</span>
                                </div>
                            </ng-template>
                        </kendo-grid-column>

                        <ng-template kendoGridNoRecordsTemplate>
                            <app-empty-state class="finacials-beta-empty-state" [imageHeight]="'76vh'"
                                [isGraphImage]="false"></app-empty-state>
                        </ng-template>
                    </kendo-grid>
                </div>
            </div>
        </div>
    </div>
</div>
<div *ngIf="show">
    <confirm-modal class="downloadNewDealInvestments" primaryButtonName="Confirm" secondaryButtonName="Cancel"
        hasHeaderStyle="true" modalTitle="Download New Transactions"
        (secondaryButtonEvent)="closeQuarterYearSelectionPopup()" (primaryButtonEvent)="downloadNewInvestment()"
        [disablePrimaryButton]="disableConfirm" popupToasterStyle="custom-toast-confirmModel">
        <div class="row mr-0 ml-0 main-row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 pl-0 pr-0">
                <div class="row mr-0 ml-0">
                    <div class="pb-2 TextTruncate" title="Quarter & Year">
                        Quarter & Year
                    </div>
                    <div>
                        <quarter-year-control [ControlName]="'pcYear'" [QuarterYear]="YearQuarter" width="257.6"
                            (onCalendarYearPicked)="QuarterYear($event)">
                        </quarter-year-control>
                    </div>
                    <div *ngIf="newInvestmentsNotFound" class="errorcolor TextTruncate"
                        title="No Investments found for the selected quarter and year">
                        No Investments found for the selected quarter and year
                    </div>
                </div>
            </div>
        </div>
    </confirm-modal>
</div>

<app-loader-component *ngIf="isLoader"></app-loader-component>